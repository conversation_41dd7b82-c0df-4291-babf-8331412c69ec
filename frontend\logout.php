<?php
/**
 * 用户退出登录
 * 保存用户信息用于快速重新登录
 */

// 引入session配置
require_once '../sql/session_config.php';
initLoginSession();

// 引入数据库配置
require_once '../sql/db_config.php';

// 检查用户是否已登录
if (isUserLoggedIn()) {
    $pdo = getDbConnection();
    
    // 获取用户信息用于快速登录
    $stmt = $pdo->prepare("SELECT id, username, quwan_id, avatar, nickname FROM users WHERE id = ?");
    $stmt->execute([$_SESSION['user_id']]);
    $user = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($user) {
        // 生成快速登录token
        $quick_login_token = bin2hex(random_bytes(32));
        $expires_at = date('Y-m-d H:i:s', strtotime('+7 days')); // 7天有效期
        
        // 保存快速登录信息到数据库
        $stmt = $pdo->prepare("
            INSERT INTO quick_login_tokens (user_id, token, user_info, expires_at, created_at) 
            VALUES (?, ?, ?, ?, NOW())
            ON DUPLICATE KEY UPDATE 
            token = VALUES(token), 
            user_info = VALUES(user_info), 
            expires_at = VALUES(expires_at),
            updated_at = NOW()
        ");
        
        $user_info = json_encode([
            'id' => $user['id'],
            'username' => $user['username'],
            'quwan_id' => $user['quwan_id'],
            'avatar' => $user['avatar'],
            'nickname' => $user['nickname']
        ]);
        
        $stmt->execute([$user['id'], $quick_login_token, $user_info, $expires_at]);
        
        // 设置快速登录cookie（7天有效）
        setcookie('quick_login_token', $quick_login_token, time() + (7 * 24 * 60 * 60), '/', '', false, true);
        
        // 设置退出登录标志
        $_SESSION['logout_success'] = true;
    }
}

// 清除用户登录状态
clearUserLoginSession();

// 重定向到登录页面
header('Location: login/index.php');
exit;
?>
