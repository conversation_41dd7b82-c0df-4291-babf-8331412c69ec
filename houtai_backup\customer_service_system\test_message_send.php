<?php
// 测试消息发送功能
header('Content-Type: text/html; charset=UTF-8');
ini_set('default_charset', 'UTF-8');

session_start();

// 检查登录状态
if (!isset($_SESSION['cs_logged_in']) || !$_SESSION['cs_logged_in']) {
    echo '<h1>请先登录客服系统</h1>';
    exit;
}

require_once '../db_config.php';

echo '<h1>🔍 测试消息发送功能</h1>';

try {
    $pdo = getDbConnection();
    
    // 获取进行中的会话
    $stmt = $pdo->query("
        SELECT session_id, user_name, user_id, customer_service_id, started_at 
        FROM customer_service_sessions 
        WHERE status = 'active' 
        ORDER BY updated_at DESC 
        LIMIT 5
    ");
    $activeSessions = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (!empty($activeSessions)) {
        echo '<h2>📋 进行中的会话</h2>';
        
        foreach ($activeSessions as $session) {
            echo '<div style="border: 1px solid #ddd; padding: 15px; margin: 10px 0; border-radius: 8px; background: #f9f9f9;">';
            echo '<h4>会话: ' . htmlspecialchars($session['session_id']) . '</h4>';
            echo '<p><strong>用户：</strong>' . htmlspecialchars($session['user_name']) . '</p>';
            echo '<p><strong>用户ID：</strong>' . htmlspecialchars($session['user_id'] ?? 'N/A') . '</p>';
            echo '<p><strong>客服ID：</strong>' . htmlspecialchars($session['customer_service_id']) . '</p>';
            echo '<p><strong>开始时间：</strong>' . htmlspecialchars($session['started_at']) . '</p>';
            
            // 测试消息发送表单
            echo '<div style="margin-top: 10px; padding: 10px; background: #e7f3ff; border-radius: 5px;">';
            echo '<h5>测试发送消息</h5>';
            echo '<form onsubmit="testSendMessage(event, \'' . $session['session_id'] . '\')">';
            echo '<input type="text" id="message_' . $session['session_id'] . '" placeholder="输入测试消息" style="width: 300px; padding: 5px; margin-right: 10px;">';
            echo '<button type="submit" style="padding: 5px 15px; background: #28a745; color: white; border: none; border-radius: 3px;">发送测试消息</button>';
            echo '</form>';
            echo '<div id="result_' . $session['session_id'] . '" style="margin-top: 10px;"></div>';
            echo '</div>';
            
            echo '</div>';
        }
    } else {
        echo '<div style="background: #fff3cd; padding: 15px; border-radius: 8px; margin: 10px 0;">';
        echo '<h3>⚠️ 没有进行中的会话</h3>';
        echo '<p>需要先接受一个会话才能测试消息发送</p>';
        echo '<a href="sessions.php" style="background: #007cba; color: white; padding: 8px 16px; text-decoration: none; border-radius: 5px;">返回会话列表</a>';
        echo '</div>';
    }
    
} catch (Exception $e) {
    echo '<div style="background: #f8d7da; padding: 15px; border-radius: 8px; margin: 10px 0; color: #721c24;">';
    echo '<h3>❌ 错误</h3>';
    echo '<p>' . htmlspecialchars($e->getMessage()) . '</p>';
    echo '</div>';
}
?>

<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>测试消息发送</title>
    <style>
        body { 
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; 
            margin: 20px; 
            background: #f5f5f5; 
        }
        .container { 
            max-width: 900px; 
            margin: 0 auto; 
            background: white; 
            padding: 20px; 
            border-radius: 10px; 
            box-shadow: 0 2px 10px rgba(0,0,0,0.1); 
        }
        .result { 
            margin: 10px 0; 
            padding: 10px; 
            border-radius: 5px; 
        }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        .info { background: #d1ecf1; color: #0c5460; }
        pre { 
            background: #f8f9fa; 
            padding: 10px; 
            border-radius: 5px; 
            overflow-x: auto; 
            font-size: 12px; 
        }
    </style>
</head>
<body>
    <div class="container">
        <div style="background: #e7f3ff; padding: 15px; border-radius: 8px; margin: 10px 0;">
            <h3>📝 测试说明</h3>
            <ul>
                <li><strong>目的</strong>：测试客服发送消息功能</li>
                <li><strong>检查</strong>：消息是否能正确插入数据库</li>
                <li><strong>验证</strong>：前台用户是否能收到消息</li>
            </ul>
        </div>
        
        <p>
            <a href="sessions.php">返回会话列表</a> | 
            <a href="final_test.php">接受会话测试</a> | 
            <a href="create_test_data.php">创建测试数据</a>
        </p>
    </div>
    
    <script>
        async function testSendMessage(event, sessionId) {
            event.preventDefault();
            
            const messageInput = document.getElementById('message_' + sessionId);
            const resultDiv = document.getElementById('result_' + sessionId);
            const message = messageInput.value.trim();
            
            if (!message) {
                resultDiv.innerHTML = '<div class="result error">请输入消息内容</div>';
                return;
            }
            
            resultDiv.innerHTML = '<div class="result info">🔄 发送中...</div>';
            
            try {
                const response = await fetch('api/send_cs_message.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        sessionId: sessionId,
                        message: message,
                        messageType: 'text'
                    })
                });
                
                const responseText = await response.text();
                console.log('发送消息响应:', responseText);
                
                let data;
                try {
                    data = JSON.parse(responseText);
                } catch (parseError) {
                    console.error('JSON解析失败:', parseError);
                    resultDiv.innerHTML = `
                        <div class="result error">
                            <strong>响应格式错误</strong><br>
                            <pre>${responseText}</pre>
                        </div>
                    `;
                    return;
                }
                
                if (data.success) {
                    resultDiv.innerHTML = `
                        <div class="result success">
                            <strong>✅ 消息发送成功！</strong><br>
                            消息ID: ${data.messageId || 'N/A'}<br>
                            <pre>${JSON.stringify(data, null, 2)}</pre>
                        </div>
                    `;
                    messageInput.value = ''; // 清空输入框
                } else {
                    resultDiv.innerHTML = `
                        <div class="result error">
                            <strong>❌ 消息发送失败</strong><br>
                            错误: ${data.error}<br>
                            <pre>${JSON.stringify(data, null, 2)}</pre>
                        </div>
                    `;
                }
                
            } catch (error) {
                console.error('发送消息失败:', error);
                resultDiv.innerHTML = `
                    <div class="result error">
                        <strong>❌ 网络错误</strong><br>
                        ${error.message}
                    </div>
                `;
            }
        }
    </script>
</body>
</html>
