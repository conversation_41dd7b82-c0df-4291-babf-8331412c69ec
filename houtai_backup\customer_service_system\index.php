<?php
// 客服管理系统主页
header('Content-Type: text/html; charset=UTF-8');
ini_set('default_charset', 'UTF-8');

session_start();

// 检查登录状态
if (!isset($_SESSION['cs_logged_in']) || !$_SESSION['cs_logged_in']) {
    header('Location: ../login.php');
    exit;
}

// 引用数据库配置文件
require_once '../db_config.php';

// 获取客服信息
$cs_name = $_SESSION['cs_name'] ?? '客服';
$cs_role = $_SESSION['cs_role'] ?? 'customer_service';
$cs_department = $_SESSION['cs_department'] ?? '';
$cs_team = $_SESSION['cs_team'] ?? '';
$cs_employee_id = $_SESSION['cs_employee_id'] ?? '';

// 获取统计数据
try {
    $pdo = getDbConnection();
    
    // 今日会话数
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM customer_service_sessions WHERE DATE(created_at) = CURDATE()");
    $today_sessions = $stmt->fetch()['count'] ?? 0;
    
    // 待处理会话数
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM customer_service_sessions WHERE status = 'waiting'");
    $waiting_sessions = $stmt->fetch()['count'] ?? 0;
    
    // 我的今日会话数
    $stmt = $pdo->prepare("SELECT COUNT(*) as count FROM customer_service_sessions WHERE customer_service_id = ? AND DATE(created_at) = CURDATE()");
    $stmt->execute([$_SESSION['cs_user_id']]);
    $my_today_sessions = $stmt->fetch()['count'] ?? 0;
    
    // 我的活跃会话数
    $stmt = $pdo->prepare("SELECT COUNT(*) as count FROM customer_service_sessions WHERE customer_service_id = ? AND status = 'active'");
    $stmt->execute([$_SESSION['cs_user_id']]);
    $my_active_sessions = $stmt->fetch()['count'] ?? 0;
    
} catch (Exception $e) {
    $today_sessions = 0;
    $waiting_sessions = 0;
    $my_today_sessions = 0;
    $my_active_sessions = 0;
}
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>趣玩星球客服管理系统</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="assets/css/customer-service.css">
</head>
<body>
    <div class="cs-container">
        <!-- 侧边栏 -->
        <?php include 'includes/sidebar.php'; ?>
        
        <!-- 主内容区 -->
        <div class="cs-main">
            <!-- 顶部导航 -->
            <?php include 'includes/topbar.php'; ?>
            
            <!-- 内容区域 -->
            <div class="cs-content">
                <div class="cs-header">
                    <h1><i class="fas fa-tachometer-alt"></i> 工作台</h1>
                    <p>欢迎回来，<?php echo htmlspecialchars($cs_name); ?>！今天也要为用户提供优质服务哦~</p>
                </div>

                <!-- 统计卡片 -->
                <div class="stats-grid">
                    <div class="stat-card primary">
                        <div class="stat-icon">
                            <i class="fas fa-comments"></i>
                        </div>
                        <div class="stat-content">
                            <h3><?php echo $today_sessions; ?></h3>
                            <p>今日总会话</p>
                        </div>
                        <div class="stat-trend up">
                            <i class="fas fa-arrow-up"></i>
                            <span>+12%</span>
                        </div>
                    </div>

                    <div class="stat-card warning">
                        <div class="stat-icon">
                            <i class="fas fa-clock"></i>
                        </div>
                        <div class="stat-content">
                            <h3><?php echo $waiting_sessions; ?></h3>
                            <p>待处理会话</p>
                        </div>
                        <div class="stat-action">
                            <a href="sessions.php?status=waiting" class="btn-sm">立即处理</a>
                        </div>
                    </div>

                    <div class="stat-card success">
                        <div class="stat-icon">
                            <i class="fas fa-user-check"></i>
                        </div>
                        <div class="stat-content">
                            <h3><?php echo $my_today_sessions; ?></h3>
                            <p>我的今日会话</p>
                        </div>
                        <div class="stat-trend up">
                            <i class="fas fa-arrow-up"></i>
                            <span>+8%</span>
                        </div>
                    </div>

                    <div class="stat-card info">
                        <div class="stat-icon">
                            <i class="fas fa-comment-dots"></i>
                        </div>
                        <div class="stat-content">
                            <h3><?php echo $my_active_sessions; ?></h3>
                            <p>我的活跃会话</p>
                        </div>
                        <div class="stat-action">
                            <a href="sessions.php?status=active&cs_id=<?php echo $_SESSION['cs_user_id']; ?>" class="btn-sm">查看详情</a>
                        </div>
                    </div>
                </div>

                <!-- 快捷操作 -->
                <div class="quick-actions">
                    <h2><i class="fas fa-bolt"></i> 快捷操作</h2>
                    <div class="action-grid">
                        <a href="sessions.php" class="action-card">
                            <div class="action-icon">
                                <i class="fas fa-comments"></i>
                            </div>
                            <h3>客服会话</h3>
                            <p>查看和处理客户会话</p>
                        </a>

                        <a href="session_management.php" class="action-card">
                            <div class="action-icon">
                                <i class="fas fa-tasks"></i>
                            </div>
                            <h3>会话管理</h3>
                            <p>管理所有客服会话</p>
                        </a>

                        <a href="quality_check.php" class="action-card">
                            <div class="action-icon">
                                <i class="fas fa-star"></i>
                            </div>
                            <h3>客服质检</h3>
                            <p>服务质量检查和评估</p>
                        </a>

                        <?php if ($cs_role === 'super_admin'): ?>
                        <a href="bot_management.php" class="action-card">
                            <div class="action-icon">
                                <i class="fas fa-robot"></i>
                            </div>
                            <h3>智能客服</h3>
                            <p>配置智能客服机器人</p>
                        </a>

                        <a href="settings.php" class="action-card">
                            <div class="action-icon">
                                <i class="fas fa-cog"></i>
                            </div>
                            <h3>系统设置</h3>
                            <p>客服系统配置管理</p>
                        </a>

                        <a href="staff_management.php" class="action-card">
                            <div class="action-icon">
                                <i class="fas fa-users-cog"></i>
                            </div>
                            <h3>客服管理</h3>
                            <p>客服人员管理</p>
                        </a>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- 最近会话 -->
                <div class="recent-sessions">
                    <h2><i class="fas fa-history"></i> 最近会话</h2>
                    <div class="session-list">
                        <!-- 这里将通过AJAX加载最近的会话数据 -->
                        <div class="loading">
                            <i class="fas fa-spinner fa-spin"></i>
                            <span>加载中...</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="assets/js/customer-service.js"></script>
    <script>
        // 页面加载完成后加载最近会话
        document.addEventListener('DOMContentLoaded', function() {
            loadRecentSessions();
        });

        function loadRecentSessions() {
            // 这里将实现AJAX加载最近会话的功能
            setTimeout(() => {
                document.querySelector('.recent-sessions .loading').innerHTML = 
                    '<p class="no-data">暂无最近会话数据</p>';
            }, 1000);
        }
    </script>
</body>
</html>
