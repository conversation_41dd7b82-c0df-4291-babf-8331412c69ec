<?php
/**
 * 统一登录注册页面
 * 根据手机号自动判断登录或注册流程
 */
// 引入session配置
require_once '../../sql/session_config.php';
initLoginSession();

// 引入数据库配置
require_once '../../sql/db_config.php';

// 如果已经登录，跳转到首页
if (isUserLoggedIn()) {
    header('Location: ../home/<USER>');
    exit;
}

// 检查是否有快速登录信息
$quick_login_user = null;
if (isset($_COOKIE['quick_login_token'])) {
    $pdo = getDbConnection();
    $stmt = $pdo->prepare("
        SELECT user_info, expires_at
        FROM quick_login_tokens
        WHERE token = ? AND expires_at > NOW()
    ");
    $stmt->execute([$_COOKIE['quick_login_token']]);
    $token_data = $stmt->fetch(PDO::FETCH_ASSOC);

    if ($token_data) {
        $quick_login_user = json_decode($token_data['user_info'], true);
    }
}
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0, user-scalable=no, shrink-to-fit=no">
    <meta name="format-detection" content="telephone=no">
    <style>
        * {
            -webkit-touch-callout: none;
            -webkit-user-select: none;
            -khtml-user-select: none;
            -moz-user-select: none;
            -ms-user-select: none;
            user-select: none;
            -webkit-tap-highlight-color: transparent;
        }

        /* 允许输入框选择文本 */
        input, textarea {
            -webkit-user-select: text;
            -khtml-user-select: text;
            -moz-user-select: text;
            -ms-user-select: text;
            user-select: text;
        }

        /* 禁止双击放大 */
        html {
            -ms-touch-action: manipulation;
            touch-action: manipulation;
        }
    </style>
    <meta name="theme-color" content="#FFFFFF">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="light-content">
    <meta name="msapplication-navbutton-color" content="#FFFFFF">

    <title>趣玩星球 - 登录注册</title>

    <!-- 引入字体图标 -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">

    <style>
        :root {
            --primary-color: #6F7BF5;
            --primary-dark: #5A67E8;
            --primary-light: #8A94F7;
            --bg-primary: #f8fafc;
            --bg-white: #ffffff;
            --bg-gray: #f5f5f5;
            --text-primary: #1a1a1a;
            --text-secondary: #666666;
            --text-muted: #999999;
            --border-color: #e5e5e5;
            --border-focus: var(--primary-color);
            --success-color: #52c41a;
            --error-color: #ff4d4f;
            --shadow-sm: 0 2px 8px rgba(0, 0, 0, 0.06);
            --shadow-md: 0 4px 12px rgba(0, 0, 0, 0.08);
            --shadow-lg: 0 8px 24px rgba(0, 0, 0, 0.12);
            --radius-sm: 6px;
            --radius-md: 12px;
            --radius-lg: 16px;
            --transition-fast: 0.2s ease;
            --transition-normal: 0.3s ease;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            -webkit-tap-highlight-color: transparent;
        }

        html {
            height: 100%;
            overflow-x: hidden;
            -webkit-text-size-adjust: 100%;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
            background: #FFFFFF;
            min-height: 100vh;
            color: var(--text-primary);
            line-height: 1.5;
            position: relative;
            overflow-x: hidden;
            overflow-y: auto;
            margin: 0;
            padding: 0;
            -webkit-overflow-scrolling: touch;
            /* 禁止iOS左右滑动 */
            touch-action: pan-y;
            -webkit-touch-callout: none;
            -webkit-user-select: none;
            -khtml-user-select: none;
            -moz-user-select: none;
            -ms-user-select: none;
            user-select: none;
        }



        /* 主容器 - 大厂风格 */
        .login-container {
            min-height: 100vh;
            background: #FFFFFF;
            display: flex;
            flex-direction: column;
            position: relative;
        }

        /* 顶部安全区域 */
        .safe-area-top {
            height: env(safe-area-inset-top, 44px);
            background: #FFFFFF;
        }

        /* 主要内容区域 */
        .main-content {
            flex: 1;
            padding: 0 24px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            min-height: calc(100vh - env(safe-area-inset-top, 44px) - env(safe-area-inset-bottom, 34px));
        }

        /* 品牌区域 */
        .brand-section {
            text-align: center;
            margin-bottom: 48px;
            padding-top: 60px;
        }

        .brand-logo {
            width: 80px;
            height: 80px;
            background: #FFFFFF;
            border-radius: 20px;
            margin: 0 auto 24px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 36px;
            color: white;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12), 0 4px 16px rgba(0, 0, 0, 0.08), 0 2px 8px rgba(0, 0, 0, 0.04);
            border: 1px solid rgba(0, 0, 0, 0.05);
        }

        .brand-text {
            width: 200px;
            height: auto;
            margin: 0 auto;
            display: block;
        }

        .brand-text img {
            width: 100%;
            height: auto;
            object-fit: contain;
            filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
        }



        /* 表单区域 */
        .form-section {
            background: var(--bg-white);
            border-radius: var(--radius-lg);
            padding: 32px 24px;
            box-shadow: var(--shadow-sm);
            border: 1px solid var(--border-color);
        }

        /* 表单组 */
        .form-group {
            margin-bottom: 24px;
        }

        /* 浮标输入框样式 */
        .floating-input {
            position: relative;
            margin-bottom: 24px;
        }

        .floating-input input {
            width: 100%;
            height: 52px;
            padding: 16px 16px 8px 16px;
            font-size: 16px;
            border: 2px solid var(--border-color);
            border-radius: var(--radius-md);
            background: var(--bg-white);
            color: var(--text-primary);
            transition: var(--transition-normal);
            outline: none;
        }

        .floating-input input:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(111, 123, 245, 0.1);
        }

        .floating-input label {
            position: absolute;
            left: 16px;
            top: 50%;
            transform: translateY(-50%);
            font-size: 16px;
            color: var(--text-muted);
            pointer-events: none;
            transition: var(--transition-normal);
            background: var(--bg-white);
            padding: 0 4px;
        }

        .floating-input input:focus + label,
        .floating-input input:not(:placeholder-shown) + label {
            top: 0;
            transform: translateY(-50%);
            font-size: 12px;
            color: var(--primary-color);
            font-weight: 600;
        }

        /* 协议勾选样式 */
        .agreement-section {
            margin: 24px 0;
        }

        .checkbox-container {
            display: flex;
            align-items: flex-start;
            gap: 12px;
            cursor: pointer;
            line-height: 1.5;
        }

        .custom-checkbox {
            position: relative;
            flex-shrink: 0;
            margin-top: 2px;
        }

        .custom-checkbox input[type="checkbox"] {
            opacity: 0;
            position: absolute;
            width: 20px;
            height: 20px;
            margin: 0;
        }

        .checkbox-mark {
            width: 20px;
            height: 20px;
            border: 2px solid var(--border-color);
            border-radius: 4px;
            background: var(--bg-white);
            transition: var(--transition-normal);
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .custom-checkbox input[type="checkbox"]:checked + .checkbox-mark {
            background: var(--primary-color);
            border-color: var(--primary-color);
            box-shadow: 0 0 0 2px rgba(111, 123, 245, 0.2);
        }

        .custom-checkbox input[type="checkbox"]:checked + .checkbox-mark::after {
            content: '✓';
            color: white;
            font-size: 12px;
            font-weight: bold;
        }

        .agreement-text {
            font-size: 14px;
            color: var(--text-secondary);
            line-height: 1.5;
        }

        .agreement-link {
            color: var(--primary-color);
            text-decoration: none;
            font-weight: 500;
        }

        .agreement-link:hover {
            text-decoration: underline;
        }

        /* 返回按钮 */
        .back-button {
            position: fixed;
            top: env(safe-area-inset-top, 44px);
            left: 16px;
            width: 40px;
            height: 40px;
            background: rgba(255, 255, 255, 0.9);
            border: none;
            border-radius: 50%;
            display: none;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            z-index: 100;
            box-shadow: var(--shadow-sm);
            backdrop-filter: blur(10px);
            transition: var(--transition-normal);
        }

        .back-button:hover {
            background: rgba(255, 255, 255, 1);
            transform: scale(1.05);
        }

        .back-button i {
            color: var(--text-primary);
            font-size: 16px;
        }

        /* 底部安全区域 */
        .safe-area-bottom {
            height: env(safe-area-inset-bottom, 0px);
            background: #FFFFFF;
        }

        /* 验证码输入框样式 */
        .verification-inputs {
            display: flex;
            gap: 8px;
            justify-content: center;
            margin: 24px 0;
            max-width: 100%;
            overflow: hidden;
            padding: 0 10px;
        }

        .verification-input {
            width: 40px !important;
            height: 48px !important;
            text-align: center;
            font-size: 20px !important;
            font-weight: 600;
            border: 2px solid var(--border-color) !important;
            border-radius: var(--radius-md) !important;
            background: var(--bg-white) !important;
            transition: var(--transition-normal) !important;
            flex-shrink: 0;
            /* 立体质感效果 */
            box-shadow:
                0 2px 4px rgba(0, 0, 0, 0.1),
                inset 0 1px 0 rgba(255, 255, 255, 0.8),
                inset 0 -1px 0 rgba(0, 0, 0, 0.05);
            position: relative;
        }

        .verification-input:focus {
            border-color: var(--primary-color) !important;
            box-shadow:
                0 0 0 3px rgba(111, 123, 245, 0.1),
                0 4px 8px rgba(0, 0, 0, 0.15),
                inset 0 1px 0 rgba(255, 255, 255, 0.9),
                inset 0 -1px 0 rgba(111, 123, 245, 0.1) !important;
            outline: none !important;
        }

        .verification-input.filled {
            border-color: var(--primary-color) !important;
            background: rgba(111, 123, 245, 0.02) !important;
            box-shadow:
                0 3px 6px rgba(111, 123, 245, 0.15),
                inset 0 1px 0 rgba(255, 255, 255, 0.9),
                inset 0 -1px 0 rgba(111, 123, 245, 0.1);
        }

        /* 移动端适配 */
        @media (max-width: 480px) {
            .verification-inputs {
                gap: 6px;
                padding: 0 15px;
            }

            .verification-input {
                width: 36px !important;
                height: 44px !important;
                font-size: 18px !important;
            }
        }

        @media (max-width: 360px) {
            .verification-inputs {
                gap: 4px;
                padding: 0 20px;
            }

            .verification-input {
                width: 32px !important;
                height: 40px !important;
                font-size: 16px !important;
            }
        }



        /* 小按钮样式 */
        .btn-small {
            height: 36px;
            padding: 0 16px;
            font-size: 14px;
            font-weight: 500;
        }

        .btn-secondary {
            background: var(--bg-gray);
            color: var(--text-secondary);
            border: 1px solid var(--border-color);
        }

        .btn-secondary:hover {
            background: var(--border-color);
            color: var(--text-primary);
        }

        /* 短信弹窗样式 */
        .sms-modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.6);
            z-index: 10000;
            display: none;
            align-items: center;
            justify-content: center;
            padding: 20px;
            backdrop-filter: blur(5px);
        }

        .sms-content {
            background: var(--bg-white);
            border-radius: var(--radius-lg);
            padding: 32px 24px;
            max-width: 360px;
            width: 100%;
            box-shadow: var(--shadow-lg);
            animation: modalSlideIn 0.3s ease;
        }

        @keyframes modalSlideIn {
            from {
                opacity: 0;
                transform: translateY(20px) scale(0.95);
            }
            to {
                opacity: 1;
                transform: translateY(0) scale(1);
            }
        }

        .sms-header {
            font-weight: 700;
            color: var(--text-primary);
            margin-bottom: 16px;
            font-size: 18px;
            text-align: center;
        }

        .sms-body {
            color: var(--text-secondary);
            line-height: 1.6;
            margin-bottom: 24px;
            font-size: 15px;
            text-align: center;
        }

        .verification-code-display {
            background: var(--primary-color);
            color: white;
            padding: 16px;
            border-radius: var(--radius-md);
            font-size: 28px;
            font-weight: 700;
            text-align: center;
            margin: 20px 0;
            letter-spacing: 6px;
            font-family: 'Courier New', monospace;
        }

        .form-group {
            margin-bottom: 24px;
        }

        .form-label {
            display: block;
            font-size: 14px;
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 8px;
        }

        .form-input {
            width: 100%;
            padding: 16px;
            border: 2px solid var(--border-color);
            border-radius: var(--radius-md);
            font-size: 16px;
            transition: var(--transition-normal);
            background: var(--bg-white);
        }

        .form-input:focus {
            outline: none;
            border-color: var(--border-focus);
            box-shadow: 0 0 0 3px rgba(64, 224, 208, 0.1);
        }

        .btn {
            width: 100%;
            padding: 16px;
            border: none;
            border-radius: var(--radius-md);
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: var(--transition-normal);
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
            text-decoration: none;
        }

        .btn-primary {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
            color: white;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-md);
        }

        .btn-primary:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .btn-secondary {
            background: var(--bg-primary);
            color: var(--text-secondary);
            border: 2px solid var(--border-color);
        }

        .btn-secondary:hover {
            background: var(--border-color);
        }

        /* 重新发送按钮激活状态 */
        .btn-resend-active {
            background: var(--primary-color) !important;
            color: white !important;
            border: 2px solid var(--primary-color) !important;
            box-shadow: 0 2px 8px rgba(111, 123, 245, 0.3);
        }

        .btn-resend-active:hover {
            background: var(--primary-dark) !important;
            border-color: var(--primary-dark) !important;
            color: white !important;
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(111, 123, 245, 0.4);
        }

        /* 协议勾选样式 */
        .agreement-section {
            margin-bottom: 24px;
        }

        .checkbox-container {
            display: flex;
            align-items: flex-start;
            gap: 12px;
            cursor: pointer;
            padding: 16px;
            border-radius: var(--radius-md);
            transition: var(--transition-normal);
            position: relative;
        }

        .checkbox-container:hover {
            background: rgba(64, 224, 208, 0.05);
        }

        .custom-checkbox {
            position: relative;
            width: 20px;
            height: 20px;
            flex-shrink: 0;
            margin-top: 2px;
        }

        .custom-checkbox input[type="checkbox"] {
            position: absolute;
            opacity: 0;
            width: 100%;
            height: 100%;
            cursor: pointer;
        }

        .checkbox-mark {
            position: absolute;
            top: 0;
            left: 0;
            width: 20px;
            height: 20px;
            border: 2px solid var(--border-color);
            border-radius: 4px;
            background: var(--bg-white);
            transition: var(--transition-normal);
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .custom-checkbox input[type="checkbox"]:checked + .checkbox-mark {
            background: var(--primary-color);
            border-color: var(--primary-color);
            transform: scale(1.1);
        }

        .custom-checkbox input[type="checkbox"]:checked + .checkbox-mark::after {
            content: '✓';
            color: white;
            font-size: 12px;
            font-weight: bold;
        }

        .agreement-text {
            font-size: 14px;
            color: var(--text-secondary);
            line-height: 1.6;
            flex: 1;
        }

        .agreement-link {
            color: var(--primary-color);
            text-decoration: none;
            font-weight: 600;
            transition: var(--transition-normal);
        }

        .agreement-link:hover {
            color: var(--primary-dark);
            text-decoration: underline;
        }

        /* 快速登录样式 */
        .quick-login-section {
            width: 100%;
            margin-bottom: 30px;
        }

        .quick-login-card {
            background: var(--bg-white);
            border-radius: var(--radius-lg);
            padding: 24px;
            box-shadow: var(--shadow-md);
            border: 1px solid var(--border-color);
            text-align: center;
        }

        .quick-login-avatar {
            width: 80px;
            height: 80px;
            margin: 0 auto 16px;
            border-radius: 50%;
            overflow: hidden;
            border: 3px solid var(--primary-color);
            box-shadow: 0 4px 12px rgba(111, 123, 245, 0.2);
        }

        .quick-login-avatar img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .quick-login-info {
            margin-bottom: 20px;
        }

        .quick-login-nickname {
            font-size: 18px;
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 4px;
        }

        .quick-login-id {
            font-size: 14px;
            color: var(--text-secondary);
        }

        .quick-login-btn {
            margin-bottom: 12px;
        }

        .switch-account-btn {
            background: var(--bg-gray);
            color: var(--text-secondary);
            border: 1px solid var(--border-color);
        }

        .switch-account-btn:hover {
            background: var(--border-color);
            color: var(--text-primary);
        }

        .loading {
            display: none;
            text-align: center;
            padding: 40px 20px;
        }

        /* 四角星加载动画 */
        .star-loader {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 8px;
            margin: 0 auto 16px;
            height: 40px;
        }

        .star {
            width: 12px;
            height: 12px;
            position: relative;
            animation: starMove 1.5s ease-in-out infinite;
        }

        .star:before {
            content: '';
            position: absolute;
            top: 0;
            left: 50%;
            width: 0;
            height: 0;
            border-left: 6px solid transparent;
            border-right: 6px solid transparent;
            border-bottom: 8px solid currentColor;
            transform: translateX(-50%);
        }

        .star:after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 50%;
            width: 0;
            height: 0;
            border-left: 6px solid transparent;
            border-right: 6px solid transparent;
            border-top: 8px solid currentColor;
            transform: translateX(-50%);
        }

        .star-1 {
            color: var(--primary-color);
            animation-delay: 0s;
        }

        .star-2 {
            color: #FF8C42;
            animation-delay: 0.5s;
        }

        .star-3 {
            color: #FF6B6B;
            animation-delay: 1s;
        }

        @keyframes starMove {
            0%, 100% {
                transform: translateY(0) scale(1);
                opacity: 0.7;
            }
            50% {
                transform: translateY(-10px) scale(1.2);
                opacity: 1;
            }
        }

        .error-message {
            background: #fee;
            color: #c53030;
            padding: 12px 16px;
            border-radius: var(--radius-md);
            margin-bottom: 16px;
            display: none;
            font-size: 14px;
        }

        .success-message {
            background: #f0fff4;
            color: #38a169;
            padding: 12px 16px;
            border-radius: var(--radius-md);
            margin-bottom: 16px;
            display: none;
            font-size: 14px;
        }

        .back-btn {
            position: absolute;
            top: 16px;
            left: 16px;
            background: rgba(255, 255, 255, 0.2);
            color: white;
            border: none;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition-normal);
            z-index: 2;
        }

        .back-btn:hover {
            background: rgba(255, 255, 255, 0.3);
        }

        /* 用户信息样式 */
        .user-info {
            display: flex;
            align-items: center;
            padding: 20px;
            background: var(--bg-primary);
            border-radius: var(--radius-md);
            margin-bottom: 24px;
        }

        .user-avatar {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            overflow: hidden;
            margin-right: 16px;
            border: 3px solid var(--primary-color);
        }

        .user-avatar img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .user-details {
            flex: 1;
        }

        .user-name {
            font-size: 18px;
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 4px;
        }

        .user-id {
            font-size: 14px;
            color: var(--text-secondary);
        }

        /* 安全状态样式 */
        .security-status {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
            padding: 12px 16px;
            border-radius: var(--radius-md);
            margin-bottom: 24px;
            font-size: 14px;
            font-weight: 600;
        }

        .security-status.trusted {
            background: rgba(72, 187, 120, 0.1);
            color: var(--success-color);
            border: 1px solid rgba(72, 187, 120, 0.2);
        }

        .security-status.risk {
            background: rgba(245, 101, 101, 0.1);
            color: var(--error-color);
            border: 1px solid rgba(245, 101, 101, 0.2);
        }

        /* 响应式设计 */
        @media (max-width: 480px) {
            .auth-container {
                margin: 10px;
                border-radius: var(--radius-md);
            }

            .auth-header {
                padding: 24px 20px;
            }

            .auth-content {
                padding: 24px 20px;
            }

            .user-info {
                padding: 16px;
            }

            .user-avatar {
                width: 50px;
                height: 50px;
                margin-right: 12px;
            }

            .user-name {
                font-size: 16px;
            }
        }

        /* 封号弹窗样式 */
        .ban-modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: 10000;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .ban-modal-overlay {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.7);
            backdrop-filter: blur(5px);
        }

        .ban-modal-content {
            position: relative;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
            max-width: 400px;
            width: 90%;
            overflow: hidden;
            animation: banModalSlideIn 0.3s ease-out;
        }

        @keyframes banModalSlideIn {
            from {
                opacity: 0;
                transform: scale(0.9) translateY(-20px);
            }
            to {
                opacity: 1;
                transform: scale(1) translateY(0);
            }
        }

        .ban-modal-header {
            position: relative;
            padding: 16px;
            text-align: right;
        }

        .ban-modal-close {
            background: #F3F4F6;
            border: none;
            color: #6B7280;
            font-size: 20px;
            cursor: pointer;
            padding: 0;
            width: 28px;
            height: 28px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
            transition: all 0.2s;
        }

        .ban-modal-close:hover {
            background: #E5E7EB;
            color: #374151;
        }

        .ban-modal-body {
            padding: 0 24px 24px 24px;
            text-align: center;
        }

        .ban-emoji-section {
            margin-bottom: 24px;
        }

        .ban-icon {
            width: 80px;
            height: 80px;
            margin: 0 auto 16px;
            background: linear-gradient(135deg, #EF4444, #DC2626);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 8px 20px rgba(239, 68, 68, 0.3);
        }

        .ban-icon i {
            font-size: 36px;
            color: white;
        }

        .ban-title {
            font-size: 20px;
            font-weight: 600;
            color: #1F2937;
            margin: 0;
        }

        .ban-info-section {
            background: #FEF2F2;
            border: 1px solid #FECACA;
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 24px;
            text-align: left;
        }

        .ban-info-item {
            margin-bottom: 12px;
            display: flex;
            align-items: flex-start;
            gap: 8px;
        }

        .ban-info-item:last-child {
            margin-bottom: 0;
        }

        .ban-info-label {
            font-weight: 600;
            color: #7F1D1D;
            min-width: 80px;
            flex-shrink: 0;
        }

        .ban-info-value {
            color: #991B1B;
            flex: 1;
            word-break: break-word;
        }

        .ban-actions {
            display: flex;
            gap: 12px;
            justify-content: center;
            margin-bottom: 16px;
        }

        .ban-action-btn {
            flex: 1;
            padding: 12px 16px;
            border: none;
            border-radius: 10px;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 6px;
            transition: all 0.2s;
        }

        .appeal-btn {
            background: linear-gradient(135deg, #10B981, #059669);
            color: white;
        }

        .appeal-btn:hover {
            background: linear-gradient(135deg, #059669, #047857);
            transform: translateY(-1px);
        }

        .service-btn {
            background: linear-gradient(135deg, #3B82F6, #2563EB);
            color: white;
        }

        .service-btn:hover {
            background: linear-gradient(135deg, #2563EB, #1D4ED8);
            transform: translateY(-1px);
        }

        .ban-footer-text {
            font-size: 12px;
            color: #6B7280;
            text-align: center;
        }

        /* 移动端适配 */
        @media (max-width: 768px) {
            .ban-modal-content {
                width: 95%;
                margin: 20px;
            }

            .ban-emoji {
                font-size: 48px;
            }

            .ban-title {
                font-size: 18px;
            }

            .ban-actions {
                flex-direction: column;
            }

            .ban-action-btn {
                flex: none;
            }
        }
    </style>
</head>
<body>
    <div class="login-container">
        <!-- 顶部安全区域 -->
        <div class="safe-area-top"></div>

        <!-- 返回按钮 -->
        <button class="back-button" id="backBtn" onclick="goBack()">
            <i class="fas fa-arrow-left"></i>
        </button>

        <!-- 主要内容区域 -->
        <div class="main-content">
            <!-- 品牌区域 -->
            <div class="brand-section">
                <div class="brand-logo">
                    <img src="https://s1.imagehub.cc/images/2025/05/26/2cdd63b39f96072fc60cd733027d2a30.png" alt="趣玩星球" style="width: 100%; height: 100%; object-fit: contain;">
                </div>
                <div class="brand-text">
                    <img src="https://s1.imagehub.cc/images/2025/05/26/ef3f8c98828faa0de2111ac0cfc9bd6d.png" alt="趣玩星球 - 探索有趣的生活">
                </div>
            </div>

            <?php if ($quick_login_user): ?>
            <!-- 快速登录区域 -->
            <div class="quick-login-section" id="quickLoginSection">
                <div class="quick-login-card">
                    <div class="quick-login-avatar">
                        <img src="<?php echo htmlspecialchars($quick_login_user['avatar'] ?: 'https://s1.imagehub.cc/images/2025/05/26/2cdd63b39f96072fc60cd733027d2a30.png'); ?>" alt="用户头像">
                    </div>
                    <div class="quick-login-info">
                        <div class="quick-login-nickname"><?php echo htmlspecialchars($quick_login_user['nickname'] ?: $quick_login_user['username']); ?></div>
                        <div class="quick-login-id">趣玩ID: <?php echo htmlspecialchars($quick_login_user['quwan_id']); ?></div>
                    </div>
                    <button type="button" class="btn btn-primary quick-login-btn" onclick="quickLogin()">
                        <i class="fas fa-sign-in-alt"></i>
                        一键登录
                    </button>
                    <button type="button" class="btn btn-secondary switch-account-btn" onclick="switchAccount()">
                        <i class="fas fa-user-plus"></i>
                        切换账号
                    </button>
                </div>
            </div>
            <?php endif; ?>

            <!-- 表单区域 -->
            <div class="form-section">
                <!-- 错误和成功消息 -->
                <div class="error-message" id="errorMessage"></div>
                <div class="success-message" id="successMessage"></div>

                <!-- 第一步：输入手机号 -->
                <div class="auth-step" id="phoneStep">
                    <div class="floating-input">
                        <input type="tel" id="phoneInput" placeholder=" " maxlength="11" inputmode="numeric">
                        <label for="phoneInput">手机号</label>
                    </div>

                    <!-- 协议勾选 -->
                    <div class="agreement-section">
                        <label class="checkbox-container" for="agreementCheckbox">
                            <div class="custom-checkbox">
                                <input type="checkbox" id="agreementCheckbox" required checked>
                                <div class="checkbox-mark"></div>
                            </div>
                            <div class="agreement-text">
                                我已阅读并同意
                                <a href="../policies/user_agreement.php?from=login" target="_blank" class="agreement-link">《趣玩星球用户协议》</a>
                                和
                                <a href="../policies/privacy_policy.php?from=login" target="_blank" class="agreement-link">《趣玩星球隐私政策》</a>
                            </div>
                        </label>
                    </div>

                    <button type="button" class="btn btn-primary" id="continueBtn">
                        <i class="fas fa-arrow-right"></i>
                        继续
                    </button>
                </div>

                <!-- 加载状态 -->
                <div class="loading" id="loadingStep">
                    <div class="star-loader">
                        <div class="star star-1"></div>
                        <div class="star star-2"></div>
                        <div class="star star-3"></div>
                    </div>
                    <p id="loadingText">正在检测账号状态...</p>
                </div>

                <!-- 其他步骤将通过JavaScript动态加载 -->
            </div>
        </div>

        <!-- 底部安全区域 -->
        <div class="safe-area-bottom"></div>
    </div>

    <!-- 短信弹窗 -->
    <div class="sms-modal" id="sms-modal">
        <div class="sms-content">
            <div class="sms-header">【趣玩星球】</div>
            <div class="sms-body">
                Hi~ 欢迎回到趣玩星球！您的验证码：
                <div class="verification-code-display" id="verification-code-display">------</div>
                验证码有效期为5分钟，请勿将验证码泄露给他人，以免造成不必要的损失。如非本人操作，请忽略此短信。
            </div>
            <button type="button" class="btn btn-primary" onclick="copyAndFillCode()" style="margin-bottom: 12px;">
                <i class="fas fa-copy"></i>
                复制并填入验证码
            </button>
            <button type="button" class="btn btn-secondary" onclick="closeSmsModal()">
                关闭
            </button>
        </div>
    </div>

    <!-- 封号详情弹窗 -->
    <div id="banModal" class="ban-modal" style="display: none;">
        <div class="ban-modal-overlay"></div>
        <div class="ban-modal-content">
            <div class="ban-modal-header">
                <button class="ban-modal-close" onclick="closeBanModal()">&times;</button>
            </div>
            <div class="ban-modal-body">
                <!-- 封禁图标和标题 -->
                <div class="ban-emoji-section">
                    <div class="ban-icon">
                        <i class="fas fa-ban"></i>
                    </div>
                    <h3 class="ban-title">账户已被封禁</h3>
                </div>

                <!-- 封禁信息 -->
                <div class="ban-info-section">
                    <div class="ban-info-item">
                        <span class="ban-info-label">封禁原因：</span>
                        <span class="ban-info-value" id="banReason">-</span>
                    </div>
                    <div class="ban-info-item">
                        <span class="ban-info-label">封禁时间：</span>
                        <span class="ban-info-value" id="banTime">-</span>
                    </div>
                    <div class="ban-info-item">
                        <span class="ban-info-label">到期时间：</span>
                        <span class="ban-info-value" id="banEndTime">-</span>
                    </div>
                </div>

                <!-- 操作按钮 -->
                <div class="ban-actions">
                    <button class="ban-action-btn appeal-btn" onclick="goToAppeal()">
                        <i class="fas fa-file-alt"></i>
                        提交申诉
                    </button>
                    <button class="ban-action-btn service-btn" onclick="contactService()">
                        <i class="fas fa-headset"></i>
                        在线客服
                    </button>
                </div>

                <div class="ban-footer-text">
                    申诉审核时间：3-5个工作日
                </div>
            </div>
        </div>
    </div>

    <script>
        // 全局变量
        let currentStep = 1;
        let userPhone = '';
        let userExists = false;
        let securityResult = null;
        let currentUser = null;

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            initializeAuth();
        });

        function initializeAuth() {
            // 绑定事件
            document.getElementById('continueBtn').addEventListener('click', handleContinue);
            document.getElementById('phoneInput').addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    handleContinue();
                }
            });

            // 手机号输入限制
            document.getElementById('phoneInput').addEventListener('input', function(e) {
                let value = e.target.value.replace(/[^0-9]/g, '');
                e.target.value = value;
                checkFormValidity();
            });

            // 协议勾选事件
            document.getElementById('agreementCheckbox').addEventListener('change', checkFormValidity);
        }

        function checkFormValidity() {
            const phone = document.getElementById('phoneInput').value.trim();
            const agreementChecked = document.getElementById('agreementCheckbox').checked;
            const continueBtn = document.getElementById('continueBtn');

            const isValid = validatePhone(phone) && agreementChecked;
            continueBtn.disabled = !isValid;
        }

        async function handleContinue() {
            const phone = document.getElementById('phoneInput').value.trim();
            const agreementChecked = document.getElementById('agreementCheckbox').checked;

            if (!validatePhone(phone)) {
                showError('请输入正确的手机号');
                return;
            }

            if (!agreementChecked) {
                showError('请先同意用户协议和隐私政策');
                return;
            }

            userPhone = phone;
            showLoading('正在检测账号状态...');

            try {
                // 检测用户是否存在
                const response = await fetch('check_user.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ phone: phone })
                });

                const result = await response.json();

                if (result.success) {
                    userExists = result.exists;

                    if (userExists) {
                        // 用户已存在，进入登录流程
                        await handleExistingUser(result.user);
                    } else {
                        // 用户不存在，显示Toast提示并进入注册流程
                        showToast('新用户，将自动注册');
                        setTimeout(() => {
                            handleNewUser();
                        }, 1500);
                    }
                } else {
                    // 检查是否是封号错误
                    if (result.banned && result.banInfo) {
                        showBanModal(result.banInfo);
                    } else {
                        showError(result.message || '检测失败，请重试');
                    }
                    hideLoading();
                }
            } catch (error) {
                console.error('检测失败:', error);
                showError('网络错误，请重试');
                hideLoading();
            }
        }

        function validatePhone(phone) {
            const phoneRegex = /^1[3-9]\d{9}$/;
            return phoneRegex.test(phone);
        }

        function showError(message) {
            const errorEl = document.getElementById('errorMessage');
            errorEl.textContent = message;
            errorEl.style.display = 'block';
            setTimeout(() => {
                errorEl.style.display = 'none';
            }, 3000);
        }

        function showSuccess(message) {
            const successEl = document.getElementById('successMessage');
            successEl.textContent = message;
            successEl.style.display = 'block';
            setTimeout(() => {
                successEl.style.display = 'none';
            }, 3000);
        }

        // Toast提示功能
        function showToast(message) {
            // 移除已存在的toast
            const existingToast = document.querySelector('.toast');
            if (existingToast) {
                existingToast.remove();
            }

            // 创建新的toast
            const toast = document.createElement('div');
            toast.className = 'toast';
            toast.textContent = message;
            document.body.appendChild(toast);

            // 显示toast
            setTimeout(() => {
                toast.classList.add('show');
            }, 100);

            // 3秒后隐藏toast
            setTimeout(() => {
                toast.classList.remove('show');
                setTimeout(() => {
                    if (toast.parentNode) {
                        toast.parentNode.removeChild(toast);
                    }
                }, 300);
            }, 3000);
        }

        function showLoading(text) {
            document.getElementById('loadingText').textContent = text;
            document.getElementById('phoneStep').style.display = 'none';
            document.getElementById('loadingStep').style.display = 'block';
        }

        function hideLoading() {
            document.getElementById('loadingStep').style.display = 'none';
            document.getElementById('phoneStep').style.display = 'block';
        }

        function goBack() {
            // 检查当前步骤，返回到相应的上一步
            if (document.getElementById('verificationStep')) {
                // 如果在验证码输入步骤，返回到用户信息显示步骤
                if (currentUser) {
                    showSecureLogin(currentUser);
                } else {
                    // 如果没有用户信息，返回到第一步
                    document.getElementById('phoneStep').style.display = 'block';
                    document.getElementById('backBtn').style.display = 'none';
                    hideError();
                }
            } else if (document.getElementById('quickLoginStep') || document.getElementById('secureLoginStep')) {
                // 如果在快速登录或安全登录步骤，返回到第一步
                document.querySelector('.form-section').innerHTML = `
                    <!-- 错误和成功消息 -->
                    <div class="error-message" id="errorMessage"></div>
                    <div class="success-message" id="successMessage"></div>

                    <!-- 第一步：输入手机号 -->
                    <div class="auth-step" id="phoneStep">
                        <div class="floating-input">
                            <input type="tel" id="phoneInput" placeholder=" " maxlength="11" inputmode="numeric" value="${userPhone}">
                            <label for="phoneInput">手机号</label>
                        </div>

                        <!-- 协议勾选 -->
                        <div class="agreement-section">
                            <label class="checkbox-container" for="agreementCheckbox">
                                <div class="custom-checkbox">
                                    <input type="checkbox" id="agreementCheckbox" required checked>
                                    <div class="checkbox-mark"></div>
                                </div>
                                <div class="agreement-text">
                                    我已阅读并同意
                                    <a href="../policies/user_agreement.php?from=login" target="_blank" class="agreement-link">《趣玩星球用户协议》</a>
                                    和
                                    <a href="../policies/privacy_policy.php?from=login" target="_blank" class="agreement-link">《趣玩星球隐私政策》</a>
                                </div>
                            </label>
                        </div>

                        <button type="button" class="btn btn-primary" id="continueBtn">
                            <i class="fas fa-arrow-right"></i>
                            继续
                        </button>
                    </div>
                `;

                // 重新绑定事件
                document.getElementById('continueBtn').addEventListener('click', handleContinue);
                document.getElementById('phoneInput').addEventListener('keypress', function(e) {
                    if (e.key === 'Enter') {
                        handleContinue();
                    }
                });
                document.getElementById('phoneInput').addEventListener('input', function(e) {
                    let value = e.target.value.replace(/[^0-9]/g, '');
                    e.target.value = value;
                    checkFormValidity();
                });
                document.getElementById('agreementCheckbox').addEventListener('change', checkFormValidity);

                // 隐藏返回按钮
                document.getElementById('backBtn').style.display = 'none';

                // 清空错误信息
                hideError();

                // 重置用户数据
                currentStep = 1;
                userPhone = '';
                userExists = false;
                securityResult = null;
                currentUser = null;
            } else {
                // 如果在第一步或其他情况，使用浏览器返回
                window.history.back();
            }
        }

        // 处理已存在用户（登录流程）
        async function handleExistingUser(user) {
            showLoading('正在进行安全检测...');

            try {
                // 调用安全检测API（复用智能登录的逻辑）
                const response = await fetch('security_check_simple.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ identifier: userPhone })
                });

                const result = await response.json();

                if (result.success) {
                    securityResult = result.security;
                    currentUser = user; // 保存用户信息

                    if (securityResult.trusted) {
                        // 环境安全，显示快速登录
                        showQuickLogin(user);
                    } else {
                        // 环境异常，需要验证码登录
                        showSecureLogin(user);
                    }
                } else {
                    showError(result.message || '安全检测失败');
                    hideLoading();
                }
            } catch (error) {
                console.error('安全检测失败:', error);
                showError('网络错误，请重试');
                hideLoading();
            }
        }

        // 显示快速登录界面
        function showQuickLogin(user) {
            hideLoading();

            const content = `
                <div class="auth-step" id="quickLoginStep">
                    <div class="user-info">
                        <div class="user-avatar">
                            <img src="${user.avatar}" alt="头像" onerror="this.src='https://s1.imagehub.cc/images/2025/04/26/f5c2f26c4564aabcbb56c5fd30f1d652.jpg'">
                        </div>
                        <div class="user-details">
                            <div class="user-name">${user.nickname || user.username}</div>
                            <div class="user-id">趣玩ID: ${user.quwan_id}</div>
                        </div>
                    </div>

                    <div class="security-status trusted">
                        <i class="fas fa-shield-check"></i>
                        <span>可信环境</span>
                    </div>

                    <button type="button" class="btn btn-primary" onclick="quickLogin(${user.id})">
                        <i class="fas fa-bolt"></i>
                        快速登录
                    </button>
                </div>
            `;

            document.querySelector('.form-section').innerHTML = `
                <div class="error-message" id="errorMessage"></div>
                <div class="success-message" id="successMessage"></div>
                ${content}
            `;
            document.getElementById('backBtn').style.display = 'block';
        }

        // 显示安全登录界面
        function showSecureLogin(user) {
            hideLoading();

            const content = `
                <div class="auth-step" id="secureLoginStep">
                    <div class="user-info">
                        <div class="user-avatar">
                            <img src="${user.avatar}" alt="头像" onerror="this.src='https://s1.imagehub.cc/images/2025/04/26/f5c2f26c4564aabcbb56c5fd30f1d652.jpg'">
                        </div>
                        <div class="user-details">
                            <div class="user-name">${user.nickname || user.username}</div>
                            <div class="user-id">趣玩ID: ${user.quwan_id}</div>
                        </div>
                    </div>

                    <div class="security-status risk">
                        <i class="fas fa-shield-exclamation"></i>
                        <span>需要验证</span>
                    </div>

                    <button type="button" class="btn btn-primary" onclick="sendLoginVerificationCode()">
                        <i class="fas fa-paper-plane"></i>
                        发送验证码
                    </button>
                </div>
            `;

            document.querySelector('.form-section').innerHTML = `
                <div class="error-message" id="errorMessage"></div>
                <div class="success-message" id="successMessage"></div>
                ${content}
            `;
            document.getElementById('backBtn').style.display = 'block';
        }

        // 快速登录
        async function quickLogin(userId) {
            try {
                const response = await fetch('quick_login_simple.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ user_id: userId })
                });

                const result = await response.json();

                if (result.success) {
                    showSuccess('登录成功！');
                    setTimeout(() => {
                        window.location.href = '../home/<USER>';
                    }, 1000);
                } else {
                    showError(result.message || '登录失败');
                }
            } catch (error) {
                console.error('快速登录失败:', error);
                showError('网络错误，请重试');
            }
        }

        // 发送登录验证码
        async function sendLoginVerificationCode() {
            const btn = document.querySelector('#secureLoginStep .btn-primary');
            if (!btn) return;

            const originalText = btn.innerHTML;
            btn.disabled = true;
            btn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 发送中...';

            try {
                const response = await fetch('send_verification.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: `phone=${encodeURIComponent(userPhone)}`
                });

                const result = await response.json();

                if (result.success) {
                    showSuccess('验证码已发送，请查收短信');

                    // 显示验证码输入界面
                    showVerificationInput();

                    // 开始倒计时 - 注意这里的btn是发送验证码按钮，需要找到重新发送按钮
                    setTimeout(() => {
                        const resendBtn = document.getElementById('resendCodeBtn');
                        if (resendBtn) {
                            startSendCodeCountdown(resendBtn);
                        }
                    }, 100);

                    // 如果有调试验证码，更新弹窗并延迟显示
                    if (result.debug_code) {
                        currentVerificationCode = result.debug_code;
                        updateSmsModal(result.debug_code);

                        // 3秒后自动显示短信弹窗
                        setTimeout(() => {
                            showSmsModal();
                        }, 3000);
                    }
                } else {
                    showError(result.message || '发送失败，请重试');
                    btn.disabled = false;
                    btn.innerHTML = originalText;
                }
            } catch (error) {
                console.error('发送验证码失败:', error);
                showError('网络错误，请重试');
                btn.disabled = false;
                btn.innerHTML = originalText;
            }
        }

        // 显示验证码输入界面
        function showVerificationInput() {
            const content = `
                <div class="auth-step" id="verificationStep">
                    <div class="form-group">
                        <label class="form-label">输入验证码</label>
                        <p style="font-size: 14px; color: var(--text-secondary); margin-bottom: 16px;">
                            验证码已发送至 ${userPhone.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2')}
                        </p>

                        <!-- 6位验证码输入框 -->
                        <div class="verification-inputs">
                            <input type="tel" class="verification-input" maxlength="1" data-index="0" inputmode="numeric" pattern="[0-9]" autocomplete="off">
                            <input type="tel" class="verification-input" maxlength="1" data-index="1" inputmode="numeric" pattern="[0-9]" autocomplete="off">
                            <input type="tel" class="verification-input" maxlength="1" data-index="2" inputmode="numeric" pattern="[0-9]" autocomplete="off">
                            <input type="tel" class="verification-input" maxlength="1" data-index="3" inputmode="numeric" pattern="[0-9]" autocomplete="off">
                            <input type="tel" class="verification-input" maxlength="1" data-index="4" inputmode="numeric" pattern="[0-9]" autocomplete="off">
                            <input type="tel" class="verification-input" maxlength="1" data-index="5" inputmode="numeric" pattern="[0-9]" autocomplete="off">
                        </div>

                        <!-- 发送验证码和查看验证码按钮组 -->
                        <div style="display: flex; gap: 8px; margin-bottom: 16px; justify-content: center;">
                            <button type="button" class="btn btn-secondary btn-small" id="resendCodeBtn" onclick="sendLoginVerificationCode()">
                                <i class="fas fa-paper-plane"></i>
                                重新发送
                            </button>
                            <button type="button" class="btn btn-secondary btn-small" onclick="showSmsModal()">
                                <i class="fas fa-sms"></i>
                                查看短信
                            </button>
                        </div>
                    </div>

                    <!-- 密码输入框 -->
                    <div class="form-group">
                        <label class="form-label" for="loginPassword">登录密码</label>
                        <input type="password" id="loginPassword" class="form-input" placeholder="请输入登录密码" autocomplete="current-password">
                    </div>

                    <button type="button" class="btn btn-primary" onclick="verifyLoginCode()">
                        <i class="fas fa-sign-in-alt"></i>
                        验证并登录
                    </button>
                </div>
            `;

            document.querySelector('.form-section').innerHTML = `
                <div class="error-message" id="errorMessage"></div>
                <div class="success-message" id="successMessage"></div>
                ${content}
            `;

            // 绑定验证码输入框事件
            bindVerificationInputs();

            // 显示返回按钮
            document.getElementById('backBtn').style.display = 'block';
        }

        // 绑定验证码输入框事件
        function bindVerificationInputs() {
            const inputs = document.querySelectorAll('.verification-input');
            inputs.forEach((input, index) => {
                input.addEventListener('input', (e) => {
                    let value = e.target.value.replace(/[^0-9]/g, '');
                    if (value.length > 1) value = value.charAt(0);
                    e.target.value = value;

                    if (value) {
                        e.target.style.borderColor = 'var(--primary-color)';
                        e.target.style.background = 'rgba(111, 123, 245, 0.05)';
                        if (index < inputs.length - 1) {
                            setTimeout(() => inputs[index + 1].focus(), 50);
                        }
                    } else {
                        e.target.style.borderColor = '#E5E7EB';
                        e.target.style.background = 'var(--bg-white)';
                    }
                });

                input.addEventListener('keydown', (e) => {
                    if (e.key === 'Backspace' && !e.target.value && index > 0) {
                        setTimeout(() => {
                            inputs[index - 1].focus();
                            inputs[index - 1].value = '';
                            inputs[index - 1].style.borderColor = '#E5E7EB';
                            inputs[index - 1].style.background = 'var(--bg-white)';
                        }, 50);
                    }
                });

                input.addEventListener('paste', (e) => {
                    e.preventDefault();
                    const paste = e.clipboardData.getData('text').replace(/[^0-9]/g, '');
                    if (paste.length >= 6) {
                        inputs.forEach((inp, i) => {
                            if (i < 6 && paste[i]) {
                                inp.value = paste[i];
                                inp.style.borderColor = 'var(--primary-color)';
                                inp.style.background = 'rgba(111, 123, 245, 0.05)';
                            }
                        });
                        inputs[5].focus();
                    }
                });
            });
        }

        // 开始发送验证码倒计时
        function startSendCodeCountdown(btn) {
            let countdown = 60;
            btn.disabled = true;
            // 移除激活状态的样式
            btn.classList.remove('btn-resend-active');

            const timer = setInterval(() => {
                btn.innerHTML = `<i class="fas fa-clock" style="margin-right: 4px;"></i>${countdown}s`;
                countdown--;

                if (countdown < 0) {
                    clearInterval(timer);
                    btn.disabled = false;
                    btn.innerHTML = '<i class="fas fa-paper-plane" style="margin-right: 4px;"></i>重新发送';
                    // 添加激活状态的样式
                    btn.classList.add('btn-resend-active');
                }
            }, 1000);
        }

        // 验证登录验证码
        async function verifyLoginCode() {
            const inputs = document.querySelectorAll('.verification-input');
            const code = Array.from(inputs).map(input => input.value).join('');
            const password = document.getElementById('loginPassword').value.trim();

            if (code.length !== 6) {
                showError('请输入完整的6位验证码');
                return;
            }

            if (!password) {
                showError('请输入登录密码');
                return;
            }

            const btn = event.target;
            btn.disabled = true;
            btn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 验证中...';

            try {
                const response = await fetch('secure_login_simple.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        user_id: currentUser ? currentUser.id : null,
                        verification_code: code,
                        password: password
                    })
                });

                const result = await response.json();

                if (result.success) {
                    showSuccess('登录成功！');
                    setTimeout(() => {
                        window.location.href = result.redirect || '../home/<USER>';
                    }, 1000);
                } else {
                    // 检查是否是封号错误
                    if (result.banned && result.banInfo) {
                        showBanModal(result.banInfo);
                    } else {
                        showError(result.message || '验证失败');
                    }
                    btn.disabled = false;
                    btn.innerHTML = '<i class="fas fa-sign-in-alt"></i> 验证并登录';
                }
            } catch (error) {
                console.error('验证失败:', error);
                showError('网络错误，请重试');
                btn.disabled = false;
                btn.innerHTML = '<i class="fas fa-sign-in-alt"></i> 验证并登录';
            }
        }

        // 添加hideError函数
        function hideError() {
            const errorEl = document.getElementById('errorMessage');
            if (errorEl) {
                errorEl.style.display = 'none';
            }
        }

        // 短信弹窗相关函数
        let currentVerificationCode = '';

        function showSmsModal() {
            document.getElementById('sms-modal').style.display = 'flex';
        }

        function closeSmsModal() {
            document.getElementById('sms-modal').style.display = 'none';
        }

        function updateSmsModal(code) {
            currentVerificationCode = code;
            document.getElementById('verification-code-display').textContent = code;
        }

        function copyAndFillCode() {
            const code = currentVerificationCode;
            const inputs = document.querySelectorAll('.verification-input');

            // 填入验证码
            code.split('').forEach((digit, index) => {
                if (inputs[index]) {
                    inputs[index].value = digit;
                    inputs[index].style.borderColor = 'var(--primary-color)';
                    inputs[index].style.background = 'rgba(111, 123, 245, 0.05)';
                }
            });

            // 关闭弹窗
            closeSmsModal();

            // 聚焦到最后一个输入框
            if (inputs[5]) {
                inputs[5].focus();
            }
        }

        // 处理新用户（注册流程）
        function handleNewUser() {
            // 将手机号存储到sessionStorage，供注册页面使用
            sessionStorage.setItem('register_phone', userPhone);

            // 跳转到注册验证码页面
            window.location.href = `../register/verify.php?phone=${encodeURIComponent(userPhone)}`;
        }

        // 快速登录功能
        function quickLogin() {
            console.log('快速登录函数被调用');

            const quickLoginBtn = document.querySelector('.quick-login-btn');
            if (!quickLoginBtn) {
                console.error('未找到快速登录按钮');
                return;
            }

            quickLoginBtn.disabled = true;
            quickLoginBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 登录中...';

            console.log('发送快速登录请求...');

            fetch('quick_login_token.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    action: 'quick_login'
                })
            })
            .then(response => {
                console.log('快速登录响应状态:', response.status);
                return response.json();
            })
            .then(data => {
                console.log('快速登录响应数据:', data);

                if (data.success) {
                    showToast('登录成功！欢迎回来');
                    setTimeout(() => {
                        window.location.href = '../home/<USER>';
                    }, 1000);
                } else {
                    // 检查是否是封号错误
                    if (data.banned && data.banInfo) {
                        showBanModal(data.banInfo);
                    } else {
                        showToast(data.message || '快速登录失败，请重新登录');
                        switchAccount();
                    }
                }
            })
            .catch(error => {
                console.error('快速登录失败:', error);
                showToast('网络错误，请重试');
                switchAccount();
            })
            .finally(() => {
                quickLoginBtn.disabled = false;
                quickLoginBtn.innerHTML = '<i class="fas fa-sign-in-alt"></i> 一键登录';
            });
        }

        // 切换账号功能
        function switchAccount() {
            // 清除快速登录cookie
            document.cookie = 'quick_login_token=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;';

            // 隐藏快速登录区域
            const quickLoginSection = document.getElementById('quickLoginSection');
            if (quickLoginSection) {
                quickLoginSection.style.display = 'none';
            }

            // 显示正常登录表单
            const formSection = document.querySelector('.form-section');
            if (formSection) {
                formSection.style.display = 'block';
            }
        }

        // 页面加载时处理快速登录显示
        document.addEventListener('DOMContentLoaded', function() {
            // 如果有快速登录，隐藏正常登录表单
            const quickLoginSection = document.getElementById('quickLoginSection');
            const formSection = document.querySelector('.form-section');
            if (quickLoginSection && formSection) {
                formSection.style.display = 'none';
            }
        });

        // 显示封号弹窗
        function showBanModal(banInfo) {
            hideLoading();

            console.log('封号信息:', banInfo); // 调试信息

            // 填充封号信息
            document.getElementById('banReason').textContent = banInfo.reason || '违反平台规定';
            document.getElementById('banTime').textContent = formatDateTime(banInfo.ban_time) || '-';

            // 显示到期时间
            let endTimeText = '';
            if (banInfo.is_permanent || !banInfo.end_time) {
                endTimeText = '永久';
            } else {
                endTimeText = formatDateTime(banInfo.end_time);
            }
            document.getElementById('banEndTime').textContent = endTimeText;

            // 显示弹窗
            document.getElementById('banModal').style.display = 'flex';

            // 阻止页面滚动
            document.body.style.overflow = 'hidden';
        }

        // 关闭封号弹窗
        function closeBanModal() {
            document.getElementById('banModal').style.display = 'none';
            document.body.style.overflow = 'auto';
        }

        // 跳转到申诉页面
        function goToAppeal() {
            showToast('正在跳转到申诉页面...');
            setTimeout(() => {
                // 获取当前用户手机号
                const phone = userPhone || (currentUser && currentUser.phone) || '';
                const url = phone ? `../appeal/index.php?phone=${encodeURIComponent(phone)}` : '../appeal/index.php';
                window.location.href = url;
            }, 1000);
        }

        // 联系在线客服
        function contactService() {
            showToast('正在为您转接在线客服...');
            setTimeout(() => {
                window.location.href = '../customer_service/chat.php';
            }, 1000);
        }

        // 兼容旧浏览器的复制方法
        function fallbackCopyTextToClipboard(text) {
            const textArea = document.createElement("textarea");
            textArea.value = text;
            textArea.style.top = "0";
            textArea.style.left = "0";
            textArea.style.position = "fixed";

            document.body.appendChild(textArea);
            textArea.focus();
            textArea.select();

            try {
                const successful = document.execCommand('copy');
                if (successful) {
                    showToast('客服QQ已复制：' + text);
                } else {
                    showToast('复制失败，客服QQ：' + text);
                }
            } catch (err) {
                showToast('复制失败，客服QQ：' + text);
            }

            document.body.removeChild(textArea);
        }

        // 格式化日期时间
        function formatDateTime(dateTimeString) {
            if (!dateTimeString) return '';

            try {
                const date = new Date(dateTimeString);
                const year = date.getFullYear();
                const month = String(date.getMonth() + 1).padStart(2, '0');
                const day = String(date.getDate()).padStart(2, '0');
                const hours = String(date.getHours()).padStart(2, '0');
                const minutes = String(date.getMinutes()).padStart(2, '0');

                return `${year}-${month}-${day} ${hours}:${minutes}`;
            } catch (error) {
                return dateTimeString;
            }
        }

    </script>
</body>
</html>