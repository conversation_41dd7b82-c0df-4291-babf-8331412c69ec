<?php
/**
 * 趣玩星球管理后台 - 数据统计仪表盘
 * 现代化、实用性、年轻化设计 v4.0
 */

session_start();

// 登录检查
if (!isset($_SESSION['admin_id'])) {
    header('Location: login.php');
    exit;
}

$admin_name = $_SESSION['admin_name'] ?? '管理员';
$admin_id = $_SESSION['admin_id'] ?? '';

// 获取实时统计数据
require_once 'db_config.php';

try {
    $pdo = getDbConnection();

    // 总用户数
    $stmt = $pdo->query("SELECT COUNT(*) as total_users FROM users");
    $total_users = $stmt->fetch()['total_users'] ?? 0;

    // 待审核认证数
    $stmt = $pdo->query("SELECT COUNT(*) as pending_verifications FROM real_name_verification WHERE status = 'pending'");
    $pending_verifications = $stmt->fetch()['pending_verifications'] ?? 0;

    // 今日新增用户
    $stmt = $pdo->query("SELECT COUNT(*) as today_users FROM users WHERE DATE(created_at) = CURDATE()");
    $today_users = $stmt->fetch()['today_users'] ?? 0;

    // 已认证用户数
    $stmt = $pdo->query("SELECT COUNT(*) as verified_users FROM real_name_verification WHERE status = 'approved'");
    $verified_users = $stmt->fetch()['verified_users'] ?? 0;

    // 本周新增用户
    $stmt = $pdo->query("SELECT COUNT(*) as week_users FROM users WHERE YEARWEEK(created_at, 1) = YEARWEEK(CURDATE(), 1)");
    $week_users = $stmt->fetch()['week_users'] ?? 0;

    // 本月新增用户
    $stmt = $pdo->query("SELECT COUNT(*) as month_users FROM users WHERE YEAR(created_at) = YEAR(CURDATE()) AND MONTH(created_at) = MONTH(CURDATE())");
    $month_users = $stmt->fetch()['month_users'] ?? 0;

    // 最近7天每日新增用户数据（用于图表）
    $daily_stats = [];
    for ($i = 6; $i >= 0; $i--) {
        $date = date('Y-m-d', strtotime("-$i days"));
        $stmt = $pdo->prepare("SELECT COUNT(*) as count FROM users WHERE DATE(created_at) = ?");
        $stmt->execute([$date]);
        $count = $stmt->fetch()['count'] ?? 0;
        $daily_stats[] = [
            'date' => $date,
            'count' => $count,
            'label' => date('m/d', strtotime($date))
        ];
    }

    // 认证状态统计
    $stmt = $pdo->query("
        SELECT
            status,
            COUNT(*) as count
        FROM real_name_verification
        GROUP BY status
    ");
    $verification_stats = [];
    while ($row = $stmt->fetch()) {
        $verification_stats[$row['status']] = $row['count'];
    }

} catch (Exception $e) {
    // 如果数据库连接失败，使用默认值
    $total_users = 0;
    $pending_verifications = 0;
    $today_users = 0;
    $verified_users = 0;
    $week_users = 0;
    $month_users = 0;
    $daily_stats = [];
    $verification_stats = [];
}
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数据统计 - 趣玩星球管理后台</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="assets/css/admin-layout.css">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        /* 仪表盘专用样式 */
        .dashboard-content {
            padding: 24px;
        }

        .page-header {
            margin-bottom: 32px;
        }

        .page-title {
            font-size: 2rem;
            font-weight: 700;
            color: var(--gray-800);
            margin-bottom: 8px;
        }

        .page-subtitle {
            color: var(--gray-600);
            font-size: 1.1rem;
        }

        /* 统计卡片 */
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 20px;
            margin-bottom: 32px;
        }

        .stat-card {
            background: white;
            border-radius: 16px;
            padding: 24px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .stat-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, var(--primary-color), var(--primary-light));
        }

        .stat-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 12px 32px rgba(0, 0, 0, 0.15);
        }

        .stat-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 16px;
        }

        .stat-icon {
            width: 56px;
            height: 56px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            color: white;
        }

        .stat-trend {
            display: flex;
            align-items: center;
            gap: 4px;
            font-size: 12px;
            font-weight: 600;
            padding: 4px 8px;
            border-radius: 20px;
        }

        .stat-trend.up {
            background: rgba(16, 185, 129, 0.1);
            color: var(--success-color);
        }

        .stat-trend.neutral {
            background: rgba(107, 114, 128, 0.1);
            color: var(--gray-600);
        }

        .stat-number {
            font-size: 2.5rem;
            font-weight: 700;
            color: var(--gray-800);
            margin-bottom: 4px;
            line-height: 1;
        }

        .stat-label {
            font-size: 0.875rem;
            color: var(--gray-600);
            font-weight: 500;
        }

        .stat-description {
            font-size: 0.75rem;
            color: var(--gray-500);
            margin-top: 8px;
        }

        /* 图表区域 */
        .charts-section {
            display: grid;
            grid-template-columns: 2fr 1fr;
            gap: 24px;
            margin-bottom: 32px;
        }

        .chart-card {
            background: white;
            border-radius: 16px;
            padding: 24px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
        }

        .chart-header {
            margin-bottom: 20px;
            padding-bottom: 16px;
            border-bottom: 2px solid var(--gray-100);
        }

        .chart-title {
            font-size: 1.25rem;
            font-weight: 700;
            color: var(--gray-800);
            margin-bottom: 4px;
        }

        .chart-subtitle {
            font-size: 0.875rem;
            color: var(--gray-500);
        }

        .chart-container {
            position: relative;
            height: 300px;
        }

        /* 响应式设计 */
        @media (max-width: 1024px) {
            .charts-section {
                grid-template-columns: 1fr;
            }

            .stats-grid {
                grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            }
        }

        @media (max-width: 768px) {
            .dashboard-content {
                padding: 16px;
            }

            .stats-grid {
                grid-template-columns: 1fr;
                gap: 16px;
            }

            .chart-container {
                height: 250px;
            }
        }
    </style>
</head>
<body>
    <div class="admin-layout">
        <!-- 左侧菜单 -->
        <?php include 'includes/sidebar.php'; ?>

        <!-- 主内容区域 -->
        <main class="main-content">
            <!-- 顶部导航栏 -->
            <?php include 'includes/topbar.php'; ?>

            <div class="dashboard-content">
                <!-- 页面标题 -->
                <div class="page-header">
                    <h1 class="page-title">数据统计仪表盘</h1>
                    <p class="page-subtitle">实时监控平台运营数据和用户增长趋势</p>
                </div>

                <!-- 统计卡片 -->
                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-header">
                            <div class="stat-icon" style="background: linear-gradient(135deg, var(--primary-color), var(--primary-light));">
                                <i class="fas fa-users"></i>
                            </div>
                            <div class="stat-trend up">
                                <i class="fas fa-arrow-up"></i>
                                <span>总计</span>
                            </div>
                        </div>
                        <div class="stat-number"><?php echo number_format($total_users); ?></div>
                        <div class="stat-label">总用户数</div>
                        <div class="stat-description">平台注册用户总数</div>
                    </div>

                    <div class="stat-card">
                        <div class="stat-header">
                            <div class="stat-icon" style="background: linear-gradient(135deg, var(--warning-color), #FBBF24);">
                                <i class="fas fa-clock"></i>
                            </div>
                            <?php if ($pending_verifications > 0): ?>
                                <div class="stat-trend up">
                                    <i class="fas fa-exclamation-triangle"></i>
                                    <span>待处理</span>
                                </div>
                            <?php else: ?>
                                <div class="stat-trend neutral">
                                    <i class="fas fa-check"></i>
                                    <span>无待处理</span>
                                </div>
                            <?php endif; ?>
                        </div>
                        <div class="stat-number"><?php echo $pending_verifications; ?></div>
                        <div class="stat-label">待审核认证</div>
                        <div class="stat-description">需要审核的实名认证申请</div>
                    </div>

                    <div class="stat-card">
                        <div class="stat-header">
                            <div class="stat-icon" style="background: linear-gradient(135deg, var(--info-color), #60A5FA);">
                                <i class="fas fa-user-plus"></i>
                            </div>
                            <div class="stat-trend up">
                                <i class="fas fa-calendar-day"></i>
                                <span>今日</span>
                            </div>
                        </div>
                        <div class="stat-number"><?php echo $today_users; ?></div>
                        <div class="stat-label">今日新增</div>
                        <div class="stat-description">今天新注册的用户数</div>
                    </div>

                    <div class="stat-card">
                        <div class="stat-header">
                            <div class="stat-icon" style="background: linear-gradient(135deg, var(--success-color), #34D399);">
                                <i class="fas fa-check-circle"></i>
                            </div>
                            <div class="stat-trend up">
                                <i class="fas fa-shield-check"></i>
                                <span>已认证</span>
                            </div>
                        </div>
                        <div class="stat-number"><?php echo $verified_users; ?></div>
                        <div class="stat-label">已认证用户</div>
                        <div class="stat-description">通过实名认证的用户数</div>
                    </div>

                    <div class="stat-card">
                        <div class="stat-header">
                            <div class="stat-icon" style="background: linear-gradient(135deg, var(--secondary-color), #764ba2);">
                                <i class="fas fa-calendar-week"></i>
                            </div>
                            <div class="stat-trend up">
                                <i class="fas fa-chart-line"></i>
                                <span>本周</span>
                            </div>
                        </div>
                        <div class="stat-number"><?php echo $week_users; ?></div>
                        <div class="stat-label">本周新增</div>
                        <div class="stat-description">本周新注册的用户数</div>
                    </div>

                    <div class="stat-card">
                        <div class="stat-header">
                            <div class="stat-icon" style="background: linear-gradient(135deg, var(--accent-color), #FF8A80);">
                                <i class="fas fa-calendar-alt"></i>
                            </div>
                            <div class="stat-trend up">
                                <i class="fas fa-trending-up"></i>
                                <span>本月</span>
                            </div>
                        </div>
                        <div class="stat-number"><?php echo $month_users; ?></div>
                        <div class="stat-label">本月新增</div>
                        <div class="stat-description">本月新注册的用户数</div>
                    </div>
                </div>

                <!-- 图表区域 -->
                <div class="charts-section">
                    <!-- 用户增长趋势图 -->
                    <div class="chart-card">
                        <div class="chart-header">
                            <h3 class="chart-title">用户增长趋势</h3>
                            <p class="chart-subtitle">最近7天每日新增用户数量</p>
                        </div>
                        <div class="chart-container">
                            <canvas id="userGrowthChart"></canvas>
                        </div>
                    </div>

                    <!-- 认证状态分布 -->
                    <div class="chart-card">
                        <div class="chart-header">
                            <h3 class="chart-title">认证状态分布</h3>
                            <p class="chart-subtitle">实名认证申请状态统计</p>
                        </div>
                        <div class="chart-container">
                            <canvas id="verificationChart"></canvas>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <script src="assets/js/admin-layout.js"></script>
    <script>
        // 数据统计仪表盘脚本

        // 准备图表数据
        const dailyStatsData = <?php echo json_encode($daily_stats); ?>;
        const verificationStatsData = <?php echo json_encode($verification_stats); ?>;

        // 页面加载完成后初始化图表
        document.addEventListener('DOMContentLoaded', function() {
            // 更新页面标题
            updatePageTitle('数据统计');

            initUserGrowthChart();
            initVerificationChart();
            initAnimations();
        });

        // 用户增长趋势图
        function initUserGrowthChart() {
            const ctx = document.getElementById('userGrowthChart').getContext('2d');

            new Chart(ctx, {
                type: 'line',
                data: {
                    labels: dailyStatsData.map(item => item.label),
                    datasets: [{
                        label: '新增用户',
                        data: dailyStatsData.map(item => item.count),
                        borderColor: '#40E0D0',
                        backgroundColor: 'rgba(64, 224, 208, 0.1)',
                        borderWidth: 3,
                        fill: true,
                        tension: 0.4,
                        pointBackgroundColor: '#40E0D0',
                        pointBorderColor: '#ffffff',
                        pointBorderWidth: 2,
                        pointRadius: 6,
                        pointHoverRadius: 8
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            grid: {
                                color: 'rgba(0, 0, 0, 0.05)'
                            },
                            ticks: {
                                color: '#6B7280'
                            }
                        },
                        x: {
                            grid: {
                                color: 'rgba(0, 0, 0, 0.05)'
                            },
                            ticks: {
                                color: '#6B7280'
                            }
                        }
                    },
                    elements: {
                        point: {
                            hoverBackgroundColor: '#20B2AA'
                        }
                    }
                }
            });
        }

        // 认证状态分布图
        function initVerificationChart() {
            const ctx = document.getElementById('verificationChart').getContext('2d');

            const statusLabels = {
                'pending': '待审核',
                'approved': '已通过',
                'rejected': '已拒绝'
            };

            const statusColors = {
                'pending': '#F59E0B',
                'approved': '#10B981',
                'rejected': '#EF4444'
            };

            const labels = [];
            const data = [];
            const colors = [];

            Object.keys(verificationStatsData).forEach(status => {
                if (verificationStatsData[status] > 0) {
                    labels.push(statusLabels[status] || status);
                    data.push(verificationStatsData[status]);
                    colors.push(statusColors[status] || '#6B7280');
                }
            });

            new Chart(ctx, {
                type: 'doughnut',
                data: {
                    labels: labels,
                    datasets: [{
                        data: data,
                        backgroundColor: colors,
                        borderWidth: 0,
                        hoverOffset: 4
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'bottom',
                            labels: {
                                padding: 20,
                                usePointStyle: true,
                                color: '#6B7280'
                            }
                        }
                    }
                }
            });
        }

        // 初始化动画
        function initAnimations() {
            // 统计卡片动画
            const statCards = document.querySelectorAll('.stat-card');
            statCards.forEach((card, index) => {
                card.style.opacity = '0';
                card.style.transform = 'translateY(20px)';
                setTimeout(() => {
                    card.style.transition = 'all 0.5s ease';
                    card.style.opacity = '1';
                    card.style.transform = 'translateY(0)';
                }, index * 100);
            });

            // 图表卡片动画
            const chartCards = document.querySelectorAll('.chart-card');
            chartCards.forEach((card, index) => {
                card.style.opacity = '0';
                card.style.transform = 'translateY(20px)';
                setTimeout(() => {
                    card.style.transition = 'all 0.5s ease';
                    card.style.opacity = '1';
                    card.style.transform = 'translateY(0)';
                }, 600 + index * 200);
            });
        }

        // 统计卡片悬浮效果
        document.querySelectorAll('.stat-card').forEach(card => {
            card.addEventListener('mouseenter', function() {
                this.style.transform = 'translateY(-4px)';
                this.style.boxShadow = '0 12px 32px rgba(0, 0, 0, 0.15)';
            });

            card.addEventListener('mouseleave', function() {
                this.style.transform = 'translateY(0)';
                this.style.boxShadow = '0 4px 20px rgba(0, 0, 0, 0.08)';
            });
        });

        console.log('📊 数据统计仪表盘已加载完成');
        console.log('� 图表数据：', {
            dailyStats: dailyStatsData,
            verificationStats: verificationStatsData
        });
    </script>
</body>
</html>
