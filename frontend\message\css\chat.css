/* 聊天页面样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    font-family: 'PingFang SC', 'Helvetica Neue', Arial, sans-serif;
}

body {
    background-color: #f0f0f0;
    color: #333333;
    line-height: 1.6;
    height: 100vh;
    overflow: hidden;
    display: flex;
    flex-direction: column;
}

/* Toast提示 */
.toast {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background-color: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 12px 20px;
    border-radius: 8px;
    font-size: 14px;
    z-index: 9999;
    display: none;
    max-width: 80%;
    text-align: center;
}

/* 顶部导航栏 */
.header-bar {
    height: 60px;
    background-color: #40E0D0;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 15px;
    color: white;
    flex-shrink: 0;
}

.back-button, .more-button {
    width: 40px;
    height: 40px;
    border: none;
    background: none;
    color: white;
    font-size: 18px;
    cursor: pointer;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: background-color 0.2s ease;
}

.back-button:hover, .more-button:hover {
    background-color: rgba(255, 255, 255, 0.1);
}

.header-info {
    flex: 1;
    text-align: center;
}

.chat-title {
    font-size: 18px;
    font-weight: bold;
    margin-bottom: 2px;
}

.chat-subtitle {
    font-size: 12px;
    opacity: 0.8;
}

/* 消息容器 */
.message-container {
    flex: 1;
    overflow-y: auto;
    padding: 10px 0;
    background: linear-gradient(to bottom, #f0f0f0, #e8e8e8);
}

.message-list {
    padding: 0 15px;
}

/* 消息项 */
.message-item {
    display: flex;
    margin-bottom: 15px;
    align-items: flex-end;
}

.message-item.own {
    flex-direction: row-reverse;
}

.message-avatar {
    width: 40px;
    height: 40px;
    margin: 0 10px;
    flex-shrink: 0;
}

.message-avatar img {
    width: 100%;
    height: 100%;
    border-radius: 50%;
    object-fit: cover;
}

.message-content {
    max-width: 70%;
    display: flex;
    flex-direction: column;
}

.message-item.own .message-content {
    align-items: flex-end;
}

.message-sender {
    font-size: 12px;
    color: #666;
    margin-bottom: 5px;
    padding: 0 12px;
}

.message-bubble {
    background-color: white;
    padding: 12px 16px;
    border-radius: 18px;
    word-wrap: break-word;
    position: relative;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.message-item.own .message-bubble {
    background-color: #40E0D0;
    color: white;
}

.message-bubble::before {
    content: '';
    position: absolute;
    bottom: 0;
    width: 0;
    height: 0;
    border: 8px solid transparent;
}

.message-item.other .message-bubble::before {
    left: -8px;
    border-right-color: white;
    border-bottom-color: white;
}

.message-item.own .message-bubble::before {
    right: -8px;
    border-left-color: #40E0D0;
    border-bottom-color: #40E0D0;
}

.message-time {
    font-size: 11px;
    color: #999;
    margin-top: 5px;
    padding: 0 12px;
}

/* 输入区域 */
.input-area {
    background-color: white;
    border-top: 1px solid #e0e0e0;
    flex-shrink: 0;
}

.input-toolbar {
    display: flex;
    align-items: flex-end;
    padding: 10px 15px;
    gap: 10px;
}

.tool-button {
    width: 36px;
    height: 36px;
    border: none;
    background: none;
    color: #666;
    font-size: 18px;
    cursor: pointer;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
    flex-shrink: 0;
}

.tool-button:hover {
    background-color: #f0f0f0;
    color: #40E0D0;
}

.input-wrapper {
    flex: 1;
    position: relative;
}

#messageInput {
    width: 100%;
    min-height: 36px;
    max-height: 100px;
    padding: 8px 12px;
    border: 1px solid #e0e0e0;
    border-radius: 18px;
    font-size: 16px;
    line-height: 1.4;
    resize: none;
    outline: none;
    background-color: #f8f9fa;
    transition: border-color 0.2s ease;
}

#messageInput:focus {
    border-color: #40E0D0;
    background-color: white;
}

.send-button {
    width: 36px;
    height: 36px;
    border: none;
    background-color: #40E0D0;
    color: white;
    font-size: 16px;
    cursor: pointer;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
    flex-shrink: 0;
}

.send-button:hover {
    background-color: #36c7b8;
    transform: scale(1.05);
}

.send-button:active {
    transform: scale(0.95);
}

/* 更多功能弹窗 */
.more-menu {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 1000;
    display: none;
}

.more-menu.show {
    display: block;
}

.more-menu-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
}

.more-menu-content {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background-color: white;
    border-radius: 20px 20px 0 0;
    padding: 20px;
    transform: translateY(100%);
    transition: transform 0.3s ease;
}

.more-menu.show .more-menu-content {
    transform: translateY(0);
}

.more-menu-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 20px;
    padding: 10px 0;
}

.more-menu-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    cursor: pointer;
    padding: 15px 10px;
    border-radius: 12px;
    transition: background-color 0.2s ease;
}

.more-menu-item:hover {
    background-color: #f8f9fa;
}

.more-menu-item i {
    width: 50px;
    height: 50px;
    background-color: #40E0D0;
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 20px;
    margin-bottom: 8px;
}

.more-menu-item span {
    font-size: 12px;
    color: #333;
    text-align: center;
}

/* 滚动条样式 */
.message-container::-webkit-scrollbar {
    width: 4px;
}

.message-container::-webkit-scrollbar-track {
    background: transparent;
}

.message-container::-webkit-scrollbar-thumb {
    background: rgba(0, 0, 0, 0.2);
    border-radius: 2px;
}

.message-container::-webkit-scrollbar-thumb:hover {
    background: rgba(0, 0, 0, 0.3);
}

/* 动画效果 */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translate(-50%, -50%) scale(0.8);
    }
    to {
        opacity: 1;
        transform: translate(-50%, -50%) scale(1);
    }
}

@keyframes fadeOut {
    from {
        opacity: 1;
        transform: translate(-50%, -50%) scale(1);
    }
    to {
        opacity: 0;
        transform: translate(-50%, -50%) scale(0.8);
    }
}

@keyframes messageSlideIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.message-item {
    animation: messageSlideIn 0.3s ease;
}

/* 响应式设计 */
@media (max-width: 480px) {
    .message-content {
        max-width: 80%;
    }
    
    .more-menu-grid {
        grid-template-columns: repeat(3, 1fr);
    }
    
    .input-toolbar {
        padding: 8px 10px;
        gap: 8px;
    }
    
    .tool-button {
        width: 32px;
        height: 32px;
        font-size: 16px;
    }
    
    .send-button {
        width: 32px;
        height: 32px;
        font-size: 14px;
    }
}
