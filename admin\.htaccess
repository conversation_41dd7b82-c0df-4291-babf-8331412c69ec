# 后台管理系统访问控制
RewriteEngine On

# 阻止直接访问敏感文件
<Files ~ "\.(log|sql|md|txt|json|xml|ini|conf)$">
    Order deny,allow
    Deny from all
</Files>

# 如果访问后台根目录，重定向到登录页面
RewriteCond %{REQUEST_URI} ^/?$
RewriteRule ^$ login.php [R=302,L]

# 安全头部设置
<IfModule mod_headers.c>
    Header always set X-Content-Type-Options nosniff
    Header always set X-Frame-Options DENY
    Header always set X-XSS-Protection "1; mode=block"
    Header always set Referrer-Policy "strict-origin-when-cross-origin"
    Header always set Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline' cdnjs.cloudflare.com; style-src 'self' 'unsafe-inline' cdnjs.cloudflare.com; img-src 'self' data:; font-src 'self' cdnjs.cloudflare.com;"
</IfModule>

# 禁止显示目录列表
Options -Indexes

# 设置默认首页为登录页面
DirectoryIndex login.php index.php

# 限制访问频率（如果服务器支持）
<IfModule mod_evasive24.c>
    DOSHashTableSize    2048
    DOSPageCount        10
    DOSPageInterval     1
    DOSSiteCount        50
    DOSSiteInterval     1
    DOSBlockingPeriod   300
</IfModule>
