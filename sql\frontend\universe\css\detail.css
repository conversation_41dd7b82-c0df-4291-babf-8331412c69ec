/* 详情页样式 */
.content-container {
    padding: 60px 15px 70px; /* 底部留出评论输入框的空间 */
}

.detail-card {
    background-color: white;
    border-radius: 12px;
    margin-bottom: 15px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    overflow: hidden;
}

.share-icon {
    font-size: 18px;
    color: white;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* 内容样式 */
.post-title {
    font-size: 20px;
    font-weight: 600;
    margin-bottom: 15px;
    color: #333;
    line-height: 1.4;
}

.post-content {
    font-size: 16px;
    color: #333;
    line-height: 1.8;
}

.post-content p {
    margin-bottom: 15px;
}

.post-content img {
    max-width: 100%;
    height: auto;
    border-radius: 8px;
    margin: 10px 0;
}

.post-content h1, .post-content h2, .post-content h3, .post-content h4, .post-content h5, .post-content h6 {
    margin: 20px 0 10px;
    font-weight: 600;
    color: #333;
}

.post-content h1 {
    font-size: 24px;
}

.post-content h2 {
    font-size: 22px;
}

.post-content h3 {
    font-size: 20px;
}

.post-content h4 {
    font-size: 18px;
}

.post-content h5, .post-content h6 {
    font-size: 16px;
}

.post-content blockquote {
    border-left: 4px solid #1E90FF;
    padding: 10px 15px;
    margin: 15px 0;
    background-color: #f8f9fa;
    color: #666;
}

.post-content ul, .post-content ol {
    margin: 15px 0;
    padding-left: 20px;
}

.post-content li {
    margin-bottom: 5px;
}

.post-content a {
    color: #1E90FF;
    text-decoration: none;
}

.post-content a:hover {
    text-decoration: underline;
}

.post-content pre {
    background-color: #f8f9fa;
    padding: 15px;
    border-radius: 8px;
    overflow-x: auto;
    margin: 15px 0;
}

.post-content code {
    font-family: Consolas, Monaco, 'Andale Mono', monospace;
    background-color: #f8f9fa;
    padding: 2px 5px;
    border-radius: 3px;
}

/* 操作按钮 */
.action-buttons {
    display: flex;
    justify-content: space-around;
}

.action-button {
    display: flex;
    align-items: center;
    color: #999;
    font-size: 14px;
    padding: 8px 15px;
    border-radius: 20px;
    transition: all 0.3s ease;
}

.action-button i {
    margin-right: 5px;
    font-size: 16px;
}

.action-button.active {
    color: #1E90FF;
}

.action-button:active {
    background-color: #f0f0f0;
}

/* 评论区 */
.comment-section {
    margin-top: 20px;
}

.section-header {
    margin-bottom: 15px;
}

.section-header h2 {
    font-size: 18px;
    font-weight: 600;
    color: #333;
}

.comment-list {
    margin-bottom: 20px;
}

.empty-comment {
    text-align: center;
    padding: 30px 0;
    color: #999;
}

.comment-item {
    display: flex;
    margin-bottom: 20px;
}

.comment-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    overflow: hidden;
    margin-right: 10px;
    flex-shrink: 0;
}

.comment-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.comment-content {
    flex: 1;
    background-color: #f8f9fa;
    border-radius: 12px;
    padding: 12px;
    position: relative;
}

.comment-user {
    font-size: 14px;
    font-weight: 500;
    color: #333;
    margin-bottom: 5px;
}

.comment-text {
    font-size: 15px;
    color: #333;
    line-height: 1.6;
    margin-bottom: 8px;
    word-break: break-word;
}

.comment-meta {
    display: flex;
    justify-content: space-between;
    font-size: 12px;
    color: #999;
}

.reply-button {
    color: #1E90FF;
    cursor: pointer;
}

/* 回复列表 */
.reply-list {
    margin-top: 15px;
    border-top: 1px solid #eee;
    padding-top: 15px;
}

.reply-item {
    display: flex;
    margin-bottom: 15px;
}

.reply-avatar {
    width: 30px;
    height: 30px;
    border-radius: 50%;
    overflow: hidden;
    margin-right: 10px;
    flex-shrink: 0;
}

.reply-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.reply-content {
    flex: 1;
}

.reply-user {
    font-size: 13px;
    font-weight: 500;
    color: #333;
    margin-bottom: 3px;
}

.reply-text {
    font-size: 14px;
    color: #333;
    line-height: 1.6;
    margin-bottom: 5px;
    word-break: break-word;
}

.reply-meta {
    font-size: 12px;
    color: #999;
}

/* 评论输入框 */
.comment-input-container {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background-color: white;
    padding: 10px 15px;
    box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.05);
    display: flex;
    align-items: center;
    z-index: 100;
}

#comment-input {
    flex: 1;
    border: 1px solid #e0e0e0;
    border-radius: 20px;
    padding: 10px 15px;
    font-size: 14px;
    outline: none;
    transition: border-color 0.3s;
}

#comment-input:focus {
    border-color: #1E90FF;
}

#send-comment {
    background-color: #1E90FF;
    color: white;
    border: none;
    border-radius: 20px;
    padding: 8px 15px;
    margin-left: 10px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
}
