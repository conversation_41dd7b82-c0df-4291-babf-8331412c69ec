<?php
/**
 * 简化的WebSocket服务器
 * 专门用于调试实时通知问题
 */

header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// 处理OPTIONS预检请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

try {
    $pdo = new PDO(
        "mysql:host=localhost;dbname=quwanplanet;charset=utf8mb4",
        "quwanplanet",
        "nJmJm23FB4Xn6Fc3",
        [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC
        ]
    );
    
    $user_id = intval($_GET['user_id'] ?? 0);
    $action = $_GET['action'] ?? 'poll';
    
    $response = [
        'success' => true,
        'action' => $action,
        'user_id' => $user_id,
        'timestamp' => date('Y-m-d H:i:s'),
        'data' => [],
        'debug' => []
    ];
    
    switch ($action) {
        case 'poll':
            // 查询未送达的通知
            $response['debug']['step'] = '1-开始查询通知';
            
            if ($user_id <= 0) {
                $response['success'] = false;
                $response['error'] = '无效的用户ID';
                break;
            }
            
            $response['debug']['step'] = '2-用户ID验证通过';
            
            // 检查表是否存在
            $stmt = $pdo->query("SHOW TABLES LIKE 'realtime_notifications'");
            if ($stmt->rowCount() === 0) {
                $response['success'] = false;
                $response['error'] = 'realtime_notifications表不存在';
                $response['debug']['error'] = '数据库表缺失';
                break;
            }
            
            $response['debug']['step'] = '3-数据库表检查通过';
            
            // 查询通知（简化查询条件）
            $stmt = $pdo->prepare("
                SELECT id, type, title, content, data, created_at, expires_at, status, delivered_at
                FROM realtime_notifications 
                WHERE user_id = ? AND status = 'pending'
                ORDER BY created_at ASC
                LIMIT 10
            ");
            $stmt->execute([$user_id]);
            $notifications = $stmt->fetchAll();
            
            $response['debug']['step'] = '4-通知查询完成';
            $response['debug']['sql_result'] = count($notifications) . ' 条记录';
            
            // 过滤未送达的通知
            $undelivered = [];
            foreach ($notifications as $notification) {
                if (empty($notification['delivered_at'])) {
                    $undelivered[] = $notification;
                }
            }
            
            $response['data']['notifications'] = $undelivered;
            $response['data']['count'] = count($undelivered);
            $response['debug']['undelivered_count'] = count($undelivered);
            
            // 如果有未送达的通知，标记为已送达
            if (!empty($undelivered)) {
                $notification_ids = array_column($undelivered, 'id');
                $placeholders = str_repeat('?,', count($notification_ids) - 1) . '?';
                
                $stmt = $pdo->prepare("
                    UPDATE realtime_notifications 
                    SET delivered_at = NOW() 
                    WHERE id IN ({$placeholders})
                ");
                $stmt->execute($notification_ids);
                
                $response['debug']['step'] = '5-通知已标记为送达';
                $response['debug']['delivered_ids'] = $notification_ids;
            }
            
            // 更新用户活动时间（如果字段存在）
            try {
                $stmt = $pdo->prepare("
                    UPDATE users 
                    SET last_activity = NOW()
                    WHERE id = ?
                ");
                $stmt->execute([$user_id]);
                $response['debug']['user_activity_updated'] = true;
            } catch (Exception $e) {
                $response['debug']['user_activity_error'] = $e->getMessage();
            }
            
            break;
            
        case 'mark_read':
            $notification_id = intval($_GET['notification_id'] ?? 0);
            
            if ($notification_id > 0 && $user_id > 0) {
                $stmt = $pdo->prepare("
                    UPDATE realtime_notifications 
                    SET status = 'read', read_at = NOW() 
                    WHERE id = ? AND user_id = ?
                ");
                $stmt->execute([$notification_id, $user_id]);
                
                $response['data']['marked_read'] = $notification_id;
                $response['debug']['affected_rows'] = $stmt->rowCount();
            } else {
                $response['success'] = false;
                $response['error'] = '无效的通知ID或用户ID';
            }
            break;
            
        case 'heartbeat':
            // 简单的心跳响应
            $response['data']['status'] = 'alive';
            $response['debug']['heartbeat_time'] = date('Y-m-d H:i:s');
            break;
            
        case 'test':
            // 测试连接
            $response['data']['message'] = '连接测试成功';
            $response['debug']['server_time'] = date('Y-m-d H:i:s');
            $response['debug']['php_version'] = PHP_VERSION;
            break;
            
        default:
            $response['success'] = false;
            $response['error'] = '未知操作: ' . $action;
    }
    
} catch (PDOException $e) {
    $response = [
        'success' => false,
        'error' => '数据库错误: ' . $e->getMessage(),
        'debug' => [
            'error_code' => $e->getCode(),
            'error_file' => $e->getFile(),
            'error_line' => $e->getLine()
        ]
    ];
} catch (Exception $e) {
    $response = [
        'success' => false,
        'error' => '系统错误: ' . $e->getMessage(),
        'debug' => [
            'error_type' => get_class($e),
            'error_file' => $e->getFile(),
            'error_line' => $e->getLine()
        ]
    ];
}

echo json_encode($response, JSON_UNESCAPED_UNICODE);
?>
