<?php
// 设置响应头
header('Content-Type: application/json');

// 启动会话
session_start();

// 检查用户是否已登录
if (!isset($_SESSION['user_id'])) {
    echo json_encode([
        'success' => false,
        'message' => '请先登录'
    ]);
    exit;
}

// 检查必填参数
if (!isset($_POST['post_id']) || empty($_POST['post_id']) || !isset($_POST['content']) || empty($_POST['content'])) {
    echo json_encode([
        'success' => false,
        'message' => '缺少必填参数'
    ]);
    exit;
}

// 获取参数
$post_id = intval($_POST['post_id']);
$user_id = $_SESSION['user_id'];
$content = trim($_POST['content']);
$parent_id = isset($_POST['parent_id']) ? intval($_POST['parent_id']) : 0;

// 引入数据库配置
require_once '../../../includes/db_config.php';

try {
    // 连接数据库
    $dsn = "mysql:host={$db_config['host']};dbname={$db_config['dbname']};charset={$db_config['charset']};port={$db_config['port']}";
    $pdo = new PDO($dsn, $db_config['username'], $db_config['password']);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

    // 开始事务
    $pdo->beginTransaction();

    // 检查内容是否存在
    $stmt = $pdo->prepare("SELECT id FROM universe_posts WHERE id = :id AND status = 1");
    $stmt->execute(['id' => $post_id]);
    if (!$stmt->fetch()) {
        echo json_encode([
            'success' => false,
            'message' => '内容不存在或已被删除'
        ]);
        exit;
    }

    // 如果是回复评论，检查父评论是否存在
    if ($parent_id > 0) {
        $stmt = $pdo->prepare("SELECT id FROM universe_comments WHERE id = :id AND post_id = :post_id AND status = 1");
        $stmt->execute([
            'id' => $parent_id,
            'post_id' => $post_id
        ]);

        if (!$stmt->fetch()) {
            echo json_encode([
                'success' => false,
                'message' => '回复的评论不存在或已被删除'
            ]);
            exit;
        }
    }

    // 添加评论
    $stmt = $pdo->prepare("
        INSERT INTO universe_comments (post_id, user_id, content, parent_id)
        VALUES (:post_id, :user_id, :content, :parent_id)
    ");

    $stmt->execute([
        'post_id' => $post_id,
        'user_id' => $user_id,
        'content' => $content,
        'parent_id' => $parent_id
    ]);

    // 更新内容评论数
    $stmt = $pdo->prepare("UPDATE universe_posts SET comments = comments + 1 WHERE id = :id");
    $stmt->execute(['id' => $post_id]);

    // 提交事务
    $pdo->commit();

    // 返回成功信息
    echo json_encode([
        'success' => true,
        'message' => $parent_id > 0 ? '回复成功' : '评论成功'
    ]);
} catch (PDOException $e) {
    // 回滚事务
    if ($pdo->inTransaction()) {
        $pdo->rollBack();
    }

    // 记录错误
    error_log("评论操作错误: " . $e->getMessage());

    // 返回错误信息
    echo json_encode([
        'success' => false,
        'message' => '操作失败，请稍后再试'
    ]);
}
