<?php
// 引入session配置
require_once '../../sql/session_config.php';
initLoginSession();

// 检查用户是否已登录
if (!isUserLoggedIn()) {
    header('Location: ../login/index.php');
    exit;
}

$user_id = $_SESSION['user_id'];

// 数据库连接配置
$db_config = [
    'host' => 'localhost',
    'username' => 'quwanplanet',
    'password' => 'nJmJm23FB4Xn6Fc3',
    'dbname' => 'quwanplanet',
    'port' => 3306,
    'charset' => 'utf8mb4'
];

// 获取用户优惠券数据
$available_coupons = [];
$used_coupons = [];
$expired_coupons = [];

try {
    $dsn = "mysql:host={$db_config['host']};dbname={$db_config['dbname']};charset={$db_config['charset']};port={$db_config['port']}";
    $pdo = new PDO($dsn, $db_config['username'], $db_config['password']);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

    // 检查表是否存在
    $stmt = $pdo->query("SHOW TABLES LIKE 'user_camping_coupons'");
    if ($stmt->rowCount() > 0) {
        // 获取用户的优惠券
        $sql = "SELECT ucc.*, cc.title, cc.description, cc.discount_amount, cc.min_amount, cc.valid_until, cc.type
                FROM user_camping_coupons ucc
                JOIN camping_coupons cc ON ucc.coupon_id = cc.id
                WHERE ucc.user_id = ?
                ORDER BY ucc.claimed_at DESC";

        $stmt = $pdo->prepare($sql);
        $stmt->execute([$user_id]);
        $user_coupons = $stmt->fetchAll(PDO::FETCH_ASSOC);

        // 分类优惠券
        foreach ($user_coupons as $coupon) {
            $now = new DateTime();
            $valid_until = new DateTime($coupon['valid_until']);

            if ($coupon['status'] === 'used') {
                $used_coupons[] = $coupon;
            } elseif ($now > $valid_until) {
                $expired_coupons[] = $coupon;
            } else {
                $available_coupons[] = $coupon;
            }
        }
    }
} catch (PDOException $e) {
    error_log("获取优惠券错误: " . $e->getMessage());
}
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <meta name="theme-color" content="#6F7BF5">
    <title>券包 - 趣玩星球</title>
    <style>
        /* CSS变量定义 */
        :root {
            --primary-color: #6F7BF5;
            --primary-light: #A8B2F8;
            --primary-dark: #5A67E8;
            --secondary-color: #8B95F7;
            --accent-color: #FF6B9D;
            --success-color: #06D6A0;
            --warning-color: #FFD166;
            --error-color: #FF6B6B;
            --gradient-primary: linear-gradient(135deg, #6F7BF5, #8B95F7);
            --gradient-secondary: linear-gradient(135deg, #A8B2F8, #6F7BF5);
            --gradient-warm: linear-gradient(135deg, #FF6B9D, #FFD166);
            --text-primary: #333333;
            --text-secondary: #666666;
            --text-light: #999999;
            --bg-white: #FFFFFF;
            --bg-light: #F8F9FA;
            --shadow-sm: 0 2px 8px rgba(0, 0, 0, 0.08);
            --shadow-md: 0 4px 16px rgba(0, 0, 0, 0.12);
            --shadow-lg: 0 8px 32px rgba(0, 0, 0, 0.16);
            --radius-sm: 8px;
            --radius-md: 12px;
            --radius-lg: 16px;
            --radius-xl: 24px;
            --transition-fast: 0.2s ease;
            --transition-normal: 0.3s ease;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }

        body {
            background: var(--bg-light);
            color: var(--text-primary);
            line-height: 1.6;
            min-height: 100vh;
        }

        .header-bar {
            display: flex;
            align-items: center;
            padding: 16px 20px;
            background: var(--bg-white);
            color: var(--text-primary);
            position: sticky;
            top: 0;
            z-index: 100;
            box-shadow: var(--shadow-sm);
            border-bottom: 1px solid #E5E7EB;
        }

        .back-button {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: var(--bg-light);
            border: none;
            color: var(--text-primary);
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition-normal);
            text-decoration: none;
            margin-right: 16px;
        }

        .back-button:hover {
            background: var(--primary-color);
            color: white;
            transform: scale(1.05);
        }

        .header-title {
            font-size: 1.125rem;
            font-weight: 600;
            color: var(--text-primary);
            flex: 1;
        }

        .tabs {
            display: flex;
            background: var(--bg-white);
            margin-bottom: 20px;
            box-shadow: var(--shadow-sm);
            position: sticky;
            top: 73px;
            z-index: 99;
            border-bottom: 1px solid #E5E7EB;
        }

        .tab {
            flex: 1;
            text-align: center;
            padding: 16px 0;
            font-size: 0.9rem;
            color: var(--text-secondary);
            position: relative;
            cursor: pointer;
            transition: var(--transition-normal);
            font-weight: 500;
        }

        .tab:hover {
            color: var(--primary-color);
        }

        .tab.active {
            color: var(--primary-color);
            font-weight: 600;
        }

        .tab.active:after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 24px;
            height: 3px;
            background: var(--gradient-primary);
            border-radius: var(--radius-sm);
        }

        .coupon-container {
            padding: 20px;
        }

        .coupon-list {
            display: flex;
            flex-direction: column;
            gap: 16px;
        }

        .coupon-card {
            background: var(--gradient-primary);
            border-radius: var(--radius-lg);
            color: white;
            overflow: hidden;
            position: relative;
            box-shadow: var(--shadow-md);
            transition: var(--transition-normal);
        }

        .coupon-card:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-lg);
        }

        .coupon-card.newbie {
            background: var(--gradient-secondary);
        }

        .coupon-card.used {
            background: linear-gradient(135deg, #9CA3AF, #6B7280);
            opacity: 0.8;
        }

        .coupon-card.expired {
            background: linear-gradient(135deg, #F87171, #EF4444);
            opacity: 0.7;
        }

        .coupon-card:before {
            content: '';
            position: absolute;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-image: radial-gradient(circle at 10% 20%, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 80%);
        }

        .coupon-content {
            display: flex;
            padding: 15px;
            position: relative;
            z-index: 1;
        }

        .coupon-amount {
            font-size: 32px;
            font-weight: bold;
            margin-right: 15px;
            display: flex;
            align-items: center;
        }

        .coupon-amount small {
            font-size: 16px;
            margin-right: 2px;
        }

        .coupon-info {
            flex: 1;
        }

        .coupon-title {
            font-size: 16px;
            font-weight: bold;
            margin-bottom: 5px;
        }

        .coupon-desc {
            font-size: 12px;
            opacity: 0.9;
            margin-bottom: 8px;
        }

        .coupon-validity {
            font-size: 12px;
            opacity: 0.8;
        }

        .coupon-footer {
            background-color: rgba(0, 0, 0, 0.1);
            padding: 10px 15px;
            font-size: 12px;
            display: flex;
            justify-content: space-between;
        }

        .coupon-rules {
            opacity: 0.9;
        }

        .coupon-use {
            font-weight: bold;
            text-decoration: none;
            color: white;
        }

        .empty-state {
            text-align: center;
            padding: 50px 20px;
            color: #999;
        }

        .empty-state i {
            font-size: 50px;
            margin-bottom: 15px;
            color: #ddd;
        }

        .empty-state p {
            font-size: 15px;
            margin-bottom: 20px;
        }

        .empty-state .btn {
            display: inline-block;
            padding: 12px 24px;
            background: var(--gradient-primary);
            color: white;
            border-radius: var(--radius-xl);
            text-decoration: none;
            font-size: 0.875rem;
            font-weight: 600;
            box-shadow: var(--shadow-md);
            transition: var(--transition-normal);
        }

        .empty-state .btn:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-lg);
        }

        .coupon-status {
            font-weight: 600;
            opacity: 0.9;
        }

        /* Toast提示 */
        .toast {
            position: fixed;
            bottom: 100px;
            left: 50%;
            transform: translateX(-50%);
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 12px 20px;
            border-radius: var(--radius-lg);
            font-size: 0.875rem;
            z-index: 10000;
            display: none;
            backdrop-filter: blur(10px);
        }
    </style>
    <!-- 引入字体图标库 -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <!-- Toast提示 -->
    <div class="toast" id="toast"></div>

    <!-- 顶部导航栏 -->
    <div class="header-bar">
        <a href="../profile/index.php" class="back-button">
            <i class="fas fa-arrow-left"></i>
        </a>
        <div class="header-title">券包</div>
    </div>

    <!-- 标签页 -->
    <div class="tabs">
        <div class="tab active" data-tab="available">可用</div>
        <div class="tab" data-tab="used">已使用</div>
        <div class="tab" data-tab="expired">已过期</div>
    </div>

    <div class="coupon-container">
        <div class="tab-content active" id="available-content">
            <?php if (empty($available_coupons)): ?>
            <div class="empty-state">
                <i class="fas fa-ticket-alt"></i>
                <p>暂无可用优惠券</p>
                <a href="../camping/index.php" class="btn">去露营页面领取</a>
            </div>
            <?php else: ?>
            <div class="coupon-list">
                <?php foreach ($available_coupons as $coupon): ?>
                <div class="coupon-card <?php echo $coupon['type'] === 'newbie_discount' ? 'newbie' : ''; ?>">
                    <div class="coupon-content">
                        <div class="coupon-amount"><small>¥</small><?php echo intval($coupon['discount_amount']); ?></div>
                        <div class="coupon-info">
                            <div class="coupon-title"><?php echo htmlspecialchars($coupon['title']); ?></div>
                            <div class="coupon-desc"><?php echo htmlspecialchars($coupon['description']); ?></div>
                            <div class="coupon-validity">有效期至：<?php echo date('Y-m-d', strtotime($coupon['valid_until'])); ?></div>
                        </div>
                    </div>
                    <div class="coupon-footer">
                        <div class="coupon-rules">满<?php echo intval($coupon['min_amount']); ?>元可用</div>
                        <a href="../camping/index.php" class="coupon-use">去使用 ></a>
                    </div>
                </div>
                <?php endforeach; ?>
            </div>
            <?php endif; ?>
        </div>

        <div class="tab-content" id="used-content" style="display: none;">
            <?php if (empty($used_coupons)): ?>
            <div class="empty-state">
                <i class="fas fa-check-circle"></i>
                <p>暂无已使用的优惠券</p>
            </div>
            <?php else: ?>
            <div class="coupon-list">
                <?php foreach ($used_coupons as $coupon): ?>
                <div class="coupon-card used">
                    <div class="coupon-content">
                        <div class="coupon-amount"><small>¥</small><?php echo intval($coupon['discount_amount']); ?></div>
                        <div class="coupon-info">
                            <div class="coupon-title"><?php echo htmlspecialchars($coupon['title']); ?></div>
                            <div class="coupon-desc"><?php echo htmlspecialchars($coupon['description']); ?></div>
                            <div class="coupon-validity">使用时间：<?php echo date('Y-m-d', strtotime($coupon['used_at'])); ?></div>
                        </div>
                    </div>
                    <div class="coupon-footer">
                        <div class="coupon-rules">满<?php echo intval($coupon['min_amount']); ?>元可用</div>
                        <div class="coupon-status">已使用</div>
                    </div>
                </div>
                <?php endforeach; ?>
            </div>
            <?php endif; ?>
        </div>

        <div class="tab-content" id="expired-content" style="display: none;">
            <?php if (empty($expired_coupons)): ?>
            <div class="empty-state">
                <i class="fas fa-calendar-times"></i>
                <p>暂无已过期的优惠券</p>
            </div>
            <?php else: ?>
            <div class="coupon-list">
                <?php foreach ($expired_coupons as $coupon): ?>
                <div class="coupon-card expired">
                    <div class="coupon-content">
                        <div class="coupon-amount"><small>¥</small><?php echo intval($coupon['discount_amount']); ?></div>
                        <div class="coupon-info">
                            <div class="coupon-title"><?php echo htmlspecialchars($coupon['title']); ?></div>
                            <div class="coupon-desc"><?php echo htmlspecialchars($coupon['description']); ?></div>
                            <div class="coupon-validity">过期时间：<?php echo date('Y-m-d', strtotime($coupon['valid_until'])); ?></div>
                        </div>
                    </div>
                    <div class="coupon-footer">
                        <div class="coupon-rules">满<?php echo intval($coupon['min_amount']); ?>元可用</div>
                        <div class="coupon-status">已过期</div>
                    </div>
                </div>
                <?php endforeach; ?>
            </div>
            <?php endif; ?>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 标签页切换
            const tabs = document.querySelectorAll('.tab');
            const tabContents = document.querySelectorAll('.tab-content');

            tabs.forEach(tab => {
                tab.addEventListener('click', function() {
                    const tabId = this.getAttribute('data-tab');

                    // 移除所有标签和内容的active类
                    tabs.forEach(t => t.classList.remove('active'));
                    tabContents.forEach(c => c.style.display = 'none');

                    // 添加active类到当前标签和对应内容
                    this.classList.add('active');
                    document.getElementById(`${tabId}-content`).style.display = 'block';
                });
            });
        });
    </script>
</body>
</html>
