<?php
session_start();

// 检查用户是否已登录
if (!isset($_SESSION['user_id'])) {
    header('Location: ../login/index.php');
    exit;
}

// 检查是否提供了内容ID
if (!isset($_GET['id']) || empty($_GET['id'])) {
    header('Location: index.php');
    exit;
}

$post_id = intval($_GET['id']);

// 引入数据库配置
require_once '../../includes/db_config.php';

try {
    $dsn = "mysql:host={$db_config['host']};dbname={$db_config['dbname']};charset={$db_config['charset']};port={$db_config['port']}";
    $pdo = new PDO($dsn, $db_config['username'], $db_config['password']);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

    // 获取内容详情
    $stmt = $pdo->prepare("
        SELECT p.*, u.username, u.avatar
        FROM universe_posts p
        JOIN users u ON p.user_id = u.id
        WHERE p.id = :id AND p.status = 1
    ");
    $stmt->execute(['id' => $post_id]);
    $post = $stmt->fetch(PDO::FETCH_ASSOC);

    // 如果内容不存在，跳转回列表页
    if (!$post) {
        header('Location: index.php');
        exit;
    }

    // 获取分类名称
    $stmt = $pdo->prepare("SELECT name FROM universe_categories WHERE id = :id");
    $stmt->execute(['id' => $post['category_id']]);
    $category = $stmt->fetch(PDO::FETCH_ASSOC);
    $post['category_name'] = $category ? $category['name'] : '';

    $stmt = $pdo->prepare("SELECT name FROM universe_subcategories WHERE id = :id");
    $stmt->execute(['id' => $post['subcategory_id']]);
    $subcategory = $stmt->fetch(PDO::FETCH_ASSOC);
    $post['subcategory_name'] = $subcategory ? $subcategory['name'] : '';

    // 更新浏览次数
    $stmt = $pdo->prepare("UPDATE universe_posts SET views = views + 1 WHERE id = :id");
    $stmt->execute(['id' => $post_id]);

    // 获取评论列表
    $stmt = $pdo->prepare("
        SELECT c.*, u.username, u.avatar
        FROM universe_comments c
        JOIN users u ON c.user_id = u.id
        WHERE c.post_id = :post_id AND c.status = 1 AND c.parent_id = 0
        ORDER BY c.created_at DESC
    ");
    $stmt->execute(['post_id' => $post_id]);
    $comments = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // 获取每条评论的回复
    foreach ($comments as &$comment) {
        $stmt = $pdo->prepare("
            SELECT r.*, u.username, u.avatar
            FROM universe_comments r
            JOIN users u ON r.user_id = u.id
            WHERE r.post_id = :post_id AND r.status = 1 AND r.parent_id = :comment_id
            ORDER BY r.created_at ASC
        ");
        $stmt->execute([
            'post_id' => $post_id,
            'comment_id' => $comment['id']
        ]);
        $comment['replies'] = $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    // 检查当前用户是否已点赞
    $stmt = $pdo->prepare("SELECT id FROM universe_likes WHERE post_id = :post_id AND user_id = :user_id");
    $stmt->execute([
        'post_id' => $post_id,
        'user_id' => $_SESSION['user_id']
    ]);
    $has_liked = $stmt->fetch() ? true : false;

    // 检查当前用户是否已收藏
    $stmt = $pdo->prepare("SELECT id FROM universe_favorites WHERE post_id = :post_id AND user_id = :user_id");
    $stmt->execute([
        'post_id' => $post_id,
        'user_id' => $_SESSION['user_id']
    ]);
    $has_favorited = $stmt->fetch() ? true : false;
} catch (PDOException $e) {
    error_log("趣玩宇宙详情页面错误: " . $e->getMessage());
    $db_error = true;
}
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <meta name="theme-color" content="#FFFFFF">
    <title><?php echo $post['title']; ?> - 趣玩宇宙</title>
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="css/detail.css">
    <!-- 引入字体图标库 -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <!-- Toast提示 -->
    <div class="toast" id="toast"></div>

    <!-- 顶部导航栏 -->
    <div class="header-bar">
        <a href="javascript:history.back()" class="back-button">
            <i class="fas fa-arrow-left"></i>
        </a>
        <div class="header-title">内容详情</div>
        <div class="share-icon">
            <i class="fas fa-share-alt"></i>
        </div>
    </div>

    <!-- 内容区域 -->
    <div class="content-container">
        <div class="detail-card">
            <div class="card-header">
                <div class="user-info">
                    <div class="avatar">
                        <img src="<?php echo !empty($post['avatar']) ? $post['avatar'] : 'https://s1.imagehub.cc/images/2025/04/26/f5c2f26c4564aabcbb56c5fd30f1d652.jpg'; ?>" alt="用户头像">
                    </div>
                    <div class="user-meta">
                        <div class="username"><?php echo $post['username']; ?></div>
                        <div class="post-meta">
                            <span class="category"><?php echo $post['category_name']; ?> · <?php echo $post['subcategory_name']; ?></span>
                            <span class="time"><?php echo date('Y-m-d H:i', strtotime($post['created_at'])); ?></span>
                        </div>
                    </div>
                </div>
            </div>

            <div class="card-content">
                <h1 class="post-title"><?php echo $post['title']; ?></h1>

                <div class="post-content">
                    <?php echo $post['content']; ?>
                </div>
            </div>

            <div class="card-footer">
                <div class="action-buttons">
                    <div class="action-button <?php echo $has_liked ? 'active' : ''; ?>" id="like-button" data-id="<?php echo $post['id']; ?>">
                        <i class="<?php echo $has_liked ? 'fas' : 'far'; ?> fa-heart"></i>
                        <span><?php echo $post['likes']; ?></span>
                    </div>
                    <div class="action-button" id="comment-button">
                        <i class="far fa-comment"></i>
                        <span><?php echo $post['comments']; ?></span>
                    </div>
                    <div class="action-button <?php echo $has_favorited ? 'active' : ''; ?>" id="favorite-button" data-id="<?php echo $post['id']; ?>">
                        <i class="<?php echo $has_favorited ? 'fas' : 'far'; ?> fa-star"></i>
                        <span>收藏</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- 评论区 -->
        <div class="comment-section">
            <div class="section-header">
                <h2>评论 (<?php echo count($comments); ?>)</h2>
            </div>

            <div class="comment-list">
                <?php if (empty($comments)): ?>
                <div class="empty-comment">
                    <p>暂无评论，快来发表第一条评论吧！</p>
                </div>
                <?php else: ?>
                <?php foreach ($comments as $comment): ?>
                <div class="comment-item">
                    <div class="comment-avatar">
                        <img src="<?php echo !empty($comment['avatar']) ? $comment['avatar'] : 'https://s1.imagehub.cc/images/2025/04/26/f5c2f26c4564aabcbb56c5fd30f1d652.jpg'; ?>" alt="用户头像">
                    </div>
                    <div class="comment-content">
                        <div class="comment-user"><?php echo $comment['username']; ?></div>
                        <div class="comment-text"><?php echo $comment['content']; ?></div>
                        <div class="comment-meta">
                            <span class="comment-time"><?php echo date('m-d H:i', strtotime($comment['created_at'])); ?></span>
                            <span class="reply-button" data-id="<?php echo $comment['id']; ?>">回复</span>
                        </div>

                        <?php if (!empty($comment['replies'])): ?>
                        <div class="reply-list">
                            <?php foreach ($comment['replies'] as $reply): ?>
                            <div class="reply-item">
                                <div class="reply-avatar">
                                    <img src="<?php echo !empty($reply['avatar']) ? $reply['avatar'] : 'https://s1.imagehub.cc/images/2025/04/26/f5c2f26c4564aabcbb56c5fd30f1d652.jpg'; ?>" alt="用户头像">
                                </div>
                                <div class="reply-content">
                                    <div class="reply-user"><?php echo $reply['username']; ?></div>
                                    <div class="reply-text"><?php echo $reply['content']; ?></div>
                                    <div class="reply-meta">
                                        <span class="reply-time"><?php echo date('m-d H:i', strtotime($reply['created_at'])); ?></span>
                                    </div>
                                </div>
                            </div>
                            <?php endforeach; ?>
                        </div>
                        <?php endif; ?>
                    </div>
                </div>
                <?php endforeach; ?>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- 评论输入框 -->
    <div class="comment-input-container">
        <input type="text" id="comment-input" placeholder="写下你的评论...">
        <button id="send-comment">发送</button>
    </div>

    <script src="js/detail.js"></script>
</body>
</html>
