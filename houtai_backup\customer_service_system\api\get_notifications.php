<?php
// 获取通知列表API
header('Content-Type: application/json; charset=UTF-8');
ini_set('default_charset', 'UTF-8');

session_start();

// 检查登录状态
if (!isset($_SESSION['cs_logged_in']) || !$_SESSION['cs_logged_in']) {
    http_response_code(401);
    echo json_encode(['error' => '未登录']);
    exit;
}

// 引用数据库配置文件
require_once '../../db_config.php';

try {
    $pdo = getDbConnection();
    
    $notifications = [];
    $total_count = 0;
    
    // 获取待处理的会话通知
    $stmt = $pdo->query("
        SELECT session_id, user_name, user_phone, created_at 
        FROM customer_service_sessions 
        WHERE status = 'waiting' 
        ORDER BY created_at DESC 
        LIMIT 10
    ");
    $waiting_sessions = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    foreach ($waiting_sessions as $session) {
        $notifications[] = [
            'id' => 'session_' . $session['session_id'],
            'type' => 'session_waiting',
            'title' => '新的客户咨询',
            'message' => ($session['user_name'] ?? '用户') . ' 正在等待客服回复',
            'icon' => 'fas fa-comment',
            'color' => 'primary',
            'time' => $session['created_at'],
            'unread' => true,
            'action_url' => 'sessions.php?status=waiting'
        ];
        $total_count++;
    }
    
    // 获取待质检的通知（仅超级管理员）
    if ($_SESSION['cs_role'] === 'super_admin') {
        $stmt = $pdo->query("
            SELECT qc.id, qc.session_id, qc.created_at, cs.name as cs_name
            FROM customer_service_quality_checks qc
            LEFT JOIN customer_service_users cs ON qc.customer_service_id = cs.id
            WHERE qc.status = 'pending' 
            ORDER BY qc.created_at DESC 
            LIMIT 5
        ");
        $pending_quality_checks = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        foreach ($pending_quality_checks as $check) {
            $notifications[] = [
                'id' => 'quality_' . $check['id'],
                'type' => 'quality_check',
                'title' => '质检任务',
                'message' => '客服 ' . ($check['cs_name'] ?? '未知') . ' 的服务需要质检',
                'icon' => 'fas fa-star',
                'color' => 'warning',
                'time' => $check['created_at'],
                'unread' => true,
                'action_url' => 'quality_check.php'
            ];
            $total_count++;
        }
    }
    
    // 获取分配给当前用户的活跃会话
    $stmt = $pdo->prepare("
        SELECT session_id, user_name, user_phone, started_at 
        FROM customer_service_sessions 
        WHERE customer_service_id = ? AND status = 'active' 
        ORDER BY started_at DESC 
        LIMIT 5
    ");
    $stmt->execute([$_SESSION['cs_user_id']]);
    $my_active_sessions = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    foreach ($my_active_sessions as $session) {
        $notifications[] = [
            'id' => 'active_' . $session['session_id'],
            'type' => 'session_active',
            'title' => '活跃会话',
            'message' => '您与 ' . ($session['user_name'] ?? '用户') . ' 的会话正在进行中',
            'icon' => 'fas fa-comment-dots',
            'color' => 'info',
            'time' => $session['started_at'],
            'unread' => false,
            'action_url' => 'sessions.php?status=active&cs_id=' . $_SESSION['cs_user_id']
        ];
    }
    
    // 按时间排序
    usort($notifications, function($a, $b) {
        return strtotime($b['time']) - strtotime($a['time']);
    });
    
    // 限制返回数量
    $notifications = array_slice($notifications, 0, 15);
    
    // 格式化时间
    foreach ($notifications as &$notification) {
        $time_diff = time() - strtotime($notification['time']);
        if ($time_diff < 60) {
            $notification['time_formatted'] = '刚刚';
        } elseif ($time_diff < 3600) {
            $notification['time_formatted'] = floor($time_diff / 60) . '分钟前';
        } elseif ($time_diff < 86400) {
            $notification['time_formatted'] = floor($time_diff / 3600) . '小时前';
        } else {
            $notification['time_formatted'] = date('m-d H:i', strtotime($notification['time']));
        }
    }
    
    echo json_encode([
        'success' => true,
        'count' => $total_count,
        'notifications' => $notifications,
        'timestamp' => time()
    ]);
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => '获取通知失败',
        'message' => $e->getMessage()
    ]);
}
?>
