<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <title>趣玩星球客服中心</title>
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <div id="app">
        <!-- 顶部导航栏 -->
        <header class="chat-header">
            <div class="header-left">
                <button class="back-btn" @click="goBack">
                    <i class="fas fa-arrow-left"></i>
                </button>
                <div class="header-info">
                    <h1 class="header-title">{{ currentTitle }}</h1>
                    <span class="header-status" :class="connectionStatus">
                        {{ connectionStatusText }}
                    </span>
                </div>
            </div>
            <div class="header-right">
                <button class="menu-btn" @click="showMenu = !showMenu">
                    <i class="fas fa-ellipsis-v"></i>
                </button>
            </div>
        </header>

        <!-- 菜单弹窗 -->
        <div class="menu-overlay" v-show="showMenu" @click="showMenu = false">
            <div class="menu-popup" @click.stop>
                <div class="menu-item" @click="endSession">
                    <i class="fas fa-sign-out-alt"></i>
                    <span>结束服务</span>
                </div>
                <div class="menu-item" @click="showRating = true; showMenu = false">
                    <i class="fas fa-star"></i>
                    <span>服务评价</span>
                </div>
            </div>
        </div>

        <!-- 聊天消息区域 -->
        <main class="chat-container" ref="chatContainer">
            <div class="messages-wrapper">
                <!-- 欢迎消息 -->
                <div class="welcome-message" v-if="!sessionStarted">
                    <div class="welcome-avatar">
                        <img src="https://s1.imagehub.cc/images/2025/05/26/2cdd63b39f96072fc60cd733027d2a30.png" alt="客服头像">
                    </div>
                    <div class="welcome-content">
                        <h3>欢迎来到趣玩星球客服中心！🌟</h3>
                        <p>我是您的专属客服助手，很高兴为您服务！</p>
                        <p>请问有什么可以帮助您的吗？</p>
                    </div>
                </div>

                <!-- 消息列表 -->
                <div class="message-list">
                    <div 
                        v-for="message in messages" 
                        :key="message.id"
                        class="message-item"
                        :class="getMessageClass(message)"
                    >
                        <!-- 发送者头像 -->
                        <div class="message-avatar" v-if="message.sender_type !== 'user'">
                            <img :src="getAvatarUrl(message)" :alt="message.sender_name">
                        </div>

                        <!-- 消息内容 -->
                        <div class="message-content">
                            <!-- 发送者名称 -->
                            <div class="message-sender" v-if="message.sender_type !== 'user'">
                                {{ message.sender_name }}
                            </div>

                            <!-- 消息气泡 -->
                            <div class="message-bubble">
                                <!-- 文本消息 -->
                                <div v-if="message.message_type === 'text'" class="message-text">
                                    {{ message.content }}
                                </div>

                                <!-- 图片消息 -->
                                <div v-else-if="message.message_type === 'image'" class="message-image">
                                    <img :src="message.file_url" :alt="message.file_name" @click="previewImage(message.file_url)">
                                </div>

                                <!-- 文件消息 -->
                                <div v-else-if="message.message_type === 'file'" class="message-file">
                                    <i class="fas fa-file"></i>
                                    <span>{{ message.file_name }}</span>
                                    <a :href="message.file_url" target="_blank" class="download-btn">下载</a>
                                </div>
                            </div>

                            <!-- 消息时间 -->
                            <div class="message-time">
                                {{ formatTime(message.created_at) }}
                            </div>
                        </div>

                        <!-- 用户头像（右侧） -->
                        <div class="message-avatar" v-if="message.sender_type === 'user'">
                            <img :src="userAvatar" alt="我">
                        </div>
                    </div>
                </div>

                <!-- 正在输入提示 -->
                <div class="typing-indicator" v-show="isTyping">
                    <div class="typing-avatar">
                        <img src="https://s1.imagehub.cc/images/2025/05/26/2cdd63b39f96072fc60cd733027d2a30.png" alt="客服">
                    </div>
                    <div class="typing-content">
                        <div class="typing-dots">
                            <span></span>
                            <span></span>
                            <span></span>
                        </div>
                        <div class="typing-text">客服正在输入...</div>
                    </div>
                </div>
            </div>
        </main>

        <!-- 底部输入区域 -->
        <footer class="chat-input">
            <div class="input-toolbar">
                <!-- 表情按钮 -->
                <button class="toolbar-btn" @click="showEmoji = !showEmoji">
                    <i class="fas fa-smile"></i>
                </button>

                <!-- 图片上传按钮 -->
                <button class="toolbar-btn" @click="$refs.imageInput.click()">
                    <i class="fas fa-image"></i>
                </button>
                <input 
                    type="file" 
                    ref="imageInput" 
                    accept="image/*" 
                    style="display: none"
                    @change="handleImageUpload"
                >

                <!-- 输入框 -->
                <div class="input-wrapper">
                    <textarea 
                        v-model="inputMessage"
                        placeholder="请输入您的问题..."
                        class="message-input"
                        rows="1"
                        @keydown.enter.prevent="sendMessage"
                        @input="adjustTextareaHeight"
                        ref="messageInput"
                    ></textarea>
                </div>

                <!-- 发送按钮 -->
                <button 
                    class="send-btn" 
                    :class="{ active: inputMessage.trim() }"
                    @click="sendMessage"
                    :disabled="!inputMessage.trim() || isSending"
                >
                    <i class="fas fa-paper-plane"></i>
                </button>
            </div>

            <!-- 表情面板 -->
            <div class="emoji-panel" v-show="showEmoji">
                <div class="emoji-grid">
                    <span 
                        v-for="emoji in emojiList" 
                        :key="emoji"
                        class="emoji-item"
                        @click="insertEmoji(emoji)"
                    >
                        {{ emoji }}
                    </span>
                </div>
            </div>
        </footer>

        <!-- 服务评价弹窗 -->
        <div class="rating-overlay" v-show="showRating" @click="showRating = false">
            <div class="rating-popup" @click.stop>
                <h3>服务评价</h3>
                <div class="rating-stars">
                    <i 
                        v-for="star in 5" 
                        :key="star"
                        class="fas fa-star"
                        :class="{ active: star <= rating }"
                        @click="rating = star"
                    ></i>
                </div>
                <textarea 
                    v-model="ratingComment"
                    placeholder="请分享您的服务体验..."
                    class="rating-comment"
                ></textarea>
                <div class="rating-actions">
                    <button class="btn-cancel" @click="showRating = false">取消</button>
                    <button class="btn-submit" @click="submitRating">提交评价</button>
                </div>
            </div>
        </div>

        <!-- 图片预览 -->
        <div class="image-preview-overlay" v-show="previewImageUrl" @click="previewImageUrl = ''">
            <div class="image-preview-container">
                <img :src="previewImageUrl" alt="图片预览">
                <button class="close-preview" @click="previewImageUrl = ''">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>

        <!-- 加载提示 -->
        <div class="loading-overlay" v-show="isLoading">
            <div class="loading-spinner">
                <div class="spinner"></div>
                <p>{{ loadingText }}</p>
            </div>
        </div>
    </div>

    <!-- Vue.js -->
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
    <script src="js/app.js"></script>
</body>
</html>
