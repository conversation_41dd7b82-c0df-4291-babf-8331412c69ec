/*
 * 陪玩认证页面 CSS样式
 * 设计要求：美观符合主题，年轻化现代化，UI精美，icon精美，布局样式美观，交互性强
 */

:root {
    --theme-color: #6F7BF5; /* 主题色 */
    --text-primary: #333;
    --text-secondary: #666;
    --bg-light: #ffffff;
    --bg-grey: #f4f6f8;
    --border-color: #e0e0e0;
    --success-color: #4CAF50;
    --warning-color: #FFC107;
    --error-color: #F44336;
    --font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
    --border-radius: 8px;
    --box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

body {
    background-color: var(--bg-grey);
    color: var(--text-primary);
    font-family: var(--font-family);
    margin: 0;
    padding-top: 56px; /* 为固定头部预留空间 */
    line-height: 1.6;
}

.header-bar {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    height: 56px;
    background-color: var(--theme-color);
    display: flex;
    align-items: center;
    padding: 0 16px;
    z-index: 1000;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    color: white;
}

.header-bar .back-button {
    font-size: 20px;
    color: white;
    text-decoration: none;
    margin-right: 16px;
    padding: 8px; /* 增加点击区域 */
    border-radius: 50%;
    transition: background-color 0.2s ease;
}

.header-bar .back-button:hover {
    background-color: rgba(255,255,255,0.2);
}

.header-bar .title {
    font-size: 18px;
    font-weight: 600;
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
}

.page-container {
    padding: 20px;
    max-width: 700px; /* 限制内容最大宽度，使其在PC端也美观 */
    margin: 0 auto;
}

/* 初始状态/加载提示 */
.status-loading,
.status-message {
    text-align: center;
    padding: 40px 20px;
    background-color: var(--bg-light);
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    margin-bottom: 20px;
}

.status-message h3 {
    color: var(--theme-color);
    margin-top: 0;
}

/* 表单容器和通用样式将在这里定义 */
.application-form {
    background-color: var(--bg-light);
    padding: 24px;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
}

.application-form h2 {
    text-align: center;
    color: var(--theme-color);
    margin-top: 0;
    margin-bottom: 24px;
    font-size: 22px;
}

/* Form Sections */
.form-section {
    margin-bottom: 30px;
    padding-bottom: 20px;
    border-bottom: 1px solid var(--border-color);
}

.form-section:last-of-type {
    border-bottom: none;
    margin-bottom: 20px;
}

.section-title {
    font-size: 18px;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 16px;
    display: flex;
    align-items: center;
}

.section-title i {
    margin-right: 8px;
    color: var(--theme-color);
}

.section-description {
    font-size: 14px;
    color: var(--text-secondary);
    margin-bottom: 16px;
    line-height: 1.5;
}

/* Form Groups and Inputs */
.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    font-weight: 500;
    margin-bottom: 8px;
    color: var(--text-secondary);
    font-size: 14px;
}

.form-group label i {
    margin-right: 6px;
    width: 16px; /* Ensure consistent icon spacing */
    text-align: center;
    color: var(--theme-color);
}

.form-group input[type="text"],
.form-group input[type="email"],
.form-group input[type="password"],
.form-group input[type="number"],
.form-group textarea {
    width: 100%;
    padding: 12px 15px;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    box-sizing: border-box;
    font-size: 16px;
    color: var(--text-primary);
    transition: border-color 0.2s ease, box-shadow 0.2s ease;
}

.form-group input[type="text"]:focus,
.form-group input[type="email"]:focus,
.form-group input[type="password"]:focus,
.form-group input[type="number"]:focus,
.form-group textarea:focus {
    outline: none;
    border-color: var(--theme-color);
    box-shadow: 0 0 0 2px rgba(111, 123, 245, 0.3); /* Theme color focus ring */
}

.form-group textarea {
    resize: vertical;
    min-height: 100px;
}

.form-text {
    display: block;
    font-size: 12px;
    color: var(--text-secondary);
    margin-top: 4px;
}

/* File Upload Styling */
.file-upload-group .file-input-wrapper {
    position: relative;
    display: inline-block;
    width: 100%;
    cursor: pointer;
}

.file-upload-group input[type="file"] {
    position: absolute;
    left: 0;
    top: 0;
    opacity: 0;
    width: 100%;
    height: 100%;
    cursor: pointer;
}

.file-upload-group .file-input-label {
    display: block;
    padding: 12px 15px;
    background-color: #e9ecef;
    border: 1px dashed var(--border-color);
    border-radius: var(--border-radius);
    text-align: center;
    color: var(--text-secondary);
    transition: background-color 0.2s ease, border-color 0.2s ease;
}

.file-upload-group .file-input-wrapper:hover .file-input-label {
    background-color: #dde2e6;
    border-color: var(--theme-color);
}

.file-preview {
    margin-top: 10px;
}

.file-preview img {
    max-width: 100%;
    max-height: 150px;
    border-radius: var(--border-radius);
    border: 1px solid var(--border-color);
    display: block;
}

.file-preview audio {
    width: 100%;
}

/* Terms and Conditions */
.terms-group {
    display: flex;
    align-items: center;
    margin-bottom: 24px;
}

.terms-group input[type="checkbox"] {
    margin-right: 10px;
    width: 18px;
    height: 18px;
    accent-color: var(--theme-color);
}

.terms-group label {
    font-size: 14px;
    color: var(--text-secondary);
    margin-bottom: 0; /* Override default label margin */
}

.terms-group label a {
    color: var(--theme-color);
    text-decoration: none;
}

.terms-group label a:hover {
    text-decoration: underline;
}

/* Submit Button */
.submit-btn {
    display: block;
    width: 100%;
    padding: 14px 20px;
    background-color: var(--theme-color);
    color: white;
    border: none;
    border-radius: var(--border-radius);
    font-size: 16px;
    font-weight: 600;
    text-align: center;
    cursor: pointer;
    transition: background-color 0.2s ease, transform 0.1s ease;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.submit-btn i {
    margin-right: 8px;
}

.submit-btn:hover {
    background-color: #5a67d8; /* Darker shade of theme color */
}

.submit-btn:active {
    transform: translateY(1px);
    box-shadow: none;
}

.form-title-icon {
    margin-right: 10px;
    font-size: 20px;
}

/* Responsive Adjustments */
@media (max-width: 600px) {
    .application-form {
        padding: 20px;
    }
    .section-title {
        font-size: 16px;
    }
    .form-group input[type="text"],
    .form-group input[type="email"],
    .form-group input[type="password"],
    .form-group input[type="number"],
    .form-group textarea {
        padding: 10px 12px;
        font-size: 15px;
    }
    .submit-btn {
        padding: 12px 15px;
        font-size: 15px;
    }
}

/* Status Message Styling */
.status-message {
    text-align: center;
    padding: 30px 20px;
    background-color: var(--bg-light);
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    margin-bottom: 20px;
}

.status-message h3 {
    color: var(--theme-color);
    margin-top: 0;
    margin-bottom: 10px;
    font-size: 20px;
}

.status-message p {
    font-size: 16px;
    color: var(--text-secondary);
    line-height: 1.6;
}

.status-message.success {
    border-left: 5px solid var(--success-color);
}
.status-message.success h3 {
    color: var(--success-color);
}

.status-message.pending {
    border-left: 5px solid var(--warning-color);
}
.status-message.pending h3 {
    color: var(--warning-color);
}

.status-message.rejected {
    border-left: 5px solid var(--error-color);
}
.status-message.rejected h3 {
    color: var(--error-color);
}

/* Utility for hiding elements initially */
.hidden {
    display: none;
}

