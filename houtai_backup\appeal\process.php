<?php
/**
 * 申诉处理页面
 * 趣玩星球管理后台
 */

// 引入认证检查
require_once '../auth_check.php';

// 引入数据库配置
require_once '../db_config.php';

// 引入布局文件
require_once '../admin_layout.php';

// 获取参数
$appeal_id = intval($_GET['id'] ?? 0);
$action = $_GET['action'] ?? '';

if (!$appeal_id || !in_array($action, ['approve', 'reject'])) {
    header('Location: index.php');
    exit;
}

// 处理表单提交
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        $pdo = getDbConnection();

        $reply = trim($_POST['reply'] ?? '');
        $action = $_POST['action'] ?? '';

        if (empty($reply)) {
            $error_message = "请填写处理意见";
        } else {
            // 开始事务
            $pdo->beginTransaction();

            // 更新申诉状态
            $new_status = $action === 'approve' ? 'approved' : 'rejected';
            $stmt = $pdo->prepare("
                UPDATE user_appeals
                SET status = ?, admin_reply = ?, admin_id = ?, admin_name = ?, processed_at = NOW()
                WHERE id = ?
            ");
            $stmt->execute([$new_status, $reply, $admin_id, $admin_name, $appeal_id]);

            // 记录操作日志
            $stmt = $pdo->prepare("
                INSERT INTO appeal_logs (appeal_id, action, description, admin_id, admin_name, ip_address, user_agent)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            ");

            $log_action = $action === 'approve' ? 'approve' : 'reject';
            $log_description = $action === 'approve' ? "申诉已通过：$reply" : "申诉已拒绝：$reply";
            $ip_address = $_SERVER['HTTP_X_FORWARDED_FOR'] ?? $_SERVER['REMOTE_ADDR'] ?? '';
            $user_agent = $_SERVER['HTTP_USER_AGENT'] ?? '';

            $stmt->execute([$appeal_id, $log_action, $log_description, $admin_id, $admin_name, $ip_address, $user_agent]);

            // 如果是通过申诉，需要解封用户
            if ($action === 'approve') {
                // 获取申诉的手机号
                $stmt = $pdo->prepare("SELECT phone FROM user_appeals WHERE id = ?");
                $stmt->execute([$appeal_id]);
                $phone = $stmt->fetchColumn();

                if ($phone) {
                    // 查找用户并解封
                    $stmt = $pdo->prepare("SELECT id FROM users WHERE phone = ?");
                    $stmt->execute([$phone]);
                    $user_id = $stmt->fetchColumn();

                    if ($user_id) {
                        // 更新用户状态为正常
                        $stmt = $pdo->prepare("UPDATE users SET status = 'active' WHERE id = ?");
                        $stmt->execute([$user_id]);

                        // 将相关的封号记录设为已取消 - 使用多种方式确保成功
                        try {
                            // 首先尝试直接删除封号记录（最简单有效）
                            $stmt = $pdo->prepare("DELETE FROM user_bans WHERE user_id = ? AND status = 'active'");
                            $stmt->execute([$user_id]);
                            $affected_bans = $stmt->rowCount();

                        } catch (PDOException $e) {
                            // 如果删除失败，尝试更新状态
                            try {
                                // 尝试不同的状态值
                                $possible_statuses = ['inactive', 'expired', 'disabled', 'cancelled'];
                                $success = false;

                                foreach ($possible_statuses as $status_value) {
                                    try {
                                        $stmt = $pdo->prepare("UPDATE user_bans SET status = ? WHERE user_id = ? AND status = 'active'");
                                        $stmt->execute([$status_value, $user_id]);
                                        $affected_bans = $stmt->rowCount();
                                        $success = true;
                                        break;
                                    } catch (PDOException $e2) {
                                        // 继续尝试下一个状态值
                                        continue;
                                    }
                                }

                                if (!$success) {
                                    // 如果所有状态值都失败，尝试删除
                                    $stmt = $pdo->prepare("DELETE FROM user_bans WHERE user_id = ?");
                                    $stmt->execute([$user_id]);
                                    $affected_bans = $stmt->rowCount();
                                }

                            } catch (PDOException $e2) {
                                // 如果user_bans表操作都失败，只要用户状态更新成功就算解封成功
                                error_log("封号记录处理失败，但用户状态已更新: " . $e2->getMessage());
                                $affected_bans = 0;
                            }
                        }

                        // 记录解封日志
                        try {
                            $stmt = $pdo->prepare("
                                INSERT INTO admin_operation_logs (admin_id, admin_name, operation_type, target_type, target_id, description, ip_address, user_agent)
                                VALUES (?, ?, 'unban', 'user', ?, ?, ?, ?)
                            ");
                            $stmt->execute([
                                $admin_id,
                                $admin_name,
                                $user_id,
                                "通过申诉解封用户，申诉ID：$appeal_id",
                                $ip_address,
                                $user_agent
                            ]);
                        } catch (Exception $e) {
                            // 如果日志表不存在，忽略错误
                        }
                    }
                }
            }

            // 提交事务
            $pdo->commit();

            $success_message = $action === 'approve' ? "申诉已通过，用户已解封" : "申诉已拒绝";

            // 3秒后跳转
            echo "<script>
                setTimeout(function() {
                    window.location.href = 'detail.php?id=$appeal_id';
                }, 3000);
            </script>";
        }

    } catch (Exception $e) {
        if (isset($pdo) && $pdo->inTransaction()) {
            $pdo->rollBack();
        }
        $error_message = "处理失败：" . $e->getMessage();
    }
}

// 获取申诉详情
try {
    $pdo = getDbConnection();
    $stmt = $pdo->prepare("SELECT * FROM user_appeals WHERE id = ?");
    $stmt->execute([$appeal_id]);
    $appeal = $stmt->fetch(PDO::FETCH_ASSOC);

    if (!$appeal) {
        header('Location: index.php');
        exit;
    }

    if ($appeal['status'] !== 'pending') {
        header('Location: detail.php?id=' . $appeal_id);
        exit;
    }

} catch (Exception $e) {
    $error_message = "数据加载失败：" . $e->getMessage();
    $appeal = null;
}

$type_map = [
    'wrongful_ban' => '误封申诉',
    'account_stolen' => '账号被盗',
    'system_error' => '系统错误',
    'other' => '其他原因'
];

$action_text = $action === 'approve' ? '通过' : '拒绝';
$action_class = $action === 'approve' ? 'success' : 'danger';

startAdminPage('处理申诉', '申诉审核');
?>

<div class="admin-content">
    <!-- 页面头部 -->
    <div class="page-header">
        <div class="page-title">
            <h1><i class="fas fa-gavel"></i> <?php echo $action_text; ?>申诉 #<?php echo $appeal_id; ?></h1>
            <div class="breadcrumb">
                <a href="index.php">申诉审核</a> /
                <a href="detail.php?id=<?php echo $appeal_id; ?>">申诉详情</a> /
                处理申诉
            </div>
        </div>
    </div>

    <?php if (isset($success_message)): ?>
        <div class="alert alert-success">
            <i class="fas fa-check-circle"></i>
            <?php echo $success_message; ?>
            <br><small>3秒后自动跳转到申诉详情页面...</small>
        </div>
    <?php endif; ?>

    <?php if (isset($error_message)): ?>
        <div class="alert alert-danger">
            <i class="fas fa-exclamation-triangle"></i>
            <?php echo $error_message; ?>
        </div>
    <?php endif; ?>

    <?php if ($appeal && !isset($success_message)): ?>
        <div class="process-container">
            <!-- 申诉信息概览 -->
            <div class="appeal-summary">
                <h3><i class="fas fa-info-circle"></i> 申诉信息概览</h3>
                <div class="summary-grid">
                    <div class="summary-item">
                        <label>手机号</label>
                        <span><?php echo htmlspecialchars($appeal['phone']); ?></span>
                    </div>
                    <div class="summary-item">
                        <label>申诉类型</label>
                        <span><?php echo $type_map[$appeal['appeal_type']] ?? $appeal['appeal_type']; ?></span>
                    </div>
                    <div class="summary-item">
                        <label>提交时间</label>
                        <span><?php echo date('Y-m-d H:i:s', strtotime($appeal['created_at'])); ?></span>
                    </div>
                    <div class="summary-item">
                        <label>联系邮箱</label>
                        <span><?php echo htmlspecialchars($appeal['email']); ?></span>
                    </div>
                </div>

                <div class="reason-section">
                    <label>申诉原因</label>
                    <div class="reason-content">
                        <?php echo nl2br(htmlspecialchars($appeal['reason'])); ?>
                    </div>
                </div>
            </div>

            <!-- 处理表单 -->
            <div class="process-form">
                <h3>
                    <i class="fas fa-<?php echo $action === 'approve' ? 'check' : 'times'; ?>"></i>
                    <?php echo $action_text; ?>申诉
                </h3>

                <form method="POST" class="form">
                    <input type="hidden" name="action" value="<?php echo $action; ?>">

                    <div class="form-group">
                        <label for="reply" class="required">处理意见</label>
                        <textarea name="reply" id="reply" class="form-control" rows="6" required
                                  placeholder="请填写<?php echo $action_text; ?>申诉的具体原因和处理意见..."></textarea>
                        <div class="form-help">
                            <?php if ($action === 'approve'): ?>
                                <i class="fas fa-info-circle"></i>
                                通过申诉后，系统将自动解封该用户账号，请确认处理意见准确无误。
                            <?php else: ?>
                                <i class="fas fa-info-circle"></i>
                                拒绝申诉后，用户可以查看拒绝原因，请详细说明拒绝理由。
                            <?php endif; ?>
                        </div>
                    </div>

                    <div class="form-actions">
                        <a href="detail.php?id=<?php echo $appeal_id; ?>" class="btn btn-secondary">
                            <i class="fas fa-arrow-left"></i> 返回
                        </a>
                        <button type="submit" class="btn btn-<?php echo $action_class; ?>">
                            <i class="fas fa-<?php echo $action === 'approve' ? 'check' : 'times'; ?>"></i>
                            确认<?php echo $action_text; ?>
                        </button>
                    </div>
                </form>
            </div>
        </div>
    <?php endif; ?>
</div>

<style>
/* 申诉处理页面样式 */
.process-container {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.appeal-summary, .process-form {
    background: white;
    border-radius: 12px;
    padding: 24px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.appeal-summary h3, .process-form h3 {
    margin: 0 0 20px 0;
    font-size: 18px;
    font-weight: 600;
    color: #1F2937;
    display: flex;
    align-items: center;
    gap: 8px;
}

.summary-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 16px;
    margin-bottom: 20px;
}

.summary-item {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.summary-item label {
    font-size: 14px;
    font-weight: 600;
    color: #6B7280;
}

.summary-item span {
    font-size: 16px;
    color: #1F2937;
}

.reason-section {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.reason-section label {
    font-size: 14px;
    font-weight: 600;
    color: #6B7280;
}

.reason-content {
    background: #F9FAFB;
    border: 1px solid #E5E7EB;
    border-radius: 8px;
    padding: 16px;
    line-height: 1.6;
    color: #374151;
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    color: #374151;
}

.form-group label.required::after {
    content: ' *';
    color: #EF4444;
}

.form-control {
    width: 100%;
    padding: 12px 16px;
    border: 2px solid #E5E7EB;
    border-radius: 8px;
    font-size: 16px;
    transition: border-color 0.2s;
}

.form-control:focus {
    outline: none;
    border-color: #6F7BF5;
    box-shadow: 0 0 0 3px rgba(111, 123, 245, 0.1);
}

.form-help {
    margin-top: 8px;
    font-size: 14px;
    color: #6B7280;
    display: flex;
    align-items: flex-start;
    gap: 6px;
}

.form-actions {
    display: flex;
    gap: 12px;
    justify-content: flex-end;
    margin-top: 24px;
}

.alert {
    padding: 16px 20px;
    border-radius: 8px;
    margin-bottom: 20px;
    display: flex;
    align-items: flex-start;
    gap: 12px;
}

.alert-success {
    background: #D1FAE5;
    border: 1px solid #A7F3D0;
    color: #065F46;
}

.alert-danger {
    background: #FEE2E2;
    border: 1px solid #FECACA;
    color: #991B1B;
}

.alert i {
    margin-top: 2px;
}

@media (max-width: 768px) {
    .summary-grid {
        grid-template-columns: 1fr;
    }

    .form-actions {
        flex-direction: column-reverse;
    }

    .form-actions .btn {
        width: 100%;
        justify-content: center;
    }
}
</style>

<?php endAdminPage(); ?>
