-- 创建系统消息表
-- 请在数据库中执行此SQL来创建系统消息表

USE quwanplanet;

-- 创建系统消息表
CREATE TABLE IF NOT EXISTS `system_messages` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL COMMENT '接收用户ID',
  `title` varchar(255) NOT NULL COMMENT '消息标题',
  `content` text NOT NULL COMMENT '消息内容',
  `type` enum('welcome','notice','warning','promotion','system') NOT NULL DEFAULT 'system' COMMENT '消息类型',
  `is_read` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否已读：0-未读，1-已读',
  `read_at` timestamp NULL DEFAULT NULL COMMENT '阅读时间',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_type` (`type`),
  KEY `idx_is_read` (`is_read`),
  KEY `idx_created_at` (`created_at`),
  CONSTRAINT `fk_system_messages_user_id` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='系统消息表';

-- 创建索引以优化查询性能
CREATE INDEX `idx_user_read_status` ON `system_messages` (`user_id`, `is_read`);
CREATE INDEX `idx_user_type` ON `system_messages` (`user_id`, `type`);

-- 插入一些示例系统消息类型说明（可选）
-- welcome: 欢迎消息
-- notice: 通知消息
-- warning: 警告消息
-- promotion: 推广消息
-- system: 系统消息
