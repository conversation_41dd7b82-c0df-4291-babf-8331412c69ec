<?php
// 简单的实时通信测试
header('Content-Type: text/html; charset=UTF-8');
ini_set('default_charset', 'UTF-8');

session_start();

// 模拟用户登录
if (!isset($_SESSION['user_id'])) {
    $_SESSION['user_id'] = 4;
    $_SESSION['user_name'] = '测试用户';
    $_SESSION['nickname'] = '测试用户';
}

$sessionId = $_GET['session_id'] ?? '';
if (empty($sessionId)) {
    echo '<h1>错误：缺少会话ID</h1>';
    exit;
}
?>

<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>简单实时通信测试</title>
    <style>
        body { 
            font-family: Arial, sans-serif; 
            margin: 20px; 
            background: #f5f5f5; 
        }
        .container { 
            max-width: 800px; 
            margin: 0 auto; 
            background: white; 
            padding: 20px; 
            border-radius: 10px; 
        }
        .status { 
            padding: 10px; 
            margin: 10px 0; 
            border-radius: 5px; 
            font-weight: bold; 
        }
        .connected { background: #d4edda; color: #155724; }
        .disconnected { background: #f8d7da; color: #721c24; }
        .connecting { background: #fff3cd; color: #856404; }
        .log { 
            background: #f8f9fa; 
            padding: 15px; 
            border-radius: 5px; 
            height: 300px; 
            overflow-y: auto; 
            font-family: monospace; 
            font-size: 12px; 
            border: 1px solid #ddd; 
        }
        .log-entry { 
            margin: 5px 0; 
            padding: 5px; 
            border-left: 3px solid #007cba; 
            background: white; 
        }
        .error { border-left-color: #dc3545; }
        .success { border-left-color: #28a745; }
        .info { border-left-color: #17a2b8; }
        button { 
            padding: 10px 20px; 
            margin: 5px; 
            border: none; 
            border-radius: 5px; 
            cursor: pointer; 
        }
        .btn-primary { background: #007cba; color: white; }
        .btn-success { background: #28a745; color: white; }
        .btn-danger { background: #dc3545; color: white; }
        .messages {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            height: 200px;
            overflow-y: auto;
            margin: 10px 0;
            border: 1px solid #ddd;
        }
        .message {
            margin: 10px 0;
            padding: 10px;
            border-radius: 8px;
            background: white;
            border-left: 4px solid #6F7BF5;
        }
        .message.user {
            border-left-color: #28a745;
            text-align: right;
        }
        .message.cs {
            border-left-color: #dc3545;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 简单实时通信测试</h1>
        
        <div class="status connecting" id="status">
            连接状态：准备中...
        </div>
        
        <div>
            <strong>会话ID：</strong><?php echo htmlspecialchars($sessionId); ?><br>
            <strong>用户ID：</strong><?php echo $_SESSION['user_id']; ?><br>
            <strong>用户名：</strong><?php echo $_SESSION['user_name']; ?>
        </div>
        
        <div style="margin: 20px 0;">
            <button class="btn-primary" onclick="startPolling()">开始轮询</button>
            <button class="btn-danger" onclick="stopPolling()">停止轮询</button>
            <button class="btn-success" onclick="testSendMessage()">发送测试消息</button>
            <button onclick="clearLog()">清空日志</button>
        </div>
        
        <h3>消息显示</h3>
        <div class="messages" id="messages">
            <div class="message">等待消息...</div>
        </div>
        
        <h3>连接日志</h3>
        <div class="log" id="log"></div>
        
        <div style="margin-top: 20px;">
            <h3>测试说明</h3>
            <ol>
                <li>点击"开始轮询"开始监听消息</li>
                <li>在客服后台发送消息</li>
                <li>观察这里是否收到消息</li>
                <li>点击"发送测试消息"测试发送功能</li>
            </ol>
            <p>
                <a href="../../houtai_backup/customer_service_system/quick_message_test.php" target="_blank">打开客服后台发送消息</a>
            </p>
        </div>
    </div>
    
    <script>
        const sessionId = '<?php echo $sessionId; ?>';
        const userId = <?php echo $_SESSION['user_id']; ?>;
        let pollingInterval = null;
        let lastMessageId = 0;
        
        function log(message, type = 'info') {
            const logDiv = document.getElementById('log');
            const entry = document.createElement('div');
            entry.className = `log-entry ${type}`;
            entry.innerHTML = `[${new Date().toLocaleTimeString()}] ${message}`;
            logDiv.appendChild(entry);
            logDiv.scrollTop = logDiv.scrollHeight;
        }
        
        function updateStatus(status, className) {
            const statusDiv = document.getElementById('status');
            statusDiv.textContent = `连接状态：${status}`;
            statusDiv.className = `status ${className}`;
        }
        
        function addMessage(type, content, sender) {
            const messagesDiv = document.getElementById('messages');
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${type}`;
            messageDiv.innerHTML = `
                <strong>${sender}:</strong> ${content}
                <div style="font-size: 11px; color: #666; margin-top: 5px;">
                    ${new Date().toLocaleTimeString()}
                </div>
            `;
            messagesDiv.appendChild(messageDiv);
            messagesDiv.scrollTop = messagesDiv.scrollHeight;
        }
        
        function startPolling() {
            if (pollingInterval) {
                clearInterval(pollingInterval);
            }
            
            log('开始轮询消息...', 'info');
            updateStatus('轮询中...', 'connecting');
            
            pollingInterval = setInterval(async function() {
                try {
                    const url = `api/get_new_messages.php?session_id=${sessionId}&last_message_id=${lastMessageId}`;
                    log(`请求: ${url}`, 'info');
                    
                    const response = await fetch(url);
                    const data = await response.json();
                    
                    log(`响应: ${JSON.stringify(data)}`, data.success ? 'success' : 'error');
                    
                    if (data.success) {
                        updateStatus('已连接', 'connected');
                        
                        if (data.messages && data.messages.length > 0) {
                            log(`收到 ${data.messages.length} 条新消息`, 'success');
                            
                            data.messages.forEach(message => {
                                if (message.sender_type === 'customer_service') {
                                    addMessage('cs', message.content, message.sender_name || '客服');
                                    
                                    // 更新最后消息ID
                                    if (message.id > lastMessageId) {
                                        lastMessageId = message.id;
                                    }
                                }
                            });
                        }
                    } else {
                        updateStatus('连接错误', 'disconnected');
                        log(`错误: ${data.error}`, 'error');
                    }
                    
                } catch (error) {
                    log(`网络错误: ${error.message}`, 'error');
                    updateStatus('网络错误', 'disconnected');
                }
            }, 3000); // 每3秒检查一次
        }
        
        function stopPolling() {
            if (pollingInterval) {
                clearInterval(pollingInterval);
                pollingInterval = null;
                log('停止轮询', 'info');
                updateStatus('已停止', 'disconnected');
            }
        }
        
        async function testSendMessage() {
            const testMessage = '测试消息 - ' + new Date().toLocaleTimeString();
            
            try {
                log(`发送测试消息: ${testMessage}`, 'info');
                
                const response = await fetch('api/send_message.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        sessionId: sessionId,
                        message: testMessage
                    })
                });
                
                const data = await response.json();
                log(`发送结果: ${JSON.stringify(data)}`, data.success ? 'success' : 'error');
                
                if (data.success) {
                    addMessage('user', testMessage, '我');
                }
                
            } catch (error) {
                log(`发送失败: ${error.message}`, 'error');
            }
        }
        
        function clearLog() {
            document.getElementById('log').innerHTML = '';
            document.getElementById('messages').innerHTML = '<div class="message">等待消息...</div>';
        }
        
        // 页面加载完成后自动开始轮询
        window.addEventListener('load', function() {
            log('页面加载完成', 'info');
            log(`会话ID: ${sessionId}`, 'info');
            log(`用户ID: ${userId}`, 'info');
            
            // 自动开始轮询
            setTimeout(startPolling, 1000);
        });
        
        // 页面卸载时停止轮询
        window.addEventListener('beforeunload', function() {
            stopPolling();
        });
    </script>
</body>
</html>
