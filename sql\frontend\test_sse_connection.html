<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SSE连接测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            font-weight: bold;
        }
        .status.success { background: #d4edda; color: #155724; }
        .status.error { background: #f8d7da; color: #721c24; }
        .status.info { background: #d1ecf1; color: #0c5460; }
        .log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            margin: 10px 0;
            max-height: 400px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
        .log-entry {
            margin: 5px 0;
            padding: 2px 0;
            border-bottom: 1px solid #eee;
        }
        .timestamp {
            color: #666;
            margin-right: 10px;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        button:disabled {
            background: #6c757d;
            cursor: not-allowed;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔗 SSE连接测试</h1>
        
        <div class="status info" id="status">等待连接...</div>
        
        <div>
            <label for="userId">用户ID:</label>
            <input type="number" id="userId" value="4" min="1">
            <button onclick="startConnection()">开始连接</button>
            <button onclick="stopConnection()">断开连接</button>
            <button onclick="clearLog()">清空日志</button>
        </div>
        
        <h3>连接日志:</h3>
        <div class="log" id="log"></div>
        
        <h3>测试说明:</h3>
        <ul>
            <li>1. 输入用户ID（默认为4）</li>
            <li>2. 点击"开始连接"建立SSE连接</li>
            <li>3. 观察连接状态和日志信息</li>
            <li>4. 在后台发送验证码，观察是否收到通知</li>
        </ul>
    </div>

    <script>
        let eventSource = null;
        let isConnected = false;
        
        function addLog(message, type = 'info') {
            const log = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            const entry = document.createElement('div');
            entry.className = 'log-entry';
            entry.innerHTML = `<span class="timestamp">[${timestamp}]</span> ${message}`;
            log.appendChild(entry);
            log.scrollTop = log.scrollHeight;
            console.log(`[${timestamp}] ${message}`);
        }
        
        function updateStatus(message, type) {
            const status = document.getElementById('status');
            status.textContent = message;
            status.className = `status ${type}`;
        }
        
        function startConnection() {
            if (eventSource) {
                eventSource.close();
            }
            
            const userId = document.getElementById('userId').value;
            if (!userId || userId <= 0) {
                updateStatus('请输入有效的用户ID', 'error');
                return;
            }
            
            addLog(`开始连接SSE服务，用户ID: ${userId}`);
            updateStatus('正在连接...', 'info');
            
            // 构建SSE URL
            const sseUrl = `/frontend/api/realtime_notifications.php?user_id=${userId}`;
            addLog(`SSE URL: ${sseUrl}`);
            
            try {
                eventSource = new EventSource(sseUrl);
                
                eventSource.onopen = function(event) {
                    isConnected = true;
                    addLog('SSE连接已建立');
                    updateStatus('连接成功 ✅', 'success');
                };
                
                eventSource.onmessage = function(event) {
                    addLog(`收到消息: ${event.data}`);
                    
                    try {
                        const data = JSON.parse(event.data);
                        handleMessage(data);
                    } catch (e) {
                        addLog(`JSON解析失败: ${e.message}`, 'error');
                    }
                };
                
                eventSource.onerror = function(event) {
                    isConnected = false;
                    addLog('SSE连接错误');
                    updateStatus('连接错误 ❌', 'error');
                    
                    if (eventSource.readyState === EventSource.CLOSED) {
                        addLog('SSE连接已关闭');
                    } else if (eventSource.readyState === EventSource.CONNECTING) {
                        addLog('SSE正在重连...');
                        updateStatus('重连中...', 'info');
                    }
                };
                
            } catch (error) {
                addLog(`创建SSE连接失败: ${error.message}`);
                updateStatus('连接失败 ❌', 'error');
            }
        }
        
        function stopConnection() {
            if (eventSource) {
                eventSource.close();
                eventSource = null;
                isConnected = false;
                addLog('SSE连接已断开');
                updateStatus('连接已断开', 'info');
            }
        }
        
        function clearLog() {
            document.getElementById('log').innerHTML = '';
        }
        
        function handleMessage(data) {
            switch (data.type) {
                case 'connection':
                    addLog(`✅ 连接确认: ${data.message}`);
                    break;
                    
                case 'heartbeat':
                    addLog(`💓 心跳包: ${new Date(data.timestamp * 1000).toLocaleTimeString()}`);
                    break;
                    
                case 'verification_code':
                    addLog(`🔑 收到验证码通知: ${data.title}`);
                    addLog(`📱 验证码内容: ${data.content}`);
                    if (data.data && data.data.code) {
                        addLog(`🔢 验证码: ${data.data.code}`);
                    }
                    // 这里可以显示验证码弹窗
                    showVerificationModal(data);
                    break;
                    
                case 'system_message':
                    addLog(`📢 系统消息: ${data.title} - ${data.content}`);
                    break;
                    
                case 'error':
                    addLog(`❌ 服务器错误: ${data.message}`);
                    break;
                    
                case 'timeout':
                    addLog(`⏰ 连接超时: ${data.message}`);
                    break;
                    
                default:
                    addLog(`❓ 未知消息类型: ${data.type}`);
            }
        }
        
        function showVerificationModal(data) {
            // 简单的验证码显示
            const code = data.data?.code || '未知';
            const note = data.data?.admin_note || '';
            const sentBy = data.data?.sent_by || '管理员';
            
            alert(`🔑 收到验证码！\n\n验证码: ${code}\n发送者: ${sentBy}\n备注: ${note}`);
        }
        
        // 页面加载时自动连接
        window.addEventListener('load', function() {
            addLog('页面加载完成');
            // 可以自动连接
            // startConnection();
        });
        
        // 页面卸载时断开连接
        window.addEventListener('beforeunload', function() {
            if (eventSource) {
                eventSource.close();
            }
        });
    </script>
</body>
</html>
