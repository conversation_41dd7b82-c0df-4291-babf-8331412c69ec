-- =====================================================
-- 趣玩星球管理后台 - 权限管理系统数据库结构
-- 创建日期: 2024-05-24
-- 说明: 包含权限管理和部门管理所需的所有数据库表
-- =====================================================

-- 设置字符集
SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- =====================================================
-- 1. 部门表 (departments)
-- =====================================================
CREATE TABLE IF NOT EXISTS `departments` (
    `id` INT PRIMARY KEY AUTO_INCREMENT,
    `name` VARCHAR(100) NOT NULL COMMENT '部门名称',
    `code` VARCHAR(50) UNIQUE COMMENT '部门代码',
    `parent_id` INT DEFAULT NULL COMMENT '上级部门ID',
    `manager_id` INT DEFAULT NULL COMMENT '部门负责人ID',
    `description` TEXT COMMENT '部门描述',
    `status` ENUM('active', 'inactive') DEFAULT 'active' COMMENT '部门状态',
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX `idx_parent_id` (`parent_id`),
    INDEX `idx_manager_id` (`manager_id`),
    INDEX `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='部门表';

-- =====================================================
-- 2. 角色表 (roles)
-- =====================================================
CREATE TABLE IF NOT EXISTS `roles` (
    `id` INT PRIMARY KEY AUTO_INCREMENT,
    `name` VARCHAR(50) NOT NULL COMMENT '角色名称',
    `code` VARCHAR(50) UNIQUE COMMENT '角色代码',
    `description` TEXT COMMENT '角色描述',
    `level` INT DEFAULT 0 COMMENT '角色级别',
    `is_system` BOOLEAN DEFAULT FALSE COMMENT '是否系统角色',
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX `idx_level` (`level`),
    INDEX `idx_is_system` (`is_system`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='角色表';

-- =====================================================
-- 3. 权限表 (permissions)
-- =====================================================
CREATE TABLE IF NOT EXISTS `permissions` (
    `id` INT PRIMARY KEY AUTO_INCREMENT,
    `name` VARCHAR(100) NOT NULL COMMENT '权限名称',
    `code` VARCHAR(100) UNIQUE COMMENT '权限代码',
    `module` VARCHAR(50) COMMENT '所属模块',
    `action` VARCHAR(50) COMMENT '操作类型',
    `description` TEXT COMMENT '权限描述',
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX `idx_module` (`module`),
    INDEX `idx_action` (`action`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='权限表';

-- =====================================================
-- 4. 角色权限关联表 (role_permissions)
-- =====================================================
CREATE TABLE IF NOT EXISTS `role_permissions` (
    `id` INT PRIMARY KEY AUTO_INCREMENT,
    `role_id` INT NOT NULL,
    `permission_id` INT NOT NULL,
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (`role_id`) REFERENCES `roles`(`id`) ON DELETE CASCADE,
    FOREIGN KEY (`permission_id`) REFERENCES `permissions`(`id`) ON DELETE CASCADE,
    UNIQUE KEY `unique_role_permission` (`role_id`, `permission_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='角色权限关联表';

-- =====================================================
-- 5. 用户角色关联表 (user_roles)
-- =====================================================
CREATE TABLE IF NOT EXISTS `user_roles` (
    `id` INT PRIMARY KEY AUTO_INCREMENT,
    `user_id` INT NOT NULL,
    `role_id` INT NOT NULL,
    `department_id` INT DEFAULT NULL COMMENT '在该部门的角色',
    `assigned_by` INT COMMENT '分配人',
    `assigned_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    `expires_at` TIMESTAMP NULL COMMENT '过期时间',
    FOREIGN KEY (`role_id`) REFERENCES `roles`(`id`) ON DELETE CASCADE,
    FOREIGN KEY (`department_id`) REFERENCES `departments`(`id`),
    INDEX `idx_user_id` (`user_id`),
    INDEX `idx_role_id` (`role_id`),
    INDEX `idx_department_id` (`department_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户角色关联表';

-- =====================================================
-- 6. 权限申请表 (permission_requests)
-- =====================================================
CREATE TABLE IF NOT EXISTS `permission_requests` (
    `id` INT PRIMARY KEY AUTO_INCREMENT,
    `user_id` INT NOT NULL COMMENT '申请人',
    `permission_id` INT NOT NULL COMMENT '申请的权限',
    `reason` TEXT COMMENT '申请理由',
    `status` ENUM('pending', 'approved', 'rejected') DEFAULT 'pending' COMMENT '申请状态',
    `reviewer_id` INT DEFAULT NULL COMMENT '审核人',
    `review_comment` TEXT COMMENT '审核意见',
    `reviewed_at` TIMESTAMP NULL COMMENT '审核时间',
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (`permission_id`) REFERENCES `permissions`(`id`),
    INDEX `idx_user_id` (`user_id`),
    INDEX `idx_status` (`status`),
    INDEX `idx_reviewer_id` (`reviewer_id`),
    INDEX `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='权限申请表';

-- =====================================================
-- 7. 用户权限表 (user_permissions) - 直接权限分配
-- =====================================================
CREATE TABLE IF NOT EXISTS `user_permissions` (
    `id` INT PRIMARY KEY AUTO_INCREMENT,
    `user_id` INT NOT NULL COMMENT '用户ID',
    `permission_id` INT NOT NULL COMMENT '权限ID',
    `granted_by` INT COMMENT '授权人',
    `granted_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    `expires_at` TIMESTAMP NULL COMMENT '过期时间',
    FOREIGN KEY (`permission_id`) REFERENCES `permissions`(`id`) ON DELETE CASCADE,
    UNIQUE KEY `unique_user_permission` (`user_id`, `permission_id`),
    INDEX `idx_user_id` (`user_id`),
    INDEX `idx_permission_id` (`permission_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户权限表';

-- =====================================================
-- 8. 检查并扩展admin_users表
-- =====================================================
-- 检查admin_users表是否存在department_id列，如果不存在则添加
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
     WHERE TABLE_SCHEMA = DATABASE() 
     AND TABLE_NAME = 'admin_users' 
     AND COLUMN_NAME = 'department_id') > 0,
    'SELECT "department_id column already exists"',
    'ALTER TABLE admin_users ADD COLUMN department_id INT DEFAULT NULL COMMENT "所属部门"'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 检查并添加position列
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
     WHERE TABLE_SCHEMA = DATABASE() 
     AND TABLE_NAME = 'admin_users' 
     AND COLUMN_NAME = 'position') > 0,
    'SELECT "position column already exists"',
    'ALTER TABLE admin_users ADD COLUMN position VARCHAR(100) DEFAULT NULL COMMENT "职位"'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 检查并添加level列
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
     WHERE TABLE_SCHEMA = DATABASE() 
     AND TABLE_NAME = 'admin_users' 
     AND COLUMN_NAME = 'level') > 0,
    'SELECT "level column already exists"',
    'ALTER TABLE admin_users ADD COLUMN level INT DEFAULT 1 COMMENT "职级"'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 检查并添加direct_manager_id列
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
     WHERE TABLE_SCHEMA = DATABASE() 
     AND TABLE_NAME = 'admin_users' 
     AND COLUMN_NAME = 'direct_manager_id') > 0,
    'SELECT "direct_manager_id column already exists"',
    'ALTER TABLE admin_users ADD COLUMN direct_manager_id INT DEFAULT NULL COMMENT "直属上级"'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 检查并添加hire_date列
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
     WHERE TABLE_SCHEMA = DATABASE() 
     AND TABLE_NAME = 'admin_users' 
     AND COLUMN_NAME = 'hire_date') > 0,
    'SELECT "hire_date column already exists"',
    'ALTER TABLE admin_users ADD COLUMN hire_date DATE DEFAULT NULL COMMENT "入职日期"'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SET FOREIGN_KEY_CHECKS = 1;
