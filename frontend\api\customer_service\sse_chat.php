<?php
/**
 * Vue客服系统 - SSE实时推送接口
 * 用于实现前后台客服系统的实时通讯
 */

header('Content-Type: text/event-stream');
header('Cache-Control: no-cache');
header('Connection: keep-alive');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// 防止脚本超时
set_time_limit(0);
ignore_user_abort(false);

// 引入数据库配置
require_once '../../config/database.php';

// 获取参数
$session_id = $_GET['session_id'] ?? '';
$user_type = $_GET['user_type'] ?? 'user'; // user 或 customer_service
$user_id = $_GET['user_id'] ?? 0;
$last_message_id = $_GET['last_message_id'] ?? 0;

if (empty($session_id)) {
    echo "event: error\n";
    echo "data: " . json_encode(['error' => '缺少会话ID']) . "\n\n";
    exit;
}

// 发送初始连接确认
echo "event: connected\n";
echo "data: " . json_encode([
    'status' => 'connected',
    'session_id' => $session_id,
    'user_type' => $user_type,
    'timestamp' => time()
]) . "\n\n";
ob_flush();
flush();

// 更新在线状态
try {
    $stmt = $pdo->prepare("
        INSERT INTO vue_customer_service_online_status 
        (user_id, user_type, session_id, is_online, browser_info, ip_address) 
        VALUES (?, ?, ?, 1, ?, ?)
        ON DUPLICATE KEY UPDATE 
        session_id = VALUES(session_id),
        is_online = 1,
        last_activity = CURRENT_TIMESTAMP,
        browser_info = VALUES(browser_info),
        ip_address = VALUES(ip_address)
    ");
    $stmt->execute([
        $user_id,
        $user_type,
        $session_id,
        $_SERVER['HTTP_USER_AGENT'] ?? '',
        $_SERVER['REMOTE_ADDR'] ?? ''
    ]);
} catch (Exception $e) {
    error_log("更新在线状态失败: " . $e->getMessage());
}

$last_check_time = time();

// 主循环 - 检查新消息和通知
while (true) {
    // 检查连接状态
    if (connection_aborted()) {
        break;
    }

    try {
        // 1. 检查新消息
        $stmt = $pdo->prepare("
            SELECT 
                id,
                sender_type,
                sender_id,
                sender_name,
                message_type,
                content,
                file_url,
                file_name,
                created_at
            FROM customer_service_messages 
            WHERE session_id = ? 
            AND id > ? 
            ORDER BY id ASC
        ");
        $stmt->execute([$session_id, $last_message_id]);
        $new_messages = $stmt->fetchAll(PDO::FETCH_ASSOC);

        if (!empty($new_messages)) {
            foreach ($new_messages as $message) {
                echo "event: new_message\n";
                echo "data: " . json_encode([
                    'id' => $message['id'],
                    'sender_type' => $message['sender_type'],
                    'sender_id' => $message['sender_id'],
                    'sender_name' => $message['sender_name'],
                    'message_type' => $message['message_type'],
                    'content' => $message['content'],
                    'file_url' => $message['file_url'],
                    'file_name' => $message['file_name'],
                    'timestamp' => strtotime($message['created_at']),
                    'formatted_time' => date('H:i', strtotime($message['created_at']))
                ]) . "\n\n";
                
                $last_message_id = $message['id'];
            }
            ob_flush();
            flush();
        }

        // 2. 检查会话状态变化
        $stmt = $pdo->prepare("
            SELECT 
                status,
                customer_service_id,
                priority,
                updated_at
            FROM customer_service_sessions 
            WHERE session_id = ?
        ");
        $stmt->execute([$session_id]);
        $session_info = $stmt->fetch(PDO::FETCH_ASSOC);

        if ($session_info) {
            $session_updated = strtotime($session_info['updated_at']);
            if ($session_updated > $last_check_time) {
                echo "event: session_update\n";
                echo "data: " . json_encode([
                    'status' => $session_info['status'],
                    'customer_service_id' => $session_info['customer_service_id'],
                    'priority' => $session_info['priority'],
                    'timestamp' => $session_updated
                ]) . "\n\n";
                ob_flush();
                flush();
            }
        }

        // 3. 检查系统通知
        $stmt = $pdo->prepare("
            SELECT 
                id,
                notification_type,
                message,
                created_at
            FROM vue_customer_service_notifications 
            WHERE session_id = ? 
            AND is_read = 0
            AND created_at > FROM_UNIXTIME(?)
            ORDER BY id ASC
        ");
        $stmt->execute([$session_id, $last_check_time]);
        $notifications = $stmt->fetchAll(PDO::FETCH_ASSOC);

        if (!empty($notifications)) {
            foreach ($notifications as $notification) {
                echo "event: notification\n";
                echo "data: " . json_encode([
                    'id' => $notification['id'],
                    'type' => $notification['notification_type'],
                    'message' => $notification['message'],
                    'timestamp' => strtotime($notification['created_at'])
                ]) . "\n\n";
                
                // 标记通知为已读
                $stmt = $pdo->prepare("UPDATE vue_customer_service_notifications SET is_read = 1 WHERE id = ?");
                $stmt->execute([$notification['id']]);
            }
            ob_flush();
            flush();
        }

        // 4. 发送心跳包（每30秒）
        if (time() - $last_check_time >= 30) {
            echo "event: heartbeat\n";
            echo "data: " . json_encode([
                'timestamp' => time(),
                'session_id' => $session_id
            ]) . "\n\n";
            ob_flush();
            flush();
            
            // 更新最后活动时间
            $stmt = $pdo->prepare("
                UPDATE vue_customer_service_online_status 
                SET last_activity = CURRENT_TIMESTAMP 
                WHERE user_id = ? AND user_type = ?
            ");
            $stmt->execute([$user_id, $user_type]);
        }

        $last_check_time = time();

    } catch (Exception $e) {
        echo "event: error\n";
        echo "data: " . json_encode([
            'error' => '服务器错误',
            'message' => $e->getMessage()
        ]) . "\n\n";
        ob_flush();
        flush();
        break;
    }

    // 休眠1秒
    sleep(1);
}

// 连接断开时更新在线状态
try {
    $stmt = $pdo->prepare("
        UPDATE vue_customer_service_online_status 
        SET is_online = 0, last_activity = CURRENT_TIMESTAMP 
        WHERE user_id = ? AND user_type = ?
    ");
    $stmt->execute([$user_id, $user_type]);
} catch (Exception $e) {
    error_log("更新离线状态失败: " . $e->getMessage());
}
?>
