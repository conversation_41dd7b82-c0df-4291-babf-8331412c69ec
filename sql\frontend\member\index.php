<?php
session_start();

// 检查用户是否登录
if (!isset($_SESSION['user_id'])) {
    header('Location: ../login/index.php');
    exit();
}

// 数据库连接
require_once '../db_config.php';

$user_id = $_SESSION['user_id'];

// 获取数据库连接
try {
    $pdo = getDbConnection();
} catch (Exception $e) {
    die('数据库连接失败');
}

// 获取用户信息
$stmt = $pdo->prepare("SELECT * FROM users WHERE id = ?");
$stmt->execute([$user_id]);
$user = $stmt->fetch();

if (!$user) {
    header('Location: ../login/index.php');
    exit();
}

// 获取会员信息
$stmt = $pdo->prepare("SELECT * FROM members WHERE user_id = ? AND status = 'active' AND (expire_date IS NULL OR expire_date > NOW())");
$stmt->execute([$user_id]);
$member_info = $stmt->fetch();

$is_member = !empty($member_info);
$member_level = $is_member ? $member_info['level'] : 0;
$member_expire_date = $is_member ? $member_info['expire_date'] : null;
$member_points = $is_member ? $member_info['points'] : 0;

$user_username = $user['username'];
$user_avatar = $user['avatar'] ?: 'https://s1.imagehub.cc/images/2025/05/02/default-avatar.png';
$is_verified = $user['is_verified'] ?? 0;
?>

<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>会员中心 - 趣玩星球</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="css/member_style.css">
    <?php
    include '../includes/bottom_nav.php';
    echo renderBottomNavCSS();
    ?>
</head>
<body>
    <!-- 顶部导航 -->
    <div class="member-header">
        <div class="header-content">
            <a href="../profile/index.php" class="back-btn">
                <i class="fas fa-arrow-left"></i>
            </a>
            <h1 class="header-title">会员中心</h1>
            <div class="header-actions">
                <a href="help.php" class="help-btn">
                    <i class="fas fa-question-circle"></i>
                </a>
            </div>
        </div>
    </div>

    <!-- 会员状态卡片 -->
    <div class="member-status-card">
        <div class="status-bg"></div>
        <div class="status-content">
            <div class="user-info">
                <div class="user-avatar">
                    <img src="<?php echo htmlspecialchars($user_avatar); ?>" alt="用户头像">
                </div>
                <div class="user-details">
                    <div class="username"><?php echo htmlspecialchars($user_username); ?></div>
                    <?php if ($is_member): ?>
                        <div class="member-badge active">
                            <i class="fas fa-crown"></i>
                            <span>Lv.<?php echo $member_level; ?> 会员</span>
                        </div>
                    <?php else: ?>
                        <div class="member-badge inactive">
                            <i class="fas fa-crown"></i>
                            <span>未开通会员</span>
                        </div>
                    <?php endif; ?>
                </div>
            </div>

            <?php if ($is_member): ?>
                <div class="member-info">
                    <div class="expire-date">
                        <span class="label">到期时间</span>
                        <span class="value"><?php echo $member_expire_date; ?></span>
                    </div>
                    <div class="member-points">
                        <span class="label">会员积分</span>
                        <span class="value"><?php echo number_format($member_points); ?></span>
                    </div>
                </div>
            <?php else: ?>
                <div class="upgrade-section">
                    <button class="upgrade-btn" onclick="showUpgradeModal()">
                        <i class="fas fa-crown"></i>
                        <span>立即开通</span>
                    </button>
                </div>
            <?php endif; ?>
        </div>
    </div>

    <!-- 会员权益 -->
    <div class="benefits-section">
        <div class="section-header">
            <h2 class="section-title">会员权益</h2>
            <span class="section-subtitle">专属特权等你享受</span>
        </div>

        <div class="benefits-grid">
            <div class="benefit-item">
                <div class="benefit-icon">
                    <i class="fas fa-shipping-fast"></i>
                </div>
                <div class="benefit-content">
                    <div class="benefit-title">免费配送</div>
                    <div class="benefit-desc">全场包邮无门槛</div>
                </div>
                <div class="benefit-status <?php echo $is_member ? 'active' : 'inactive'; ?>">
                    <i class="fas fa-<?php echo $is_member ? 'check' : 'lock'; ?>"></i>
                </div>
            </div>

            <div class="benefit-item">
                <div class="benefit-icon">
                    <i class="fas fa-percentage"></i>
                </div>
                <div class="benefit-content">
                    <div class="benefit-title">专属折扣</div>
                    <div class="benefit-desc">享受9折优惠价格</div>
                </div>
                <div class="benefit-status <?php echo $is_member ? 'active' : 'inactive'; ?>">
                    <i class="fas fa-<?php echo $is_member ? 'check' : 'lock'; ?>"></i>
                </div>
            </div>

            <div class="benefit-item">
                <div class="benefit-icon">
                    <i class="fas fa-gift"></i>
                </div>
                <div class="benefit-content">
                    <div class="benefit-title">生日礼包</div>
                    <div class="benefit-desc">专属生日惊喜</div>
                </div>
                <div class="benefit-status <?php echo $is_member ? 'active' : 'inactive'; ?>">
                    <i class="fas fa-<?php echo $is_member ? 'check' : 'lock'; ?>"></i>
                </div>
            </div>

            <div class="benefit-item">
                <div class="benefit-icon">
                    <i class="fas fa-star"></i>
                </div>
                <div class="benefit-content">
                    <div class="benefit-title">积分加倍</div>
                    <div class="benefit-desc">购物积分翻倍</div>
                </div>
                <div class="benefit-status <?php echo $is_member ? 'active' : 'inactive'; ?>">
                    <i class="fas fa-<?php echo $is_member ? 'check' : 'lock'; ?>"></i>
                </div>
            </div>

            <div class="benefit-item">
                <div class="benefit-icon">
                    <i class="fas fa-headset"></i>
                </div>
                <div class="benefit-content">
                    <div class="benefit-title">专属客服</div>
                    <div class="benefit-desc">VIP客服通道</div>
                </div>
                <div class="benefit-status <?php echo $is_member ? 'active' : 'inactive'; ?>">
                    <i class="fas fa-<?php echo $is_member ? 'check' : 'lock'; ?>"></i>
                </div>
            </div>

            <div class="benefit-item">
                <div class="benefit-icon">
                    <i class="fas fa-calendar-alt"></i>
                </div>
                <div class="benefit-content">
                    <div class="benefit-title">优先预约</div>
                    <div class="benefit-desc">活动优先报名</div>
                </div>
                <div class="benefit-status <?php echo $is_member ? 'active' : 'inactive'; ?>">
                    <i class="fas fa-<?php echo $is_member ? 'check' : 'lock'; ?>"></i>
                </div>
            </div>
        </div>
    </div>

    <!-- 会员套餐 -->
    <div class="packages-section">
        <div class="section-header">
            <h2 class="section-title">会员套餐</h2>
            <span class="section-subtitle">选择适合你的套餐</span>
        </div>

        <div class="packages-grid">
            <div class="package-item">
                <div class="package-header">
                    <div class="package-name">月度会员</div>
                    <div class="package-price">
                        <span class="currency">¥</span>
                        <span class="amount">19</span>
                        <span class="period">/月</span>
                    </div>
                </div>
                <div class="package-features">
                    <div class="feature">✓ 所有基础权益</div>
                    <div class="feature">✓ 专属客服</div>
                    <div class="feature">✓ 9折优惠</div>
                </div>
                <button class="package-btn" onclick="selectPackage('monthly')">选择套餐</button>
            </div>

            <div class="package-item recommended">
                <div class="recommend-badge">推荐</div>
                <div class="package-header">
                    <div class="package-name">年度会员</div>
                    <div class="package-price">
                        <span class="currency">¥</span>
                        <span class="amount">199</span>
                        <span class="period">/年</span>
                    </div>
                    <div class="package-save">省60元</div>
                </div>
                <div class="package-features">
                    <div class="feature">✓ 所有基础权益</div>
                    <div class="feature">✓ 专属客服</div>
                    <div class="feature">✓ 8.5折优惠</div>
                    <div class="feature">✓ 生日礼包</div>
                </div>
                <button class="package-btn primary" onclick="selectPackage('yearly')">选择套餐</button>
            </div>

            <div class="package-item">
                <div class="package-header">
                    <div class="package-name">终身会员</div>
                    <div class="package-price">
                        <span class="currency">¥</span>
                        <span class="amount">999</span>
                        <span class="period">永久</span>
                    </div>
                </div>
                <div class="package-features">
                    <div class="feature">✓ 所有权益</div>
                    <div class="feature">✓ 8折优惠</div>
                    <div class="feature">✓ 专属标识</div>
                    <div class="feature">✓ 优先体验</div>
                </div>
                <button class="package-btn" onclick="selectPackage('lifetime')">选择套餐</button>
            </div>
        </div>
    </div>

    <!-- 会员记录 -->
    <?php if ($is_member): ?>
    <div class="records-section">
        <div class="section-header">
            <h2 class="section-title">会员记录</h2>
            <a href="records.php" class="view-all">查看全部</a>
        </div>

        <div class="records-list">
            <!-- 这里显示会员使用记录 -->
            <div class="record-item">
                <div class="record-icon">
                    <i class="fas fa-gift"></i>
                </div>
                <div class="record-content">
                    <div class="record-title">获得生日礼包</div>
                    <div class="record-time">2024-01-15 14:30</div>
                </div>
                <div class="record-value">+100积分</div>
            </div>
        </div>
    </div>
    <?php endif; ?>

    <!-- 开通会员弹窗 -->
    <div class="upgrade-modal" id="upgradeModal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>开通会员</h3>
                <button class="close-btn" onclick="hideUpgradeModal()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <p>选择会员套餐，享受专属权益</p>
                <!-- 这里可以放置支付选项 -->
            </div>
        </div>
    </div>

    <!-- 底部导航 -->
    <?php echo renderBottomNav('profile'); ?>

    <script src="js/member.js"></script>
</body>
</html>
