<?php
// 现代化管理后台布局组件
function renderAdminLayout($title, $content, $current_page = '') {
    $admin_name = $_SESSION['admin_name'] ?? '管理员';
    $admin_avatar = $_SESSION['admin_avatar'] ?? 'https://via.placeholder.com/40';

    return <<<HTML
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>$title - 趣玩星球管理后台</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
            overflow-x: hidden;
        }

        /* 主容器 */
        .admin-container {
            display: flex;
            min-height: 100vh;
            position: relative;
        }

        /* 现代化侧边栏 */
        .modern-sidebar {
            width: 280px;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 0 24px 24px 0;
            padding: 24px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .modern-sidebar::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(45deg, #40E0D0, #AFFBF2);
            opacity: 0.05;
            z-index: 0;
        }

        .sidebar-header {
            position: relative;
            z-index: 1;
            text-align: center;
            margin-bottom: 32px;
            padding-bottom: 24px;
            border-bottom: 2px solid rgba(64, 224, 208, 0.2);
        }

        .logo-container {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 12px;
        }

        .logo-icon {
            width: 64px;
            height: 64px;
            background: linear-gradient(135deg, #40E0D0, #AFFBF2);
            border-radius: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 28px;
            color: white;
            box-shadow: 0 8px 24px rgba(64, 224, 208, 0.3);
            animation: logoFloat 3s ease-in-out infinite;
        }

        @keyframes logoFloat {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-5px); }
        }

        .logo-text h2 {
            font-size: 24px;
            font-weight: 700;
            color: #333;
            margin: 0;
        }

        .logo-text p {
            font-size: 14px;
            color: #666;
            margin: 4px 0 0 0;
        }

        /* 导航网格 */
        .modern-nav {
            position: relative;
            z-index: 1;
        }

        .nav-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 16px;
        }

        .nav-card {
            background: white;
            border-radius: 16px;
            padding: 20px;
            text-decoration: none;
            color: #333;
            transition: all 0.3s ease;
            border: 2px solid transparent;
            position: relative;
            overflow: hidden;
            text-align: center;
            min-height: 120px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            gap: 8px;
        }

        .nav-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, #40E0D0, #AFFBF2);
            opacity: 0;
            transition: opacity 0.3s ease;
            z-index: 0;
        }

        .nav-card:hover::before {
            opacity: 0.1;
        }

        .nav-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 12px 32px rgba(0, 0, 0, 0.15);
            border-color: #40E0D0;
        }

        .nav-card.active {
            background: linear-gradient(135deg, #40E0D0, #AFFBF2);
            color: white;
            box-shadow: 0 8px 24px rgba(64, 224, 208, 0.4);
        }

        .nav-card.active .nav-icon {
            color: white;
        }

        .nav-card.active .nav-description {
            color: rgba(255, 255, 255, 0.8);
        }

        .nav-icon {
            position: relative;
            z-index: 1;
            font-size: 24px;
            color: #40E0D0;
            margin-bottom: 4px;
            transition: all 0.3s ease;
        }

        .nav-label {
            position: relative;
            z-index: 1;
            font-size: 14px;
            font-weight: 600;
            margin-bottom: 2px;
        }

        .nav-description {
            position: relative;
            z-index: 1;
            font-size: 12px;
            color: #666;
            opacity: 0.8;
        }

        /* 主内容区 */
        .main-content {
            flex: 1;
            padding: 24px;
            position: relative;
        }

        /* 顶部导航 */
        .top-nav {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 20px;
            padding: 16px 24px;
            margin-bottom: 24px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
        }

        .page-title {
            font-size: 24px;
            font-weight: 700;
            color: #333;
            margin: 0;
        }

        .user-menu {
            position: relative;
        }

        .user-info {
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 8px 16px;
            background: rgba(64, 224, 208, 0.1);
            border-radius: 12px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .user-info:hover {
            background: rgba(64, 224, 208, 0.2);
        }

        .user-avatar {
            width: 36px;
            height: 36px;
            border-radius: 10px;
            object-fit: cover;
        }

        .user-details {
            display: flex;
            flex-direction: column;
            align-items: flex-start;
        }

        .user-name {
            font-size: 14px;
            font-weight: 600;
            color: #333;
        }

        .user-role {
            font-size: 12px;
            color: #666;
        }

        /* 内容区域 */
        .content-area {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 20px;
            padding: 32px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            min-height: calc(100vh - 200px);
            animation: fadeIn 0.5s ease;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        /* 用户下拉菜单 */
        .user-dropdown {
            position: absolute;
            top: 100%;
            right: 0;
            background: white;
            border-radius: 12px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15);
            padding: 8px;
            min-width: 180px;
            opacity: 0;
            visibility: hidden;
            transform: translateY(-10px);
            transition: all 0.3s ease;
            z-index: 1000;
        }

        .user-dropdown.show {
            opacity: 1;
            visibility: visible;
            transform: translateY(0);
        }

        .user-dropdown-item {
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 12px 16px;
            border-radius: 8px;
            text-decoration: none;
            color: #333;
            font-size: 14px;
            transition: all 0.3s ease;
            border: none;
            background: none;
            width: 100%;
            text-align: left;
            cursor: pointer;
        }

        .user-dropdown-item:hover {
            background: rgba(64, 224, 208, 0.1);
            color: #40E0D0;
        }

        .user-dropdown-divider {
            height: 1px;
            background: #eee;
            margin: 8px 0;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .modern-sidebar {
                position: fixed;
                left: -280px;
                top: 0;
                height: 100vh;
                z-index: 1000;
                border-radius: 0 24px 24px 0;
            }

            .modern-sidebar.mobile-open {
                left: 0;
            }

            .main-content {
                padding: 16px;
            }

            .nav-grid {
                grid-template-columns: 1fr;
            }

            .content-area {
                padding: 20px;
            }
        }

        /* 加载动画 */
        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid rgba(64, 224, 208, 0.3);
            border-radius: 50%;
            border-top-color: #40E0D0;
            animation: spin 1s ease-in-out infinite;
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div class="admin-container">
        <!-- 现代化侧边栏 -->
        <div class="modern-sidebar" id="sidebar">
            <div class="sidebar-header">
                <div class="logo-container">
                    <div class="logo-icon">
                        <i class="fas fa-star"></i>
                    </div>
                    <div class="logo-text">
                        <h2>趣玩星球</h2>
                        <p>管理后台</p>
                    </div>
                </div>
            </div>

            <nav class="modern-nav">
                <div class="nav-grid">
                    <a href="dashboard.php" class="nav-card ' . ($current_page === 'dashboard' ? 'active' : '') . '">
                        <div class="nav-icon">
                            <i class="fas fa-chart-line"></i>
                        </div>
                        <div class="nav-label">仪表盘</div>
                        <div class="nav-description">数据概览</div>
                    </a>

                    <a href="user_management.php" class="nav-card ' . ($current_page === 'user_management' ? 'active' : '') . '">
                        <div class="nav-icon">
                            <i class="fas fa-users"></i>
                        </div>
                        <div class="nav-label">用户管理</div>
                        <div class="nav-description">用户信息</div>
                    </a>

                    <a href="verification_list.php" class="nav-card ' . (in_array($current_page, ['verification_list', 'verification_detail', 'verification_approve', 'verification_reject']) ? 'active' : '') . '">
                        <div class="nav-icon">
                            <i class="fas fa-user-check"></i>
                        </div>
                        <div class="nav-label">实名认证</div>
                        <div class="nav-description">身份审核</div>
                    </a>

                    <a href="knowledge_base.php" class="nav-card ' . (in_array($current_page, ['knowledge_base', 'knowledge_base_view', 'article_view']) ? 'active' : '') . '">
                        <div class="nav-icon">
                            <i class="fas fa-book-open"></i>
                        </div>
                        <div class="nav-label">知识库</div>
                        <div class="nav-description">智慧分享</div>
                    </a>

                    <a href="settings.php" class="nav-card ' . ($current_page === 'settings' ? 'active' : '') . '">
                        <div class="nav-icon">
                            <i class="fas fa-cog"></i>
                        </div>
                        <div class="nav-label">系统设置</div>
                        <div class="nav-description">配置管理</div>
                    </a>

                    <a href="profile.php" class="nav-card ' . ($current_page === 'profile' ? 'active' : '') . '">
                        <div class="nav-icon">
                            <i class="fas fa-user-circle"></i>
                        </div>
                        <div class="nav-label">个人资料</div>
                        <div class="nav-description">账户设置</div>
                    </a>
                </div>
            </nav>
        </div>

        <!-- 主内容区 -->
        <div class="main-content">
            <!-- 顶部导航 -->
            <div class="top-nav">
                <h1 class="page-title">$title</h1>

                <div class="user-menu">
                    <div class="user-info" onclick="toggleUserMenu()">
                        <img src="$admin_avatar" alt="头像" class="user-avatar">
                        <div class="user-details">
                            <span class="user-name">$admin_name</span>
                            <span class="user-role">管理员</span>
                        </div>
                        <i class="fas fa-chevron-down" style="margin-left: 8px; font-size: 12px; color: #666;"></i>
                    </div>
                    <div class="user-dropdown" id="userDropdown">
                        <a href="profile.php" class="user-dropdown-item">
                            <i class="fas fa-user"></i>
                            个人资料
                        </a>
                        <button onclick="openEditProfileModal()" class="user-dropdown-item">
                            <i class="fas fa-edit"></i>
                            修改资料
                        </button>
                        <div class="user-dropdown-divider"></div>
                        <a href="logout.php" class="user-dropdown-item">
                            <i class="fas fa-sign-out-alt"></i>
                            退出登录
                        </a>
                    </div>
                </div>
            </div>

            <!-- 内容区域 -->
            <div class="content-area">
                $content
            </div>
        </div>
    </div>

    <script>
        // 用户菜单切换
        function toggleUserMenu() {
            const dropdown = document.getElementById('userDropdown');
            dropdown.classList.toggle('show');
        }

        // 点击外部关闭用户菜单
        document.addEventListener('click', function(e) {
            const userMenu = e.target.closest('.user-menu');
            if (!userMenu) {
                const dropdown = document.getElementById('userDropdown');
                if (dropdown) {
                    dropdown.classList.remove('show');
                }
            }
        });

        // 打开编辑资料弹窗
        function openEditProfileModal() {
            document.getElementById('userDropdown').classList.remove('show');
            alert('编辑资料功能开发中...');
        }

        // 移动端侧边栏切换
        function toggleMobileSidebar() {
            const sidebar = document.getElementById('sidebar');
            sidebar.classList.toggle('mobile-open');
        }

        // 响应式处理
        window.addEventListener('resize', function() {
            const sidebar = document.getElementById('sidebar');
            if (window.innerWidth > 768) {
                sidebar.classList.remove('mobile-open');
            }
        });
    </script>
</body>
</html>
HTML;
}

// 获取激活状态的类名
function getActiveClass($page, $current_page) {
    return $page === $current_page ? 'class="nav-link active tooltip"' : '';
}
?>
