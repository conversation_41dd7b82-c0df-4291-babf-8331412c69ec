<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="theme-color" content="#0a0a2e">
    <title>完整功能测试</title>
    <link rel="stylesheet" href="css/style.css">
    <style>
        body {
            padding: 20px;
            background: linear-gradient(135deg, #0a0a2e 0%, #16213e 25%, #1a1a3a 50%, #0f3460 75%, #0a0a2e 100%);
            min-height: 100vh;
        }
        .test-container {
            max-width: 500px;
            margin: 20px auto;
            background: white;
            padding: 30px;
            border-radius: 16px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
        }
        .test-title {
            text-align: center;
            color: #333;
            margin-bottom: 30px;
        }
        .test-section {
            margin-bottom: 25px;
        }
        .test-btn {
            background: #40E0D0;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            width: 100%;
            margin: 10px 0;
            font-size: 16px;
        }
        .test-btn:hover {
            background: #20B2AA;
        }
        .status {
            margin-top: 10px;
            padding: 10px;
            border-radius: 5px;
            font-size: 14px;
        }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        .info { background: #d1ecf1; color: #0c5460; }
    </style>
</head>
<body>
    <!-- Toast提示 -->
    <div class="toast" id="toast"></div>

    <div class="test-container">
        <h1 class="test-title">🧪 完整功能测试</h1>

        <!-- 头像上传测试 -->
        <div class="test-section">
            <h3>📸 头像上传测试</h3>
            <div class="avatar-upload">
                <div class="avatar-preview">
                    <div id="avatar-preview" style="background-image: url('img/default-avatar.jpg');"></div>
                    <div class="avatar-edit">
                        <input type="file" id="avatar" name="avatar" accept=".png, .jpg, .jpeg">
                        <label for="avatar" title="选择头像">
                            <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M23 19C23 20.1046 22.1046 21 21 21H3C1.89543 21 1 20.1046 1 19V8C1 6.89543 1.89543 6 3 6H7L9 4H15L17 6H21C22.1046 6 23 6.89543 23 8V19Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                <circle cx="12" cy="13" r="3" stroke="currentColor" stroke-width="2"/>
                            </svg>
                        </label>
                    </div>
                </div>
                <div class="avatar-text">点击相机图标选择头像</div>
            </div>
            <button class="test-btn" onclick="testAvatarFunction()">测试头像点击响应</button>
            <div id="avatar-result" class="status info">等待测试...</div>
        </div>

        <!-- 日期选择测试 -->
        <div class="test-section">
            <h3>📅 日期选择测试</h3>
            <div class="form-group">
                <label for="birth_date">出生日期</label>
                <div class="date-input-container">
                    <input type="text" id="birth_date" name="birth_date" placeholder="请选择出生日期" readonly>
                    <button type="button" class="date-select-btn" onclick="openDatePicker()">
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                            <rect x="3" y="4" width="18" height="18" rx="2" ry="2" stroke="currentColor" stroke-width="2"/>
                            <line x1="16" y1="2" x2="16" y2="6" stroke="currentColor" stroke-width="2"/>
                            <line x1="8" y1="2" x2="8" y2="6" stroke="currentColor" stroke-width="2"/>
                            <line x1="3" y1="10" x2="21" y2="10" stroke="currentColor" stroke-width="2"/>
                        </svg>
                    </button>
                </div>
            </div>
            <button class="test-btn" onclick="testDateFunction()">测试日期选择器</button>
            <div id="date-result" class="status info">等待测试...</div>
        </div>

        <!-- 地区选择测试 -->
        <div class="test-section">
            <h3>🌍 地区选择测试</h3>
            <div class="form-group">
                <label for="region">所在地区</label>
                <div class="region-input-container">
                    <input type="text" id="region" name="region" placeholder="点击选择城市" readonly>
                    <button type="button" class="region-select-btn" onclick="openCitySelector()">
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                            <path d="M21 10C21 17 12 23 12 23S3 17 3 10C3 5.02944 7.02944 1 12 1C16.9706 1 21 5.02944 21 10Z" stroke="currentColor" stroke-width="2"/>
                            <circle cx="12" cy="10" r="3" stroke="currentColor" stroke-width="2"/>
                        </svg>
                    </button>
                </div>
            </div>
            <button class="test-btn" onclick="testRegionFunction()">测试地区选择</button>
            <div id="region-result" class="status info">等待测试...</div>
        </div>

        <!-- 综合测试 -->
        <div class="test-section">
            <h3>🔧 综合测试</h3>
            <button class="test-btn" onclick="runAllTests()">运行所有测试</button>
            <button class="test-btn" onclick="checkConsole()">检查控制台</button>
            <div id="overall-result" class="status info">等待测试...</div>
        </div>
    </div>

    <!-- 日期选择器弹窗 -->
    <div class="date-picker-overlay" id="date-picker-overlay">
        <div class="date-picker-modal">
            <div class="date-picker-header">
                <h3>选择出生日期</h3>
                <button class="date-picker-close" onclick="closeDatePicker()">
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                        <line x1="18" y1="6" x2="6" y2="18" stroke="currentColor" stroke-width="2"/>
                        <line x1="6" y1="6" x2="18" y2="18" stroke="currentColor" stroke-width="2"/>
                    </svg>
                </button>
            </div>
            
            <div class="date-picker-content">
                <div class="date-selectors">
                    <div class="selector-group">
                        <label>年份</label>
                        <div class="selector-wrapper">
                            <select id="year-selector" class="date-selector"></select>
                        </div>
                    </div>
                    
                    <div class="selector-group">
                        <label>月份</label>
                        <div class="selector-wrapper">
                            <select id="month-selector" class="date-selector">
                                <option value="1">1月</option>
                                <option value="2">2月</option>
                                <option value="3">3月</option>
                                <option value="4">4月</option>
                                <option value="5">5月</option>
                                <option value="6">6月</option>
                                <option value="7">7月</option>
                                <option value="8">8月</option>
                                <option value="9">9月</option>
                                <option value="10">10月</option>
                                <option value="11">11月</option>
                                <option value="12">12月</option>
                            </select>
                        </div>
                    </div>
                    
                    <div class="selector-group">
                        <label>日期</label>
                        <div class="selector-wrapper">
                            <select id="day-selector" class="date-selector"></select>
                        </div>
                    </div>
                </div>
                
                <div class="date-picker-actions">
                    <button class="btn-cancel" onclick="closeDatePicker()">取消</button>
                    <button class="btn-confirm" onclick="confirmDateSelection()">确认</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 头像审核动画 -->
    <div class="avatar-moderation-overlay" id="avatar-moderation-overlay">
        <div class="moderation-planet-system">
            <div class="moderation-orbit moderation-orbit-1">
                <div class="moderation-orbit-star"></div>
            </div>
            <div class="moderation-orbit moderation-orbit-2">
                <div class="moderation-orbit-star"></div>
            </div>
            <div class="moderation-orbit moderation-orbit-3">
                <div class="moderation-orbit-star"></div>
            </div>
            <div class="moderation-central-planet"></div>
        </div>
        <div class="moderation-text">头像自动审核中</div>
        <div class="moderation-subtext">正在检测头像内容是否符合社区规范...</div>
    </div>

    <script>
        // 设置全局变量
        window.verifiedPhone = 'test_phone';

        // 测试函数
        function testAvatarFunction() {
            const result = document.getElementById('avatar-result');
            try {
                const avatarInput = document.getElementById('avatar');
                const avatarPreview = document.getElementById('avatar-preview');
                
                if (avatarInput && avatarPreview) {
                    // 模拟点击
                    avatarPreview.click();
                    result.className = 'status success';
                    result.textContent = '✅ 头像点击功能正常，文件选择器应该已打开';
                } else {
                    result.className = 'status error';
                    result.textContent = '❌ 头像元素未找到';
                }
            } catch (error) {
                result.className = 'status error';
                result.textContent = '❌ 头像功能测试失败: ' + error.message;
            }
        }

        function testDateFunction() {
            const result = document.getElementById('date-result');
            try {
                if (typeof window.openDatePicker === 'function') {
                    window.openDatePicker();
                    result.className = 'status success';
                    result.textContent = '✅ 日期选择器已打开，请测试选择功能';
                } else {
                    result.className = 'status error';
                    result.textContent = '❌ openDatePicker函数未定义';
                }
            } catch (error) {
                result.className = 'status error';
                result.textContent = '❌ 日期选择器测试失败: ' + error.message;
            }
        }

        function testRegionFunction() {
            const result = document.getElementById('region-result');
            try {
                if (typeof window.openCitySelector === 'function') {
                    // 不实际跳转，只测试函数是否存在
                    result.className = 'status success';
                    result.textContent = '✅ 地区选择功能正常，点击按钮会跳转到城市选择页面';
                } else {
                    result.className = 'status error';
                    result.textContent = '❌ openCitySelector函数未定义';
                }
            } catch (error) {
                result.className = 'status error';
                result.textContent = '❌ 地区选择测试失败: ' + error.message;
            }
        }

        function runAllTests() {
            testAvatarFunction();
            testDateFunction();
            testRegionFunction();
            
            const result = document.getElementById('overall-result');
            result.className = 'status info';
            result.textContent = '🎯 所有测试已运行，请查看各项结果';
        }

        function checkConsole() {
            const result = document.getElementById('overall-result');
            result.className = 'status info';
            result.innerHTML = `
                📋 请打开浏览器开发者工具查看控制台：<br>
                • 按F12打开开发者工具<br>
                • 切换到Console标签<br>
                • 查看是否有JavaScript错误
            `;
        }
    </script>
    <script>
        // 设置全局变量
        window.verifiedPhone = 'test_phone';
    </script>
    <script src="js/script.js"></script>
</body>
</html>
