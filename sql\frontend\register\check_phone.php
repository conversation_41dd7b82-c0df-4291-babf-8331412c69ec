<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST');
header('Access-Control-Allow-Headers: Content-Type');

// 数据库配置
$host = 'localhost';
$dbname = 'quwanplanet';
$username = 'quwanplanet';
$password = 'nJmJm23FB4Xn6Fc3';

try {
    $pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
} catch(PDOException $e) {
    echo json_encode(['error' => '数据库连接失败']);
    exit;
}

// 获取POST数据
$input = json_decode(file_get_contents('php://input'), true);

if (!isset($input['phone'])) {
    echo json_encode(['error' => '缺少手机号参数']);
    exit;
}

$phone = trim($input['phone']);

// 验证手机号格式
if (!preg_match('/^1[3-9]\d{9}$/', $phone)) {
    echo json_encode(['error' => '手机号格式不正确']);
    exit;
}

try {
    // 检查手机号是否已存在
    $stmt = $pdo->prepare("SELECT COUNT(*) FROM users WHERE phone = ?");
    $stmt->execute([$phone]);
    $count = $stmt->fetchColumn();

    echo json_encode(['exists' => $count > 0]);
} catch(PDOException $e) {
    echo json_encode(['error' => '查询失败']);
}
?>
