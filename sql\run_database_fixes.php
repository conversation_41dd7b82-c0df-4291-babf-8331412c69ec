<?php
/**
 * 数据库修复执行脚本
 * 自动执行数据库修复文件，添加新的表和字段
 */

// 设置错误报告
error_reporting(E_ALL);
ini_set('display_errors', 1);

// 引入数据库配置
require_once 'db_config.php';

// 检查是否通过命令行或浏览器访问
$is_cli = php_sapi_name() === 'cli';

if (!$is_cli) {
    // 浏览器访问时的HTML头部
    echo "<!DOCTYPE html>
    <html lang='zh-CN'>
    <head>
        <meta charset='UTF-8'>
        <meta name='viewport' content='width=device-width, initial-scale=1.0'>
        <title>数据库修复工具</title>
        <style>
            body { font-family: monospace; background: #f5f5f5; padding: 20px; }
            .container { max-width: 800px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
            .success { color: #10B981; }
            .error { color: #EF4444; }
            .info { color: #3B82F6; }
            .warning { color: #F59E0B; }
            pre { background: #f8f9fa; padding: 10px; border-radius: 4px; overflow-x: auto; }
        </style>
    </head>
    <body>
        <div class='container'>
            <h1>🔧 数据库修复工具</h1>
            <p>正在执行数据库修复...</p>
            <pre>";
}

function logMessage($message, $type = 'info') {
    global $is_cli;
    
    $timestamp = date('Y-m-d H:i:s');
    
    if ($is_cli) {
        echo "[$timestamp] $message\n";
    } else {
        $class = $type;
        echo "<span class='$class'>[$timestamp] $message</span>\n";
    }
    
    // 实时输出
    if (ob_get_level()) {
        ob_flush();
    }
    flush();
}

try {
    logMessage("开始数据库修复...", 'info');
    
    // 连接数据库
    $pdo = getDbConnection();
    logMessage("数据库连接成功", 'success');
    
    // 创建数据库修复记录表（如果不存在）
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS database_fixes (
            id INT AUTO_INCREMENT PRIMARY KEY,
            fix_name VARCHAR(255) NOT NULL UNIQUE,
            description TEXT,
            executed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            INDEX idx_fix_name (fix_name)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ");
    logMessage("数据库修复记录表检查完成", 'success');
    
    // 获取所有修复文件
    $fixes_dir = __DIR__ . '/database_fixes/';
    $fix_files = glob($fixes_dir . '*.sql');
    
    if (empty($fix_files)) {
        logMessage("未找到修复文件", 'warning');
        exit;
    }
    
    logMessage("找到 " . count($fix_files) . " 个修复文件", 'info');
    
    // 获取已执行的修复
    $stmt = $pdo->query("SELECT fix_name FROM database_fixes");
    $executed_fixes = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    foreach ($fix_files as $fix_file) {
        $fix_name = basename($fix_file, '.sql');
        
        // 检查是否已执行
        if (in_array($fix_name, $executed_fixes)) {
            logMessage("跳过已执行的修复: $fix_name", 'warning');
            continue;
        }
        
        logMessage("执行修复: $fix_name", 'info');
        
        try {
            // 读取SQL文件
            $sql_content = file_get_contents($fix_file);
            
            if (empty($sql_content)) {
                logMessage("修复文件为空: $fix_name", 'error');
                continue;
            }
            
            // 开始事务
            $pdo->beginTransaction();
            
            // 分割SQL语句（简单的分割，基于分号）
            $sql_statements = array_filter(
                array_map('trim', explode(';', $sql_content)),
                function($stmt) {
                    return !empty($stmt) && !preg_match('/^\s*--/', $stmt);
                }
            );
            
            $executed_statements = 0;
            
            foreach ($sql_statements as $sql) {
                if (trim($sql)) {
                    try {
                        $pdo->exec($sql);
                        $executed_statements++;
                    } catch (PDOException $e) {
                        // 某些语句可能因为表已存在等原因失败，这是正常的
                        if (strpos($e->getMessage(), 'already exists') === false && 
                            strpos($e->getMessage(), 'Duplicate') === false) {
                            logMessage("SQL执行警告: " . $e->getMessage(), 'warning');
                        }
                    }
                }
            }
            
            // 提交事务
            $pdo->commit();
            
            logMessage("修复 $fix_name 执行完成，执行了 $executed_statements 条语句", 'success');
            
        } catch (Exception $e) {
            // 回滚事务
            if ($pdo->inTransaction()) {
                $pdo->rollBack();
            }
            
            logMessage("修复 $fix_name 执行失败: " . $e->getMessage(), 'error');
        }
    }
    
    logMessage("所有数据库修复执行完成！", 'success');
    
    // 显示当前数据库状态
    logMessage("\n=== 数据库状态检查 ===", 'info');
    
    // 检查关键表是否存在
    $tables_to_check = [
        'user_appeals' => '用户申诉表',
        'appeal_logs' => '申诉日志表',
        'customer_service_bot' => '客服机器人配置表',
        'customer_service_replies' => '客服回复规则表',
        'customer_service_conversations' => '客服对话记录表'
    ];
    
    foreach ($tables_to_check as $table => $description) {
        try {
            $stmt = $pdo->query("SHOW TABLES LIKE '$table'");
            if ($stmt->rowCount() > 0) {
                // 获取表的记录数
                $count_stmt = $pdo->query("SELECT COUNT(*) FROM $table");
                $count = $count_stmt->fetchColumn();
                logMessage("✓ $description ($table): $count 条记录", 'success');
            } else {
                logMessage("✗ $description ($table): 表不存在", 'error');
            }
        } catch (Exception $e) {
            logMessage("✗ $description ($table): 检查失败 - " . $e->getMessage(), 'error');
        }
    }
    
} catch (Exception $e) {
    logMessage("数据库修复失败: " . $e->getMessage(), 'error');
    
    if (isset($pdo) && $pdo->inTransaction()) {
        $pdo->rollBack();
    }
}

if (!$is_cli) {
    echo "</pre>
            <p><strong>修复完成！</strong></p>
            <p><a href='../houtai_backup/index.php'>返回后台管理</a></p>
        </div>
    </body>
    </html>";
}

logMessage("脚本执行结束", 'info');
?>
