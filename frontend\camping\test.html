<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>露营页面测试</title>
    <link rel="stylesheet" href="css/camping.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <!-- 页面头部 -->
    <header class="camping-header">
        <div class="header-background">
            <div class="header-overlay"></div>
        </div>
        <div class="header-content">
            <div class="header-top">
                <button class="back-btn" onclick="history.back()">
                    <i class="fas fa-arrow-left"></i>
                </button>
                <h1 class="page-title">露营</h1>
                <button class="share-btn">
                    <i class="fas fa-share-alt"></i>
                </button>
            </div>
            <div class="header-description">
                <p>发现精彩露营活动，与志同道合的朋友一起探索自然</p>
            </div>
        </div>
    </header>

    <!-- 主要内容区域 -->
    <main class="camping-main">
        <!-- 活动优惠券领取区域 -->
        <section class="coupon-section">
            <div class="section-header">
                <h2><i class="fas fa-ticket-alt"></i> 活动优惠券</h2>
                <span class="countdown" id="countdown">23:59:45</span>
            </div>
            <div class="coupon-grid">
                <div class="coupon-card" data-coupon-id="1">
                    <div class="coupon-left">
                        <div class="coupon-amount">¥30</div>
                        <div class="coupon-condition">参加活动减免</div>
                    </div>
                    <div class="coupon-right">
                        <div class="coupon-title">露营活动券</div>
                        <div class="coupon-desc">参加露营活动立减30元</div>
                        <button class="claim-btn">立即领取</button>
                    </div>
                </div>

                <div class="coupon-card" data-coupon-id="2">
                    <div class="coupon-left">
                        <div class="coupon-amount">¥50</div>
                        <div class="coupon-condition">组局活动减免</div>
                    </div>
                    <div class="coupon-right">
                        <div class="coupon-title">组局优惠券</div>
                        <div class="coupon-desc">发起露营活动减免费用</div>
                        <button class="claim-btn">立即领取</button>
                    </div>
                </div>
            </div>
        </section>

        <!-- 热门露营地推荐 -->
        <section class="camping-spots">
            <div class="section-header">
                <h2><i class="fas fa-map-marker-alt"></i> 热门露营地</h2>
                <a href="#" class="more-link">查看更多 <i class="fas fa-chevron-right"></i></a>
            </div>
            <div class="spots-grid">
                <div class="spot-card">
                    <div class="spot-image">
                        <img src="https://via.placeholder.com/400x200?text=山谷露营地" alt="山谷露营地">
                        <div class="spot-badge">推荐</div>
                    </div>
                    <div class="spot-info">
                        <h3>山谷露营地</h3>
                        <div class="spot-rating">
                            <i class="fas fa-star"></i>
                            <span>4.8</span>
                            <span class="review-count">(128条评价)</span>
                        </div>
                        <div class="spot-features">
                            <span class="feature-tag">篝火</span>
                            <span class="feature-tag">烧烤</span>
                            <span class="feature-tag">观星</span>
                        </div>
                        <div class="spot-price">
                            <span class="price">¥88</span>
                            <span class="unit">/人/晚</span>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- 装备推荐 -->
        <section class="equipment-section">
            <div class="section-header">
                <h2><i class="fas fa-shopping-bag"></i> 装备推荐</h2>
                <a href="#" class="more-link">查看全部 <i class="fas fa-chevron-right"></i></a>
            </div>
            <div class="equipment-categories">
                <div class="category-tab active" data-category="tent">帐篷</div>
                <div class="category-tab" data-category="sleeping">睡眠</div>
                <div class="category-tab" data-category="cooking">炊具</div>
                <div class="category-tab" data-category="lighting">照明</div>
            </div>
            <div class="equipment-grid" id="equipment-grid">
                <!-- 装备内容将通过JavaScript动态加载 -->
            </div>
        </section>
    </main>

    <!-- 加载动画 -->
    <div class="loading-overlay" id="loading-overlay">
        <div class="four-star-loader">
            <div class="star-point"></div>
            <div class="star-point"></div>
            <div class="star-point"></div>
            <div class="star-point"></div>
        </div>
        <div class="loading-text">正在加载中...</div>
    </div>

    <!-- Toast提示 -->
    <div class="toast" id="toast"></div>

    <script src="js/camping.js"></script>

    <!-- 测试按钮 -->
    <div style="position: fixed; bottom: 20px; right: 20px; z-index: 10000;">
        <button onclick="testLoading()" style="background: #6F7BF5; color: white; border: none; padding: 10px 20px; border-radius: 8px; margin: 5px; cursor: pointer;">测试加载</button>
        <button onclick="testToast()" style="background: #FF6B9D; color: white; border: none; padding: 10px 20px; border-radius: 8px; margin: 5px; cursor: pointer;">测试提示</button>
    </div>

    <script>
        function testLoading() {
            const loading = document.getElementById('loading-overlay');
            loading.classList.add('active');
            setTimeout(() => {
                loading.classList.remove('active');
            }, 3000);
        }

        function testToast() {
            if (window.CampingPage) {
                const camping = new window.CampingPage();
                camping.showToast('这是一个测试提示消息！');
            }
        }
    </script>
</body>
</html>
