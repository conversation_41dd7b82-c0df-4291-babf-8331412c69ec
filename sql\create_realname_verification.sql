-- 实名认证表
CREATE TABLE IF NOT EXISTS `realname_verification` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `user_id` int(11) NOT NULL COMMENT '用户ID',
    `real_name` varchar(50) NOT NULL COMMENT '真实姓名',
    `id_card_number` varchar(18) NOT NULL COMMENT '身份证号码',
    `id_card_front_url` varchar(500) DEFAULT NULL COMMENT '身份证正面照片URL',
    `id_card_back_url` varchar(500) DEFAULT NULL COMMENT '身份证反面照片URL',
    `face_photo_url` varchar(500) DEFAULT NULL COMMENT '手持身份证照片URL',
    `verification_status` enum('pending','approved','rejected') DEFAULT 'pending' COMMENT '认证状态：pending-待审核，approved-已通过，rejected-已拒绝',
    `verification_reason` text DEFAULT NULL COMMENT '审核备注（拒绝原因等）',
    `submitted_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '提交时间',
    `verified_at` timestamp NULL DEFAULT NULL COMMENT '审核时间',
    `verified_by` int(11) DEFAULT NULL COMMENT '审核员ID',
    `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    UNIQUE KEY `user_id` (`user_id`),
    UNIQUE KEY `id_card_number` (`id_card_number`),
    KEY `verification_status` (`verification_status`),
    KEY `submitted_at` (`submitted_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='实名认证表';

-- 发布作品表
CREATE TABLE IF NOT EXISTS `user_works` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `user_id` int(11) NOT NULL COMMENT '用户ID',
    `title` varchar(200) NOT NULL COMMENT '作品标题',
    `description` text DEFAULT NULL COMMENT '作品描述',
    `images` json DEFAULT NULL COMMENT '作品图片URLs（JSON数组）',
    `location` varchar(100) DEFAULT NULL COMMENT '发布地点',
    `latitude` decimal(10,8) DEFAULT NULL COMMENT '纬度',
    `longitude` decimal(11,8) DEFAULT NULL COMMENT '经度',
    `tags` json DEFAULT NULL COMMENT '标签（JSON数组）',
    `category` varchar(50) DEFAULT NULL COMMENT '分类',
    `status` enum('draft','published','hidden','deleted') DEFAULT 'published' COMMENT '状态',
    `view_count` int(11) DEFAULT 0 COMMENT '浏览次数',
    `like_count` int(11) DEFAULT 0 COMMENT '点赞次数',
    `comment_count` int(11) DEFAULT 0 COMMENT '评论次数',
    `share_count` int(11) DEFAULT 0 COMMENT '分享次数',
    `is_featured` tinyint(1) DEFAULT 0 COMMENT '是否精选',
    `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    KEY `user_id` (`user_id`),
    KEY `status` (`status`),
    KEY `category` (`category`),
    KEY `created_at` (`created_at`),
    KEY `location` (`location`),
    KEY `is_featured` (`is_featured`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='用户作品表';

-- 发布组局表
CREATE TABLE IF NOT EXISTS `user_activities` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `user_id` int(11) NOT NULL COMMENT '发起人ID',
    `title` varchar(200) NOT NULL COMMENT '活动标题',
    `description` text DEFAULT NULL COMMENT '活动描述',
    `activity_type` varchar(50) NOT NULL COMMENT '活动类型（娱乐、运动、旅游等）',
    `location` varchar(200) NOT NULL COMMENT '活动地点',
    `latitude` decimal(10,8) DEFAULT NULL COMMENT '纬度',
    `longitude` decimal(11,8) DEFAULT NULL COMMENT '经度',
    `start_time` datetime NOT NULL COMMENT '开始时间',
    `end_time` datetime DEFAULT NULL COMMENT '结束时间',
    `max_participants` int(11) DEFAULT NULL COMMENT '最大参与人数',
    `current_participants` int(11) DEFAULT 1 COMMENT '当前参与人数',
    `fee` decimal(10,2) DEFAULT 0.00 COMMENT '活动费用',
    `contact_info` varchar(200) DEFAULT NULL COMMENT '联系方式',
    `requirements` text DEFAULT NULL COMMENT '参与要求',
    `images` json DEFAULT NULL COMMENT '活动图片URLs（JSON数组）',
    `tags` json DEFAULT NULL COMMENT '标签（JSON数组）',
    `status` enum('draft','published','ongoing','completed','cancelled','deleted') DEFAULT 'published' COMMENT '状态',
    `view_count` int(11) DEFAULT 0 COMMENT '浏览次数',
    `like_count` int(11) DEFAULT 0 COMMENT '点赞次数',
    `comment_count` int(11) DEFAULT 0 COMMENT '评论次数',
    `share_count` int(11) DEFAULT 0 COMMENT '分享次数',
    `is_featured` tinyint(1) DEFAULT 0 COMMENT '是否精选',
    `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    KEY `user_id` (`user_id`),
    KEY `activity_type` (`activity_type`),
    KEY `location` (`location`),
    KEY `start_time` (`start_time`),
    KEY `status` (`status`),
    KEY `created_at` (`created_at`),
    KEY `is_featured` (`is_featured`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='用户活动组局表';

-- 活动参与表
CREATE TABLE IF NOT EXISTS `activity_participants` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `activity_id` int(11) NOT NULL COMMENT '活动ID',
    `user_id` int(11) NOT NULL COMMENT '参与者ID',
    `status` enum('pending','approved','rejected','cancelled') DEFAULT 'pending' COMMENT '参与状态',
    `join_message` text DEFAULT NULL COMMENT '参与留言',
    `joined_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '申请时间',
    `approved_at` timestamp NULL DEFAULT NULL COMMENT '批准时间',
    `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    UNIQUE KEY `activity_user` (`activity_id`, `user_id`),
    KEY `activity_id` (`activity_id`),
    KEY `user_id` (`user_id`),
    KEY `status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='活动参与表';

-- 作品点赞表
CREATE TABLE IF NOT EXISTS `work_likes` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `work_id` int(11) NOT NULL COMMENT '作品ID',
    `user_id` int(11) NOT NULL COMMENT '用户ID',
    `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    UNIQUE KEY `work_user` (`work_id`, `user_id`),
    KEY `work_id` (`work_id`),
    KEY `user_id` (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='作品点赞表';

-- 活动点赞表
CREATE TABLE IF NOT EXISTS `activity_likes` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `activity_id` int(11) NOT NULL COMMENT '活动ID',
    `user_id` int(11) NOT NULL COMMENT '用户ID',
    `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    UNIQUE KEY `activity_user` (`activity_id`, `user_id`),
    KEY `activity_id` (`activity_id`),
    KEY `user_id` (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='活动点赞表';

-- 作品评论表
CREATE TABLE IF NOT EXISTS `work_comments` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `work_id` int(11) NOT NULL COMMENT '作品ID',
    `user_id` int(11) NOT NULL COMMENT '评论者ID',
    `parent_id` int(11) DEFAULT NULL COMMENT '父评论ID（回复功能）',
    `content` text NOT NULL COMMENT '评论内容',
    `status` enum('published','hidden','deleted') DEFAULT 'published' COMMENT '状态',
    `like_count` int(11) DEFAULT 0 COMMENT '点赞次数',
    `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    KEY `work_id` (`work_id`),
    KEY `user_id` (`user_id`),
    KEY `parent_id` (`parent_id`),
    KEY `status` (`status`),
    KEY `created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='作品评论表';

-- 活动评论表
CREATE TABLE IF NOT EXISTS `activity_comments` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `activity_id` int(11) NOT NULL COMMENT '活动ID',
    `user_id` int(11) NOT NULL COMMENT '评论者ID',
    `parent_id` int(11) DEFAULT NULL COMMENT '父评论ID（回复功能）',
    `content` text NOT NULL COMMENT '评论内容',
    `status` enum('published','hidden','deleted') DEFAULT 'published' COMMENT '状态',
    `like_count` int(11) DEFAULT 0 COMMENT '点赞次数',
    `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    KEY `activity_id` (`activity_id`),
    KEY `user_id` (`user_id`),
    KEY `parent_id` (`parent_id`),
    KEY `status` (`status`),
    KEY `created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='活动评论表';

-- 在users表中添加实名认证状态字段（如果不存在）
ALTER TABLE `users` ADD COLUMN IF NOT EXISTS `is_verified` tinyint(1) DEFAULT 0 COMMENT '是否已实名认证' AFTER `avatar`;
ALTER TABLE `users` ADD COLUMN IF NOT EXISTS `verification_level` enum('none','basic','advanced') DEFAULT 'none' COMMENT '认证级别' AFTER `is_verified`;
