/**
 * 趣玩星球管理后台 - 布局样式
 * 现代化左侧菜单布局
 */

:root {
    /* 主题色系 - 统一为前台主题色 #6F7BF5 */
    --primary-color: #6F7BF5;
    --primary-light: #8B92F7;
    --primary-lighter: #A7ADF9;
    --primary-dark: #5A67F3;
    --primary-darker: #4A5AF1;

    /* 渐变色系 */
    --primary-gradient: linear-gradient(135deg, #6F7BF5 0%, #8B92F7 100%);
    --primary-gradient-hover: linear-gradient(135deg, #5A67F3 0%, #6F7BF5 100%);
    --primary-gradient-light: linear-gradient(135deg, rgba(111, 123, 245, 0.1) 0%, rgba(139, 146, 247, 0.1) 100%);
    --primary-gradient-dark: linear-gradient(135deg, #4A5AF1 0%, #5A67F3 100%);

    /* 辅助色系 */
    --secondary-color: #8B92F7;
    --accent-color: #FF6B6B;
    --accent-light: #FF8E8E;

    /* 功能色彩 */
    --success-color: #10B981;
    --success-light: #34D399;
    --success-lighter: #6EE7B7;
    --warning-color: #F59E0B;
    --warning-light: #FBBF24;
    --warning-lighter: #FCD34D;
    --error-color: #EF4444;
    --error-light: #F87171;
    --error-lighter: #FCA5A5;
    --info-color: #3B82F6;
    --info-light: #60A5FA;
    --info-lighter: #93C5FD;

    /* 中性色系 */
    --white: #FFFFFF;
    --gray-50: #F9FAFB;
    --gray-100: #F3F4F6;
    --gray-200: #E5E7EB;
    --gray-300: #D1D5DB;
    --gray-400: #9CA3AF;
    --gray-500: #6B7280;
    --gray-600: #4B5563;
    --gray-700: #374151;
    --gray-800: #1F2937;
    --gray-900: #111827;

    /* 阴影系统 */
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
    --shadow-primary: 0 10px 25px -5px rgba(111, 123, 245, 0.3);
    --shadow-primary-lg: 0 20px 40px -10px rgba(111, 123, 245, 0.4);

    /* 设计系统 */
    --border-radius-sm: 6px;
    --border-radius: 12px;
    --border-radius-lg: 16px;
    --border-radius-xl: 20px;
    --border-radius-2xl: 24px;
    --border-radius-full: 9999px;

    /* 间距系统 */
    --spacing-xs: 4px;
    --spacing-sm: 8px;
    --spacing-md: 16px;
    --spacing-lg: 24px;
    --spacing-xl: 32px;
    --spacing-2xl: 48px;
    --spacing-3xl: 64px;

    /* 动画系统 */
    --transition-fast: all 0.15s ease;
    --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    --transition-slow: all 0.5s ease;
    --transition-bounce: all 0.3s cubic-bezier(0.68, -0.55, 0.265, 1.55);

    /* 布局系统 */
    --sidebar-width: 280px;
    --sidebar-width-collapsed: 80px;
    --topbar-height: 80px;

    /* 字体系统 */
    --font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
    --font-size-xs: 0.75rem;
    --font-size-sm: 0.875rem;
    --font-size-base: 1rem;
    --font-size-lg: 1.125rem;
    --font-size-xl: 1.25rem;
    --font-size-2xl: 1.5rem;
    --font-size-3xl: 1.875rem;
    --font-size-4xl: 2.25rem;

    /* Z-index 层级 */
    --z-dropdown: 1000;
    --z-sticky: 1020;
    --z-fixed: 1030;
    --z-modal-backdrop: 1040;
    --z-modal: 1050;
    --z-popover: 1060;
    --z-tooltip: 1070;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: var(--font-family);
    background: var(--gray-50);
    color: var(--gray-800);
    line-height: 1.6;
    font-size: var(--font-size-base);
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* 主布局 */
.admin-layout {
    display: flex;
    min-height: 100vh;
    background: var(--gray-50);
}

/* 左侧菜单 - 年轻化重构 */
.sidebar {
    width: var(--sidebar-width);
    background: var(--white);
    border-right: 1px solid var(--gray-200);
    display: flex;
    flex-direction: column;
    position: fixed;
    height: 100vh;
    z-index: var(--z-fixed);
    box-shadow: var(--shadow-xl);
    transition: var(--transition);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
}

/* 侧边栏头部 - 年轻化设计 */
.sidebar-header {
    padding: var(--spacing-xl) var(--spacing-lg);
    border-bottom: 1px solid var(--gray-200);
    background: var(--primary-gradient-light);
    position: relative;
    overflow: hidden;
}

.sidebar-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: var(--primary-gradient);
    opacity: 0.05;
    z-index: -1;
}

.logo {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    position: relative;
    z-index: 1;
}

.logo-icon {
    width: 56px;
    height: 56px;
    background: var(--white);
    border-radius: var(--border-radius-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: var(--shadow-primary);
    position: relative;
    overflow: hidden;
    transition: var(--transition-bounce);
    padding: var(--spacing-sm);
}

.logo-image {
    width: 100%;
    height: 100%;
    object-fit: contain;
    border-radius: var(--border-radius);
    transition: var(--transition);
}

.logo-icon::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: linear-gradient(45deg, transparent, rgba(255,255,255,0.1), transparent);
    transform: rotate(45deg);
    transition: var(--transition);
}

.logo-icon:hover {
    transform: scale(1.05);
    box-shadow: var(--shadow-primary-lg);
}

.logo-icon:hover .logo-image {
    transform: scale(1.1);
}

.logo-icon:hover::before {
    animation: shine 0.6s ease-in-out;
}

@keyframes shine {
    0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
    100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
}

.logo-text h2 {
    font-size: var(--font-size-xl);
    font-weight: 800;
    color: var(--gray-800);
    margin-bottom: 2px;
    background: var(--primary-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    letter-spacing: -0.025em;
}

.logo-text p {
    font-size: var(--font-size-sm);
    color: var(--gray-500);
    font-weight: 500;
    letter-spacing: 0.025em;
}

/* 导航菜单 - 年轻化重构 */
.sidebar-nav {
    flex: 1;
    padding: var(--spacing-lg) 0;
    overflow-y: auto;
    overflow-x: hidden;
}

.nav-list {
    list-style: none;
    padding: 0 var(--spacing-md);
}

.nav-item {
    margin-bottom: var(--spacing-xs);
    position: relative;
}

.nav-link {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    padding: var(--spacing-md) var(--spacing-lg);
    color: var(--gray-600);
    text-decoration: none;
    transition: var(--transition);
    position: relative;
    border-radius: var(--border-radius-lg);
    font-weight: 500;
    font-size: var(--font-size-sm);
    overflow: hidden;
}

.nav-link::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: var(--primary-gradient-light);
    opacity: 0;
    transition: var(--transition);
    z-index: -1;
}

.nav-link:hover {
    color: var(--primary-color);
    text-decoration: none;
    transform: translateX(4px);
    box-shadow: var(--shadow-md);
}

.nav-link:hover::before {
    opacity: 1;
}

.nav-item.active > .nav-link {
    background: var(--primary-gradient);
    color: white;
    box-shadow: var(--shadow-primary);
    transform: translateX(4px);
}

.nav-item.active > .nav-link::before {
    opacity: 0;
}

.nav-link i {
    width: 20px;
    text-align: center;
    font-size: 18px;
    transition: var(--transition);
}

.nav-item.active .nav-link i {
    transform: scale(1.1);
}

.nav-link-text {
    flex: 1;
    font-weight: 600;
    letter-spacing: 0.025em;
}

.submenu-arrow {
    margin-left: auto;
    transition: var(--transition-bounce);
    font-size: 14px;
    opacity: 0.7;
}

.nav-item.has-submenu.open .submenu-arrow {
    transform: rotate(90deg);
    opacity: 1;
}

/* 菜单项徽章 */
.nav-badge {
    background: var(--error-color);
    color: white;
    font-size: var(--font-size-xs);
    font-weight: 700;
    padding: 2px 8px;
    border-radius: var(--border-radius-full);
    min-width: 20px;
    text-align: center;
    margin-left: auto;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.7; }
}

/* 子菜单 - 年轻化重构 */
.submenu {
    list-style: none;
    max-height: 0;
    overflow: hidden;
    transition: var(--transition-slow);
    background: var(--gray-50);
    border-radius: var(--border-radius);
    margin: var(--spacing-xs) var(--spacing-md) var(--spacing-sm);
    opacity: 0;
    transform: translateY(-10px);
}

.submenu.show {
    max-height: 400px;
    opacity: 1;
    transform: translateY(0);
    padding: var(--spacing-sm) 0;
}

.submenu-item {
    margin-bottom: var(--spacing-xs);
    position: relative;
}

.submenu-link {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    padding: var(--spacing-sm) var(--spacing-lg) var(--spacing-sm) var(--spacing-3xl);
    color: var(--gray-600);
    text-decoration: none;
    transition: var(--transition);
    position: relative;
    border-radius: var(--border-radius);
    font-weight: 500;
    font-size: var(--font-size-sm);
    margin: 0 var(--spacing-sm);
}

.submenu-link::before {
    content: '';
    position: absolute;
    left: var(--spacing-lg);
    top: 50%;
    transform: translateY(-50%);
    width: 6px;
    height: 6px;
    background: var(--gray-400);
    border-radius: 50%;
    transition: var(--transition);
}

.submenu-link:hover {
    background: var(--white);
    color: var(--primary-color);
    text-decoration: none;
    transform: translateX(4px);
    box-shadow: var(--shadow-sm);
}

.submenu-link:hover::before {
    background: var(--primary-color);
    transform: translateY(-50%) scale(1.2);
}

.submenu-item.active .submenu-link {
    background: var(--primary-gradient);
    color: white;
    box-shadow: var(--shadow-primary);
    transform: translateX(4px);
}

.submenu-item.active .submenu-link::before {
    background: white;
    transform: translateY(-50%) scale(1.3);
}

.submenu-link i {
    width: 16px;
    text-align: center;
    font-size: 14px;
    transition: var(--transition);
}

.submenu-item.active .submenu-link i {
    transform: scale(1.1);
}

.submenu-link-text {
    font-weight: 600;
    letter-spacing: 0.025em;
}

/* 徽章 */
.badge {
    background: var(--error-color);
    color: white;
    font-size: 10px;
    font-weight: 600;
    padding: 2px 6px;
    border-radius: 10px;
    min-width: 18px;
    text-align: center;
    margin-left: auto;
}

/* 侧边栏底部 */
.sidebar-footer {
    padding: 20px;
    border-top: 1px solid var(--gray-200);
    margin-top: auto;
}

/* 主内容区域 */
.main-content {
    flex: 1;
    margin-left: var(--sidebar-width);
    min-height: 100vh;
    background: var(--gray-50);
}

/* 响应式设计 */
@media (max-width: 1024px) {
    :root {
        --sidebar-width: 220px;
    }
}

@media (max-width: 768px) {
    .sidebar {
        transform: translateX(-100%);
        transition: transform 0.3s ease;
    }

    .sidebar.mobile-open {
        transform: translateX(0);
    }

    .main-content {
        margin-left: 0;
    }

    /* 移动端菜单按钮 */
    .mobile-menu-btn {
        position: fixed;
        top: 20px;
        left: 20px;
        z-index: 1001;
        background: var(--primary-color);
        color: white;
        border: none;
        width: 48px;
        height: 48px;
        border-radius: var(--border-radius);
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 18px;
        cursor: pointer;
        box-shadow: var(--shadow-lg);
    }
}

/* 滚动条样式 */
.sidebar-nav::-webkit-scrollbar {
    width: 6px;
}

.sidebar-nav::-webkit-scrollbar-track {
    background: var(--gray-100);
}

.sidebar-nav::-webkit-scrollbar-thumb {
    background: var(--gray-300);
    border-radius: 3px;
}

.sidebar-nav::-webkit-scrollbar-thumb:hover {
    background: var(--gray-400);
}

/* 动画效果 */
@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateX(-20px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

.nav-item {
    animation: slideIn 0.3s ease forwards;
}

.nav-item:nth-child(1) { animation-delay: 0.1s; }
.nav-item:nth-child(2) { animation-delay: 0.2s; }
.nav-item:nth-child(3) { animation-delay: 0.3s; }
.nav-item:nth-child(4) { animation-delay: 0.4s; }
.nav-item:nth-child(5) { animation-delay: 0.5s; }
