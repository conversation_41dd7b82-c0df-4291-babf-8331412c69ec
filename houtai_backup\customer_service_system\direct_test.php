<?php
// 直接测试接受会话功能
header('Content-Type: text/html; charset=UTF-8');
ini_set('default_charset', 'UTF-8');

session_start();

// 检查登录状态
if (!isset($_SESSION['cs_logged_in']) || !$_SESSION['cs_logged_in']) {
    echo '<h1>请先登录客服系统</h1>';
    exit;
}

require_once '../db_config.php';

echo '<h1>🔧 直接测试接受会话</h1>';

// 如果有POST请求，直接处理
if ($_POST['test_accept'] ?? false) {
    $sessionId = $_POST['session_id'] ?? '';
    
    if ($sessionId) {
        echo '<h2>正在测试接受会话: ' . htmlspecialchars($sessionId) . '</h2>';
        
        try {
            $pdo = getDbConnection();
            
            // 检查会话是否存在
            $stmt = $pdo->prepare("SELECT * FROM customer_service_sessions WHERE session_id = ?");
            $stmt->execute([$sessionId]);
            $session = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if (!$session) {
                echo '<p style="color: red;">❌ 会话不存在</p>';
            } else {
                echo '<p style="color: green;">✅ 会话存在</p>';
                echo '<p>状态: ' . $session['status'] . '</p>';
                echo '<p>用户: ' . $session['user_name'] . '</p>';
                
                if ($session['status'] !== 'waiting') {
                    echo '<p style="color: orange;">⚠️ 会话状态不是等待中</p>';
                } else {
                    // 直接更新会话状态，不插入任何消息
                    echo '<h3>直接更新会话状态...</h3>';
                    
                    try {
                        $stmt = $pdo->prepare("
                            UPDATE customer_service_sessions 
                            SET customer_service_id = ?, status = 'active', updated_at = NOW() 
                            WHERE session_id = ?
                        ");
                        $stmt->execute([$_SESSION['cs_user_id'], $sessionId]);
                        
                        echo '<p style="color: green;">✅ 会话状态更新成功！</p>';
                        echo '<p>客服ID: ' . $_SESSION['cs_user_id'] . '</p>';
                        echo '<p>客服姓名: ' . $_SESSION['cs_name'] . '</p>';
                        
                        // 验证更新结果
                        $stmt = $pdo->prepare("SELECT * FROM customer_service_sessions WHERE session_id = ?");
                        $stmt->execute([$sessionId]);
                        $updatedSession = $stmt->fetch(PDO::FETCH_ASSOC);
                        
                        echo '<h3>更新后的会话信息:</h3>';
                        echo '<pre>' . print_r($updatedSession, true) . '</pre>';
                        
                    } catch (Exception $e) {
                        echo '<p style="color: red;">❌ 更新会话状态失败: ' . $e->getMessage() . '</p>';
                    }
                }
            }
            
        } catch (Exception $e) {
            echo '<p style="color: red;">❌ 数据库错误: ' . $e->getMessage() . '</p>';
        }
    }
}

// 获取等待中的会话
try {
    $pdo = getDbConnection();
    $stmt = $pdo->query("SELECT session_id, user_name, status, priority, started_at FROM customer_service_sessions WHERE status = 'waiting' ORDER BY started_at DESC LIMIT 5");
    $waitingSessions = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (!empty($waitingSessions)) {
        echo '<h2>📋 等待中的会话</h2>';
        
        foreach ($waitingSessions as $session) {
            echo '<div style="border: 1px solid #ddd; padding: 10px; margin: 10px 0; background: #f9f9f9;">';
            echo '<p><strong>会话ID:</strong> ' . htmlspecialchars($session['session_id']) . '</p>';
            echo '<p><strong>用户:</strong> ' . htmlspecialchars($session['user_name']) . '</p>';
            echo '<p><strong>状态:</strong> ' . htmlspecialchars($session['status']) . '</p>';
            echo '<p><strong>优先级:</strong> ' . htmlspecialchars($session['priority']) . '</p>';
            echo '<form method="POST" style="display: inline;">';
            echo '<input type="hidden" name="session_id" value="' . htmlspecialchars($session['session_id']) . '">';
            echo '<button type="submit" name="test_accept" value="1" style="background: #28a745; color: white; border: none; padding: 8px 16px; border-radius: 5px; cursor: pointer;">直接测试接受</button>';
            echo '</form>';
            echo '</div>';
        }
    } else {
        echo '<p>没有等待中的会话</p>';
        echo '<p><a href="create_test_data.php">创建测试数据</a></p>';
    }
    
} catch (Exception $e) {
    echo '<p style="color: red;">获取会话列表失败: ' . $e->getMessage() . '</p>';
}
?>

<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>直接测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        pre { background: #f5f5f5; padding: 10px; border-radius: 5px; overflow-x: auto; }
    </style>
</head>
<body>
    <hr>
    <h2>🔍 调试信息</h2>
    <p><strong>当前客服信息:</strong></p>
    <ul>
        <li>客服ID: <?php echo $_SESSION['cs_user_id'] ?? 'N/A'; ?></li>
        <li>客服姓名: <?php echo $_SESSION['cs_name'] ?? 'N/A'; ?></li>
        <li>工号: <?php echo $_SESSION['cs_employee_id'] ?? 'N/A'; ?></li>
        <li>角色: <?php echo $_SESSION['cs_role'] ?? 'N/A'; ?></li>
    </ul>
    
    <p>
        <a href="sessions.php">返回会话列表</a> | 
        <a href="quick_test.php">快速测试</a> | 
        <a href="create_test_data.php">创建测试数据</a>
    </p>
</body>
</html>
