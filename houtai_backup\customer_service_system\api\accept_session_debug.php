<?php
// 调试版接受会话API
error_reporting(E_ALL);
ini_set('display_errors', 1);

// 捕获所有输出
ob_start();

header('Content-Type: application/json; charset=UTF-8');
ini_set('default_charset', 'UTF-8');

session_start();

// 记录调试信息
$debug = [];
$debug['session_data'] = $_SESSION;
$debug['request_method'] = $_SERVER['REQUEST_METHOD'];
$debug['input_raw'] = file_get_contents('php://input');

// 检查客服登录状态
if (!isset($_SESSION['cs_logged_in']) || !$_SESSION['cs_logged_in']) {
    $debug['error'] = 'cs_logged_in not set or false';
    ob_end_clean();
    http_response_code(401);
    echo json_encode(['error' => '客服未登录', 'debug' => $debug]);
    exit;
}

// 只接受POST请求
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    $debug['error'] = 'not POST method';
    ob_end_clean();
    http_response_code(405);
    echo json_encode(['error' => '方法不允许', 'debug' => $debug]);
    exit;
}

// 获取POST数据
$input = json_decode(file_get_contents('php://input'), true);
$sessionId = $input['sessionId'] ?? '';
$debug['input_decoded'] = $input;
$debug['session_id'] = $sessionId;

if (empty($sessionId)) {
    $debug['error'] = 'sessionId empty';
    ob_end_clean();
    http_response_code(400);
    echo json_encode(['error' => '会话ID不能为空', 'debug' => $debug]);
    exit;
}

// 引用数据库配置文件
try {
    require_once '../../db_config.php';
    $debug['db_config_loaded'] = true;
} catch (Exception $e) {
    $debug['db_config_error'] = $e->getMessage();
    ob_end_clean();
    echo json_encode(['error' => '数据库配置加载失败', 'debug' => $debug]);
    exit;
}

try {
    $pdo = getDbConnection();
    $debug['db_connected'] = true;

    // 检查会话是否存在且为等待状态
    $stmt = $pdo->prepare("SELECT * FROM customer_service_sessions WHERE session_id = ?");
    $stmt->execute([$sessionId]);
    $session = $stmt->fetch(PDO::FETCH_ASSOC);
    $debug['session_found'] = $session ? true : false;

    if (!$session) {
        ob_end_clean();
        http_response_code(404);
        echo json_encode(['error' => '会话不存在', 'debug' => $debug]);
        exit;
    }

    $debug['session_status'] = $session['status'];

    if ($session['status'] !== 'waiting') {
        ob_end_clean();
        http_response_code(400);
        echo json_encode(['error' => '会话状态不是等待中，无法接受', 'debug' => $debug]);
        exit;
    }

    if ($session['customer_service_id'] && $session['customer_service_id'] != $_SESSION['cs_user_id']) {
        $debug['conflict_cs_id'] = $session['customer_service_id'];
        $debug['current_cs_id'] = $_SESSION['cs_user_id'];
        ob_end_clean();
        http_response_code(400);
        echo json_encode(['error' => '会话已被其他客服接受', 'debug' => $debug]);
        exit;
    }

    // 只更新会话状态，不做任何其他操作
    $stmt = $pdo->prepare("
        UPDATE customer_service_sessions
        SET customer_service_id = ?, status = 'active', updated_at = NOW()
        WHERE session_id = ?
    ");
    $stmt->execute([$_SESSION['cs_user_id'], $sessionId]);
    $debug['rows_affected'] = $stmt->rowCount();

    // 检查更新是否成功
    if ($stmt->rowCount() > 0) {
        // 清除任何意外的输出
        ob_end_clean();

        echo json_encode([
            'success' => true,
            'message' => '会话接受成功（调试版）',
            'session' => [
                'session_id' => $sessionId,
                'status' => 'active',
                'cs_name' => $_SESSION['cs_name'],
                'cs_employee_id' => $_SESSION['cs_employee_id'] ?? ''
            ],
            'debug' => $debug
        ]);
    } else {
        ob_end_clean();
        echo json_encode([
            'success' => false,
            'error' => '更新会话状态失败',
            'message' => '没有行被更新',
            'debug' => $debug
        ]);
    }

} catch (Exception $e) {
    $debug['exception'] = [
        'message' => $e->getMessage(),
        'file' => $e->getFile(),
        'line' => $e->getLine()
    ];

    // 清除任何意外的输出
    ob_end_clean();

    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => '接受会话失败',
        'message' => $e->getMessage(),
        'debug' => $debug
    ]);
}
?>
