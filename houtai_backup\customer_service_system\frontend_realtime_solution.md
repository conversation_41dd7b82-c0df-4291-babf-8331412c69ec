# 前台实时通信解决方案

## 🎯 问题分析

**现状**：客服后台可以发送消息，消息也正确插入数据库，但前台用户看不到消息。

**原因**：前台页面缺少实时通信机制来获取新消息。

## 🔧 解决方案

### 方案一：轮询机制（推荐，简单可靠）

在前台页面添加定时轮询，定期检查新消息和通知。

#### 1. 创建API接口

**文件：** `api/get_user_messages.php`

```php
<?php
// 获取用户消息API
header('Content-Type: application/json; charset=UTF-8');
session_start();

$userId = $_GET['user_id'] ?? $_SESSION['user_id'] ?? '';
$lastMessageId = $_GET['last_id'] ?? 0;

if (!$userId) {
    echo json_encode(['error' => '用户未登录']);
    exit;
}

require_once '../../db_config.php';

try {
    $pdo = getDbConnection();
    
    // 获取新消息
    $stmt = $pdo->prepare("
        SELECT m.*, s.session_id 
        FROM customer_service_messages m
        JOIN customer_service_sessions s ON m.session_id = s.session_id
        WHERE s.user_id = ? AND m.id > ?
        ORDER BY m.created_at ASC
    ");
    $stmt->execute([$userId, $lastMessageId]);
    $messages = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // 获取未读通知
    $stmt = $pdo->prepare("
        SELECT * FROM realtime_notifications 
        WHERE user_id = ? AND status = 'unread'
        ORDER BY created_at DESC
    ");
    $stmt->execute([$userId]);
    $notifications = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo json_encode([
        'success' => true,
        'messages' => $messages,
        'notifications' => $notifications,
        'timestamp' => time()
    ]);
    
} catch (Exception $e) {
    echo json_encode(['error' => $e->getMessage()]);
}
?>
```

#### 2. 前台JavaScript代码

```javascript
class CustomerServiceChat {
    constructor(userId, sessionId) {
        this.userId = userId;
        this.sessionId = sessionId;
        this.lastMessageId = 0;
        this.pollInterval = null;
        this.init();
    }
    
    init() {
        this.startPolling();
        this.bindEvents();
    }
    
    startPolling() {
        // 每3秒检查一次新消息
        this.pollInterval = setInterval(() => {
            this.checkNewMessages();
        }, 3000);
        
        // 立即检查一次
        this.checkNewMessages();
    }
    
    async checkNewMessages() {
        try {
            const response = await fetch(`api/get_user_messages.php?user_id=${this.userId}&last_id=${this.lastMessageId}`);
            const data = await response.json();
            
            if (data.success) {
                // 显示新消息
                if (data.messages.length > 0) {
                    this.displayMessages(data.messages);
                    this.lastMessageId = Math.max(...data.messages.map(m => m.id));
                }
                
                // 显示通知
                if (data.notifications.length > 0) {
                    this.showNotifications(data.notifications);
                }
            }
        } catch (error) {
            console.error('检查新消息失败:', error);
        }
    }
    
    displayMessages(messages) {
        const chatContainer = document.getElementById('chat-messages');
        
        messages.forEach(message => {
            const messageDiv = document.createElement('div');
            messageDiv.className = message.sender_type === 'customer_service' ? 'message cs-message' : 'message user-message';
            
            messageDiv.innerHTML = `
                <div class="message-content">
                    <div class="message-sender">${message.sender_name}</div>
                    <div class="message-text">${message.content}</div>
                    <div class="message-time">${message.created_at}</div>
                </div>
            `;
            
            chatContainer.appendChild(messageDiv);
            chatContainer.scrollTop = chatContainer.scrollHeight;
        });
    }
    
    showNotifications(notifications) {
        notifications.forEach(notification => {
            this.showToast(notification.title, notification.message || '您有新消息');
        });
        
        // 标记通知为已读
        this.markNotificationsRead();
    }
    
    showToast(title, message) {
        // 创建通知提示
        const toast = document.createElement('div');
        toast.className = 'notification-toast';
        toast.innerHTML = `
            <div class="toast-title">${title}</div>
            <div class="toast-message">${message}</div>
        `;
        
        document.body.appendChild(toast);
        
        // 3秒后自动消失
        setTimeout(() => {
            toast.remove();
        }, 3000);
    }
    
    async markNotificationsRead() {
        try {
            await fetch('api/mark_notifications_read.php', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ user_id: this.userId })
            });
        } catch (error) {
            console.error('标记通知已读失败:', error);
        }
    }
    
    stopPolling() {
        if (this.pollInterval) {
            clearInterval(this.pollInterval);
        }
    }
}

// 使用示例
document.addEventListener('DOMContentLoaded', function() {
    const userId = getUserId(); // 获取当前用户ID
    const sessionId = getSessionId(); // 获取当前会话ID
    
    if (userId) {
        const chat = new CustomerServiceChat(userId, sessionId);
        
        // 页面卸载时停止轮询
        window.addEventListener('beforeunload', () => {
            chat.stopPolling();
        });
    }
});
```

#### 3. CSS样式

```css
.message {
    margin: 10px 0;
    padding: 10px;
    border-radius: 10px;
    max-width: 70%;
}

.cs-message {
    background: #007cba;
    color: white;
    margin-left: auto;
    text-align: right;
}

.user-message {
    background: #f1f1f1;
    color: #333;
    margin-right: auto;
}

.message-sender {
    font-size: 12px;
    opacity: 0.8;
    margin-bottom: 5px;
}

.message-text {
    font-size: 14px;
    line-height: 1.4;
}

.message-time {
    font-size: 11px;
    opacity: 0.6;
    margin-top: 5px;
}

.notification-toast {
    position: fixed;
    top: 20px;
    right: 20px;
    background: #28a745;
    color: white;
    padding: 15px;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.3);
    z-index: 10000;
    max-width: 300px;
    animation: slideIn 0.3s ease;
}

.toast-title {
    font-weight: bold;
    margin-bottom: 5px;
}

.toast-message {
    font-size: 14px;
}

@keyframes slideIn {
    from { transform: translateX(100%); opacity: 0; }
    to { transform: translateX(0); opacity: 1; }
}
```

### 方案二：WebSocket实时通信（高级）

如果需要更实时的通信，可以使用WebSocket：

1. **服务端**：使用Ratchet或ReactPHP创建WebSocket服务器
2. **客服发送消息时**：通过WebSocket推送给对应用户
3. **前台**：监听WebSocket消息并实时显示

### 方案三：Server-Sent Events (SSE)

使用SSE实现服务器主动推送：

```php
// sse_messages.php
header('Content-Type: text/event-stream');
header('Cache-Control: no-cache');

$userId = $_GET['user_id'] ?? '';

while (true) {
    // 检查新消息
    $newMessages = checkNewMessages($userId);
    
    if (!empty($newMessages)) {
        echo "data: " . json_encode($newMessages) . "\n\n";
        flush();
    }
    
    sleep(2); // 每2秒检查一次
}
```

## 🚀 推荐实施步骤

1. **立即实施**：轮询机制（方案一）
   - 简单可靠，兼容性好
   - 可以快速解决当前问题

2. **后续优化**：考虑WebSocket或SSE
   - 更实时的体验
   - 减少服务器负载

3. **测试验证**：
   - 使用提供的测试页面验证功能
   - 确保消息能正确显示

## 📋 实施清单

- [ ] 创建 `api/get_user_messages.php`
- [ ] 在前台页面添加JavaScript轮询代码
- [ ] 添加CSS样式美化消息显示
- [ ] 测试消息接收功能
- [ ] 优化轮询频率和性能
- [ ] 添加错误处理和重连机制

## 🔍 调试工具

使用提供的测试页面：
- `test_message_flow.php` - 测试消息发送流程
- `frontend_test.php` - 模拟前台消息接收

这样就能解决前台用户收不到消息的问题！
