<?php
session_start();
header('Content-Type: application/json');

// 检查管理员登录状态
if (!isset($_SESSION['admin_id'])) {
    echo json_encode([
        'success' => false,
        'message' => '未登录'
    ]);
    exit;
}

require_once '../../sql/db_config.php';

try {
    $pdo = getDbConnection();
    
    $counts = [];
    
    // 待审核实名认证数量
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM realname_verification WHERE verification_status = 'pending'");
    $counts['pending_verifications'] = $stmt->fetch(PDO::FETCH_ASSOC)['count'] ?? 0;
    
    // 待审核内容数量（如果有内容审核功能）
    try {
        $stmt = $pdo->query("SELECT COUNT(*) as count FROM user_works WHERE status = 'pending'");
        $counts['pending_works'] = $stmt->fetch(PDO::FETCH_ASSOC)['count'] ?? 0;
    } catch (PDOException $e) {
        $counts['pending_works'] = 0;
    }
    
    try {
        $stmt = $pdo->query("SELECT COUNT(*) as count FROM user_activities WHERE status = 'pending'");
        $counts['pending_activities'] = $stmt->fetch(PDO::FETCH_ASSOC)['count'] ?? 0;
    } catch (PDOException $e) {
        $counts['pending_activities'] = 0;
    }
    
    // 今日新增用户
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM users WHERE DATE(created_at) = CURDATE()");
    $counts['today_users'] = $stmt->fetch(PDO::FETCH_ASSOC)['count'] ?? 0;
    
    echo json_encode([
        'success' => true,
        'counts' => $counts
    ]);
    
} catch (PDOException $e) {
    error_log("Check pending error: " . $e->getMessage());
    echo json_encode([
        'success' => false,
        'message' => '查询失败'
    ]);
}
?>
