<?php
session_start();

// 检查用户是否已登录
if (!isset($_SESSION['user_id'])) {
    header('Location: ../login/index.php');
    exit;
}

// 引入数据库配置
require_once '../../includes/db_config.php';

try {
    $dsn = "mysql:host={$db_config['host']};dbname={$db_config['dbname']};charset={$db_config['charset']};port={$db_config['port']}";
    $pdo = new PDO($dsn, $db_config['username'], $db_config['password']);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

    // 获取用户信息
    $stmt = $pdo->prepare("SELECT * FROM users WHERE id = :id");
    $stmt->execute(['id' => $_SESSION['user_id']]);
    $user = $stmt->fetch(PDO::FETCH_ASSOC);

    // 获取用户的作品列表
    $stmt = $pdo->prepare("
        SELECT p.*, c.name as category_name, s.name as subcategory_name
        FROM universe_posts p
        JOIN universe_categories c ON p.category_id = c.id
        JOIN universe_subcategories s ON p.subcategory_id = s.id
        WHERE p.user_id = :user_id AND p.status = 1
        ORDER BY p.created_at DESC
    ");
    $stmt->execute(['user_id' => $_SESSION['user_id']]);
    $posts = $stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    error_log("我的作品页面错误: " . $e->getMessage());
    $db_error = true;
}
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <meta name="theme-color" content="#1E90FF">
    <title>我的作品 - 趣玩宇宙</title>
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="css/my_posts.css">
    <!-- 引入字体图标库 -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <!-- Toast提示 -->
    <div class="toast" id="toast"></div>

    <!-- 顶部导航栏 -->
    <div class="header-bar">
        <a href="../profile/index.php" class="back-button">
            <i class="fas fa-arrow-left"></i>
        </a>
        <div class="header-title">我的作品</div>
        <a href="publish/index.php" class="publish-icon">
            <i class="fas fa-plus"></i>
        </a>
    </div>

    <!-- 内容区域 -->
    <div class="content-container">
        <!-- 作品列表 -->
        <div class="content-list">
            <?php if (empty($posts)): ?>
            <div class="empty-state">
                <i class="fas fa-rocket"></i>
                <p>暂无作品，快来发布第一篇吧！</p>
                <a href="publish/index.php" class="btn">立即发布</a>
            </div>
            <?php else: ?>
            <?php foreach ($posts as $post): ?>
            <div class="content-card">
                <a href="detail.php?id=<?php echo $post['id']; ?>" class="card-content">
                    <h3 class="post-title"><?php echo $post['title']; ?></h3>

                    <?php if (!empty($post['cover_image'])): ?>
                    <div class="post-cover">
                        <img src="<?php echo $post['cover_image']; ?>" alt="封面图片">
                    </div>
                    <?php endif; ?>

                    <div class="post-excerpt">
                        <?php
                        // 移除HTML标签，获取纯文本
                        $excerpt = strip_tags($post['content']);
                        // 截取前100个字符
                        $excerpt = mb_substr($excerpt, 0, 100, 'UTF-8');
                        echo $excerpt . (mb_strlen($excerpt, 'UTF-8') >= 100 ? '...' : '');
                        ?>
                    </div>
                </a>

                <div class="card-footer">
                    <div class="post-meta">
                        <span class="category"><?php echo $post['category_name']; ?> · <?php echo $post['subcategory_name']; ?></span>
                        <span class="time"><?php echo date('Y-m-d H:i', strtotime($post['created_at'])); ?></span>
                    </div>
                    <div class="action-buttons">
                        <div class="action-button">
                            <i class="far fa-eye"></i>
                            <span><?php echo $post['views']; ?></span>
                        </div>
                        <div class="action-button">
                            <i class="far fa-comment"></i>
                            <span><?php echo $post['comments']; ?></span>
                        </div>
                        <div class="action-button">
                            <i class="far fa-heart"></i>
                            <span><?php echo $post['likes']; ?></span>
                        </div>
                        <div class="action-button delete-button" data-id="<?php echo $post['id']; ?>">
                            <i class="far fa-trash-alt"></i>
                        </div>
                    </div>
                </div>
            </div>
            <?php endforeach; ?>
            <?php endif; ?>
        </div>
    </div>

    <!-- 删除确认弹窗 -->
    <div class="delete-modal" id="delete-modal">
        <div class="modal-content">
            <h3>确认删除</h3>
            <p>确定要删除这篇作品吗？删除后无法恢复。</p>
            <div class="modal-buttons">
                <button class="cancel-button">取消</button>
                <button class="confirm-button">确认删除</button>
            </div>
        </div>
    </div>

    <script src="js/my_posts.js"></script>
</body>
</html>
