-- =====================================================
-- 趣玩星球管理后台 - 完整数据库脚本
-- 可直接在phpMyAdmin中执行
-- =====================================================

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- =====================================================
-- 1. 部门表 (departments)
-- =====================================================
CREATE TABLE IF NOT EXISTS `departments` (
    `id` INT PRIMARY KEY AUTO_INCREMENT,
    `name` VARCHAR(100) NOT NULL COMMENT '部门名称',
    `code` VARCHAR(50) UNIQUE COMMENT '部门代码',
    `parent_id` INT DEFAULT NULL COMMENT '上级部门ID',
    `manager_id` INT DEFAULT NULL COMMENT '部门负责人ID',
    `description` TEXT COMMENT '部门描述',
    `status` ENUM('active', 'inactive') DEFAULT 'active' COMMENT '部门状态',
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX `idx_parent_id` (`parent_id`),
    INDEX `idx_manager_id` (`manager_id`),
    INDEX `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='部门表';

-- =====================================================
-- 2. 角色表 (roles)
-- =====================================================
CREATE TABLE IF NOT EXISTS `roles` (
    `id` INT PRIMARY KEY AUTO_INCREMENT,
    `name` VARCHAR(50) NOT NULL COMMENT '角色名称',
    `code` VARCHAR(50) UNIQUE COMMENT '角色代码',
    `description` TEXT COMMENT '角色描述',
    `level` INT DEFAULT 0 COMMENT '角色级别',
    `is_system` BOOLEAN DEFAULT FALSE COMMENT '是否系统角色',
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX `idx_level` (`level`),
    INDEX `idx_is_system` (`is_system`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='角色表';

-- =====================================================
-- 3. 权限表 (permissions)
-- =====================================================
CREATE TABLE IF NOT EXISTS `permissions` (
    `id` INT PRIMARY KEY AUTO_INCREMENT,
    `name` VARCHAR(100) NOT NULL COMMENT '权限名称',
    `code` VARCHAR(100) UNIQUE COMMENT '权限代码',
    `module` VARCHAR(50) COMMENT '所属模块',
    `action` VARCHAR(50) COMMENT '操作类型',
    `description` TEXT COMMENT '权限描述',
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX `idx_module` (`module`),
    INDEX `idx_action` (`action`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='权限表';

-- =====================================================
-- 4. 角色权限关联表 (role_permissions)
-- =====================================================
CREATE TABLE IF NOT EXISTS `role_permissions` (
    `id` INT PRIMARY KEY AUTO_INCREMENT,
    `role_id` INT NOT NULL,
    `permission_id` INT NOT NULL,
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (`role_id`) REFERENCES `roles`(`id`) ON DELETE CASCADE,
    FOREIGN KEY (`permission_id`) REFERENCES `permissions`(`id`) ON DELETE CASCADE,
    UNIQUE KEY `unique_role_permission` (`role_id`, `permission_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='角色权限关联表';

-- =====================================================
-- 5. 用户角色关联表 (user_roles)
-- =====================================================
CREATE TABLE IF NOT EXISTS `user_roles` (
    `id` INT PRIMARY KEY AUTO_INCREMENT,
    `user_id` INT NOT NULL,
    `role_id` INT NOT NULL,
    `department_id` INT DEFAULT NULL COMMENT '在该部门的角色',
    `assigned_by` INT COMMENT '分配人',
    `assigned_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    `expires_at` TIMESTAMP NULL COMMENT '过期时间',
    FOREIGN KEY (`role_id`) REFERENCES `roles`(`id`) ON DELETE CASCADE,
    FOREIGN KEY (`department_id`) REFERENCES `departments`(`id`),
    INDEX `idx_user_id` (`user_id`),
    INDEX `idx_role_id` (`role_id`),
    INDEX `idx_department_id` (`department_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户角色关联表';

-- =====================================================
-- 6. 权限申请表 (permission_requests)
-- =====================================================
CREATE TABLE IF NOT EXISTS `permission_requests` (
    `id` INT PRIMARY KEY AUTO_INCREMENT,
    `user_id` INT NOT NULL COMMENT '申请人',
    `permission_id` INT NOT NULL COMMENT '申请的权限',
    `reason` TEXT COMMENT '申请理由',
    `status` ENUM('pending', 'approved', 'rejected') DEFAULT 'pending' COMMENT '申请状态',
    `reviewer_id` INT DEFAULT NULL COMMENT '审核人',
    `review_comment` TEXT COMMENT '审核意见',
    `reviewed_at` TIMESTAMP NULL COMMENT '审核时间',
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (`permission_id`) REFERENCES `permissions`(`id`),
    INDEX `idx_user_id` (`user_id`),
    INDEX `idx_status` (`status`),
    INDEX `idx_reviewer_id` (`reviewer_id`),
    INDEX `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='权限申请表';

-- =====================================================
-- 7. 用户权限表 (user_permissions)
-- =====================================================
CREATE TABLE IF NOT EXISTS `user_permissions` (
    `id` INT PRIMARY KEY AUTO_INCREMENT,
    `user_id` INT NOT NULL COMMENT '用户ID',
    `permission_id` INT NOT NULL COMMENT '权限ID',
    `granted_by` INT COMMENT '授权人',
    `granted_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    `expires_at` TIMESTAMP NULL COMMENT '过期时间',
    FOREIGN KEY (`permission_id`) REFERENCES `permissions`(`id`) ON DELETE CASCADE,
    UNIQUE KEY `unique_user_permission` (`user_id`, `permission_id`),
    INDEX `idx_user_id` (`user_id`),
    INDEX `idx_permission_id` (`permission_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户权限表';

-- =====================================================
-- 8. 扩展admin_users表（如果列不存在则添加）
-- =====================================================
ALTER TABLE `admin_users`
ADD COLUMN IF NOT EXISTS `department_id` INT DEFAULT NULL COMMENT '所属部门',
ADD COLUMN IF NOT EXISTS `position` VARCHAR(100) DEFAULT NULL COMMENT '职位',
ADD COLUMN IF NOT EXISTS `level` INT DEFAULT 1 COMMENT '职级',
ADD COLUMN IF NOT EXISTS `direct_manager_id` INT DEFAULT NULL COMMENT '直属上级',
ADD COLUMN IF NOT EXISTS `hire_date` DATE DEFAULT NULL COMMENT '入职日期';

-- =====================================================
-- 插入初始数据
-- =====================================================

-- 插入部门数据
INSERT IGNORE INTO `departments` (`id`, `name`, `code`, `parent_id`, `manager_id`, `description`, `status`) VALUES
(1, '客户服务中心', 'CS', NULL, NULL, '负责用户咨询、投诉处理和客户关系维护', 'active'),
(2, '行政管理中心', 'ADMIN', NULL, NULL, '负责公司行政事务、办公环境和后勤保障', 'active'),
(3, '人资与组织运营中心', 'HR', NULL, NULL, '负责人力资源管理、组织架构优化和员工发展', 'active'),
(4, 'MCN直播运营中心', 'MCN', NULL, NULL, '负责直播内容策划、主播管理和直播运营', 'active'),
(5, 'UGC视频创作中心', 'UGC', NULL, NULL, '负责用户生成内容的审核、推广和创作者扶持', 'active'),
(6, '财务与商分中心', 'FINANCE', NULL, NULL, '负责财务管理、商业分析和数据统计', 'active'),
(7, '战略发展中心', 'STRATEGY', NULL, NULL, '负责公司战略规划、市场分析和业务发展', 'active'),
(8, '职能支持中心', 'SUPPORT', NULL, NULL, '负责技术支持、运维保障和系统维护', 'active'),
(9, '用户体验中心', 'UX', NULL, NULL, '负责产品设计、用户体验优化和界面设计', 'active'),
(10, '市场与商务中心', 'MARKETING', NULL, NULL, '负责市场推广、商务合作和品牌建设', 'active'),
(11, '城市玩伴中心', 'CITY', NULL, NULL, '负责城市活动策划、线下玩伴服务', 'active'),
(12, '游戏瓦板中心', 'GAME', NULL, NULL, '负责游戏内容运营、瓦板功能开发', 'active'),
(13, '组局搭子中心', 'TEAM', NULL, NULL, '负责组局功能、搭子匹配算法优化', 'active'),
(14, '景点门票中心', 'TICKET', NULL, NULL, '负责景点合作、门票销售和旅游服务', 'active'),
(15, '攻略内容运营中心', 'CONTENT', NULL, NULL, '负责攻略内容审核、推荐算法和内容运营', 'active');

-- 插入角色数据
INSERT IGNORE INTO `roles` (`id`, `name`, `code`, `description`, `level`, `is_system`) VALUES
(1, '超级管理员', 'super_admin', '拥有系统所有权限，可以管理所有功能模块', 10, TRUE),
(2, '部门总管理员', 'dept_super_admin', '可以管理多个部门，拥有跨部门权限', 9, TRUE),
(3, '部门管理员', 'dept_admin', '管理本部门员工和基础功能权限', 8, TRUE),
(4, '部门主管', 'dept_supervisor', '部门内的主管级别，拥有部分管理权限', 6, FALSE),
(5, '审核专员', 'reviewer', '负责用户认证审核和内容审核工作', 5, FALSE),
(6, '客服专员', 'customer_service', '处理用户咨询和客户服务相关工作', 4, FALSE),
(7, '数据分析师', 'data_analyst', '负责数据分析、报表制作和业务洞察', 5, FALSE),
(8, '内容运营', 'content_operator', '负责内容审核、推荐和运营工作', 4, FALSE),
(9, '普通员工', 'employee', '基础员工权限，可查看基本信息', 2, TRUE),
(10, '实习生', 'intern', '实习生权限，受限的基础功能访问', 1, TRUE);

-- 插入权限数据
INSERT IGNORE INTO `permissions` (`id`, `name`, `code`, `module`, `action`, `description`) VALUES
-- 用户管理权限
(1, '查看用户列表', 'user.view', 'user', 'view', '可以查看所有用户信息'),
(2, '创建用户', 'user.create', 'user', 'create', '可以创建新用户账户'),
(3, '编辑用户', 'user.edit', 'user', 'edit', '可以修改用户信息'),
(4, '删除用户', 'user.delete', 'user', 'delete', '可以删除用户账户'),
(5, '用户权限管理', 'user.permission', 'user', 'permission', '可以分配用户权限'),

-- 认证审核权限
(6, '查看认证申请', 'verification.view', 'verification', 'view', '可以查看认证申请列表'),
(7, '审核认证', 'verification.review', 'verification', 'review', '可以审核认证申请'),
(8, '认证统计', 'verification.stats', 'verification', 'stats', '可以查看认证统计数据'),

-- 权限管理权限
(9, '权限申请', 'permission.apply', 'permission', 'apply', '可以申请新权限'),
(10, '权限审批', 'permission.approve', 'permission', 'approve', '可以审批权限申请'),
(11, '角色管理', 'role.manage', 'role', 'manage', '可以管理系统角色'),
(12, '权限配置', 'permission.config', 'permission', 'config', '可以配置系统权限'),

-- 部门管理权限
(13, '查看部门', 'department.view', 'department', 'view', '可以查看部门信息'),
(14, '创建部门', 'department.create', 'department', 'create', '可以创建新部门'),
(15, '编辑部门', 'department.edit', 'department', 'edit', '可以修改部门信息'),
(16, '删除部门', 'department.delete', 'department', 'delete', '可以删除部门'),
(17, '员工管理', 'department.employee', 'department', 'employee', '可以管理部门员工'),

-- 系统管理权限
(18, '系统配置', 'system.config', 'system', 'config', '可以修改系统配置'),
(19, '日志查看', 'system.logs', 'system', 'logs', '可以查看系统日志'),
(20, '数据备份', 'system.backup', 'system', 'backup', '可以进行数据备份'),
(21, '系统监控', 'system.monitor', 'system', 'monitor', '可以监控系统状态'),

-- 数据统计权限
(22, '数据查看', 'stats.view', 'stats', 'view', '可以查看基础统计数据'),
(23, '数据导出', 'stats.export', 'stats', 'export', '可以导出数据报表'),
(24, '高级分析', 'stats.analysis', 'stats', 'analysis', '可以进行高级数据分析'),

-- 内容管理权限
(25, '内容审核', 'content.review', 'content', 'review', '可以审核用户内容'),
(26, '内容推荐', 'content.recommend', 'content', 'recommend', '可以设置内容推荐'),
(27, '内容删除', 'content.delete', 'content', 'delete', '可以删除违规内容');

-- 插入角色权限关联数据
-- 超级管理员拥有所有权限
INSERT IGNORE INTO `role_permissions` (`role_id`, `permission_id`) VALUES
(1, 1), (1, 2), (1, 3), (1, 4), (1, 5), (1, 6), (1, 7), (1, 8), (1, 9), (1, 10),
(1, 11), (1, 12), (1, 13), (1, 14), (1, 15), (1, 16), (1, 17), (1, 18), (1, 19), (1, 20),
(1, 21), (1, 22), (1, 23), (1, 24), (1, 25), (1, 26), (1, 27);

-- 部门总管理员权限
INSERT IGNORE INTO `role_permissions` (`role_id`, `permission_id`) VALUES
(2, 1), (2, 2), (2, 3), (2, 5), (2, 6), (2, 7), (2, 8), (2, 10), (2, 11), (2, 13), (2, 14), (2, 15), (2, 17), (2, 22), (2, 23);

-- 部门管理员权限
INSERT IGNORE INTO `role_permissions` (`role_id`, `permission_id`) VALUES
(3, 1), (3, 3), (3, 6), (3, 7), (3, 9), (3, 13), (3, 15), (3, 17), (3, 22);

-- 审核专员权限
INSERT IGNORE INTO `role_permissions` (`role_id`, `permission_id`) VALUES
(5, 6), (5, 7), (5, 8), (5, 25);

-- 客服专员权限
INSERT IGNORE INTO `role_permissions` (`role_id`, `permission_id`) VALUES
(6, 1), (6, 6), (6, 22);

-- 普通员工权限
INSERT IGNORE INTO `role_permissions` (`role_id`, `permission_id`) VALUES
(9, 9), (9, 22);

SET FOREIGN_KEY_CHECKS = 1;
