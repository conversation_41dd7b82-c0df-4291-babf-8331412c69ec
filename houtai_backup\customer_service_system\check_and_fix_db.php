<?php
// 检查和修复数据库表结构
header('Content-Type: text/html; charset=UTF-8');
ini_set('default_charset', 'UTF-8');

session_start();

// 检查登录状态
if (!isset($_SESSION['cs_logged_in']) || !$_SESSION['cs_logged_in']) {
    echo '<h1>请先登录客服系统</h1>';
    echo '<a href="../login.php">点击登录</a>';
    exit;
}

require_once '../db_config.php';

echo '<h1>数据库表结构检查和修复</h1>';

try {
    $pdo = getDbConnection();

    // 检查 realtime_notifications 表结构
    echo '<h2>1. 检查 realtime_notifications 表结构</h2>';

    $stmt = $pdo->query("DESCRIBE realtime_notifications");
    $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);

    echo '<h3>当前表结构：</h3>';
    echo '<table border="1" style="border-collapse: collapse;">';
    echo '<tr><th>字段名</th><th>类型</th><th>是否为空</th><th>键</th><th>默认值</th><th>额外</th></tr>';

    $hasMessageColumn = false;
    foreach ($columns as $column) {
        echo '<tr>';
        echo '<td>' . htmlspecialchars($column['Field']) . '</td>';
        echo '<td>' . htmlspecialchars($column['Type']) . '</td>';
        echo '<td>' . htmlspecialchars($column['Null']) . '</td>';
        echo '<td>' . htmlspecialchars($column['Key']) . '</td>';
        echo '<td>' . htmlspecialchars($column['Default'] ?? 'NULL') . '</td>';
        echo '<td>' . htmlspecialchars($column['Extra']) . '</td>';
        echo '</tr>';

        if ($column['Field'] === 'message') {
            $hasMessageColumn = true;
        }
    }
    echo '</table>';

    // 检查 customer_service_messages 表结构
    echo '<h2>2. 检查 customer_service_messages 表结构</h2>';

    $stmt = $pdo->query("DESCRIBE customer_service_messages");
    $msgColumns = $stmt->fetchAll(PDO::FETCH_ASSOC);

    echo '<h3>当前表结构：</h3>';
    echo '<table border="1" style="border-collapse: collapse;">';
    echo '<tr><th>字段名</th><th>类型</th><th>是否为空</th><th>键</th><th>默认值</th><th>额外</th></tr>';

    $contentColumnInfo = null;
    foreach ($msgColumns as $column) {
        echo '<tr>';
        echo '<td>' . htmlspecialchars($column['Field']) . '</td>';
        echo '<td>' . htmlspecialchars($column['Type']) . '</td>';
        echo '<td>' . htmlspecialchars($column['Null']) . '</td>';
        echo '<td>' . htmlspecialchars($column['Key']) . '</td>';
        echo '<td>' . htmlspecialchars($column['Default'] ?? 'NULL') . '</td>';
        echo '<td>' . htmlspecialchars($column['Extra']) . '</td>';
        echo '</tr>';

        if ($column['Field'] === 'content') {
            $contentColumnInfo = $column;
        }
    }
    echo '</table>';

    // 分析问题
    $needsMessageFix = !$hasMessageColumn;
    $needsContentFix = $contentColumnInfo && $contentColumnInfo['Null'] === 'NO' && $contentColumnInfo['Default'] === null;

    echo '<h2>3. 问题分析</h2>';
    if ($needsMessageFix) {
        echo '<p style="color: red;">✗ realtime_notifications 表缺少 message 字段</p>';
    } else {
        echo '<p style="color: green;">✓ realtime_notifications 表的 message 字段正常</p>';
    }

    if ($needsContentFix) {
        echo '<p style="color: red;">✗ customer_service_messages 表的 content 字段不允许NULL且没有默认值</p>';
    } else {
        echo '<p style="color: green;">✓ customer_service_messages 表的 content 字段正常</p>';
    }

    // 修复操作
    if ($_POST['fix_tables'] ?? false) {
        echo '<h2>4. 正在修复表结构...</h2>';

        $fixSuccess = true;

        try {
            if ($needsMessageFix) {
                echo '<p>添加 realtime_notifications.message 字段...</p>';
                $pdo->exec("ALTER TABLE realtime_notifications ADD COLUMN message TEXT COMMENT '通知消息内容' AFTER title");
                echo '<p style="color: green;">✓ 成功添加 message 字段</p>';
            }

            if ($needsContentFix) {
                echo '<p>修复 customer_service_messages.content 字段...</p>';
                // TEXT类型字段在MySQL严格模式下不能有默认值，只能设置为允许NULL
                $pdo->exec("ALTER TABLE customer_service_messages MODIFY COLUMN content TEXT NULL COMMENT '消息内容'");
                echo '<p style="color: green;">✓ 成功修复 content 字段（设置为允许NULL）</p>';
            }

            if (!$needsMessageFix && !$needsContentFix) {
                echo '<p style="color: blue;">所有表结构都正常，无需修复</p>';
            }

        } catch (Exception $e) {
            echo '<p style="color: red;">✗ 修复失败：' . $e->getMessage() . '</p>';
            $fixSuccess = false;
        }

        if ($fixSuccess) {
            echo '<p style="color: green; font-weight: bold;">✓ 表结构修复完成！</p>';
            echo '<p><a href="' . $_SERVER['PHP_SELF'] . '">重新检查表结构</a></p>';
        }

    } else if ($needsMessageFix || $needsContentFix) {
        echo '<h2>4. 修复操作</h2>';
        echo '<p>检测到表结构问题，需要修复：</p>';
        echo '<ul>';
        if ($needsMessageFix) {
            echo '<li>添加 realtime_notifications.message 字段</li>';
        }
        if ($needsContentFix) {
            echo '<li>修复 customer_service_messages.content 字段（允许NULL和设置默认值）</li>';
        }
        echo '</ul>';

        echo '<form method="POST">';
        echo '<button type="submit" name="fix_tables" value="1" style="padding: 10px 20px; background: #dc3545; color: white; border: none; border-radius: 5px; cursor: pointer;">修复所有问题</button>';
        echo '</form>';
    } else {
        echo '<h2>4. 修复状态</h2>';
        echo '<p style="color: green; font-weight: bold;">✓ 所有表结构都正常！</p>';
    }

    // 测试API
    if (!$needsMessageFix && !$needsContentFix) {
        echo '<h2>5. 测试API功能</h2>';

        // 查找一个等待中的会话进行测试
        $stmt = $pdo->query("SELECT session_id FROM customer_service_sessions WHERE status = 'waiting' LIMIT 1");
        $testSession = $stmt->fetch();

        if ($testSession) {
            $testSessionId = $testSession['session_id'];
            echo '<p>测试会话ID: ' . $testSessionId . '</p>';
            echo '<button onclick="testFixedAPI(\'' . $testSessionId . '\')" style="padding: 8px 16px; background: #28a745; color: white; border: none; border-radius: 3px; cursor: pointer;">测试接受API</button>';
            echo '<div id="testResult"></div>';
        } else {
            echo '<p>没有可测试的等待中会话</p>';
            echo '<p><a href="create_test_data.php">创建测试数据</a></p>';
        }
    } else {
        echo '<h2>5. 测试API功能</h2>';
        echo '<p style="color: orange;">请先修复表结构问题，然后重新加载页面进行测试</p>';
    }

} catch (Exception $e) {
    echo '<p style="color: red;">数据库错误：' . $e->getMessage() . '</p>';
}
?>

<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>数据库表结构检查和修复</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        table { margin: 10px 0; }
        th, td { padding: 8px; text-align: left; border: 1px solid #ddd; }
        th { background-color: #f2f2f2; }
        button { margin: 5px; }
        pre { background: #f5f5f5; padding: 10px; border-radius: 5px; }
    </style>
</head>
<body>
    <script>
        async function testFixedAPI(sessionId) {
            const resultDiv = document.getElementById('testResult');
            resultDiv.innerHTML = '<p>测试中...</p>';

            try {
                const response = await fetch('api/accept_session.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        sessionId: sessionId
                    })
                });

                const data = await response.json();

                if (data.success) {
                    resultDiv.innerHTML = '<h3>✓ API测试成功！</h3><pre>' + JSON.stringify(data, null, 2) + '</pre>';
                } else {
                    resultDiv.innerHTML = '<h3>✗ API测试失败</h3><pre>' + JSON.stringify(data, null, 2) + '</pre>';
                }

            } catch (error) {
                resultDiv.innerHTML = '<h3>✗ 网络错误</h3><p>' + error.message + '</p>';
            }
        }
    </script>

    <hr>
    <p>
        <a href="diagnose.php">返回诊断页面</a> |
        <a href="sessions.php">返回会话列表</a> |
        <a href="test_accept_api.php">API测试页面</a>
    </p>
</body>
</html>
