<?php
// 创建测试数据
header('Content-Type: text/html; charset=UTF-8');
ini_set('default_charset', 'UTF-8');

session_start();

// 检查登录状态
if (!isset($_SESSION['cs_logged_in']) || !$_SESSION['cs_logged_in']) {
    echo '<h1>请先登录客服系统</h1>';
    echo '<a href="../login.php">点击登录</a>';
    exit;
}

require_once '../db_config.php';

echo '<h1>创建测试数据</h1>';

if ($_POST['create_data'] ?? false) {
    try {
        $pdo = getDbConnection();
        
        echo '<h2>开始创建测试数据...</h2>';
        
        // 检查并创建测试用户
        $stmt = $pdo->prepare("SELECT id FROM users WHERE phone = '13800138000'");
        $stmt->execute();
        $testUser = $stmt->fetch();
        
        if (!$testUser) {
            echo '<p>创建测试用户...</p>';
            $stmt = $pdo->prepare("
                INSERT INTO users (phone, username, nickname, created_at) 
                VALUES ('13800138000', 'test_user', '测试用户', NOW())
            ");
            $stmt->execute();
            $testUserId = $pdo->lastInsertId();
            echo '<p style="color: green;">✓ 测试用户创建成功，ID: ' . $testUserId . '</p>';
        } else {
            $testUserId = $testUser['id'];
            echo '<p style="color: blue;">测试用户已存在，ID: ' . $testUserId . '</p>';
        }
        
        // 删除旧的测试会话
        echo '<p>清理旧的测试数据...</p>';
        $stmt = $pdo->prepare("DELETE FROM customer_service_messages WHERE session_id LIKE 'session_test_%'");
        $stmt->execute();
        
        $stmt = $pdo->prepare("DELETE FROM customer_service_sessions WHERE session_id LIKE 'session_test_%'");
        $stmt->execute();
        
        // 创建等待中的测试会话
        echo '<p>创建等待中的测试会话...</p>';
        $waitingSessions = [
            [
                'session_id' => 'session_test_waiting_001',
                'priority' => 'normal',
                'message' => '你好，我需要帮助'
            ],
            [
                'session_id' => 'session_test_waiting_002', 
                'priority' => 'high',
                'message' => '请问如何使用这个功能？'
            ],
            [
                'session_id' => 'session_test_waiting_003',
                'priority' => 'urgent', 
                'message' => '紧急问题，请尽快处理！'
            ]
        ];
        
        foreach ($waitingSessions as $index => $sessionData) {
            // 创建会话
            $stmt = $pdo->prepare("
                INSERT INTO customer_service_sessions (
                    session_id, user_id, user_phone, user_name, status, priority, 
                    source, message_count, started_at, created_at
                ) VALUES (?, ?, '13800138000', '测试用户', 'waiting', ?, 'web', 1, NOW(), NOW())
            ");
            $stmt->execute([$sessionData['session_id'], $testUserId, $sessionData['priority']]);
            
            // 创建用户消息
            $stmt = $pdo->prepare("
                INSERT INTO customer_service_messages (
                    session_id, sender_type, sender_id, sender_name, 
                    message_type, content, created_at
                ) VALUES (?, 'user', ?, '测试用户', 'text', ?, NOW())
            ");
            $stmt->execute([$sessionData['session_id'], $testUserId, $sessionData['message']]);
            
            echo '<p style="color: green;">✓ 创建会话: ' . $sessionData['session_id'] . ' (' . $sessionData['priority'] . ')</p>';
        }
        
        echo '<h2 style="color: green;">测试数据创建完成！</h2>';
        echo '<p><a href="sessions.php">查看会话列表</a></p>';
        echo '<p><a href="test_accept_api.php">测试接受API</a></p>';
        
    } catch (Exception $e) {
        echo '<p style="color: red;">创建测试数据失败：' . $e->getMessage() . '</p>';
    }
}
?>

<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>创建测试数据</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        button { padding: 10px 20px; background: #007cba; color: white; border: none; border-radius: 5px; cursor: pointer; }
        button:hover { background: #005a87; }
    </style>
</head>
<body>
    <?php if (!($_POST['create_data'] ?? false)): ?>
    <p>这将创建以下测试数据：</p>
    <ul>
        <li>测试用户：13800138000</li>
        <li>3个等待中的会话（普通、高、紧急优先级）</li>
        <li>每个会话包含一条用户消息</li>
    </ul>
    
    <form method="POST">
        <button type="submit" name="create_data" value="1">创建测试数据</button>
    </form>
    <?php endif; ?>
    
    <p><a href="test_db.php">数据库测试</a></p>
    <p><a href="sessions.php">返回会话列表</a></p>
</body>
</html>
