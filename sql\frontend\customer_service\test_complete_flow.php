<?php
// 完整流程测试页面
header('Content-Type: text/html; charset=UTF-8');
ini_set('default_charset', 'UTF-8');

session_start();

// 模拟用户登录（用于测试）
if (!isset($_SESSION['user_id'])) {
    $_SESSION['user_id'] = 4; // 默认用户ID
    $_SESSION['user_name'] = '测试用户';
}

require_once '../../houtai_backup/db_config.php';

echo '<h1>🔄 完整流程测试</h1>';

try {
    $pdo = getDbConnection();
    
    // 获取用户的会话
    $stmt = $pdo->prepare("
        SELECT session_id, status, customer_service_id, started_at, updated_at, message_count
        FROM customer_service_sessions 
        WHERE user_id = ? 
        ORDER BY updated_at DESC 
        LIMIT 3
    ");
    $stmt->execute([$_SESSION['user_id']]);
    $sessions = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (!empty($sessions)) {
        echo '<h2>📋 您的客服会话</h2>';
        
        foreach ($sessions as $session) {
            $statusColor = $session['status'] === 'active' ? 'green' : ($session['status'] === 'waiting' ? 'orange' : 'gray');
            
            echo '<div style="border: 1px solid #ddd; padding: 15px; margin: 10px 0; border-radius: 8px; background: #f9f9f9;">';
            echo '<h4>会话: ' . htmlspecialchars($session['session_id']) . '</h4>';
            echo '<p><strong>状态:</strong> <span style="color: ' . $statusColor . ';">' . htmlspecialchars($session['status']) . '</span></p>';
            echo '<p><strong>客服ID:</strong> ' . htmlspecialchars($session['customer_service_id'] ?? '未分配') . '</p>';
            echo '<p><strong>消息数量:</strong> ' . htmlspecialchars($session['message_count']) . '</p>';
            echo '<p><strong>开始时间:</strong> ' . htmlspecialchars($session['started_at']) . '</p>';
            echo '<p><strong>更新时间:</strong> ' . htmlspecialchars($session['updated_at']) . '</p>';
            
            // 显示最近的消息
            $stmt = $pdo->prepare("
                SELECT sender_type, sender_name, content, created_at 
                FROM customer_service_messages 
                WHERE session_id = ? 
                ORDER BY created_at DESC 
                LIMIT 3
            ");
            $stmt->execute([$session['session_id']]);
            $messages = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            if (!empty($messages)) {
                echo '<h5>最近消息:</h5>';
                echo '<div style="background: #f8f9fa; padding: 10px; border-radius: 5px; max-height: 150px; overflow-y: auto;">';
                foreach ($messages as $msg) {
                    $senderColor = $msg['sender_type'] === 'customer_service' ? '#007cba' : '#28a745';
                    echo '<div style="margin: 5px 0; padding: 5px; border-left: 3px solid ' . $senderColor . ';">';
                    echo '<small style="color: #666;">' . $msg['created_at'] . ' - ' . $msg['sender_name'] . '</small><br>';
                    echo htmlspecialchars($msg['content']);
                    echo '</div>';
                }
                echo '</div>';
            }
            
            // 操作按钮
            echo '<div style="margin-top: 15px;">';
            if ($session['status'] === 'active') {
                echo '<a href="chat.php?session_id=' . urlencode($session['session_id']) . '" style="background: #6F7BF5; color: white; padding: 8px 16px; text-decoration: none; border-radius: 5px; margin-right: 10px;">💬 打开聊天</a>';
                echo '<button onclick="testAPI(\'' . $session['session_id'] . '\')" style="background: #28a745; color: white; padding: 8px 16px; border: none; border-radius: 5px; cursor: pointer; margin-right: 10px;">🧪 测试API</button>';
                echo '<a href="../../houtai_backup/customer_service_system/quick_message_test.php" target="_blank" style="background: #dc3545; color: white; padding: 8px 16px; text-decoration: none; border-radius: 5px;">📤 客服发送消息</a>';
            } else {
                echo '<span style="color: #666;">会话未激活</span>';
            }
            echo '</div>';
            
            echo '</div>';
        }
    } else {
        echo '<div style="background: #fff3cd; padding: 15px; border-radius: 8px; margin: 10px 0;">';
        echo '<h3>⚠️ 没有找到客服会话</h3>';
        echo '<p>请先创建一个客服会话进行测试</p>';
        echo '<a href="../../houtai_backup/customer_service_system/create_test_data.php" style="background: #007cba; color: white; padding: 8px 16px; text-decoration: none; border-radius: 5px;">创建测试会话</a>';
        echo '</div>';
    }
    
} catch (Exception $e) {
    echo '<div style="background: #f8d7da; padding: 15px; border-radius: 8px; margin: 10px 0; color: #721c24;">';
    echo '<h3>❌ 数据库错误</h3>';
    echo '<p>' . htmlspecialchars($e->getMessage()) . '</p>';
    echo '</div>';
}
?>

<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>完整流程测试</title>
    <style>
        body { 
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; 
            margin: 20px; 
            background: #f5f5f5; 
        }
        .container { 
            max-width: 900px; 
            margin: 0 auto; 
            background: white; 
            padding: 20px; 
            border-radius: 10px; 
            box-shadow: 0 2px 10px rgba(0,0,0,0.1); 
        }
        .result { 
            margin: 10px 0; 
            padding: 15px; 
            border-radius: 8px; 
            border-left: 4px solid #28a745; 
        }
        .success { background: #d4edda; color: #155724; border-left-color: #28a745; }
        .error { background: #f8d7da; color: #721c24; border-left-color: #dc3545; }
        .info { background: #d1ecf1; color: #0c5460; border-left-color: #17a2b8; }
        .warning { background: #fff3cd; color: #856404; border-left-color: #ffc107; }
        pre { 
            background: #f8f9fa; 
            padding: 10px; 
            border-radius: 5px; 
            overflow-x: auto; 
            font-size: 12px; 
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="result info">
            <h3>🔄 完整测试流程</h3>
            <ol>
                <li><strong>客服后台</strong>：接受会话并发送消息</li>
                <li><strong>前台API</strong>：测试消息获取接口</li>
                <li><strong>前台聊天</strong>：打开聊天页面查看实时接收</li>
                <li><strong>验证结果</strong>：确认消息能正确显示</li>
            </ol>
        </div>
        
        <div class="result warning">
            <h3>⚠️ 注意事项</h3>
            <ul>
                <li>确保已修复数据库字段长度问题</li>
                <li>前台聊天页面已修复轮询机制</li>
                <li>打开浏览器开发者工具查看控制台日志</li>
                <li>测试时注意观察轮询响应和消息接收</li>
            </ul>
        </div>
        
        <div id="test-result"></div>
        
        <p style="margin-top: 20px;">
            <a href="index.php">返回客服首页</a> | 
            <a href="../../houtai_backup/customer_service_system/sessions.php">客服后台</a> | 
            <a href="../../houtai_backup/customer_service_system/quick_message_test.php">快速消息测试</a>
        </p>
    </div>
    
    <script>
        async function testAPI(sessionId) {
            const resultDiv = document.getElementById('test-result');
            resultDiv.innerHTML = '<div class="result info">🔄 测试前台消息获取API...</div>';
            
            try {
                // 测试获取消息API
                const response = await fetch(`api/get_new_messages.php?session_id=${sessionId}&last_check=0`);
                const data = await response.json();
                
                console.log('API测试响应:', data);
                
                if (data.success) {
                    resultDiv.innerHTML = `
                        <div class="result success">
                            <h3>✅ 前台API测试成功</h3>
                            <p><strong>会话ID：</strong>${sessionId}</p>
                            <p><strong>消息数量：</strong>${data.count}</p>
                            <p><strong>时间戳：</strong>${data.timestamp}</p>
                            <p><strong>消息列表：</strong></p>
                            <pre>${JSON.stringify(data.messages, null, 2)}</pre>
                            <div style="margin-top: 15px;">
                                <a href="chat.php?session_id=${sessionId}" style="background: #6F7BF5; color: white; padding: 8px 16px; text-decoration: none; border-radius: 5px; margin-right: 10px;">💬 打开聊天页面</a>
                                <button onclick="location.reload()" style="background: #6c757d; color: white; padding: 8px 16px; border: none; border-radius: 5px; cursor: pointer;">🔄 刷新页面</button>
                            </div>
                        </div>
                    `;
                } else {
                    resultDiv.innerHTML = `
                        <div class="result error">
                            <h3>❌ 前台API测试失败</h3>
                            <p><strong>错误：</strong>${data.error}</p>
                            <p><strong>消息：</strong>${data.message || 'N/A'}</p>
                            <pre>${JSON.stringify(data, null, 2)}</pre>
                        </div>
                    `;
                }
                
            } catch (error) {
                console.error('API测试失败:', error);
                resultDiv.innerHTML = `
                    <div class="result error">
                        <h3>❌ 网络错误</h3>
                        <p>${error.message}</p>
                    </div>
                `;
            }
        }
    </script>
</body>
</html>
