<?php
session_start();

// 检查用户是否已登录
if (!isset($_SESSION['user_id'])) {
    header('Location: ../login/index.php');
    exit;
}

// 获取用户信息
$user_id = $_SESSION['user_id'];
$user_name = $_SESSION['nickname'] ?? '用户';
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <meta name="theme-color" content="#6F7BF5">
    <title>客服中心 - 趣玩星球</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        /* 基础重置和字体 */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            color: #333333;
            line-height: 1.6;
            min-height: 100vh;
            overflow-x: hidden;
        }

        /* 顶部导航栏 */
        .header-bar {
            background: linear-gradient(135deg, #6F7BF5 0%, #4D5DFB 100%);
            color: white;
            padding: 16px 20px;
            display: flex;
            align-items: center;
            position: sticky;
            top: 0;
            z-index: 1000;
            box-shadow: 0 4px 20px rgba(111, 123, 245, 0.3);
            backdrop-filter: blur(10px);
        }

        .back-button {
            margin-right: 16px;
            font-size: 20px;
            color: white;
            text-decoration: none;
            padding: 8px;
            border-radius: 50%;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .back-button:hover {
            background: rgba(255, 255, 255, 0.2);
            transform: scale(1.1);
        }

        .header-title {
            font-size: 20px;
            font-weight: 600;
            flex: 1;
        }

        /* 主容器 */
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 30px 20px;
        }

        /* 欢迎区域 */
        .welcome-section {
            text-align: center;
            margin-bottom: 40px;
            padding: 40px 20px;
            background: white;
            border-radius: 20px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .welcome-icon {
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg, #6F7BF5, #4D5DFB);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 20px;
            font-size: 36px;
            color: white;
            box-shadow: 0 8px 24px rgba(111, 123, 245, 0.3);
        }

        .welcome-title {
            font-size: 28px;
            font-weight: 700;
            color: #333;
            margin-bottom: 12px;
        }

        .welcome-subtitle {
            font-size: 16px;
            color: #666;
            margin-bottom: 30px;
            line-height: 1.6;
        }

        /* 服务卡片网格 */
        .services-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 24px;
            margin-bottom: 40px;
        }

        .service-card {
            background: white;
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
            border: 1px solid rgba(255, 255, 255, 0.2);
            position: relative;
            overflow: hidden;
        }

        .service-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(135deg, #6F7BF5, #4D5DFB);
        }

        .service-card:hover {
            transform: translateY(-8px);
            box-shadow: 0 16px 48px rgba(0, 0, 0, 0.15);
        }

        .service-icon {
            width: 60px;
            height: 60px;
            background: linear-gradient(135deg, #6F7BF5, #4D5DFB);
            border-radius: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            color: white;
            margin-bottom: 20px;
            box-shadow: 0 4px 16px rgba(111, 123, 245, 0.3);
        }

        .service-title {
            font-size: 20px;
            font-weight: 600;
            color: #333;
            margin-bottom: 12px;
        }

        .service-description {
            font-size: 14px;
            color: #666;
            line-height: 1.6;
            margin-bottom: 24px;
        }

        .service-features {
            list-style: none;
            margin-bottom: 24px;
        }

        .service-features li {
            font-size: 13px;
            color: #666;
            margin-bottom: 8px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .service-features li::before {
            content: '✓';
            color: #06D6A0;
            font-weight: bold;
            font-size: 12px;
        }

        /* 按钮样式 */
        .btn {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
            padding: 14px 28px;
            background: linear-gradient(135deg, #6F7BF5, #4D5DFB);
            color: white;
            text-decoration: none;
            border-radius: 12px;
            font-weight: 600;
            font-size: 16px;
            transition: all 0.3s ease;
            border: none;
            cursor: pointer;
            box-shadow: 0 4px 16px rgba(111, 123, 245, 0.3);
            position: relative;
            overflow: hidden;
        }

        .btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.5s;
        }

        .btn:hover::before {
            left: 100%;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 24px rgba(111, 123, 245, 0.4);
        }

        .btn:active {
            transform: translateY(0);
        }

        .btn-secondary {
            background: linear-gradient(135deg, #f8f9fa, #e9ecef);
            color: #333;
            box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
        }

        .btn-secondary:hover {
            box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
        }

        /* 统计信息 */
        .stats-section {
            background: white;
            border-radius: 20px;
            padding: 30px;
            margin-bottom: 40px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 24px;
        }

        .stat-item {
            text-align: center;
            padding: 20px;
            background: linear-gradient(135deg, #f8f9fa, #ffffff);
            border-radius: 16px;
            border: 1px solid rgba(111, 123, 245, 0.1);
        }

        .stat-number {
            font-size: 32px;
            font-weight: 700;
            color: #6F7BF5;
            margin-bottom: 8px;
        }

        .stat-label {
            font-size: 14px;
            color: #666;
            font-weight: 500;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .container {
                padding: 20px 16px;
            }

            .welcome-section {
                padding: 30px 20px;
                margin-bottom: 30px;
            }

            .welcome-icon {
                width: 60px;
                height: 60px;
                font-size: 28px;
            }

            .welcome-title {
                font-size: 24px;
            }

            .services-grid {
                grid-template-columns: 1fr;
                gap: 20px;
            }

            .service-card {
                padding: 24px;
            }

            .stats-grid {
                grid-template-columns: repeat(2, 1fr);
                gap: 16px;
            }

            .stat-item {
                padding: 16px;
            }

            .stat-number {
                font-size: 24px;
            }

            .btn {
                padding: 12px 24px;
                font-size: 14px;
            }
        }

        @media (max-width: 480px) {
            .header-bar {
                padding: 12px 16px;
            }

            .header-title {
                font-size: 18px;
            }

            .welcome-title {
                font-size: 20px;
            }

            .stats-grid {
                grid-template-columns: 1fr;
            }
        }

        /* 动画效果 */
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .fade-in-up {
            animation: fadeInUp 0.6s ease-out;
        }

        .fade-in-up:nth-child(2) { animation-delay: 0.1s; }
        .fade-in-up:nth-child(3) { animation-delay: 0.2s; }
        .fade-in-up:nth-child(4) { animation-delay: 0.3s; }
    </style>
</head>
<body>
    <!-- 顶部导航栏 -->
    <div class="header-bar">
        <a href="../profile/index.php" class="back-button">
            <i class="fas fa-arrow-left"></i>
        </a>
        <div class="header-title">客服中心</div>
    </div>

    <div class="container">
        <!-- 欢迎区域 -->
        <div class="welcome-section fade-in-up">
            <div class="welcome-icon">
                <i class="fas fa-headset"></i>
            </div>
            <h1 class="welcome-title">趣玩星球客服中心</h1>
            <p class="welcome-subtitle">我们提供7×24小时专业客服服务<br>让您的每一个问题都能得到及时解答</p>
            <a href="chat.php" class="btn">
                <i class="fas fa-comments"></i>
                开始在线客服
            </a>
        </div>

        <!-- 服务统计 -->
        <div class="stats-section fade-in-up">
            <div class="stats-grid">
                <div class="stat-item">
                    <div class="stat-number">24</div>
                    <div class="stat-label">小时在线</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">98%</div>
                    <div class="stat-label">满意度</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">30</div>
                    <div class="stat-label">秒响应</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">1000+</div>
                    <div class="stat-label">日服务量</div>
                </div>
            </div>
        </div>

        <!-- 服务项目 -->
        <div class="services-grid">
            <div class="service-card fade-in-up">
                <div class="service-icon">
                    <i class="fas fa-robot"></i>
                </div>
                <h3 class="service-title">智能客服</h3>
                <p class="service-description">AI智能客服24小时在线，快速解答常见问题，提供即时帮助。</p>
                <ul class="service-features">
                    <li>24小时在线服务</li>
                    <li>秒级响应速度</li>
                    <li>智能问题识别</li>
                    <li>多轮对话支持</li>
                </ul>
                <a href="chat.php" class="btn">立即咨询</a>
            </div>

            <div class="service-card fade-in-up">
                <div class="service-icon">
                    <i class="fas fa-user-tie"></i>
                </div>
                <h3 class="service-title">人工客服</h3>
                <p class="service-description">专业客服团队提供一对一服务，解决复杂问题和个性化需求。</p>
                <ul class="service-features">
                    <li>专业服务团队</li>
                    <li>一对一专属服务</li>
                    <li>复杂问题处理</li>
                    <li>个性化解决方案</li>
                </ul>
                <a href="chat.php" class="btn">转人工客服</a>
            </div>

            <div class="service-card fade-in-up">
                <div class="service-icon">
                    <i class="fas fa-question-circle"></i>
                </div>
                <h3 class="service-title">常见问题</h3>
                <p class="service-description">整理了用户最关心的问题和详细解答，帮您快速找到答案。</p>
                <ul class="service-features">
                    <li>分类问题整理</li>
                    <li>详细解答说明</li>
                    <li>图文并茂展示</li>
                    <li>定期更新维护</li>
                </ul>
                <a href="#" class="btn btn-secondary">查看FAQ</a>
            </div>
        </div>

        <!-- 返回按钮 -->
        <div style="text-align: center; margin-top: 40px;">
            <a href="../profile/index.php" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i>
                返回个人中心
            </a>
        </div>
    </div>
</body>
</html>
