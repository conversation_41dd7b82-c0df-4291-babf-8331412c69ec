<?php
// 完整的双向通信测试
header('Content-Type: text/html; charset=UTF-8');
ini_set('default_charset', 'UTF-8');

session_start();

// 模拟用户登录
if (!isset($_SESSION['user_id'])) {
    $_SESSION['user_id'] = 4;
    $_SESSION['user_name'] = '测试用户';
    $_SESSION['nickname'] = '测试用户';
}

require_once '../../houtai_backup/db_config.php';

echo '<h1>🔄 完整双向通信测试</h1>';

try {
    $pdo = getDbConnection();
    
    // 创建测试会话
    $testSessionId = 'complete_test_' . time() . '_' . rand(1000, 9999);
    
    $stmt = $pdo->prepare("
        INSERT INTO customer_service_sessions
        (session_id, user_id, user_name, status, priority, source, started_at)
        VALUES (?, ?, ?, 'active', 'normal', 'web', NOW())
    ");
    $stmt->execute([
        $testSessionId,
        $_SESSION['user_id'],
        $_SESSION['user_name']
    ]);
    
    echo '<div style="background: #d4edda; padding: 15px; border-radius: 8px; margin: 10px 0;">';
    echo '<h3>✅ 测试会话创建成功</h3>';
    echo '<p><strong>会话ID:</strong> ' . htmlspecialchars($testSessionId) . '</p>';
    echo '<p><strong>用户ID:</strong> ' . $_SESSION['user_id'] . '</p>';
    echo '<p><strong>状态:</strong> active（已激活）</p>';
    echo '</div>';
    
    // 获取现有会话
    $stmt = $pdo->prepare("
        SELECT session_id, status, customer_service_id, started_at, message_count
        FROM customer_service_sessions 
        WHERE user_id = ? 
        ORDER BY started_at DESC 
        LIMIT 5
    ");
    $stmt->execute([$_SESSION['user_id']]);
    $sessions = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (!empty($sessions)) {
        echo '<h2>📋 选择测试会话</h2>';
        
        foreach ($sessions as $session) {
            $statusColor = $session['status'] === 'active' ? 'green' : ($session['status'] === 'waiting' ? 'orange' : 'gray');
            
            echo '<div style="border: 1px solid #ddd; padding: 15px; margin: 10px 0; border-radius: 8px; background: #f9f9f9;">';
            echo '<h4>会话: ' . htmlspecialchars($session['session_id']) . '</h4>';
            echo '<p><strong>状态:</strong> <span style="color: ' . $statusColor . ';">' . htmlspecialchars($session['status']) . '</span></p>';
            echo '<p><strong>客服ID:</strong> ' . htmlspecialchars($session['customer_service_id'] ?? '未分配') . '</p>';
            echo '<p><strong>消息数量:</strong> ' . htmlspecialchars($session['message_count']) . '</p>';
            echo '<p><strong>开始时间:</strong> ' . htmlspecialchars($session['started_at']) . '</p>';
            
            echo '<div style="margin-top: 15px;">';
            echo '<a href="simple_realtime_test.php?session_id=' . urlencode($session['session_id']) . '" style="background: #6F7BF5; color: white; padding: 8px 16px; text-decoration: none; border-radius: 5px; margin-right: 10px;">🧪 简单测试</a>';
            echo '<a href="chat_fixed.php?session_id=' . urlencode($session['session_id']) . '" style="background: #28a745; color: white; padding: 8px 16px; text-decoration: none; border-radius: 5px; margin-right: 10px;">💬 修复版聊天</a>';
            echo '<a href="../../houtai_backup/customer_service_system/session_detail.php?id=' . urlencode($session['session_id']) . '" target="_blank" style="background: #dc3545; color: white; padding: 8px 16px; text-decoration: none; border-radius: 5px;">🎯 客服后台</a>';
            echo '</div>';
            
            echo '</div>';
        }
    }
    
} catch (Exception $e) {
    echo '<div style="background: #f8d7da; padding: 15px; border-radius: 8px; margin: 10px 0; color: #721c24;">';
    echo '<h3>❌ 错误</h3>';
    echo '<p>' . htmlspecialchars($e->getMessage()) . '</p>';
    echo '</div>';
}
?>

<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>完整双向通信测试</title>
    <style>
        body { 
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; 
            margin: 20px; 
            background: #f5f5f5; 
        }
        .container { 
            max-width: 1000px; 
            margin: 0 auto; 
            background: white; 
            padding: 20px; 
            border-radius: 10px; 
            box-shadow: 0 2px 10px rgba(0,0,0,0.1); 
        }
        .test-plan {
            background: #e7f3ff;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
            border-left: 4px solid #6F7BF5;
        }
        .step {
            background: #fff3cd;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
            border-left: 4px solid #ffc107;
        }
        .problem {
            background: #f8d7da;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
            border-left: 4px solid #dc3545;
        }
        .solution {
            background: #d4edda;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
            border-left: 4px solid #28a745;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="problem">
            <h3>🚨 当前问题</h3>
            <ol>
                <li><strong>后台需要刷新才能看到前台用户消息</strong> - 后台轮询可能有问题</li>
                <li><strong>前台收不到后台消息</strong> - 前台实时通知可能有问题</li>
            </ol>
        </div>
        
        <div class="test-plan">
            <h3>🔍 诊断计划</h3>
            <p>我们将分步骤测试每个组件：</p>
            <ol>
                <li><strong>简单测试</strong>：测试基础的轮询功能</li>
                <li><strong>后台测试</strong>：检查后台是否能接收用户消息</li>
                <li><strong>前台测试</strong>：检查前台是否能接收客服消息</li>
                <li><strong>双向测试</strong>：测试完整的双向通信</li>
            </ol>
        </div>
        
        <div class="step">
            <h3>📝 测试步骤</h3>
            <ol>
                <li><strong>选择会话</strong>：从上面选择一个测试会话</li>
                <li><strong>打开简单测试</strong>：点击"🧪 简单测试"按钮</li>
                <li><strong>开始轮询</strong>：在测试页面点击"开始轮询"</li>
                <li><strong>打开客服后台</strong>：点击"🎯 客服后台"按钮</li>
                <li><strong>发送消息</strong>：在客服后台发送消息</li>
                <li><strong>观察结果</strong>：查看前台是否收到消息</li>
                <li><strong>测试反向</strong>：在前台发送消息，查看后台是否收到</li>
            </ol>
        </div>
        
        <div class="solution">
            <h3>💡 可能的解决方案</h3>
            <ul>
                <li><strong>如果简单测试失败</strong>：检查API路径和数据库连接</li>
                <li><strong>如果后台轮询失败</strong>：检查客服后台的轮询代码</li>
                <li><strong>如果前台通知失败</strong>：检查实时通知系统</li>
                <li><strong>如果数据库问题</strong>：检查消息插入和查询</li>
            </ul>
        </div>
        
        <div style="background: #d1ecf1; padding: 15px; border-radius: 8px; margin: 20px 0;">
            <h3>🔧 调试信息</h3>
            <p><strong>用户ID：</strong><?php echo $_SESSION['user_id']; ?></p>
            <p><strong>用户名：</strong><?php echo $_SESSION['user_name']; ?></p>
            <p><strong>会话数量：</strong><?php echo count($sessions ?? []); ?></p>
            <p><strong>数据库连接：</strong>✅ 正常</p>
        </div>
        
        <p style="margin-top: 30px;">
            <a href="index.php">返回客服首页</a> | 
            <a href="../../houtai_backup/customer_service_system/sessions.php">客服后台列表</a> | 
            <a href="../../houtai_backup/customer_service_system/quick_message_test.php">快速发送消息</a>
        </p>
    </div>
</body>
</html>
