<?php
// 引入session配置
require_once '../../sql/session_config.php';
initLoginSession();

// 检查用户是否已登录
$is_logged_in = isUserLoggedIn();

// 数据库连接配置
$db_config = [
    'host' => 'localhost',
    'username' => 'quwanplanet',
    'password' => 'nJmJm23FB4Xn6Fc3',
    'dbname' => 'quwanplanet',
    'port' => 3306,
    'charset' => 'utf8mb4'
];

// 获取用户信息
$user_avatar_display_url = 'https://s1.imagehub.cc/images/2025/04/26/f5c2f26c4564aabcbb56c5fd30f1d652.jpg';

if ($is_logged_in) {
    try {
        $dsn = "mysql:host={$db_config['host']};dbname={$db_config['dbname']};charset={$db_config['charset']};port={$db_config['port']}";
        $pdo = new PDO($dsn, $db_config['username'], $db_config['password']);
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

        $stmt = $pdo->prepare("SELECT avatar FROM users WHERE id = :user_id");
        $stmt->execute(['user_id' => $_SESSION['user_id']]);
        $user_data = $stmt->fetch(PDO::FETCH_ASSOC);

        if ($user_data && !empty($user_data['avatar'])) {
            $user_avatar_display_url = $user_data['avatar'];
        }
    } catch (PDOException $e) {
        error_log("露营页面错误: " . $e->getMessage());
    }
}
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <meta name="theme-color" content="#6F7BF5">
    <title>露营 - 趣玩星球</title>
    <link rel="stylesheet" href="css/camping.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <?php
    include '../includes/bottom_nav.php';
    echo renderBottomNavCSS();
    ?>
</head>
<body>
    <!-- 页面头部 -->
    <header class="camping-header">
        <div class="header-background">
            <div class="header-overlay"></div>
        </div>
        <div class="header-content">
            <div class="header-top">
                <button class="back-btn" onclick="history.back()">
                    <i class="fas fa-arrow-left"></i>
                </button>
                <h1 class="page-title">露营</h1>
                <button class="share-btn">
                    <i class="fas fa-share-alt"></i>
                </button>
            </div>
            <div class="header-description">
                <p>发现精彩露营活动，与志同道合的朋友一起探索自然</p>
            </div>
        </div>
    </header>

    <!-- 主要内容区域 -->
    <main class="camping-main">
        <!-- 活动优惠券领取区域 -->
        <section class="coupon-section">
            <div class="coupon-container">
                <div class="coupon-header">
                    <h2><i class="fas fa-ticket-alt"></i> 限时优惠券</h2>
                    <span class="countdown" id="countdown">23:59:45</span>
                </div>
                <div class="coupon-list" id="coupon-list">
                    <!-- 优惠券将通过JavaScript动态加载 -->
                </div>
            </div>
        </section>

        <!-- 热门露营活动 -->
        <section class="camping-activities">
            <div class="section-header">
                <h2><i class="fas fa-fire"></i> 热门露营活动</h2>
                <div class="header-actions">
                    <?php if ($is_logged_in): ?>
                    <a href="create-activity.php" class="create-activity-btn">
                        <i class="fas fa-plus"></i> 发起活动
                    </a>
                    <?php endif; ?>
                    <a href="#" class="more-link">查看更多 <i class="fas fa-chevron-right"></i></a>
                </div>
            </div>
            <div class="activities-grid">
                <div class="activity-card">
                    <div class="activity-image">
                        <img src="https://s1.imagehub.cc/images/2025/05/15/camping_activity_1.jpg" alt="春季山谷露营">
                        <div class="activity-badge">热门</div>
                        <div class="activity-status">招募中</div>
                    </div>
                    <div class="activity-info">
                        <h3>春季山谷露营 · 观星赏月</h3>
                        <div class="activity-organizer">
                            <img src="https://s1.imagehub.cc/images/2025/04/26/f5c2f26c4564aabcbb56c5fd30f1d652.jpg" alt="组局者" class="organizer-avatar">
                            <span class="organizer-name">户外达人小李</span>
                            <span class="organizer-level">LV.8</span>
                        </div>
                        <div class="activity-details">
                            <div class="detail-item">
                                <i class="fas fa-calendar-alt"></i>
                                <span>5月25日-26日</span>
                            </div>
                            <div class="detail-item">
                                <i class="fas fa-map-marker-alt"></i>
                                <span>北京·怀柔山谷</span>
                            </div>
                            <div class="detail-item">
                                <i class="fas fa-users"></i>
                                <span>已有12人参加 (限20人)</span>
                            </div>
                        </div>
                        <div class="activity-features">
                            <span class="feature-tag">篝火晚会</span>
                            <span class="feature-tag">观星</span>
                            <span class="feature-tag">烧烤</span>
                        </div>
                        <div class="activity-price">
                            <span class="price">¥168</span>
                            <span class="unit">/人</span>
                            <span class="original-price">¥198</span>
                        </div>
                    </div>
                    <button class="join-activity-btn">立即参加</button>
                </div>

                <div class="activity-card">
                    <div class="activity-image">
                        <img src="https://s1.imagehub.cc/images/2025/05/15/camping_activity_2.jpg" alt="湖边露营">
                        <div class="activity-badge new">新活动</div>
                        <div class="activity-status">招募中</div>
                    </div>
                    <div class="activity-info">
                        <h3>湖边露营 · 日出摄影</h3>
                        <div class="activity-organizer">
                            <img src="https://s1.imagehub.cc/images/2025/04/26/f5c2f26c4564aabcbb56c5fd30f1d652.jpg" alt="组局者" class="organizer-avatar">
                            <span class="organizer-name">摄影师小王</span>
                            <span class="organizer-level">LV.6</span>
                        </div>
                        <div class="activity-details">
                            <div class="detail-item">
                                <i class="fas fa-calendar-alt"></i>
                                <span>6月1日-2日</span>
                            </div>
                            <div class="detail-item">
                                <i class="fas fa-map-marker-alt"></i>
                                <span>上海·淀山湖</span>
                            </div>
                            <div class="detail-item">
                                <i class="fas fa-users"></i>
                                <span>已有8人参加 (限15人)</span>
                            </div>
                        </div>
                        <div class="activity-features">
                            <span class="feature-tag">日出摄影</span>
                            <span class="feature-tag">钓鱼</span>
                            <span class="feature-tag">划船</span>
                        </div>
                        <div class="activity-price">
                            <span class="price">¥228</span>
                            <span class="unit">/人</span>
                            <span class="original-price">¥258</span>
                        </div>
                    </div>
                    <button class="join-activity-btn">立即参加</button>
                </div>
            </div>
        </section>

        <!-- 发起露营活动 -->
        <section class="create-activity-section">
            <div class="section-header">
                <h2><i class="fas fa-plus-circle"></i> 发起露营活动</h2>
            </div>
            <div class="create-activity-card">
                <div class="create-activity-content">
                    <div class="create-icon">
                        <i class="fas fa-campground"></i>
                    </div>
                    <div class="create-text">
                        <h3>成为露营组局者</h3>
                        <p>发起你的专属露营活动，邀请志同道合的朋友一起探索自然</p>
                    </div>
                </div>
                <button class="create-activity-btn">立即发起</button>
            </div>
        </section>

        <!-- 露营活动分类 -->
        <section class="activity-categories-section">
            <div class="section-header">
                <h2><i class="fas fa-list"></i> 活动分类</h2>
            </div>
            <div class="categories-grid">
                <div class="category-item" data-category="mountain">
                    <div class="category-icon">
                        <i class="fas fa-mountain"></i>
                    </div>
                    <span class="category-name">山地露营</span>
                    <span class="category-count">23个活动</span>
                </div>
                <div class="category-item" data-category="lake">
                    <div class="category-icon">
                        <i class="fas fa-water"></i>
                    </div>
                    <span class="category-name">湖边露营</span>
                    <span class="category-count">18个活动</span>
                </div>
                <div class="category-item" data-category="forest">
                    <div class="category-icon">
                        <i class="fas fa-tree"></i>
                    </div>
                    <span class="category-name">森林露营</span>
                    <span class="category-count">15个活动</span>
                </div>
                <div class="category-item" data-category="beach">
                    <div class="category-icon">
                        <i class="fas fa-umbrella-beach"></i>
                    </div>
                    <span class="category-name">海边露营</span>
                    <span class="category-count">12个活动</span>
                </div>
            </div>
        </section>

        <!-- 露营攻略 -->
        <section class="camping-guides-section">
            <div class="section-header">
                <h2><i class="fas fa-book-open"></i> 露营攻略</h2>
                <a href="#" class="more-link">查看更多 <i class="fas fa-chevron-right"></i></a>
            </div>
            <div class="guides-list">
                <div class="guide-item">
                    <div class="guide-image">
                        <img src="https://s1.imagehub.cc/images/2025/05/15/camping_guide_1.jpg" alt="新手露营指南">
                    </div>
                    <div class="guide-content">
                        <h4>新手露营完全指南</h4>
                        <p>从装备准备到安全注意事项，一篇文章带你入门露营</p>
                        <div class="guide-meta">
                            <span><i class="fas fa-eye"></i> 1.2k阅读</span>
                            <span><i class="fas fa-thumbs-up"></i> 89点赞</span>
                        </div>
                    </div>
                </div>
                <div class="guide-item">
                    <div class="guide-image">
                        <img src="https://s1.imagehub.cc/images/2025/05/15/camping_guide_2.jpg" alt="露营美食制作">
                    </div>
                    <div class="guide-content">
                        <h4>露营美食制作技巧</h4>
                        <p>在野外也能享受美味，简单易做的露营料理分享</p>
                        <div class="guide-meta">
                            <span><i class="fas fa-eye"></i> 856阅读</span>
                            <span><i class="fas fa-thumbs-up"></i> 67点赞</span>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <!-- 加载动画 -->
    <div class="loading-overlay" id="loading-overlay">
        <div class="four-star-loader">
            <div class="star-point"></div>
            <div class="star-point"></div>
            <div class="star-point"></div>
            <div class="star-point"></div>
        </div>
        <div class="loading-text">正在加载中...</div>
    </div>

    <!-- 活动规则入口 -->
    <div class="activity-rules-entrance" id="activity-rules-entrance">
        <div class="rules-text">
            <span>活</span>
            <span>动</span>
            <span>规</span>
            <span>则</span>
        </div>
    </div>

    <!-- Toast提示 -->
    <div class="toast" id="toast"></div>

    <?php echo renderBottomNav('home', $is_logged_in); ?>

    <script>
        // 传递登录状态到JavaScript
        window.userLoggedIn = <?php echo $is_logged_in ? 'true' : 'false'; ?>;
        window.userId = <?php echo $is_logged_in ? $_SESSION['user_id'] : 'null'; ?>;
    </script>
    <script src="js/camping.js"></script>
    <?php echo renderBottomNavJS($is_logged_in); ?>
</body>
</html>
