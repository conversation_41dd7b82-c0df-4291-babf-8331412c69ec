<?php
/**
 * 测试编辑API的简单页面
 */

session_start();

// 模拟管理员登录（仅用于测试）
if (!isset($_SESSION['admin_id'])) {
    $_SESSION['admin_id'] = 1;
    $_SESSION['admin_name'] = '测试管理员';
}

?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试编辑API</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 600px;
            margin: 50px auto;
            padding: 20px;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input, textarea {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        button {
            background: #007cba;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        button:hover {
            background: #005a87;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 4px;
            white-space: pre-wrap;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
    </style>
</head>
<body>
    <h1>测试用户编辑API</h1>
    
    <form id="testForm">
        <div class="form-group">
            <label for="user_id">用户ID:</label>
            <input type="number" id="user_id" name="user_id" value="1" required>
        </div>
        
        <div class="form-group">
            <label for="username">用户昵称:</label>
            <input type="text" id="username" name="username" placeholder="留空表示不修改">
        </div>
        
        <div class="form-group">
            <label for="quwan_id">趣玩ID:</label>
            <input type="text" id="quwan_id" name="quwan_id" placeholder="留空表示不修改">
        </div>
        
        <div class="form-group">
            <label for="email">邮箱:</label>
            <input type="email" id="email" name="email" placeholder="留空表示不修改">
        </div>
        
        <div class="form-group">
            <label for="phone">手机号:</label>
            <input type="text" id="phone" name="phone" placeholder="留空表示不修改">
        </div>
        
        <div class="form-group">
            <label for="region">地区:</label>
            <input type="text" id="region" name="region" placeholder="留空表示不修改">
        </div>
        
        <div class="form-group">
            <label for="bio">个人简介:</label>
            <textarea id="bio" name="bio" rows="3" placeholder="留空表示不修改"></textarea>
        </div>
        
        <button type="submit">测试修改</button>
    </form>
    
    <div id="result"></div>

    <script>
        document.getElementById('testForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const formData = new FormData(this);
            formData.append('action', 'edit_user');
            
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = '正在提交...';
            
            try {
                const response = await fetch('edit_user_api.php', {
                    method: 'POST',
                    body: formData
                });
                
                const result = await response.json();
                
                if (result.success) {
                    resultDiv.className = 'result success';
                    resultDiv.textContent = '修改成功！\n\n' + JSON.stringify(result, null, 2);
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.textContent = '修改失败：' + result.message + '\n\n' + JSON.stringify(result, null, 2);
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = '网络错误：' + error.message;
            }
        });
    </script>
</body>
</html>
