/**
 * 优惠券管理JavaScript
 * 处理优惠券的增删改查操作
 */

// 全局变量
let currentCouponId = null;
let couponsData = [];

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    initializePage();
});

// 初始化页面
function initializePage() {
    // 设置默认的有效期时间
    const now = new Date();
    const tomorrow = new Date(now.getTime() + 24 * 60 * 60 * 1000);
    const nextMonth = new Date(now.getTime() + 30 * 24 * 60 * 60 * 1000);
    
    document.getElementById('valid_from').value = formatDateTimeLocal(tomorrow);
    document.getElementById('valid_until').value = formatDateTimeLocal(nextMonth);
    
    // 加载优惠券数据
    loadCouponsData();
}

// 格式化日期时间为本地格式
function formatDateTimeLocal(date) {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    const hours = String(date.getHours()).padStart(2, '0');
    const minutes = String(date.getMinutes()).padStart(2, '0');
    
    return `${year}-${month}-${day}T${hours}:${minutes}`;
}

// 加载优惠券数据
function loadCouponsData() {
    const rows = document.querySelectorAll('#couponsTable tbody tr');
    couponsData = [];
    
    rows.forEach(row => {
        const cells = row.cells;
        if (cells.length > 0) {
            couponsData.push({
                id: cells[0].textContent,
                title: cells[1].textContent,
                type: cells[2].textContent,
                amount: cells[3].textContent,
                minAmount: cells[4].textContent,
                totalQuantity: cells[5].textContent,
                claimedQuantity: cells[6].textContent,
                validPeriod: cells[7].textContent,
                status: cells[8].textContent.trim()
            });
        }
    });
}

// 显示添加优惠券模态框
function showAddModal() {
    currentCouponId = null;
    document.getElementById('modalTitle').textContent = '添加优惠券';
    document.getElementById('couponForm').reset();
    
    // 重新设置默认时间
    const now = new Date();
    const tomorrow = new Date(now.getTime() + 24 * 60 * 60 * 1000);
    const nextMonth = new Date(now.getTime() + 30 * 24 * 60 * 60 * 1000);
    
    document.getElementById('valid_from').value = formatDateTimeLocal(tomorrow);
    document.getElementById('valid_until').value = formatDateTimeLocal(nextMonth);
    
    document.getElementById('couponModal').classList.add('show');
}

// 编辑优惠券
function editCoupon(id) {
    currentCouponId = id;
    document.getElementById('modalTitle').textContent = '编辑优惠券';
    
    // 这里应该从服务器获取优惠券详情，暂时使用表格数据
    const row = document.querySelector(`#couponsTable tbody tr td:first-child`);
    if (row && row.textContent == id) {
        const cells = row.parentElement.cells;
        
        document.getElementById('couponId').value = id;
        document.getElementById('title').value = cells[1].textContent;
        
        // 设置类型
        const typeText = cells[2].textContent;
        const typeMap = {
            '参加活动': 'join_discount',
            '组局活动': 'organize_discount',
            '新人专享': 'newbie_discount'
        };
        document.getElementById('type').value = typeMap[typeText] || 'newbie_discount';
        
        // 提取金额数字
        document.getElementById('discount_amount').value = cells[3].textContent.replace('¥', '');
        document.getElementById('min_amount').value = cells[4].textContent.replace('¥', '');
        document.getElementById('total_quantity').value = cells[5].textContent;
        
        // 这里需要从服务器获取完整的优惠券信息，包括描述和有效期
        // 暂时设置默认值
        document.getElementById('description').value = '';
        
        const now = new Date();
        const nextMonth = new Date(now.getTime() + 30 * 24 * 60 * 60 * 1000);
        document.getElementById('valid_from').value = formatDateTimeLocal(now);
        document.getElementById('valid_until').value = formatDateTimeLocal(nextMonth);
    }
    
    document.getElementById('couponModal').classList.add('show');
}

// 删除优惠券
function deleteCoupon(id) {
    if (confirm('确定要删除这张优惠券吗？删除后无法恢复。')) {
        const formData = new FormData();
        formData.append('action', 'delete_coupon');
        formData.append('id', id);
        
        fetch('camping_coupons.php', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showMessage('删除成功', 'success');
                setTimeout(() => {
                    location.reload();
                }, 1000);
            } else {
                showMessage(data.message, 'error');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showMessage('操作失败，请重试', 'error');
        });
    }
}

// 切换优惠券状态
function toggleStatus(id) {
    const formData = new FormData();
    formData.append('action', 'toggle_status');
    formData.append('id', id);
    
    fetch('camping_coupons.php', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showMessage('状态更新成功', 'success');
            setTimeout(() => {
                location.reload();
            }, 1000);
        } else {
            showMessage(data.message, 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showMessage('操作失败，请重试', 'error');
    });
}

// 保存优惠券
function saveCoupon() {
    const form = document.getElementById('couponForm');
    const formData = new FormData(form);
    
    // 验证表单
    if (!validateForm()) {
        return;
    }
    
    // 设置操作类型
    if (currentCouponId) {
        formData.append('action', 'edit_coupon');
        formData.append('id', currentCouponId);
    } else {
        formData.append('action', 'add_coupon');
    }
    
    // 显示加载状态
    const saveBtn = document.querySelector('.modal-footer .btn-primary');
    const originalText = saveBtn.textContent;
    saveBtn.textContent = '保存中...';
    saveBtn.disabled = true;
    
    fetch('camping_coupons.php', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showMessage(data.message, 'success');
            closeModal();
            setTimeout(() => {
                location.reload();
            }, 1000);
        } else {
            showMessage(data.message, 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showMessage('操作失败，请重试', 'error');
    })
    .finally(() => {
        saveBtn.textContent = originalText;
        saveBtn.disabled = false;
    });
}

// 验证表单
function validateForm() {
    const title = document.getElementById('title').value.trim();
    const discountAmount = parseFloat(document.getElementById('discount_amount').value);
    const totalQuantity = parseInt(document.getElementById('total_quantity').value);
    const validFrom = new Date(document.getElementById('valid_from').value);
    const validUntil = new Date(document.getElementById('valid_until').value);
    
    if (!title) {
        showMessage('请输入优惠券标题', 'error');
        return false;
    }
    
    if (isNaN(discountAmount) || discountAmount <= 0) {
        showMessage('请输入有效的优惠金额', 'error');
        return false;
    }
    
    if (isNaN(totalQuantity) || totalQuantity <= 0) {
        showMessage('请输入有效的发放数量', 'error');
        return false;
    }
    
    if (validFrom >= validUntil) {
        showMessage('有效期结束时间必须晚于开始时间', 'error');
        return false;
    }
    
    return true;
}

// 关闭模态框
function closeModal() {
    document.getElementById('couponModal').classList.remove('show');
    currentCouponId = null;
}

// 过滤优惠券
function filterCoupons() {
    const searchTerm = document.getElementById('searchInput').value.toLowerCase();
    const rows = document.querySelectorAll('#couponsTable tbody tr');
    
    rows.forEach(row => {
        const text = row.textContent.toLowerCase();
        if (text.includes(searchTerm)) {
            row.style.display = '';
        } else {
            row.style.display = 'none';
        }
    });
}

// 显示消息
function showMessage(message, type = 'info') {
    // 创建消息元素
    const messageEl = document.createElement('div');
    messageEl.className = `message message-${type}`;
    messageEl.textContent = message;
    
    // 添加样式
    messageEl.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        padding: 12px 20px;
        border-radius: 6px;
        color: white;
        font-weight: 500;
        z-index: 10000;
        animation: slideIn 0.3s ease;
    `;
    
    // 设置背景色
    switch (type) {
        case 'success':
            messageEl.style.background = '#10b981';
            break;
        case 'error':
            messageEl.style.background = '#ef4444';
            break;
        case 'warning':
            messageEl.style.background = '#f59e0b';
            break;
        default:
            messageEl.style.background = '#6366f1';
    }
    
    // 添加到页面
    document.body.appendChild(messageEl);
    
    // 3秒后自动移除
    setTimeout(() => {
        messageEl.style.animation = 'slideOut 0.3s ease';
        setTimeout(() => {
            if (messageEl.parentNode) {
                messageEl.parentNode.removeChild(messageEl);
            }
        }, 300);
    }, 3000);
}

// 添加动画样式
const style = document.createElement('style');
style.textContent = `
    @keyframes slideIn {
        from {
            transform: translateX(100%);
            opacity: 0;
        }
        to {
            transform: translateX(0);
            opacity: 1;
        }
    }
    
    @keyframes slideOut {
        from {
            transform: translateX(0);
            opacity: 1;
        }
        to {
            transform: translateX(100%);
            opacity: 0;
        }
    }
`;
document.head.appendChild(style);

// 点击模态框外部关闭
document.addEventListener('click', function(e) {
    const modal = document.getElementById('couponModal');
    if (e.target === modal) {
        closeModal();
    }
});

// ESC键关闭模态框
document.addEventListener('keydown', function(e) {
    if (e.key === 'Escape') {
        closeModal();
    }
});
