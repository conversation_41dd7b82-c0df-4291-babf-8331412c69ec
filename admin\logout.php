<?php
// 引入session配置
require_once '../sql/session_config.php';
initAdminSession();

// 清除管理员登录状态
clearAdminLoginSession();

// 删除会话cookie
if (ini_get("session.use_cookies")) {
    $params = session_get_cookie_params();
    setcookie(session_name(), '', time() - 42000,
        $params["path"], $params["domain"],
        $params["secure"], $params["httponly"]
    );
}

// 销毁会话
session_destroy();

// 重定向到登录页面
header('Location: login.php');
exit;
?>
