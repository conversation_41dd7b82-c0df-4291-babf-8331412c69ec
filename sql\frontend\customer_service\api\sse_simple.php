<?php
// 简化版SSE测试
header('Content-Type: text/event-stream');
header('Cache-Control: no-cache');
header('Connection: keep-alive');
header('Access-Control-Allow-Origin: *');

// 禁用输出缓冲
while (ob_get_level()) {
    ob_end_clean();
}

// 设置无限执行时间
set_time_limit(0);
ignore_user_abort(false);

$sessionId = $_GET['session_id'] ?? 'test_session';
$userId = $_GET['user_id'] ?? 4;

// 发送连接成功消息
echo "data: " . json_encode([
    'type' => 'connected',
    'message' => 'SSE连接成功',
    'session_id' => $sessionId,
    'user_id' => $userId,
    'timestamp' => time()
]) . "\n\n";
flush();

// 引用数据库配置文件
try {
    require_once '../../../houtai_backup/db_config.php';
    $pdo = getDbConnection();
    
    echo "data: " . json_encode([
        'type' => 'database_connected',
        'message' => '数据库连接成功',
        'timestamp' => time()
    ]) . "\n\n";
    flush();
    
} catch (Exception $e) {
    echo "data: " . json_encode([
        'type' => 'error',
        'message' => '数据库连接失败: ' . $e->getMessage(),
        'timestamp' => time()
    ]) . "\n\n";
    flush();
    exit;
}

$lastCheckTime = time();
$counter = 0;

// 持续监听
while (true) {
    $counter++;
    
    // 检查连接是否还活着
    if (connection_aborted()) {
        break;
    }
    
    try {
        // 每10秒发送一次心跳
        if ($counter % 5 == 0) {
            echo "data: " . json_encode([
                'type' => 'heartbeat',
                'counter' => $counter,
                'timestamp' => time()
            ]) . "\n\n";
            flush();
        }
        
        // 检查新消息
        $stmt = $pdo->prepare("
            SELECT id, sender_type, sender_name, content, created_at, UNIX_TIMESTAMP(created_at) as timestamp
            FROM customer_service_messages 
            WHERE session_id = ? 
            AND sender_type = 'customer_service' 
            AND UNIX_TIMESTAMP(created_at) > ?
            ORDER BY created_at ASC
        ");
        $stmt->execute([$sessionId, $lastCheckTime]);
        $newMessages = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        if (!empty($newMessages)) {
            foreach ($newMessages as $message) {
                echo "data: " . json_encode([
                    'type' => 'new_message',
                    'message_id' => $message['id'],
                    'sender_type' => $message['sender_type'],
                    'sender_name' => $message['sender_name'],
                    'content' => $message['content'],
                    'created_at' => $message['created_at'],
                    'timestamp' => $message['timestamp'],
                    'session_id' => $sessionId
                ]) . "\n\n";
                flush();
                
                $lastCheckTime = $message['timestamp'];
            }
        }
        
        // 等待2秒
        sleep(2);
        
    } catch (Exception $e) {
        echo "data: " . json_encode([
            'type' => 'error',
            'message' => $e->getMessage(),
            'timestamp' => time()
        ]) . "\n\n";
        flush();
        break;
    }
}
?>
