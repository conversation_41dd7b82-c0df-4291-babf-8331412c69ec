<?php
/**
 * 客服系统左侧菜单组件
 */

// 获取当前页面文件名
$current_page = basename($_SERVER['PHP_SELF']);
$current_dir = basename(dirname($_SERVER['PHP_SELF']));

// 获取客服信息
$cs_name = $_SESSION['cs_name'] ?? '客服';
$cs_role = $_SESSION['cs_role'] ?? 'customer_service';
$cs_department = $_SESSION['cs_department'] ?? '';
$cs_team = $_SESSION['cs_team'] ?? '';

// 获取待处理数量
try {
    if (!isset($pdo)) {
        require_once __DIR__ . '/../../db_config.php';
        $pdo = getDbConnection();
    }

    // 待处理会话数量
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM customer_service_sessions WHERE status = 'waiting'");
    $waiting_count = $stmt->fetch()['count'] ?? 0;

    // 待质检数量
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM customer_service_quality_checks WHERE status = 'pending'");
    $quality_pending_count = $stmt->fetch()['count'] ?? 0;

} catch (Exception $e) {
    $waiting_count = 0;
    $quality_pending_count = 0;
}
?>

<aside class="cs-sidebar">
    <div class="cs-sidebar-header">
        <div class="cs-logo">
            <div class="cs-logo-icon">
                <img src="https://s1.imagehub.cc/images/2025/05/26/2cdd63b39f96072fc60cd733027d2a30.png" alt="趣玩星球" class="cs-logo-image">
            </div>
            <div class="cs-logo-text">
                <h2>趣玩星球</h2>
                <p>客服管理系统</p>
            </div>
        </div>
    </div>

    <nav class="cs-sidebar-nav">
        <ul class="cs-nav-list">
            <!-- 工作台 -->
            <li class="cs-nav-item <?php echo $current_page === 'index.php' ? 'active' : ''; ?>">
                <a href="index.php" class="cs-nav-link">
                    <i class="fas fa-tachometer-alt"></i>
                    <span class="cs-nav-link-text">工作台</span>
                </a>
            </li>

            <!-- 客服会话 -->
            <li class="cs-nav-item <?php echo $current_page === 'sessions.php' ? 'active' : ''; ?>">
                <a href="sessions.php" class="cs-nav-link">
                    <i class="fas fa-comments"></i>
                    <span class="cs-nav-link-text">客服会话</span>
                    <?php if ($waiting_count > 0): ?>
                        <span class="cs-nav-badge"><?php echo $waiting_count; ?></span>
                    <?php endif; ?>
                </a>
            </li>

            <!-- 会话管理 -->
            <li class="cs-nav-item <?php echo $current_page === 'session_management.php' ? 'active' : ''; ?>">
                <a href="session_management.php" class="cs-nav-link">
                    <i class="fas fa-tasks"></i>
                    <span class="cs-nav-link-text">会话管理</span>
                </a>
            </li>

            <?php if ($cs_role === 'super_admin'): ?>
            <!-- 智能客服管理 (仅超级管理员可见) -->
            <li class="cs-nav-item <?php echo $current_page === 'bot_management.php' ? 'active' : ''; ?>">
                <a href="bot_management.php" class="cs-nav-link">
                    <i class="fas fa-robot"></i>
                    <span class="cs-nav-link-text">智能客服管理</span>
                    <span class="cs-admin-only">管理员</span>
                </a>
            </li>

            <!-- 客服系统设置 (仅超级管理员可见) -->
            <li class="cs-nav-item <?php echo $current_page === 'settings.php' ? 'active' : ''; ?>">
                <a href="settings.php" class="cs-nav-link">
                    <i class="fas fa-cog"></i>
                    <span class="cs-nav-link-text">客服系统设置</span>
                    <span class="cs-admin-only">管理员</span>
                </a>
            </li>
            <?php endif; ?>

            <!-- 客服质检 -->
            <li class="cs-nav-item <?php echo $current_page === 'quality_check.php' ? 'active' : ''; ?>">
                <a href="quality_check.php" class="cs-nav-link">
                    <i class="fas fa-star"></i>
                    <span class="cs-nav-link-text">客服质检</span>
                    <?php if ($quality_pending_count > 0): ?>
                        <span class="cs-nav-badge"><?php echo $quality_pending_count; ?></span>
                    <?php endif; ?>
                </a>
            </li>

            <?php if ($cs_role === 'super_admin'): ?>
            <!-- 客服管理 (仅超级管理员可见) -->
            <li class="cs-nav-item <?php echo $current_page === 'staff_management.php' ? 'active' : ''; ?>">
                <a href="staff_management.php" class="cs-nav-link">
                    <i class="fas fa-users-cog"></i>
                    <span class="cs-nav-link-text">客服管理</span>
                    <span class="cs-admin-only">管理员</span>
                </a>
            </li>

            <!-- 河图系统 (仅超级管理员可见) -->
            <li class="cs-nav-item <?php echo $current_page === 'hetu_system.php' ? 'active' : ''; ?>">
                <a href="hetu_system.php" class="cs-nav-link">
                    <i class="fas fa-chart-network"></i>
                    <span class="cs-nav-link-text">河图系统</span>
                    <span class="cs-admin-only">管理员</span>
                </a>
            </li>
            <?php endif; ?>

            <!-- 分隔线 -->
            <li class="cs-nav-divider"></li>

            <!-- 个人中心 -->
            <li class="cs-nav-item <?php echo $current_page === 'profile.php' ? 'active' : ''; ?>">
                <a href="profile.php" class="cs-nav-link">
                    <i class="fas fa-user"></i>
                    <span class="cs-nav-link-text">个人中心</span>
                </a>
            </li>

            <!-- 帮助中心 -->
            <li class="cs-nav-item <?php echo $current_page === 'help.php' ? 'active' : ''; ?>">
                <a href="help.php" class="cs-nav-link">
                    <i class="fas fa-question-circle"></i>
                    <span class="cs-nav-link-text">帮助中心</span>
                </a>
            </li>
        </ul>
    </nav>

    <!-- 用户信息 -->
    <div class="cs-user-info">
        <div class="cs-user-avatar">
            <i class="fas fa-user-circle"></i>
        </div>
        <div class="cs-user-details">
            <div class="cs-user-name"><?php echo htmlspecialchars($cs_name); ?></div>
            <div class="cs-user-role">
                <?php echo $cs_role === 'super_admin' ? '超级管理员' : '客服专员'; ?>
            </div>
            <div class="cs-user-department">
                <?php echo htmlspecialchars($cs_department); ?>
                <?php if ($cs_team): ?>
                    <br><small><?php echo htmlspecialchars($cs_team); ?></small>
                <?php endif; ?>
            </div>
        </div>
        <div class="cs-user-actions">
            <a href="logout.php" class="cs-logout-btn" title="退出登录">
                <i class="fas fa-sign-out-alt"></i>
            </a>
        </div>
    </div>
</aside>

<script>
// 侧边栏交互脚本
document.addEventListener('DOMContentLoaded', function() {
    // 添加菜单项点击效果
    const navLinks = document.querySelectorAll('.cs-nav-link');
    navLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            // 移除其他活动状态
            document.querySelectorAll('.cs-nav-item').forEach(item => {
                item.classList.remove('active');
            });
            
            // 添加当前活动状态
            this.parentElement.classList.add('active');
        });
    });
});
</script>
