<?php
session_start();

// 检查登录状态
if (!isset($_SESSION['admin_id'])) {
    header('Location: login.php');
    exit;
}

require_once 'db_config.php';
require_once 'admin_layout.php';
require_once 'ip_cache.php';

$user_id = intval($_GET['id'] ?? 0);
if (!$user_id) {
    header('Location: user_management.php');
    exit;
}

try {
    $pdo = getDbConnection();

    // 获取用户基本信息
    $stmt = $pdo->prepare("
        SELECT
            u.*,
            rv.verification_status,
            rv.real_name,
            rv.id_card_number,
            rv.id_card_front_url,
            rv.id_card_back_url,
            rv.verification_reason,
            rv.submitted_at,
            rv.verified_at,
            au.name as verified_by_name,
            au.employee_id as verified_by_employee_id
        FROM users u
        LEFT JOIN realname_verification rv ON u.id = rv.user_id
        LEFT JOIN admin_users au ON rv.verified_by = au.id
        WHERE u.id = ?
    ");
    $stmt->execute([$user_id]);
    $user = $stmt->fetch(PDO::FETCH_ASSOC);

    if (!$user) {
        header('Location: user_management.php');
        exit;
    }

    // 获取登录日志
    $stmt = $pdo->prepare("
        SELECT * FROM login_logs
        WHERE user_id = ?
        ORDER BY login_time DESC
        LIMIT 10
    ");
    $stmt->execute([$user_id]);
    $login_logs = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // 获取最后登录信息
    $last_login = !empty($login_logs) ? $login_logs[0] : null;

    // 获取所有日志（管理日志 + 用户日志）
    $all_logs = [];

    // 获取管理日志
    $stmt = $pdo->prepare("
        SELECT
            'admin' as log_type,
            admin_name as operator_name,
            '' as employee_id,
            '' as department,
            action as type,
            reason as content,
            created_at as log_time
        FROM admin_logs
        WHERE target_user_id = ?
        ORDER BY created_at DESC
    ");
    $stmt->execute([$user_id]);
    $admin_logs = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // 调试信息 - 临时添加
    error_log("用户ID: $user_id, 管理日志数量: " . count($admin_logs));

    // 获取用户日志
    try {
        $stmt = $pdo->prepare("
            SELECT
                'custom' as log_type,
                operator_name,
                employee_id,
                department,
                type,
                content,
                created_at as log_time
            FROM user_logs
            WHERE user_id = ?
            ORDER BY created_at DESC
        ");
        $stmt->execute([$user_id]);
        $user_logs = $stmt->fetchAll(PDO::FETCH_ASSOC);

        // 合并日志
        $all_logs = array_merge($admin_logs, $user_logs);
    } catch (PDOException $e) {
        // user_logs表不存在，只使用管理日志
        $all_logs = $admin_logs;
    }

    // 按时间排序
    usort($all_logs, function($a, $b) {
        return strtotime($b['log_time']) - strtotime($a['log_time']);
    });

    // 限制显示数量
    $all_logs = array_slice($all_logs, 0, 20);

    // 获取钱包信息
    $stmt = $pdo->prepare("SELECT * FROM user_wallets WHERE user_id = ?");
    $stmt->execute([$user_id]);
    $wallet = $stmt->fetch(PDO::FETCH_ASSOC);

    // 获取作品信息
    $stmt = $pdo->prepare("
        SELECT * FROM user_works
        WHERE user_id = ? AND status != 'deleted'
        ORDER BY created_at DESC
        LIMIT 10
    ");
    $stmt->execute([$user_id]);
    $works = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // 获取组局信息（露营活动）
    $stmt = $pdo->prepare("
        SELECT * FROM camping_events
        WHERE organizer_id = ?
        ORDER BY created_at DESC
        LIMIT 10
    ");
    $stmt->execute([$user_id]);
    $events = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // 获取订单信息
    $stmt = $pdo->prepare("
        SELECT * FROM user_orders
        WHERE user_id = ?
        ORDER BY created_at DESC
        LIMIT 10
    ");
    $stmt->execute([$user_id]);
    $orders = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // 获取用户优惠券信息
    $user_coupons = [];
    try {
        $stmt = $pdo->prepare("
            SELECT
                ucc.*,
                cc.title,
                cc.description,
                cc.type,
                cc.discount_amount,
                cc.min_amount,
                cc.valid_from,
                cc.valid_until
            FROM user_camping_coupons ucc
            JOIN camping_coupons cc ON ucc.coupon_id = cc.id
            WHERE ucc.user_id = ?
            ORDER BY ucc.claimed_at DESC
            LIMIT 20
        ");
        $stmt->execute([$user_id]);
        $user_coupons = $stmt->fetchAll(PDO::FETCH_ASSOC);
    } catch (PDOException $e) {
        // 如果表不存在，忽略错误
        error_log("获取用户优惠券信息失败: " . $e->getMessage());
    }

    // 获取会员信息
    $stmt = $pdo->prepare("SELECT * FROM user_memberships WHERE user_id = ?");
    $stmt->execute([$user_id]);
    $membership = $stmt->fetch(PDO::FETCH_ASSOC);

    // 获取禁言记录
    $stmt = $pdo->prepare("
        SELECT * FROM user_mutes
        WHERE user_id = ? AND status = 'active' AND end_time > NOW()
        ORDER BY created_at DESC
    ");
    $stmt->execute([$user_id]);
    $active_mutes = $stmt->fetchAll(PDO::FETCH_ASSOC);

} catch (PDOException $e) {
    $error_message = '数据库错误：' . $e->getMessage();
}

// 敏感信息掩码函数
function maskName($name) {
    if (empty($name) || $name === '-') return $name;
    $length = mb_strlen($name, 'UTF-8');
    if ($length <= 1) return $name;
    if ($length == 2) return mb_substr($name, 0, 1, 'UTF-8') . '*';
    return mb_substr($name, 0, 1, 'UTF-8') . str_repeat('*', $length - 2) . mb_substr($name, -1, 1, 'UTF-8');
}

function maskIdCard($idCard) {
    if (empty($idCard) || $idCard === '-') return $idCard;
    $length = strlen($idCard);
    if ($length <= 8) return str_repeat('*', $length);
    return substr($idCard, 0, 4) . str_repeat('*', $length - 8) . substr($idCard, -4);
}

function maskPhone($phone) {
    if (empty($phone) || $phone === '-') return $phone;
    $length = strlen($phone);
    if ($length <= 7) return str_repeat('*', $length);
    return substr($phone, 0, 3) . str_repeat('*', $length - 7) . substr($phone, -4);
}

// 辅助函数
function parseUserAgent($userAgent) {
    $device = '未知设备';
    $os = '未知系统';
    $browser = '未知浏览器';

    if (empty($userAgent)) {
        return ['device' => $device, 'os' => $os, 'browser' => $browser];
    }

    // 检测操作系统
    if (preg_match('/Windows NT/i', $userAgent)) {
        $os = 'Windows';
        $device = 'PC';
    } elseif (preg_match('/Mac OS X/i', $userAgent)) {
        $os = 'macOS';
        $device = 'Mac';
    } elseif (preg_match('/iPhone/i', $userAgent)) {
        $os = 'iOS';
        $device = 'iPhone';
    } elseif (preg_match('/iPad/i', $userAgent)) {
        $os = 'iOS';
        $device = 'iPad';
    } elseif (preg_match('/Android/i', $userAgent)) {
        $os = 'Android';
        $device = '安卓设备';
    } elseif (preg_match('/Linux/i', $userAgent)) {
        $os = 'Linux';
        $device = 'PC';
    }

    // 检测浏览器
    if (preg_match('/Chrome/i', $userAgent)) {
        $browser = 'Chrome';
    } elseif (preg_match('/Firefox/i', $userAgent)) {
        $browser = 'Firefox';
    } elseif (preg_match('/Safari/i', $userAgent)) {
        $browser = 'Safari';
    } elseif (preg_match('/Edge/i', $userAgent)) {
        $browser = 'Edge';
    }

    return ['device' => $device, 'os' => $os, 'browser' => $browser];
}

function getDeviceIcon($userAgent) {
    if (preg_match('/iPhone|iPad/i', $userAgent)) {
        return 'mobile-alt';
    } elseif (preg_match('/Android/i', $userAgent)) {
        return 'mobile-alt';
    } elseif (preg_match('/Mac/i', $userAgent)) {
        return 'laptop';
    } else {
        return 'desktop';
    }
}

function getActionIcon($action) {
    $icons = [
        '封号' => 'ban',
        '解封' => 'unlock',
        '禁言' => 'volume-mute',
        '封IP' => 'globe',
        '警告' => 'exclamation-triangle'
    ];

    return $icons[$action] ?? 'cog';
}

// 状态标签函数
function getUserStatusBadge($status) {
    switch ($status) {
        case 'active':
            return '<span class="badge success">正常</span>';
        case 'banned':
            return '<span class="badge danger">已封号</span>';
        case 'inactive':
            return '<span class="badge secondary">未激活</span>';
        default:
            return '<span class="badge secondary">未知</span>';
    }
}

function getVerificationBadge($status) {
    switch ($status) {
        case 'approved':
            return '<span class="badge success"><i class="fas fa-check-circle"></i> 已认证</span>';
        case 'pending':
            return '<span class="badge warning"><i class="fas fa-clock"></i> 审核中</span>';
        case 'rejected':
            return '<span class="badge danger"><i class="fas fa-times-circle"></i> 已拒绝</span>';
        default:
            return '<span class="badge secondary">未认证</span>';
    }
}

function getGenderText($gender) {
    switch ($gender) {
        case 'male':
            return '男';
        case 'female':
            return '女';
        case 'other':
            return '其他';
        default:
            return '未设置';
    }
}

function getMembershipLevelText($level) {
    switch ($level) {
        case 'normal':
            return '普通用户';
        case 'vip':
            return 'VIP会员';
        case 'svip':
            return 'SVIP会员';
        default:
            return '未知';
    }
}

// IP地址解析功能已移至ip_cache.php

// 身份证信息解析函数
function parseIdCard($id_card) {
    if (strlen($id_card) !== 18) {
        return ['region' => '未知', 'gender' => '未知', 'age' => '未知'];
    }

    // 地区代码映射（简化版）
    $regions = [
        '11' => '北京市', '12' => '天津市', '13' => '河北省', '14' => '山西省',
        '15' => '内蒙古', '21' => '辽宁省', '22' => '吉林省', '23' => '黑龙江',
        '31' => '上海市', '32' => '江苏省', '33' => '浙江省', '34' => '安徽省',
        '35' => '福建省', '36' => '江西省', '37' => '山东省', '41' => '河南省',
        '42' => '湖北省', '43' => '湖南省', '44' => '广东省', '45' => '广西',
        '46' => '海南省', '50' => '重庆市', '51' => '四川省', '52' => '贵州省',
        '53' => '云南省', '54' => '西藏', '61' => '陕西省', '62' => '甘肃省',
        '63' => '青海省', '64' => '宁夏', '65' => '新疆', '71' => '台湾',
        '81' => '香港', '82' => '澳门'
    ];

    $region_code = substr($id_card, 0, 2);
    $region = $regions[$region_code] ?? '未知地区';

    // 性别判断（倒数第二位）
    $gender_code = intval(substr($id_card, -2, 1));
    $gender = ($gender_code % 2 === 0) ? '女' : '男';

    // 年龄计算
    $birth_year = substr($id_card, 6, 4);
    $birth_month = substr($id_card, 10, 2);
    $birth_day = substr($id_card, 12, 2);
    $birth_date = $birth_year . '-' . $birth_month . '-' . $birth_day;

    $today = new DateTime();
    $birth = new DateTime($birth_date);
    $age = $today->diff($birth)->y;

    return [
        'region' => $region,
        'gender' => $gender,
        'age' => $age,
        'birth_date' => $birth_date
    ];
}

// 解析身份证信息
$id_card_info = [];
if (!empty($user['id_card_number'])) {
    $id_card_info = parseIdCard($user['id_card_number']);
}

// 处理管理操作
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';

    // 处理用户信息编辑
    if ($action === 'edit_user') {
        try {
            $update_fields = [];
            $update_params = [];

            // 检查并更新各个字段
            if (isset($_POST['username']) && !empty(trim($_POST['username']))) {
                $update_fields[] = "username = ?";
                $update_params[] = trim($_POST['username']);
            }

            if (isset($_POST['email'])) {
                $update_fields[] = "email = ?";
                $update_params[] = trim($_POST['email']) ?: null;
            }

            if (isset($_POST['phone'])) {
                $update_fields[] = "phone = ?";
                $update_params[] = trim($_POST['phone']) ?: null;
            }

            if (isset($_POST['quwanplanet_id']) && !empty(trim($_POST['quwanplanet_id']))) {
                $update_fields[] = "quwanplanet_id = ?";
                $update_params[] = trim($_POST['quwanplanet_id']);
            }

            if (isset($_POST['region'])) {
                $update_fields[] = "region = ?";
                $update_params[] = trim($_POST['region']) ?: null;
            }

            if (isset($_POST['bio'])) {
                $update_fields[] = "bio = ?";
                $update_params[] = trim($_POST['bio']) ?: null;
            }

            if (!empty($update_fields)) {
                $update_params[] = $user_id;
                $sql = "UPDATE users SET " . implode(", ", $update_fields) . " WHERE id = ?";
                $stmt = $pdo->prepare($sql);
                $stmt->execute($update_params);

                // 记录管理日志
                $stmt = $pdo->prepare("
                    INSERT INTO admin_logs (admin_id, admin_name, target_user_id, action, reason, created_at)
                    VALUES (?, ?, ?, ?, ?, NOW())
                ");
                $stmt->execute([
                    $_SESSION['admin_id'],
                    $_SESSION['admin_name'],
                    $user_id,
                    '编辑用户信息',
                    '管理员修改了用户信息'
                ]);

                $_SESSION['success_message'] = '用户信息更新成功！';
                header('Location: user_detail.php?id=' . $user_id);
                exit;
            } else {
                $_SESSION['error_message'] = '没有需要更新的字段';
            }
        } catch (PDOException $e) {
            $_SESSION['error_message'] = '更新用户信息失败：' . $e->getMessage();
        }
    }
    // 处理其他管理操作
    else {
        $reason = trim($_POST['reason'] ?? '');

        if (empty($reason)) {
            $error_message = '请填写操作原因';
        } else {
            try {
                $admin_id = $_SESSION['admin_id'];
                $admin_name = $_SESSION['admin_name'];

                switch ($action) {
                    case 'ban_user':
                        $stmt = $pdo->prepare("UPDATE users SET status = 'banned' WHERE id = ?");
                        $stmt->execute([$user_id]);
                        $log_action = '封号';
                        break;

                    case 'unban_user':
                        $stmt = $pdo->prepare("UPDATE users SET status = 'active' WHERE id = ?");
                        $stmt->execute([$user_id]);
                        $log_action = '解封';
                        break;

                    case 'mute_user':
                        // 这里需要添加禁言逻辑
                        $log_action = '禁言';
                        break;

                    case 'ban_ip':
                        // 这里需要添加封IP逻辑
                        $log_action = '封IP';
                        break;
                }

                // 记录管理日志
                $stmt = $pdo->prepare("
                    INSERT INTO admin_logs (admin_id, admin_name, target_user_id, action, reason, created_at)
                    VALUES (?, ?, ?, ?, ?, NOW())
                ");
                $stmt->execute([$admin_id, $admin_name, $user_id, $log_action, $reason]);

                $success_message = '操作成功';

                // 刷新页面数据
                header('Location: ' . $_SERVER['PHP_SELF'] . '?id=' . $user_id);
                exit;

            } catch (PDOException $e) {
                $error_message = '操作失败：' . $e->getMessage();
            }
        }
    }
}

// 构建页面内容
$content = '';

// 显示操作结果消息
if (isset($_GET['action_success'])) {
    $content .= '
    <div class="alert alert-success">
        <i class="fas fa-check-circle"></i>
        ' . htmlspecialchars($_GET['action_success']) . '操作成功！
    </div>';
}

if (isset($_GET['log_added'])) {
    $content .= '
    <div class="alert alert-success">
        <i class="fas fa-check-circle"></i>
        日志添加成功！
    </div>';
}



// 显示消息（只在有消息时显示，并立即清除session）
$show_success = isset($_SESSION['success_message']);
$show_error = isset($_SESSION['error_message']);
$success_msg = $show_success ? $_SESSION['success_message'] : '';
$error_msg = $show_error ? $_SESSION['error_message'] : '';

// 立即清除session消息，防止刷新后重复显示
if ($show_success) unset($_SESSION['success_message']);
if ($show_error) unset($_SESSION['error_message']);

if ($show_success) {
    $content .= '
    <div class="alert alert-success alert-dismissible" id="successAlert">
        <i class="fas fa-check-circle"></i>
        ' . htmlspecialchars($success_msg) . '
        <button type="button" class="alert-close" onclick="closeAlert(\'successAlert\')">&times;</button>
    </div>';
}

if ($show_error) {
    $content .= '
    <div class="alert alert-danger alert-dismissible" id="errorAlert">
        <i class="fas fa-exclamation-circle"></i>
        ' . htmlspecialchars($error_msg) . '
        <button type="button" class="alert-close" onclick="closeAlert(\'errorAlert\')">&times;</button>
    </div>';
}



if (isset($_GET['error'])) {
    $content .= '
    <div class="alert alert-danger">
        <i class="fas fa-exclamation-triangle"></i>
        ' . htmlspecialchars($_GET['error']) . '
    </div>';
}

$content .= '
<div class="user-detail-container">
    <!-- 用户基本信息 -->
    <div class="section-card">
        <div class="card-header">
            <h3><i class="fas fa-user"></i> 基本信息</h3>
            <div class="header-actions">
                <div class="user-status">
                    ' . getUserStatusBadge($user['status']) . '
                </div>
                <button class="edit-btn" data-section="basic" title="编辑基本信息">
                    <i class="fas fa-pencil-alt"></i>
                </button>
            </div>
        </div>
        <div class="card-body">
            <div class="user-profile">
                <div class="user-avatar-large">
                    <img src="' . htmlspecialchars($user['avatar'] ?? 'https://via.placeholder.com/120') . '"
                         alt="用户头像" class="avatar-img">
                </div>
                <div class="user-basic-info">
                    <h2>' . htmlspecialchars($user['username']) . '</h2>
                    <div class="user-meta">
                        <div class="meta-item">
                            <span class="label">趣玩ID：</span>
                            <span class="value highlight">' . htmlspecialchars($user['quwanplanet_id']) . '</span>
                        </div>
                        <div class="meta-item">
                            <span class="label">性别：</span>
                            <span class="value">' . getGenderText($user['gender']) . '</span>
                        </div>
                        <div class="meta-item">
                            <span class="label">地区：</span>
                            <span class="value">' . htmlspecialchars($user['region'] ?? '未设置') . '</span>
                        </div>
                        <div class="meta-item">
                            <span class="label">个人简介：</span>
                            <span class="value">' . htmlspecialchars($user['bio'] ?? '暂无简介') . '</span>
                        </div>
                        <div class="meta-item">
                            <span class="label">注册时间：</span>
                            <span class="value">' . date('Y-m-d H:i:s', strtotime($user['created_at'])) . '</span>
                        </div>
                    </div>
                </div>
            </div>

            <div class="info-grid">
                <div class="info-section">
                    <h4>联系信息</h4>
                    <div class="info-list">
                        <div class="info-item">
                            <span class="label">手机号：</span>
                            <span class="value sensitive-info" data-type="phone" data-value="' . htmlspecialchars($user['phone'] ?? '-') . '">
                                <span class="masked-text">' . maskPhone($user['phone'] ?? '-') . '</span>
                                <button class="btn-toggle-sensitive" onclick="toggleSensitiveInfo(this, \'phone\')" title="查看完整信息">
                                    <i class="fas fa-eye"></i>
                                </button>
                            </span>
                        </div>
                        <div class="info-item">
                            <span class="label">邮箱：</span>
                            <span class="value">' . htmlspecialchars($user['email'] ?? '-') . '</span>
                        </div>
                    </div>
                </div>

                <div class="info-section">
                    <h4>登录信息</h4>
                    <div class="info-list">
                        <div class="info-item">
                            <span class="label">最后登录：</span>
                            <span class="value">' . ($user['last_login'] ? date('Y-m-d H:i:s', strtotime($user['last_login'])) : '从未登录') . '</span>
                        </div>
                        <div class="info-item">
                            <span class="label">最后登录IP：</span>
                            <span class="value">' . ($last_login ? htmlspecialchars($last_login['ip_address']) : '-') . '</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>';

// 实名认证信息
if ($user['verification_status']) {
    $content .= '
    <div class="section-card">
        <div class="card-header">
            <h3><i class="fas fa-id-card"></i> 实名认证信息</h3>
            ' . getVerificationBadge($user['verification_status']) . '
        </div>
        <div class="card-body">
            <div class="verification-info">
                <div class="info-grid">
                    <div class="info-section">
                        <h4>认证信息</h4>
                        <div class="info-list">
                            <div class="info-item">
                                <span class="label">真实姓名：</span>
                                <span class="value sensitive-info" data-type="name" data-value="' . htmlspecialchars($user['real_name'] ?? '-') . '">
                                    <span class="masked-text">' . maskName($user['real_name'] ?? '-') . '</span>
                                    <button class="btn-toggle-sensitive" onclick="toggleSensitiveInfo(this, \'real_name\')" title="查看完整信息">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                </span>
                            </div>
                            <div class="info-item">
                                <span class="label">身份证号：</span>
                                <span class="value sensitive-info" data-type="id_card" data-value="' . htmlspecialchars($user['id_card_number'] ?? '-') . '">
                                    <span class="masked-text">' . maskIdCard($user['id_card_number'] ?? '-') . '</span>
                                    <button class="btn-toggle-sensitive" onclick="toggleSensitiveInfo(this, \'id_card\')" title="查看完整信息">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                </span>
                            </div>
                            <div class="info-item">
                                <span class="label">提交时间：</span>
                                <span class="value">' . ($user['submitted_at'] ? date('Y-m-d H:i:s', strtotime($user['submitted_at'])) : '-') . '</span>
                            </div>
                        </div>
                    </div>';

    if (!empty($id_card_info)) {
        $content .= '
                    <div class="info-section">
                        <h4>身份证解析信息</h4>
                        <div class="info-list">
                            <div class="info-item">
                                <span class="label">身份证归属地：</span>
                                <span class="value">' . $id_card_info['region'] . '</span>
                            </div>
                            <div class="info-item">
                                <span class="label">性别：</span>
                                <span class="value">' . $id_card_info['gender'] . '</span>
                            </div>
                            <div class="info-item">
                                <span class="label">年龄：</span>
                                <span class="value">' . $id_card_info['age'] . '岁</span>
                            </div>
                            <div class="info-item">
                                <span class="label">出生日期：</span>
                                <span class="value">' . $id_card_info['birth_date'] . '</span>
                            </div>
                        </div>
                    </div>';
    }

    $content .= '
                </div>';

    // 身份证照片
    if ($user['id_card_front_url'] || $user['id_card_back_url']) {
        $content .= '
                <div class="id-card-photos">
                    <h4>身份证照片</h4>
                    <div class="photo-grid">';

        if ($user['id_card_front_url']) {
            $content .= '
                        <div class="photo-item">
                            <label>正面</label>
                            <img src="' . htmlspecialchars($user['id_card_front_url']) . '" alt="身份证正面" class="id-card-img">
                        </div>';
        }

        if ($user['id_card_back_url']) {
            $content .= '
                        <div class="photo-item">
                            <label>反面</label>
                            <img src="' . htmlspecialchars($user['id_card_back_url']) . '" alt="身份证反面" class="id-card-img">
                        </div>';
        }

        $content .= '
                    </div>
                </div>';
    }

    $content .= '
            </div>
        </div>
    </div>';
}

// 钱包信息
$content .= '
<div class="section-card">
    <div class="card-header">
        <h3><i class="fas fa-wallet"></i> 钱包信息</h3>
    </div>
    <div class="card-body">
        <div class="wallet-info">';

if ($wallet) {
    $content .= '
            <div class="wallet-stats">
                <div class="wallet-stat-item">
                    <div class="stat-icon primary">
                        <i class="fas fa-coins"></i>
                    </div>
                    <div class="stat-details">
                        <div class="stat-value">¥' . number_format($wallet['balance'], 2) . '</div>
                        <div class="stat-label">可用余额</div>
                    </div>
                </div>

                <div class="wallet-stat-item">
                    <div class="stat-icon warning">
                        <i class="fas fa-lock"></i>
                    </div>
                    <div class="stat-details">
                        <div class="stat-value">¥' . number_format($wallet['frozen_balance'], 2) . '</div>
                        <div class="stat-label">冻结金额</div>
                    </div>
                </div>

                <div class="wallet-stat-item">
                    <div class="stat-icon success">
                        <i class="fas fa-arrow-up"></i>
                    </div>
                    <div class="stat-details">
                        <div class="stat-value">¥' . number_format($wallet['total_recharge'], 2) . '</div>
                        <div class="stat-label">累计充值</div>
                    </div>
                </div>

                <div class="wallet-stat-item">
                    <div class="stat-icon danger">
                        <i class="fas fa-arrow-down"></i>
                    </div>
                    <div class="stat-details">
                        <div class="stat-value">¥' . number_format($wallet['total_withdraw'], 2) . '</div>
                        <div class="stat-label">累计提现</div>
                    </div>
                </div>
            </div>';
} else {
    $content .= '
            <div class="empty-state">
                <i class="fas fa-wallet"></i>
                <p>暂无钱包信息</p>
            </div>';
}

$content .= '
        </div>
    </div>
</div>';

// 会员信息
$content .= '
<div class="section-card">
    <div class="card-header">
        <h3><i class="fas fa-crown"></i> 会员信息</h3>
        <button class="btn-edit" onclick="openEditModal(\'membership\', \'会员信息\')" title="编辑会员信息">
            <i class="fas fa-pencil-alt"></i>
        </button>
    </div>
    <div class="card-body">
        <div class="membership-info">';

if ($membership) {
    $level_badges = [
        'normal' => '<span class="badge secondary">普通用户</span>',
        'vip' => '<span class="badge warning">VIP会员</span>',
        'svip' => '<span class="badge danger">SVIP会员</span>'
    ];

    $is_expired = $membership['expire_time'] && strtotime($membership['expire_time']) < time();

    $content .= '
            <div class="membership-card">
                <div class="membership-level">
                    ' . $level_badges[$membership['level']] . '
                    ' . ($is_expired ? '<span class="badge secondary">已过期</span>' : '') . '
                </div>
                <div class="membership-details">
                    <div class="detail-item">
                        <span class="label">会员等级：</span>
                        <span class="value">' . getMembershipLevelText($membership['level']) . '</span>
                    </div>
                    <div class="detail-item">
                        <span class="label">到期时间：</span>
                        <span class="value">' . ($membership['expire_time'] ? date('Y-m-d H:i:s', strtotime($membership['expire_time'])) : '永久') . '</span>
                    </div>
                    <div class="detail-item">
                        <span class="label">累计天数：</span>
                        <span class="value">' . $membership['total_days'] . ' 天</span>
                    </div>
                </div>
            </div>';
} else {
    $content .= '
            <div class="empty-state">
                <i class="fas fa-crown"></i>
                <p>暂无会员信息</p>
            </div>';
}

$content .= '
        </div>
    </div>
</div>';

// 作品信息
$content .= '
<div class="section-card">
    <div class="card-header">
        <h3><i class="fas fa-images"></i> 作品信息</h3>
        <span class="count-badge">' . (is_array($works) ? count($works) : 0) . ' 个作品</span>
    </div>
    <div class="card-body">
        <div class="works-info">';

if (!empty($works)) {
    $content .= '
            <div class="works-list">';

    foreach (array_slice($works, 0, 5) as $work) {
        $type_icons = [
            'text' => 'file-text',
            'image' => 'image',
            'video' => 'video'
        ];

        $content .= '
                <div class="work-item">
                    <div class="work-icon">
                        <i class="fas fa-' . $type_icons[$work['type']] . '"></i>
                    </div>
                    <div class="work-content">
                        <div class="work-title">' . htmlspecialchars($work['title']) . '</div>
                        <div class="work-stats">
                            <span><i class="fas fa-heart"></i> ' . $work['likes_count'] . '</span>
                            <span><i class="fas fa-eye"></i> ' . $work['views_count'] . '</span>
                            <span><i class="fas fa-comment"></i> ' . $work['comments_count'] . '</span>
                        </div>
                    </div>
                    <div class="work-time">' . date('Y-m-d', strtotime($work['created_at'])) . '</div>
                </div>';
    }

    $content .= '
            </div>';
} else {
    $content .= '
            <div class="empty-state">
                <i class="fas fa-images"></i>
                <p>暂无作品</p>
            </div>';
}

$content .= '
        </div>
    </div>
</div>';

// 组局信息
$content .= '
<div class="section-card">
    <div class="card-header">
        <h3><i class="fas fa-users"></i> 组局信息</h3>
        <span class="count-badge">' . (is_array($events) ? count($events) : 0) . ' 个组局</span>
    </div>
    <div class="card-body">
        <div class="events-info">';

if (!empty($events)) {
    $content .= '
            <div class="events-list">';

    foreach (array_slice($events, 0, 5) as $event) {
        $status_badges = [
            'draft' => '<span class="badge secondary">草稿</span>',
            'recruiting' => '<span class="badge primary">招募中</span>',
            'full' => '<span class="badge warning">已满员</span>',
            'ongoing' => '<span class="badge info">进行中</span>',
            'completed' => '<span class="badge success">已完成</span>',
            'cancelled' => '<span class="badge danger">已取消</span>'
        ];

        $category_names = [
            'mountain' => '山地露营',
            'lake' => '湖边露营',
            'forest' => '森林露营',
            'beach' => '海边露营'
        ];

        $category_name = isset($category_names[$event['category']]) ? $category_names[$event['category']] : '露营活动';

        $content .= '
                <div class="event-item">
                    <div class="event-content">
                        <div class="event-title">' . htmlspecialchars($event['title']) . '</div>
                        <div class="event-category">' . $category_name . '</div>
                        <div class="event-details">
                            <span><i class="fas fa-clock"></i> ' . date('Y-m-d H:i', strtotime($event['start_time'])) . '</span>
                            <span><i class="fas fa-map-marker-alt"></i> ' . htmlspecialchars($event['location']) . '</span>
                            <span><i class="fas fa-users"></i> ' . $event['current_participants'] . '/' . $event['max_participants'] . '</span>
                            <span><i class="fas fa-yen-sign"></i> ¥' . number_format($event['price'], 2) . '</span>
                        </div>
                    </div>
                    <div class="event-status">' . $status_badges[$event['status']] . '</div>
                </div>';
    }

    $content .= '
            </div>';
} else {
    $content .= '
            <div class="empty-state">
                <i class="fas fa-users"></i>
                <p>暂无组局</p>
            </div>';
}

$content .= '
        </div>
    </div>
</div>';

// 优惠券信息
$content .= '
<div class="section-card">
    <div class="card-header">
        <h3><i class="fas fa-ticket-alt"></i> 优惠券信息</h3>
        <span class="count-badge">' . count($user_coupons) . ' 张优惠券</span>
    </div>
    <div class="card-body">
        <div class="coupons-info">';

if (!empty($user_coupons)) {
    // 统计优惠券状态
    $coupon_stats = [
        'claimed' => 0,
        'used' => 0,
        'expired' => 0
    ];

    $now = new DateTime();
    foreach ($user_coupons as $coupon) {
        if ($coupon['status'] === 'used') {
            $coupon_stats['used']++;
        } elseif ($coupon['status'] === 'expired' || $now > new DateTime($coupon['valid_until'])) {
            $coupon_stats['expired']++;
        } else {
            $coupon_stats['claimed']++;
        }
    }

    $content .= '
            <div class="coupon-stats">
                <div class="stat-item">
                    <div class="stat-number">' . $coupon_stats['claimed'] . '</div>
                    <div class="stat-label">可用</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">' . $coupon_stats['used'] . '</div>
                    <div class="stat-label">已使用</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">' . $coupon_stats['expired'] . '</div>
                    <div class="stat-label">已过期</div>
                </div>
            </div>

            <div class="coupons-list">';

    $type_names = [
        'join_discount' => '参加活动',
        'organize_discount' => '组局活动',
        'newbie_discount' => '新人专享'
    ];

    foreach (array_slice($user_coupons, 0, 10) as $coupon) {
        $status_class = '';
        $status_text = '';
        $now = new DateTime();
        $valid_until = new DateTime($coupon['valid_until']);

        if ($coupon['status'] === 'used') {
            $status_class = 'used';
            $status_text = '已使用';
        } elseif ($coupon['status'] === 'expired' || $now > $valid_until) {
            $status_class = 'expired';
            $status_text = '已过期';
        } else {
            $status_class = 'available';
            $status_text = '可使用';
        }

        $type_name = isset($type_names[$coupon['type']]) ? $type_names[$coupon['type']] : '未知类型';

        $content .= '
                <div class="coupon-item ' . $status_class . '">
                    <div class="coupon-content">
                        <div class="coupon-header">
                            <div class="coupon-title">' . htmlspecialchars($coupon['title']) . '</div>
                            <div class="coupon-amount">¥' . number_format($coupon['discount_amount'], 0) . '</div>
                        </div>
                        <div class="coupon-details">
                            <span class="coupon-type">' . $type_name . '</span>
                            <span class="coupon-condition">满¥' . number_format($coupon['min_amount'], 0) . '可用</span>
                        </div>
                        <div class="coupon-dates">
                            <span><i class="fas fa-calendar-plus"></i> 领取：' . date('Y-m-d H:i', strtotime($coupon['claimed_at'])) . '</span>
                            <span><i class="fas fa-calendar-times"></i> 到期：' . date('Y-m-d H:i', strtotime($coupon['valid_until'])) . '</span>';

        if ($coupon['used_at']) {
            $content .= '<span><i class="fas fa-check-circle"></i> 使用：' . date('Y-m-d H:i', strtotime($coupon['used_at'])) . '</span>';
        }

        $content .= '
                        </div>
                    </div>
                    <div class="coupon-status">
                        <span class="status-badge ' . $status_class . '">' . $status_text . '</span>
                    </div>
                </div>';
    }

    $content .= '
            </div>';
} else {
    $content .= '
            <div class="empty-state">
                <i class="fas fa-ticket-alt"></i>
                <p>暂无优惠券</p>
            </div>';
}

$content .= '
        </div>
    </div>
</div>';

// 订单信息
$content .= '
<div class="section-card">
    <div class="card-header">
        <h3><i class="fas fa-shopping-cart"></i> 订单信息</h3>
        <span class="count-badge">' . (is_array($orders) ? count($orders) : 0) . ' 个订单</span>
    </div>
    <div class="card-body">
        <div class="orders-info">';

if (!empty($orders)) {
    $content .= '
            <div class="orders-list">';

    foreach (array_slice($orders, 0, 5) as $order) {
        $status_badges = [
            'pending' => '<span class="badge warning">待支付</span>',
            'paid' => '<span class="badge success">已支付</span>',
            'cancelled' => '<span class="badge danger">已取消</span>',
            'refunded' => '<span class="badge secondary">已退款</span>'
        ];

        $type_names = [
            'recharge' => '充值',
            'service' => '服务',
            'product' => '商品'
        ];

        $content .= '
                <div class="order-item">
                    <div class="order-content">
                        <div class="order-no">' . htmlspecialchars($order['order_no']) . '</div>
                        <div class="order-details">
                            <span>类型：' . $type_names[$order['type']] . '</span>
                            <span>金额：¥' . number_format($order['amount'], 2) . '</span>
                            <span>时间：' . date('Y-m-d H:i', strtotime($order['created_at'])) . '</span>
                        </div>
                    </div>
                    <div class="order-status">' . $status_badges[$order['status']] . '</div>
                </div>';
    }

    $content .= '
            </div>';
} else {
    $content .= '
            <div class="empty-state">
                <i class="fas fa-shopping-cart"></i>
                <p>暂无订单</p>
            </div>';
}

$content .= '
        </div>
    </div>
</div>';

// 管理操作区域
$content .= '
<div class="section-card">
    <div class="card-header">
        <h3><i class="fas fa-tools"></i> 管理操作</h3>
    </div>
    <div class="card-body">
        <div class="admin-actions">
            <div class="action-grid">
                <div class="action-group">
                    <h4>账号管理</h4>
                    <div class="action-buttons">
                        <button type="button" class="btn danger" onclick="openActionModal(\'ban_user\', \'封号\', \'ban\')"
                                ' . ($user['status'] === 'banned' ? 'disabled' : '') . '>
                            <i class="fas fa-ban"></i> 封号
                        </button>
                        <button type="button" class="btn success" onclick="openActionModal(\'unban_user\', \'解封\', \'unlock\')"
                                ' . ($user['status'] !== 'banned' ? 'disabled' : '') . '>
                            <i class="fas fa-unlock"></i> 解封
                        </button>
                        <button type="button" class="btn warning" onclick="openActionModal(\'mute_user\', \'禁言\', \'volume-mute\')">
                            <i class="fas fa-volume-mute"></i> 禁言
                        </button>
                    </div>
                </div>

                <div class="action-group">
                    <h4>IP管理</h4>
                    <div class="action-buttons">
                        <button type="button" class="btn danger" onclick="openActionModal(\'ban_ip\', \'封IP\', \'globe\')">
                            <i class="fas fa-globe"></i> 封IP
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>';

// 登录设备信息
if (!empty($login_logs)) {
    $content .= '
    <div class="section-card">
        <div class="card-header">
            <h3><i class="fas fa-devices"></i> 登录设备信息</h3>
        </div>
        <div class="card-body">
            <div class="device-info">
                <div class="current-device">
                    <h4>最近登录设备</h4>
                    <div class="device-card">
                        <div class="device-icon">
                            <i class="fas fa-' . getDeviceIcon($last_login['user_agent'] ?? '') . '"></i>
                        </div>
                        <div class="device-details">
                            <div class="device-name">' . parseUserAgent($last_login['user_agent'] ?? '')['device'] . '</div>
                            <div class="device-os">' . parseUserAgent($last_login['user_agent'] ?? '')['os'] . '</div>
                            <div class="device-browser">' . parseUserAgent($last_login['user_agent'] ?? '')['browser'] . '</div>
                            <div class="device-time">' . ($last_login ? date('Y-m-d H:i:s', strtotime($last_login['login_time'])) : '-') . '</div>
                        </div>
                        <div class="device-location">
                            <div class="location-ip">' . ($last_login['ip_address'] ?? '-') . '</div>
                            <div class="location-area">' . ($last_login ? getLocationFromIP($last_login['ip_address']) : '未知地区') . '</div>
                        </div>
                    </div>
                </div>

                <div class="login-history">
                    <h4>登录历史</h4>
                    <div class="history-list">';

    foreach (array_slice($login_logs, 0, 5) as $log) {
        $ua_info = parseUserAgent($log['user_agent'] ?? '');
        $content .= '
                        <div class="history-item">
                            <div class="history-icon">
                                <i class="fas fa-' . getDeviceIcon($log['user_agent'] ?? '') . '"></i>
                            </div>
                            <div class="history-details">
                                <div class="history-device">' . $ua_info['device'] . ' • ' . $ua_info['os'] . '</div>
                                <div class="history-time">' . date('Y-m-d H:i:s', strtotime($log['login_time'])) . '</div>
                            </div>
                            <div class="history-location">
                                <div class="history-ip">' . htmlspecialchars($log['ip_address']) . '</div>
                                <div class="history-area">' . htmlspecialchars(getLocationFromIP($log['ip_address'])) . '</div>
                            </div>
                        </div>';
    }

    $content .= '
                    </div>
                </div>
            </div>
        </div>
    </div>';
}

// 管理日志
$content .= '
<div class="section-card">
    <div class="card-header">
        <h3><i class="fas fa-clipboard-list"></i> 日志</h3>
        <div class="header-actions">
            <div class="dropdown">
                <button class="btn-export" onclick="toggleExportMenu()">
                    <i class="fas fa-download"></i>
                    导出日志
                    <i class="fas fa-chevron-down"></i>
                </button>
                <div class="dropdown-menu" id="exportMenu">
                    <a href="export_logs.php?user_id=' . $user_id . '&format=csv" class="dropdown-item">
                        <i class="fas fa-file-csv"></i> 导出为CSV
                    </a>
                    <a href="export_logs.php?user_id=' . $user_id . '&format=excel" class="dropdown-item">
                        <i class="fas fa-file-excel"></i> 导出为Excel
                    </a>
                    <a href="export_logs.php?user_id=' . $user_id . '&format=pdf" class="dropdown-item" target="_blank">
                        <i class="fas fa-file-pdf"></i> 导出为PDF
                    </a>
                </div>
            </div>
            <button class="btn-write-log" onclick="openLogModal()">
                <i class="fas fa-pencil-alt"></i> 写日志
            </button>
        </div>
    </div>
    <div class="card-body">
        <div class="admin-logs">';

if (!empty($all_logs)) {
    $content .= '
            <div class="table-responsive">
                <table class="logs-table">
                    <thead>
                        <tr>
                            <th>姓名</th>
                            <th>部门</th>
                            <th>工号</th>
                            <th>类型</th>
                            <th>日志内容</th>
                            <th>操作时间</th>
                        </tr>
                    </thead>
                    <tbody>';

    foreach ($all_logs as $log) {
        $type_badges = [
            '系统' => '<span class="badge badge-success">系统</span>',
            '完成' => '<span class="badge badge-primary">完成</span>',
            '待跟进' => '<span class="badge badge-warning">待跟进</span>',
            '小结' => '<span class="badge badge-info">小结</span>',
            '封号' => '<span class="badge badge-danger">封号</span>',
            '解封' => '<span class="badge badge-success">解封</span>',
            '禁言' => '<span class="badge badge-warning">禁言</span>',
            '封IP' => '<span class="badge badge-danger">封IP</span>',
            '查看敏感信息' => '<span class="badge badge-secondary">查看敏感信息</span>',
            '敏感信息查看' => '<span class="badge badge-secondary">敏感信息查看</span>',
            '测试操作' => '<span class="badge badge-info">测试操作</span>',
            '测试日志' => '<span class="badge badge-light">测试日志</span>'
        ];

        $badge = isset($type_badges[$log['type']]) ? $type_badges[$log['type']] : '<span class="badge badge-light">' . htmlspecialchars($log['type']) . '</span>';

        $operator_name = htmlspecialchars($log['operator_name']);
        if ($log['employee_id']) {
            $operator_name .= '（' . htmlspecialchars($log['employee_id']) . '）';
        }

        $content .= '
                        <tr>
                            <td>' . $operator_name . '</td>
                            <td>' . htmlspecialchars($log['department'] ?: '-') . '</td>
                            <td>' . htmlspecialchars($log['employee_id'] ?: '-') . '</td>
                            <td>' . $badge . '</td>
                            <td class="log-content-cell">' . htmlspecialchars($log['content']) . '</td>
                            <td>' . date('Y-m-d H:i:s', strtotime($log['log_time'])) . '</td>
                        </tr>';
    }

    $content .= '
                    </tbody>
                </table>
            </div>';
} else {
    $content .= '
            <div class="empty-state">
                <i class="fas fa-clipboard-list"></i>
                <p>暂无管理日志</p>
            </div>';
}

$content .= '
        </div>
    </div>
</div>

<!-- 写日志弹窗 -->
<div id="logModal" class="modal" style="display: none;">
    <div class="modal-content">
        <div class="modal-header">
            <h3><i class="fas fa-pencil-alt"></i> 写日志</h3>
            <button class="modal-close" onclick="closeLogModal()">&times;</button>
        </div>
        <form id="logForm" method="post" action="add_log.php">
            <input type="hidden" name="user_id" value="' . $user_id . '">
            <div class="modal-body">
                <div class="form-group">
                    <label>类型</label>
                    <select name="type" required>
                        <option value="">请选择类型</option>
                        <option value="系统">系统</option>
                        <option value="完成">完成</option>
                        <option value="待跟进">待跟进</option>
                        <option value="小结">小结</option>
                        <option value="其他">其他</option>
                    </select>
                </div>
                <div class="form-group">
                    <label>日志内容</label>
                    <textarea name="content" rows="4" placeholder="请输入日志内容..." required></textarea>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn-secondary" onclick="closeLogModal()">取消</button>
                <button type="submit" class="btn-primary">保存日志</button>
            </div>
        </form>
    </div>
</div>

<!-- 管理操作弹窗 -->
<div id="actionModal" class="modal" style="display: none;">
    <div class="modal-content">
        <div class="modal-header">
            <h3 id="actionModalTitle"><i class="fas fa-ban"></i> 操作确认</h3>
            <button class="modal-close" onclick="closeActionModal()">&times;</button>
        </div>
        <form id="actionForm" method="post" action="user_action.php">
            <input type="hidden" name="user_id" value="' . $user_id . '">
            <input type="hidden" name="action" id="actionType">
            <div class="modal-body">
                <div class="action-warning">
                    <i class="fas fa-exclamation-triangle"></i>
                    <p id="actionWarningText">此操作将对用户账户产生影响，请谨慎操作。</p>
                </div>

                <!-- 时效性选择 -->
                <div class="form-group" id="durationGroup" style="display: none;">
                    <label>操作时长</label>
                    <select name="duration" id="durationSelect">
                        <option value="1h">1小时</option>
                        <option value="6h">6小时</option>
                        <option value="12h">12小时</option>
                        <option value="1d">1天</option>
                        <option value="3d">3天</option>
                        <option value="7d">7天</option>
                        <option value="30d">30天</option>
                        <option value="permanent">永久</option>
                    </select>
                </div>

                <div class="form-group">
                    <label>操作原因</label>
                    <textarea name="reason" rows="4" placeholder="请详细说明操作原因..." required></textarea>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn-secondary" onclick="closeActionModal()">取消</button>
                <button type="submit" class="btn-danger" id="actionConfirmBtn">确认操作</button>
            </div>
        </form>
    </div>
</div>

</div>

';

// 输出页面
echo renderAdminLayout('用户详情 - ' . htmlspecialchars($user['username']), $content, 'user_management');
?>

<script>
function confirmAction(form) {
    const action = form.querySelector("button[type=submit]:focus").value;
    const reason = form.reason.value.trim();

    if (!reason) {
        alert("请填写操作原因");
        return false;
    }

    const actionNames = {
        "ban_user": "封号",
        "unban_user": "解封",
        "mute_user": "禁言",
        "ban_ip": "封IP"
    };

    return confirm(`确定要${actionNames[action]}该用户吗？\\n原因：${reason}`);
}

// 点击提交按钮时设置焦点
document.querySelectorAll(".action-buttons button").forEach(btn => {
    btn.addEventListener("click", function() {
        this.focus();
    });
});

// 日志弹窗功能
function openLogModal() {
    document.getElementById("logModal").style.display = "flex";
}

function closeLogModal() {
    document.getElementById("logModal").style.display = "none";
    document.getElementById("logForm").reset();
}

// 点击弹窗外部关闭
document.getElementById("logModal").addEventListener("click", function(e) {
    if (e.target === this) {
        closeLogModal();
    }
});

// 管理操作弹窗功能
function openActionModal(action, actionName, icon) {
    const modal = document.getElementById("actionModal");
    const title = document.getElementById("actionModalTitle");
    const actionType = document.getElementById("actionType");
    const warningText = document.getElementById("actionWarningText");
    const confirmBtn = document.getElementById("actionConfirmBtn");
    const durationGroup = document.getElementById("durationGroup");

    // 设置弹窗内容
    title.innerHTML = `<i class="fas fa-${icon}"></i> ${actionName}确认`;
    actionType.value = action;
    confirmBtn.textContent = `确认${actionName}`;

    // 设置警告文本
    const warnings = {
        "ban_user": "封号后用户将无法登录和使用平台功能，请谨慎操作。",
        "unban_user": "解封后用户将恢复正常使用权限。",
        "mute_user": "禁言后用户将无法发送消息和评论。",
        "ban_ip": "封IP后该IP地址将无法访问平台。"
    };

    warningText.textContent = warnings[action] || "此操作将对用户账户产生影响，请谨慎操作。";

    // 显示或隐藏时效性选择
    if (action === "ban_user" || action === "mute_user" || action === "ban_ip") {
        durationGroup.style.display = "block";
    } else {
        durationGroup.style.display = "none";
    }

    // 设置按钮样式
    confirmBtn.className = action === "unban_user" ? "btn-success" : "btn-danger";

    modal.style.display = "flex";
}

function closeActionModal() {
    document.getElementById("actionModal").style.display = "none";
    document.getElementById("actionForm").reset();
}

// 点击弹窗外部关闭
document.getElementById("actionModal").addEventListener("click", function(e) {
    if (e.target === this) {
        closeActionModal();
    }
});

// 敏感信息显隐功能
function toggleSensitiveInfo(button, type) {
    const sensitiveInfo = button.closest('.sensitive-info');
    const maskedText = sensitiveInfo.querySelector('.masked-text');
    const icon = button.querySelector('i');
    const fullValue = sensitiveInfo.getAttribute('data-value');

    if (icon.classList.contains('fa-eye')) {
        // 显示完整信息
        maskedText.textContent = fullValue;
        icon.classList.remove('fa-eye');
        icon.classList.add('fa-eye-slash');
        button.title = '隐藏信息';

        // 记录查看敏感信息的日志
        logSensitiveView(type);
    } else {
        // 隐藏信息
        const dataType = sensitiveInfo.getAttribute('data-type');
        let maskedValue = fullValue;

        switch(dataType) {
            case 'name':
                maskedValue = maskName(fullValue);
                break;
            case 'id_card':
                maskedValue = maskIdCard(fullValue);
                break;
            case 'phone':
                maskedValue = maskPhone(fullValue);
                break;
        }

        maskedText.textContent = maskedValue;
        icon.classList.remove('fa-eye-slash');
        icon.classList.add('fa-eye');
        button.title = '查看完整信息';
    }
}

// 记录敏感信息查看日志
function logSensitiveView(type) {
    const typeNames = {
        "real_name": "真实姓名",
        "id_card": "身份证号",
        "phone": "手机号"
    };

    fetch("log_sensitive_view.php", {
        method: "POST",
        headers: {
            "Content-Type": "application/json",
        },
        body: JSON.stringify({
            user_id: <?php echo intval($user_id); ?>,
            type: type,
            type_name: typeNames[type] || type
        })
    }).catch(error => {
        console.error("日志记录失败:", error);
    });
}

// JavaScript掩码函数
function maskName(name) {
    if (!name || name === "-") return name;
    const length = name.length;
    if (length <= 1) return name;
    if (length === 2) return name[0] + "*";
    return name[0] + "*".repeat(length - 2) + name[length - 1];
}

function maskIdCard(idCard) {
    if (!idCard || idCard === "-") return idCard;
    const length = idCard.length;
    if (length <= 8) return "*".repeat(length);
    return idCard.substring(0, 4) + "*".repeat(length - 8) + idCard.substring(length - 4);
}

function maskPhone(phone) {
    if (!phone || phone === "-") return phone;
    const length = phone.length;
    if (length <= 7) return "*".repeat(length);
    return phone.substring(0, 3) + "*".repeat(length - 7) + phone.substring(length - 4);
}

// 编辑用户信息功能
function editUserInfo(section) {
    const user_id = ' . $user_id . ';

    switch(section) {
        case "basic":
            openEditModal("基本信息", [
                {name: "username", label: "用户名", type: "text", value: "' . htmlspecialchars($user['username']) . '"},
                {name: "email", label: "邮箱", type: "email", value: "' . htmlspecialchars($user['email'] ?? '') . '"},
                {name: "phone", label: "手机号", type: "tel", value: "' . htmlspecialchars($user['phone'] ?? '') . '"},
                {name: "quwanplanet_id", label: "趣玩ID", type: "text", value: "' . htmlspecialchars($user['quwanplanet_id']) . '"}
            ]);
            break;
        case "profile":
            openEditModal("个人资料", [
                {name: "region", label: "地区", type: "text", value: "' . htmlspecialchars($user['region'] ?? '') . '"},
                {name: "bio", label: "个人简介", type: "textarea", value: "' . htmlspecialchars($user['bio'] ?? '') . '"}
            ]);
            break;
        case "avatar":
            openAvatarUploadModal();
            break;
        default:
            alert("编辑功能开发中...");
    }
}

// 打开编辑弹窗
function openEditModal(title, fields) {
    const modal = document.getElementById("editModal") || createEditModal();

    document.getElementById("editModalTitle").textContent = title;

    const form = document.getElementById("editForm");
    const fieldsContainer = document.getElementById("editFields");
    fieldsContainer.innerHTML = "";

    fields.forEach(field => {
        const fieldDiv = document.createElement("div");
        fieldDiv.className = "form-field";

        if (field.type === "textarea") {
            fieldDiv.innerHTML = `
                <label for="${field.name}">${field.label}</label>
                <textarea name="${field.name}" id="${field.name}" rows="4" placeholder="请输入${field.label}">${field.value}</textarea>
            `;
        } else {
            fieldDiv.innerHTML = `
                <label for="${field.name}">${field.label}</label>
                <input type="${field.type}" name="${field.name}" id="${field.name}" value="${field.value}" placeholder="请输入${field.label}">
            `;
        }

        fieldsContainer.appendChild(fieldDiv);
    });

    modal.style.display = "flex";
}

// 创建编辑弹窗
function createEditModal() {
    const modalHTML = `
        <div id="editModal" class="modal" style="display: none;">
            <div class="modal-content">
                <div class="modal-header">
                    <h3 id="editModalTitle">编辑信息</h3>
                    <button class="modal-close" onclick="closeEditModal()">&times;</button>
                </div>
                <form id="editForm" method="post">
                    <input type="hidden" name="action" value="edit_user">
                    <input type="hidden" name="user_id" value="' . $user_id . '">
                    <div class="modal-body">
                        <div id="editFields"></div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn-secondary" onclick="closeEditModal()">取消</button>
                        <button type="submit" class="btn-primary">保存修改</button>
                    </div>
                </form>
            </div>
        </div>
    `;

    document.body.insertAdjacentHTML("beforeend", modalHTML);
    return document.getElementById("editModal");
}

// 关闭编辑弹窗
function closeEditModal() {
    const modal = document.getElementById("editModal");
    if (modal) {
        modal.style.display = "none";
    }
}

// 头像上传弹窗
function openAvatarUploadModal() {
    alert("头像上传功能开发中...");
}

// 提示框自动消失和关闭功能
function closeAlert(alertId) {
    const alert = document.getElementById(alertId);
    if (alert) {
        alert.classList.add('alert-fade-out');
        setTimeout(() => {
            alert.remove();
        }, 300);
    }
}

// 自动消失提示框
function autoCloseAlerts() {
    const alerts = document.querySelectorAll('.alert-dismissible');
    alerts.forEach(alert => {
        setTimeout(() => {
            if (alert && alert.parentNode) {
                closeAlert(alert.id);
            }
        }, 5000); // 5秒后自动消失
    });
}

// 导出菜单功能
function toggleExportMenu() {
    const menu = document.getElementById('exportMenu');
    menu.classList.toggle('show');
}

// 点击外部关闭导出菜单
document.addEventListener('click', function(e) {
    const dropdown = e.target.closest('.dropdown');
    if (!dropdown) {
        const menu = document.getElementById('exportMenu');
        if (menu) {
            menu.classList.remove('show');
        }
    }
});

// 为编辑按钮添加点击事件
document.addEventListener('DOMContentLoaded', function() {
    // 为所有编辑按钮添加点击事件
    document.querySelectorAll('.edit-btn').forEach(btn => {
        btn.addEventListener('click', function() {
            const section = this.getAttribute('data-section');
            editUserInfo(section);
        });
    });

    // 启动自动消失提示框
    autoCloseAlerts();
});
</script>


