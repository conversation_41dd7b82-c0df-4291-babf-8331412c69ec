<?php
header('Content-Type: application/json');

try {
    // 数据库配置
    $host = 'localhost';
    $dbname = 'quwanplanet';
    $username = 'root';
    $password = '';
    $charset = 'utf8mb4';
    $port = 3306;

    $dsn = "mysql:host={$host};dbname={$dbname};charset={$charset};port={$port}";
    $pdo = new PDO($dsn, $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

    // 测试查询
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM users");
    $result = $stmt->fetch(PDO::FETCH_ASSOC);

    echo json_encode([
        'success' => true,
        'message' => '数据库连接成功',
        'user_count' => $result['count']
    ]);

} catch (PDOException $e) {
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage()
    ]);
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage()
    ]);
}
?>
