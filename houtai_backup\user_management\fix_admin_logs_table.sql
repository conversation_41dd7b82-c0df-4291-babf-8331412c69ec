-- =====================================================
-- 修复管理日志表结构问题
-- 解决 employee_id 和 department 字段缺失的问题
-- =====================================================

SET NAMES utf8mb4;

-- =====================================================
-- 1. 检查并修复 admin_logs 表结构
-- =====================================================

-- 添加缺失的字段到 admin_logs 表
ALTER TABLE `admin_logs` 
ADD COLUMN IF NOT EXISTS `employee_id` VARCHAR(50) DEFAULT NULL COMMENT '员工工号' AFTER `admin_name`,
ADD COLUMN IF NOT EXISTS `department` VARCHAR(100) DEFAULT NULL COMMENT '部门名称' AFTER `employee_id`;

-- =====================================================
-- 2. 检查并创建 user_logs 表（如果不存在）
-- =====================================================

CREATE TABLE IF NOT EXISTS `user_logs` (
    `id` INT(11) NOT NULL AUTO_INCREMENT COMMENT '日志ID',
    `user_id` INT(11) NOT NULL COMMENT '用户ID',
    `operator_name` VARCHAR(100) NOT NULL COMMENT '操作员姓名',
    `employee_id` VARCHAR(50) DEFAULT NULL COMMENT '员工工号',
    `department` VARCHAR(100) DEFAULT NULL COMMENT '部门名称',
    `type` VARCHAR(50) NOT NULL COMMENT '日志类型',
    `content` TEXT NOT NULL COMMENT '日志内容',
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    PRIMARY KEY (`id`),
    KEY `idx_user_id` (`user_id`),
    KEY `idx_type` (`type`),
    KEY `idx_created_at` (`created_at`),
    KEY `idx_employee_id` (`employee_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户管理日志表';

-- =====================================================
-- 3. 检查并创建 admin_users 表的扩展字段（如果不存在）
-- =====================================================

-- 为 admin_users 表添加员工信息字段
ALTER TABLE `admin_users` 
ADD COLUMN IF NOT EXISTS `employee_id` VARCHAR(50) DEFAULT NULL COMMENT '员工工号' AFTER `name`,
ADD COLUMN IF NOT EXISTS `department` VARCHAR(100) DEFAULT NULL COMMENT '所属部门' AFTER `employee_id`,
ADD COLUMN IF NOT EXISTS `position` VARCHAR(100) DEFAULT NULL COMMENT '职位' AFTER `department`;

-- =====================================================
-- 4. 创建索引优化查询性能
-- =====================================================

-- 为 admin_logs 表添加索引
ALTER TABLE `admin_logs` 
ADD INDEX IF NOT EXISTS `idx_employee_id` (`employee_id`),
ADD INDEX IF NOT EXISTS `idx_department` (`department`),
ADD INDEX IF NOT EXISTS `idx_target_user_action` (`target_user_id`, `action`);

-- =====================================================
-- 5. 插入测试数据（如果表为空）
-- =====================================================

-- 更新现有管理员的员工信息（示例数据）
UPDATE `admin_users` SET 
    `employee_id` = CASE 
        WHEN `id` = 1 THEN 'A001'
        WHEN `id` = 2 THEN 'A002'
        WHEN `id` = 3 THEN 'A003'
        ELSE CONCAT('EMP', LPAD(`id`, 3, '0'))
    END,
    `department` = CASE 
        WHEN `id` = 1 THEN '系统管理部'
        WHEN `id` = 2 THEN '用户运营部'
        WHEN `id` = 3 THEN '客户服务部'
        ELSE '综合管理部'
    END,
    `position` = CASE 
        WHEN `id` = 1 THEN '系统管理员'
        WHEN `id` = 2 THEN '运营专员'
        WHEN `id` = 3 THEN '客服专员'
        ELSE '管理员'
    END
WHERE `employee_id` IS NULL OR `employee_id` = '';

-- =====================================================
-- 6. 创建视图简化日志查询
-- =====================================================

-- 创建管理日志视图
CREATE OR REPLACE VIEW `v_admin_logs` AS
SELECT 
    al.id,
    al.admin_id,
    al.admin_name,
    al.employee_id,
    al.department,
    al.target_user_id,
    al.action,
    al.reason as content,
    al.created_at,
    'admin' as log_type,
    au.position
FROM admin_logs al
LEFT JOIN admin_users au ON al.admin_id = au.id;

-- 创建用户日志视图
CREATE OR REPLACE VIEW `v_user_logs` AS
SELECT 
    ul.id,
    ul.user_id,
    ul.operator_name,
    ul.employee_id,
    ul.department,
    ul.type,
    ul.content,
    ul.created_at,
    'custom' as log_type
FROM user_logs ul;

-- 创建合并日志视图
CREATE OR REPLACE VIEW `v_all_user_logs` AS
SELECT 
    id,
    target_user_id as user_id,
    admin_name as operator_name,
    employee_id,
    department,
    action as type,
    content,
    created_at,
    log_type,
    position
FROM v_admin_logs
WHERE target_user_id IS NOT NULL

UNION ALL

SELECT 
    id,
    user_id,
    operator_name,
    employee_id,
    department,
    type,
    content,
    created_at,
    log_type,
    NULL as position
FROM v_user_logs

ORDER BY created_at DESC;

-- =====================================================
-- 7. 创建存储过程简化日志操作
-- =====================================================

DELIMITER $$

-- 创建添加管理日志的存储过程
CREATE PROCEDURE IF NOT EXISTS `sp_add_admin_log`(
    IN p_admin_id INT,
    IN p_admin_name VARCHAR(100),
    IN p_employee_id VARCHAR(50),
    IN p_department VARCHAR(100),
    IN p_target_user_id INT,
    IN p_action VARCHAR(50),
    IN p_reason TEXT
)
BEGIN
    INSERT INTO admin_logs (
        admin_id, admin_name, employee_id, department,
        target_user_id, action, reason, created_at
    ) VALUES (
        p_admin_id, p_admin_name, p_employee_id, p_department,
        p_target_user_id, p_action, p_reason, NOW()
    );
END$$

-- 创建添加用户日志的存储过程
CREATE PROCEDURE IF NOT EXISTS `sp_add_user_log`(
    IN p_user_id INT,
    IN p_operator_name VARCHAR(100),
    IN p_employee_id VARCHAR(50),
    IN p_department VARCHAR(100),
    IN p_type VARCHAR(50),
    IN p_content TEXT
)
BEGIN
    INSERT INTO user_logs (
        user_id, operator_name, employee_id, department,
        type, content, created_at
    ) VALUES (
        p_user_id, p_operator_name, p_employee_id, p_department,
        p_type, p_content, NOW()
    );
END$$

DELIMITER ;

-- =====================================================
-- 8. 验证修复结果
-- =====================================================

-- 检查 admin_logs 表结构
SELECT 
    COLUMN_NAME,
    DATA_TYPE,
    IS_NULLABLE,
    COLUMN_DEFAULT,
    COLUMN_COMMENT
FROM INFORMATION_SCHEMA.COLUMNS
WHERE TABLE_SCHEMA = DATABASE()
AND TABLE_NAME = 'admin_logs'
ORDER BY ORDINAL_POSITION;

-- 检查 user_logs 表结构
SELECT 
    COLUMN_NAME,
    DATA_TYPE,
    IS_NULLABLE,
    COLUMN_DEFAULT,
    COLUMN_COMMENT
FROM INFORMATION_SCHEMA.COLUMNS
WHERE TABLE_SCHEMA = DATABASE()
AND TABLE_NAME = 'user_logs'
ORDER BY ORDINAL_POSITION;

-- 检查 admin_users 表结构
SELECT 
    COLUMN_NAME,
    DATA_TYPE,
    IS_NULLABLE,
    COLUMN_DEFAULT,
    COLUMN_COMMENT
FROM INFORMATION_SCHEMA.COLUMNS
WHERE TABLE_SCHEMA = DATABASE()
AND TABLE_NAME = 'admin_users'
AND COLUMN_NAME IN ('employee_id', 'department', 'position')
ORDER BY ORDINAL_POSITION;

-- =====================================================
-- 执行完成提示
-- =====================================================
SELECT 'Admin logs table structure fixed successfully!' as message;
