<?php
// 最终修复方案 - 针对外键约束和字段问题
header('Content-Type: text/html; charset=UTF-8');
ini_set('default_charset', 'UTF-8');

session_start();

// 检查登录状态
if (!isset($_SESSION['cs_logged_in']) || !$_SESSION['cs_logged_in']) {
    echo '<h1>请先登录客服系统</h1>';
    exit;
}

require_once '../db_config.php';

echo '<h1>最终修复方案</h1>';

try {
    $pdo = getDbConnection();
    
    if ($_POST['final_fix'] ?? false) {
        echo '<h2>正在执行最终修复...</h2>';
        
        // 1. 检查并处理外键约束
        echo '<h3>1. 处理外键约束</h3>';
        
        // 查看外键约束
        $stmt = $pdo->query("
            SELECT CONSTRAINT_NAME, COLUMN_NAME, REFERENCED_TABLE_NAME, REFERENCED_COLUMN_NAME
            FROM information_schema.KEY_COLUMN_USAGE 
            WHERE TABLE_SCHEMA = DATABASE() 
            AND TABLE_NAME = 'customer_service_messages' 
            AND REFERENCED_TABLE_NAME IS NOT NULL
        ");
        $foreignKeys = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        if (!empty($foreignKeys)) {
            echo '<p>发现外键约束：</p><ul>';
            foreach ($foreignKeys as $fk) {
                echo '<li>' . $fk['CONSTRAINT_NAME'] . ': ' . $fk['COLUMN_NAME'] . ' -> ' . $fk['REFERENCED_TABLE_NAME'] . '.' . $fk['REFERENCED_COLUMN_NAME'] . '</li>';
            }
            echo '</ul>';
            
            // 临时删除外键约束
            foreach ($foreignKeys as $fk) {
                try {
                    $pdo->exec("ALTER TABLE customer_service_messages DROP FOREIGN KEY " . $fk['CONSTRAINT_NAME']);
                    echo '<p style="color: green;">✓ 临时删除外键约束: ' . $fk['CONSTRAINT_NAME'] . '</p>';
                } catch (Exception $e) {
                    echo '<p style="color: red;">删除外键约束失败: ' . $e->getMessage() . '</p>';
                }
            }
        } else {
            echo '<p>没有发现外键约束</p>';
        }
        
        // 2. 修复字段
        echo '<h3>2. 修复字段</h3>';
        
        try {
            // 修复 content 字段 - 强制设置
            $pdo->exec("ALTER TABLE customer_service_messages MODIFY COLUMN content TEXT NULL");
            echo '<p style="color: green;">✓ 修复 content 字段</p>';
        } catch (Exception $e) {
            echo '<p style="color: red;">修复 content 字段失败: ' . $e->getMessage() . '</p>';
        }
        
        try {
            // 修复 session_id 字段
            $pdo->exec("ALTER TABLE customer_service_messages MODIFY COLUMN session_id VARCHAR(255) NOT NULL");
            echo '<p style="color: green;">✓ 修复 session_id 字段</p>';
        } catch (Exception $e) {
            echo '<p style="color: red;">修复 session_id 字段失败: ' . $e->getMessage() . '</p>';
        }
        
        // 3. 重新创建外键约束
        echo '<h3>3. 重新创建外键约束</h3>';
        
        if (!empty($foreignKeys)) {
            foreach ($foreignKeys as $fk) {
                try {
                    $pdo->exec("
                        ALTER TABLE customer_service_messages 
                        ADD CONSTRAINT " . $fk['CONSTRAINT_NAME'] . " 
                        FOREIGN KEY (" . $fk['COLUMN_NAME'] . ") 
                        REFERENCES " . $fk['REFERENCED_TABLE_NAME'] . "(" . $fk['REFERENCED_COLUMN_NAME'] . ")
                    ");
                    echo '<p style="color: green;">✓ 重新创建外键约束: ' . $fk['CONSTRAINT_NAME'] . '</p>';
                } catch (Exception $e) {
                    echo '<p style="color: orange;">重新创建外键约束失败（可忽略）: ' . $e->getMessage() . '</p>';
                }
            }
        }
        
        // 4. 测试插入 - 使用真实的session_id
        echo '<h3>4. 测试数据插入</h3>';
        
        // 获取一个真实的session_id
        $stmt = $pdo->query("SELECT session_id FROM customer_service_sessions LIMIT 1");
        $realSession = $stmt->fetch();
        
        if ($realSession) {
            try {
                $testMessage = '最终修复测试消息';
                
                $stmt = $pdo->prepare("
                    INSERT INTO customer_service_messages
                    (session_id, sender_type, sender_id, sender_name, message_type, content, created_at)
                    VALUES (?, 'system', NULL, '系统', 'system', ?, NOW())
                ");
                
                $stmt->execute([$realSession['session_id'], $testMessage]);
                echo '<p style="color: green;">✓ 数据插入测试成功</p>';
                
                // 清理测试数据
                $stmt = $pdo->prepare("DELETE FROM customer_service_messages WHERE session_id = ? AND content = ?");
                $stmt->execute([$realSession['session_id'], $testMessage]);
                echo '<p>测试数据已清理</p>';
                
            } catch (Exception $e) {
                echo '<p style="color: red;">✗ 数据插入测试失败: ' . $e->getMessage() . '</p>';
                
                // 尝试更简单的插入
                echo '<p>尝试最简单的插入...</p>';
                try {
                    $stmt = $pdo->prepare("
                        INSERT INTO customer_service_messages (session_id, content)
                        VALUES (?, ?)
                    ");
                    $stmt->execute([$realSession['session_id'], $testMessage]);
                    echo '<p style="color: green;">✓ 简单插入成功</p>';
                    
                    // 清理
                    $stmt = $pdo->prepare("DELETE FROM customer_service_messages WHERE session_id = ? AND content = ?");
                    $stmt->execute([$realSession['session_id'], $testMessage]);
                    
                } catch (Exception $e2) {
                    echo '<p style="color: red;">简单插入也失败: ' . $e2->getMessage() . '</p>';
                }
            }
        } else {
            echo '<p style="color: orange;">没有找到真实的session_id进行测试</p>';
        }
        
        echo '<h2 style="color: green;">最终修复完成！</h2>';
    }
    
    // 显示当前状态
    echo '<h2>当前状态检查</h2>';
    
    // 检查字段状态
    $stmt = $pdo->query("DESCRIBE customer_service_messages");
    $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo '<h3>字段状态：</h3>';
    foreach ($columns as $column) {
        $status = '';
        if ($column['Field'] === 'content') {
            $status = ($column['Null'] === 'YES') ? '✓ 正常' : '✗ 仍有问题';
            $color = ($column['Null'] === 'YES') ? 'green' : 'red';
            echo '<p style="color: ' . $color . ';">content: ' . $status . ' (NULL: ' . $column['Null'] . ', Default: ' . ($column['Default'] ?? 'NULL') . ')</p>';
        }
        if ($column['Field'] === 'session_id') {
            $status = ($column['Null'] === 'NO') ? '✓ 正常' : '✗ 有问题';
            $color = ($column['Null'] === 'NO') ? 'green' : 'red';
            echo '<p style="color: ' . $color . ';">session_id: ' . $status . ' (NULL: ' . $column['Null'] . ')</p>';
        }
    }
    
    // API测试
    echo '<h2>API功能测试</h2>';
    $stmt = $pdo->query("SELECT session_id FROM customer_service_sessions WHERE status = 'waiting' LIMIT 1");
    $testSession = $stmt->fetch();
    
    if ($testSession) {
        $testSessionId = $testSession['session_id'];
        echo '<p>测试会话ID: ' . $testSessionId . '</p>';
        echo '<button onclick="testAPI(\'' . $testSessionId . '\')" style="padding: 8px 16px; background: #28a745; color: white; border: none; border-radius: 3px; cursor: pointer;">测试接受API</button>';
        echo '<div id="result"></div>';
    } else {
        echo '<p>没有等待中的会话可供测试</p>';
    }
    
} catch (Exception $e) {
    echo '<p style="color: red;">错误: ' . $e->getMessage() . '</p>';
}
?>

<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>最终修复方案</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .warning { background: #fff3cd; padding: 15px; border-radius: 5px; margin: 20px 0; border-left: 4px solid #ffc107; }
        .fix-btn { background: #dc3545; color: white; border: none; border-radius: 3px; cursor: pointer; font-size: 16px; padding: 12px 24px; }
        pre { background: #f5f5f5; padding: 10px; border-radius: 5px; }
    </style>
</head>
<body>
    <?php if (!($_POST['final_fix'] ?? false)): ?>
    <div class="warning">
        <h3>⚠️ 最终修复方案</h3>
        <p>这个修复方案将：</p>
        <ul>
            <li><strong>临时删除外键约束</strong>（修复完成后重新创建）</li>
            <li><strong>强制修复所有字段</strong>（包括content和session_id）</li>
            <li><strong>使用真实数据测试</strong>（确保修复有效）</li>
        </ul>
        <p><strong>⚠️ 建议在执行前备份数据库！</strong></p>
    </div>
    
    <form method="POST">
        <button type="submit" name="final_fix" value="1" class="fix-btn">执行最终修复</button>
    </form>
    <?php endif; ?>
    
    <script>
        async function testAPI(sessionId) {
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = '<p>🔄 测试中...</p>';
            
            try {
                const response = await fetch('api/accept_session.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        sessionId: sessionId
                    })
                });
                
                const data = await response.json();
                
                if (data.success) {
                    resultDiv.innerHTML = `
                        <h3 style="color: green;">🎉 成功！接受会话功能正常！</h3>
                        <pre>${JSON.stringify(data, null, 2)}</pre>
                        <p><a href="sessions.php" style="background: #28a745; color: white; padding: 8px 16px; text-decoration: none; border-radius: 3px;">查看会话列表</a></p>
                    `;
                } else {
                    resultDiv.innerHTML = `
                        <h3 style="color: red;">❌ 仍然失败</h3>
                        <pre>${JSON.stringify(data, null, 2)}</pre>
                        <p style="color: red;">请联系技术支持，提供以上错误信息</p>
                    `;
                }
                
            } catch (error) {
                resultDiv.innerHTML = '<h3 style="color: red;">❌ 网络错误</h3><p>' + error.message + '</p>';
            }
        }
    </script>
    
    <hr>
    <p><a href="sessions.php">返回会话列表</a> | <a href="complete_fix.php">完整修复</a></p>
</body>
</html>
