<?php
// 快速测试修复效果
header('Content-Type: text/html; charset=UTF-8');
ini_set('default_charset', 'UTF-8');

session_start();

// 检查登录状态
if (!isset($_SESSION['cs_logged_in']) || !$_SESSION['cs_logged_in']) {
    echo '<h1>请先登录客服系统</h1>';
    exit;
}

require_once '../db_config.php';

echo '<h1>🚀 快速测试修复效果</h1>';

try {
    $pdo = getDbConnection();
    
    // 获取等待中的会话
    $stmt = $pdo->query("SELECT session_id, user_name, priority, started_at FROM customer_service_sessions WHERE status = 'waiting' ORDER BY started_at DESC LIMIT 3");
    $waitingSessions = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (!empty($waitingSessions)) {
        echo '<h2>📋 等待中的会话</h2>';
        echo '<div style="background: #e7f3ff; padding: 15px; border-radius: 8px; margin: 10px 0;">';
        echo '<p><strong>说明：</strong>现在使用的是简化版API，跳过了系统消息插入，避免数据库字段问题。</p>';
        echo '</div>';
        
        foreach ($waitingSessions as $session) {
            echo '<div style="border: 1px solid #ddd; padding: 15px; margin: 10px 0; border-radius: 8px; background: #f9f9f9;">';
            echo '<h3>会话: ' . htmlspecialchars($session['session_id']) . '</h3>';
            echo '<p><strong>用户：</strong>' . htmlspecialchars($session['user_name']) . '</p>';
            echo '<p><strong>优先级：</strong>' . htmlspecialchars($session['priority']) . '</p>';
            echo '<p><strong>开始时间：</strong>' . htmlspecialchars($session['started_at']) . '</p>';
            echo '<button onclick="quickAccept(\'' . $session['session_id'] . '\')" style="padding: 8px 16px; background: #28a745; color: white; border: none; border-radius: 5px; cursor: pointer; font-size: 14px;">🎯 快速接受</button>';
            echo '</div>';
        }
    } else {
        echo '<div style="background: #fff3cd; padding: 15px; border-radius: 8px; margin: 10px 0;">';
        echo '<h3>⚠️ 没有等待中的会话</h3>';
        echo '<p>需要创建一些测试数据来测试功能</p>';
        echo '<a href="create_test_data.php" style="background: #007cba; color: white; padding: 8px 16px; text-decoration: none; border-radius: 5px;">创建测试数据</a>';
        echo '</div>';
    }
    
} catch (Exception $e) {
    echo '<div style="background: #f8d7da; padding: 15px; border-radius: 8px; margin: 10px 0; color: #721c24;">';
    echo '<h3>❌ 错误</h3>';
    echo '<p>' . htmlspecialchars($e->getMessage()) . '</p>';
    echo '</div>';
}
?>

<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>快速测试</title>
    <style>
        body { 
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; 
            margin: 20px; 
            background: #f5f5f5; 
        }
        .container { 
            max-width: 800px; 
            margin: 0 auto; 
            background: white; 
            padding: 20px; 
            border-radius: 10px; 
            box-shadow: 0 2px 10px rgba(0,0,0,0.1); 
        }
        .result { 
            margin: 20px 0; 
            padding: 15px; 
            border-radius: 8px; 
            border-left: 4px solid #28a745; 
        }
        .success { background: #d4edda; color: #155724; border-left-color: #28a745; }
        .error { background: #f8d7da; color: #721c24; border-left-color: #dc3545; }
        .info { background: #d1ecf1; color: #0c5460; border-left-color: #17a2b8; }
        pre { 
            background: #f8f9fa; 
            padding: 10px; 
            border-radius: 5px; 
            overflow-x: auto; 
            font-size: 12px; 
        }
        .nav { 
            background: #6F7BF5; 
            color: white; 
            padding: 10px 15px; 
            border-radius: 5px; 
            margin: 20px 0; 
        }
        .nav a { 
            color: white; 
            text-decoration: none; 
            margin-right: 15px; 
        }
        .nav a:hover { text-decoration: underline; }
    </style>
</head>
<body>
    <div class="container">
        <div id="result"></div>
        
        <div class="nav">
            <strong>🔗 快速导航：</strong>
            <a href="sessions.php">会话列表</a>
            <a href="test_simple_api.php">API测试</a>
            <a href="create_test_data.php">创建测试数据</a>
        </div>
        
        <div class="result info">
            <h3>📝 修复说明</h3>
            <ul>
                <li><strong>会话列表页面</strong>：已修改为使用简化版API</li>
                <li><strong>原版API</strong>：已修改为跳过系统消息插入</li>
                <li><strong>核心功能</strong>：会话状态更新和实时通知正常工作</li>
                <li><strong>影响</strong>：不会在消息记录中显示"客服已接受会话"的系统消息</li>
            </ul>
        </div>
    </div>
    
    <script>
        async function quickAccept(sessionId) {
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = '<div class="result info">🔄 正在接受会话 ' + sessionId + '...</div>';
            
            try {
                // 使用简化版API
                const response = await fetch('api/accept_session_simple.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        sessionId: sessionId
                    })
                });
                
                const data = await response.json();
                
                if (data.success) {
                    resultDiv.innerHTML = `
                        <div class="result success">
                            <h3>🎉 接受会话成功！</h3>
                            <p><strong>会话ID：</strong>${sessionId}</p>
                            <p><strong>状态：</strong>已变更为进行中</p>
                            <p><strong>分配客服：</strong>${data.session.cs_name}</p>
                            <pre>${JSON.stringify(data, null, 2)}</pre>
                            <p>
                                <a href="sessions.php" style="background: #28a745; color: white; padding: 8px 16px; text-decoration: none; border-radius: 5px; margin-right: 10px;">查看会话列表</a>
                                <a href="session_detail.php?id=${sessionId}" style="background: #007cba; color: white; padding: 8px 16px; text-decoration: none; border-radius: 5px;">打开会话详情</a>
                            </p>
                        </div>
                    `;
                    
                    // 3秒后自动刷新页面
                    setTimeout(() => {
                        window.location.reload();
                    }, 3000);
                    
                } else {
                    resultDiv.innerHTML = `
                        <div class="result error">
                            <h3>❌ 接受会话失败</h3>
                            <p><strong>错误：</strong>${data.error}</p>
                            <pre>${JSON.stringify(data, null, 2)}</pre>
                        </div>
                    `;
                }
                
            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="result error">
                        <h3>❌ 网络错误</h3>
                        <p>${error.message}</p>
                    </div>
                `;
            }
        }
    </script>
</body>
</html>
