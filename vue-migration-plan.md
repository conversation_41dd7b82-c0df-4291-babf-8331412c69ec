# Vue + uniCloud 迁移方案

## 迁移策略
在现有项目基础上，新增Vue + uniCloud实现，保持域名不变：
- 前端：planet.vancrest.xyz (Vue H5版本)
- 后端：vansmrz.vancrest.xyz (保留PHP管理后台，新增uniCloud API)

## 技术栈对比

### 原PHP方案问题
- 实时通信复杂（SSE + 轮询不稳定）
- 安全性相对较弱
- 支付接口安全隐患
- 服务器维护复杂

### Vue + uniCloud方案优势
- 原生WebSocket支持，实时通信简单稳定
- 云函数天然防护，安全性更高
- 无需服务器维护，自动扩容
- 支付接口更安全
- 一套代码支持H5/小程序/APP

## 实现方案

### 1. 保留现有PHP后台
- 管理员登录：vansmrz.vancrest.xyz/admin
- 数据库管理：继续使用MySQL
- 客服工作台：升级为Vue版本

### 2. 新增Vue前端
- 用户端：planet.vancrest.xyz (Vue H5)
- 客服聊天：实时WebSocket通信
- 移动端：uniapp打包APP

### 3. uniCloud云函数
```
uniCloud-aliyun/
├── cloudfunctions/
│   ├── user-login/          # 用户登录
│   ├── user-register/       # 用户注册
│   ├── send-sms/           # 发送验证码
│   ├── chat-create/        # 创建客服会话
│   ├── chat-send/          # 发送消息
│   ├── chat-realtime/      # 实时消息推送
│   └── admin-api/          # 管理后台API
└── database/
    └── db_init.json        # 数据库初始化
```

## 数据库迁移

### 保持现有MySQL结构
- 用户表：users
- 客服会话：customer_service_sessions  
- 消息表：messages
- 管理员：admins

### 新增uniCloud数据库
- 实时消息缓存
- 文件上传记录
- 推送通知记录

## 部署方案

### 1. 域名配置
```
planet.vancrest.xyz -> Vue H5应用
vansmrz.vancrest.xyz -> PHP管理后台 + Vue客服工作台
```

### 2. uniCloud部署
- 阿里云服务商
- 自动SSL证书
- CDN加速
- 自动备份

### 3. 安卓签名（免费方案）
```bash
# 方法1：Android Studio生成
keytool -genkey -v -keystore quwanplanet.keystore -alias quwanplanet -keyalg RSA -keysize 2048 -validity 10000

# 方法2：HBuilderX自动生成
在manifest.json中配置即可
```

## 实时通信方案

### uniCloud实时推送
```javascript
// 前端监听
const db = uniCloud.database()
const watcher = db.collection('messages')
  .where({ session_id: sessionId })
  .watch({
    onChange: (snapshot) => {
      // 收到新消息
      handleNewMessage(snapshot.docs[0])
    }
  })

// 后端推送
await db.collection('messages').add({
  session_id: sessionId,
  content: message,
  sender_type: 'customer_service',
  created_at: new Date()
})
```

### WebSocket备选方案
```javascript
// 如果需要更复杂的实时功能
const socket = new WebSocket('wss://your-websocket-server')
socket.onmessage = (event) => {
  const message = JSON.parse(event.data)
  handleMessage(message)
}
```

## 开发步骤

### 第一步：基础框架 (1天)
1. 创建Vue项目结构
2. 配置uniCloud
3. 设置基础路由和状态管理

### 第二步：用户系统 (2天)
1. 手机号登录页面
2. 验证码输入组件
3. 用户信息完善
4. 登录状态管理

### 第三步：客服系统 (3天)
1. 聊天界面UI
2. 实时消息功能
3. 文件上传功能
4. AI机器人集成

### 第四步：管理后台 (2天)
1. Vue版客服工作台
2. 实时会话管理
3. 用户数据展示
4. 权限控制

### 第五步：部署优化 (1天)
1. 域名配置
2. SSL证书
3. 性能优化
4. 安全加固

## 成本对比

### 服务器方案（当前）
- 服务器费用：约500元/月
- 维护成本：高
- 扩容复杂：需手动配置

### uniCloud方案（推荐）
- 免费额度：足够小型应用
- 付费版本：按量计费，约100-200元/月
- 维护成本：零
- 自动扩容：无需配置

## 安全优势

### uniCloud安全特性
- 云函数隔离执行
- 自动防DDoS攻击
- 数据库访问控制
- API接口加密传输

### 支付安全
- 官方SDK集成
- 服务端验证
- 防重放攻击
- 交易记录加密

## 迁移时间表

### 第1周：准备阶段
- 环境搭建
- 技术验证
- 数据库设计

### 第2周：核心功能
- 用户登录系统
- 基础聊天功能
- 实时通信测试

### 第3周：完善功能
- 管理后台集成
- 文件上传功能
- 安全加固

### 第4周：测试部署
- 功能测试
- 性能优化
- 正式部署

要开始实施这个方案吗？我可以先帮你创建Vue版本的核心功能。
