<?php
// 引入session配置
require_once '../../sql/session_config.php';
initLoginSession();

// 检查用户是否已登录
if (!isUserLoggedIn()) {
    header('Location: ../login/index.php');
    exit;
}
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <meta name="theme-color" content="#1E90FF">
    <title>商城 - 趣玩星球</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'PingFang SC', 'Helvetica Neue', Arial, sans-serif;
        }

        body {
            background-color: #f8f9fa;
            color: #333333;
            line-height: 1.6;
            min-height: 100vh;
        }

        .header-bar {
            display: flex;
            align-items: center;
            padding: 15px;
            background-color: #1E90FF;
            color: white;
            position: sticky;
            top: 0;
            z-index: 100;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        .back-button {
            margin-right: 15px;
            font-size: 18px;
            color: white;
            text-decoration: none;
        }

        .header-title {
            font-size: 18px;
            font-weight: bold;
        }

        .search-bar {
            display: flex;
            align-items: center;
            background-color: white;
            border-radius: 20px;
            margin: 15px;
            padding: 10px 15px;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
        }

        .search-bar i {
            color: #999;
            margin-right: 10px;
        }

        .search-bar input {
            flex: 1;
            border: none;
            outline: none;
            font-size: 14px;
        }

        .category-tabs {
            display: flex;
            overflow-x: auto;
            padding: 0 10px;
            margin-bottom: 15px;
            background-color: white;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
            -webkit-overflow-scrolling: touch;
            scrollbar-width: none; /* Firefox */
        }

        .category-tabs::-webkit-scrollbar {
            display: none; /* Chrome, Safari, Edge */
        }

        .category-tab {
            padding: 12px 15px;
            font-size: 14px;
            color: #666;
            white-space: nowrap;
            position: relative;
        }

        .category-tab.active {
            color: #1E90FF;
            font-weight: 500;
        }

        .category-tab.active:after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 15px;
            right: 15px;
            height: 3px;
            background-color: #1E90FF;
            border-radius: 3px;
        }

        .banner {
            margin: 0 15px 15px;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
        }

        .banner img {
            width: 100%;
            height: auto;
            display: block;
        }

        .section-title {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin: 20px 15px 10px;
        }

        .section-title h2 {
            font-size: 16px;
            color: #333;
        }

        .section-title a {
            font-size: 13px;
            color: #999;
            text-decoration: none;
        }

        .product-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 15px;
            padding: 0 15px;
        }

        .product-card {
            background-color: white;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
        }

        .product-image {
            width: 100%;
            height: 150px;
            position: relative;
        }

        .product-image img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .product-tag {
            position: absolute;
            top: 10px;
            left: 10px;
            background-color: rgba(30, 144, 255, 0.8);
            color: white;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
        }

        .product-info {
            padding: 12px;
        }

        .product-title {
            font-size: 14px;
            font-weight: 500;
            margin-bottom: 5px;
            color: #333;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
            text-overflow: ellipsis;
            height: 42px;
        }

        .product-price {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .price {
            font-size: 16px;
            font-weight: bold;
            color: #FF6B6B;
        }

        .price small {
            font-size: 12px;
            font-weight: normal;
        }

        .sold {
            font-size: 12px;
            color: #999;
        }

        .empty-state {
            text-align: center;
            padding: 50px 20px;
            color: #999;
        }

        .empty-state i {
            font-size: 50px;
            margin-bottom: 15px;
            color: #ddd;
        }

        .empty-state p {
            font-size: 15px;
            margin-bottom: 20px;
        }
    </style>
    <!-- 引入字体图标库 -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <!-- 顶部导航栏 -->
    <div class="header-bar">
        <a href="../profile/index.php" class="back-button">
            <i class="fas fa-arrow-left"></i>
        </a>
        <div class="header-title">趣玩商城</div>
    </div>

    <!-- 搜索栏 -->
    <div class="search-bar">
        <i class="fas fa-search"></i>
        <input type="text" placeholder="搜索商品">
    </div>

    <!-- 分类标签 -->
    <div class="category-tabs">
        <div class="category-tab active">推荐</div>
        <div class="category-tab">会员专区</div>
        <div class="category-tab">头像框</div>
        <div class="category-tab">铭牌</div>
        <div class="category-tab">靓号</div>
        <div class="category-tab">周边商品</div>
        <div class="category-tab">数码产品</div>
        <div class="category-tab">生活用品</div>
        <div class="category-tab">服饰鞋包</div>
    </div>

    <!-- 轮播图 -->
    <div class="banner">
        <img src="https://s1.imagehub.cc/images/2025/05/16/shop_banner.jpg" alt="商城活动">
    </div>

    <!-- 热门商品 -->
    <div class="section-title">
        <h2>热门商品</h2>
        <a href="#">更多 <i class="fas fa-chevron-right"></i></a>
    </div>

    <div class="product-grid">
        <div class="product-card">
            <div class="product-image">
                <img src="https://s1.imagehub.cc/images/2025/05/16/product1.jpg" alt="商品图片">
                <div class="product-tag">热销</div>
            </div>
            <div class="product-info">
                <div class="product-title">趣玩星球限定T恤 2025新款潮流印花短袖</div>
                <div class="product-price">
                    <div class="price"><small>¥</small>99</div>
                    <div class="sold">已售 521</div>
                </div>
            </div>
        </div>

        <div class="product-card">
            <div class="product-image">
                <img src="https://s1.imagehub.cc/images/2025/05/16/product2.jpg" alt="商品图片">
                <div class="product-tag">新品</div>
            </div>
            <div class="product-info">
                <div class="product-title">趣玩星球吉祥物公仔 毛绒玩具抱枕</div>
                <div class="product-price">
                    <div class="price"><small>¥</small>129</div>
                    <div class="sold">已售 342</div>
                </div>
            </div>
        </div>

        <div class="product-card">
            <div class="product-image">
                <img src="https://s1.imagehub.cc/images/2025/05/16/product3.jpg" alt="商品图片">
            </div>
            <div class="product-info">
                <div class="product-title">趣玩星球保温杯 304不锈钢双层真空</div>
                <div class="product-price">
                    <div class="price"><small>¥</small>79</div>
                    <div class="sold">已售 198</div>
                </div>
            </div>
        </div>

        <div class="product-card">
            <div class="product-image">
                <img src="https://s1.imagehub.cc/images/2025/05/16/product4.jpg" alt="商品图片">
                <div class="product-tag">会员价</div>
            </div>
            <div class="product-info">
                <div class="product-title">趣玩星球帆布包 环保购物袋单肩包</div>
                <div class="product-price">
                    <div class="price"><small>¥</small>59</div>
                    <div class="sold">已售 267</div>
                </div>
            </div>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 分类标签点击效果
            const categoryTabs = document.querySelectorAll('.category-tab');
            categoryTabs.forEach(tab => {
                tab.addEventListener('click', function() {
                    // 移除所有标签的active类
                    categoryTabs.forEach(t => t.classList.remove('active'));

                    // 添加active类到当前标签
                    this.classList.add('active');

                    // 根据分类跳转到相应页面
                    const category = this.textContent.trim();
                    switch(category) {
                        case '头像框':
                            window.location.href = 'avatar_frames/index.php';
                            break;
                        case '铭牌':
                            window.location.href = 'nameplates/index.php';
                            break;
                        case '靓号':
                            window.location.href = 'special_numbers/index.php';
                            break;
                        // 其他分类可以在这里添加
                    }
                });
            });

            // 商品卡片点击效果
            const productCards = document.querySelectorAll('.product-card');
            productCards.forEach(card => {
                card.addEventListener('click', function() {
                    // 点击效果
                    this.style.transform = 'scale(0.98)';
                    setTimeout(() => {
                        this.style.transform = 'scale(1)';
                    }, 150);

                    // 这里可以添加跳转到商品详情页的逻辑
                    const productTitle = this.querySelector('.product-title').textContent;
                    alert(`商品详情页即将上线，敬请期待\n${productTitle}`);
                });
            });
        });
    </script>
</body>
</html>
