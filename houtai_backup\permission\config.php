<?php
/**
 * 权限管理 - 权限配置页面
 * 趣玩星球管理后台
 */

// 临时移除session检查，用于测试
// session_start();

// 登录检查 - 临时注释
// if (!isset($_SESSION['admin_id'])) {
//     header('Location: ../login.php');
//     exit;
// }

// 使用模拟数据进行测试
$admin_name = '测试管理员';
$admin_id = '1';

// 模拟权限数据
$permission_modules = [
    'user' => [
        'name' => '用户管理',
        'icon' => 'fas fa-users',
        'color' => '#40E0D0',
        'permissions' => [
            ['id' => 1, 'name' => '查看用户列表', 'code' => 'user.view', 'description' => '可以查看所有用户信息'],
            ['id' => 2, 'name' => '创建用户', 'code' => 'user.create', 'description' => '可以创建新用户账户'],
            ['id' => 3, 'name' => '编辑用户', 'code' => 'user.edit', 'description' => '可以修改用户信息'],
            ['id' => 4, 'name' => '删除用户', 'code' => 'user.delete', 'description' => '可以删除用户账户'],
            ['id' => 5, 'name' => '用户权限管理', 'code' => 'user.permission', 'description' => '可以分配用户权限']
        ]
    ],
    'verification' => [
        'name' => '实名认证',
        'icon' => 'fas fa-id-card',
        'color' => '#F59E0B',
        'permissions' => [
            ['id' => 6, 'name' => '查看认证申请', 'code' => 'verification.view', 'description' => '可以查看认证申请列表'],
            ['id' => 7, 'name' => '审核认证', 'code' => 'verification.review', 'description' => '可以审核认证申请'],
            ['id' => 8, 'name' => '认证统计', 'code' => 'verification.stats', 'description' => '可以查看认证统计数据']
        ]
    ],
    'permission' => [
        'name' => '权限管理',
        'icon' => 'fas fa-shield-alt',
        'color' => '#8B5CF6',
        'permissions' => [
            ['id' => 9, 'name' => '权限申请', 'code' => 'permission.apply', 'description' => '可以申请新权限'],
            ['id' => 10, 'name' => '权限审批', 'code' => 'permission.approve', 'description' => '可以审批权限申请'],
            ['id' => 11, 'name' => '角色管理', 'code' => 'role.manage', 'description' => '可以管理系统角色'],
            ['id' => 12, 'name' => '权限配置', 'code' => 'permission.config', 'description' => '可以配置系统权限']
        ]
    ],
    'department' => [
        'name' => '部门管理',
        'icon' => 'fas fa-building',
        'color' => '#10B981',
        'permissions' => [
            ['id' => 13, 'name' => '查看部门', 'code' => 'department.view', 'description' => '可以查看部门信息'],
            ['id' => 14, 'name' => '创建部门', 'code' => 'department.create', 'description' => '可以创建新部门'],
            ['id' => 15, 'name' => '编辑部门', 'code' => 'department.edit', 'description' => '可以修改部门信息'],
            ['id' => 16, 'name' => '删除部门', 'code' => 'department.delete', 'description' => '可以删除部门'],
            ['id' => 17, 'name' => '员工管理', 'code' => 'department.employee', 'description' => '可以管理部门员工']
        ]
    ],
    'system' => [
        'name' => '系统管理',
        'icon' => 'fas fa-cog',
        'color' => '#6B7280',
        'permissions' => [
            ['id' => 18, 'name' => '系统配置', 'code' => 'system.config', 'description' => '可以修改系统配置'],
            ['id' => 19, 'name' => '日志查看', 'code' => 'system.logs', 'description' => '可以查看系统日志'],
            ['id' => 20, 'name' => '数据备份', 'code' => 'system.backup', 'description' => '可以进行数据备份'],
            ['id' => 21, 'name' => '系统监控', 'code' => 'system.monitor', 'description' => '可以监控系统状态']
        ]
    ]
];
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>权限配置 - 趣玩星球管理后台</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="../assets/css/admin-layout.css">
    <style>
        /* 权限配置页面专用样式 */
        .config-content {
            padding: 24px;
        }

        .page-actions {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 32px;
        }

        .search-box {
            display: flex;
            align-items: center;
            gap: 12px;
            background: white;
            padding: 8px 16px;
            border-radius: var(--border-radius);
            border: 2px solid var(--gray-200);
            transition: var(--transition);
        }

        .search-box:focus-within {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(64, 224, 208, 0.1);
        }

        .search-input {
            border: none;
            outline: none;
            font-size: 0.875rem;
            width: 300px;
        }

        .add-permission-btn {
            background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: var(--border-radius);
            font-weight: 600;
            cursor: pointer;
            transition: var(--transition);
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .add-permission-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(64, 224, 208, 0.3);
        }

        .modules-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 24px;
        }

        .module-card {
            background: white;
            border-radius: 16px;
            padding: 24px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
            border: 2px solid transparent;
            transition: all 0.3s ease;
        }

        .module-card:hover {
            border-color: var(--primary-color);
            transform: translateY(-2px);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
        }

        .module-header {
            display: flex;
            align-items: center;
            gap: 16px;
            margin-bottom: 24px;
            padding-bottom: 16px;
            border-bottom: 2px solid var(--gray-100);
        }

        .module-icon {
            width: 56px;
            height: 56px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            color: white;
        }

        .module-info h3 {
            font-size: 1.25rem;
            font-weight: 700;
            color: var(--gray-800);
            margin-bottom: 4px;
        }

        .module-count {
            font-size: 0.875rem;
            color: var(--gray-500);
        }

        .permissions-list {
            display: flex;
            flex-direction: column;
            gap: 12px;
        }

        .permission-item {
            background: var(--gray-50);
            border-radius: 12px;
            padding: 16px;
            border: 2px solid transparent;
            transition: all 0.3s ease;
            position: relative;
        }

        .permission-item:hover {
            background: white;
            border-color: var(--primary-color);
            box-shadow: 0 4px 12px rgba(64, 224, 208, 0.1);
        }

        .permission-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 8px;
        }

        .permission-info h4 {
            font-weight: 600;
            color: var(--gray-800);
            margin-bottom: 4px;
        }

        .permission-code {
            font-size: 0.75rem;
            color: var(--gray-500);
            background: var(--gray-200);
            padding: 2px 8px;
            border-radius: 8px;
            font-family: monospace;
        }

        .permission-actions {
            display: flex;
            gap: 4px;
        }

        .action-btn {
            width: 32px;
            height: 32px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            transition: var(--transition);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 0.875rem;
        }

        .btn-edit {
            background: var(--info-color);
            color: white;
        }

        .btn-edit:hover {
            background: #2563EB;
            transform: scale(1.1);
        }

        .btn-delete {
            background: var(--error-color);
            color: white;
        }

        .btn-delete:hover {
            background: #DC2626;
            transform: scale(1.1);
        }

        .permission-description {
            color: var(--gray-600);
            font-size: 0.875rem;
            line-height: 1.4;
        }

        /* 模态框样式 */
        .modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            display: none;
            align-items: center;
            justify-content: center;
            z-index: 1000;
            backdrop-filter: blur(5px);
        }

        .modal.show {
            display: flex;
        }

        .modal-content {
            background: white;
            border-radius: 16px;
            padding: 24px;
            max-width: 500px;
            width: 90%;
            max-height: 80vh;
            overflow-y: auto;
        }

        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 24px;
            padding-bottom: 16px;
            border-bottom: 2px solid var(--gray-100);
        }

        .modal-title {
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--gray-800);
        }

        .modal-close {
            background: none;
            border: none;
            font-size: 24px;
            color: var(--gray-400);
            cursor: pointer;
            transition: var(--transition);
        }

        .modal-close:hover {
            color: var(--gray-600);
        }

        .form-group {
            display: flex;
            flex-direction: column;
            gap: 8px;
            margin-bottom: 20px;
        }

        .form-label {
            font-weight: 600;
            color: var(--gray-700);
            font-size: 0.875rem;
        }

        .form-input,
        .form-select,
        .form-textarea {
            padding: 12px 16px;
            border: 2px solid var(--gray-200);
            border-radius: var(--border-radius);
            font-size: 0.875rem;
            transition: var(--transition);
        }

        .form-input:focus,
        .form-select:focus,
        .form-textarea:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(64, 224, 208, 0.1);
        }

        .form-textarea {
            resize: vertical;
            min-height: 80px;
        }

        .modal-actions {
            display: flex;
            gap: 12px;
            justify-content: flex-end;
            margin-top: 24px;
            padding-top: 16px;
            border-top: 2px solid var(--gray-100);
        }

        .btn-cancel {
            background: var(--gray-200);
            color: var(--gray-700);
            padding: 10px 20px;
            border: none;
            border-radius: var(--border-radius);
            cursor: pointer;
            transition: var(--transition);
        }

        .btn-cancel:hover {
            background: var(--gray-300);
        }

        .btn-save {
            background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: var(--border-radius);
            cursor: pointer;
            transition: var(--transition);
        }

        .btn-save:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(64, 224, 208, 0.3);
        }

        /* 统计信息 */
        .stats-bar {
            background: white;
            border-radius: 12px;
            padding: 16px 24px;
            margin-bottom: 24px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .stat-item {
            text-align: center;
        }

        .stat-number {
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--primary-color);
        }

        .stat-label {
            font-size: 0.875rem;
            color: var(--gray-600);
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .config-content {
                padding: 16px;
            }

            .modules-grid {
                grid-template-columns: 1fr;
            }

            .page-actions {
                flex-direction: column;
                gap: 16px;
                align-items: stretch;
            }

            .search-input {
                width: 100%;
            }

            .stats-bar {
                flex-direction: column;
                gap: 16px;
            }
        }
    </style>
</head>
<body>
    <div class="admin-layout">
        <!-- 左侧菜单 -->
        <?php include '../includes/sidebar.php'; ?>

        <!-- 主内容区域 -->
        <main class="main-content">
            <!-- 顶部导航栏 -->
            <?php include '../includes/topbar.php'; ?>

            <div class="config-content">
                <!-- 页面标题和操作 -->
                <div class="page-actions">
                    <div>
                        <h1 class="page-title">
                            <i class="fas fa-key" style="color: var(--primary-color);"></i>
                            权限配置
                        </h1>
                        <p class="page-subtitle">管理系统权限和功能模块</p>
                    </div>
                    <div style="display: flex; gap: 16px; align-items: center;">
                        <div class="search-box">
                            <i class="fas fa-search" style="color: var(--gray-400);"></i>
                            <input type="text" class="search-input" placeholder="搜索权限..." id="searchInput">
                        </div>
                        <button class="add-permission-btn" onclick="openPermissionModal()">
                            <i class="fas fa-plus"></i>
                            新建权限
                        </button>
                    </div>
                </div>

                <!-- 统计信息 -->
                <div class="stats-bar">
                    <div class="stat-item">
                        <div class="stat-number"><?php echo count($permission_modules); ?></div>
                        <div class="stat-label">功能模块</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number">
                            <?php
                            $total_permissions = 0;
                            foreach ($permission_modules as $module) {
                                $total_permissions += count($module['permissions']);
                            }
                            echo $total_permissions;
                            ?>
                        </div>
                        <div class="stat-label">总权限数</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number">5</div>
                        <div class="stat-label">系统角色</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number">52</div>
                        <div class="stat-label">用户总数</div>
                    </div>
                </div>

                <!-- 权限模块列表 -->
                <div class="modules-grid">
                    <?php foreach ($permission_modules as $module_key => $module): ?>
                        <div class="module-card">
                            <div class="module-header">
                                <div class="module-icon" style="background: <?php echo $module['color']; ?>;">
                                    <i class="<?php echo $module['icon']; ?>"></i>
                                </div>
                                <div class="module-info">
                                    <h3><?php echo htmlspecialchars($module['name']); ?></h3>
                                    <div class="module-count"><?php echo count($module['permissions']); ?> 个权限</div>
                                </div>
                            </div>

                            <div class="permissions-list">
                                <?php foreach ($module['permissions'] as $permission): ?>
                                    <div class="permission-item">
                                        <div class="permission-header">
                                            <div class="permission-info">
                                                <h4><?php echo htmlspecialchars($permission['name']); ?></h4>
                                                <div class="permission-code"><?php echo htmlspecialchars($permission['code']); ?></div>
                                            </div>
                                            <div class="permission-actions">
                                                <button class="action-btn btn-edit" onclick="editPermission(<?php echo $permission['id']; ?>)" title="编辑">
                                                    <i class="fas fa-edit"></i>
                                                </button>
                                                <button class="action-btn btn-delete" onclick="deletePermission(<?php echo $permission['id']; ?>)" title="删除">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </div>
                                        </div>
                                        <div class="permission-description">
                                            <?php echo htmlspecialchars($permission['description']); ?>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            </div>
        </main>
    </div>

    <!-- 权限编辑模态框 -->
    <div class="modal" id="permissionModal">
        <div class="modal-content">
            <div class="modal-header">
                <div class="modal-title" id="modalTitle">新建权限</div>
                <button class="modal-close" onclick="closePermissionModal()">&times;</button>
            </div>
            <form id="permissionForm">
                <div class="form-group">
                    <label class="form-label">权限名称</label>
                    <input type="text" class="form-input" name="name" placeholder="请输入权限名称" required>
                </div>
                <div class="form-group">
                    <label class="form-label">权限代码</label>
                    <input type="text" class="form-input" name="code" placeholder="module.action" required>
                </div>
                <div class="form-group">
                    <label class="form-label">所属模块</label>
                    <select class="form-select" name="module" required>
                        <option value="">选择模块</option>
                        <?php foreach ($permission_modules as $key => $module): ?>
                            <option value="<?php echo $key; ?>"><?php echo htmlspecialchars($module['name']); ?></option>
                        <?php endforeach; ?>
                    </select>
                </div>
                <div class="form-group">
                    <label class="form-label">权限描述</label>
                    <textarea class="form-textarea" name="description" placeholder="请输入权限描述"></textarea>
                </div>

                <div class="modal-actions">
                    <button type="button" class="btn-cancel" onclick="closePermissionModal()">取消</button>
                    <button type="submit" class="btn-save">保存权限</button>
                </div>
            </form>
        </div>
    </div>

    <script src="../assets/js/admin-layout.js"></script>
    <script>
        // 权限配置页面脚本
        document.addEventListener('DOMContentLoaded', function() {
            // 更新页面标题
            updatePageTitle('权限配置');

            // 搜索功能
            const searchInput = document.getElementById('searchInput');
            searchInput.addEventListener('input', function() {
                const searchTerm = this.value.toLowerCase();
                const permissionItems = document.querySelectorAll('.permission-item');

                permissionItems.forEach(item => {
                    const name = item.querySelector('h4').textContent.toLowerCase();
                    const code = item.querySelector('.permission-code').textContent.toLowerCase();
                    const description = item.querySelector('.permission-description').textContent.toLowerCase();

                    if (name.includes(searchTerm) || code.includes(searchTerm) || description.includes(searchTerm)) {
                        item.style.display = 'block';
                    } else {
                        item.style.display = 'none';
                    }
                });
            });

            // 动画效果
            const cards = document.querySelectorAll('.module-card, .permission-item');
            cards.forEach((card, index) => {
                card.style.opacity = '0';
                card.style.transform = 'translateY(20px)';
                setTimeout(() => {
                    card.style.transition = 'all 0.5s ease';
                    card.style.opacity = '1';
                    card.style.transform = 'translateY(0)';
                }, index * 50);
            });

            // 表单提交处理
            document.getElementById('permissionForm').addEventListener('submit', function(e) {
                e.preventDefault();

                const formData = new FormData(this);
                const permissionName = formData.get('name');

                // 模拟保存
                alert(`权限 "${permissionName}" 已保存成功！`);
                closePermissionModal();

                // 刷新页面
                setTimeout(() => {
                    location.reload();
                }, 1000);
            });
        });

        function openPermissionModal(permissionId = null) {
            if (permissionId) {
                document.getElementById('modalTitle').textContent = '编辑权限';
                // 这里可以加载权限数据填充表单
            } else {
                document.getElementById('modalTitle').textContent = '新建权限';
                document.getElementById('permissionForm').reset();
            }
            document.getElementById('permissionModal').classList.add('show');
        }

        function closePermissionModal() {
            document.getElementById('permissionModal').classList.remove('show');
            document.getElementById('permissionForm').reset();
        }

        function editPermission(permissionId) {
            openPermissionModal(permissionId);
        }

        function deletePermission(permissionId) {
            if (confirm('确定要删除这个权限吗？\n\n删除后，拥有此权限的角色将失去相应功能。')) {
                alert('权限删除成功！');
                // 这里添加删除逻辑
                setTimeout(() => {
                    location.reload();
                }, 1000);
            }
        }

        // 点击模态框外部关闭
        document.getElementById('permissionModal').addEventListener('click', function(e) {
            if (e.target === this) {
                closePermissionModal();
            }
        });

        console.log('🔑 权限配置页面已加载');
    </script>
</body>
</html>
