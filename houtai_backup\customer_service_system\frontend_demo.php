<?php
// 前台实时消息演示页面
header('Content-Type: text/html; charset=UTF-8');
ini_set('default_charset', 'UTF-8');

$userId = $_GET['user_id'] ?? 4; // 默认用户ID
$sessionId = $_GET['session_id'] ?? '';

require_once '../db_config.php';

// 获取用户信息
$userName = '测试用户';
try {
    $pdo = getDbConnection();
    
    if ($sessionId) {
        $stmt = $pdo->prepare("SELECT * FROM customer_service_sessions WHERE session_id = ?");
        $stmt->execute([$sessionId]);
        $session = $stmt->fetch(PDO::FETCH_ASSOC);
        if ($session) {
            $userId = $session['user_id'];
            $userName = $session['user_name'];
        }
    }
} catch (Exception $e) {
    // 忽略错误，使用默认值
}
?>

<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>客服聊天 - 前台演示</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f5f5f5;
            height: 100vh;
            display: flex;
            flex-direction: column;
        }
        
        .header {
            background: #6F7BF5;
            color: white;
            padding: 15px 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .header h1 {
            font-size: 18px;
        }
        
        .status {
            font-size: 12px;
            opacity: 0.9;
        }
        
        .chat-container {
            flex: 1;
            display: flex;
            flex-direction: column;
            max-width: 800px;
            margin: 0 auto;
            background: white;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .messages {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
            background: #fafafa;
        }
        
        .message {
            margin: 10px 0;
            display: flex;
        }
        
        .message.user {
            justify-content: flex-end;
        }
        
        .message.cs {
            justify-content: flex-start;
        }
        
        .message-bubble {
            max-width: 70%;
            padding: 12px 16px;
            border-radius: 18px;
            word-wrap: break-word;
        }
        
        .message.user .message-bubble {
            background: #6F7BF5;
            color: white;
        }
        
        .message.cs .message-bubble {
            background: white;
            color: #333;
            border: 1px solid #e1e1e1;
        }
        
        .message-info {
            font-size: 11px;
            opacity: 0.7;
            margin-bottom: 4px;
        }
        
        .message-text {
            font-size: 14px;
            line-height: 1.4;
        }
        
        .input-area {
            padding: 20px;
            border-top: 1px solid #e1e1e1;
            background: white;
        }
        
        .input-group {
            display: flex;
            gap: 10px;
        }
        
        .message-input {
            flex: 1;
            padding: 12px 16px;
            border: 1px solid #ddd;
            border-radius: 25px;
            outline: none;
            font-size: 14px;
        }
        
        .send-btn {
            padding: 12px 24px;
            background: #6F7BF5;
            color: white;
            border: none;
            border-radius: 25px;
            cursor: pointer;
            font-size: 14px;
        }
        
        .send-btn:hover {
            background: #5a67d8;
        }
        
        .send-btn:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        
        .notification {
            position: fixed;
            top: 20px;
            right: 20px;
            background: #28a745;
            color: white;
            padding: 15px 20px;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.3);
            z-index: 1000;
            max-width: 300px;
            animation: slideIn 0.3s ease;
        }
        
        .notification.error {
            background: #dc3545;
        }
        
        .controls {
            padding: 10px 20px;
            background: #f8f9fa;
            border-bottom: 1px solid #e1e1e1;
            display: flex;
            gap: 10px;
            align-items: center;
            font-size: 12px;
        }
        
        .control-btn {
            padding: 5px 10px;
            background: #007cba;
            color: white;
            border: none;
            border-radius: 3px;
            cursor: pointer;
            font-size: 11px;
        }
        
        .control-btn.active {
            background: #28a745;
        }
        
        @keyframes slideIn {
            from { transform: translateX(100%); opacity: 0; }
            to { transform: translateX(0); opacity: 1; }
        }
        
        @media (max-width: 600px) {
            .chat-container {
                height: 100vh;
                margin: 0;
            }
            
            .header {
                padding: 10px 15px;
            }
            
            .messages {
                padding: 15px;
            }
            
            .input-area {
                padding: 15px;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <div>
            <h1>💬 客服聊天</h1>
            <div class="status">用户: <?php echo htmlspecialchars($userName); ?> (ID: <?php echo htmlspecialchars($userId); ?>)</div>
        </div>
        <div>
            <div id="connection-status">🔄 连接中...</div>
        </div>
    </div>
    
    <div class="chat-container">
        <div class="controls">
            <span>实时通信:</span>
            <button id="start-polling" class="control-btn">开始轮询</button>
            <button id="stop-polling" class="control-btn">停止轮询</button>
            <span id="poll-status">已停止</span>
            <span style="margin-left: 20px;">最后更新: <span id="last-update">未更新</span></span>
        </div>
        
        <div id="messages" class="messages">
            <div class="message cs">
                <div class="message-bubble">
                    <div class="message-info">系统消息</div>
                    <div class="message-text">欢迎使用客服系统！这是前台实时消息演示页面。</div>
                </div>
            </div>
        </div>
        
        <div class="input-area">
            <div class="input-group">
                <input type="text" id="message-input" class="message-input" placeholder="输入消息..." disabled>
                <button id="send-btn" class="send-btn" disabled>发送</button>
            </div>
            <div style="margin-top: 10px; font-size: 12px; color: #666;">
                注意: 这是演示页面，发送功能已禁用。请在客服后台发送消息测试接收功能。
            </div>
        </div>
    </div>
    
    <script>
        class CustomerServiceChat {
            constructor(userId, sessionId) {
                this.userId = userId;
                this.sessionId = sessionId;
                this.lastMessageId = 0;
                this.pollInterval = null;
                this.isPolling = false;
                
                this.messagesContainer = document.getElementById('messages');
                this.statusElement = document.getElementById('connection-status');
                this.pollStatusElement = document.getElementById('poll-status');
                this.lastUpdateElement = document.getElementById('last-update');
                
                this.bindEvents();
            }
            
            bindEvents() {
                document.getElementById('start-polling').addEventListener('click', () => {
                    this.startPolling();
                });
                
                document.getElementById('stop-polling').addEventListener('click', () => {
                    this.stopPolling();
                });
            }
            
            startPolling() {
                if (this.isPolling) return;
                
                this.isPolling = true;
                this.updateStatus('🟢 已连接', 'success');
                this.pollStatusElement.textContent = '轮询中...';
                document.getElementById('start-polling').classList.add('active');
                document.getElementById('stop-polling').classList.remove('active');
                
                // 立即检查一次
                this.checkNewMessages();
                
                // 每3秒检查一次
                this.pollInterval = setInterval(() => {
                    this.checkNewMessages();
                }, 3000);
            }
            
            stopPolling() {
                if (!this.isPolling) return;
                
                this.isPolling = false;
                this.updateStatus('🔴 已断开', 'error');
                this.pollStatusElement.textContent = '已停止';
                document.getElementById('start-polling').classList.remove('active');
                document.getElementById('stop-polling').classList.add('active');
                
                if (this.pollInterval) {
                    clearInterval(this.pollInterval);
                    this.pollInterval = null;
                }
            }
            
            async checkNewMessages() {
                try {
                    const url = `api/get_user_messages.php?user_id=${this.userId}&last_id=${this.lastMessageId}${this.sessionId ? '&session_id=' + this.sessionId : ''}`;
                    const response = await fetch(url);
                    const data = await response.json();
                    
                    if (data.success) {
                        // 更新最后更新时间
                        this.lastUpdateElement.textContent = new Date().toLocaleTimeString();
                        
                        // 显示新消息
                        if (data.messages && data.messages.length > 0) {
                            this.displayMessages(data.messages);
                            this.lastMessageId = data.latest_message_id;
                        }
                        
                        // 显示通知
                        if (data.notifications && data.notifications.length > 0) {
                            this.showNotifications(data.notifications);
                        }
                        
                        this.updateStatus('🟢 已连接', 'success');
                    } else {
                        this.updateStatus('⚠️ 错误: ' + data.error, 'error');
                    }
                } catch (error) {
                    console.error('检查新消息失败:', error);
                    this.updateStatus('❌ 连接失败', 'error');
                }
            }
            
            displayMessages(messages) {
                messages.forEach(message => {
                    this.addMessage(message);
                });
                
                // 滚动到底部
                this.messagesContainer.scrollTop = this.messagesContainer.scrollHeight;
            }
            
            addMessage(message) {
                const messageDiv = document.createElement('div');
                messageDiv.className = `message ${message.sender_type === 'customer_service' ? 'cs' : 'user'}`;
                
                const time = new Date(message.created_at).toLocaleTimeString();
                
                messageDiv.innerHTML = `
                    <div class="message-bubble">
                        <div class="message-info">${message.sender_name} - ${time}</div>
                        <div class="message-text">${this.escapeHtml(message.content)}</div>
                    </div>
                `;
                
                this.messagesContainer.appendChild(messageDiv);
            }
            
            showNotifications(notifications) {
                notifications.forEach(notification => {
                    this.showToast(notification.title, notification.message || '您有新消息');
                });
            }
            
            showToast(title, message, type = 'success') {
                const toast = document.createElement('div');
                toast.className = `notification ${type}`;
                toast.innerHTML = `
                    <div style="font-weight: bold; margin-bottom: 5px;">${title}</div>
                    <div>${message}</div>
                `;
                
                document.body.appendChild(toast);
                
                setTimeout(() => {
                    toast.remove();
                }, 4000);
            }
            
            updateStatus(text, type) {
                this.statusElement.textContent = text;
                this.statusElement.className = type;
            }
            
            escapeHtml(text) {
                const div = document.createElement('div');
                div.textContent = text;
                return div.innerHTML;
            }
        }
        
        // 初始化聊天
        document.addEventListener('DOMContentLoaded', function() {
            const userId = <?php echo json_encode($userId); ?>;
            const sessionId = <?php echo json_encode($sessionId); ?>;
            
            const chat = new CustomerServiceChat(userId, sessionId);
            
            // 页面卸载时停止轮询
            window.addEventListener('beforeunload', () => {
                chat.stopPolling();
            });
            
            // 显示使用说明
            setTimeout(() => {
                chat.showToast(
                    '使用说明', 
                    '点击"开始轮询"按钮开始接收消息，然后在客服后台发送消息测试！', 
                    'success'
                );
            }, 1000);
        });
    </script>
</body>
</html>
