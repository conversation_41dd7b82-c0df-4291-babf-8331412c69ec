<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>客服管理测试页面 - 趣玩星球后台</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="../assets/css/admin.css">
    <style>
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: var(--spacing-lg);
            margin-bottom: var(--spacing-2xl);
        }

        .stat-card {
            background: white;
            padding: var(--spacing-xl);
            border-radius: var(--radius-xl);
            box-shadow: var(--shadow-lg);
            text-align: center;
            border-left: 4px solid var(--primary-color);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .stat-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
        }

        .stat-card:hover {
            transform: translateY(-4px);
            box-shadow: var(--shadow-xl);
        }

        .stat-number {
            font-size: 2.5rem;
            font-weight: 700;
            color: var(--primary-color);
            margin-bottom: var(--spacing-sm);
            background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .stat-label {
            color: var(--gray-600);
            font-size: 0.875rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.05em;
        }

        .config-section {
            background: white;
            border-radius: var(--radius-xl);
            padding: var(--spacing-2xl);
            margin-bottom: var(--spacing-xl);
            box-shadow: var(--shadow-lg);
            border: 1px solid var(--gray-100);
            transition: all 0.3s ease;
        }

        .config-section:hover {
            box-shadow: var(--shadow-xl);
        }

        .config-section h3 {
            color: var(--gray-800);
            font-size: 1.25rem;
            font-weight: 700;
            margin-bottom: var(--spacing-lg);
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);
            padding-bottom: var(--spacing-md);
            border-bottom: 2px solid var(--gray-100);
        }

        .config-section h3 i {
            color: var(--primary-color);
            font-size: 1.125rem;
        }
    </style>
</head>
<body>
    <div class="admin-container">
        <!-- 侧边栏 -->
        <aside class="sidebar">
            <div class="sidebar-header">
                <div class="logo">
                    <div class="logo-icon">
                        <img src="https://s1.imagehub.cc/images/2025/05/26/2cdd63b39f96072fc60cd733027d2a30.png" alt="趣玩星球" class="logo-image">
                    </div>
                    <div class="logo-text">
                        <h2>趣玩星球</h2>
                        <p>管理后台</p>
                    </div>
                </div>
            </div>

            <nav class="sidebar-nav">
                <ul class="nav-list">
                    <li class="nav-item">
                        <a href="/houtai_backup/home.php" class="nav-link">
                            <i class="fas fa-home"></i>
                            <span class="nav-link-text">首页</span>
                        </a>
                    </li>
                    <li class="nav-item active">
                        <a href="/houtai_backup/customer_service/index.php" class="nav-link">
                            <i class="fas fa-headset"></i>
                            <span class="nav-link-text">客服管理</span>
                        </a>
                    </li>
                </ul>
            </nav>
        </aside>

        <!-- 主内容区域 -->
        <div class="main-content">
            <div class="content-wrapper">
                <div class="content-header">
                    <h1><i class="fas fa-headset"></i> 客服管理测试页面</h1>
                    <div class="header-actions">
                        <button class="btn btn-primary" onclick="showToast('机器人配置功能开发中！', 'info')">
                            <i class="fas fa-robot"></i>
                            机器人配置
                        </button>
                        <button class="btn btn-success" onclick="showToast('回复规则管理功能开发中！', 'info')">
                            <i class="fas fa-comments"></i>
                            回复规则
                        </button>
                    </div>
                </div>

                <!-- 统计卡片 -->
                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-number">156</div>
                        <div class="stat-label">今日对话数</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number">2,847</div>
                        <div class="stat-label">总对话数</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number">23</div>
                        <div class="stat-label">活跃会话</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number">8</div>
                        <div class="stat-label">人工客服</div>
                    </div>
                </div>

                <!-- 机器人配置 -->
                <div class="config-section">
                    <h3><i class="fas fa-robot"></i> 当前机器人配置</h3>
                    <div class="info-grid">
                        <div class="info-item">
                            <span class="label">机器人名称</span>
                            <span class="value">趣玩小助手</span>
                        </div>
                        <div class="info-item">
                            <span class="label">状态</span>
                            <span class="value">
                                <span class="status-badge success">启用</span>
                            </span>
                        </div>
                        <div class="info-item full-width">
                            <span class="label">欢迎消息</span>
                            <div class="value">您好！我是趣玩星球智能客服小助手🤖<br><br>我可以帮助您解决以下问题：<br>• 账号相关问题<br>• 功能使用指导<br>• 常见问题解答<br><br>请描述您遇到的问题，我会尽力为您解答！</div>
                        </div>
                    </div>
                </div>

                <!-- 测试按钮 -->
                <div class="config-section">
                    <h3><i class="fas fa-flask"></i> 功能测试</h3>
                    <div style="display: flex; gap: var(--spacing-md); flex-wrap: wrap;">
                        <button class="btn btn-primary" onclick="showToast('这是一个成功消息！', 'success')">
                            <i class="fas fa-check"></i>
                            成功消息
                        </button>
                        <button class="btn btn-warning" onclick="showToast('这是一个警告消息！', 'warning')">
                            <i class="fas fa-exclamation-triangle"></i>
                            警告消息
                        </button>
                        <button class="btn btn-danger" onclick="showToast('这是一个错误消息！', 'error')">
                            <i class="fas fa-times"></i>
                            错误消息
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Toast 通知函数
        function showToast(message, type = 'info') {
            const toast = document.createElement('div');
            toast.className = `toast ${type}`;
            toast.innerHTML = `
                <div style="display: flex; align-items: center; gap: 12px;">
                    <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'warning' ? 'exclamation-triangle' : type === 'error' ? 'times-circle' : 'info-circle'}"></i>
                    <span>${message}</span>
                    <button onclick="this.parentElement.parentElement.remove()" style="background: none; border: none; color: inherit; cursor: pointer; margin-left: auto;">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            `;
            
            document.body.appendChild(toast);
            
            // 自动移除
            setTimeout(() => {
                if (toast.parentElement) {
                    toast.remove();
                }
            }, 5000);
        }

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 添加统计卡片的动画效果
            const statCards = document.querySelectorAll('.stat-card');
            statCards.forEach((card, index) => {
                card.style.animationDelay = `${index * 0.1}s`;
                card.style.animation = 'fadeInUp 0.6s ease forwards';
            });

            showToast('客服管理页面样式修复完成！', 'success');
        });
    </script>
</body>
</html>
