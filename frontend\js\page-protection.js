// 页面防护JavaScript

(function() {
    'use strict';

    // 禁用右键菜单
    document.addEventListener('contextmenu', function(e) {
        e.preventDefault();
        e.stopPropagation();
        return false;
    }, true);

    // 禁用选择文本
    document.addEventListener('selectstart', function(e) {
        // 允许输入框选择文本
        if (e.target.tagName === 'INPUT' || e.target.tagName === 'TEXTAREA') {
            return true;
        }
        // 允许链接和按钮
        if (e.target.tagName === 'A' || e.target.tagName === 'BUTTON') {
            return true;
        }
        e.preventDefault();
        e.stopPropagation();
        return false;
    }, true);

    // 禁用拖拽
    document.addEventListener('dragstart', function(e) {
        e.preventDefault();
        e.stopPropagation();
        return false;
    }, true);

    // 禁用图片拖拽
    document.addEventListener('dragover', function(e) {
        e.preventDefault();
        e.stopPropagation();
        return false;
    }, true);

    document.addEventListener('drop', function(e) {
        e.preventDefault();
        e.stopPropagation();
        return false;
    }, true);

    // 禁用常用快捷键
    document.addEventListener('keydown', function(e) {
        // 禁用F12 (开发者工具)
        if (e.keyCode === 123) {
            e.preventDefault();
            e.stopPropagation();
            return false;
        }

        // 禁用Ctrl+Shift+I (开发者工具)
        if (e.ctrlKey && e.shiftKey && e.keyCode === 73) {
            e.preventDefault();
            e.stopPropagation();
            return false;
        }

        // 禁用Ctrl+Shift+J (控制台)
        if (e.ctrlKey && e.shiftKey && e.keyCode === 74) {
            e.preventDefault();
            e.stopPropagation();
            return false;
        }

        // 禁用Ctrl+U (查看源代码)
        if (e.ctrlKey && e.keyCode === 85) {
            e.preventDefault();
            e.stopPropagation();
            return false;
        }

        // 禁用Ctrl+S (保存页面)
        if (e.ctrlKey && e.keyCode === 83) {
            e.preventDefault();
            e.stopPropagation();
            return false;
        }

        // 禁用Ctrl+A (全选) - 除了输入框
        if (e.ctrlKey && e.keyCode === 65) {
            if (e.target.tagName !== 'INPUT' && e.target.tagName !== 'TEXTAREA') {
                e.preventDefault();
                e.stopPropagation();
                return false;
            }
        }

        // 禁用Ctrl+C (复制) - 除了输入框
        if (e.ctrlKey && e.keyCode === 67) {
            if (e.target.tagName !== 'INPUT' && e.target.tagName !== 'TEXTAREA') {
                e.preventDefault();
                e.stopPropagation();
                return false;
            }
        }

        // 禁用Ctrl+V (粘贴) - 除了输入框
        if (e.ctrlKey && e.keyCode === 86) {
            if (e.target.tagName !== 'INPUT' && e.target.tagName !== 'TEXTAREA') {
                e.preventDefault();
                e.stopPropagation();
                return false;
            }
        }

        // 禁用Ctrl+X (剪切) - 除了输入框
        if (e.ctrlKey && e.keyCode === 88) {
            if (e.target.tagName !== 'INPUT' && e.target.tagName !== 'TEXTAREA') {
                e.preventDefault();
                e.stopPropagation();
                return false;
            }
        }

        // 禁用Ctrl+P (打印)
        if (e.ctrlKey && e.keyCode === 80) {
            e.preventDefault();
            e.stopPropagation();
            return false;
        }

        // 禁用Ctrl+Shift+C (元素检查器)
        if (e.ctrlKey && e.shiftKey && e.keyCode === 67) {
            e.preventDefault();
            e.stopPropagation();
            return false;
        }
    }, true);

    // 禁用缩放
    document.addEventListener('wheel', function(e) {
        if (e.ctrlKey) {
            e.preventDefault();
            e.stopPropagation();
            return false;
        }
    }, { passive: false });

    // 禁用触摸缩放
    let lastTouchEnd = 0;
    document.addEventListener('touchstart', function(e) {
        // 允许单指触摸链接、按钮、输入框
        if (e.touches.length === 1) {
            const target = e.target;
            if (target.tagName === 'A' || target.tagName === 'BUTTON' ||
                target.tagName === 'INPUT' || target.tagName === 'TEXTAREA' ||
                target.closest('a') || target.closest('button')) {
                return true;
            }
        }

        if (e.touches.length > 1) {
            e.preventDefault();
            e.stopPropagation();
            return false;
        }
    }, { passive: false });

    document.addEventListener('touchmove', function(e) {
        if (e.touches.length > 1) {
            e.preventDefault();
            e.stopPropagation();
            return false;
        }
    }, { passive: false });

    document.addEventListener('touchend', function(e) {
        const now = (new Date()).getTime();
        if (now - lastTouchEnd <= 300) {
            e.preventDefault();
            e.stopPropagation();
            return false;
        }
        lastTouchEnd = now;
    }, false);

    // 禁用手势缩放
    document.addEventListener('gesturestart', function(e) {
        e.preventDefault();
        e.stopPropagation();
        return false;
    }, true);

    document.addEventListener('gesturechange', function(e) {
        e.preventDefault();
        e.stopPropagation();
        return false;
    }, true);

    document.addEventListener('gestureend', function(e) {
        e.preventDefault();
        e.stopPropagation();
        return false;
    }, true);

    // 防止页面被拖拽
    document.addEventListener('mousedown', function(e) {
        // 允许链接、按钮、输入框的鼠标事件
        if (e.target.tagName === 'A' || e.target.tagName === 'BUTTON' ||
            e.target.tagName === 'INPUT' || e.target.tagName === 'TEXTAREA' ||
            e.target.closest('a') || e.target.closest('button')) {
            return true;
        }

        if (e.target.tagName === 'IMG') {
            e.preventDefault();
            e.stopPropagation();
            return false;
        }
    }, true);

    // 禁用长按菜单 (移动端)
    document.addEventListener('touchstart', function(e) {
        // 只对图片禁用长按，允许链接和按钮正常工作
        if (e.target.tagName === 'IMG') {
            e.preventDefault();
        }
    }, { passive: false });

    // 监听页面可见性变化，防止开发者工具
    let devtools = false;
    setInterval(function() {
        if (window.outerHeight - window.innerHeight > 200 || window.outerWidth - window.innerWidth > 200) {
            if (!devtools) {
                devtools = true;
                console.clear();
                console.log('%c请勿尝试查看或修改页面代码！', 'color: red; font-size: 20px; font-weight: bold;');
            }
        } else {
            devtools = false;
        }
    }, 500);

    // 清空控制台
    if (typeof console !== 'undefined') {
        console.clear();
        console.log('%c趣玩星球', 'color: #40E0D0; font-size: 24px; font-weight: bold;');
        console.log('%c请勿在此处输入任何代码！', 'color: red; font-size: 16px;');
    }

    // 禁用调试器
    setInterval(function() {
        debugger;
    }, 100);

    // 重写console方法
    if (typeof console !== 'undefined') {
        const originalLog = console.log;
        console.log = function() {
            // 只允许特定的日志输出
            return;
        };

        console.warn = function() { return; };
        console.error = function() { return; };
        console.info = function() { return; };
        console.debug = function() { return; };
    }

    // 防止页面被嵌入iframe
    if (window.top !== window.self) {
        window.top.location = window.self.location;
    }

    // 设置viewport防止缩放
    let viewport = document.querySelector('meta[name="viewport"]');
    if (!viewport) {
        viewport = document.createElement('meta');
        viewport.name = 'viewport';
        document.head.appendChild(viewport);
    }
    viewport.content = 'width=device-width, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0, user-scalable=no, shrink-to-fit=no';

})();
