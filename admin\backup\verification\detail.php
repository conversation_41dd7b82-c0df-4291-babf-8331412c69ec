<?php
// 设置字符编码
header('Content-Type: text/html; charset=UTF-8');
ini_set('default_charset', 'UTF-8');

session_start();

// 检查登录状态
if (!isset($_SESSION['admin_id'])) {
    header('Location: login.php');
    exit;
}

// 直接引用当前目录的数据库配置文件
require_once 'db_config.php';

$id = intval($_GET['id'] ?? 0);
if (!$id) {
    header('Location: verification_list.php');
    exit;
}

try {
    $pdo = getDbConnection();

    // 获取认证详情
    $stmt = $pdo->prepare("
        SELECT
            rv.*,
            u.username,
            u.quwanplanet_id,
            u.phone,
            u.email,
            u.created_at as user_created_at,
            au.name as admin_name,
            au.employee_id as admin_employee_id
        FROM realname_verification rv
        LEFT JOIN users u ON rv.user_id = u.id
        LEFT JOIN admin_users au ON rv.verified_by = au.id
        WHERE rv.id = ?
    ");
    $stmt->execute([$id]);
    $verification = $stmt->fetch(PDO::FETCH_ASSOC);

    if (!$verification) {
        header('Location: verification_list.php');
        exit;
    }

} catch (PDOException $e) {
    $error_message = '数据库错误：' . $e->getMessage();
}

// 状态标签
function getStatusBadge($status) {
    switch ($status) {
        case 'pending':
            return '<span class="badge badge-warning"><i class="fas fa-clock"></i> 待审核</span>';
        case 'approved':
            return '<span class="badge badge-success"><i class="fas fa-check-circle"></i> 已通过</span>';
        case 'rejected':
            return '<span class="badge badge-danger"><i class="fas fa-times-circle"></i> 已拒绝</span>';
        default:
            return '<span class="badge badge-secondary">未知</span>';
    }
}
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>实名认证详情 - <?php echo htmlspecialchars($verification['real_name']); ?></title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #f5f6fa;
            color: #333;
        }

        .header {
            background: white;
            padding: 15px 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .header h1 {
            color: #333;
            font-size: 24px;
        }

        .back-btn {
            background: #3498db;
            color: white;
            padding: 8px 16px;
            border: none;
            border-radius: 6px;
            text-decoration: none;
            font-size: 14px;
            display: inline-flex;
            align-items: center;
            gap: 5px;
        }

        .container {
            max-width: 800px;
            margin: 20px auto;
            padding: 0 20px;
        }

        .detail-card {
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
            margin-bottom: 20px;
        }

        .card-header {
            background: #f8f9fa;
            padding: 20px;
            border-bottom: 1px solid #eee;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .card-header h2 {
            color: #333;
            font-size: 20px;
        }

        .card-body {
            padding: 20px;
        }

        .info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
        }

        .info-item {
            display: flex;
            flex-direction: column;
            gap: 5px;
        }

        .info-label {
            font-weight: 600;
            color: #555;
            font-size: 14px;
        }

        .info-value {
            font-size: 16px;
            color: #333;
        }

        .id-card {
            font-family: monospace;
            font-size: 18px;
            letter-spacing: 1px;
        }

        .badge {
            padding: 6px 12px;
            border-radius: 15px;
            font-size: 14px;
            font-weight: 500;
            display: inline-flex;
            align-items: center;
            gap: 5px;
        }

        .badge-warning {
            background: #fff3cd;
            color: #856404;
        }

        .badge-success {
            background: #d4edda;
            color: #155724;
        }

        .badge-danger {
            background: #f8d7da;
            color: #721c24;
        }

        .actions {
            display: flex;
            gap: 10px;
            margin-top: 20px;
        }

        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            font-size: 14px;
            font-weight: 500;
        }

        .btn-success {
            background: #27ae60;
            color: white;
        }

        .btn-danger {
            background: #e74c3c;
            color: white;
        }

        .btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.2);
        }

        .reason-section {
            background: #fff5f5;
            border: 1px solid #fed7d7;
            border-radius: 8px;
            padding: 15px;
            margin-top: 15px;
        }

        .reason-section h4 {
            color: #c53030;
            margin-bottom: 10px;
        }

        .timeline {
            border-left: 3px solid #3498db;
            padding-left: 20px;
            margin-top: 20px;
        }

        .timeline-item {
            margin-bottom: 15px;
            position: relative;
        }

        .timeline-item::before {
            content: '';
            position: absolute;
            left: -26px;
            top: 5px;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: #3498db;
        }

        .timeline-time {
            font-size: 12px;
            color: #666;
            margin-bottom: 5px;
        }

        .timeline-content {
            font-size: 14px;
            color: #333;
        }

        @media (max-width: 768px) {
            .info-grid {
                grid-template-columns: 1fr;
            }

            .actions {
                flex-direction: column;
            }

            .card-header {
                flex-direction: column;
                gap: 10px;
                align-items: flex-start;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1><i class="fas fa-user-check"></i> 实名认证详情</h1>
        <a href="verification_list.php" class="back-btn">
            <i class="fas fa-arrow-left"></i> 返回列表
        </a>
    </div>

    <div class="container">
        <!-- 基本信息 -->
        <div class="detail-card">
            <div class="card-header">
                <h2>基本信息</h2>
                <?php echo getStatusBadge($verification['verification_status']); ?>
            </div>
            <div class="card-body">
                <div class="info-grid">
                    <div class="info-item">
                        <div class="info-label">认证ID</div>
                        <div class="info-value"><?php echo $verification['id']; ?></div>
                    </div>

                    <div class="info-item">
                        <div class="info-label">趣玩ID</div>
                        <div class="info-value" style="font-weight: 600; color: #3498db;"><?php echo htmlspecialchars($verification['quwanplanet_id']); ?></div>
                    </div>

                    <div class="info-item">
                        <div class="info-label">用户名</div>
                        <div class="info-value"><?php echo htmlspecialchars($verification['username']); ?></div>
                    </div>

                    <div class="info-item">
                        <div class="info-label">手机号</div>
                        <div class="info-value"><?php echo htmlspecialchars($verification['phone'] ?? '-'); ?></div>
                    </div>

                    <div class="info-item">
                        <div class="info-label">邮箱</div>
                        <div class="info-value"><?php echo htmlspecialchars($verification['email'] ?? '-'); ?></div>
                    </div>

                    <div class="info-item">
                        <div class="info-label">真实姓名</div>
                        <div class="info-value"><?php echo htmlspecialchars($verification['real_name']); ?></div>
                    </div>

                    <div class="info-item">
                        <div class="info-label">身份证号</div>
                        <div class="info-value id-card"><?php echo htmlspecialchars($verification['id_card_number']); ?></div>
                    </div>

                    <div class="info-item">
                        <div class="info-label">提交时间</div>
                        <div class="info-value">
                            <?php
                            if ($verification['submitted_at'] && $verification['submitted_at'] !== '0000-00-00 00:00:00') {
                                echo date('Y年m月d日 H:i:s', strtotime($verification['submitted_at']));
                            } else {
                                echo '-';
                            }
                            ?>
                        </div>
                    </div>

                    <div class="info-item">
                        <div class="info-label">审核人员</div>
                        <div class="info-value">
                            <?php
                            if ($verification['admin_name']) {
                                echo htmlspecialchars($verification['admin_name']);
                                if ($verification['admin_employee_id']) {
                                    echo '（' . htmlspecialchars($verification['admin_employee_id']) . '）';
                                }
                            } else {
                                echo '未审核';
                            }
                            ?>
                        </div>
                    </div>
                </div>

                <?php if ($verification['verification_status'] === 'rejected' && !empty($verification['verification_reason'])): ?>
                    <div class="reason-section">
                        <h4><i class="fas fa-exclamation-triangle"></i> 拒绝原因</h4>
                        <p><?php echo htmlspecialchars($verification['verification_reason']); ?></p>
                    </div>
                <?php endif; ?>

                <!-- 操作按钮 -->
                <?php if ($verification['verification_status'] === 'pending'): ?>
                    <div class="actions">
                        <a href="verification_approve.php?id=<?php echo $verification['id']; ?>"
                           class="btn btn-success"
                           onclick="return confirm('确认通过此实名认证申请？')">
                            <i class="fas fa-check"></i> 通过认证
                        </a>
                        <a href="verification_reject.php?id=<?php echo $verification['id']; ?>"
                           class="btn btn-danger">
                            <i class="fas fa-times"></i> 拒绝认证
                        </a>
                    </div>
                <?php endif; ?>
            </div>
        </div>

        <!-- 操作时间线 -->
        <div class="detail-card">
            <div class="card-header">
                <h2>操作记录</h2>
            </div>
            <div class="card-body">
                <div class="timeline">
                    <?php if ($verification['submitted_at'] && $verification['submitted_at'] !== '0000-00-00 00:00:00'): ?>
                        <div class="timeline-item">
                            <div class="timeline-time">
                                <?php echo date('Y-m-d H:i:s', strtotime($verification['submitted_at'])); ?>
                            </div>
                            <div class="timeline-content">
                                <strong>用户提交实名认证申请</strong><br>
                                姓名：<?php echo htmlspecialchars($verification['real_name']); ?>
                            </div>
                        </div>
                    <?php endif; ?>

                    <?php if ($verification['verification_status'] === 'approved' && $verification['verified_at'] && $verification['verified_at'] !== '0000-00-00 00:00:00'): ?>
                        <div class="timeline-item">
                            <div class="timeline-time">
                                <?php echo date('Y-m-d H:i:s', strtotime($verification['verified_at'])); ?>
                            </div>
                            <div class="timeline-content">
                                <strong style="color: #27ae60;">认证审核通过</strong><br>
                                审核人员：<?php
                                if ($verification['admin_name']) {
                                    echo htmlspecialchars($verification['admin_name']);
                                    if ($verification['admin_employee_id']) {
                                        echo '（工号：' . htmlspecialchars($verification['admin_employee_id']) . '）';
                                    }
                                } else {
                                    echo '系统';
                                }
                                ?>
                            </div>
                        </div>
                    <?php elseif ($verification['verification_status'] === 'rejected'): ?>
                        <div class="timeline-item">
                            <div class="timeline-time">
                                <?php echo date('Y-m-d H:i:s'); ?>
                            </div>
                            <div class="timeline-content">
                                <strong style="color: #e74c3c;">认证审核被拒绝</strong><br>
                                审核人员：<?php
                                if ($verification['admin_name']) {
                                    echo htmlspecialchars($verification['admin_name']);
                                    if ($verification['admin_employee_id']) {
                                        echo '（工号：' . htmlspecialchars($verification['admin_employee_id']) . '）';
                                    }
                                } else {
                                    echo '系统';
                                }
                                ?><br>
                                <?php if (!empty($verification['verification_reason'])): ?>
                                    拒绝原因：<?php echo htmlspecialchars($verification['verification_reason']); ?>
                                <?php endif; ?>
                            </div>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
