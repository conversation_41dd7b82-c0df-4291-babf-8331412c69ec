<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="user-id" content="4">
    <title>前后台联动完整测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 16px;
            padding: 32px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        }

        .header {
            text-align: center;
            margin-bottom: 32px;
        }

        .header h1 {
            color: #667eea;
            margin: 0 0 8px 0;
            font-size: 2rem;
            font-weight: 700;
        }

        .test-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 24px;
            margin-bottom: 24px;
        }

        .test-section {
            background: #f8f9fa;
            border-radius: 12px;
            padding: 24px;
            border-left: 4px solid #667eea;
        }

        .test-section h3 {
            margin: 0 0 16px 0;
            color: #333;
            font-size: 1.2rem;
        }

        .test-btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-right: 12px;
            margin-bottom: 8px;
        }

        .test-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(102, 126, 234, 0.3);
        }

        .test-btn.secondary {
            background: #6c757d;
        }

        .test-btn.success {
            background: #28a745;
        }

        .test-btn.danger {
            background: #dc3545;
        }

        .status {
            margin-top: 16px;
            padding: 12px;
            border-radius: 8px;
            font-size: 14px;
        }

        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .status.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }

        .log-section {
            background: #2d3748;
            color: #e2e8f0;
            border-radius: 12px;
            padding: 20px;
            margin-top: 24px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            max-height: 400px;
            overflow-y: auto;
        }

        .log-section h4 {
            margin: 0 0 12px 0;
            color: #a0aec0;
        }

        .log-entry {
            margin-bottom: 8px;
            padding: 4px 0;
            border-bottom: 1px solid #4a5568;
        }

        .log-time {
            color: #68d391;
            margin-right: 8px;
        }

        .full-width {
            grid-column: 1 / -1;
        }

        input, select {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            margin: 4px 0 8px 0;
        }

        label {
            font-weight: 600;
            color: #555;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔔 前后台联动完整测试</h1>
            <p>测试验证码发送和实时通知功能</p>
        </div>

        <div class="test-grid">
            <!-- 1. 数据库检查 -->
            <div class="test-section">
                <h3>📊 数据库检查</h3>
                <button class="test-btn danger" onclick="fixDatabase()">修复数据库字段</button>
                <button class="test-btn" onclick="checkDatabase()">检查数据库</button>
                <button class="test-btn secondary" onclick="checkNotifications()">查看通知记录</button>
                <div id="dbStatus" class="status info">点击按钮检查数据库状态</div>
            </div>

            <!-- 2. WebSocket连接测试 -->
            <div class="test-section">
                <h3>🔗 WebSocket连接测试</h3>
                <label>用户ID:</label>
                <input type="number" id="userId" value="4" min="1">
                <button class="test-btn success" onclick="startWebSocket()">开始连接</button>
                <button class="test-btn danger" onclick="stopWebSocket()">断开连接</button>
                <div id="wsStatus" class="status info">等待连接...</div>
            </div>

            <!-- 3. 模拟发送验证码 -->
            <div class="test-section">
                <h3>📱 模拟发送验证码</h3>
                <label>手机号:</label>
                <input type="text" id="testPhone" value="13800138000">
                <label>验证码类型:</label>
                <select id="testType">
                    <option value="admin_send">管理员发送</option>
                    <option value="security_verify">安全验证</option>
                    <option value="system_notice">系统通知</option>
                </select>
                <label>备注:</label>
                <input type="text" id="testNote" value="测试验证码">
                <button class="test-btn" onclick="simulateSendCode()">模拟发送</button>
                <div id="sendStatus" class="status info">准备发送验证码</div>
            </div>

            <!-- 4. 前台通知测试 -->
            <div class="test-section">
                <h3>🎯 前台通知测试</h3>
                <button class="test-btn" onclick="testVerificationModal()">测试验证码弹窗</button>
                <button class="test-btn" onclick="testToastNotification()">测试Toast通知</button>
                <button class="test-btn" onclick="testErrorNotification()">测试错误通知</button>
                <div id="frontendStatus" class="status info">点击按钮测试前台功能</div>
            </div>
        </div>

        <!-- 日志区域 -->
        <div class="log-section">
            <h4>📝 测试日志</h4>
            <div id="logContainer"></div>
        </div>
    </div>

    <!-- WebSocket通知组件 -->
    <script src="components/websocket_notifications.js"></script>
    <script>
        // 设置测试用户ID
        window.currentUserId = 4;

        let notifications = null;
        let logContainer = document.getElementById('logContainer');

        // 添加日志
        function addLog(message, type = 'info') {
            const time = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.className = 'log-entry';
            logEntry.innerHTML = `<span class="log-time">[${time}]</span> ${message}`;
            logContainer.appendChild(logEntry);
            logContainer.scrollTop = logContainer.scrollHeight;

            console.log(`[${time}] ${message}`);
        }

        // 更新状态
        function updateStatus(elementId, message, type) {
            const element = document.getElementById(elementId);
            element.textContent = message;
            element.className = `status ${type}`;
        }

        // 修复数据库字段
        async function fixDatabase() {
            addLog('开始修复数据库字段...');
            updateStatus('dbStatus', '修复中...', 'info');

            try {
                const response = await fetch('api/fix_database_fields.php');
                const data = await response.json();

                if (data.success) {
                    addLog('数据库字段修复完成');
                    data.operations.forEach(operation => {
                        addLog(`- ${operation}`);
                    });
                    updateStatus('dbStatus', '数据库修复完成 ✅', 'success');
                } else {
                    addLog('数据库修复失败: ' + data.error);
                    updateStatus('dbStatus', '数据库修复失败 ❌', 'error');
                }
            } catch (error) {
                addLog('数据库修复请求失败: ' + error.message);
                updateStatus('dbStatus', '修复请求失败 ❌', 'error');
            }
        }

        // 检查数据库
        async function checkDatabase() {
            addLog('开始检查数据库...');
            updateStatus('dbStatus', '检查中...', 'info');

            try {
                const response = await fetch('api/check_notifications_proxy.php?user_id=4');
                const data = await response.json();

                if (data.success) {
                    addLog('数据库检查完成');
                    addLog(`验证码记录数: ${data.debug.stats.verification_codes_count}`);
                    addLog(`通知记录数: ${data.debug.stats.realtime_notifications_count}`);
                    addLog(`待推送通知数: ${data.debug.stats.pending_notifications_count}`);

                    updateStatus('dbStatus', '数据库检查完成 ✅', 'success');
                } else {
                    addLog('数据库检查失败: ' + data.error);
                    updateStatus('dbStatus', '数据库检查失败 ❌', 'error');
                }
            } catch (error) {
                addLog('数据库检查请求失败: ' + error.message);
                updateStatus('dbStatus', '请求失败 ❌', 'error');
            }
        }

        // 查看通知记录
        async function checkNotifications() {
            addLog('查询通知记录...');

            try {
                const response = await fetch('api/check_notifications_proxy.php?user_id=4');
                const data = await response.json();

                if (data.success) {
                    addLog('=== 最近的通知记录 ===');
                    data.data.realtime_notifications.forEach(notification => {
                        addLog(`ID:${notification.id} | 类型:${notification.type} | 状态:${notification.status} | 标题:${notification.title}`);
                    });
                    addLog('=== 记录查询完成 ===');
                }
            } catch (error) {
                addLog('查询通知记录失败: ' + error.message);
            }
        }

        // 开始WebSocket连接
        function startWebSocket() {
            const userId = document.getElementById('userId').value;
            addLog(`开始WebSocket连接，用户ID: ${userId}`);
            updateStatus('wsStatus', '连接中...', 'info');

            if (notifications) {
                notifications.disconnect();
            }

            notifications = new WebSocketNotifications(userId);
            notifications.connect();

            // 监听连接状态
            setTimeout(() => {
                if (notifications && notifications.isConnected) {
                    updateStatus('wsStatus', 'WebSocket连接成功 ✅', 'success');
                    addLog('WebSocket连接建立成功');
                } else {
                    updateStatus('wsStatus', 'WebSocket连接失败 ❌', 'error');
                    addLog('WebSocket连接建立失败');
                }
            }, 3000);
        }

        // 停止WebSocket连接
        function stopWebSocket() {
            if (notifications) {
                notifications.disconnect();
                notifications = null;
                updateStatus('wsStatus', 'WebSocket连接已断开', 'info');
                addLog('WebSocket连接已断开');
            }
        }

        // 模拟发送验证码
        async function simulateSendCode() {
            const userId = document.getElementById('userId').value;
            const phone = document.getElementById('testPhone').value;
            const type = document.getElementById('testType').value;
            const note = document.getElementById('testNote').value;

            addLog(`模拟发送验证码: 用户${userId}, 手机${phone}, 类型${type}`);
            updateStatus('sendStatus', '发送中...', 'info');

            const data = {
                user_id: parseInt(userId),
                phone: phone,
                type: type,
                note: note,
                expiry: 5
            };

            try {
                const response = await fetch('api/send_verification_code_proxy.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(data)
                });

                const result = await response.json();

                if (result.success) {
                    addLog('验证码发送成功！');
                    addLog(`验证码: ${result.data.code}`);
                    updateStatus('sendStatus', '发送成功 ✅', 'success');
                } else {
                    addLog('验证码发送失败: ' + result.message);
                    updateStatus('sendStatus', '发送失败 ❌', 'error');

                    if (result.debug) {
                        addLog('调试信息: ' + JSON.stringify(result.debug));
                    }
                }
            } catch (error) {
                addLog('发送请求失败: ' + error.message);
                updateStatus('sendStatus', '请求失败 ❌', 'error');
            }
        }

        // 测试验证码弹窗
        function testVerificationModal() {
            if (notifications) {
                const mockData = {
                    type: 'verification_code',
                    id: Date.now(),
                    title: '测试验证码',
                    content: '这是一个测试验证码：123456',
                    data: {
                        code: '123456',
                        expires_at: new Date(Date.now() + 5 * 60 * 1000).toISOString(),
                        admin_note: '这是测试备注',
                        sent_by: '测试管理员'
                    }
                };

                notifications.showVerificationCodeModal(mockData);
                addLog('测试验证码弹窗已显示');
                updateStatus('frontendStatus', '弹窗测试完成 ✅', 'success');
            } else {
                addLog('请先建立WebSocket连接');
                updateStatus('frontendStatus', '请先连接WebSocket ⚠️', 'error');
            }
        }

        // 测试Toast通知
        function testToastNotification() {
            if (notifications) {
                notifications.showToastNotification('测试标题', '这是一条测试Toast通知', 'info');
                addLog('测试Toast通知已显示');
                updateStatus('frontendStatus', 'Toast测试完成 ✅', 'success');
            } else {
                addLog('请先建立WebSocket连接');
                updateStatus('frontendStatus', '请先连接WebSocket ⚠️', 'error');
            }
        }

        // 测试错误通知
        function testErrorNotification() {
            if (notifications) {
                notifications.showToastNotification('错误提示', '这是一个测试错误消息', 'error');
                addLog('测试错误通知已显示');
                updateStatus('frontendStatus', '错误通知测试完成 ✅', 'success');
            } else {
                addLog('请先建立WebSocket连接');
                updateStatus('frontendStatus', '请先连接WebSocket ⚠️', 'error');
            }
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            addLog('测试页面加载完成');
            addLog('请按顺序进行测试：1.修复数据库 → 2.检查数据库 → 3.建立WebSocket连接 → 4.发送验证码 → 5.观察前台弹窗');
        });

        // 页面卸载时断开连接
        window.addEventListener('beforeunload', function() {
            if (notifications) {
                notifications.disconnect();
            }
        });
    </script>
</body>
</html>
