<template>
  <view id="app">
    <!-- 全局通知容器 -->
    <view id="notification-container" class="notification-container"></view>
    
    <!-- 全局加载遮罩 -->
    <view v-if="globalLoading" class="global-loading">
      <view class="loading-content">
        <view class="loading-star"></view>
        <text class="loading-text">{{ loadingText }}</text>
      </view>
    </view>
  </view>
</template>

<script>
import { ref, onMounted } from 'vue'
import { useUserStore } from '@/store/user'

export default {
  name: 'App',
  setup() {
    const userStore = useUserStore()
    const globalLoading = ref(false)
    const loadingText = ref('加载中...')

    // 全局加载控制
    const showGlobalLoading = (text = '加载中...') => {
      loadingText.value = text
      globalLoading.value = true
    }

    const hideGlobalLoading = () => {
      globalLoading.value = false
    }

    // 应用启动时的初始化
    onMounted(async () => {
      try {
        showGlobalLoading('初始化应用...')
        
        // 检查登录状态
        await userStore.checkLoginStatus()
        
        // 初始化uniCloud
        await initUniCloud()
        
        hideGlobalLoading()
      } catch (error) {
        console.error('应用初始化失败:', error)
        hideGlobalLoading()
      }
    })

    // 初始化uniCloud
    const initUniCloud = async () => {
      try {
        // 这里可以添加uniCloud的初始化逻辑
        console.log('uniCloud初始化完成')
      } catch (error) {
        console.error('uniCloud初始化失败:', error)
      }
    }

    // 全局方法挂载
    uni.$showGlobalLoading = showGlobalLoading
    uni.$hideGlobalLoading = hideGlobalLoading

    return {
      globalLoading,
      loadingText
    }
  }
}
</script>

<style lang="scss">
@import '@/uni.scss';

#app {
  height: 100vh;
  overflow: hidden;
}

/* 全局通知容器 */
.notification-container {
  position: fixed;
  top: 0;
  right: 0;
  z-index: 9999;
  padding: var(--spacing-md);
  pointer-events: none;
  max-width: 100vw;
}

/* 全局加载遮罩 */
.global-loading {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(5px);
  z-index: 10000;
  display: flex;
  align-items: center;
  justify-content: center;
}

.loading-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--spacing-md);
}

.loading-text {
  font-size: var(--font-md);
  color: var(--text-secondary);
  font-weight: 500;
}

/* 全局滚动条样式 */
::-webkit-scrollbar {
  width: 4px;
  height: 4px;
}

::-webkit-scrollbar-track {
  background: var(--bg-tertiary);
  border-radius: var(--radius-sm);
}

::-webkit-scrollbar-thumb {
  background: var(--border-color);
  border-radius: var(--radius-sm);
}

::-webkit-scrollbar-thumb:hover {
  background: var(--text-light);
}

/* 全局动画 */
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

.slide-up-enter-active,
.slide-up-leave-active {
  transition: all 0.3s ease;
}

.slide-up-enter-from {
  transform: translateY(100%);
  opacity: 0;
}

.slide-up-leave-to {
  transform: translateY(100%);
  opacity: 0;
}

.scale-enter-active,
.scale-leave-active {
  transition: all 0.2s ease;
}

.scale-enter-from,
.scale-leave-to {
  transform: scale(0.9);
  opacity: 0;
}

/* 安全区域适配 */
.safe-area-top {
  padding-top: constant(safe-area-inset-top);
  padding-top: env(safe-area-inset-top);
}

.safe-area-bottom {
  padding-bottom: constant(safe-area-inset-bottom);
  padding-bottom: env(safe-area-inset-bottom);
}

.safe-area-left {
  padding-left: constant(safe-area-inset-left);
  padding-left: env(safe-area-inset-left);
}

.safe-area-right {
  padding-right: constant(safe-area-inset-right);
  padding-right: env(safe-area-inset-right);
}

/* 禁用横向滚动 */
page {
  overflow-x: hidden;
}

/* 状态栏样式 */
.status-bar {
  height: var(--status-bar-height);
  background-color: var(--primary-color);
}

/* 导航栏样式 */
.custom-navbar {
  background-color: var(--primary-color);
  color: white;
  display: flex;
  align-items: center;
  padding: var(--spacing-sm) var(--spacing-md);
  height: 44px;
}

.navbar-title {
  flex: 1;
  text-align: center;
  font-size: var(--font-lg);
  font-weight: 600;
}

.navbar-back {
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: var(--radius-full);
  background: rgba(255, 255, 255, 0.1);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .notification-container {
    padding: var(--spacing-sm);
  }
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
  :root {
    --text-primary: #F7FAFC;
    --text-secondary: #E2E8F0;
    --text-light: #A0AEC0;
    --border-color: #4A5568;
    --bg-primary: #2D3748;
    --bg-secondary: #1A202C;
    --bg-tertiary: #2D3748;
  }
}
</style>
