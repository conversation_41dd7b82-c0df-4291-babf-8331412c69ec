/* 后台管理系统样式 */
:root {
    --primary-color: #40E0D0;
    --secondary-color: #36C5B0;
    --success-color: #28a745;
    --danger-color: #dc3545;
    --warning-color: #ffc107;
    --info-color: #17a2b8;
    --light-color: #f8f9fa;
    --dark-color: #343a40;
    --border-color: #dee2e6;
    --text-muted: #6c757d;
    --sidebar-width: 260px;
    --header-height: 70px;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background-color: #f5f6fa;
    color: #333;
    line-height: 1.6;
}

/* 管理员容器 */
.admin-container {
    display: flex;
    min-height: 100vh;
}

/* 侧边栏 */
.sidebar {
    width: var(--sidebar-width);
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    position: fixed;
    height: 100vh;
    overflow-y: auto;
    z-index: 1000;
    box-shadow: 2px 0 10px rgba(0, 0, 0, 0.1);
}

.sidebar-header {
    padding: 20px;
    text-align: center;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.sidebar-header h2 {
    font-size: 24px;
    margin-bottom: 5px;
}

.sidebar-header p {
    font-size: 14px;
    opacity: 0.8;
}

.sidebar-nav ul {
    list-style: none;
    padding: 20px 0;
}

.sidebar-nav li {
    margin-bottom: 5px;
}

.sidebar-nav a {
    display: flex;
    align-items: center;
    padding: 12px 20px;
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
    transition: all 0.3s ease;
    position: relative;
}

.sidebar-nav a:hover,
.sidebar-nav li.active a {
    background: rgba(255, 255, 255, 0.1);
    color: white;
}

.sidebar-nav a i {
    width: 20px;
    margin-right: 12px;
    font-size: 16px;
}

.sidebar-nav .badge {
    background: var(--danger-color);
    color: white;
    font-size: 12px;
    padding: 2px 6px;
    border-radius: 10px;
    margin-left: auto;
}

.sidebar-footer {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    padding: 20px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.admin-info {
    display: flex;
    align-items: center;
    margin-bottom: 15px;
}

.admin-avatar {
    width: 40px;
    height: 40px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 12px;
}

.admin-details .admin-name {
    font-weight: 600;
    margin-bottom: 2px;
}

.admin-details .admin-role {
    font-size: 12px;
    opacity: 0.8;
}

.logout-btn {
    display: flex;
    align-items: center;
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
    font-size: 14px;
    transition: color 0.3s ease;
}

.logout-btn:hover {
    color: white;
}

.logout-btn i {
    margin-right: 8px;
}

/* 主内容区 */
.main-content {
    flex: 1;
    margin-left: var(--sidebar-width);
    min-height: 100vh;
}

/* 顶部栏 */
.top-header {
    background: white;
    padding: 20px 30px;
    border-bottom: 1px solid var(--border-color);
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.header-left h1 {
    font-size: 28px;
    color: var(--dark-color);
    margin-bottom: 5px;
}

.header-left p {
    color: var(--text-muted);
    font-size: 14px;
}

.header-right {
    color: var(--text-muted);
    font-size: 14px;
}

/* 统计卡片 */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    padding: 30px;
}

.stat-card {
    background: white;
    border-radius: 12px;
    padding: 25px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.08);
    display: flex;
    align-items: center;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.12);
}

.stat-icon {
    width: 60px;
    height: 60px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 20px;
}

.stat-icon i {
    font-size: 24px;
}

.text-primary { color: var(--primary-color) !important; }
.text-success { color: var(--success-color) !important; }
.text-danger { color: var(--danger-color) !important; }
.text-warning { color: var(--warning-color) !important; }
.text-info { color: var(--info-color) !important; }

.stat-content h3 {
    font-size: 32px;
    font-weight: 700;
    color: var(--dark-color);
    margin-bottom: 5px;
}

.stat-content p {
    color: var(--text-muted);
    font-size: 14px;
}

.stat-action {
    margin-left: auto;
}

.stat-action a {
    color: var(--primary-color);
    text-decoration: none;
    font-size: 14px;
    font-weight: 500;
}

/* 快速操作 */
.quick-actions {
    padding: 0 30px 30px;
}

.quick-actions h2 {
    margin-bottom: 20px;
    color: var(--dark-color);
}

.action-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
}

.action-card {
    background: white;
    border-radius: 12px;
    padding: 25px;
    text-decoration: none;
    color: inherit;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.08);
    transition: all 0.3s ease;
    text-align: center;
}

.action-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.12);
    text-decoration: none;
    color: inherit;
}

.action-card i {
    font-size: 40px;
    color: var(--primary-color);
    margin-bottom: 15px;
}

.action-card h3 {
    font-size: 18px;
    margin-bottom: 10px;
    color: var(--dark-color);
}

.action-card p {
    color: var(--text-muted);
    font-size: 14px;
}

/* 警告消息 */
.alert {
    padding: 15px 20px;
    margin: 20px 30px;
    border-radius: 8px;
    display: flex;
    align-items: center;
}

.alert i {
    margin-right: 10px;
    font-size: 16px;
}

.alert-success {
    background: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.alert-error {
    background: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

/* 筛选区域 */
.filter-section {
    padding: 20px 30px;
    background: white;
    border-bottom: 1px solid var(--border-color);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.filter-tabs {
    display: flex;
    gap: 10px;
}

.filter-tab {
    padding: 8px 16px;
    border-radius: 20px;
    text-decoration: none;
    color: var(--text-muted);
    background: var(--light-color);
    transition: all 0.3s ease;
    font-size: 14px;
}

.filter-tab.active,
.filter-tab:hover {
    background: var(--primary-color);
    color: white;
    text-decoration: none;
}

.filter-info {
    color: var(--text-muted);
    font-size: 14px;
}

/* 认证列表 */
.verification-list {
    padding: 30px;
}

.verification-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
    gap: 20px;
}

.verification-card {
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.08);
    overflow: hidden;
    transition: transform 0.3s ease;
}

.verification-card:hover {
    transform: translateY(-2px);
}

.card-header {
    padding: 20px;
    border-bottom: 1px solid var(--border-color);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.user-info h3 {
    font-size: 18px;
    margin-bottom: 5px;
    color: var(--dark-color);
}

.user-info p {
    color: var(--text-muted);
    font-size: 14px;
}

.status-badge {
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 500;
}

.status-pending {
    background: #fff3cd;
    color: #856404;
}

.status-approved {
    background: #d4edda;
    color: #155724;
}

.status-rejected {
    background: #f8d7da;
    color: #721c24;
}

.card-content {
    padding: 20px;
}

.info-row {
    display: flex;
    margin-bottom: 10px;
}

.info-row .label {
    font-weight: 500;
    color: var(--text-muted);
    min-width: 80px;
}

.info-row .value {
    color: var(--dark-color);
}

.card-actions {
    padding: 20px;
    border-top: 1px solid var(--border-color);
    display: flex;
    gap: 10px;
}

/* 按钮 */
.btn {
    padding: 8px 16px;
    border: none;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 6px;
    transition: all 0.3s ease;
}

.btn-success {
    background: var(--success-color);
    color: white;
}

.btn-success:hover {
    background: #218838;
}

.btn-danger {
    background: var(--danger-color);
    color: white;
}

.btn-danger:hover {
    background: #c82333;
}

.btn-secondary {
    background: var(--text-muted);
    color: white;
}

.btn-secondary:hover {
    background: #5a6268;
}

/* 空状态 */
.empty-state {
    text-align: center;
    padding: 60px 20px;
    color: var(--text-muted);
}

.empty-state i {
    font-size: 48px;
    margin-bottom: 20px;
    opacity: 0.5;
}

.empty-state h3 {
    font-size: 20px;
    margin-bottom: 10px;
}

/* 分页 */
.pagination {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 20px;
    margin-top: 30px;
}

.page-btn {
    padding: 8px 16px;
    background: var(--primary-color);
    color: white;
    text-decoration: none;
    border-radius: 6px;
    font-size: 14px;
    transition: background 0.3s ease;
}

.page-btn:hover {
    background: var(--secondary-color);
    text-decoration: none;
    color: white;
}

.page-info {
    color: var(--text-muted);
    font-size: 14px;
}

/* 模态框 */
.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    z-index: 2000;
}

.modal.show {
    display: flex;
    align-items: center;
    justify-content: center;
}

.modal-content {
    background: white;
    border-radius: 12px;
    max-width: 500px;
    width: 90%;
    max-height: 80vh;
    overflow-y: auto;
}

.modal-header {
    padding: 20px;
    border-bottom: 1px solid var(--border-color);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-header h3 {
    margin: 0;
    color: var(--dark-color);
}

.modal-close {
    background: none;
    border: none;
    font-size: 24px;
    cursor: pointer;
    color: var(--text-muted);
}

.modal-body {
    padding: 20px;
}

.modal-footer {
    padding: 20px;
    border-top: 1px solid var(--border-color);
    display: flex;
    justify-content: flex-end;
    gap: 10px;
}

/* 表单 */
.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    color: var(--dark-color);
}

.form-group textarea {
    width: 100%;
    padding: 10px;
    border: 1px solid var(--border-color);
    border-radius: 6px;
    font-family: inherit;
    font-size: 14px;
    resize: vertical;
}

.form-group textarea:focus {
    outline: none;
    border-color: var(--primary-color);
}

/* 响应式设计 */
@media (max-width: 768px) {
    .sidebar {
        transform: translateX(-100%);
        transition: transform 0.3s ease;
    }
    
    .main-content {
        margin-left: 0;
    }
    
    .stats-grid,
    .action-grid {
        grid-template-columns: 1fr;
    }
    
    .verification-grid {
        grid-template-columns: 1fr;
    }
    
    .top-header {
        padding: 15px 20px;
    }
    
    .quick-actions,
    .verification-list {
        padding: 20px;
    }
}
