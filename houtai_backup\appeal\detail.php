<?php
/**
 * 申诉详情页面
 * 趣玩星球管理后台
 */

// 引入认证检查
require_once '../auth_check.php';

// 引入数据库配置
require_once '../db_config.php';

// 引入布局文件
require_once '../admin_layout.php';

// 获取申诉ID
$appeal_id = intval($_GET['id'] ?? 0);

if (!$appeal_id) {
    header('Location: index.php');
    exit;
}

try {
    $pdo = getDbConnection();

    // 获取申诉详情
    $stmt = $pdo->prepare("SELECT * FROM user_appeals WHERE id = ?");
    $stmt->execute([$appeal_id]);
    $appeal = $stmt->fetch(PDO::FETCH_ASSOC);

    if (!$appeal) {
        header('Location: index.php');
        exit;
    }

    // 获取申诉日志
    $log_stmt = $pdo->prepare("
        SELECT * FROM appeal_logs
        WHERE appeal_id = ?
        ORDER BY created_at DESC
    ");
    $log_stmt->execute([$appeal_id]);
    $logs = $log_stmt->fetchAll(PDO::FETCH_ASSOC);

    // 解析证明材料
    $evidence_files = [];
    if (!empty($appeal['evidence_files'])) {
        $evidence_files = json_decode($appeal['evidence_files'], true) ?: [];
    }

} catch (Exception $e) {
    $error_message = "数据加载失败：" . $e->getMessage();
    $appeal = null;
    $logs = [];
    $evidence_files = [];
}

// 状态和类型映射
$status_map = [
    'pending' => ['text' => '待处理', 'class' => 'warning'],
    'processing' => ['text' => '处理中', 'class' => 'info'],
    'approved' => ['text' => '已通过', 'class' => 'success'],
    'rejected' => ['text' => '已拒绝', 'class' => 'danger']
];

$type_map = [
    'wrongful_ban' => '误封申诉',
    'account_stolen' => '账号被盗',
    'system_error' => '系统错误',
    'other' => '其他原因'
];

$action_map = [
    'submit' => '提交申诉',
    'process' => '开始处理',
    'approve' => '通过申诉',
    'reject' => '拒绝申诉',
    'comment' => '添加备注'
];

startAdminPage('申诉详情', '申诉审核');
?>

<div class="admin-content">
    <!-- 页面头部 -->
    <div class="page-header">
        <div class="page-title">
            <h1><i class="fas fa-file-alt"></i> 申诉详情 #<?php echo $appeal['id']; ?></h1>
            <div class="breadcrumb">
                <a href="index.php">申诉审核</a> / 申诉详情
            </div>
        </div>
        <div class="page-actions">
            <a href="index.php" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> 返回列表
            </a>
            <?php if ($appeal && $appeal['status'] === 'pending'): ?>
                <button class="btn btn-success" onclick="processAppeal(<?php echo $appeal['id']; ?>, 'approve')">
                    <i class="fas fa-check"></i> 通过申诉
                </button>
                <button class="btn btn-danger" onclick="processAppeal(<?php echo $appeal['id']; ?>, 'reject')">
                    <i class="fas fa-times"></i> 拒绝申诉
                </button>
            <?php elseif ($appeal && $appeal['status'] === 'approved'): ?>
                <button class="btn btn-warning" onclick="forceSyncStatus(<?php echo $appeal['id']; ?>)">
                    <i class="fas fa-sync"></i> 强制同步状态
                </button>
            <?php endif; ?>
        </div>
    </div>

    <?php if ($appeal): ?>
        <div class="detail-container">
            <!-- 基本信息 -->
            <div class="detail-card">
                <div class="card-header">
                    <h3><i class="fas fa-info-circle"></i> 基本信息</h3>
                    <span class="status-badge status-<?php echo $status_map[$appeal['status']]['class']; ?>">
                        <?php echo $status_map[$appeal['status']]['text']; ?>
                    </span>
                </div>
                <div class="card-content">
                    <div class="info-grid">
                        <div class="info-item">
                            <label>申诉ID</label>
                            <span>#<?php echo $appeal['id']; ?></span>
                        </div>
                        <div class="info-item">
                            <label>手机号</label>
                            <span><?php echo htmlspecialchars($appeal['phone']); ?></span>
                        </div>
                        <div class="info-item">
                            <label>申诉类型</label>
                            <span class="type-badge">
                                <?php echo $type_map[$appeal['appeal_type']] ?? $appeal['appeal_type']; ?>
                            </span>
                        </div>
                        <div class="info-item">
                            <label>联系邮箱</label>
                            <span><?php echo htmlspecialchars($appeal['email']); ?></span>
                        </div>
                        <div class="info-item">
                            <label>提交时间</label>
                            <span><?php echo date('Y-m-d H:i:s', strtotime($appeal['created_at'])); ?></span>
                        </div>
                        <?php if ($appeal['processed_at']): ?>
                            <div class="info-item">
                                <label>处理时间</label>
                                <span><?php echo date('Y-m-d H:i:s', strtotime($appeal['processed_at'])); ?></span>
                            </div>
                        <?php endif; ?>
                        <?php if ($appeal['admin_name']): ?>
                            <div class="info-item">
                                <label>处理管理员</label>
                                <span><?php echo htmlspecialchars($appeal['admin_name']); ?></span>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>

            <!-- 申诉内容 -->
            <div class="detail-card">
                <div class="card-header">
                    <h3><i class="fas fa-comment-alt"></i> 申诉内容</h3>
                </div>
                <div class="card-content">
                    <div class="reason-content">
                        <?php echo nl2br(htmlspecialchars($appeal['reason'])); ?>
                    </div>
                </div>
            </div>

            <!-- 证明材料 -->
            <?php if (!empty($evidence_files)): ?>
                <div class="detail-card">
                    <div class="card-header">
                        <h3><i class="fas fa-paperclip"></i> 证明材料</h3>
                    </div>
                    <div class="card-content">
                        <div class="evidence-grid">
                            <?php foreach ($evidence_files as $file): ?>
                                <div class="evidence-item">
                                    <img src="../../uploads/appeals/<?php echo htmlspecialchars($file['filename']); ?>"
                                         alt="证明材料"
                                         onclick="showImageModal(this.src)">
                                    <div class="evidence-info">
                                        <div class="file-name"><?php echo htmlspecialchars($file['original_name']); ?></div>
                                        <div class="file-size"><?php echo formatFileSize($file['size']); ?></div>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    </div>
                </div>
            <?php endif; ?>

            <!-- 管理员回复 -->
            <?php if ($appeal['admin_reply']): ?>
                <div class="detail-card">
                    <div class="card-header">
                        <h3><i class="fas fa-reply"></i> 管理员回复</h3>
                    </div>
                    <div class="card-content">
                        <div class="reply-content">
                            <?php echo nl2br(htmlspecialchars($appeal['admin_reply'])); ?>
                        </div>
                    </div>
                </div>
            <?php endif; ?>

            <!-- 操作日志 -->
            <div class="detail-card">
                <div class="card-header">
                    <h3><i class="fas fa-history"></i> 操作日志</h3>
                </div>
                <div class="card-content">
                    <div class="log-timeline">
                        <?php foreach ($logs as $log): ?>
                            <div class="log-item">
                                <div class="log-time">
                                    <?php echo date('Y-m-d H:i:s', strtotime($log['created_at'])); ?>
                                </div>
                                <div class="log-content">
                                    <div class="log-action">
                                        <?php echo $action_map[$log['action']] ?? $log['action']; ?>
                                    </div>
                                    <?php if ($log['description']): ?>
                                        <div class="log-description">
                                            <?php echo htmlspecialchars($log['description']); ?>
                                        </div>
                                    <?php endif; ?>
                                    <?php if ($log['admin_name']): ?>
                                        <div class="log-admin">
                                            操作人：<?php echo htmlspecialchars($log['admin_name']); ?>
                                        </div>
                                    <?php endif; ?>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                </div>
            </div>
        </div>
    <?php else: ?>
        <div class="error-message">
            <i class="fas fa-exclamation-triangle"></i>
            申诉记录不存在或已被删除
        </div>
    <?php endif; ?>
</div>

<!-- 图片查看模态框 -->
<div id="imageModal" class="image-modal" onclick="closeImageModal()">
    <div class="modal-content">
        <img id="modalImage" src="" alt="证明材料">
        <button class="modal-close" onclick="closeImageModal()">
            <i class="fas fa-times"></i>
        </button>
    </div>
</div>

<style>
/* 申诉详情页面样式 */
.detail-container {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.detail-card {
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

.card-header {
    background: #F8FAFC;
    padding: 20px;
    border-bottom: 1px solid #E5E7EB;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.card-header h3 {
    margin: 0;
    font-size: 16px;
    font-weight: 600;
    color: #1F2937;
    display: flex;
    align-items: center;
    gap: 8px;
}

.card-content {
    padding: 20px;
}

.info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
}

.info-item {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.info-item label {
    font-size: 14px;
    font-weight: 600;
    color: #6B7280;
}

.info-item span {
    font-size: 16px;
    color: #1F2937;
}

.reason-content, .reply-content {
    background: #F9FAFB;
    border: 1px solid #E5E7EB;
    border-radius: 8px;
    padding: 16px;
    line-height: 1.6;
    color: #374151;
}

.evidence-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 16px;
}

.evidence-item {
    border: 1px solid #E5E7EB;
    border-radius: 8px;
    overflow: hidden;
    cursor: pointer;
    transition: transform 0.2s;
}

.evidence-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.evidence-item img {
    width: 100%;
    height: 150px;
    object-fit: cover;
}

.evidence-info {
    padding: 12px;
}

.file-name {
    font-size: 14px;
    font-weight: 500;
    color: #1F2937;
    margin-bottom: 4px;
}

.file-size {
    font-size: 12px;
    color: #6B7280;
}

.log-timeline {
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.log-item {
    display: flex;
    gap: 16px;
    padding: 16px;
    background: #F9FAFB;
    border-radius: 8px;
}

.log-time {
    font-size: 14px;
    color: #6B7280;
    white-space: nowrap;
    min-width: 140px;
}

.log-content {
    flex: 1;
}

.log-action {
    font-weight: 600;
    color: #1F2937;
    margin-bottom: 4px;
}

.log-description {
    color: #6B7280;
    margin-bottom: 4px;
}

.log-admin {
    font-size: 12px;
    color: #9CA3AF;
}

.image-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    display: none;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

.modal-content {
    position: relative;
    max-width: 90%;
    max-height: 90%;
}

.modal-content img {
    max-width: 100%;
    max-height: 100%;
    border-radius: 8px;
}

.modal-close {
    position: absolute;
    top: -40px;
    right: 0;
    background: white;
    border: none;
    border-radius: 50%;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
}

.type-badge {
    background: linear-gradient(135deg, #6F7BF5, #5A67D8);
    color: white;
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 13px;
    font-weight: 600;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
    box-shadow: 0 2px 4px rgba(111, 123, 245, 0.3);
    display: inline-block;
    min-width: 60px;
    text-align: center;
}

.status-badge {
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 13px;
    font-weight: 600;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
    display: inline-block;
    min-width: 60px;
    text-align: center;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.status-warning {
    background: linear-gradient(135deg, #F59E0B, #D97706);
    color: white;
}
.status-info {
    background: linear-gradient(135deg, #3B82F6, #2563EB);
    color: white;
}
.status-success {
    background: linear-gradient(135deg, #10B981, #059669);
    color: white;
}
.status-danger {
    background: linear-gradient(135deg, #EF4444, #DC2626);
    color: white;
}

@media (max-width: 768px) {
    .page-header {
        flex-direction: column;
        gap: 16px;
    }

    .page-actions {
        width: 100%;
        justify-content: center;
    }

    .info-grid {
        grid-template-columns: 1fr;
    }

    .evidence-grid {
        grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
    }

    .log-item {
        flex-direction: column;
        gap: 8px;
    }

    .log-time {
        min-width: auto;
    }
}
</style>

<script>
function processAppeal(appealId, action) {
    const actionText = action === 'approve' ? '通过' : '拒绝';
    const confirmText = `确定要${actionText}这个申诉吗？`;

    if (confirm(confirmText)) {
        window.location.href = `process.php?id=${appealId}&action=${action}`;
    }
}

function forceSyncStatus(appealId) {
    if (confirm('确定要强制同步用户状态吗？这将确保申诉通过后用户账号完全解封。')) {
        // 显示加载提示
        const button = event.target;
        const originalText = button.innerHTML;
        button.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 同步中...';
        button.disabled = true;

        fetch(`force_sync.php?id=${appealId}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('状态同步成功！用户账号已完全解封。');
                window.location.reload();
            } else {
                alert('同步失败：' + data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('同步操作失败，请稍后重试');
        })
        .finally(() => {
            button.innerHTML = originalText;
            button.disabled = false;
        });
    }
}

function showImageModal(src) {
    document.getElementById('modalImage').src = src;
    document.getElementById('imageModal').style.display = 'flex';
}

function closeImageModal() {
    document.getElementById('imageModal').style.display = 'none';
}

// ESC键关闭模态框
document.addEventListener('keydown', function(e) {
    if (e.key === 'Escape') {
        closeImageModal();
    }
});
</script>

<?php
// 文件大小格式化函数
function formatFileSize($bytes) {
    if ($bytes >= 1048576) {
        return number_format($bytes / 1048576, 2) . ' MB';
    } elseif ($bytes >= 1024) {
        return number_format($bytes / 1024, 2) . ' KB';
    } else {
        return $bytes . ' B';
    }
}

endAdminPage();
?>
