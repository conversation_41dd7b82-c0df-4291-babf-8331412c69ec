<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="user-id" content="4">
    <title>实时通知演示 - 趣玩星球</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .header {
            text-align: center;
            color: white;
            margin-bottom: 40px;
        }
        
        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
            text-shadow: 0 2px 4px rgba(0,0,0,0.3);
        }
        
        .header p {
            font-size: 1.1rem;
            opacity: 0.9;
        }
        
        .demo-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 40px;
        }
        
        .demo-card {
            background: white;
            border-radius: 16px;
            padding: 24px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }
        
        .demo-card:hover {
            transform: translateY(-5px);
        }
        
        .demo-card h3 {
            color: #667eea;
            margin-bottom: 16px;
            font-size: 1.3rem;
        }
        
        .demo-card p {
            color: #666;
            margin-bottom: 20px;
            line-height: 1.6;
        }
        
        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            width: 100%;
            margin-bottom: 10px;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(102, 126, 234, 0.3);
        }
        
        .btn.secondary {
            background: #6c757d;
        }
        
        .btn.success {
            background: #28a745;
        }
        
        .btn.warning {
            background: #ffc107;
            color: #333;
        }
        
        .btn.danger {
            background: #dc3545;
        }
        
        .status-panel {
            background: white;
            border-radius: 16px;
            padding: 24px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        
        .status-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px 0;
            border-bottom: 1px solid #eee;
        }
        
        .status-item:last-child {
            border-bottom: none;
        }
        
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        
        .status-indicator.online {
            background: #28a745;
            animation: pulse 2s infinite;
        }
        
        .status-indicator.offline {
            background: #dc3545;
        }
        
        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }
        
        .user-info {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 16px;
            padding: 24px;
            text-align: center;
            margin-bottom: 20px;
        }
        
        .user-avatar {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            background: rgba(255,255,255,0.2);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2rem;
            margin: 0 auto 16px;
        }
        
        .instructions {
            background: #f8f9fa;
            border-left: 4px solid #667eea;
            padding: 20px;
            border-radius: 0 8px 8px 0;
            margin-bottom: 20px;
        }
        
        .instructions h4 {
            color: #667eea;
            margin-bottom: 12px;
        }
        
        .instructions ol {
            margin-left: 20px;
        }
        
        .instructions li {
            margin-bottom: 8px;
            line-height: 1.5;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔔 实时通知演示</h1>
            <p>体验前后台联动的WebSocket实时通知功能</p>
        </div>
        
        <!-- 用户信息 -->
        <div class="user-info">
            <div class="user-avatar">👤</div>
            <h3>演示用户</h3>
            <p>用户ID: 4 | 手机号: 13800138000</p>
            <small>当后台管理员发送验证码时，您将实时收到弹窗通知</small>
        </div>
        
        <!-- 使用说明 -->
        <div class="instructions">
            <h4>📋 使用说明</h4>
            <ol>
                <li>确保WebSocket通知服务已连接（查看下方状态面板）</li>
                <li>打开后台管理页面：<code>https://vansmrz.vancrest.xyz/houtai_backup/user_management/send_verification_code_page.php</code></li>
                <li>在后台搜索并选择用户ID为4的用户</li>
                <li>填写验证码信息并发送</li>
                <li>观察本页面是否实时弹出验证码通知</li>
            </ol>
        </div>
        
        <!-- 状态面板 -->
        <div class="status-panel">
            <h3>📊 连接状态</h3>
            <div class="status-item">
                <span>WebSocket连接</span>
                <span id="wsStatus">
                    <span class="status-indicator offline"></span>
                    检查中...
                </span>
            </div>
            <div class="status-item">
                <span>用户ID</span>
                <span id="userIdStatus">4</span>
            </div>
            <div class="status-item">
                <span>最后轮询时间</span>
                <span id="lastPollTime">未开始</span>
            </div>
            <div class="status-item">
                <span>收到通知数量</span>
                <span id="notificationCount">0</span>
            </div>
        </div>
        
        <!-- 功能演示 -->
        <div class="demo-grid">
            <div class="demo-card">
                <h3>🔗 连接控制</h3>
                <p>手动控制WebSocket连接状态</p>
                <button class="btn" onclick="connectNotifications()">连接通知服务</button>
                <button class="btn secondary" onclick="disconnectNotifications()">断开连接</button>
                <button class="btn success" onclick="checkStatus()">检查状态</button>
            </div>
            
            <div class="demo-card">
                <h3>🧪 测试通知</h3>
                <p>发送测试通知，验证功能是否正常</p>
                <button class="btn" onclick="testVerificationModal()">测试验证码弹窗</button>
                <button class="btn warning" onclick="testToastNotification()">测试Toast通知</button>
                <button class="btn danger" onclick="testErrorNotification()">测试错误通知</button>
            </div>
            
            <div class="demo-card">
                <h3>📱 模拟发送</h3>
                <p>模拟后台发送验证码（用于测试）</p>
                <button class="btn" onclick="simulateSendCode()">模拟发送验证码</button>
                <button class="btn secondary" onclick="checkDatabase()">查看数据库记录</button>
                <button class="btn success" onclick="resetNotifications()">重置通知状态</button>
            </div>
            
            <div class="demo-card">
                <h3>⚙️ 调试工具</h3>
                <p>调试和监控工具</p>
                <button class="btn" onclick="openDebugTool()">打开调试工具</button>
                <button class="btn secondary" onclick="viewConsole()">查看控制台</button>
                <button class="btn warning" onclick="clearNotifications()">清空通知</button>
            </div>
        </div>
    </div>

    <!-- 引入实时通知集成脚本 -->
    <script src="js/realtime_notifications_integration.js"></script>
    
    <script>
        let notificationCount = 0;
        
        // 更新状态显示
        function updateStatus() {
            const wsStatus = document.getElementById('wsStatus');
            const lastPollTime = document.getElementById('lastPollTime');
            
            if (window.realtimeNotifications && window.realtimeNotifications.isConnected) {
                wsStatus.innerHTML = '<span class="status-indicator online"></span>已连接';
            } else {
                wsStatus.innerHTML = '<span class="status-indicator offline"></span>未连接';
            }
            
            lastPollTime.textContent = new Date().toLocaleTimeString();
        }
        
        // 连接通知服务
        function connectNotifications() {
            window.RealtimeNotifications.connect();
            setTimeout(updateStatus, 1000);
        }
        
        // 断开连接
        function disconnectNotifications() {
            window.RealtimeNotifications.disconnect();
            updateStatus();
        }
        
        // 检查状态
        function checkStatus() {
            updateStatus();
            const isConnected = window.RealtimeNotifications.isConnected();
            alert(`连接状态: ${isConnected ? '已连接' : '未连接'}`);
        }
        
        // 测试验证码弹窗
        function testVerificationModal() {
            if (window.realtimeNotifications) {
                const mockData = {
                    type: 'verification_code',
                    title: '测试验证码',
                    content: '这是一个测试验证码：123456',
                    data: JSON.stringify({
                        code: '123456',
                        expires_at: new Date(Date.now() + 5 * 60 * 1000).toISOString(),
                        admin_note: '这是测试备注信息',
                        sent_by: '测试管理员'
                    })
                };
                
                window.realtimeNotifications.showVerificationCodeModal(mockData);
                notificationCount++;
                document.getElementById('notificationCount').textContent = notificationCount;
            } else {
                alert('请先连接通知服务');
            }
        }
        
        // 测试Toast通知
        function testToastNotification() {
            window.RealtimeNotifications.sendTestNotification('测试标题', '这是一条测试Toast通知', 'info');
            notificationCount++;
            document.getElementById('notificationCount').textContent = notificationCount;
        }
        
        // 测试错误通知
        function testErrorNotification() {
            window.RealtimeNotifications.sendTestNotification('错误提示', '这是一个测试错误消息', 'error');
            notificationCount++;
            document.getElementById('notificationCount').textContent = notificationCount;
        }
        
        // 模拟发送验证码
        async function simulateSendCode() {
            try {
                const response = await fetch('api/send_verification_code_proxy.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        user_id: 4,
                        phone: '13800138000',
                        type: 'admin_send',
                        note: '前台演示页面测试',
                        expiry: 5
                    })
                });
                
                const data = await response.json();
                
                if (data.success) {
                    alert(`验证码发送成功！验证码：${data.data.code}`);
                } else {
                    alert('发送失败：' + data.message);
                }
            } catch (error) {
                alert('发送失败：' + error.message);
            }
        }
        
        // 查看数据库记录
        async function checkDatabase() {
            try {
                const response = await fetch('api/check_notifications_proxy.php?user_id=4');
                const data = await response.json();
                
                if (data.success) {
                    const notifications = data.data.realtime_notifications || [];
                    let message = `数据库中共有 ${notifications.length} 条通知记录：\n\n`;
                    
                    notifications.slice(0, 5).forEach((notification, index) => {
                        message += `${index + 1}. ${notification.title} (${notification.status})\n`;
                    });
                    
                    alert(message);
                } else {
                    alert('查询失败：' + data.error);
                }
            } catch (error) {
                alert('查询失败：' + error.message);
            }
        }
        
        // 重置通知状态
        async function resetNotifications() {
            try {
                const response = await fetch('api/reset_notifications.php?user_id=4', {
                    method: 'POST'
                });
                const data = await response.json();
                
                if (data.success) {
                    alert('通知状态重置成功');
                } else {
                    alert('重置失败：' + data.error);
                }
            } catch (error) {
                alert('重置失败：' + error.message);
            }
        }
        
        // 打开调试工具
        function openDebugTool() {
            window.open('test_websocket_debug.html', '_blank');
        }
        
        // 查看控制台
        function viewConsole() {
            alert('请按F12打开开发者工具，查看Console标签页中的日志信息');
        }
        
        // 清空通知
        function clearNotifications() {
            const containers = document.querySelectorAll('#notification-container .toast, .verification-modal');
            containers.forEach(container => container.remove());
            notificationCount = 0;
            document.getElementById('notificationCount').textContent = notificationCount;
        }
        
        // 监听通知事件
        document.addEventListener('DOMContentLoaded', function() {
            // 定期更新状态
            setInterval(updateStatus, 5000);
            
            // 初始状态更新
            setTimeout(updateStatus, 2000);
            
            console.log('实时通知演示页面加载完成');
        });
        
        // 监听通知接收事件（如果有的话）
        window.addEventListener('notification-received', function(event) {
            notificationCount++;
            document.getElementById('notificationCount').textContent = notificationCount;
            updateStatus();
        });
    </script>
</body>
</html>
