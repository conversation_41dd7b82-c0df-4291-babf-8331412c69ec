/**
 * 搜索功能JavaScript
 * 处理搜索页面的交互逻辑
 */

class SearchManager {
    constructor() {
        this.searchOverlayPage = document.getElementById('search-overlay-page');
        this.searchOverlayInput = document.getElementById('search-overlay-input');
        this.searchOverlayBackBtn = document.getElementById('search-overlay-back-btn');
        this.searchOverlayCancelBtn = document.getElementById('search-overlay-cancel-btn');
        this.hotKeywordsList = document.getElementById('hot-keywords-list');
        this.trendingList = document.getElementById('trending-list');
        this.historySection = document.getElementById('search-history-section');
        this.historyList = document.getElementById('history-list');
        this.clearHistoryBtn = document.getElementById('clear-history-btn');

        // 新增的元素
        this.searchLoadingOverlay = document.getElementById('search-loading-overlay');
        this.searchResultsPage = document.getElementById('search-results-page');
        this.searchResultsBackBtn = document.getElementById('search-results-back-btn');
        this.searchResultsKeyword = document.getElementById('search-results-keyword');
        this.searchResultsContent = document.getElementById('search-results-content');

        this.init();
    }

    init() {
        this.bindEvents();
        this.loadInitialData();
    }

    bindEvents() {
        // 搜索框点击事件
        const mainSearchInput = document.getElementById('main-search-input');
        if (mainSearchInput) {
            mainSearchInput.addEventListener('click', () => {
                this.openSearchOverlay();
            });
        }

        // 关闭搜索页面
        if (this.searchOverlayBackBtn) {
            this.searchOverlayBackBtn.addEventListener('click', () => {
                this.closeSearchOverlay();
            });
        }

        if (this.searchOverlayCancelBtn) {
            this.searchOverlayCancelBtn.addEventListener('click', () => {
                this.closeSearchOverlay();
            });
        }

        // 搜索输入框事件
        if (this.searchOverlayInput) {
            this.searchOverlayInput.addEventListener('keypress', (e) => {
                if (e.key === 'Enter') {
                    this.performSearch(this.searchOverlayInput.value.trim());
                }
            });
        }

        // 清空历史记录
        if (this.clearHistoryBtn) {
            this.clearHistoryBtn.addEventListener('click', () => {
                this.clearSearchHistory();
            });
        }

        // 搜索结果页面返回按钮
        if (this.searchResultsBackBtn) {
            this.searchResultsBackBtn.addEventListener('click', () => {
                this.closeSearchResults();
            });
        }
    }

    openSearchOverlay() {
        if (this.searchOverlayPage) {
            this.searchOverlayPage.classList.add('active');
            document.body.style.overflow = 'hidden';

            // 延迟聚焦，确保页面完全显示
            setTimeout(() => {
                if (this.searchOverlayInput) {
                    this.searchOverlayInput.focus();
                }
            }, 100);

            // 加载搜索历史
            this.loadSearchHistory();
        }
    }

    closeSearchOverlay() {
        if (this.searchOverlayPage) {
            this.searchOverlayPage.classList.remove('active');
            document.body.style.overflow = '';

            // 清空搜索框
            if (this.searchOverlayInput) {
                this.searchOverlayInput.value = '';
            }
        }
    }

    async loadInitialData() {
        await Promise.all([
            this.loadHotKeywords(),
            this.loadTrendingList()
        ]);
    }

    async loadHotKeywords() {
        try {
            const response = await fetch('api/search_api.php?action=get_hot_keywords');
            const result = await response.json();

            if (result.success && result.data) {
                this.renderHotKeywords(result.data);
            }
        } catch (error) {
            console.error('加载热门搜索失败:', error);
        }
    }

    async loadTrendingList() {
        try {
            const response = await fetch('api/search_api.php?action=get_trending_list');
            const result = await response.json();

            if (result.success && result.data) {
                this.renderTrendingList(result.data);
            }
        } catch (error) {
            console.error('加载热门榜单失败:', error);
        }
    }

    async loadSearchHistory() {
        try {
            const response = await fetch('api/search_api.php?action=get_search_history');
            const result = await response.json();

            if (result.success && result.data && result.data.length > 0) {
                this.renderSearchHistory(result.data);
                this.historySection.style.display = 'block';
            } else {
                this.historySection.style.display = 'none';
            }
        } catch (error) {
            console.error('加载搜索历史失败:', error);
            this.historySection.style.display = 'none';
        }
    }

    renderHotKeywords(keywords) {
        if (!this.hotKeywordsList) return;

        this.hotKeywordsList.innerHTML = '';
        keywords.forEach(keyword => {
            const tag = document.createElement('span');
            tag.className = 'keyword-tag';
            tag.textContent = keyword;
            tag.addEventListener('click', () => {
                this.performSearch(keyword);
            });
            this.hotKeywordsList.appendChild(tag);
        });
    }

    renderTrendingList(trending) {
        if (!this.trendingList) return;

        this.trendingList.innerHTML = '';
        trending.forEach(item => {
            const li = document.createElement('li');
            li.addEventListener('click', () => {
                this.performSearch(item.search_keyword);
            });

            const rankClass = item.trend_type === 'hot' ? 'hot' :
                             item.trend_type === 'new' ? 'new' : '';

            li.innerHTML = `
                <span class="rank ${rankClass}">${item.rank_position}</span>
                <span class="title">${item.title}</span>
                <span class="metric">${item.search_count_display}</span>
            `;

            this.trendingList.appendChild(li);
        });
    }

    renderSearchHistory(history) {
        if (!this.historyList) return;

        this.historyList.innerHTML = '';
        history.forEach(item => {
            const li = document.createElement('li');
            li.className = 'history-item';

            li.innerHTML = `
                <span class="history-text">${item.search_keyword}</span>
                <i class="fas fa-times delete-history" data-keyword="${item.search_keyword}" data-type="${item.search_type}"></i>
            `;

            // 点击历史记录进行搜索
            li.querySelector('.history-text').addEventListener('click', () => {
                this.performSearch(item.search_keyword);
            });

            // 删除单条历史记录
            li.querySelector('.delete-history').addEventListener('click', (e) => {
                e.stopPropagation();
                this.deleteSearchHistory(item.search_keyword, item.search_type);
            });

            this.historyList.appendChild(li);
        });
    }

    async performSearch(keyword) {
        if (!keyword) return;

        // 保存搜索历史
        await this.addSearchHistory(keyword);

        // 关闭搜索页面
        this.closeSearchOverlay();

        // 显示加载动画
        this.showSearchLoading();

        try {
            // 执行搜索请求
            const formData = new FormData();
            formData.append('action', 'perform_search');
            formData.append('keyword', keyword);
            formData.append('search_type', 'all');

            const response = await fetch('api/search_api.php', {
                method: 'POST',
                body: formData
            });

            const result = await response.json();

            // 隐藏加载动画
            this.hideSearchLoading();

            if (result.success) {
                // 显示搜索结果
                this.showSearchResults(result.data);
            } else {
                this.showToast(result.message || '搜索失败');
            }

        } catch (error) {
            console.error('搜索请求失败:', error);
            this.hideSearchLoading();
            this.showToast('搜索失败，请稍后重试');
        }
    }

    async addSearchHistory(keyword) {
        try {
            const formData = new FormData();
            formData.append('action', 'add_search_history');
            formData.append('keyword', keyword);
            formData.append('search_type', 'all');

            await fetch('api/search_api.php', {
                method: 'POST',
                body: formData
            });
        } catch (error) {
            console.error('保存搜索历史失败:', error);
        }
    }

    async deleteSearchHistory(keyword, searchType) {
        try {
            const formData = new FormData();
            formData.append('action', 'delete_search_history');
            formData.append('keyword', keyword);
            formData.append('search_type', searchType);

            const response = await fetch('api/search_api.php', {
                method: 'POST',
                body: formData
            });

            const result = await response.json();
            if (result.success) {
                // 重新加载搜索历史
                this.loadSearchHistory();
            }
        } catch (error) {
            console.error('删除搜索历史失败:', error);
        }
    }

    async clearSearchHistory() {
        if (!confirm('确定要清空所有搜索历史吗？')) {
            return;
        }

        try {
            const formData = new FormData();
            formData.append('action', 'clear_search_history');

            const response = await fetch('api/search_api.php', {
                method: 'POST',
                body: formData
            });

            const result = await response.json();
            if (result.success) {
                this.historySection.style.display = 'none';
                this.showToast('搜索历史已清空');
            }
        } catch (error) {
            console.error('清空搜索历史失败:', error);
        }
    }

    showSearchLoading() {
        if (this.searchLoadingOverlay) {
            this.searchLoadingOverlay.classList.add('active');
            document.body.style.overflow = 'hidden';
        }
    }

    hideSearchLoading() {
        if (this.searchLoadingOverlay) {
            this.searchLoadingOverlay.classList.remove('active');
            document.body.style.overflow = '';
        }
    }

    showSearchResults(data) {
        if (!this.searchResultsPage) return;

        // 设置搜索关键词
        if (this.searchResultsKeyword) {
            this.searchResultsKeyword.textContent = `"${data.keyword}" 的搜索结果`;
        }

        // 渲染搜索结果内容
        this.renderSearchResults(data);

        // 显示搜索结果页面
        this.searchResultsPage.classList.add('active');
        document.body.style.overflow = 'hidden';
    }

    closeSearchResults() {
        if (this.searchResultsPage) {
            this.searchResultsPage.classList.remove('active');
            document.body.style.overflow = '';
        }
    }

    renderSearchResults(data) {
        if (!this.searchResultsContent) return;

        if (data.has_results && data.results.length > 0) {
            // 有搜索结果
            let html = '<div class="search-results-list">';
            data.results.forEach(item => {
                html += `
                    <div class="search-result-item">
                        <div class="result-title">${item.title}</div>
                        <div class="result-description">${item.description}</div>
                        <div class="result-type">${this.getTypeLabel(item.type)}</div>
                    </div>
                `;
            });
            html += '</div>';
            this.searchResultsContent.innerHTML = html;
        } else {
            // 无搜索结果，显示空状态
            this.renderEmptyState(data.keyword);
        }
    }

    renderEmptyState(keyword) {
        const html = `
            <div class="empty-state-container">
                <div class="empty-state-illustration">
                    <div class="search-decoration"></div>
                    <div class="search-decoration"></div>
                    <div class="search-decoration"></div>
                    <div class="search-illustration">
                        <div class="magnifying-glass">
                            <i class="fas fa-search search-icon-inner"></i>
                        </div>
                    </div>
                </div>
                <div class="empty-state-title">暂无搜索结果</div>
                <div class="empty-state-description">
                    很抱歉，没有找到与"${keyword}"相关的内容<br>
                    请尝试其他关键词或查看下方建议
                </div>
                <div class="empty-state-suggestions">
                    <h4>搜索建议：</h4>
                    <ul class="suggestion-list">
                        <li class="suggestion-item">尝试使用更简单的关键词</li>
                        <li class="suggestion-item">检查关键词是否拼写正确</li>
                        <li class="suggestion-item">尝试使用同义词或相关词汇</li>
                        <li class="suggestion-item">浏览热门搜索获取灵感</li>
                    </ul>
                </div>
            </div>
        `;
        this.searchResultsContent.innerHTML = html;
    }

    getTypeLabel(type) {
        const typeLabels = {
            'game': '游戏陪玩',
            'food': '美食推荐',
            'entertainment': '娱乐活动',
            'activity': '活动推荐',
            'travel': '旅游景点',
            'shopping': '购物消费'
        };
        return typeLabels[type] || '其他';
    }

    showToast(message) {
        // 使用现有的toast函数
        if (typeof showToast === 'function') {
            showToast(message);
        } else {
            console.log(message);
        }
    }
}

// 页面加载完成后初始化搜索管理器
document.addEventListener('DOMContentLoaded', function() {
    new SearchManager();
});
