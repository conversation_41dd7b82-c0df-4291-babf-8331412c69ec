<?php
/**
 * 发送手机验证码API
 */

session_start();
header('Content-Type: application/json; charset=utf-8');

// 错误处理
error_reporting(0);
ini_set('display_errors', 0);

// 捕获所有错误并返回JSON
set_error_handler(function($severity, $message, $file, $line) {
    echo json_encode(['success' => false, 'message' => '系统错误，请稍后重试']);
    exit;
});

set_exception_handler(function($exception) {
    echo json_encode(['success' => false, 'message' => '系统错误，请稍后重试']);
    exit;
});

// 数据库连接配置
$db_config = [
    'host' => 'localhost',
    'username' => 'quwanplanet',
    'password' => 'nJmJm23FB4Xn6Fc3',
    'dbname' => 'quwanplanet',
    'port' => 3306,
    'charset' => 'utf8mb4'
];

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode(['success' => false, 'message' => '请求方法错误']);
    exit;
}

$phone = trim($_POST['phone'] ?? '');

// 验证手机号格式
if (empty($phone)) {
    echo json_encode(['success' => false, 'message' => '请输入手机号']);
    exit;
}

if (!preg_match('/^1[3-9]\d{9}$/', $phone)) {
    echo json_encode(['success' => false, 'message' => '手机号格式不正确']);
    exit;
}

try {
    // 连接数据库
    $pdo = new PDO(
        "mysql:host={$db_config['host']};port={$db_config['port']};dbname={$db_config['dbname']};charset={$db_config['charset']}",
        $db_config['username'],
        $db_config['password'],
        [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
            PDO::ATTR_EMULATE_PREPARES => false
        ]
    );

    // 检查手机号是否已注册
    $stmt = $pdo->prepare("SELECT id, username FROM users WHERE phone = ?");
    $stmt->execute([$phone]);
    $user = $stmt->fetch();

    if (!$user) {
        echo json_encode(['success' => false, 'message' => '该手机号未注册']);
        exit;
    }

    // 生成6位验证码
    $verification_code = sprintf('%06d', mt_rand(0, 999999));

    // 将验证码存储到session中（实际项目中应该发送短信）
    $_SESSION['verification_code'] = $verification_code;
    $_SESSION['verification_phone'] = $phone;
    $_SESSION['verification_time'] = time();
    $_SESSION['verification_user'] = $user;

    // 模拟发送短信（实际项目中这里应该调用短信API）
    error_log("验证码发送到 {$phone}: {$verification_code}");

    echo json_encode([
        'success' => true,
        'message' => '验证码已发送',
        'debug_code' => $verification_code, // 仅用于测试，生产环境应删除
        'user' => [
            'username' => $user['username'],
            'phone_masked' => substr($phone, 0, 3) . '****' . substr($phone, -4)
        ]
    ]);

} catch (PDOException $e) {
    error_log("验证码发送失败: " . $e->getMessage());
    echo json_encode(['success' => false, 'message' => '发送失败，请稍后重试']);
}
?>
