<?php
session_start();

// 检查用户是否已登录
if (!isset($_SESSION['user_id'])) {
    header('Location: ../login/index.php');
    exit;
}

$user_id = $_SESSION['user_id'];
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <meta name="theme-color" content="#40E0D0">
    <title>发布组局 - 趣玩星球</title>
    <link rel="stylesheet" href="css/publish.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <!-- 顶部导航栏 -->
    <div class="header-bar">
        <a href="../home/<USER>" class="back-button">
            <i class="fas fa-arrow-left"></i>
        </a>
        <div class="header-title">发布组局</div>
        <button class="publish-button" onclick="publishActivity()">发布</button>
    </div>

    <div class="container">
        <form id="activityForm" enctype="multipart/form-data">
            <!-- 活动图片 -->
            <div class="upload-section">
                <div class="upload-grid" id="uploadGrid">
                    <div class="upload-item add-photo" onclick="selectImages()">
                        <i class="fas fa-plus"></i>
                        <span>添加图片</span>
                        <small>最多3张</small>
                    </div>
                </div>
                <input type="file" id="imageInput" multiple accept="image/*" style="display: none;">
            </div>

            <!-- 活动标题 -->
            <div class="form-group">
                <label for="title">活动标题</label>
                <input type="text" id="title" name="title" placeholder="给你的活动起个吸引人的标题..." maxlength="50" required>
                <div class="char-count">0/50</div>
            </div>

            <!-- 活动类型 -->
            <div class="form-group">
                <label for="activityType">活动类型</label>
                <select id="activityType" name="activity_type" required>
                    <option value="">请选择活动类型</option>
                    <option value="entertainment">娱乐</option>
                    <option value="sports">运动</option>
                    <option value="travel">旅游</option>
                    <option value="food">美食</option>
                    <option value="study">学习</option>
                    <option value="social">社交</option>
                    <option value="game">游戏</option>
                    <option value="other">其他</option>
                </select>
            </div>

            <!-- 活动描述 -->
            <div class="form-group">
                <label for="description">活动描述</label>
                <textarea id="description" name="description" placeholder="详细描述你的活动内容、安排等..." maxlength="500" rows="4" required></textarea>
                <div class="char-count">0/500</div>
            </div>

            <!-- 活动时间 -->
            <div class="form-group">
                <label for="startTime">开始时间</label>
                <input type="datetime-local" id="startTime" name="start_time" required>
            </div>

            <div class="form-group">
                <label for="endTime">结束时间（可选）</label>
                <input type="datetime-local" id="endTime" name="end_time">
            </div>

            <!-- 活动地点 -->
            <div class="form-group">
                <label for="location">活动地点</label>
                <div class="location-input">
                    <input type="text" id="location" name="location" placeholder="点击选择活动地点" readonly required>
                    <button type="button" class="location-btn" onclick="selectLocation()">
                        <i class="fas fa-map-marker-alt"></i>
                    </button>
                </div>
            </div>

            <!-- 参与人数 -->
            <div class="form-group">
                <label for="maxParticipants">最大参与人数</label>
                <div class="number-input">
                    <button type="button" class="number-btn" onclick="changeNumber('maxParticipants', -1)">-</button>
                    <input type="number" id="maxParticipants" name="max_participants" value="5" min="2" max="50" readonly>
                    <button type="button" class="number-btn" onclick="changeNumber('maxParticipants', 1)">+</button>
                </div>
                <small>包括你在内，最少2人，最多50人</small>
            </div>

            <!-- 活动费用 -->
            <div class="form-group">
                <label for="fee">活动费用（元）</label>
                <div class="fee-input">
                    <input type="number" id="fee" name="fee" placeholder="0" min="0" max="9999" step="0.01">
                    <span class="fee-unit">元/人</span>
                </div>
                <small>0表示免费活动</small>
            </div>

            <!-- 联系方式 -->
            <div class="form-group">
                <label for="contact">联系方式</label>
                <input type="text" id="contact" name="contact_info" placeholder="微信号、QQ号或手机号" maxlength="50">
                <small>方便参与者联系你</small>
            </div>

            <!-- 参与要求 -->
            <div class="form-group">
                <label for="requirements">参与要求</label>
                <textarea id="requirements" name="requirements" placeholder="对参与者的要求或注意事项..." maxlength="200" rows="3"></textarea>
                <div class="char-count">0/200</div>
            </div>

            <!-- 标签 -->
            <div class="form-group">
                <label for="tags">标签</label>
                <div class="tags-input-container">
                    <input type="text" id="tagsInput" placeholder="输入标签，按回车添加" maxlength="20">
                    <div class="tags-display" id="tagsDisplay"></div>
                </div>
                <small>最多添加5个标签</small>
            </div>
        </form>
    </div>

    <!-- 加载提示 -->
    <div class="loading-overlay" id="loadingOverlay" style="display: none;">
        <div class="loading-content">
            <div class="loading-spinner"></div>
            <p>正在发布组局...</p>
        </div>
    </div>

    <!-- Toast提示 -->
    <div class="toast" id="toast"></div>

    <script src="js/activity.js"></script>
</body>
</html>
