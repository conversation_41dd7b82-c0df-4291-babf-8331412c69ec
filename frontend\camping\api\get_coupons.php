<?php
// 获取露营优惠券接口
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// 引入session配置
require_once '../../../sql/session_config.php';
initLoginSession();

// 数据库连接配置
$db_config = [
    'host' => 'localhost',
    'username' => 'quwanplanet',
    'password' => 'nJmJm23FB4Xn6Fc3',
    'dbname' => 'quwanplanet',
    'port' => 3306,
    'charset' => 'utf8mb4'
];

try {
    $dsn = "mysql:host={$db_config['host']};dbname={$db_config['dbname']};charset={$db_config['charset']};port={$db_config['port']}";
    $pdo = new PDO($dsn, $db_config['username'], $db_config['password']);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

    // 检查表是否存在
    $stmt = $pdo->query("SHOW TABLES LIKE 'camping_coupons'");
    if ($stmt->rowCount() == 0) {
        // 表不存在，返回默认数据
        echo json_encode([
            'success' => true,
            'coupons' => [
                [
                    'id' => 1,
                    'title' => '新人专享券',
                    'description' => '首次参加露营活动专享优惠',
                    'amount' => 20,
                    'type' => 'newbie_discount',
                    'validity' => '30天有效'
                ],
                [
                    'id' => 2,
                    'title' => '放肆趣玩券',
                    'description' => '参加任意露营活动立减优惠',
                    'amount' => 30,
                    'type' => 'join_discount',
                    'validity' => '30天有效'
                ]
            ]
        ]);
        exit;
    }

    // 获取当前有效的优惠券
    $sql = "SELECT id, title, description, type, discount_amount as amount, 
                   CONCAT(DATEDIFF(valid_until, NOW()), '天有效') as validity,
                   total_quantity, claimed_quantity
            FROM camping_coupons 
            WHERE status = 'active' 
            AND NOW() BETWEEN valid_from AND valid_until 
            AND claimed_quantity < total_quantity
            ORDER BY type, id
            LIMIT 2";
    
    $stmt = $pdo->prepare($sql);
    $stmt->execute();
    $coupons = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // 如果用户已登录，检查已领取的优惠券
    $user_claimed = [];
    if (isUserLoggedIn()) {
        $user_id = $_SESSION['user_id'];
        $claimed_sql = "SELECT coupon_id FROM user_camping_coupons WHERE user_id = ? AND status = 'claimed'";
        $claimed_stmt = $pdo->prepare($claimed_sql);
        $claimed_stmt->execute([$user_id]);
        $user_claimed = $claimed_stmt->fetchAll(PDO::FETCH_COLUMN);
    }

    // 标记已领取的优惠券
    foreach ($coupons as &$coupon) {
        $coupon['claimed'] = in_array($coupon['id'], $user_claimed);
    }

    echo json_encode([
        'success' => true,
        'coupons' => $coupons
    ]);

} catch (PDOException $e) {
    error_log("获取优惠券错误: " . $e->getMessage());
    
    // 数据库错误时返回默认数据
    echo json_encode([
        'success' => true,
        'coupons' => [
            [
                'id' => 1,
                'title' => '新人专享券',
                'description' => '首次参加露营活动专享优惠',
                'amount' => 20,
                'type' => 'newbie_discount',
                'validity' => '30天有效',
                'claimed' => false
            ],
            [
                'id' => 2,
                'title' => '放肆趣玩券',
                'description' => '参加任意露营活动立减优惠',
                'amount' => 30,
                'type' => 'join_discount',
                'validity' => '30天有效',
                'claimed' => false
            ]
        ]
    ]);
}
?>
