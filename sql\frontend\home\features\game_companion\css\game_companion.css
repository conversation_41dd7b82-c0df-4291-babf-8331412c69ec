/* frontend/home/<USER>/game_companion/css/game_companion.css */

/* 全局变量 */
:root {
    --primary-color: #6C5CE7; /* 主题色：紫色 */
    --secondary-color: #00B894; /* 辅助色：绿色 */
    --accent-color: #FF7675; /* 强调色：粉红色 */
    --dark-color: #2D3436; /* 深色 */
    --light-color: #F8F9FA; /* 浅色 */
    --gradient-primary: linear-gradient(135deg, #6C5CE7, #a29bfe); /* 主渐变 */
    --gradient-secondary: linear-gradient(135deg, #00B894, #55efc4); /* 辅助渐变 */
    --gradient-accent: linear-gradient(135deg, #FF7675, #fab1a0); /* 强调渐变 */
    --shadow-sm: 0 2px 8px rgba(0, 0, 0, 0.1); /* 小阴影 */
    --shadow-md: 0 4px 12px rgba(0, 0, 0, 0.15); /* 中阴影 */
    --shadow-lg: 0 8px 24px rgba(0, 0, 0, 0.2); /* 大阴影 */
    --border-radius-sm: 8px; /* 小圆角 */
    --border-radius-md: 12px; /* 中圆角 */
    --border-radius-lg: 20px; /* 大圆角 */
    --border-radius-full: 50%; /* 圆形 */
    --transition-fast: 0.2s ease; /* 快速过渡 */
    --transition-normal: 0.3s ease; /* 正常过渡 */
    --transition-slow: 0.5s ease; /* 慢速过渡 */
}

/* 基础样式重置 */
* {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
}

/* 新的游戏玩伴页面布局样式 */
.game-companion-new-layout {
    padding: 0;
    background-color: #f8f9fa;
    font-family: 'Segoe UI', 'PingFang SC', 'Microsoft YaHei', sans-serif;
    color: var(--dark-color);
    overflow-x: hidden;
}

/* 顶部横幅 */
.companion-banner {
    background: var(--gradient-primary);
    padding: 40px 20px;
    color: white;
    text-align: center;
    border-radius: 0 0 var(--border-radius-lg) var(--border-radius-lg);
    margin-bottom: 30px;
    position: relative;
    overflow: hidden;
}

.companion-banner::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image: url('https://via.placeholder.com/1200x400?text=Gaming+Background');
    background-size: cover;
    background-position: center;
    opacity: 0.2;
    z-index: 0;
}

.banner-content {
    position: relative;
    z-index: 1;
    max-width: 800px;
    margin: 0 auto;
}

.companion-banner h1 {
    font-size: 2.5rem;
    margin-bottom: 15px;
    font-weight: 700;
    text-shadow: 0 2px 4px rgba(0,0,0,0.2);
}

.companion-banner .highlight-text {
    color: #FDCB6E;
    position: relative;
    display: inline-block;
}

.companion-banner .highlight-text::after {
    content: '';
    position: absolute;
    bottom: -3px;
    left: 0;
    width: 100%;
    height: 3px;
    background-color: #FDCB6E;
    border-radius: 3px;
}

.companion-banner p {
    font-size: 1.2rem;
    margin-bottom: 25px;
    opacity: 0.9;
}

.banner-search {
    display: flex;
    max-width: 500px;
    margin: 0 auto;
    background: white;
    border-radius: var(--border-radius-md);
    overflow: hidden;
    box-shadow: var(--shadow-md);
}

.search-input {
    flex: 1;
    padding: 15px 20px;
    border: none;
    font-size: 16px;
    outline: none;
}

.search-btn {
    background: var(--secondary-color);
    color: white;
    border: none;
    padding: 0 25px;
    font-size: 18px;
    cursor: pointer;
    transition: background-color var(--transition-fast);
}

.search-btn:hover {
    background-color: #00a383;
}

/* 通用部分标题样式 */
.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding: 0 15px;
}

.section-header h2 {
    font-size: 1.5rem;
    color: var(--dark-color);
    font-weight: 700;
    display: flex;
    align-items: center;
    gap: 10px;
}

.section-header h2 .fas {
    color: var(--primary-color);
}

.view-more {
    color: var(--primary-color);
    text-decoration: none;
    font-size: 0.9rem;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 5px;
    transition: color var(--transition-fast);
}

.view-more:hover {
    color: #5649c0;
}

/* 陪玩列表容器 */
.companion-list-container {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(220px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
    padding: 0 15px;
}

/* 陪玩卡片基础样式 */
.companion-card {
    background-color: white;
    border-radius: var(--border-radius-md);
    box-shadow: var(--shadow-sm);
    padding: 15px;
    position: relative;
    transition: transform var(--transition-fast), box-shadow var(--transition-fast);
    overflow: hidden;
}

.companion-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-md);
}

/* 卡片徽章 */
.card-badge {
    position: absolute;
    top: 10px;
    left: 10px;
    background: var(--gradient-accent);
    color: white;
    padding: 3px 10px;
    border-radius: var(--border-radius-sm);
    font-size: 0.7rem;
    font-weight: 600;
    z-index: 2;
}

.card-badge.female {
    background: var(--gradient-accent);
}

.card-badge.male {
    background: var(--gradient-primary);
}

/* 头像容器 */
.card-avatar {
    position: relative;
    width: 80px;
    height: 80px;
    margin: 0 auto 15px;
}

.card-avatar img {
    width: 100%;
    height: 100%;
    border-radius: var(--border-radius-full);
    object-fit: cover;
    border: 3px solid white;
    box-shadow: var(--shadow-sm);
}

/* 在线状态指示器 */
.online-indicator {
    position: absolute;
    bottom: 5px;
    right: 5px;
    width: 12px;
    height: 12px;
    background-color: #2ecc71;
    border-radius: var(--border-radius-full);
    border: 2px solid white;
}

.online-indicator.offline {
    background-color: #e74c3c;
}

/* 游戏标签 */
.game-tags {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    gap: 5px;
    margin: 8px 0;
}

.game-tag, .rank-tag {
    background-color: #f1f2f6;
    color: #636e72;
    padding: 3px 8px;
    border-radius: var(--border-radius-sm);
    font-size: 0.7rem;
    font-weight: 500;
}

.rank-tag {
    background-color: #fff3cd;
    color: #856404;
}

/* 评分 */
.rating {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 2px;
    margin: 8px 0;
    color: #f1c40f;
    font-size: 0.8rem;
}

.rating span {
    color: #636e72;
    margin-left: 5px;
    font-weight: 600;
}

/* 游戏分类卡片 */
.game-categories {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
    padding: 0 15px;
}

.game-category-card {
    background-color: white;
    border-radius: var(--border-radius-md);
    box-shadow: var(--shadow-sm);
    overflow: hidden;
    transition: transform var(--transition-fast), box-shadow var(--transition-fast);
}

.game-category-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-md);
}

.game-image-container {
    position: relative;
    width: 100%;
    height: 160px;
    overflow: hidden;
}

.game-image-container img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform var(--transition-normal);
}

.game-category-card:hover .game-image-container img {
    transform: scale(1.05);
}

.game-overlay {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: linear-gradient(to top, rgba(0,0,0,0.7), transparent);
    padding: 15px 10px 10px;
    color: white;
    font-size: 0.8rem;
}

.game-players-count {
    display: inline-flex;
    align-items: center;
    gap: 5px;
    font-weight: 500;
}

.game-info {
    padding: 15px;
}

.game-info h4 {
    font-size: 1.1rem;
    margin-bottom: 8px;
    color: var(--dark-color);
}

.game-description {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
}

.game-description p {
    color: #636e72;
    font-size: 0.9rem;
}

.game-stats {
    display: flex;
    gap: 8px;
}

.game-stat {
    display: inline-flex;
    align-items: center;
    gap: 3px;
    font-size: 0.75rem;
    color: #636e72;
    background-color: #f1f2f6;
    padding: 2px 8px;
    border-radius: 12px;
}

.game-stat .fas {
    color: #f39c12;
}

.game-stat .fa-fire {
    color: #e74c3c;
}

.game-stat .fa-arrow-trend-up {
    color: #2ecc71;
}

.find-companions-btn {
    width: 100%;
    background: var(--gradient-secondary);
    color: white;
    border: none;
    border-radius: var(--border-radius-sm);
    padding: 10px;
    font-size: 0.9rem;
    font-weight: 600;
    cursor: pointer;
    transition: opacity var(--transition-fast);
}

.find-companions-btn:hover {
    opacity: 0.9;
}

.find-companions-btn.coming-soon {
    background: #bdc3c7;
    cursor: not-allowed;
}

/* 快速筛选标签 */
.quick-filters {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    margin-bottom: 20px;
    padding: 0 15px;
}

.quick-filter-btn, .advanced-filter-btn {
    background-color: white;
    border: 1px solid #e0e0e0;
    border-radius: 20px;
    padding: 8px 15px;
    font-size: 0.9rem;
    color: #636e72;
    cursor: pointer;
    transition: all var(--transition-fast);
}

.quick-filter-btn:hover, .advanced-filter-btn:hover {
    background-color: #f5f5f5;
}

.quick-filter-btn.active {
    background-color: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.advanced-filter-btn {
    background-color: #f8f9fa;
    display: flex;
    align-items: center;
    gap: 5px;
    margin-left: auto;
}

/* 视图选项 */
.view-options {
    display: flex;
    gap: 5px;
}

.view-option {
    background-color: white;
    border: 1px solid #e0e0e0;
    border-radius: var(--border-radius-sm);
    width: 36px;
    height: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    color: #636e72;
    transition: all var(--transition-fast);
}

.view-option:hover {
    background-color: #f5f5f5;
}

.view-option.active {
    background-color: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

/* 高级筛选面板 */
.advanced-filters {
    background-color: white;
    border-radius: var(--border-radius-md);
    box-shadow: var(--shadow-md);
    padding: 20px;
    margin-bottom: 20px;
    width: 100%;
    max-width: 100%;
    display: none; /* 默认隐藏，通过JS控制显示 */
}

.filters-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
}

.filters-header h4 {
    font-size: 1rem;
    color: var(--dark-color);
    display: flex;
    align-items: center;
    gap: 8px;
}

.filters-header h4 .fas {
    color: var(--primary-color);
}

.close-filters-btn {
    background: none;
    border: none;
    color: #636e72;
    font-size: 1rem;
    cursor: pointer;
    padding: 5px;
}

.close-filters-btn:hover {
    color: #2d3436;
}

.filter-group {
    margin-bottom: 15px;
}

.filter-group label {
    display: block;
    font-size: 0.9rem;
    color: #636e72;
    margin-bottom: 5px;
}

.filter-group select,
.filter-group input[type="text"] {
    width: 100%;
    padding: 10px;
    border: 1px solid #e0e0e0;
    border-radius: var(--border-radius-sm);
    font-size: 0.9rem;
    color: var(--dark-color);
    background-color: white;
    transition: border-color var(--transition-fast);
}

.filter-group select:focus,
.filter-group input[type="text"]:focus {
    border-color: var(--primary-color);
    outline: none;
}

/* 价格滑块 */
.range-slider-container {
    padding: 10px 0;
}

.range-slider {
    width: 100%;
    height: 5px;
    -webkit-appearance: none;
    background: #e0e0e0;
    border-radius: 5px;
    outline: none;
}

.range-slider::-webkit-slider-thumb {
    -webkit-appearance: none;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background: var(--primary-color);
    cursor: pointer;
}

.range-values {
    display: flex;
    justify-content: space-between;
    margin-top: 10px;
    font-size: 0.8rem;
    color: #636e72;
}

.filter-actions {
    display: flex;
    gap: 10px;
    margin-top: 20px;
}

.reset-btn, .apply-btn {
    padding: 10px 15px;
    border-radius: var(--border-radius-sm);
    font-size: 0.9rem;
    font-weight: 500;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 5px;
    transition: all var(--transition-fast);
}

.reset-btn {
    background-color: #f8f9fa;
    color: #636e72;
    border: 1px solid #e0e0e0;
}

.reset-btn:hover {
    background-color: #e9ecef;
}

.apply-btn {
    background-color: var(--primary-color);
    color: white;
    border: none;
    flex: 1;
}

.apply-btn:hover {
    background-color: #5649c0;
}

/* 玩伴列表 */
.companion-list {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(220px, 1fr));
    gap: 20px;
    width: 100%;
    padding: 0 15px;
}

.companion-list.grid-view {
    grid-template-columns: repeat(auto-fill, minmax(220px, 1fr));
}

.companion-list.list-view {
    grid-template-columns: 1fr;
}

/* 玩伴卡片内容 */
.companion-info {
    text-align: center;
}

.companion-info h4 {
    font-size: 1rem;
    color: var(--dark-color);
    margin-bottom: 5px;
}

.companion-details {
    display: flex;
    justify-content: center;
    gap: 10px;
    margin: 8px 0;
}

.detail-item {
    display: flex;
    align-items: center;
    gap: 3px;
    font-size: 0.75rem;
    color: #636e72;
}

.price-tag {
    font-size: 1rem;
    font-weight: 700;
    color: var(--accent-color);
    margin: 10px 0;
}

.card-actions {
    display: flex;
    gap: 8px;
    margin-top: 10px;
}

.action-btn {
    flex: 1;
    padding: 8px;
    border: none;
    border-radius: var(--border-radius-sm);
    font-size: 0.85rem;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 5px;
    transition: all var(--transition-fast);
}

.chat-btn {
    background-color: #f8f9fa;
    color: #636e72;
    border: 1px solid #e0e0e0;
}

.chat-btn:hover {
    background-color: #e9ecef;
}

.invite-btn {
    background: var(--gradient-primary);
    color: white;
}

.invite-btn:hover {
    opacity: 0.9;
}

.invite-btn:disabled {
    background: #bdc3c7;
    cursor: not-allowed;
    opacity: 0.7;
}

/* 加载更多按钮 */
.load-more-container {
    grid-column: 1 / -1;
    text-align: center;
    margin: 20px 0;
}

.load-more-btn {
    background-color: white;
    border: 1px solid #e0e0e0;
    border-radius: var(--border-radius-md);
    padding: 10px 20px;
    font-size: 0.9rem;
    color: var(--primary-color);
    cursor: pointer;
    display: inline-flex;
    align-items: center;
    gap: 8px;
    transition: all var(--transition-fast);
}

.load-more-btn:hover {
    background-color: #f5f5f5;
}

/* 推荐组合 */
.companion-combos {
    padding: 20px 0;
    margin-bottom: 30px;
}

.combo-cards {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 20px;
    padding: 0 15px;
}

.combo-card {
    background-color: white;
    border-radius: var(--border-radius-md);
    box-shadow: var(--shadow-sm);
    padding: 20px;
    transition: transform var(--transition-fast), box-shadow var(--transition-fast);
}

.combo-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-md);
}

.combo-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
}

.combo-header h3 {
    font-size: 1.1rem;
    color: var(--dark-color);
}

.combo-price {
    font-size: 1rem;
    font-weight: 700;
    color: var(--accent-color);
}

.combo-members {
    display: flex;
    gap: 15px;
    margin-bottom: 15px;
}

.combo-member {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 5px;
}

.combo-member img {
    width: 60px;
    height: 60px;
    border-radius: var(--border-radius-full);
    object-fit: cover;
    border: 3px solid white;
    box-shadow: var(--shadow-sm);
}

.combo-member span {
    font-size: 0.8rem;
    color: #636e72;
}

.combo-description {
    font-size: 0.9rem;
    color: #636e72;
    margin-bottom: 15px;
    line-height: 1.4;
}

.book-combo-btn {
    width: 100%;
    background: var(--gradient-accent);
    color: white;
    border: none;
    border-radius: var(--border-radius-sm);
    padding: 10px;
    font-size: 0.9rem;
    font-weight: 600;
    cursor: pointer;
    transition: opacity var(--transition-fast);
}

.book-combo-btn:hover {
    opacity: 0.9;
}

/* 浮动聊天按钮 */
.floating-chat-btn {
    position: fixed;
    bottom: 30px;
    right: 30px;
    width: 60px;
    height: 60px;
    background: var(--gradient-primary);
    color: white;
    border-radius: var(--border-radius-full);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    box-shadow: var(--shadow-lg);
    cursor: pointer;
    z-index: 1000;
    transition: transform var(--transition-fast);
}

.floating-chat-btn:hover {
    transform: scale(1.1);
}

.notification-badge {
    position: absolute;
    top: -5px;
    right: -5px;
    background-color: var(--accent-color);
    color: white;
    font-size: 0.7rem;
    font-weight: 700;
    width: 22px;
    height: 22px;
    border-radius: var(--border-radius-full);
    display: flex;
    align-items: center;
    justify-content: center;
    border: 2px solid white;
}

/* 响应式调整 */
@media (max-width: 768px) {
    .companion-banner h1 {
        font-size: 2rem;
    }

    .companion-banner p {
        font-size: 1rem;
    }

    .game-categories {
        grid-template-columns: repeat(auto-fill, minmax(220px, 1fr));
    }

    .combo-cards {
        grid-template-columns: 1fr;
    }

    .floating-chat-btn {
        width: 50px;
        height: 50px;
        font-size: 1.2rem;
        bottom: 20px;
        right: 20px;
    }
}

@media (max-width: 576px) {
    .companion-banner h1 {
        font-size: 1.5rem;
    }

    .companion-list-container,
    .companion-list {
        grid-template-columns: repeat(auto-fill, minmax(160px, 1fr));
    }

    .advanced-filter-btn span {
        display: none;
    }

    .section-header h2 {
        font-size: 1.2rem;
    }
}

/* 技能筛选容器 */
.skill-filters-container {
    background-color: #fff;
    padding: 15px;
    border-radius: 8px;
    box-shadow: 0 1px 4px rgba(0,0,0,0.05);
}

.skill-filters-container .filter-group {
    margin-bottom: 10px;
}

.skill-filters-container .filter-group label {
    display: block;
    font-size: 14px;
    color: #555;
    margin-bottom: 5px;
    font-weight: 500;
}

.skill-filters-container .filter-options select,
.skill-filters-container .filter-options input[type="text"] {
    width: 100%;
    padding: 8px 10px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 13px;
    box-sizing: border-box;
}

/* 确保新的样式不会影响到旧的筛选框（如果它们意外地同时显示） */
#game-specific-filters .game-filter-item select {
    /* 这里可以覆盖或重置旧筛选框的特定样式，如果需要的话 */
    /* 但由于我们已经移除了旧的HTML，这部分可能不是必需的 */
}

/* Section specific styles */
.recommended-masters h2,
.popular-games-section h2,
.companions-overview h2 {
    font-size: 20px; /* 统一分区标题大小 */
    color: #2c3e50; /* 深蓝灰色，更专业 */
    margin-top: 25px;
    margin-bottom: 15px;
    padding-bottom: 8px;
    border-bottom: 3px solid #40E0D0; /* 更明显的主题色下划线 */
    display: flex;
    align-items: center;
}

.recommended-masters h2 .fas,
.popular-games-section h2 .fas,
.companions-overview h2 .fas {
    margin-right: 10px;
    color: #40E0D0; /* 图标也使用主题色 */
}

/* Master cards */
.master-card {
    border-left: 4px solid #f39c12; /* 大神卡片特殊标记 - 橙色 */
}

.master-card .companion-info h4 {
    color: #f39c12;
}

.master-game, .master-rank {
    font-size: 13px;
    color: #7f8c8d; /* 灰色文字 */
    margin: 3px 0;
}

.view-profile-btn {
    background-color: #3498db; /* 蓝色按钮 */
    color: white;
    padding: 8px 12px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 13px;
    transition: background-color 0.2s ease;
}

.view-profile-btn:hover {
    background-color: #2980b9;
}

/* Popular Games Section */
.game-categories {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr)); /* 响应式游戏分类卡片 */
    gap: 20px;
    margin-bottom: 20px;
}

.game-category-card {
    background-color: #ffffff;
    border-radius: 8px;
    box-shadow: 0 3px 10px rgba(0,0,0,0.08);
    padding: 15px;
    text-align: center;
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.game-category-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 5px 15px rgba(0,0,0,0.12);
}

.game-category-card img {
    width: 100%;
    max-width: 150px; /* 限制图片最大宽度 */
    height: auto; /* 高度自适应 */
    border-radius: 6px;
    margin-bottom: 10px;
    object-fit: cover;
}

.game-category-card h4 {
    font-size: 16px;
    color: #34495e; /* 深蓝灰色标题 */
    margin-bottom: 5px;
}

.game-category-card p {
    font-size: 13px;
    color: #95a5a6; /* 浅灰色描述 */
    margin-bottom: 12px;
}

.find-companions-btn {
    background-color: #2ecc71; /* 绿色按钮 */
    color: white;
    padding: 10px 15px;
    border: none;
    border-radius: 5px;
    font-size: 14px;
    cursor: pointer;
    transition: background-color 0.2s ease;
}

.find-companions-btn:hover {
    background-color: #27ae60;
}

.find-companions-btn:disabled {
    background-color: #bdc3c7; /* 禁用状态灰色 */
    cursor: not-allowed;
}

/* Filters and List Container */
.filters-and-list-container {
    display: flex;
    flex-wrap: wrap; /* 允许换行 */
    gap: 20px;
}

.filters-and-list-container .filters {
    flex: 1; /* 筛选器占据一部分空间 */
    min-width: 250px; /* 筛选器最小宽度 */
    background-color: #fff;
    padding: 15px;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.07);
    align-self: flex-start; /* 顶部对齐 */
}

.filters-and-list-container .filters h4 {
    font-size: 16px;
    color: #333;
    margin-top: 0;
    margin-bottom: 15px;
    padding-bottom: 8px;
    border-bottom: 1px solid #eee;
    display: flex;
    align-items: center;
}

.filters-and-list-container .filters h4 .fas {
    margin-right: 8px;
    color: #555;
}

.filters-and-list-container .companion-list {
    flex: 3; /* 列表占据更多空间 */
    min-width: 300px; /* 列表最小宽度 */
    display: grid; /* 改为 grid 布局 */
    grid-template-columns: repeat(auto-fill, minmax(180px, 1fr)); /* 响应式卡片 */
    gap: 15px;
}

/* Companion Card Enhancements */
.companion-card .companion-info .game-played {
    font-size: 13px;
    color: #555;
    margin: 4px 0;
}

.companion-card .companion-info .game-played .fas {
    margin-right: 5px;
    color: #777;
}

.status-tag {
    display: inline-block;
    padding: 3px 8px;
    border-radius: 12px;
    font-size: 11px;
    font-weight: 500;
    text-transform: uppercase;
}

.status-online {
    background-color: #e8f5e9; /* 淡绿色背景 */
    color: #4caf50; /* 绿色文字 */
}

.status-offline {
    background-color: #fce4ec; /* 淡粉色背景 */
    color: #e91e63; /* 粉色文字 */
}

.invite-btn {
    background-color: #40E0D0; /* 主题色 */
    color: white;
    padding: 8px 12px;
    border: none;
    border-radius: 4px;
    font-size: 13px;
    cursor: pointer;
    transition: background-color 0.2s ease, opacity 0.2s ease;
    display: inline-flex; /* 使图标和文字在同一行 */
    align-items: center; /* 垂直居中图标和文字 */
    gap: 5px; /* 图标和文字之间的间距 */
}

.invite-btn:hover {
    opacity: 0.85;
}

.invite-btn:disabled {
    background-color: #ccc;
    cursor: not-allowed;
    opacity: 0.7;
}

.invite-btn .fas {
    font-size: 12px; /* 调整按钮内图标大小 */
}

/* Placeholder text in companion list */
.companion-list .placeholder-text {
    grid-column: 1 / -1; /* 让占位符横跨所有列 */
    text-align: center;
    padding: 20px;
    color: #aaa;
    font-style: italic;
    font-size: 14px;
}

/* General filter group styling (if not already covered or needs override) */
.filters .filter-group {
    margin-bottom: 15px;
}

.filters .filter-group label {
    display: block;
    font-size: 14px;
    color: #555;
    margin-bottom: 6px;
    font-weight: 500;
}

.filters .filter-group select,
.filters .filter-group input[type="text"] {
    width: 100%;
    padding: 10px 12px;
    border: 1px solid #ddd;
    border-radius: 5px;
    font-size: 14px;
    box-sizing: border-box;
    background-color: #fdfdfd;
    transition: border-color 0.2s ease;
}

.filters .filter-group select:focus,
.filters .filter-group input[type="text"]:focus {
    border-color: #40E0D0;
    outline: none;
}

/* Apply Filters Button - already styled by .dynamic-search-button, but can add specifics */
#apply-filters-btn.dynamic-search-button {
    width: 100%;
    margin-top: 10px; /* Add some space above the button */
}

/* 占位提示信息样式 (如果页面本身有其他 p 标签需要这个样式) */
.game-companion-new-layout > p {
    color: #888;
    font-style: italic;
    text-align: center;
    padding: 20px 0;
}