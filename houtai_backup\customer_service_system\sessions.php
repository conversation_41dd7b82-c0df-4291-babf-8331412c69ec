<?php
// 客服会话页面
header('Content-Type: text/html; charset=UTF-8');
ini_set('default_charset', 'UTF-8');

session_start();

// 检查登录状态
if (!isset($_SESSION['cs_logged_in']) || !$_SESSION['cs_logged_in']) {
    header('Location: ../login.php');
    exit;
}

// 引用数据库配置文件
require_once '../db_config.php';

// 获取客服信息
$cs_name = $_SESSION['cs_name'] ?? '客服';
$cs_role = $_SESSION['cs_role'] ?? 'customer_service';
$cs_user_id = $_SESSION['cs_user_id'] ?? 0;

// 获取筛选参数
$status_filter = $_GET['status'] ?? 'all';
$cs_filter = $_GET['cs_id'] ?? 'all';

// 获取会话列表
try {
    $pdo = getDbConnection();

    $where_conditions = [];
    $params = [];

    if ($status_filter !== 'all') {
        $where_conditions[] = "s.status = ?";
        $params[] = $status_filter;
    }

    if ($cs_filter !== 'all') {
        $where_conditions[] = "s.customer_service_id = ?";
        $params[] = $cs_filter;
    }

    $where_clause = !empty($where_conditions) ? 'WHERE ' . implode(' AND ', $where_conditions) : '';

    $sql = "
        SELECT s.*,
               cs.name as cs_name,
               cs.employee_id as cs_employee_id
        FROM customer_service_sessions s
        LEFT JOIN customer_service_users cs ON s.customer_service_id = cs.id
        $where_clause
        ORDER BY s.created_at DESC
        LIMIT 50
    ";

    $stmt = $pdo->prepare($sql);
    $stmt->execute($params);
    $sessions = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // 获取客服列表用于筛选
    $stmt = $pdo->query("SELECT id, name, employee_id FROM customer_service_users WHERE status = 'active' ORDER BY name");
    $customer_services = $stmt->fetchAll(PDO::FETCH_ASSOC);

} catch (Exception $e) {
    $sessions = [];
    $customer_services = [];
    $error_message = "数据加载失败: " . $e->getMessage();
}
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>客服会话 - 趣玩星球客服管理系统</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="assets/css/customer-service.css">
    <style>
        .sessions-container {
            background: white;
            border-radius: 16px;
            padding: 25px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.08);
        }

        .sessions-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 25px;
            flex-wrap: wrap;
            gap: 15px;
        }

        .sessions-filters {
            display: flex;
            gap: 15px;
            align-items: center;
            flex-wrap: wrap;
        }

        .filter-select {
            padding: 8px 12px;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            background: white;
            color: #333;
            font-size: 14px;
            transition: all 0.3s ease;
        }

        .filter-select:focus {
            outline: none;
            border-color: #6F7BF5;
            box-shadow: 0 0 0 3px rgba(111,123,245,0.1);
        }

        .sessions-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }

        .sessions-table th,
        .sessions-table td {
            padding: 15px;
            text-align: left;
            border-bottom: 1px solid #e9ecef;
        }

        .sessions-table th {
            background: #f8f9fa;
            font-weight: 600;
            color: #333;
            font-size: 14px;
        }

        .sessions-table td {
            font-size: 14px;
            color: #666;
        }

        .status-badge {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
            text-transform: uppercase;
        }

        .status-waiting {
            background: #fff3cd;
            color: #856404;
        }

        .status-active {
            background: #d1ecf1;
            color: #0c5460;
        }

        .status-closed {
            background: #d4edda;
            color: #155724;
        }

        .status-transferred {
            background: #f8d7da;
            color: #721c24;
        }

        .priority-badge {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
        }

        .priority-low { background: #e2e3e5; color: #6c757d; }
        .priority-normal { background: #cce5ff; color: #004085; }
        .priority-high { background: #fff3cd; color: #856404; }
        .priority-urgent { background: #f8d7da; color: #721c24; }

        .action-btn {
            padding: 6px 12px;
            border: none;
            border-radius: 6px;
            font-size: 12px;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-right: 5px;
        }

        .btn-primary {
            background: #6F7BF5;
            color: white;
        }

        .btn-success {
            background: #06D6A0;
            color: white;
        }

        .btn-warning {
            background: #FFD166;
            color: #333;
        }

        .action-btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        }

        .no-sessions {
            text-align: center;
            padding: 60px 20px;
            color: #999;
        }

        .no-sessions i {
            font-size: 48px;
            margin-bottom: 15px;
            color: #ddd;
        }
    </style>
</head>
<body>
    <div class="cs-container">
        <!-- 侧边栏 -->
        <?php include 'includes/sidebar.php'; ?>

        <!-- 主内容区 -->
        <div class="cs-main">
            <!-- 顶部导航 -->
            <?php include 'includes/topbar.php'; ?>

            <!-- 内容区域 -->
            <div class="cs-content">
                <div class="cs-header">
                    <h1><i class="fas fa-comments"></i> 客服会话</h1>
                    <p>管理和处理客户咨询会话</p>
                </div>

                <div class="sessions-container">
                    <div class="sessions-header">
                        <h2>会话列表</h2>
                        <div class="sessions-filters">
                            <select class="filter-select" onchange="filterSessions()" id="statusFilter">
                                <option value="all" <?php echo $status_filter === 'all' ? 'selected' : ''; ?>>全部状态</option>
                                <option value="waiting" <?php echo $status_filter === 'waiting' ? 'selected' : ''; ?>>等待中</option>
                                <option value="active" <?php echo $status_filter === 'active' ? 'selected' : ''; ?>>进行中</option>
                                <option value="closed" <?php echo $status_filter === 'closed' ? 'selected' : ''; ?>>已结束</option>
                                <option value="transferred" <?php echo $status_filter === 'transferred' ? 'selected' : ''; ?>>已转接</option>
                            </select>

                            <select class="filter-select" onchange="filterSessions()" id="csFilter">
                                <option value="all" <?php echo $cs_filter === 'all' ? 'selected' : ''; ?>>全部客服</option>
                                <?php foreach ($customer_services as $cs): ?>
                                    <option value="<?php echo $cs['id']; ?>" <?php echo $cs_filter == $cs['id'] ? 'selected' : ''; ?>>
                                        <?php echo htmlspecialchars($cs['name']); ?> (<?php echo htmlspecialchars($cs['employee_id']); ?>)
                                    </option>
                                <?php endforeach; ?>
                            </select>

                            <button class="action-btn btn-primary" onclick="refreshSessions()">
                                <i class="fas fa-sync-alt"></i> 刷新
                            </button>
                        </div>
                    </div>

                    <?php if (empty($sessions)): ?>
                        <div class="no-sessions">
                            <i class="fas fa-comments"></i>
                            <h3>暂无会话数据</h3>
                            <p>当前筛选条件下没有找到相关会话</p>
                        </div>
                    <?php else: ?>
                        <table class="sessions-table">
                            <thead>
                                <tr>
                                    <th>会话ID</th>
                                    <th>用户信息</th>
                                    <th>客服</th>
                                    <th>状态</th>
                                    <th>优先级</th>
                                    <th>开始时间</th>
                                    <th>消息数</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($sessions as $session): ?>
                                    <tr>
                                        <td>
                                            <strong><?php echo htmlspecialchars($session['session_id']); ?></strong>
                                        </td>
                                        <td>
                                            <div>
                                                <strong><?php echo htmlspecialchars($session['user_name'] ?? '未知用户'); ?></strong>
                                                <?php if ($session['user_phone']): ?>
                                                    <br><small><?php echo htmlspecialchars($session['user_phone']); ?></small>
                                                <?php endif; ?>
                                            </div>
                                        </td>
                                        <td>
                                            <?php if ($session['cs_name']): ?>
                                                <div>
                                                    <strong><?php echo htmlspecialchars($session['cs_name']); ?></strong>
                                                    <br><small><?php echo htmlspecialchars($session['cs_employee_id']); ?></small>
                                                </div>
                                            <?php else: ?>
                                                <span class="text-muted">未分配</span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <span class="status-badge status-<?php echo $session['status']; ?>">
                                                <?php
                                                $status_labels = [
                                                    'waiting' => '等待中',
                                                    'active' => '进行中',
                                                    'closed' => '已结束',
                                                    'transferred' => '已转接'
                                                ];
                                                echo $status_labels[$session['status']] ?? $session['status'];
                                                ?>
                                            </span>
                                        </td>
                                        <td>
                                            <span class="priority-badge priority-<?php echo $session['priority']; ?>">
                                                <?php
                                                $priority_labels = [
                                                    'low' => '低',
                                                    'normal' => '普通',
                                                    'high' => '高',
                                                    'urgent' => '紧急'
                                                ];
                                                echo $priority_labels[$session['priority']] ?? $session['priority'];
                                                ?>
                                            </span>
                                        </td>
                                        <td>
                                            <?php echo date('m-d H:i', strtotime($session['started_at'])); ?>
                                        </td>
                                        <td>
                                            <strong><?php echo $session['message_count']; ?></strong>
                                        </td>
                                        <td>
                                            <?php if ($session['status'] === 'waiting'): ?>
                                                <button class="action-btn btn-primary" onclick="acceptSession('<?php echo $session['session_id']; ?>')">
                                                    <i class="fas fa-hand-paper"></i> 接受
                                                </button>
                                            <?php elseif ($session['status'] === 'active'): ?>
                                                <button class="action-btn btn-success" onclick="viewSession('<?php echo $session['session_id']; ?>')">
                                                    <i class="fas fa-eye"></i> 查看
                                                </button>
                                                <button class="action-btn btn-warning" onclick="closeSession('<?php echo $session['session_id']; ?>')">
                                                    <i class="fas fa-times"></i> 结束
                                                </button>
                                            <?php else: ?>
                                                <button class="action-btn btn-success" onclick="viewSession('<?php echo $session['session_id']; ?>')">
                                                    <i class="fas fa-eye"></i> 查看
                                                </button>
                                            <?php endif; ?>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <script src="assets/js/customer-service.js"></script>
    <script>
        function filterSessions() {
            const status = document.getElementById('statusFilter').value;
            const cs = document.getElementById('csFilter').value;

            const params = new URLSearchParams();
            if (status !== 'all') params.append('status', status);
            if (cs !== 'all') params.append('cs_id', cs);

            window.location.href = 'sessions.php?' + params.toString();
        }

        function refreshSessions() {
            window.location.reload();
        }

        async function acceptSession(sessionId) {
            if (confirm('确定要接受这个会话吗？')) {
                try {
                    console.log('发送接受请求，会话ID:', sessionId);

                    // 使用调试版API，查看详细错误信息
                    const response = await fetch('api/accept_session_debug.php', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({
                            sessionId: sessionId
                        })
                    });

                    console.log('响应状态:', response.status);

                    // 先检查响应是否是JSON
                    const responseText = await response.text();
                    console.log('原始响应:', responseText);

                    let data;
                    try {
                        data = JSON.parse(responseText);
                        console.log('解析后的数据:', data);
                    } catch (parseError) {
                        console.error('JSON解析失败:', parseError);
                        console.error('响应内容:', responseText);
                        showToast('服务器返回了无效的响应格式，请查看控制台了解详情', 'error');
                        return;
                    }

                    if (data.success) {
                        showToast('会话接受成功！', 'success');
                        setTimeout(() => window.location.reload(), 1000);
                    } else {
                        let errorMsg = data.error || '未知错误';
                        if (data.debug) {
                            console.log('调试信息:', data.debug);
                            errorMsg += ' (详细信息请查看控制台)';
                        }
                        showToast('接受失败：' + errorMsg, 'error');
                    }
                } catch (error) {
                    console.error('接受会话失败:', error);
                    showToast('网络错误：' + error.message, 'error');
                }
            }
        }

        function viewSession(sessionId) {
            // 在新窗口中打开会话详情页面
            const width = 1200;
            const height = 800;
            const left = (screen.width - width) / 2;
            const top = (screen.height - height) / 2;

            window.open(
                'session_detail.php?id=' + sessionId,
                'session_detail_' + sessionId,
                `width=${width},height=${height},left=${left},top=${top},resizable=yes,scrollbars=yes,status=yes,menubar=no,toolbar=no,location=no`
            );
        }

        async function closeSession(sessionId) {
            const reason = prompt('请输入结束会话的原因（可选）：');
            if (reason !== null) { // 用户没有取消
                try {
                    const response = await fetch('api/close_session.php', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({
                            sessionId: sessionId,
                            reason: reason
                        })
                    });

                    const data = await response.json();

                    if (data.success) {
                        showToast('会话已成功结束！', 'success');
                        setTimeout(() => window.location.reload(), 1000);
                    } else {
                        showToast('结束失败：' + (data.error || '未知错误'), 'error');
                    }
                } catch (error) {
                    console.error('结束会话失败:', error);
                    showToast('网络错误，请稍后重试', 'error');
                }
            }
        }

        // 简单的提示函数
        function showToast(message, type = 'info') {
            // 创建提示元素
            const toast = document.createElement('div');
            toast.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                padding: 12px 20px;
                border-radius: 8px;
                color: white;
                font-size: 14px;
                z-index: 10000;
                transition: all 0.3s ease;
                max-width: 300px;
                word-wrap: break-word;
            `;

            // 根据类型设置颜色
            switch(type) {
                case 'success':
                    toast.style.background = '#06D6A0';
                    break;
                case 'error':
                    toast.style.background = '#FF6B6B';
                    break;
                case 'warning':
                    toast.style.background = '#FFD166';
                    toast.style.color = '#333';
                    break;
                default:
                    toast.style.background = '#6F7BF5';
            }

            toast.textContent = message;
            document.body.appendChild(toast);

            // 3秒后自动移除
            setTimeout(() => {
                toast.style.opacity = '0';
                toast.style.transform = 'translateX(100%)';
                setTimeout(() => {
                    if (toast.parentNode) {
                        toast.parentNode.removeChild(toast);
                    }
                }, 300);
            }, 3000);
        }
    </script>
</body>
</html>
