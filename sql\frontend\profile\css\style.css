/* 全局样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    font-family: 'PingFang SC', 'Helvetica Neue', Arial, sans-serif;
}

body {
    background-color: #f8f9fa;
    color: #333333;
    line-height: 1.6;
    min-height: 100vh;
    position: relative;
    padding-bottom: 60px; /* 为底部导航栏留出空间 */
}

a {
    text-decoration: none;
    color: inherit;
}

/* Toast提示样式 */
.toast {
    position: fixed;
    top: 20px;
    left: 50%;
    transform: translateX(-50%);
    background-color: rgba(255, 255, 255, 0.95);
    color: #333333;
    padding: 14px 24px;
    border-radius: 12px;
    box-shadow: 0 6px 16px rgba(0, 0, 0, 0.12);
    z-index: 9999;
    display: none;
    border-left: 4px solid #1E90FF;
    font-size: 15px;
    font-weight: 500;
    max-width: 90%;
    text-align: center;
    pointer-events: none;
}

/* 用户信息卡片样式 - 带背景图 */
.user-card {
    position: relative;
    border-radius: 0;
    margin-bottom: 25px;
    padding-bottom: 20px;
    overflow: hidden;
}

/* 背景图 */
.card-bg {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 260px;
    background-image: url('https://s1.imagehub.cc/images/2025/04/26/a3947e820a13653b37c816360a5a9b68.jpg');
    background-size: cover;
    background-position: center;
    z-index: 0;
}

/* 顶部操作按钮 */
.card-actions {
    position: absolute;
    top: 15px;
    right: 15px;
    display: flex;
    gap: 15px;
    z-index: 2;
}

.action-btn {
    font-size: 20px;
    color: white;
    text-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
}

/* 用户头像（居中显示） */
.user-avatar-center {
    position: relative;
    width: 100px;
    height: 100px;
    margin: 0 auto;
    z-index: 1;
    margin-top: 60px;
}

/* 头像容器 */
.avatar-circle {
    width: 100%;
    height: 100%;
    border-radius: 50%;
    overflow: hidden;
    border: 3px solid white;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
}

.user-avatar-center img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

/* 用户铭牌样式 */
.user-nameplate {
    position: absolute;
    bottom: -12px;
    left: 50%;
    transform: translateX(-50%);
    width: 110px;
    height: 28px;
    z-index: 2;
    filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.2));
}

.user-nameplate img {
    width: 100%;
    height: 100%;
    object-fit: contain;
}

/* 用户基本信息（居中显示） */
.user-info-center {
    position: relative;
    text-align: center;
    padding: 25px 20px 25px; /* 增加顶部内边距，为铭牌留出空间 */
    z-index: 1;
    color: white;
}

/* 添加渐变背景，使文字更清晰 */
.user-info-center::before {
    content: '';
    position: absolute;
    top: -40px;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(to top, rgba(0, 0, 0, 0.5), rgba(0, 0, 0, 0.2), transparent);
    z-index: -1;
}

.user-name {
    font-size: 22px;
    font-weight: bold;
    margin-bottom: 5px;
    color: white;
    text-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
}

.user-id-container {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 5px;
}

.user-id {
    font-size: 15px;
    color: rgba(255, 255, 255, 0.9);
    text-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
}

.copy-id-btn {
    background: none;
    border: none;
    color: rgba(255, 255, 255, 0.9);
    font-size: 15px;
    padding: 0;
    margin-left: 5px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
}

.copy-id-btn i {
    text-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
}

/* 用户简介容器 */
.bio-container {
    background-color: white;
    padding: 10px 0;
}

/* 用户简介（移到统计信息下方） */
.user-bio {
    font-size: 14px;
    color: #666;
    padding: 5px 15px;
    text-align: left;
    line-height: 1.5;
}

/* 统计信息容器 */
.stats-container {
    position: relative;
    margin: 0;
    z-index: 2;
    margin-top: -20px;
}

.user-stats {
    display: flex;
    justify-content: space-around;
    background-color: white;
    border-radius: 12px 12px 0 0;
    padding: 15px 5px;
    box-shadow: 0 -4px 15px rgba(0, 0, 0, 0.1);
    position: relative;
    z-index: 3;
}

.stat-item {
    text-align: center;
    flex: 1;
}

.stat-value {
    font-size: 18px;
    font-weight: bold;
    color: #333;
}

.stat-label {
    font-size: 13px;
    color: #999;
}

/* 会员卡片样式 */
.member-card {
    background-color: white;
    margin: 0;
    position: relative;
    overflow: hidden;
    border-top: 1px solid #f0f0f0;
}

.member-card-bg {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, #FFD700, #FFA500);
    opacity: 0.1;
    z-index: 0;
}

.member-info {
    display: flex;
    align-items: center;
    padding: 15px;
    position: relative;
    z-index: 1;
}

.member-icon {
    width: 40px;
    height: 40px;
    margin-right: 12px;
}

.member-icon img {
    width: 100%;
    height: 100%;
    object-fit: contain;
}

.member-details {
    flex: 1;
}

.member-title {
    font-size: 16px;
    font-weight: bold;
    color: #333;
    margin-bottom: 3px;
}

.member-level {
    font-size: 13px;
    color: #FFA500;
}

.member-action {
    margin-left: 10px;
}

.member-btn {
    background: linear-gradient(135deg, #FFD700, #FFA500);
    color: white;
    border: none;
    border-radius: 20px;
    padding: 8px 15px;
    font-size: 13px;
    font-weight: 500;
    cursor: pointer;
    box-shadow: 0 2px 8px rgba(255, 165, 0, 0.3);
    transition: all 0.3s ease;
}

.member-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(255, 165, 0, 0.4);
}

.member-benefits {
    display: flex;
    justify-content: space-around;
    padding: 0 0 15px;
    position: relative;
    z-index: 1;
}

.benefit-item {
    display: flex;
    flex-direction: column;
    align-items: center;
}

.benefit-icon {
    width: 32px;
    height: 32px;
    background-color: rgba(255, 165, 0, 0.1);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 5px;
}

.benefit-icon i {
    color: #FFA500;
    font-size: 16px;
}

.benefit-text {
    font-size: 12px;
    color: #666;
}

/* 金刚区样式 */
.feature-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    background-color: white;
    border-radius: 0 0 15px 15px;
    margin: 0 0 15px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
    overflow: hidden;
}

/* 自定义图标样式 */
.custom-icon {
    width: 28px;
    height: 28px;
    object-fit: contain;
}

.custom-menu-icon {
    width: 24px;
    height: 24px;
    object-fit: contain;
}

.feature-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 15px 10px;
    text-decoration: none;
    transition: all 0.3s ease;
}

.feature-item:active {
    background-color: #f9f9f9;
}

.feature-icon {
    width: 45px;
    height: 45px;
    border-radius: 50%;
    background-color: rgba(30, 144, 255, 0.1);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 8px;
}

.feature-icon i {
    font-size: 20px;
    color: #1E90FF;
}

.feature-label {
    font-size: 13px;
    color: #333;
    font-weight: 500;
}

/* 功能菜单样式 */
.menu-section {
    margin: 15px 0;
    padding: 0;
}

.menu-group {
    background-color: white;
    border-radius: 0;
    margin: 0 0 1px;
    overflow: hidden;
}

.menu-item {
    display: flex;
    align-items: center;
    padding: 15px;
    border-bottom: 1px solid #f0f0f0;
    text-decoration: none;
}

.menu-item:last-child {
    border-bottom: none;
}

.menu-icon {
    width: 36px;
    height: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 15px;
    font-size: 18px;
    color: #1E90FF;
    background-color: rgba(30, 144, 255, 0.1);
    border-radius: 50%;
}

.creator-icon {
    color: #FFA41B;
}

.menu-label {
    flex: 1;
    font-size: 15px;
    color: #333;
    font-weight: 500;
}

.menu-arrow {
    color: #ccc;
    font-size: 14px;
}

/* 底部导航栏样式 */
.bottom-nav {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background-color: white;
    display: flex;
    justify-content: space-around;
    padding: 8px 0;
    box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.05);
    z-index: 100;
}

.nav-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 5px 0;
    width: 20%;
}

.nav-item i {
    font-size: 24px;
    margin-bottom: 3px;
    color: #999;
}

.nav-item span {
    font-size: 12px;
    color: #999;
}

.nav-item.active i,
.nav-item.active span {
    color: #1E90FF;
}

/* 动画效果 */
@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

.menu-item:active,
.wallet-item:active {
    background-color: #f9f9f9;
}
