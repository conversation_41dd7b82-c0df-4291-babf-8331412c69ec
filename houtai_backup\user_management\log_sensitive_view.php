<?php
/**
 * 记录敏感信息查看日志
 * 趣玩星球管理后台
 */

session_start();

// 检查登录状态
if (!isset($_SESSION['admin_id'])) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => '未登录']);
    exit;
}

require_once '../db_config.php';

$response = ['success' => false, 'message' => ''];

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $user_id = intval($_POST['user_id'] ?? 0);
    $info_type = trim($_POST['info_type'] ?? '');

    if (!$user_id || !$info_type) {
        $response['message'] = '参数不完整';
        echo json_encode($response);
        exit;
    }

    try {
        $pdo = getDbConnection();

        // 获取管理员信息
        $admin_name = $_SESSION['admin_name'] ?? '管理员';
        $admin_employee_id = $_SESSION['admin_employee_id'] ?? '';
        $admin_department = $_SESSION['admin_department'] ?? '';

        // 获取用户信息
        $stmt = $pdo->prepare("SELECT username, quwan_id FROM users WHERE id = ?");
        $stmt->execute([$user_id]);
        $user = $stmt->fetch(PDO::FETCH_ASSOC);

        if (!$user) {
            $response['message'] = '用户不存在';
            echo json_encode($response);
            exit;
        }

        // 根据信息类型生成日志内容
        $content_map = [
            'phone' => '查看了用户手机号码',
            'id_card' => '查看了用户身份证号码',
            'real_name' => '查看了用户真实姓名',
            'email' => '查看了用户邮箱地址',
            'sensitive_info' => '查看了用户敏感信息'
        ];

        $content = $content_map[$info_type] ?? '查看了用户敏感信息';
        $content .= "（用户：{$user['username']}，趣玩ID：{$user['quwanplanet_id']}）";

        // 插入日志记录
        $stmt = $pdo->prepare("
            INSERT INTO user_logs (user_id, operator_name, employee_id, department, type, content, created_at)
            VALUES (?, ?, ?, ?, ?, ?, NOW())
        ");

        $stmt->execute([
            $user_id,
            $admin_name,
            $admin_employee_id,
            $admin_department,
            '敏感信息查看',
            $content
        ]);

        $response['success'] = true;
        $response['message'] = '日志记录成功';

    } catch (PDOException $e) {
        $response['message'] = '数据库错误：' . $e->getMessage();
    }
}

header('Content-Type: application/json');
echo json_encode($response);
?>
