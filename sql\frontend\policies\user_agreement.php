<?php
session_start();

// 获取来源页面参数，决定返回链接
$from = isset($_GET['from']) ? $_GET['from'] : 'home';

// 根据来源设置返回链接和标题
switch($from) {
    case 'login':
        $back_url = '../login/index.php';
        $back_title = '返回登录';
        break;
    case 'register':
        $back_url = '../login/index.php';
        $back_title = '返回注册';
        break;
    case 'home':
    default:
        $back_url = '../home/<USER>';
        $back_title = '返回首页';
        break;
}
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0, user-scalable=no, shrink-to-fit=no">
    <meta name="format-detection" content="telephone=no">
    <style>
        * {
            -webkit-touch-callout: none;
            -webkit-user-select: none;
            -khtml-user-select: none;
            -moz-user-select: none;
            -ms-user-select: none;
            user-select: none;
            -webkit-tap-highlight-color: transparent;
        }

        /* 允许选择文本内容 */
        .policy-content, .policy-content p, .policy-content li, .policy-content h2 {
            -webkit-user-select: text;
            -khtml-user-select: text;
            -moz-user-select: text;
            -ms-user-select: text;
            user-select: text;
        }

        /* 禁止双击放大 */
        html {
            -ms-touch-action: manipulation;
            touch-action: manipulation;
        }
    </style>
    <title>用户协议 - 趣玩星球</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="../css/page-protection.css">
    <style>
        /* CSS变量定义 - 主题色系统 */
        :root {
            --primary-color: #6F7BF5;
            --primary-light: #8A94F7;
            --primary-dark: #5A67E8;
            --secondary-color: #7C3AED;
            --accent-color: #FF6B9D;
            --gradient-primary: linear-gradient(135deg, #6F7BF5, #7C3AED);
            --gradient-secondary: linear-gradient(135deg, #8A94F7, #6F7BF5);
            --gradient-accent: linear-gradient(135deg, #FF6B9D, #FFB6C1);
            --text-primary: #333333;
            --text-secondary: #666666;
            --text-light: #999999;
            --bg-white: #FFFFFF;
            --bg-light: #F8F9FA;
            --bg-gradient: linear-gradient(135deg, #F0FFFF, #F8F9FA);
            --shadow-sm: 0 2px 8px rgba(111, 123, 245, 0.08);
            --shadow-md: 0 4px 16px rgba(111, 123, 245, 0.12);
            --shadow-lg: 0 8px 32px rgba(111, 123, 245, 0.16);
            --radius-sm: 8px;
            --radius-md: 12px;
            --radius-lg: 16px;
            --radius-xl: 24px;
            --transition-fast: 0.2s ease;
            --transition-normal: 0.3s ease;
            --transition-slow: 0.4s ease;
        }

        body {
            background: var(--bg-gradient);
            color: var(--text-primary);
            font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;
            line-height: 1.6;
            position: relative;
            overflow-x: hidden;
        }

        /* 背景装饰 */
        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="20" cy="20" r="3" fill="rgba(111,123,245,0.05)"/><circle cx="80" cy="40" r="2" fill="rgba(124,58,237,0.05)"/><circle cx="60" cy="80" r="1.5" fill="rgba(255,107,157,0.05)"/><circle cx="30" cy="70" r="2.5" fill="rgba(138,148,247,0.05)"/></svg>');
            pointer-events: none;
            z-index: -1;
        }

        .policy-container {
            max-width: 900px;
            margin: 30px auto;
            padding: 40px;
            background: var(--bg-white);
            border-radius: var(--radius-xl);
            box-shadow: var(--shadow-lg);
            position: relative;
            overflow: hidden;
        }

        .policy-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: var(--gradient-primary);
        }

        .policy-header {
            text-align: center;
            margin-bottom: 40px;
            padding-bottom: 30px;
            border-bottom: 2px solid rgba(111, 123, 245, 0.1);
            position: relative;
        }

        .policy-header::after {
            content: '';
            position: absolute;
            bottom: -2px;
            left: 50%;
            transform: translateX(-50%);
            width: 60px;
            height: 2px;
            background: var(--gradient-primary);
        }

        .policy-header h1 {
            background: var(--gradient-primary);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            font-size: 28px;
            font-weight: 700;
            margin-bottom: 16px;
            position: relative;
            white-space: nowrap;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .policy-header h1::before {
            content: '\f3ed';
            font-family: 'Font Awesome 6 Free';
            font-weight: 900;
            color: var(--primary-color);
            font-size: 20px;
            margin-right: 8px;
            -webkit-text-fill-color: var(--primary-color);
            flex-shrink: 0;
        }

        .policy-header p {
            color: var(--text-secondary);
            font-size: 15px;
            margin-bottom: 8px;
            font-weight: 500;
        }

        .policy-content h2 {
            color: var(--text-primary);
            font-size: 22px;
            font-weight: 600;
            margin-top: 40px;
            margin-bottom: 20px;
            padding: 16px 20px;
            background: linear-gradient(135deg, rgba(111, 123, 245, 0.05), rgba(138, 148, 247, 0.05));
            border-radius: var(--radius-md);
            border-left: 4px solid var(--primary-color);
            position: relative;
        }

        .policy-content h2::before {
            content: '\f0c9';
            font-family: 'Font Awesome 6 Free';
            font-weight: 900;
            color: var(--primary-color);
            font-size: 18px;
            margin-right: 10px;
        }

        .policy-content p {
            margin-bottom: 18px;
            text-align: justify;
            line-height: 1.7;
            color: var(--text-primary);
        }

        .policy-content ul, .policy-content ol {
            margin-bottom: 20px;
            padding-left: 24px;
        }

        .policy-content li {
            margin-bottom: 10px;
            line-height: 1.6;
            color: var(--text-primary);
            position: relative;
        }

        .policy-content ul li::before {
            content: '\f00c';
            font-family: 'Font Awesome 6 Free';
            font-weight: 900;
            color: var(--secondary-color);
            font-size: 12px;
            position: absolute;
            left: -20px;
            top: 4px;
        }

        .policy-footer {
            margin-top: 50px;
            padding: 30px;
            background: linear-gradient(135deg, rgba(111, 123, 245, 0.05), rgba(138, 148, 247, 0.05));
            border-radius: var(--radius-lg);
            text-align: center;
            font-size: 15px;
            color: var(--text-secondary);
            border: 2px solid rgba(111, 123, 245, 0.1);
        }

        .back-button {
            display: inline-flex;
            align-items: center;
            margin-top: 24px;
            padding: 12px 24px;
            background: var(--gradient-primary);
            color: white;
            border-radius: 50px;
            text-decoration: none;
            font-weight: 600;
            transition: var(--transition-normal);
            box-shadow: var(--shadow-md);
        }

        .back-button:hover {
            background: var(--gradient-secondary);
            transform: translateY(-3px);
            box-shadow: var(--shadow-lg);
        }

        .back-button i {
            margin-right: 8px;
            font-size: 14px;
        }

        .header-bar {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 64px;
            background: var(--bg-white);
            backdrop-filter: blur(10px);
            box-shadow: var(--shadow-sm);
            z-index: 1000;
            display: flex;
            align-items: center;
            border-bottom: 1px solid rgba(111, 123, 245, 0.1);
        }

        .header-content {
            width: 100%;
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
            display: flex;
            align-items: center;
            position: relative;
        }

        .page-title {
            position: absolute;
            left: 50%;
            transform: translateX(-50%);
            font-size: 18px;
            font-weight: 600;
            color: var(--text-primary);
            background: var(--gradient-primary);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .back-button-top {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 40px;
            height: 40px;
            background: var(--bg-white);
            color: var(--primary-color);
            border-radius: 50%;
            text-decoration: none;
            transition: var(--transition-normal);
            box-shadow: var(--shadow-sm);
            position: absolute;
            left: 15px;
            border: 2px solid rgba(111, 123, 245, 0.2);
        }

        .back-button-top svg {
            width: 20px;
            height: 20px;
        }

        .back-button-top:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-md);
            background: var(--primary-light);
            border-color: var(--primary-color);
        }

        body {
            padding-top: 84px;
        }

        .highlight {
            background: linear-gradient(135deg, rgba(255, 107, 157, 0.05), rgba(255, 182, 193, 0.05));
            padding: 20px;
            border-radius: var(--radius-md);
            border-left: 4px solid var(--accent-color);
            margin-bottom: 24px;
            position: relative;
        }

        .highlight::before {
            content: '\f071';
            font-family: 'Font Awesome 6 Free';
            font-weight: 900;
            color: var(--accent-color);
            font-size: 16px;
            position: absolute;
            top: 20px;
            left: -12px;
            background: var(--bg-white);
            padding: 4px;
            border-radius: 50%;
        }
    </style>
</head>
<body>
    <div class="header-bar">
        <div class="header-content">
            <a href="<?php echo $back_url; ?>" class="back-button-top" title="<?php echo $back_title; ?>">
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <path d="M15 18l-6-6 6-6"/>
                </svg>
            </a>
            <div class="page-title">用户协议</div>
        </div>
    </div>

    <div class="policy-container">
        <div class="policy-header">
            <h1>趣玩星球用户协议</h1>
            <p>版本号：v1.0</p>
            <p>生效日期：2025年5月15日</p>
        </div>

        <div class="policy-content">
            <h2>1. 导言</h2>
            <p>欢迎您使用趣玩星球！本用户服务协议（以下简称"本协议"）是您与趣玩星球运营方（以下简称"我们"或"趣玩星球"）之间关于您使用趣玩星球平台及相关服务所订立的法律协议。</p>

            <p>1.1 <strong>协议主体</strong>：本协议由您（趣玩星球用户）与趣玩星球运营方共同订立。趣玩星球运营方是指负责趣玩星球平台运营的法律实体。</p>

            <p>1.2 <strong>协议范围</strong>：本协议适用于您通过任何方式（包括但不限于网页、移动应用程序、小程序等）访问和使用趣玩星球提供的所有产品和服务。</p>

            <p>1.3 <strong>协议效力</strong>：当您点击"同意"、"注册"或以其他方式使用趣玩星球服务时，即表示您已充分阅读、理解并同意接受本协议的全部条款。本协议即在您与趣玩星球之间产生法律约束力。</p>

            <p>1.4 <strong>协议修改</strong>：我们有权根据法律法规变化、业务发展需要等原因修改本协议。修改后的协议将在趣玩星球平台公布，并自公布之日起生效。如您不同意修改后的协议，应立即停止使用我们的服务；如您继续使用，则视为同意修改后的协议。</p>

            <div class="highlight">
                <p><strong>重要提示：趣玩星球仅面向18周岁及以上的成年人提供服务。</strong>如您未满18周岁，请立即停止使用我们的服务。我们建立了严格的年龄验证机制，一经发现未成年人使用，将立即终止服务并删除相关账号。</p>
            </div>

            <h2>2. 趣玩产品及服务</h2>
            <p>2.1 <strong>服务概述</strong>：趣玩星球是一个综合性社交娱乐平台，致力于为用户提供城市玩伴匹配、游戏组队、旅行结伴、活动组织、兴趣社交等多元化服务。</p>

            <p>2.2 <strong>核心功能</strong>：</p>
            <ul>
                <li><strong>社交匹配</strong>：基于地理位置、兴趣爱好、年龄等条件的智能匹配服务</li>
                <li><strong>活动组织</strong>：用户可发起或参与各类线上线下活动</li>
                <li><strong>游戏组队</strong>：为游戏爱好者提供组队、陪玩等服务</li>
                <li><strong>旅行结伴</strong>：旅行路线分享、结伴出行等服务</li>
                <li><strong>内容分享</strong>：图文、视频等多媒体内容发布与分享</li>
                <li><strong>即时通讯</strong>：私信、群聊等沟通工具</li>
                <li><strong>商业服务</strong>：付费会员、虚拟礼品、增值服务等</li>
            </ul>

            <p>2.3 <strong>服务特性</strong>：</p>
            <ul>
                <li>我们采用先进的算法技术，为您提供个性化的内容推荐和用户匹配</li>
                <li>我们建立了完善的安全保障体系，包括实名认证、行为监控、举报处理等</li>
                <li>我们提供7×24小时的客户服务支持</li>
                <li>我们持续优化产品功能，为用户提供更好的使用体验</li>
            </ul>

            <p>2.4 <strong>服务变更</strong>：我们保留随时修改、暂停或终止部分或全部服务的权利。对于重大服务变更，我们将提前通知用户。</p>

            <h2>3. 趣玩账号</h2>
            <p>3.1 <strong>账号注册</strong>：</p>
            <ul>
                <li>我们提供趣玩星球账号的注册渠道，您可以通过移动电话号码或趣玩星球允许的其他方式注册趣玩星球账号</li>
                <li>您在注册时必须提供真实、准确、完整、有效的个人信息，包括但不限于真实姓名、有效手机号码、电子邮箱地址等</li>
                <li>您应当及时更新您的个人信息，确保信息的真实性和有效性</li>
                <li>您理解并同意，您有义务保证注册信息的真实性，如因信息不真实而引起的问题及后果，由您自行承担</li>
            </ul>

            <p>3.2 <strong>账号安全</strong>：</p>
            <ul>
                <li>您的趣玩星球账号仅限您本人使用，禁止赠与、借用、租用、转让或售卖</li>
                <li>您应当妥善保管您的账号及密码，对您账号项下的所有活动承担责任</li>
                <li>如发现有他人冒用或盗用您的账号及密码，或其他任何未经合法授权使用的情形，您应立即通知我们并提供相关证明材料</li>
                <li>因您保管不善可能导致的任何损失由您自行承担，我们不承担任何责任</li>
            </ul>

            <p>3.3 <strong>账号管理</strong>：</p>
            <ul>
                <li>我们有权根据用户实际使用情况，决定限制一个自然人注册账号的数量</li>
                <li>对于长期未使用的账号，我们有权进行回收处理</li>
                <li>对于违反本协议的账号，我们有权采取警告、限制功能、暂停使用、注销账号等措施</li>
                <li>账号注销后，账号中的虚拟财产等权益将被清空且无法恢复</li>
            </ul>

            <p>3.4 <strong>实名认证</strong>：</p>
            <ul>
                <li>根据国家法律法规要求，您需要完成实名认证后方可使用趣玩星球的完整功能</li>
                <li>实名认证需要您提供真实有效的身份证件信息</li>
                <li>我们将严格保护您的身份信息，仅用于身份验证和法律法规要求的用途</li>
                <li>如您拒绝实名认证或提供虚假信息，我们有权限制或终止为您提供服务</li>
            </ul>

            <h2>4. 信息内容发布规范</h2>
            <p>4.1 <strong>内容发布原则</strong>：趣玩星球致力于为用户提供健康、友善、高质量的交流平台。在您规范完成真实身份信息认证后，可以使用趣玩制作、发布视频、图片、文字等信息内容，并保证所发布信息内容（无论是否公开）符合法律法规要求。</p>

            <p>4.2 <strong>禁止发布的违法违规内容</strong>：您不得制作、复制、发布、传播以下违法违规内容：</p>
            <ul>
                <li><strong>反对宪法确定的基本原则的</strong></li>
                <li><strong>危害国家安全，泄露国家秘密，颠覆国家政权，破坏国家统一的</strong></li>
                <li><strong>损害国家荣誉和利益的</strong></li>
                <li><strong>歪曲、丑化、亵渎、否定英雄烈士事迹和精神，以侮辱、诽谤或者其他方式侵害英雄烈士的姓名、肖像、名誉、荣誉的</strong></li>
                <li><strong>宣扬恐怖主义、极端主义或者煽动实施恐怖活动、极端主义活动的</strong></li>
                <li><strong>煽动民族仇恨、民族歧视，破坏民族团结的</strong></li>
                <li><strong>破坏国家宗教政策，宣扬邪教和封建迷信的</strong></li>
                <li><strong>散布谣言，扰乱社会秩序，破坏社会稳定的</strong></li>
                <li><strong>散布淫秽、色情、赌博、暴力、凶杀、恐怖或者教唆犯罪的</strong></li>
                <li><strong>侮辱或者诽谤他人，侵害他人名誉权、隐私权、知识产权或其他合法权益的</strong></li>
                <li><strong>违反互联网相关法律法规的商业广告</strong></li>
                <li><strong>法律、行政法规禁止的其他内容</strong></li>
            </ul>

            <p>4.3 <strong>禁止发布的不良内容</strong>，亦应当主动抵制此类内容的传播：</p>
            <ul>
                <li><strong>使用夸张标题，内容与标题严重不符的</strong></li>
                <li><strong>炒作绯闻、丑闻、劣迹等的</strong></li>
                <li><strong>不当评述自然灾害、重大事故等灾难的</strong></li>
                <li><strong>带有性暗示、性挑逗等易使人产生性联想的</strong></li>
                <li><strong>展现血腥、惊悚、残忍等致人身心不适的</strong></li>
                <li><strong>煽动人群歧视、地域歧视等的</strong></li>
                <li><strong>宣扬低俗、庸俗、媚俗内容的</strong></li>
                <li><strong>可能引发未成年人模仿不安全行为和违反社会公德行为、诱导未成年人不良嗜好等的</strong></li>
                <li><strong>其他对网络生态造成不良影响的内容</strong></li>
            </ul>

            <p>4.4 <strong>内容审核机制</strong>：</p>
            <ul>
                <li>我们建立了完善的内容审核体系，包括技术审核和人工审核相结合的方式</li>
                <li>对于违规内容，我们将及时删除并对发布者采取相应处罚措施</li>
                <li>我们鼓励用户积极举报违规内容，共同维护平台环境</li>
                <li>对于恶意举报行为，我们也将采取相应的处罚措施</li>
            </ul>

            <h2>5. 网络安全保护</h2>
            <p>5.1 <strong>安全保障体系</strong>：</p>
            <ul>
                <li>我们建立了完善的网络安全防护体系，采用多层次的安全技术保护用户信息和平台安全</li>
                <li>我们定期进行安全评估和漏洞修复，确保系统安全稳定运行</li>
                <li>我们建立了24小时安全监控机制，及时发现和处理安全威胁</li>
            </ul>

            <p>5.2 <strong>用户安全义务</strong>：</p>
            <ul>
                <li>您应当妥善保管自己的账号密码，不得将账号借给他人使用</li>
                <li>您不得利用技术手段破坏平台安全或干扰他人正常使用</li>
                <li>发现安全漏洞或异常情况时，应及时向我们报告</li>
            </ul>

            <h2>6. 投诉举报</h2>
            <p>6.1 <strong>举报渠道</strong>：</p>
            <ul>
                <li>您可以通过平台内的举报功能对违规内容或行为进行举报</li>
                <li>您可以通过客服中心提交详细的投诉材料</li>
                <li>我们设立了专门的举报邮箱接收用户举报</li>
            </ul>

            <p>6.2 <strong>处理流程</strong>：</p>
            <ul>
                <li>我们将在收到举报后24小时内开始处理</li>
                <li>对于紧急情况，我们将立即采取必要措施</li>
                <li>处理结果将及时反馈给举报人</li>
                <li>我们保护举报人的隐私，不会泄露举报人信息</li>
            </ul>

            <h2>7. 违约处理</h2>
            <p>4.1 违规行为的认定：</p>
            <p>趣玩星球有权根据本协议、平台规则及相关法律法规，独立判断用户行为是否构成违规。我们可能通过以下方式发现违规行为：</p>
            <ul>
                <li>平台自动检测系统发现；</li>
                <li>人工审核发现；</li>
                <li>其他用户举报；</li>
                <li>主管部门通知或投诉；</li>
                <li>媒体报道等其他渠道获取的信息。</li>
            </ul>

            <p>4.2 处罚措施：</p>
            <p>根据违规行为的性质、情节、危害程度以及用户的违规历史等因素，我们可能采取以下一种或多种处罚措施：</p>
            <ul>
                <li><strong>警告</strong>：对轻微违规行为，我们可能向您发出警告提醒；</li>
                <li><strong>限制功能</strong>：限制您使用趣玩星球的部分功能，如发布内容、评论、私信等；</li>
                <li><strong>禁言</strong>：在一定时间内（如1天、3天、7天、15天、30天等）或永久禁止您发布内容、评论或进行其他互动；</li>
                <li><strong>内容删除</strong>：删除您发布的违规内容；</li>
                <li><strong>账号冻结</strong>：在一定时间内暂停您使用账号；</li>
                <li><strong>账号封禁</strong>：永久禁止您使用该账号；</li>
                <li><strong>设备封禁</strong>：禁止您使用特定设备注册或登录趣玩星球；</li>
                <li><strong>IP封禁</strong>：禁止特定IP地址注册或登录趣玩星球；</li>
                <li><strong>永久封禁</strong>：永久禁止您使用趣玩星球的所有服务；</li>
                <li><strong>其他措施</strong>：根据实际情况采取其他必要措施。</li>
            </ul>

            <p>4.3 具体违规行为及处罚标准：</p>
            <ul>
                <li><strong>发布违法违规内容</strong>：
                    <ul>
                        <li>首次：内容删除+警告</li>
                        <li>再次：内容删除+禁言3-7天</li>
                        <li>屡次：内容删除+账号冻结15-30天</li>
                        <li>情节严重：账号永久封禁</li>
                    </ul>
                </li>
                <li><strong>发布色情低俗内容</strong>：
                    <ul>
                        <li>首次：内容删除+禁言3天</li>
                        <li>再次：内容删除+账号冻结7-15天</li>
                        <li>屡次或情节严重：账号永久封禁</li>
                    </ul>
                </li>
                <li><strong>骚扰他人</strong>：
                    <ul>
                        <li>首次：警告+禁言1-3天</li>
                        <li>再次：禁言7-15天</li>
                        <li>屡次：账号冻结15-30天</li>
                        <li>情节严重：账号永久封禁</li>
                    </ul>
                </li>
                <li><strong>煽动民族仇恨或地域歧视</strong>：
                    <ul>
                        <li>首次：内容删除+禁言7天</li>
                        <li>再次：内容删除+账号冻结15-30天</li>
                        <li>屡次或情节严重：账号永久封禁</li>
                    </ul>
                </li>
                <li><strong>涉及国家机密、国家安全或军事机密</strong>：
                    <ul>
                        <li>立即删除内容+账号永久封禁，并可能依法向有关部门报告</li>
                    </ul>
                </li>
                <li><strong>冒充他人或官方</strong>：账号永久封禁</li>
                <li><strong>发布虚假信息</strong>：
                    <ul>
                        <li>首次：内容删除+警告</li>
                        <li>再次：内容删除+禁言3-7天</li>
                        <li>屡次：账号冻结7-15天</li>
                        <li>造成严重后果：账号永久封禁</li>
                    </ul>
                </li>
                <li><strong>恶意举报</strong>：
                    <ul>
                        <li>首次：警告</li>
                        <li>再次：限制举报功能7-15天</li>
                        <li>屡次：账号冻结7-15天</li>
                    </ul>
                </li>
                <li><strong>规避处罚</strong>（如使用小号规避禁言等）：加重处罚直至永久封禁</li>
            </ul>

            <p>4.4 申诉机制：</p>
            <p>如您认为我们的处罚有误，可以通过以下方式申诉：</p>
            <ul>
                <li>在收到处罚通知后15天内，通过客服中心提交申诉；</li>
                <li>申诉应当包含您的账号信息、申诉理由及相关证据；</li>
                <li>我们将在收到申诉后15个工作日内进行审核并回复；</li>
                <li>在申诉期间，原处罚措施仍然有效；</li>
                <li>对于同一处罚决定，仅受理一次申诉。</li>
            </ul>

            <p>4.5 免责声明：</p>
            <p>趣玩星球有权根据实际情况调整处罚措施，且无需事先通知。对于违反本协议的行为，我们保留追究相关责任的权利。</p>

            <h2>5. 未成年人保护与青少年模式</h2>
            <p>5.1 未成年人保护原则：</p>
            <p>趣玩星球高度重视未成年人的保护工作，严格遵守《中华人民共和国未成年人保护法》《网络保护条例》等法律法规的规定。虽然本平台仅面向18周岁及以上的成年人提供服务，但我们仍建立了完善的未成年人保护机制。</p>

            <p>5.2 防沉迷系统：</p>
            <ul>
                <li>我们建立了基于实名认证的账号体系，通过技术手段识别未成年人；</li>
                <li>对于被识别为未成年人的账号，我们将限制其使用本平台服务；</li>
                <li>我们会定期排查平台内容，确保不存在诱导未成年人违法犯罪、实施不良行为或者危害未成年人身心健康的内容。</li>
            </ul>

            <p>5.3 青少年模式：</p>
            <p>虽然趣玩星球仅面向成年人开放，但为了防止未成年人冒充成年人使用本平台，我们设置了青少年模式：</p>
            <ul>
                <li>当监护人发现未成年人使用本平台时，可以联系客服开启青少年模式；</li>
                <li>开启青少年模式后，账号将被限制使用大部分功能，仅保留基础浏览功能；</li>
                <li>青少年模式下，将屏蔽所有社交互动、交易、支付等功能；</li>
                <li>青少年模式下，将优先展示适合青少年的健康内容；</li>
                <li>青少年模式可通过监护人验证后关闭。</li>
            </ul>

            <p>5.4 家长监护：</p>
            <ul>
                <li>我们鼓励家长和监护人参与到未成年人的网络保护中；</li>
                <li>监护人可以通过客服申请对未成年人账号进行监护；</li>
                <li>监护人有权要求我们删除未成年人账号及相关信息；</li>
                <li>我们为监护人提供便捷的投诉举报通道。</li>
            </ul>

            <p>5.5 教育引导：</p>
            <ul>
                <li>我们将在平台内设置未成年人保护专区，提供网络安全教育内容；</li>
                <li>我们会定期开展未成年人网络保护的宣传活动；</li>
                <li>我们将与学校、家长等社会各方面合作，共同保护未成年人健康成长。</li>
            </ul>

            <h2>6. 服务内容与规则</h2>
            <p>6.1 趣玩星球提供的服务包括但不限于：城市玩伴、游戏玩伴、旅行玩伴、组织活动等社交服务。</p>
            <p>6.2 您理解并同意，我们提供的服务中可能包括广告，您同意在使用过程中显示趣玩星球和第三方供应商提供的广告。</p>
            <p>6.3 您使用趣玩星球服务时应遵守平台发布的相关规则，包括但不限于社区规范、活动规则等。</p>

            <h2>7. 知识产权</h2>
            <p>7.1 趣玩星球服务中包含的所有内容，包括但不限于文字、图片、音频、视频、图表、标识、版面设计、程序等，均由我们或相关权利人依法拥有其知识产权。</p>
            <p>7.2 未经我们或相关权利人书面许可，您不得以任何形式复制、修改、抄袭、传播或使用上述内容。</p>

            <h2>8. 用户内容</h2>
            <p>8.1 您通过趣玩星球发布的内容（包括但不限于文字、图片、视频等）仍归您所有，但您授予我们在全球范围内免费、非独家、可转授权的许可，允许我们使用、复制、修改、改编、出版、翻译、据以创作衍生作品、传播、表演和展示此等内容。</p>
            <p>8.2 您应对您发布的内容负责，确保其不违反法律法规及本协议规定。</p>

            <h2>9. 隐私保护</h2>
            <p>9.1 保护您的隐私是我们的重要原则，我们会按照《隐私政策》收集、使用、储存和分享您的个人信息。</p>
            <p>9.2 未经您的同意，我们不会向第三方披露您的个人信息，除非有法律法规要求或为保护趣玩星球的合法权益。</p>

            <h2>10. 免责声明</h2>
            <p>10.1 您理解并同意，趣玩星球服务可能会受到多种因素的影响，我们不保证服务不会中断，也不保证服务的及时性、安全性或准确性。</p>
            <p>10.2 对于因不可抗力或趣玩星球不能控制的原因造成的服务中断或其他缺陷，我们不承担责任，但将尽力减少因此给您造成的损失和影响。</p>
            <p>10.3 趣玩星球不对用户间因线上沟通、线下见面等行为承担任何责任。用户应自行承担因此产生的所有风险。</p>

            <h2>11. 协议终止</h2>
            <p>11.1 您有权在任何时候终止使用趣玩星球服务。</p>
            <p>11.2 如您违反本协议，我们有权视情况采取包括但不限于删除内容、暂停或终止服务、封禁账号等措施。</p>
            <p>11.3 协议终止后，我们有权保留您的账号信息及过往行为记录。</p>

            <h2>12. 法律适用与争议解决</h2>
            <p>12.1 本协议适用中华人民共和国法律。</p>
            <p>12.2 因本协议引起的或与本协议有关的任何争议，应友好协商解决；协商不成的，任何一方均可向趣玩星球所在地有管辖权的人民法院提起诉讼。</p>
        </div>

        <div class="policy-footer">
            <p>如您对本协议有任何疑问，请联系我们的客服团队。</p>
        </div>
    </div>
    <script src="../js/page-protection.js"></script>
</body>
</html>
