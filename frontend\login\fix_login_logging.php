<?php
/**
 * 修复前台登录记录功能
 * 确保所有登录API都正确记录到数据库
 */

require_once '../../sql/db_config.php';

header('Content-Type: text/plain; charset=utf-8');

echo "=== 修复前台登录记录功能 ===\n\n";

try {
    $pdo = getDbConnection();
    echo "✓ 数据库连接成功\n";
} catch (Exception $e) {
    echo "✗ 数据库连接失败: " . $e->getMessage() . "\n";
    exit;
}

// 1. 确保login_logs表存在且结构正确
echo "\n1. 检查并修复login_logs表结构:\n";

try {
    // 检查表是否存在
    $stmt = $pdo->query("SHOW TABLES LIKE 'login_logs'");
    if ($stmt->rowCount() == 0) {
        echo "  创建login_logs表...\n";
        
        $createSQL = "
        CREATE TABLE `login_logs` (
          `id` int(11) NOT NULL AUTO_INCREMENT,
          `user_id` int(11) NOT NULL,
          `login_time` datetime NOT NULL,
          `ip_address` varchar(45) NOT NULL,
          `user_agent` text,
          `status` enum('success','failed') NOT NULL DEFAULT 'success',
          `device_fingerprint` varchar(32) DEFAULT NULL,
          `login_type` enum('quick_login','secure_login','normal_login','verification_code','test_login') DEFAULT 'normal_login',
          `location` varchar(100) DEFAULT NULL,
          `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
          PRIMARY KEY (`id`),
          KEY `idx_user_id` (`user_id`),
          KEY `idx_login_time` (`login_time`),
          KEY `idx_ip_address` (`ip_address`),
          KEY `idx_device_fingerprint` (`device_fingerprint`),
          KEY `idx_status` (`status`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ";
        
        $pdo->exec($createSQL);
        echo "✓ login_logs表创建成功\n";
    } else {
        echo "✓ login_logs表已存在\n";
        
        // 检查是否需要添加缺失的字段
        $stmt = $pdo->query("DESCRIBE login_logs");
        $columns = $stmt->fetchAll(PDO::FETCH_COLUMN);
        
        $required_columns = ['location', 'device_fingerprint', 'created_at'];
        foreach ($required_columns as $column) {
            if (!in_array($column, $columns)) {
                echo "  添加缺失字段: $column\n";
                
                switch ($column) {
                    case 'location':
                        $pdo->exec("ALTER TABLE login_logs ADD COLUMN location VARCHAR(100) DEFAULT NULL");
                        break;
                    case 'device_fingerprint':
                        $pdo->exec("ALTER TABLE login_logs ADD COLUMN device_fingerprint VARCHAR(32) DEFAULT NULL");
                        break;
                    case 'created_at':
                        $pdo->exec("ALTER TABLE login_logs ADD COLUMN created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP");
                        break;
                }
                echo "✓ 字段 $column 添加成功\n";
            }
        }
        
        // 检查login_type枚举值
        $stmt = $pdo->query("SHOW COLUMNS FROM login_logs LIKE 'login_type'");
        $column_info = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($column_info && strpos($column_info['Type'], 'verification_code') === false) {
            echo "  更新login_type枚举值...\n";
            $pdo->exec("ALTER TABLE login_logs MODIFY COLUMN login_type ENUM('quick_login','secure_login','normal_login','verification_code','test_login') DEFAULT 'normal_login'");
            echo "✓ login_type枚举值更新成功\n";
        }
    }
    
} catch (Exception $e) {
    echo "✗ 修复表结构失败: " . $e->getMessage() . "\n";
}

// 2. 检查login_logger.php文件
echo "\n2. 检查login_logger.php文件:\n";

$login_logger_path = 'login_logger.php';
if (file_exists($login_logger_path)) {
    echo "✓ login_logger.php文件存在\n";
    
    // 测试关键函数
    require_once $login_logger_path;
    
    if (function_exists('recordUserLoginLog')) {
        echo "✓ recordUserLoginLog函数可用\n";
    } else {
        echo "✗ recordUserLoginLog函数不存在\n";
    }
    
    if (function_exists('getUserRealIP')) {
        echo "✓ getUserRealIP函数可用\n";
    } else {
        echo "✗ getUserRealIP函数不存在\n";
    }
    
    if (function_exists('getLocationFromIP')) {
        echo "✓ getLocationFromIP函数可用\n";
    } else {
        echo "✗ getLocationFromIP函数不存在\n";
    }
    
} else {
    echo "✗ login_logger.php文件不存在\n";
    echo "  请确保该文件存在于 frontend/login/ 目录下\n";
}

// 3. 检查各个登录API文件
echo "\n3. 检查登录API文件:\n";

$login_files = [
    'quick_login_simple.php' => '快速登录',
    'secure_login_simple.php' => '安全登录',
    'verify_code_login.php' => '验证码登录',
    'quick_login_token.php' => 'Token快速登录',
    '../onboarding/complete_registration.php' => '注册完成'
];

foreach ($login_files as $file => $description) {
    if (file_exists($file)) {
        echo "✓ $description ($file) 文件存在\n";
        
        // 检查文件内容是否包含登录记录代码
        $content = file_get_contents($file);
        
        if (strpos($content, 'recordUserLoginLog') !== false) {
            echo "  ✓ 使用统一的recordUserLoginLog函数\n";
        } elseif (strpos($content, 'INSERT INTO login_logs') !== false) {
            echo "  ⚠ 使用直接插入方式，建议改为统一函数\n";
        } else {
            echo "  ✗ 未找到登录记录代码\n";
        }
    } else {
        echo "✗ $description ($file) 文件不存在\n";
    }
}

// 4. 添加一些测试数据（如果表为空）
echo "\n4. 检查测试数据:\n";

try {
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM login_logs");
    $count = $stmt->fetch()['count'];
    
    if ($count == 0) {
        echo "  数据库为空，添加测试数据...\n";
        
        // 获取前5个用户
        $stmt = $pdo->query("SELECT id FROM users ORDER BY id LIMIT 5");
        $users = $stmt->fetchAll(PDO::FETCH_COLUMN);
        
        if (!empty($users)) {
            $test_data = [];
            foreach ($users as $user_id) {
                $test_data[] = [
                    $user_id,
                    date('Y-m-d H:i:s', strtotime('-' . rand(1, 72) . ' hours')),
                    '192.168.1.' . rand(100, 200),
                    'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
                    'success',
                    'quick_login',
                    '北京市'
                ];
                
                $test_data[] = [
                    $user_id,
                    date('Y-m-d H:i:s', strtotime('-' . rand(1, 48) . ' hours')),
                    '183.232.45.' . rand(100, 200),
                    'Mozilla/5.0 (iPhone; CPU iPhone OS 17_0 like Mac OS X)',
                    'success',
                    'secure_login',
                    '上海市'
                ];
            }
            
            $stmt = $pdo->prepare("
                INSERT INTO login_logs (user_id, login_time, ip_address, user_agent, status, login_type, location)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            ");
            
            foreach ($test_data as $data) {
                $stmt->execute($data);
            }
            
            echo "✓ 已添加 " . count($test_data) . " 条测试数据\n";
        } else {
            echo "  没有用户数据，无法添加测试数据\n";
        }
    } else {
        echo "✓ 数据库中已有 $count 条登录记录\n";
    }
    
} catch (Exception $e) {
    echo "✗ 处理测试数据失败: " . $e->getMessage() . "\n";
}

echo "\n=== 修复完成 ===\n";
echo "\n现在可以:\n";
echo "1. 运行 test_login_logging.php 查看登录记录\n";
echo "2. 进行实际登录测试\n";
echo "3. 在后台查看用户的设备和IP信息\n";
echo "\n如果仍然没有数据，请:\n";
echo "1. 确保用户实际进行了登录操作\n";
echo "2. 检查PHP错误日志\n";
echo "3. 确认数据库权限正常\n";
?>
