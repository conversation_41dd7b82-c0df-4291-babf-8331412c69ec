<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>布局测试 - 趣玩星球管理后台</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="assets/css/admin-layout.css">
    <style>
        /* 测试页面专用样式 */
        .test-content {
            padding: 24px;
        }

        .test-section {
            background: white;
            border-radius: 16px;
            padding: 24px;
            margin-bottom: 24px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
        }

        .test-title {
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--gray-800);
            margin-bottom: 16px;
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .test-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 16px;
        }

        .test-item {
            padding: 16px;
            background: var(--gray-50);
            border-radius: 12px;
            border-left: 4px solid var(--primary-color);
        }

        .test-item h4 {
            color: var(--gray-800);
            margin-bottom: 8px;
        }

        .test-item p {
            color: var(--gray-600);
            font-size: 0.875rem;
        }

        .status-badge {
            display: inline-flex;
            align-items: center;
            gap: 4px;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 0.75rem;
            font-weight: 600;
        }

        .status-success {
            background: rgba(16, 185, 129, 0.1);
            color: var(--success-color);
        }

        .status-warning {
            background: rgba(245, 158, 11, 0.1);
            color: var(--warning-color);
        }

        /* 顶部导航栏样式 */
        .topbar {
            height: 60px;
            background: white;
            border-bottom: 1px solid var(--gray-200);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 24px;
            box-shadow: var(--shadow-sm);
        }

        .topbar-left {
            display: flex;
            align-items: center;
            gap: 16px;
        }

        .page-breadcrumb {
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 0.875rem;
            color: var(--gray-600);
        }

        .breadcrumb-current {
            color: var(--primary-color);
            font-weight: 600;
        }

        .topbar-right {
            display: flex;
            align-items: center;
            gap: 20px;
        }

        .user-profile-dropdown {
            position: relative;
        }

        .user-profile-trigger {
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 8px 12px;
            border-radius: var(--border-radius);
            cursor: pointer;
            transition: var(--transition);
            border: 1px solid transparent;
        }

        .user-profile-trigger:hover {
            background: var(--gray-50);
            border-color: var(--gray-200);
        }

        .user-avatar {
            width: 36px;
            height: 36px;
            background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
            border-radius: var(--border-radius);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: 600;
            font-size: 14px;
        }

        .user-info {
            display: flex;
            flex-direction: column;
            align-items: flex-start;
        }

        .user-name {
            font-weight: 600;
            color: var(--gray-800);
            font-size: 14px;
            line-height: 1.2;
        }

        .user-role {
            font-size: 12px;
            color: var(--gray-500);
            line-height: 1.2;
        }

        .dropdown-arrow {
            font-size: 12px;
            color: var(--gray-400);
            transition: var(--transition);
        }
    </style>
</head>
<body>
    <div class="admin-layout">
        <!-- 左侧菜单 -->
        <aside class="sidebar">
            <div class="sidebar-header">
                <div class="logo">
                    <div class="logo-icon">
                        <i class="fas fa-star"></i>
                    </div>
                    <div class="logo-text">
                        <h2>趣玩星球</h2>
                        <p>管理后台</p>
                    </div>
                </div>
            </div>

            <nav class="sidebar-nav">
                <ul class="nav-list">
                    <!-- 首页 -->
                    <li class="nav-item">
                        <a href="#" class="nav-link">
                            <i class="fas fa-home"></i>
                            <span>首页</span>
                        </a>
                    </li>

                    <!-- 用户管理 -->
                    <li class="nav-item has-submenu active expanded">
                        <a href="#" class="nav-link" onclick="toggleSubmenu(this)">
                            <i class="fas fa-users"></i>
                            <span>用户管理</span>
                            <i class="fas fa-chevron-down submenu-arrow" style="transform: rotate(180deg);"></i>
                        </a>
                        <ul class="submenu show">
                            <li class="submenu-item">
                                <a href="#" class="submenu-link">
                                    <i class="fas fa-list"></i>
                                    <span>用户列表</span>
                                </a>
                            </li>
                            <li class="submenu-item active">
                                <a href="#" class="submenu-link">
                                    <i class="fas fa-user-check"></i>
                                    <span>实名认证审核</span>
                                    <span class="badge">3</span>
                                </a>
                            </li>
                        </ul>
                    </li>

                    <!-- 数据统计 -->
                    <li class="nav-item has-submenu">
                        <a href="#" class="nav-link" onclick="toggleSubmenu(this)">
                            <i class="fas fa-chart-bar"></i>
                            <span>数据统计</span>
                            <i class="fas fa-chevron-down submenu-arrow"></i>
                        </a>
                        <ul class="submenu">
                            <li class="submenu-item">
                                <a href="#" class="submenu-link">
                                    <i class="fas fa-tachometer-alt"></i>
                                    <span>仪表盘</span>
                                </a>
                            </li>
                        </ul>
                    </li>

                    <!-- 系统设置 -->
                    <li class="nav-item has-submenu">
                        <a href="#" class="nav-link" onclick="toggleSubmenu(this)">
                            <i class="fas fa-cog"></i>
                            <span>系统设置</span>
                            <i class="fas fa-chevron-down submenu-arrow"></i>
                        </a>
                        <ul class="submenu">
                            <li class="submenu-item">
                                <a href="#" class="submenu-link">
                                    <i class="fas fa-sliders-h"></i>
                                    <span>系统配置</span>
                                </a>
                            </li>
                            <li class="submenu-item">
                                <a href="#" class="submenu-link">
                                    <i class="fas fa-file-alt"></i>
                                    <span>日志管理</span>
                                </a>
                            </li>
                            <li class="submenu-item">
                                <a href="#" class="submenu-link">
                                    <i class="fas fa-shield-alt"></i>
                                    <span>权限管理</span>
                                </a>
                            </li>
                        </ul>
                    </li>
                </ul>
            </nav>

            <div class="sidebar-footer">
                <!-- 底部留空，退出登录已移至顶部 -->
            </div>
        </aside>

        <!-- 主内容区域 -->
        <main class="main-content">
            <!-- 顶部导航栏 -->
            <header class="topbar">
                <div class="topbar-left">
                    <div class="page-breadcrumb">
                        <span class="breadcrumb-item">趣玩星球管理后台</span>
                        <i class="fas fa-chevron-right"></i>
                        <span class="breadcrumb-current">布局测试</span>
                    </div>
                </div>

                <div class="topbar-right">
                    <div class="user-profile-dropdown">
                        <div class="user-profile-trigger">
                            <div class="user-avatar">测</div>
                            <div class="user-info">
                                <div class="user-name">测试管理员</div>
                                <div class="user-role">工号：12001</div>
                            </div>
                            <i class="fas fa-chevron-down dropdown-arrow"></i>
                        </div>
                    </div>
                </div>
            </header>

            <div class="test-content">
                <div class="test-section">
                    <h2 class="test-title">
                        <i class="fas fa-check-circle" style="color: var(--success-color);"></i>
                        布局测试页面
                    </h2>
                    <p style="color: var(--gray-600); margin-bottom: 24px;">
                        这是一个测试页面，用于验证新的左侧菜单布局系统是否正常工作。
                    </p>

                    <div class="test-grid">
                        <div class="test-item">
                            <h4>左侧菜单</h4>
                            <p>支持父子菜单结构，智能展开收起</p>
                            <span class="status-badge status-success">
                                <i class="fas fa-check"></i>
                                正常
                            </span>
                        </div>

                        <div class="test-item">
                            <h4>响应式设计</h4>
                            <p>适配桌面、平板、手机各种设备</p>
                            <span class="status-badge status-success">
                                <i class="fas fa-check"></i>
                                正常
                            </span>
                        </div>

                        <div class="test-item">
                            <h4>徽章提示</h4>
                            <p>实时显示待处理事项数量</p>
                            <span class="status-badge status-success">
                                <i class="fas fa-check"></i>
                                正常
                            </span>
                        </div>

                        <div class="test-item">
                            <h4>交互动画</h4>
                            <p>流畅的悬浮和点击动画效果</p>
                            <span class="status-badge status-success">
                                <i class="fas fa-check"></i>
                                正常
                            </span>
                        </div>
                    </div>
                </div>

                <div class="test-section">
                    <h3 class="test-title">
                        <i class="fas fa-keyboard" style="color: var(--info-color);"></i>
                        快捷键测试
                    </h3>
                    <div class="test-grid">
                        <div class="test-item">
                            <h4>Alt + H</h4>
                            <p>跳转到首页</p>
                        </div>
                        <div class="test-item">
                            <h4>Alt + U</h4>
                            <p>跳转到用户管理</p>
                        </div>
                        <div class="test-item">
                            <h4>Alt + V</h4>
                            <p>跳转到实名认证</p>
                        </div>
                        <div class="test-item">
                            <h4>Alt + D</h4>
                            <p>跳转到数据统计</p>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <script src="assets/js/admin-layout.js"></script>
    <script>
        // 菜单交互脚本
        function toggleSubmenu(element) {
            const submenu = element.nextElementSibling;
            const arrow = element.querySelector('.submenu-arrow');
            const navItem = element.parentElement;

            // 切换显示状态
            submenu.classList.toggle('show');
            navItem.classList.toggle('expanded');

            // 旋转箭头
            if (submenu.classList.contains('show')) {
                arrow.style.transform = 'rotate(180deg)';
            } else {
                arrow.style.transform = 'rotate(0deg)';
            }
        }

        console.log('🧪 布局测试页面已加载');
        console.log('📱 请测试不同屏幕尺寸下的响应式效果');
    </script>
</body>
</html>
