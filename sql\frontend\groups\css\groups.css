/* 组局页面样式 */

/* CSS变量定义 */
:root {
    --primary-color: #6F7BF5;
    --primary-light: #A8B2F8;
    --primary-dark: #5A67E8;
    --secondary-color: #8B95F7;
    --accent-color: #FF6B9D;
    --success-color: #06D6A0;
    --warning-color: #FFD166;
    --error-color: #FF6B6B;
    --gradient-primary: linear-gradient(135deg, #6F7BF5, #8B95F7);
    --gradient-secondary: linear-gradient(135deg, #A8B2F8, #6F7BF5);
    --gradient-warm: linear-gradient(135deg, #FF6B9D, #FFD166);
    --text-primary: #333333;
    --text-secondary: #666666;
    --text-light: #999999;
    --bg-white: #FFFFFF;
    --bg-light: #F8F9FA;
    --shadow-sm: 0 2px 8px rgba(0, 0, 0, 0.08);
    --shadow-md: 0 4px 16px rgba(0, 0, 0, 0.12);
    --shadow-lg: 0 8px 32px rgba(0, 0, 0, 0.16);
    --radius-sm: 8px;
    --radius-md: 12px;
    --radius-lg: 16px;
    --radius-xl: 24px;
    --transition-fast: 0.2s ease;
    --transition-normal: 0.3s ease;
}

/* 基础样式重置 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

body {
    background: var(--bg-light);
    color: var(--text-primary);
    line-height: 1.6;
    min-height: 100vh;
}

/* 顶部导航栏 */
.header-bar {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 16px 20px;
    background: var(--bg-white);
    color: var(--text-primary);
    position: sticky;
    top: 0;
    z-index: 100;
    box-shadow: var(--shadow-sm);
    border-bottom: 1px solid #E5E7EB;
}

.back-button,
.create-button {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: var(--bg-light);
    border: none;
    color: var(--text-primary);
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: var(--transition-normal);
    text-decoration: none;
}

.back-button:hover,
.create-button:hover {
    background: var(--primary-color);
    color: white;
    transform: scale(1.05);
}

.create-button {
    background: var(--gradient-primary);
    color: white;
}

.create-button:hover {
    background: var(--gradient-secondary);
    transform: scale(1.1);
}

.header-title {
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--text-primary);
    flex: 1;
    text-align: center;
}

/* 统计信息 */
.stats-section {
    padding: 20px;
    background: var(--bg-white);
    margin-bottom: 20px;
}

.stats-container {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 16px;
}

.stat-item {
    text-align: center;
    padding: 16px 8px;
    background: var(--bg-light);
    border-radius: var(--radius-md);
    transition: var(--transition-normal);
}

.stat-item:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-sm);
}

.stat-value {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: 4px;
}

.stat-label {
    font-size: 0.8rem;
    color: var(--text-secondary);
    font-weight: 500;
}

/* 活动列表 */
.activities-container {
    padding: 0 20px 20px;
}

.activities-list {
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.activity-card {
    background: var(--bg-white);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-md);
    overflow: hidden;
    transition: var(--transition-normal);
}

.activity-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.activity-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 20px 12px;
    border-bottom: 1px solid var(--bg-light);
}

.activity-type {
    font-size: 0.875rem;
    font-weight: 600;
    color: var(--primary-color);
    background: rgba(111, 123, 245, 0.1);
    padding: 4px 12px;
    border-radius: var(--radius-lg);
}

.activity-status {
    font-size: 0.75rem;
    font-weight: 600;
    padding: 4px 8px;
    border-radius: var(--radius-sm);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.status-draft {
    background: #F3F4F6;
    color: #6B7280;
}

.status-recruiting {
    background: #DCFCE7;
    color: #166534;
}

.status-full {
    background: #FEF3C7;
    color: #92400E;
}

.status-ongoing {
    background: #DBEAFE;
    color: #1E40AF;
}

.status-completed {
    background: #E0E7FF;
    color: #5B21B6;
}

.status-cancelled {
    background: #FEE2E2;
    color: #991B1B;
}

.activity-content {
    padding: 16px 20px;
}

.activity-title {
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 12px;
    line-height: 1.4;
}

.activity-info {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 8px;
    margin-bottom: 12px;
}

.info-item {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 0.875rem;
    color: var(--text-secondary);
}

.info-item i {
    color: var(--primary-color);
    width: 14px;
    text-align: center;
}

.activity-features {
    display: flex;
    flex-wrap: wrap;
    gap: 6px;
    margin-top: 12px;
}

.feature-tag {
    background: var(--bg-light);
    color: var(--text-secondary);
    padding: 4px 8px;
    border-radius: var(--radius-sm);
    font-size: 0.75rem;
    font-weight: 500;
}

.feature-more {
    background: var(--primary-color);
    color: white;
    padding: 4px 8px;
    border-radius: var(--radius-sm);
    font-size: 0.75rem;
    font-weight: 600;
}

.activity-footer {
    padding: 12px 20px 16px;
    border-top: 1px solid var(--bg-light);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.activity-time {
    font-size: 0.75rem;
    color: var(--text-light);
}

.activity-actions {
    display: flex;
    gap: 8px;
}

.action-btn {
    padding: 6px 12px;
    border: none;
    border-radius: var(--radius-sm);
    font-size: 0.75rem;
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition-normal);
    display: flex;
    align-items: center;
    gap: 4px;
}

.edit-btn {
    background: var(--bg-light);
    color: var(--text-secondary);
}

.edit-btn:hover {
    background: var(--primary-color);
    color: white;
}

.publish-btn {
    background: var(--gradient-primary);
    color: white;
}

.publish-btn:hover {
    background: var(--gradient-secondary);
    transform: translateY(-1px);
}

.view-btn {
    background: var(--bg-light);
    color: var(--text-secondary);
}

.view-btn:hover {
    background: var(--success-color);
    color: white;
}

.manage-btn {
    background: var(--warning-color);
    color: white;
}

.manage-btn:hover {
    background: #F59E0B;
    transform: translateY(-1px);
}

/* 空状态 */
.empty-state {
    text-align: center;
    padding: 60px 20px;
    color: var(--text-light);
}

.empty-state i {
    font-size: 3rem;
    margin-bottom: 16px;
    color: var(--text-light);
}

.empty-state p {
    font-size: 1rem;
    margin-bottom: 8px;
    color: var(--text-secondary);
}

.empty-desc {
    font-size: 0.875rem;
    margin-bottom: 24px !important;
    color: var(--text-light);
}

.empty-state .btn {
    display: inline-block;
    padding: 12px 24px;
    background: var(--gradient-primary);
    color: white;
    border-radius: var(--radius-xl);
    text-decoration: none;
    font-size: 0.875rem;
    font-weight: 600;
    box-shadow: var(--shadow-md);
    transition: var(--transition-normal);
}

.empty-state .btn:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

/* Toast提示 */
.toast {
    position: fixed;
    bottom: 100px;
    left: 50%;
    transform: translateX(-50%);
    background: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 12px 20px;
    border-radius: var(--radius-lg);
    font-size: 0.875rem;
    z-index: 10000;
    display: none;
    backdrop-filter: blur(10px);
}

/* 响应式设计 */
@media (max-width: 768px) {
    .stats-container {
        grid-template-columns: repeat(2, 1fr);
        gap: 12px;
    }
    
    .activity-info {
        grid-template-columns: 1fr;
        gap: 6px;
    }
    
    .activity-actions {
        flex-direction: column;
        gap: 6px;
    }
    
    .action-btn {
        width: 100%;
        justify-content: center;
    }
}

@media (max-width: 480px) {
    .header-bar {
        padding: 12px 16px;
    }
    
    .stats-section,
    .activities-container {
        padding: 16px;
    }
    
    .activity-header,
    .activity-content,
    .activity-footer {
        padding-left: 16px;
        padding-right: 16px;
    }
}
