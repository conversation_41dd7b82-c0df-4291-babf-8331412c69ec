<?php
session_start();
require_once '../db_config.php';

// 检查管理员登录状态
if (!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
    header('Location: ../login.php');
    exit;
}

echo "<h1>数据库表结构检查</h1>";
echo "<style>
    table { border-collapse: collapse; margin: 20px 0; }
    th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
    th { background-color: #f2f2f2; }
    .success { color: green; font-weight: bold; }
    .error { color: red; font-weight: bold; }
    .warning { color: orange; font-weight: bold; }
</style>";

try {
    $pdo = getDbConnection();
    echo "<p class='success'>✅ 数据库连接成功！</p>";
    
    // 检查 admin_logs 表结构
    echo "<h2>admin_logs 表当前结构</h2>";
    $stmt = $pdo->query("DESCRIBE admin_logs");
    $admin_logs_columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<table>";
    echo "<tr><th>字段名</th><th>类型</th><th>允许NULL</th><th>键</th><th>默认值</th><th>额外</th></tr>";
    
    $has_employee_id = false;
    $has_department = false;
    
    foreach ($admin_logs_columns as $column) {
        echo "<tr>";
        echo "<td>" . htmlspecialchars($column['Field']) . "</td>";
        echo "<td>" . htmlspecialchars($column['Type']) . "</td>";
        echo "<td>" . htmlspecialchars($column['Null']) . "</td>";
        echo "<td>" . htmlspecialchars($column['Key']) . "</td>";
        echo "<td>" . htmlspecialchars($column['Default'] ?? 'NULL') . "</td>";
        echo "<td>" . htmlspecialchars($column['Extra']) . "</td>";
        echo "</tr>";
        
        if ($column['Field'] === 'employee_id') {
            $has_employee_id = true;
        }
        if ($column['Field'] === 'department') {
            $has_department = true;
        }
    }
    echo "</table>";
    
    echo "<h3>admin_logs 表字段检查结果：</h3>";
    echo "<ul>";
    echo "<li>" . ($has_employee_id ? "<span class='success'>✅ employee_id 字段已存在</span>" : "<span class='error'>❌ employee_id 字段缺失</span>") . "</li>";
    echo "<li>" . ($has_department ? "<span class='success'>✅ department 字段已存在</span>" : "<span class='error'>❌ department 字段缺失</span>") . "</li>";
    echo "</ul>";
    
    // 检查 user_logs 表是否存在
    echo "<h2>user_logs 表检查</h2>";
    $stmt = $pdo->query("SHOW TABLES LIKE 'user_logs'");
    if ($stmt->rowCount() > 0) {
        echo "<p class='success'>✅ user_logs 表存在</p>";
        
        $stmt = $pdo->query("DESCRIBE user_logs");
        $user_logs_columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo "<table>";
        echo "<tr><th>字段名</th><th>类型</th><th>允许NULL</th><th>键</th><th>默认值</th><th>额外</th></tr>";
        foreach ($user_logs_columns as $column) {
            echo "<tr>";
            echo "<td>" . htmlspecialchars($column['Field']) . "</td>";
            echo "<td>" . htmlspecialchars($column['Type']) . "</td>";
            echo "<td>" . htmlspecialchars($column['Null']) . "</td>";
            echo "<td>" . htmlspecialchars($column['Key']) . "</td>";
            echo "<td>" . htmlspecialchars($column['Default'] ?? 'NULL') . "</td>";
            echo "<td>" . htmlspecialchars($column['Extra']) . "</td>";
            echo "</tr>";
        }
        echo "</table>";
        
        // 获取记录数量
        $stmt = $pdo->query("SELECT COUNT(*) as count FROM user_logs");
        $count = $stmt->fetch()['count'];
        echo "<p>表中共有 {$count} 条记录</p>";
        
    } else {
        echo "<p class='error'>❌ user_logs 表不存在</p>";
    }
    
    // 检查 admin_users 表结构
    echo "<h2>admin_users 表结构检查</h2>";
    $stmt = $pdo->query("DESCRIBE admin_users");
    $admin_users_columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<table>";
    echo "<tr><th>字段名</th><th>类型</th><th>允许NULL</th><th>键</th><th>默认值</th><th>额外</th></tr>";
    
    $admin_has_employee_id = false;
    $admin_has_department = false;
    
    foreach ($admin_users_columns as $column) {
        echo "<tr>";
        echo "<td>" . htmlspecialchars($column['Field']) . "</td>";
        echo "<td>" . htmlspecialchars($column['Type']) . "</td>";
        echo "<td>" . htmlspecialchars($column['Null']) . "</td>";
        echo "<td>" . htmlspecialchars($column['Key']) . "</td>";
        echo "<td>" . htmlspecialchars($column['Default'] ?? 'NULL') . "</td>";
        echo "<td>" . htmlspecialchars($column['Extra']) . "</td>";
        echo "</tr>";
        
        if ($column['Field'] === 'employee_id') {
            $admin_has_employee_id = true;
        }
        if ($column['Field'] === 'department') {
            $admin_has_department = true;
        }
    }
    echo "</table>";
    
    echo "<h3>admin_users 表字段检查结果：</h3>";
    echo "<ul>";
    echo "<li>" . ($admin_has_employee_id ? "<span class='success'>✅ employee_id 字段已存在</span>" : "<span class='error'>❌ employee_id 字段缺失</span>") . "</li>";
    echo "<li>" . ($admin_has_department ? "<span class='success'>✅ department 字段已存在</span>" : "<span class='error'>❌ department 字段缺失</span>") . "</li>";
    echo "</ul>";
    
    // 生成修复建议
    echo "<h2>修复建议</h2>";
    
    $need_fixes = [];
    
    if (!$has_department) {
        $need_fixes[] = "ALTER TABLE admin_logs ADD COLUMN department VARCHAR(100) DEFAULT NULL;";
    }
    
    if ($stmt->rowCount() == 0) { // user_logs 表不存在
        $need_fixes[] = "CREATE TABLE user_logs (
    id INT(11) NOT NULL AUTO_INCREMENT,
    user_id INT(11) NOT NULL,
    operator_name VARCHAR(100) NOT NULL,
    employee_id VARCHAR(50) DEFAULT NULL,
    department VARCHAR(100) DEFAULT NULL,
    type VARCHAR(50) NOT NULL,
    content TEXT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (id)
);";
    }
    
    if (!$admin_has_employee_id) {
        $need_fixes[] = "ALTER TABLE admin_users ADD COLUMN employee_id VARCHAR(50) DEFAULT NULL;";
    }
    
    if (!$admin_has_department) {
        $need_fixes[] = "ALTER TABLE admin_users ADD COLUMN department VARCHAR(100) DEFAULT NULL;";
    }
    
    if (empty($need_fixes)) {
        echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; padding: 15px; border-radius: 5px;'>";
        echo "<h3 class='success'>🎉 所有表结构都正确！</h3>";
        echo "<p>数据库表结构完整，管理日志功能应该可以正常使用。</p>";
        echo "<p><a href='detail.php?id=1'>测试用户详情页面</a></p>";
        echo "</div>";
    } else {
        echo "<div style='background: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 5px;'>";
        echo "<h3 class='warning'>🔧 需要执行以下SQL语句：</h3>";
        echo "<p>请在数据库中<strong>逐条执行</strong>以下SQL语句：</p>";
        echo "<pre style='background: #f8f9fa; padding: 10px; border-radius: 5px;'>";
        foreach ($need_fixes as $i => $sql) {
            echo "-- 第" . ($i + 1) . "步\n";
            echo $sql . "\n\n";
        }
        echo "</pre>";
        echo "</div>";
    }
    
    // 测试日志插入功能
    if ($has_employee_id && $has_department && $stmt->rowCount() > 0) {
        echo "<h2>功能测试</h2>";
        
        // 获取一个测试用户ID
        $stmt = $pdo->query("SELECT id FROM users LIMIT 1");
        $test_user = $stmt->fetch();
        
        if ($test_user) {
            echo "<p><strong>测试建议：</strong></p>";
            echo "<ol>";
            echo "<li>访问用户详情页面：<a href='detail.php?id=" . $test_user['id'] . "'>用户ID " . $test_user['id'] . " 详情页</a></li>";
            echo "<li>点击'写日志'按钮</li>";
            echo "<li>填写测试日志信息</li>";
            echo "<li>提交并查看是否成功</li>";
            echo "</ol>";
        }
    }
    
} catch (PDOException $e) {
    echo "<p class='error'>❌ 数据库错误：" . htmlspecialchars($e->getMessage()) . "</p>";
}

echo "<br><br><a href='index.php'>返回用户管理</a>";
?>
