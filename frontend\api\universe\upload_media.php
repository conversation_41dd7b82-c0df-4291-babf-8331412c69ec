<?php
// 设置响应头
header('Content-Type: application/json');

// 启动会话
session_start();

// 检查用户是否已登录
if (!isset($_SESSION['user_id'])) {
    // 为了测试，暂时跳过登录检查
    $_SESSION['user_id'] = 1; // 使用默认用户ID

    // 记录调试信息
    error_log("用户未登录，使用默认用户ID: 1");

    // 正式环境应该取消注释下面的代码
    /*
    echo json_encode([
        'success' => false,
        'message' => '请先登录'
    ]);
    exit;
    */
}

// 引入Cloudinary助手
require_once '../../includes/cloudinary_helper.php';

// 创建Cloudinary助手实例
$cloudinary = new CloudinaryHelper();

// 启用错误报告
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// 处理上传请求
$response = [
    'success' => false,
    'message' => '未知错误',
    'files' => [],
    'debug' => []
];

// 添加调试信息
$response['debug']['session'] = isset($_SESSION['user_id']) ? 'User ID: ' . $_SESSION['user_id'] : 'No user session';
$response['debug']['files'] = $_FILES;

// 处理图片上传
if (isset($_FILES['images'])) {
    $images = reArrayFiles($_FILES['images']);

    foreach ($images as $image) {
        if ($image['error'] === UPLOAD_ERR_OK) {
            // 检查文件类型
            $allowed_types = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
            if (!in_array($image['type'], $allowed_types)) {
                continue; // 跳过不允许的文件类型
            }

            // 上传到Cloudinary
            $result = $cloudinary->uploadImage($image['tmp_name'], [
                'public_id' => 'universe_' . time() . '_' . mt_rand(1000, 9999)
            ]);

            if ($result['success']) {
                $response['files'][] = [
                    'id' => $result['data']['public_id'],
                    'url' => $result['data']['secure_url'],
                    'type' => 'image'
                ];
            }
        }
    }

    if (count($response['files']) > 0) {
        $response['success'] = true;
        $response['message'] = '上传成功';
    } else {
        $response['message'] = '上传失败，请检查文件格式';
    }
}
// 处理视频上传
else if (isset($_FILES['video']) && $_FILES['video']['error'] === UPLOAD_ERR_OK) {
    // 检查文件类型
    $allowed_types = ['video/mp4', 'video/webm', 'video/ogg'];
    if (in_array($_FILES['video']['type'], $allowed_types)) {
        // 上传到Cloudinary
        $result = $cloudinary->uploadVideo($_FILES['video']['tmp_name'], [
            'public_id' => 'universe_video_' . time() . '_' . mt_rand(1000, 9999)
        ]);

        if ($result['success']) {
            $response['files'][] = [
                'id' => $result['data']['public_id'],
                'url' => $result['data']['secure_url'],
                'type' => 'video'
            ];

            $response['success'] = true;
            $response['message'] = '上传成功';
        } else {
            $response['message'] = '视频上传失败';
        }
    } else {
        $response['message'] = '不支持的视频格式';
    }
}
// 处理音频上传
else if (isset($_FILES['audio']) && $_FILES['audio']['error'] === UPLOAD_ERR_OK) {
    // 检查文件类型
    $allowed_types = ['audio/mpeg', 'audio/mp3', 'audio/wav', 'audio/ogg'];
    if (in_array($_FILES['audio']['type'], $allowed_types)) {
        // 上传到Cloudinary
        $result = $cloudinary->uploadAudio($_FILES['audio']['tmp_name'], [
            'public_id' => 'universe_audio_' . time() . '_' . mt_rand(1000, 9999)
        ]);

        if ($result['success']) {
            $response['files'][] = [
                'id' => $result['data']['public_id'],
                'url' => $result['data']['secure_url'],
                'type' => 'audio'
            ];

            $response['success'] = true;
            $response['message'] = '上传成功';
        } else {
            $response['message'] = '音频上传失败';
        }
    } else {
        $response['message'] = '不支持的音频格式';
    }
}
// 处理封面图片上传
else if (isset($_FILES['cover']) && $_FILES['cover']['error'] === UPLOAD_ERR_OK) {
    // 检查文件类型
    $allowed_types = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
    if (in_array($_FILES['cover']['type'], $allowed_types)) {
        // 上传到Cloudinary
        $result = $cloudinary->uploadImage($_FILES['cover']['tmp_name'], [
            'public_id' => 'universe_cover_' . time() . '_' . mt_rand(1000, 9999),
            'transformation' => 'w_800,h_450,c_fill,g_auto'
        ]);

        if ($result['success']) {
            $response['files'][] = [
                'id' => $result['data']['public_id'],
                'url' => $result['data']['secure_url'],
                'type' => 'image'
            ];

            $response['success'] = true;
            $response['message'] = '上传成功';
        } else {
            $response['message'] = '封面上传失败';
        }
    } else {
        $response['message'] = '不支持的图片格式';
    }
} else {
    $response['message'] = '未检测到上传文件';
}

// 返回JSON响应
echo json_encode($response);

/**
 * 重新组织文件数组
 * @param array $file_post $_FILES数组
 * @return array 重组后的数组
 */
function reArrayFiles($file_post) {
    $file_ary = [];
    $file_count = count($file_post['name']);
    $file_keys = array_keys($file_post);

    for ($i = 0; $i < $file_count; $i++) {
        foreach ($file_keys as $key) {
            $file_ary[$i][$key] = $file_post[$key][$i];
        }
    }

    return $file_ary;
}
