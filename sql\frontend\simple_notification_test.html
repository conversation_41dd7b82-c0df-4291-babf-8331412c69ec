<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>简化通知测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            font-weight: bold;
        }
        .status.success { background: #d4edda; color: #155724; }
        .status.error { background: #f8d7da; color: #721c24; }
        .status.info { background: #d1ecf1; color: #0c5460; }
        .status.warning { background: #fff3cd; color: #856404; }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        .log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 10px;
            margin: 10px 0;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔔 简化通知测试</h1>

        <div class="status info">
            <strong>测试说明：</strong>这个页面用于调试实时通知功能，不依赖复杂的集成脚本
        </div>

        <div>
            <label>用户ID: <input type="number" id="userId" value="4"></label>
            <button onclick="startTest()">开始测试</button>
            <button onclick="stopTest()">停止测试</button>
            <button onclick="clearLog()">清空日志</button>
        </div>

        <div id="status" class="status info">等待开始测试...</div>

        <div class="log" id="log">点击"开始测试"按钮开始...\n</div>

        <!-- 通知容器 -->
        <div id="notification-container" style="
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 10000;
            pointer-events: none;
        "></div>
    </div>

    <script>
        let pollInterval = null;
        let isRunning = false;

        function log(message) {
            const logDiv = document.getElementById('log');
            const time = new Date().toLocaleTimeString();
            logDiv.textContent += `[${time}] ${message}\n`;
            logDiv.scrollTop = logDiv.scrollHeight;
            console.log(`[通知测试] ${message}`);
        }

        function setStatus(message, type = 'info') {
            const statusDiv = document.getElementById('status');
            statusDiv.textContent = message;
            statusDiv.className = `status ${type}`;
        }

        function startTest() {
            if (isRunning) {
                log('测试已在运行中');
                return;
            }

            const userId = document.getElementById('userId').value;
            if (!userId || userId === '0') {
                setStatus('请输入有效的用户ID', 'error');
                return;
            }

            isRunning = true;
            log(`开始测试用户 ${userId} 的实时通知`);
            setStatus('正在轮询通知...', 'info');

            // 每2秒轮询一次
            pollInterval = setInterval(() => {
                pollNotifications(userId);
            }, 2000);

            // 立即执行一次
            pollNotifications(userId);
        }

        function stopTest() {
            if (pollInterval) {
                clearInterval(pollInterval);
                pollInterval = null;
            }
            isRunning = false;
            log('测试已停止');
            setStatus('测试已停止', 'warning');
        }

        function clearLog() {
            document.getElementById('log').textContent = '';
        }

        async function pollNotifications(userId) {
            try {
                const response = await fetch(`api/simple_websocket_server.php?action=poll&user_id=${userId}`);
                const data = await response.json();

                if (data.success) {
                    log(`轮询成功，找到 ${data.data.count} 条通知`);

                    if (data.debug) {
                        log(`调试信息: ${JSON.stringify(data.debug)}`);
                    }

                    if (data.data.notifications && data.data.notifications.length > 0) {
                        setStatus(`收到 ${data.data.count} 条新通知！`, 'success');

                        data.data.notifications.forEach(notification => {
                            log(`处理通知: ${notification.title}`);
                            showNotification(notification);
                        });
                    } else {
                        setStatus('轮询正常，无新通知', 'info');
                    }
                } else {
                    log(`轮询失败: ${data.error}`);
                    setStatus('轮询失败', 'error');
                }
            } catch (error) {
                log(`请求错误: ${error.message}`);
                setStatus('请求失败', 'error');
            }
        }

        function showNotification(notification) {
            try {
                const data = typeof notification.data === 'string' ?
                    JSON.parse(notification.data) : notification.data;

                log(`显示通知弹窗: ${notification.title}`);

                if (notification.type === 'verification_code') {
                    showVerificationModal(notification, data);
                } else {
                    showToast(notification.title, notification.content);
                }

                // 标记为已读
                markAsRead(notification.id);

            } catch (error) {
                log(`显示通知失败: ${error.message}`);
                showToast('通知错误', notification.content || '收到新通知');
            }
        }

        function showVerificationModal(notification, data) {
            const modal = document.createElement('div');
            modal.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0, 0, 0, 0.6);
                z-index: 10001;
                display: flex;
                align-items: center;
                justify-content: center;
                padding: 20px;
            `;

            const code = data?.code || '******';
            const adminNote = data?.admin_note || '';
            const sentBy = data?.sent_by || '管理员';

            modal.innerHTML = `
                <div style="
                    background: white;
                    border-radius: 16px;
                    padding: 32px 24px;
                    max-width: 400px;
                    width: 100%;
                    text-align: center;
                    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
                ">
                    <div style="
                        width: 60px;
                        height: 60px;
                        background: linear-gradient(135deg, #6F7BF5 0%, #9B59B6 100%);
                        border-radius: 50%;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        margin: 0 auto 16px;
                        color: white;
                        font-size: 24px;
                    ">🔑</div>

                    <h3 style="margin: 0 0 8px 0; color: #2D3748;">${notification.title}</h3>
                    <p style="color: #718096; font-size: 14px; margin: 0 0 24px 0;">来自 ${sentBy}</p>

                    <div style="
                        background: linear-gradient(135deg, #6F7BF5 0%, #9B59B6 100%);
                        color: white;
                        padding: 20px;
                        border-radius: 12px;
                        font-size: 32px;
                        font-weight: 700;
                        letter-spacing: 8px;
                        font-family: 'Courier New', monospace;
                        margin: 24px 0;
                        cursor: pointer;
                        user-select: all;
                    " onclick="
                        navigator.clipboard.writeText('${code}').then(() => {
                            alert('验证码已复制到剪贴板');
                        }).catch(() => {
                            prompt('请手动复制验证码:', '${code}');
                        });
                    ">${code}</div>

                    ${adminNote ? `
                    <div style="
                        background: #F7FAFC;
                        border-radius: 8px;
                        padding: 16px;
                        margin: 20px 0;
                        text-align: left;
                        color: #4A5568;
                        font-size: 14px;
                    ">
                        📝 备注：${adminNote}
                    </div>
                    ` : ''}

                    <button onclick="this.closest('div').remove();" style="
                        width: 100%;
                        padding: 12px 16px;
                        background: linear-gradient(135deg, #6F7BF5 0%, #9B59B6 100%);
                        color: white;
                        border: none;
                        border-radius: 8px;
                        font-size: 14px;
                        font-weight: 600;
                        cursor: pointer;
                    ">我知道了</button>
                </div>
            `;

            document.body.appendChild(modal);

            // 自动关闭
            setTimeout(() => {
                if (document.body.contains(modal)) {
                    modal.remove();
                }
            }, 60000);
        }

        function showToast(title, message) {
            const container = document.getElementById('notification-container');
            const toast = document.createElement('div');

            toast.style.cssText = `
                background: #3182CE;
                color: white;
                padding: 16px 20px;
                border-radius: 8px;
                margin-bottom: 12px;
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
                pointer-events: auto;
                cursor: pointer;
                max-width: 350px;
                transform: translateX(100%);
                transition: transform 0.3s ease;
            `;

            toast.innerHTML = `
                <div style="font-weight: 600; margin-bottom: 4px;">${title}</div>
                <div style="font-size: 14px; opacity: 0.9;">${message}</div>
            `;

            container.appendChild(toast);

            // 显示动画
            setTimeout(() => {
                toast.style.transform = 'translateX(0)';
            }, 100);

            // 点击关闭
            toast.addEventListener('click', () => {
                toast.style.transform = 'translateX(100%)';
                setTimeout(() => {
                    if (toast.parentNode) {
                        toast.remove();
                    }
                }, 300);
            });

            // 自动关闭
            setTimeout(() => {
                if (toast.parentNode) {
                    toast.style.transform = 'translateX(100%)';
                    setTimeout(() => {
                        if (toast.parentNode) {
                            toast.remove();
                        }
                    }, 300);
                }
            }, 5000);
        }

        async function markAsRead(notificationId) {
            try {
                const userId = document.getElementById('userId').value;
                const response = await fetch(`api/simple_websocket_server.php?action=mark_read&user_id=${userId}&notification_id=${notificationId}`);
                const data = await response.json();

                if (data.success) {
                    log(`通知 ${notificationId} 已标记为已读`);
                } else {
                    log(`标记已读失败: ${data.error}`);
                }
            } catch (error) {
                log(`标记已读错误: ${error.message}`);
            }
        }

        // 页面加载完成
        document.addEventListener('DOMContentLoaded', function() {
            log('简化通知测试页面加载完成');
            log('请设置用户ID并点击"开始测试"按钮');
        });

        // 页面卸载时停止测试
        window.addEventListener('beforeunload', function() {
            stopTest();
        });
    </script>
</body>
</html>
