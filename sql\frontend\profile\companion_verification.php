<?php
session_start();

// 检查用户是否已登录
if (!isset($_SESSION['user_id'])) {
    header('Location: ../login/index.php');
    exit;
}

// 数据库连接配置 (确保这个路径正确，或者使用全局配置文件)
require_once __DIR__ . '/../../config.php'; // 假设您的配置文件在此

$user_id = $_SESSION['user_id'];
$current_application_status = 'not_applied'; // 默认状态
$application_message = '';
$applicant_details = []; // 用于预填表单（如果适用，例如被拒绝后修改）

try {
    $dsn = "mysql:host={$db_config['host']};dbname={$db_config['dbname']};charset={$db_config['charset']};port={$db_config['port']}";
    $pdo = new PDO($dsn, $db_config['username'], $db_config['password']);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

    // 1. 检查 companion_verification 表中的状态
    $stmt_cv = $pdo->prepare("SELECT verification_status FROM companion_verification WHERE user_id = :user_id");
    $stmt_cv->execute(['user_id' => $user_id]);
    $cv_result = $stmt_cv->fetch(PDO::FETCH_ASSOC);

    if ($cv_result) {
        $current_application_status = $cv_result['verification_status'];
    } else {
        // 如果 companion_verification 中没有记录，视为 'not_applied'
        // 也可以在这里为用户插入一条 'not_applied' 记录，如果业务逻辑需要
        // $insertStmt = $pdo->prepare("INSERT INTO companion_verification (user_id, verification_status) VALUES (:user_id, 'not_applied') ON DUPLICATE KEY UPDATE user_id=user_id");
        // $insertStmt->execute(['user_id' => $user_id]);
        $current_application_status = 'not_applied';
    }

    // 2. 如果状态是 pending, approved, rejected，尝试获取最新的申请详情
    if (in_array($current_application_status, ['pending', 'approved', 'rejected'])) {
        $stmt_app = $pdo->prepare("SELECT * FROM companion_applications WHERE user_id = :user_id ORDER BY application_date DESC LIMIT 1");
        $stmt_app->execute(['user_id' => $user_id]);
        $app_details = $stmt_app->fetch(PDO::FETCH_ASSOC);
        if($app_details) {
            $applicant_details = $app_details;
        }
    }

    // 设置提示信息
    switch ($current_application_status) {
        case 'pending':
            $application_message = '您的陪玩申请正在审核中，请耐心等待。';
            break;
        case 'approved':
            $application_message = '恭喜您！您的陪玩申请已通过。您现在是认证陪玩了！';
            break;
        case 'rejected':
            $application_message = '很遗憾，您的陪玩申请未通过。';
            if (!empty($applicant_details['admin_notes'])) {
                $application_message .= ' 拒绝原因：' . htmlspecialchars($applicant_details['admin_notes']);
            }
            $application_message .= ' 您可以修改信息后重新提交。';
            break;
        case 'not_applied':
            $application_message = '您尚未申请成为陪玩，请填写下面的表单进行申请。';
            break;
        default:
            $application_message = '欢迎申请成为陪玩！';
            break;
    }

} catch (PDOException $e) {
    error_log("陪玩认证页面数据库错误 (companion_verification.php): " . $e->getMessage());
    $current_application_status = 'error_loading';
    $application_message = '加载申请状态失败，请稍后再试或联系客服。';
}

$page_title = "申请成为陪玩";
if ($current_application_status === 'pending') $page_title = "陪玩申请审核中";
if ($current_application_status === 'approved') $page_title = "陪玩认证已通过";
if ($current_application_status === 'rejected') $page_title = "陪玩申请未通过";

?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <meta name="theme-color" content="#6F7BF5"> <!-- 主题色 -->
    <title><?php echo htmlspecialchars($page_title); ?> - 趣玩星球</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="css/companion_verification.css">
    <style>
        body {
            background-color: #f4f6f8;
            color: #333;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
            margin: 0;
            padding-top: 56px; /* 为固定头部预留空间 */
        }
        .header-bar {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 56px;
            background-color: #6F7BF5; /* 主题色 */
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            color: white;
        }
        .header-bar .back-button {
            font-size: 20px;
            color: white;
            text-decoration: none;
            margin-right: 16px;
        }
        .header-bar .title {
            font-size: 18px;
            font-weight: 600;
            margin: 0 auto;
            transform: translateX(-10%); /* 尝试使标题居中，可能需要根据返回按钮宽度微调 */
        }
        .page-container {
            padding: 20px;
        }
        /* 更多样式将在 companion_verification.css 中定义 */
    </style>
</head>
<body>

<div class="header-bar">
    <a href="../profile/index.php" class="back-button"><i class="fas fa-arrow-left"></i></a>
    <span class="title"><?php echo htmlspecialchars($page_title); ?></span>
    <!-- 为了让标题在视觉上更居中，可以放一个和返回按钮等宽的透明元素在右边，或者用flex布局调整 -->
</div>

<div class="page-container" id="app">
    <!-- Vue.js 或其他前端框架的挂载点，或者直接用原生JS操作 -->
    <div id="application-area">
        <!-- 状态显示区域 -->
        <div class="status-message" id="status-display" style="display: none;">
            <h3>申请状态</h3>
            <p id="current-status-message">您的申请正在处理中...</p>
        </div>

        <!-- 申请表单 -->
        <form id="companion-application-form" class="application-form" style="display: none;" enctype="multipart/form-data">
            <h2><i class="fas fa-user-check form-title-icon"></i> 成为认证陪玩</h2>
            
            <div class="form-section">
                <h3 class="section-title">基本信息</h3>
                <div class="form-group">
                    <label for="real_name"><i class="fas fa-user"></i> 真实姓名</label>
                    <input type="text" id="real_name" name="real_name" placeholder="请输入您的真实姓名" required>
                </div>
                <div class="form-group">
                    <label for="id_number"><i class="fas fa-id-card"></i> 身份证号</label>
                    <input type="text" id="id_number" name="id_number" placeholder="请输入您的18位身份证号" pattern="^\d{17}(\d|X|x)$" required>
                </div>
                <div class="form-group">
                    <label for="contact_info"><i class="fas fa-comments"></i> 联系方式</label>
                    <input type="text" id="contact_info" name="contact_info" placeholder="QQ / 微信等，方便联系您">
                </div>
            </div>

            <div class="form-section">
                <h3 class="section-title">陪玩资料</h3>
                <div class="form-group">
                    <label><i class="fas fa-user-tag"></i> 申请类型</label>
                    <div class="radio-group">
                        <label for="type_game"><input type="radio" id="type_game" name="application_type" value="game" checked> 游戏陪玩</label>
                        <label for="type_city"><input type="radio" id="type_city" name="application_type" value="city"> 城市陪玩</label>
                    </div>
                </div>
                <div class="form-group">
                    <label for="game_interests"><i class="fas fa-gamepad"></i> 擅长游戏/领域</label>
                    <input type="text" id="game_interests" name="game_interests" placeholder="如：英雄联盟、王者荣耀、聊天谈心">
                    <small class="form-text">多个请用逗号或空格隔开。</small>
                </div>

                <div id="city-specific-fields" style="display: none;">
                    <div class="form-group">
                        <label for="service_city"><i class="fas fa-city"></i> 服务城市</label>
                        <input type="text" id="service_city" name="service_city" placeholder="如：上海、北京">
                    </div>
                    <div class="form-group">
                        <label for="service_items"><i class="fas fa-concierge-bell"></i> 服务项目</label>
                        <textarea id="service_items" name="service_items" rows="3" placeholder="如：美食探店、景点打卡、休闲娱乐、商务伴游等，多个请用逗号隔开。"></textarea>
                    </div>
                </div>
                <div class="form-group">
                    <label for="self_description"><i class="fas fa-microphone-alt"></i> 自我介绍/陪玩宣言</label>
                    <textarea id="self_description" name="self_description" rows="4" placeholder="展现您的魅力和特色，吸引更多人！" required></textarea>
                </div>
                <div class="form-group file-upload-group">
                    <label for="voice_sample_url"><i class="fas fa-volume-up"></i> 语音介绍 (选填, MP3/WAV, <2MB)</label>
                    <div class="file-input-wrapper">
                        <input type="file" id="voice_sample_url" name="voice_sample_url" accept=".mp3,.wav">
                        <span class="file-input-label">选择文件</span>
                    </div>
                    <div class="file-preview" id="voice-preview"></div>
                </div>
            </div>

            <div class="form-section">
                <h3 class="section-title">身份验证材料</h3>
                <p class="section-description">请上传清晰、未经过修改的身份证照片，仅用于身份验证，我们会严格保密您的信息。</p>
                <div class="form-group file-upload-group">
                    <label for="id_card_front_url"><i class="fas fa-address-card"></i> 身份证正面照片 (JPG/PNG, <2MB)</label>
                    <div class="file-input-wrapper">
                        <input type="file" id="id_card_front_url" name="id_card_front_url" accept=".jpg,.jpeg,.png" required>
                        <span class="file-input-label">选择文件</span>
                    </div>
                    <div class="file-preview" id="id-card-front-preview"></div>
                </div>
                <div class="form-group file-upload-group">
                    <label for="id_card_back_url"><i class="fas fa-id-card-alt"></i> 身份证背面照片 (JPG/PNG, <2MB)</label>
                    <div class="file-input-wrapper">
                        <input type="file" id="id_card_back_url" name="id_card_back_url" accept=".jpg,.jpeg,.png" required>
                        <span class="file-input-label">选择文件</span>
                    </div>
                    <div class="file-preview" id="id-card-back-preview"></div>
                </div>
            </div>
            
            <div class="form-group terms-group">
                <input type="checkbox" id="agree_terms" name="agree_terms" required>
                <label for="agree_terms">我已阅读并同意 <a href="/terms/companion_agreement.php" target="_blank">《趣玩星球陪玩服务协议》</a></label>
            </div>

            <button type="submit" class="submit-btn"><i class="fas fa-paper-plane"></i> 提交申请</button>
        </form>
    </div>

</div>

<script>
const currentApplicationStatus = "<?php echo $current_application_status; ?>";
const applicationMessage = "<?php echo addslashes($application_message); ?>";
const applicantDetails = <?php echo json_encode($applicant_details ?: new stdClass()); ?>;

document.addEventListener('DOMContentLoaded', function() {
    const applicationForm = document.getElementById('companion-application-form');
    const statusDisplay = document.getElementById('status-display');
    const statusMessageEl = document.getElementById('current-status-message');

    const applicationTypeRadios = document.querySelectorAll('input[name="application_type"]');
    const gameInterestsFormGroup = document.getElementById('game_interests')?.closest('.form-group');
    const citySpecificFieldsDiv = document.getElementById('city-specific-fields');
    const serviceCityInput = document.getElementById('service_city');
    const serviceItemsInput = document.getElementById('service_items');
    const gameInterestsInput = document.getElementById('game_interests');

    function toggleCompanionFields(type) {
        if (type === 'game') {
            if(gameInterestsFormGroup) gameInterestsFormGroup.style.display = '';
            if(citySpecificFieldsDiv) citySpecificFieldsDiv.style.display = 'none';
            if(gameInterestsInput) gameInterestsInput.required = true;
            if(serviceCityInput) serviceCityInput.required = false;
            if(serviceItemsInput) serviceItemsInput.required = false;
            // Clear city fields if they were filled for a different type previously
            if(serviceCityInput) serviceCityInput.value = '';
            if(serviceItemsInput) serviceItemsInput.value = '';
        } else if (type === 'city') {
            if(gameInterestsFormGroup) gameInterestsFormGroup.style.display = 'none';
            if(citySpecificFieldsDiv) citySpecificFieldsDiv.style.display = '';
            if(gameInterestsInput) gameInterestsInput.required = false;
            if(serviceCityInput) serviceCityInput.required = true;
            if(serviceItemsInput) serviceItemsInput.required = true;
            // Clear game field if it was filled for a different type previously
            if(gameInterestsInput) gameInterestsInput.value = '';
        }
    }

    if (applicationTypeRadios.length > 0) {
        applicationTypeRadios.forEach(radio => {
            radio.addEventListener('change', function() {
                toggleCompanionFields(this.value);
            });
        });

        let initialType = 'game'; // Default
        if (applicantDetails && applicantDetails.application_type) {
            initialType = applicantDetails.application_type;
            const radioToSelect = document.querySelector('input[name="application_type"][value="' + initialType + '"]');
            if (radioToSelect) radioToSelect.checked = true;
        }
        toggleCompanionFields(initialType); // Set initial state
    }

    // Initialize page based on status & pre-fill form
    if (currentApplicationStatus === 'not_applied' || currentApplicationStatus === 'rejected') {
        statusDisplay.style.display = 'none';
        if (applicationForm) applicationForm.style.display = 'block';

        if (applicantDetails) {
            if (document.getElementById('real_name')) document.getElementById('real_name').value = applicantDetails.real_name || '';
            if (document.getElementById('id_number')) document.getElementById('id_number').value = applicantDetails.id_number || '';
            if (document.getElementById('contact_info')) document.getElementById('contact_info').value = applicantDetails.contact_info || '';
            if (gameInterestsInput) gameInterestsInput.value = applicantDetails.game_interests || '';
            if (document.getElementById('self_description')) document.getElementById('self_description').value = applicantDetails.self_description || '';

            // Pre-fill type-specific fields
            if (applicantDetails.application_type === 'city' && applicantDetails.type_specific_details) {
                try {
                    const details = JSON.parse(applicantDetails.type_specific_details);
                    if (serviceCityInput) serviceCityInput.value = details.service_city || '';
                    if (serviceItemsInput) serviceItemsInput.value = details.service_items || '';
                } catch (e) {
                    console.warn('Could not parse type_specific_details as JSON:', applicantDetails.type_specific_details);
                    // Fallback if not JSON (e.g. older data or simple string)
                    if (serviceItemsInput && typeof applicantDetails.type_specific_details === 'string') {
                         // Assuming for older data, it might have just been one field, or we decide how to handle it.
                         // For now, let's try to put it into service_items if it's a string.
                         // serviceItemsInput.value = applicantDetails.type_specific_details;
                    }
                }
            }
        }

        if (currentApplicationStatus === 'rejected' && applicationMessage && applicationForm) {
            const rejectedNotice = document.createElement('p');
            rejectedNotice.className = 'status-message rejected';
            rejectedNotice.innerHTML = `<strong>申请未通过:</strong> ${applicationMessage.replace(' 您可以修改信息后重新提交。', '')}`;
            applicationForm.insertBefore(rejectedNotice, applicationForm.firstChild);
        }

    } else if (currentApplicationStatus === 'pending' || currentApplicationStatus === 'approved' || currentApplicationStatus === 'error_loading') {
        if (applicationForm) applicationForm.style.display = 'none';
        statusMessageEl.innerHTML = applicationMessage;
        statusDisplay.className = `status-message ${currentApplicationStatus}`;
        statusDisplay.style.display = 'block';
    } else {
        if (applicationForm) applicationForm.style.display = 'block';
        statusDisplay.style.display = 'none';
    }

    // File Preview Logic
    function setupFilePreview(fileInputId, previewElementId, isImage = true) {
        const fileInput = document.getElementById(fileInputId);
        const previewElement = document.getElementById(previewElementId);
        if (!fileInput || !previewElement) return;

        fileInput.addEventListener('change', function(event) {
            const file = event.target.files[0];
            previewElement.innerHTML = ''; // Clear previous preview
            if (file) {
                const fileNameSpan = document.createElement('span');
                fileNameSpan.textContent = `已选择: ${file.name} (${(file.size / 1024 / 1024).toFixed(2)}MB)`;
                previewElement.appendChild(fileNameSpan);

                if (isImage && file.type.startsWith('image/')) {
                    const reader = new FileReader();
                    reader.onload = function(e) {
                        const img = document.createElement('img');
                        img.src = e.target.result;
                        previewElement.appendChild(img);
                    }
                    reader.readAsDataURL(file);
                } else if (!isImage && file.type.startsWith('audio/')) {
                    const reader = new FileReader();
                    reader.onload = function(e) {
                        const audio = document.createElement('audio');
                        audio.controls = true;
                        audio.src = e.target.result;
                        previewElement.appendChild(audio);
                    }
                    reader.readAsDataURL(file);
                }
            }
        });
    }

    if (document.getElementById('voice_sample_url')) setupFilePreview('voice_sample_url', 'voice-preview', false);
    if (document.getElementById('id_card_front_url')) setupFilePreview('id_card_front_url', 'id-card-front-preview', true);
    if (document.getElementById('id_card_back_url')) setupFilePreview('id_card_back_url', 'id-card-back-preview', true);

    // Form Submission Logic
    if (applicationForm) {
        applicationForm.addEventListener('submit', function(event) {
            event.preventDefault();
            const submitButton = applicationForm.querySelector('button[type="submit"]');
            const originalButtonText = submitButton.innerHTML;
            submitButton.disabled = true;
            submitButton.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 正在提交...';

            // Clear previous error messages
            const existingError = applicationForm.querySelector('.status-message.error');
            if (existingError) existingError.remove();

            const formData = new FormData(applicationForm);

            fetch('api/submit_companion_application.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    applicationForm.style.display = 'none';
                    statusMessageEl.innerHTML = data.message || '申请提交成功！我们会在1-3个工作日内完成审核。';
                    statusDisplay.className = 'status-message success';
                    statusDisplay.style.display = 'block';
                    document.querySelector('.header-bar .title').textContent = '陪玩申请审核中';
                } else {
                    let errorMsg = data.message || '提交失败，请检查您的输入并重试。';
                    if (data.errors) {
                        errorMsg += '<ul>';
                        for (const key in data.errors) {
                            errorMsg += `<li>${data.errors[key]}</li>`;
                        }
                        errorMsg += '</ul>';
                    }
                    const errorDiv = document.createElement('div');
                    errorDiv.className = 'status-message error';
                    errorDiv.innerHTML = `<h3>提交失败</h3><p>${errorMsg}</p>`;
                    applicationForm.insertBefore(errorDiv, submitButton);
                    submitButton.disabled = false;
                    submitButton.innerHTML = originalButtonText;
                }
            })
            .catch(error => {
                console.error('Error submitting application:', error);
                const errorDiv = document.createElement('div');
                errorDiv.className = 'status-message error';
                errorDiv.innerHTML = '<h3>提交出错</h3><p>网络错误或服务器无法响应，请稍后再试。</p>';
                applicationForm.insertBefore(errorDiv, submitButton);
                submitButton.disabled = false;
                submitButton.innerHTML = originalButtonText;
            });
        });
    }
});

</script>

</body>
</html>
