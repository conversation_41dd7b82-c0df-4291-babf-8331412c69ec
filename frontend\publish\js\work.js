// 发布作品页面JavaScript

let selectedImages = [];
let selectedTags = [];
let currentLocation = null;

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    initializeEventListeners();
    updateCharCounts();
});

// 初始化事件监听器
function initializeEventListeners() {
    // 图片选择
    document.getElementById('imageInput').addEventListener('change', handleImageSelect);
    
    // 字符计数
    document.getElementById('title').addEventListener('input', updateCharCounts);
    document.getElementById('description').addEventListener('input', updateCharCounts);
    
    // 标签输入
    document.getElementById('tagsInput').addEventListener('keypress', handleTagInput);
}

// 选择图片
function selectImages() {
    if (selectedImages.length >= 9) {
        showToast('最多只能上传9张图片');
        return;
    }
    document.getElementById('imageInput').click();
}

// 处理图片选择
function handleImageSelect(event) {
    const files = Array.from(event.target.files);
    
    if (selectedImages.length + files.length > 9) {
        showToast('最多只能上传9张图片');
        return;
    }
    
    files.forEach(file => {
        if (file.type.startsWith('image/')) {
            if (file.size > 10 * 1024 * 1024) { // 10MB限制
                showToast('图片大小不能超过10MB');
                return;
            }
            
            const reader = new FileReader();
            reader.onload = function(e) {
                selectedImages.push({
                    file: file,
                    url: e.target.result
                });
                updateImageGrid();
            };
            reader.readAsDataURL(file);
        }
    });
    
    // 清空input
    event.target.value = '';
}

// 更新图片网格
function updateImageGrid() {
    const grid = document.getElementById('uploadGrid');
    grid.innerHTML = '';
    
    // 添加已选择的图片
    selectedImages.forEach((image, index) => {
        const item = document.createElement('div');
        item.className = 'upload-item image-preview';
        item.style.backgroundImage = `url(${image.url})`;
        
        const removeBtn = document.createElement('button');
        removeBtn.className = 'remove-btn';
        removeBtn.innerHTML = '<i class="fas fa-times"></i>';
        removeBtn.onclick = () => removeImage(index);
        
        item.appendChild(removeBtn);
        grid.appendChild(item);
    });
    
    // 添加上传按钮（如果还没达到上限）
    if (selectedImages.length < 9) {
        const addItem = document.createElement('div');
        addItem.className = 'upload-item add-photo';
        addItem.onclick = selectImages;
        addItem.innerHTML = `
            <i class="fas fa-plus"></i>
            <span>添加图片</span>
            <small>${selectedImages.length}/9</small>
        `;
        grid.appendChild(addItem);
    }
}

// 移除图片
function removeImage(index) {
    selectedImages.splice(index, 1);
    updateImageGrid();
}

// 更新字符计数
function updateCharCounts() {
    const title = document.getElementById('title');
    const description = document.getElementById('description');
    
    if (title) {
        const titleCount = title.parentElement.querySelector('.char-count');
        if (titleCount) {
            titleCount.textContent = `${title.value.length}/100`;
        }
    }
    
    if (description) {
        const descCount = description.parentElement.querySelector('.char-count');
        if (descCount) {
            descCount.textContent = `${description.value.length}/500`;
        }
    }
}

// 处理标签输入
function handleTagInput(event) {
    if (event.key === 'Enter') {
        event.preventDefault();
        addTag();
    }
}

// 添加标签
function addTag() {
    const input = document.getElementById('tagsInput');
    const tag = input.value.trim();
    
    if (!tag) return;
    
    if (selectedTags.length >= 5) {
        showToast('最多只能添加5个标签');
        return;
    }
    
    if (selectedTags.includes(tag)) {
        showToast('标签已存在');
        return;
    }
    
    if (tag.length > 20) {
        showToast('标签长度不能超过20个字符');
        return;
    }
    
    selectedTags.push(tag);
    input.value = '';
    updateTagsDisplay();
}

// 更新标签显示
function updateTagsDisplay() {
    const display = document.getElementById('tagsDisplay');
    display.innerHTML = '';
    
    selectedTags.forEach((tag, index) => {
        const tagItem = document.createElement('div');
        tagItem.className = 'tag-item';
        tagItem.innerHTML = `
            <span>${tag}</span>
            <button class="tag-remove" onclick="removeTag(${index})">
                <i class="fas fa-times"></i>
            </button>
        `;
        display.appendChild(tagItem);
    });
}

// 移除标签
function removeTag(index) {
    selectedTags.splice(index, 1);
    updateTagsDisplay();
}

// 获取位置
function getLocation() {
    if (!navigator.geolocation) {
        showToast('浏览器不支持定位功能');
        return;
    }
    
    showToast('正在获取位置...');
    
    navigator.geolocation.getCurrentPosition(
        function(position) {
            const lat = position.coords.latitude;
            const lng = position.coords.longitude;
            
            // 这里应该调用地图API获取地址信息
            // 暂时使用模拟数据
            currentLocation = {
                latitude: lat,
                longitude: lng,
                address: `位置 ${lat.toFixed(4)}, ${lng.toFixed(4)}`
            };
            
            document.getElementById('location').value = currentLocation.address;
            showToast('位置获取成功');
        },
        function(error) {
            showToast('位置获取失败，请手动输入');
            document.getElementById('location').removeAttribute('readonly');
        }
    );
}

// 发布作品
async function publishWork() {
    const form = document.getElementById('workForm');
    const formData = new FormData();
    
    // 验证必填字段
    const title = document.getElementById('title').value.trim();
    const category = document.getElementById('category').value;
    
    if (!title) {
        showToast('请输入作品标题');
        return;
    }
    
    if (!category) {
        showToast('请选择作品分类');
        return;
    }
    
    if (selectedImages.length === 0) {
        showToast('请至少上传一张图片');
        return;
    }
    
    // 显示加载状态
    showLoading(true);
    
    try {
        // 添加表单数据
        formData.append('title', title);
        formData.append('description', document.getElementById('description').value.trim());
        formData.append('category', category);
        formData.append('tags', JSON.stringify(selectedTags));
        formData.append('privacy', document.querySelector('input[name="privacy"]:checked').value);
        
        if (currentLocation) {
            formData.append('location', currentLocation.address);
            formData.append('latitude', currentLocation.latitude);
            formData.append('longitude', currentLocation.longitude);
        }
        
        // 添加图片文件
        selectedImages.forEach((image, index) => {
            formData.append(`images[]`, image.file);
        });
        
        // 发送请求
        const response = await fetch('../api/publish_work.php', {
            method: 'POST',
            body: formData
        });
        
        const result = await response.json();
        
        if (result.success) {
            showToast('作品发布成功！');
            setTimeout(() => {
                window.location.href = '../home/<USER>';
            }, 1500);
        } else {
            showToast(result.message || '发布失败，请重试');
        }
        
    } catch (error) {
        console.error('发布错误:', error);
        showToast('网络错误，请检查网络连接');
    } finally {
        showLoading(false);
    }
}

// 显示/隐藏加载状态
function showLoading(show) {
    const overlay = document.getElementById('loadingOverlay');
    const publishBtn = document.querySelector('.publish-button');
    
    if (show) {
        overlay.style.display = 'flex';
        publishBtn.disabled = true;
    } else {
        overlay.style.display = 'none';
        publishBtn.disabled = false;
    }
}

// 显示Toast提示
function showToast(message) {
    const toast = document.getElementById('toast');
    toast.textContent = message;
    toast.style.display = 'block';
    
    setTimeout(() => {
        toast.style.display = 'none';
    }, 3000);
}
