<?php
/**
 * 搜索功能API
 * 处理搜索历史、热门搜索、热门榜单等功能
 */

// 启动session
session_start();

// 设置响应头
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// 处理OPTIONS请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

// 数据库配置
$db_config = [
    'host' => 'localhost',
    'username' => 'quwanplanet',
    'password' => 'nJmJm23FB4Xn6Fc3',
    'dbname' => 'quwanplanet',
    'port' => 3306,
    'charset' => 'utf8mb4'
];

$dsn = "mysql:host={$db_config['host']};dbname={$db_config['dbname']};charset={$db_config['charset']};port={$db_config['port']}";
$username = $db_config['username'];
$password = $db_config['password'];
$options = [
    PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
    PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
    PDO::ATTR_EMULATE_PREPARES => false,
];

// 获取请求参数
$action = $_GET['action'] ?? $_POST['action'] ?? '';
$user_id = $_SESSION['user_id'] ?? null;

// 响应函数
function sendResponse($success, $data = null, $message = '') {
    echo json_encode([
        'success' => $success,
        'data' => $data,
        'message' => $message
    ], JSON_UNESCAPED_UNICODE);
    exit;
}

// 数据库连接
try {
    $pdo = new PDO($dsn, $username, $password, $options);
} catch (PDOException $e) {
    sendResponse(false, null, '数据库连接失败');
}

switch ($action) {
    case 'get_hot_keywords':
        getHotKeywords($pdo);
        break;

    case 'get_trending_list':
        getTrendingList($pdo);
        break;

    case 'get_search_history':
        getSearchHistory($pdo, $user_id);
        break;

    case 'add_search_history':
        addSearchHistory($pdo, $user_id);
        break;

    case 'delete_search_history':
        deleteSearchHistory($pdo, $user_id);
        break;

    case 'clear_search_history':
        clearSearchHistory($pdo, $user_id);
        break;

    case 'perform_search':
        performSearch($pdo, $user_id);
        break;

    default:
        sendResponse(false, null, '无效的操作');
}

/**
 * 获取热门搜索关键词
 */
function getHotKeywords($pdo) {
    try {
        $stmt = $pdo->prepare("
            SELECT keyword
            FROM hot_search_keywords
            WHERE is_active = 1
            ORDER BY display_order ASC, search_count DESC
            LIMIT 8
        ");
        $stmt->execute();
        $keywords = $stmt->fetchAll(PDO::FETCH_COLUMN);

        sendResponse(true, $keywords);
    } catch (PDOException $e) {
        sendResponse(false, null, '获取热门搜索失败');
    }
}

/**
 * 获取热门榜单
 */
function getTrendingList($pdo) {
    try {
        $stmt = $pdo->prepare("
            SELECT title, search_keyword, rank_position, search_count, trend_type
            FROM trending_searches
            WHERE is_active = 1
            ORDER BY rank_position ASC
            LIMIT 10
        ");
        $stmt->execute();
        $trending = $stmt->fetchAll(PDO::FETCH_ASSOC);

        // 格式化搜索次数显示
        foreach ($trending as &$item) {
            if ($item['search_count'] >= 10000) {
                $item['search_count_display'] = round($item['search_count'] / 10000, 1) . '万 搜';
            } else {
                $item['search_count_display'] = $item['search_count'] . ' 搜';
            }
        }

        sendResponse(true, $trending);
    } catch (PDOException $e) {
        sendResponse(false, null, '获取热门榜单失败');
    }
}

/**
 * 获取用户搜索历史
 */
function getSearchHistory($pdo, $user_id) {
    if (!$user_id) {
        sendResponse(true, []); // 未登录用户返回空数组
    }

    try {
        $stmt = $pdo->prepare("
            SELECT DISTINCT search_keyword, search_type, MAX(search_time) as last_search_time
            FROM user_search_history
            WHERE user_id = ?
            GROUP BY search_keyword, search_type
            ORDER BY last_search_time DESC
            LIMIT 10
        ");
        $stmt->execute([$user_id]);
        $history = $stmt->fetchAll(PDO::FETCH_ASSOC);

        sendResponse(true, $history);
    } catch (PDOException $e) {
        sendResponse(false, null, '获取搜索历史失败');
    }
}

/**
 * 添加搜索历史
 */
function addSearchHistory($pdo, $user_id) {
    $keyword = trim($_POST['keyword'] ?? '');
    $search_type = $_POST['search_type'] ?? 'all';
    $ip_address = $_SERVER['REMOTE_ADDR'] ?? '';
    $user_agent = $_SERVER['HTTP_USER_AGENT'] ?? '';

    if (empty($keyword)) {
        sendResponse(false, null, '搜索关键词不能为空');
    }

    if (!$user_id) {
        sendResponse(true, null, '未登录用户不记录搜索历史');
    }

    try {
        // 添加搜索历史
        $stmt = $pdo->prepare("
            INSERT INTO user_search_history (user_id, search_keyword, search_type, ip_address, user_agent)
            VALUES (?, ?, ?, ?, ?)
        ");
        $stmt->execute([$user_id, $keyword, $search_type, $ip_address, $user_agent]);

        // 更新热门搜索关键词统计
        $stmt = $pdo->prepare("
            INSERT INTO hot_search_keywords (keyword, search_count)
            VALUES (?, 1)
            ON DUPLICATE KEY UPDATE search_count = search_count + 1
        ");
        $stmt->execute([$keyword]);

        sendResponse(true, null, '搜索历史已保存');
    } catch (PDOException $e) {
        sendResponse(false, null, '保存搜索历史失败');
    }
}

/**
 * 删除单条搜索历史
 */
function deleteSearchHistory($pdo, $user_id) {
    $keyword = trim($_POST['keyword'] ?? '');
    $search_type = $_POST['search_type'] ?? 'all';

    if (!$user_id) {
        sendResponse(false, null, '请先登录');
    }

    if (empty($keyword)) {
        sendResponse(false, null, '搜索关键词不能为空');
    }

    try {
        $stmt = $pdo->prepare("
            DELETE FROM user_search_history
            WHERE user_id = ? AND search_keyword = ? AND search_type = ?
        ");
        $stmt->execute([$user_id, $keyword, $search_type]);

        sendResponse(true, null, '搜索历史已删除');
    } catch (PDOException $e) {
        sendResponse(false, null, '删除搜索历史失败');
    }
}

/**
 * 清空用户搜索历史
 */
function clearSearchHistory($pdo, $user_id) {
    if (!$user_id) {
        sendResponse(false, null, '请先登录');
    }

    try {
        $stmt = $pdo->prepare("DELETE FROM user_search_history WHERE user_id = ?");
        $stmt->execute([$user_id]);

        sendResponse(true, null, '搜索历史已清空');
    } catch (PDOException $e) {
        sendResponse(false, null, '清空搜索历史失败');
    }
}

/**
 * 执行搜索
 */
function performSearch($pdo, $user_id) {
    $keyword = trim($_POST['keyword'] ?? '');
    $search_type = $_POST['search_type'] ?? 'all';

    if (empty($keyword)) {
        sendResponse(false, null, '搜索关键词不能为空');
    }

    try {
        // 这里可以根据实际需求实现搜索逻辑
        // 目前返回模拟的搜索结果

        // 模拟搜索延迟
        usleep(800000); // 0.8秒延迟

        // 模拟搜索结果（实际项目中这里应该是真实的搜索逻辑）
        $searchResults = [];

        // 根据关键词模拟一些结果
        $mockResults = [
            '游戏' => [
                ['title' => '王者荣耀陪玩', 'type' => 'game', 'description' => '专业陪玩，技术过硬'],
                ['title' => '英雄联盟组队', 'type' => 'game', 'description' => '寻找队友一起上分'],
            ],
            '美食' => [
                ['title' => '附近火锅店', 'type' => 'food', 'description' => '评分4.8分的网红火锅'],
                ['title' => '日料推荐', 'type' => 'food', 'description' => '新鲜刺身，环境优雅'],
            ],
            '电影' => [
                ['title' => '最新电影票', 'type' => 'entertainment', 'description' => '热映大片，优惠购票'],
                ['title' => 'IMAX影院', 'type' => 'entertainment', 'description' => '震撼视听体验'],
            ],
            'KTV' => [
                ['title' => 'KTV包厢预订', 'type' => 'entertainment', 'description' => '豪华包厢，设备齐全'],
                ['title' => '量贩式KTV', 'type' => 'entertainment', 'description' => '价格实惠，歌曲丰富'],
            ]
        ];

        // 简单的关键词匹配
        foreach ($mockResults as $key => $results) {
            if (strpos($keyword, $key) !== false || strpos($key, $keyword) !== false) {
                $searchResults = array_merge($searchResults, $results);
            }
        }

        // 如果没有匹配的结果，返回空数组
        if (empty($searchResults)) {
            // 检查是否是常见搜索词
            $commonKeywords = ['周末', '聚会', '户外', '运动', '密室', '剧本杀'];
            foreach ($commonKeywords as $common) {
                if (strpos($keyword, $common) !== false) {
                    $searchResults = [
                        ['title' => '相关活动推荐', 'type' => 'activity', 'description' => '为您推荐相关的精彩活动']
                    ];
                    break;
                }
            }
        }

        $response = [
            'keyword' => $keyword,
            'results' => $searchResults,
            'total' => count($searchResults),
            'has_results' => !empty($searchResults)
        ];

        sendResponse(true, $response);

    } catch (PDOException $e) {
        sendResponse(false, null, '搜索失败，请稍后重试');
    }
}
?>
