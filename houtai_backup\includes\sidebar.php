<?php
/**
 * 左侧菜单组件
 * 趣玩星球管理后台
 */

// 获取当前页面文件名
$current_page = basename($_SERVER['PHP_SELF']);
$current_dir = basename(dirname($_SERVER['PHP_SELF']));

// 获取待审核数量
$pending_count = 0; // Initialize for real name verification
$appeal_pending_count = 0; // Initialize for appeals
$companion_pending_count = 0; // Initialize for companion applications

try {
    $pdo_for_sidebar = null;
    // Check if $pdo is already set and is a valid PDO object (e.g., by admin_layout.php)
    if (isset($pdo) && $pdo instanceof PDO) {
        $pdo_for_sidebar = $pdo;
    } else {
        // If $pdo is not set or invalid, try to create a new one
        // Ensure $db_config is available from db_config.php
        if (!isset($db_config)) { 
            require_once __DIR__ . '/../db_config.php';
        }
        // Check if $db_config was loaded successfully
        if (isset($db_config) && is_array($db_config)) {
            $dsn_sidebar = "mysql:host={$db_config['host']};dbname={$db_config['dbname']};charset={$db_config['charset']};port={$db_config['port']}";
            $options_sidebar = [
                PDO::ATTR_ERRMODE            => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                PDO::ATTR_EMULATE_PREPARES   => false,
            ];
            $pdo_for_sidebar = new PDO($dsn_sidebar, $db_config['username'], $db_config['password'], $options_sidebar);
        } else {
            error_log("sidebar.php: db_config is not available for PDO instantiation for counts.");
        }
    }

    if ($pdo_for_sidebar) {
        // 实名审核待审核数量
        $stmt_realname = $pdo_for_sidebar->query("SELECT COUNT(*) as count FROM real_name_verification WHERE status = 'pending'");
        $pending_count = $stmt_realname->fetchColumn() ?? 0;

        // 申诉待审核数量
        try {
            $stmt_appeal = $pdo_for_sidebar->query("SELECT COUNT(*) as count FROM user_appeals WHERE status = 'pending'");
            $appeal_pending_count = $stmt_appeal->fetchColumn() ?? 0;
        } catch (Exception $e_appeal) {
            // $appeal_pending_count remains 0 if query fails
            error_log("sidebar.php: Error fetching appeal_pending_count: " . $e_appeal->getMessage());
        }
        
        // 陪玩申请待审核数量
        try {
            $stmt_companion = $pdo_for_sidebar->query("SELECT COUNT(*) as count FROM companion_applications WHERE status = 'pending'");
            $companion_pending_count = $stmt_companion->fetchColumn() ?? 0;
        } catch (Exception $e_companion) {
            // $companion_pending_count remains 0 if query fails
            error_log("sidebar.php: Error fetching companion_pending_count: " . $e_companion->getMessage());
        }
    }
} catch (PDOException $e_pdo) {
    error_log("sidebar.php: Database connection or query error for counts: " . $e_pdo->getMessage());
    // All counts remain 0 or their last successfully fetched value if only one query failed
} catch (Exception $e_general) {
    error_log("sidebar.php: General error fetching counts: " . $e_general->getMessage());
    // All counts remain 0 or their last successfully fetched value
}

// 获取管理员信息
$admin_name = $_SESSION['admin_name'] ?? '管理员';
$admin_role = $_SESSION['admin_role'] ?? '系统管理员';
?>

<aside class="sidebar">
    <div class="sidebar-header">
        <div class="logo">
            <div class="logo-icon">
                <img src="https://s1.imagehub.cc/images/2025/05/26/2cdd63b39f96072fc60cd733027d2a30.png" alt="趣玩星球" class="logo-image">
            </div>
            <div class="logo-text">
                <h2>趣玩星球</h2>
                <p>管理后台</p>
            </div>
        </div>
    </div>

    <nav class="sidebar-nav">
        <ul class="nav-list">
            <!-- 首页 -->
            <li class="nav-item <?php echo $current_page === 'home.php' ? 'active' : ''; ?>">
                <a href="/houtai_backup/home.php" class="nav-link">
                    <i class="fas fa-home"></i>
                    <span class="nav-link-text">首页</span>
                </a>
            </li>

            <!-- 用户管理 -->
            <li class="nav-item has-submenu <?php echo ($current_dir === 'user_management' || $current_dir === 'verification' || $current_dir === 'appeal') ? 'active open' : ''; ?>">
                <a href="#" class="nav-link" onclick="toggleSubmenu(this)">
                    <i class="fas fa-users"></i>
                    <span class="nav-link-text">用户管理</span>
                    <i class="fas fa-chevron-right submenu-arrow"></i>
                </a>
                <ul class="submenu <?php echo ($current_dir === 'user_management' || $current_dir === 'verification' || $current_dir === 'appeal') ? 'show' : ''; ?>">
                    <li class="submenu-item <?php echo $current_dir === 'user_management' ? 'active' : ''; ?>">
                        <a href="/houtai_backup/user_management/index.php" class="submenu-link">
                            <i class="fas fa-user-friends"></i>
                            <span class="submenu-link-text">用户管理</span>
                        </a>
                    </li>
                    <li class="submenu-item <?php echo $current_dir === 'verification' ? 'active' : ''; ?>">
                        <a href="/houtai_backup/verification/index.php" class="submenu-link">
                            <i class="fas fa-user-shield"></i>
                            <span class="submenu-link-text">实名审核</span>
                            <?php if ($pending_count > 0): ?>
                                <span class="nav-badge"><?php echo $pending_count; ?></span>
                            <?php endif; ?>
                        </a>
                    </li>
                    <li class="submenu-item <?php echo $current_dir === 'appeal' ? 'active' : ''; ?>">
                        <a href="/houtai_backup/appeal/index.php" class="submenu-link">
                            <i class="fas fa-file-alt"></i>
                            <span class="submenu-link-text">申诉审核</span>
                            <?php if ($appeal_pending_count > 0): ?>
                                <span class="nav-badge"><?php echo $appeal_pending_count; ?></span>
                            <?php endif; ?>
                        </a>
                    </li>
                    <li class="submenu-item <?php echo ($current_page === 'companion_application_review.php' || $current_page === 'companion_application_detail.php') ? 'active' : ''; ?>">
                        <a href="/houtai_backup/user_management/companion_application_review.php" class="submenu-link">
                            <i class="fas fa-user-check"></i>
                            <span class="submenu-link-text">陪玩审核</span>
                            <?php if ($companion_pending_count > 0): ?>
                                <span class="nav-badge"><?php echo $companion_pending_count; ?></span>
                            <?php endif; ?>
                        </a>
                    </li>
                    <li class="submenu-item <?php echo $current_page === 'user_profiles.php' ? 'active' : ''; ?>">
                        <a href="/houtai_backup/user_management/user_profiles.php" class="submenu-link">
                            <i class="fas fa-user-chart"></i>
                            <span class="submenu-link-text">用户画像</span>
                        </a>
                    </li>
                    <li class="submenu-item <?php echo $current_page === 'verification_code_logs.php' ? 'active' : ''; ?>">
                        <a href="/houtai_backup/user_management/verification_code_logs.php" class="submenu-link">
                            <i class="fas fa-key"></i>
                            <span class="submenu-link-text">验证码日志</span>
                        </a>
                    </li>
                </ul>
            </li>

            <!-- 数据统计 -->
            <li class="nav-item has-submenu <?php echo $current_page === 'dashboard.php' ? 'active open' : ''; ?>">
                <a href="#" class="nav-link" onclick="toggleSubmenu(this)">
                    <i class="fas fa-chart-line"></i>
                    <span class="nav-link-text">数据统计</span>
                    <i class="fas fa-chevron-right submenu-arrow"></i>
                </a>
                <ul class="submenu <?php echo $current_page === 'dashboard.php' ? 'show' : ''; ?>">
                    <li class="submenu-item <?php echo $current_page === 'dashboard.php' ? 'active' : ''; ?>">
                        <a href="/houtai_backup/dashboard.php" class="submenu-link">
                            <i class="fas fa-chart-pie"></i>
                            <span class="submenu-link-text">数据仪表盘</span>
                        </a>
                    </li>
                </ul>
            </li>



            <!-- 优惠券管理 -->
            <li class="nav-item has-submenu <?php echo $current_dir === 'coupon_management' ? 'active open' : ''; ?>">
                <a href="#" class="nav-link" onclick="toggleSubmenu(this)">
                    <i class="fas fa-ticket-alt"></i>
                    <span class="nav-link-text">优惠券管理</span>
                    <i class="fas fa-chevron-right submenu-arrow"></i>
                </a>
                <ul class="submenu <?php echo $current_dir === 'coupon_management' ? 'show' : ''; ?>">
                    <li class="submenu-item <?php echo $current_page === 'camping_coupons.php' ? 'active' : ''; ?>">
                        <a href="/houtai_backup/coupon_management/camping_coupons.php" class="submenu-link">
                            <i class="fas fa-campground"></i>
                            <span class="submenu-link-text">露营优惠券</span>
                        </a>
                    </li>
                    <li class="submenu-item <?php echo $current_page === 'statistics.php' ? 'active' : ''; ?>">
                        <a href="/houtai_backup/coupon_management/statistics.php" class="submenu-link">
                            <i class="fas fa-analytics"></i>
                            <span class="submenu-link-text">统计分析</span>
                        </a>
                    </li>
                </ul>
            </li>

            <!-- 权限管理 -->
            <li class="nav-item has-submenu">
                <a href="#" class="nav-link" onclick="toggleSubmenu(this)">
                    <i class="fas fa-shield-alt"></i>
                    <span class="nav-link-text">权限管理</span>
                    <i class="fas fa-chevron-right submenu-arrow"></i>
                </a>
                <ul class="submenu">
                    <li class="submenu-item">
                        <a href="/houtai_backup/permission/index.php" class="submenu-link">
                            <i class="fas fa-hand-paper"></i>
                            <span class="submenu-link-text">权限申请</span>
                        </a>
                    </li>
                    <li class="submenu-item">
                        <a href="/houtai_backup/permission/approval.php" class="submenu-link">
                            <i class="fas fa-check-double"></i>
                            <span class="submenu-link-text">权限审批</span>
                        </a>
                    </li>
                    <li class="submenu-item">
                        <a href="/houtai_backup/permission/roles.php" class="submenu-link">
                            <i class="fas fa-users-cog"></i>
                            <span class="submenu-link-text">角色管理</span>
                        </a>
                    </li>
                    <li class="submenu-item">
                        <a href="/houtai_backup/permission/config.php" class="submenu-link">
                            <i class="fas fa-key"></i>
                            <span class="submenu-link-text">权限配置</span>
                        </a>
                    </li>
                </ul>
            </li>

            <!-- 部门管理 -->
            <li class="nav-item has-submenu">
                <a href="#" class="nav-link" onclick="toggleSubmenu(this)">
                    <i class="fas fa-building"></i>
                    <span>部门管理</span>
                    <i class="fas fa-chevron-down submenu-arrow"></i>
                </a>
                <ul class="submenu">
                    <li class="submenu-item">
                        <a href="/houtai_backup/department/index.php" class="submenu-link">
                            <i class="fas fa-sitemap"></i>
                            <span>部门总览</span>
                        </a>
                    </li>
                    <li class="submenu-item">
                        <a href="/houtai_backup/department/my_department.php" class="submenu-link">
                            <i class="fas fa-users"></i>
                            <span>我的部门</span>
                        </a>
                    </li>
                    <li class="submenu-item">
                        <a href="#" class="submenu-link" onclick="showComingSoon('员工管理')">
                            <i class="fas fa-user-tie"></i>
                            <span>员工管理</span>
                        </a>
                    </li>
                    <li class="submenu-item">
                        <a href="#" class="submenu-link" onclick="showComingSoon('组织架构')">
                            <i class="fas fa-project-diagram"></i>
                            <span>组织架构</span>
                        </a>
                    </li>
                </ul>
            </li>



            <!-- 系统设置 -->
            <li class="nav-item has-submenu">
                <a href="#" class="nav-link" onclick="toggleSubmenu(this)">
                    <i class="fas fa-cog"></i>
                    <span>系统设置</span>
                    <i class="fas fa-chevron-down submenu-arrow"></i>
                </a>
                <ul class="submenu">
                    <li class="submenu-item">
                        <a href="#" class="submenu-link" onclick="showComingSoon('系统配置')">
                            <i class="fas fa-sliders-h"></i>
                            <span>系统配置</span>
                        </a>
                    </li>
                    <li class="submenu-item">
                        <a href="#" class="submenu-link" onclick="showComingSoon('日志管理')">
                            <i class="fas fa-file-alt"></i>
                            <span>日志管理</span>
                        </a>
                    </li>
                    <li class="submenu-item">
                        <a href="#" class="submenu-link" onclick="showComingSoon('系统监控')">
                            <i class="fas fa-chart-line"></i>
                            <span>系统监控</span>
                        </a>
                    </li>
                    <li class="submenu-item">
                        <a href="#" class="submenu-link" onclick="showComingSoon('备份恢复')">
                            <i class="fas fa-database"></i>
                            <span>备份恢复</span>
                        </a>
                    </li>
                </ul>
            </li>
        </ul>
    </nav>


</aside>

<script>
// 菜单交互脚本
function toggleSubmenu(element) {
    const submenu = element.nextElementSibling;
    const arrow = element.querySelector('.submenu-arrow');
    const navItem = element.parentElement;

    // 切换显示状态
    submenu.classList.toggle('show');
    navItem.classList.toggle('expanded');

    // 旋转箭头
    if (submenu.classList.contains('show')) {
        arrow.style.transform = 'rotate(180deg)';
    } else {
        arrow.style.transform = 'rotate(0deg)';
    }
}

function showComingSoon(feature) {
    alert(`${feature}功能正在开发中，敬请期待！`);
}

// 页面加载时展开当前活动的菜单
document.addEventListener('DOMContentLoaded', function() {
    const activeNavItem = document.querySelector('.nav-item.active.has-submenu');
    if (activeNavItem) {
        const submenu = activeNavItem.querySelector('.submenu');
        const arrow = activeNavItem.querySelector('.submenu-arrow');
        if (submenu && !submenu.classList.contains('show')) {
            submenu.classList.add('show');
            activeNavItem.classList.add('expanded');
            if (arrow) {
                arrow.style.transform = 'rotate(180deg)';
            }
        }
    }
});
</script>
