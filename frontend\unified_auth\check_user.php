<?php
/**
 * 检测用户是否存在API
 * 用于统一登录注册流程
 */

session_start();
header('Content-Type: application/json; charset=utf-8');

// 错误处理
error_reporting(0);
ini_set('display_errors', 0);

// 捕获所有错误并返回JSON
set_error_handler(function($severity, $message, $file, $line) {
    echo json_encode(['success' => false, 'message' => '系统错误，请稍后重试']);
    exit;
});

set_exception_handler(function($exception) {
    echo json_encode(['success' => false, 'message' => '系统错误，请稍后重试']);
    exit;
});

// 数据库连接配置
$db_config = [
    'host' => 'localhost',
    'username' => 'quwanplanet',
    'password' => 'nJmJm23FB4Xn6Fc3',
    'dbname' => 'quwanplanet',
    'port' => 3306,
    'charset' => 'utf8mb4'
];

try {
    $pdo = new PDO(
        "mysql:host={$db_config['host']};port={$db_config['port']};dbname={$db_config['dbname']};charset={$db_config['charset']}",
        $db_config['username'],
        $db_config['password'],
        [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
            PDO::ATTR_EMULATE_PREPARES => false
        ]
    );
} catch (PDOException $e) {
    echo json_encode(['success' => false, 'message' => '数据库连接失败']);
    exit;
}

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode(['success' => false, 'message' => '请求方法错误']);
    exit;
}

$input = json_decode(file_get_contents('php://input'), true);
$phone = trim($input['phone'] ?? '');

if (empty($phone)) {
    echo json_encode(['success' => false, 'message' => '请输入手机号']);
    exit;
}

// 验证手机号格式
if (!preg_match('/^1[3-9]\d{9}$/', $phone)) {
    echo json_encode(['success' => false, 'message' => '请输入正确的手机号']);
    exit;
}

try {
    // 查找用户
    $stmt = $pdo->prepare("SELECT id, username, nickname, quwan_id, phone, avatar, status, created_at FROM users WHERE phone = ?");
    $stmt->execute([$phone]);
    $user = $stmt->fetch();

    if ($user) {
        // 用户存在
        
        // 检查用户状态
        if ($user['status'] === 'banned') {
            echo json_encode([
                'success' => false, 
                'message' => '账户已被封禁，请联系客服'
            ]);
            exit;
        }

        // 检查是否为未完成注册的用户
        if ($user['status'] === 'incomplete') {
            echo json_encode([
                'success' => true,
                'exists' => false,
                'message' => '请完成注册流程'
            ]);
            exit;
        }

        // 返回用户信息（用于登录流程）
        echo json_encode([
            'success' => true,
            'exists' => true,
            'user' => [
                'id' => $user['id'],
                'username' => $user['username'],
                'nickname' => $user['nickname'],
                'quwan_id' => $user['quwan_id'],
                'phone' => $user['phone'],
                'avatar' => $user['avatar'] ?? 'https://s1.imagehub.cc/images/2025/04/26/f5c2f26c4564aabcbb56c5fd30f1d652.jpg',
                'phone_masked' => substr($phone, 0, 3) . '****' . substr($phone, -4)
            ]
        ]);
    } else {
        // 用户不存在，需要注册
        echo json_encode([
            'success' => true,
            'exists' => false,
            'message' => '新用户，需要注册'
        ]);
    }

} catch (PDOException $e) {
    error_log("用户检测失败: " . $e->getMessage());
    echo json_encode(['success' => false, 'message' => '查询失败，请稍后重试']);
}
?>
