<?php
/**
 * Vue客服系统 - 会话管理接口
 * 处理会话的创建、获取、更新等操作
 */

header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// 处理预检请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

// 引入数据库配置
require_once '../../config/database.php';

$method = $_SERVER['REQUEST_METHOD'];
$action = $_GET['action'] ?? '';

try {
    switch ($method) {
        case 'GET':
            handleGetRequest($pdo, $action);
            break;
        case 'POST':
            handlePostRequest($pdo, $action);
            break;
        case 'PUT':
            handlePutRequest($pdo, $action);
            break;
        default:
            http_response_code(405);
            echo json_encode(['success' => false, 'message' => '不支持的请求方法']);
    }
} catch (Exception $e) {
    error_log("会话管理接口错误: " . $e->getMessage());
    echo json_encode([
        'success' => false,
        'message' => '服务器错误',
        'error' => $e->getMessage()
    ]);
}

/**
 * 处理GET请求
 */
function handleGetRequest($pdo, $action) {
    switch ($action) {
        case 'get_session':
            getSession($pdo);
            break;
        case 'get_messages':
            getMessages($pdo);
            break;
        case 'get_waiting_sessions':
            getWaitingSessions($pdo);
            break;
        case 'get_active_sessions':
            getActiveSessions($pdo);
            break;
        default:
            echo json_encode(['success' => false, 'message' => '未知操作']);
    }
}

/**
 * 处理POST请求
 */
function handlePostRequest($pdo, $action) {
    switch ($action) {
        case 'create_session':
            createSession($pdo);
            break;
        case 'assign_customer_service':
            assignCustomerService($pdo);
            break;
        default:
            echo json_encode(['success' => false, 'message' => '未知操作']);
    }
}

/**
 * 处理PUT请求
 */
function handlePutRequest($pdo, $action) {
    switch ($action) {
        case 'update_session_status':
            updateSessionStatus($pdo);
            break;
        case 'end_session':
            endSession($pdo);
            break;
        default:
            echo json_encode(['success' => false, 'message' => '未知操作']);
    }
}

/**
 * 创建新会话
 */
function createSession($pdo) {
    $input = json_decode(file_get_contents('php://input'), true);
    
    $user_id = $input['user_id'] ?? 0;
    $user_phone = $input['user_phone'] ?? '';
    $user_name = $input['user_name'] ?? '';
    $source = $input['source'] ?? 'web';
    
    // 生成会话ID
    $session_id = 'cs_' . date('YmdHis') . '_' . rand(1000, 9999);
    
    $stmt = $pdo->prepare("
        INSERT INTO customer_service_sessions (
            session_id,
            user_id,
            user_phone,
            user_name,
            status,
            source,
            started_at,
            created_at
        ) VALUES (?, ?, ?, ?, 'waiting', ?, NOW(), NOW())
    ");
    
    $stmt->execute([
        $session_id,
        $user_id,
        $user_phone,
        $user_name,
        $source
    ]);
    
    echo json_encode([
        'success' => true,
        'message' => '会话创建成功',
        'data' => [
            'session_id' => $session_id,
            'status' => 'waiting'
        ]
    ]);
}

/**
 * 获取会话信息
 */
function getSession($pdo) {
    $session_id = $_GET['session_id'] ?? '';
    
    if (empty($session_id)) {
        echo json_encode(['success' => false, 'message' => '缺少会话ID']);
        return;
    }
    
    $stmt = $pdo->prepare("
        SELECT 
            s.*,
            cs.name as customer_service_name,
            cs.avatar as customer_service_avatar
        FROM customer_service_sessions s
        LEFT JOIN customer_service_users cs ON s.customer_service_id = cs.id
        WHERE s.session_id = ?
    ");
    $stmt->execute([$session_id]);
    $session = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($session) {
        echo json_encode([
            'success' => true,
            'data' => $session
        ]);
    } else {
        echo json_encode([
            'success' => false,
            'message' => '会话不存在'
        ]);
    }
}

/**
 * 获取会话消息
 */
function getMessages($pdo) {
    $session_id = $_GET['session_id'] ?? '';
    $limit = $_GET['limit'] ?? 50;
    $offset = $_GET['offset'] ?? 0;
    
    if (empty($session_id)) {
        echo json_encode(['success' => false, 'message' => '缺少会话ID']);
        return;
    }
    
    $stmt = $pdo->prepare("
        SELECT 
            id,
            sender_type,
            sender_id,
            sender_name,
            message_type,
            content,
            file_url,
            file_name,
            is_read,
            created_at
        FROM customer_service_messages 
        WHERE session_id = ? 
        ORDER BY id DESC 
        LIMIT ? OFFSET ?
    ");
    $stmt->execute([$session_id, $limit, $offset]);
    $messages = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // 反转数组，使最新消息在底部
    $messages = array_reverse($messages);
    
    echo json_encode([
        'success' => true,
        'data' => $messages
    ]);
}

/**
 * 获取等待中的会话
 */
function getWaitingSessions($pdo) {
    $stmt = $pdo->prepare("
        SELECT 
            session_id,
            user_id,
            user_name,
            user_phone,
            priority,
            started_at,
            message_count
        FROM customer_service_sessions 
        WHERE status = 'waiting' 
        ORDER BY priority DESC, started_at ASC
    ");
    $stmt->execute();
    $sessions = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo json_encode([
        'success' => true,
        'data' => $sessions
    ]);
}

/**
 * 获取活跃会话
 */
function getActiveSessions($pdo) {
    $customer_service_id = $_GET['customer_service_id'] ?? 0;
    
    $sql = "
        SELECT 
            s.session_id,
            s.user_id,
            s.user_name,
            s.user_phone,
            s.priority,
            s.started_at,
            s.message_count,
            cs.name as customer_service_name
        FROM customer_service_sessions s
        LEFT JOIN customer_service_users cs ON s.customer_service_id = cs.id
        WHERE s.status = 'active'
    ";
    
    $params = [];
    if ($customer_service_id > 0) {
        $sql .= " AND s.customer_service_id = ?";
        $params[] = $customer_service_id;
    }
    
    $sql .= " ORDER BY s.updated_at DESC";
    
    $stmt = $pdo->prepare($sql);
    $stmt->execute($params);
    $sessions = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo json_encode([
        'success' => true,
        'data' => $sessions
    ]);
}

/**
 * 分配客服
 */
function assignCustomerService($pdo) {
    $input = json_decode(file_get_contents('php://input'), true);
    
    $session_id = $input['session_id'] ?? '';
    $customer_service_id = $input['customer_service_id'] ?? 0;
    
    if (empty($session_id) || $customer_service_id <= 0) {
        echo json_encode(['success' => false, 'message' => '参数错误']);
        return;
    }
    
    $stmt = $pdo->prepare("
        UPDATE customer_service_sessions 
        SET 
            customer_service_id = ?,
            status = 'active',
            updated_at = NOW()
        WHERE session_id = ? AND status = 'waiting'
    ");
    
    $result = $stmt->execute([$customer_service_id, $session_id]);
    
    if ($stmt->rowCount() > 0) {
        echo json_encode([
            'success' => true,
            'message' => '客服分配成功'
        ]);
    } else {
        echo json_encode([
            'success' => false,
            'message' => '分配失败，会话可能已被处理'
        ]);
    }
}

/**
 * 更新会话状态
 */
function updateSessionStatus($pdo) {
    $input = json_decode(file_get_contents('php://input'), true);
    
    $session_id = $input['session_id'] ?? '';
    $status = $input['status'] ?? '';
    
    if (empty($session_id) || empty($status)) {
        echo json_encode(['success' => false, 'message' => '参数错误']);
        return;
    }
    
    $stmt = $pdo->prepare("
        UPDATE customer_service_sessions 
        SET status = ?, updated_at = NOW()
        WHERE session_id = ?
    ");
    
    $stmt->execute([$status, $session_id]);
    
    echo json_encode([
        'success' => true,
        'message' => '状态更新成功'
    ]);
}

/**
 * 结束会话
 */
function endSession($pdo) {
    $input = json_decode(file_get_contents('php://input'), true);
    
    $session_id = $input['session_id'] ?? '';
    $satisfaction_score = $input['satisfaction_score'] ?? null;
    $satisfaction_comment = $input['satisfaction_comment'] ?? '';
    
    if (empty($session_id)) {
        echo json_encode(['success' => false, 'message' => '缺少会话ID']);
        return;
    }
    
    // 计算会话时长
    $stmt = $pdo->prepare("
        SELECT started_at FROM customer_service_sessions 
        WHERE session_id = ?
    ");
    $stmt->execute([$session_id]);
    $session = $stmt->fetch(PDO::FETCH_ASSOC);
    
    $duration = 0;
    if ($session) {
        $start_time = strtotime($session['started_at']);
        $duration = time() - $start_time;
    }
    
    $stmt = $pdo->prepare("
        UPDATE customer_service_sessions 
        SET 
            status = 'closed',
            ended_at = NOW(),
            duration = ?,
            satisfaction_score = ?,
            satisfaction_comment = ?,
            updated_at = NOW()
        WHERE session_id = ?
    ");
    
    $stmt->execute([
        $duration,
        $satisfaction_score,
        $satisfaction_comment,
        $session_id
    ]);
    
    echo json_encode([
        'success' => true,
        'message' => '会话已结束'
    ]);
}
?>
