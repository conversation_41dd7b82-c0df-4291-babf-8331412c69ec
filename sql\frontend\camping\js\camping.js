/**
 * 露营页面JavaScript
 * 处理页面交互逻辑
 */

class CampingPage {
    constructor() {
        this.loadingOverlay = document.getElementById('loading-overlay');
        this.toast = document.getElementById('toast');
        this.countdown = document.getElementById('countdown');
        this.couponList = document.getElementById('coupon-list');

        this.init();
    }

    init() {
        this.bindEvents();
        this.startCountdown();
        this.loadCoupons();
        this.initAnimations();
    }

    bindEvents() {
        // 优惠券领取事件
        const claimBtns = document.querySelectorAll('.claim-btn');
        claimBtns.forEach(btn => {
            btn.addEventListener('click', (e) => {
                e.stopPropagation();
                this.claimCoupon(btn);
            });
        });

        // 活动参加事件
        const joinActivityBtns = document.querySelectorAll('.join-activity-btn');
        joinActivityBtns.forEach(btn => {
            btn.addEventListener('click', (e) => {
                e.stopPropagation();
                this.joinActivity(btn);
            });
        });

        // 发起活动事件
        const createActivityBtn = document.querySelector('.create-activity-btn');
        if (createActivityBtn) {
            createActivityBtn.addEventListener('click', () => {
                this.createActivity();
            });
        }

        // 活动分类点击事件
        const categoryItems = document.querySelectorAll('.category-item');
        categoryItems.forEach(item => {
            item.addEventListener('click', () => {
                this.viewCategoryActivities(item);
            });
        });

        // 攻略点击事件
        const guideItems = document.querySelectorAll('.guide-item');
        guideItems.forEach(item => {
            item.addEventListener('click', () => {
                this.viewGuide(item);
            });
        });

        // 活动卡片点击事件
        const activityCards = document.querySelectorAll('.activity-card');
        activityCards.forEach(card => {
            card.addEventListener('click', (e) => {
                // 如果点击的是按钮，不触发卡片点击
                if (e.target.classList.contains('join-activity-btn')) {
                    return;
                }
                this.viewActivityDetail(card);
            });
        });

        // 分享按钮事件
        const shareBtn = document.querySelector('.share-btn');
        if (shareBtn) {
            shareBtn.addEventListener('click', () => {
                this.sharePage();
            });
        }

        // 活动规则入口点击事件
        const rulesEntrance = document.getElementById('activity-rules-entrance');
        if (rulesEntrance) {
            rulesEntrance.addEventListener('click', () => {
                this.viewActivityRules();
            });
        }
    }

    // 加载优惠券
    loadCoupons() {
        if (!this.couponList) return;

        // 从后端获取优惠券数据
        fetch('api/get_coupons.php')
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    this.renderCoupons(data.coupons);
                } else {
                    // 如果后端接口不存在，使用默认数据
                    this.renderDefaultCoupons();
                }
            })
            .catch(() => {
                // 接口错误时使用默认数据
                this.renderDefaultCoupons();
            });
    }

    // 渲染默认优惠券
    renderDefaultCoupons() {
        const defaultCoupons = [
            {
                id: 1,
                title: '新人专享券',
                description: '首次参加露营活动专享优惠',
                amount: 20,
                type: 'newbie_discount',
                validity: '30天有效'
            },
            {
                id: 2,
                title: '放肆趣玩券',
                description: '参加任意露营活动立减优惠',
                amount: 30,
                type: 'join_discount',
                validity: '30天有效'
            }
        ];
        this.renderCoupons(defaultCoupons);
    }

    // 渲染优惠券
    renderCoupons(coupons) {
        if (!this.couponList) return;

        this.couponList.innerHTML = '';

        coupons.forEach(coupon => {
            const couponItem = document.createElement('div');
            couponItem.className = `coupon-item ${coupon.type === 'newbie_discount' ? 'newbie' : ''}`;
            couponItem.setAttribute('data-coupon-id', coupon.id);

            couponItem.innerHTML = `
                <div class="coupon-amount">¥${coupon.amount}</div>
                <div class="coupon-title">${coupon.title}</div>
                <div class="coupon-desc">${coupon.description}</div>
                <div class="coupon-validity">${coupon.validity}</div>
                <button class="claim-btn">立即领取</button>
            `;

            // 绑定点击事件
            const claimBtn = couponItem.querySelector('.claim-btn');
            claimBtn.addEventListener('click', (e) => {
                e.stopPropagation();
                this.claimCoupon(claimBtn, coupon);
            });

            this.couponList.appendChild(couponItem);
        });
    }

    // 优惠券领取
    claimCoupon(btn, couponData) {
        if (btn.classList.contains('claimed')) {
            this.showToast('您已经领取过这张活动优惠券了');
            return;
        }

        // 检查登录状态
        if (!this.checkLoginStatus()) {
            this.showToast('请先登录后再领取优惠券');
            setTimeout(() => {
                window.location.href = '../login/index.php';
            }, 1500);
            return;
        }

        this.showLoading();

        // 发送领取请求到后端
        fetch('api/claim_coupon.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                coupon_id: couponData.id
            })
        })
        .then(response => response.json())
        .then(data => {
            this.hideLoading();
            if (data.success) {
                btn.textContent = '已领取';
                btn.classList.add('claimed');
                this.showToast('活动优惠券领取成功！已存入您的券包');

                // 添加成功动画效果
                btn.style.transform = 'scale(1.1)';
                setTimeout(() => {
                    btn.style.transform = 'scale(1)';
                }, 200);
            } else {
                this.showToast(data.message || '领取失败，请重试');
            }
        })
        .catch(() => {
            this.hideLoading();
            this.showToast('网络错误，请重试');
        });
    }

    // 检查登录状态
    checkLoginStatus() {
        // 优先使用PHP传递的登录状态
        if (typeof window.userLoggedIn !== 'undefined') {
            return window.userLoggedIn;
        }

        // 备用检查方法
        return document.cookie.includes('user_logged_in') ||
               sessionStorage.getItem('user_id') ||
               localStorage.getItem('user_id') ||
               document.body.getAttribute('data-logged-in') === 'true';
    }

    // 参加露营活动
    joinActivity(btn) {
        const activityCard = btn.closest('.activity-card');
        const activityName = activityCard.querySelector('h3').textContent;

        this.showLoading();

        // 模拟API请求
        setTimeout(() => {
            this.hideLoading();
            btn.textContent = '已参加';
            btn.style.background = 'var(--success-color)';
            btn.disabled = true;
            this.showToast(`成功参加 ${activityName}！`);

            // 更新参与人数
            const participantInfo = activityCard.querySelector('.detail-item:nth-child(3) span');
            if (participantInfo) {
                const currentText = participantInfo.textContent;
                const currentCount = parseInt(currentText.match(/\d+/)[0]);
                participantInfo.textContent = currentText.replace(/已有\d+人/, `已有${currentCount + 1}人`);
            }
        }, 1000);
    }

    // 发起露营活动
    createActivity() {
        this.showToast('正在跳转到活动发起页面...');

        // 这里可以跳转到活动发起页面
        setTimeout(() => {
            this.showToast('活动发起功能即将上线，敬请期待！');
        }, 1000);
    }

    // 查看分类活动
    viewCategoryActivities(item) {
        const categoryName = item.querySelector('.category-name').textContent;
        this.showToast(`正在查看 ${categoryName} 相关活动...`);

        // 这里可以跳转到分类活动页面
        // window.location.href = `category.php?type=${item.dataset.category}`;
    }

    // 查看攻略详情
    viewGuide(item) {
        const guideTitle = item.querySelector('h4').textContent;
        this.showToast(`正在查看攻略：${guideTitle}`);

        // 这里可以跳转到攻略详情页
        // window.location.href = `guide-detail.php?id=${item.dataset.guideId}`;
    }

    // 查看活动详情
    viewActivityDetail(card) {
        const activityName = card.querySelector('h3').textContent;
        this.showToast(`正在查看活动详情：${activityName}`);

        // 这里可以跳转到活动详情页
        // window.location.href = `activity-detail.php?id=${card.dataset.activityId}`;
    }

    // 查看活动规则
    viewActivityRules() {
        this.showToast('正在跳转到活动规则页面...');

        // 跳转到活动规则页面
        setTimeout(() => {
            window.location.href = 'activity-rules.php';
        }, 500);
    }

    // 分享页面
    sharePage() {
        if (navigator.share) {
            navigator.share({
                title: '露营活动 - 趣玩星球',
                text: '发现精彩的露营活动，与志同道合的朋友一起探索自然',
                url: window.location.href
            });
        } else {
            // 复制链接到剪贴板
            navigator.clipboard.writeText(window.location.href).then(() => {
                this.showToast('链接已复制到剪贴板');
            }).catch(() => {
                this.showToast('分享功能暂不可用');
            });
        }
    }

    // 倒计时功能
    startCountdown() {
        if (!this.countdown) return;

        let timeLeft = 24 * 60 * 60 - 1; // 23:59:59

        const updateCountdown = () => {
            const hours = Math.floor(timeLeft / 3600);
            const minutes = Math.floor((timeLeft % 3600) / 60);
            const seconds = timeLeft % 60;

            this.countdown.textContent =
                `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;

            timeLeft--;
            if (timeLeft < 0) {
                timeLeft = 24 * 60 * 60 - 1; // 重置为24小时
            }
        };

        updateCountdown();
        setInterval(updateCountdown, 1000);
    }

    // 初始化动画
    initAnimations() {
        // 页面加载动画
        const cards = document.querySelectorAll('.coupon-card, .spot-card, .activity-card');
        cards.forEach((card, index) => {
            card.style.opacity = '0';
            card.style.transform = 'translateY(20px)';

            setTimeout(() => {
                card.style.transition = 'all 0.6s ease';
                card.style.opacity = '1';
                card.style.transform = 'translateY(0)';
            }, index * 100);
        });
    }

    // 显示加载动画
    showLoading() {
        if (this.loadingOverlay) {
            this.loadingOverlay.classList.add('active');
        }
    }

    // 隐藏加载动画
    hideLoading() {
        if (this.loadingOverlay) {
            this.loadingOverlay.classList.remove('active');
        }
    }

    // 显示Toast提示
    showToast(message, duration = 3000) {
        if (!this.toast) return;

        this.toast.textContent = message;
        this.toast.style.display = 'block';
        this.toast.style.opacity = '0';
        this.toast.style.transform = 'translateX(-50%) translateY(20px)';

        // 动画显示
        setTimeout(() => {
            this.toast.style.transition = 'all 0.3s ease';
            this.toast.style.opacity = '1';
            this.toast.style.transform = 'translateX(-50%) translateY(0)';
        }, 10);

        // 自动隐藏
        setTimeout(() => {
            this.toast.style.opacity = '0';
            this.toast.style.transform = 'translateX(-50%) translateY(20px)';

            setTimeout(() => {
                this.toast.style.display = 'none';
            }, 300);
        }, duration);
    }
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    new CampingPage();
});

// 确保全局可访问
window.CampingPage = CampingPage;
