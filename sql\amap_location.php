<?php
/**
 * 高德地图API配置文件
 * 用于生成高德地图JS API的URL
 */

// 高德地图API配置
$amap_config = [
    'web_key' => 'e318a539d606974c5f13654e905cf555',
    'security_key' => 'b2d738d21b1aed6f1ec0d61a9c935a7b',
    'js_api_version' => '2.0'
];

/**
 * 获取高德地图JS API的URL
 * @return string 高德地图JS API的完整URL
 */
function getAmapJsApiUrl() {
    global $amap_config;
    
    $base_url = 'https://webapi.amap.com/maps';
    $version = $amap_config['js_api_version'];
    $key = $amap_config['web_key'];
    
    // 构建完整的API URL
    $api_url = "{$base_url}?v={$version}&key={$key}&plugin=AMap.Geolocation";
    
    return $api_url;
}

/**
 * 获取高德地图Web服务API的Key
 * @return string Web服务API Key
 */
function getAmapWebServiceKey() {
    global $amap_config;
    return $amap_config['web_key'];
}

/**
 * 获取高德地图安全密钥
 * @return string 安全密钥
 */
function getAmapSecurityKey() {
    global $amap_config;
    return $amap_config['security_key'];
}

/**
 * 验证高德地图API配置是否完整
 * @return bool 配置是否完整
 */
function validateAmapConfig() {
    global $amap_config;
    
    return !empty($amap_config['web_key']) && 
           !empty($amap_config['security_key']) && 
           !empty($amap_config['js_api_version']);
}

/**
 * 获取地理编码API URL
 * @param string $address 地址
 * @return string 地理编码API URL
 */
function getGeocodingApiUrl($address) {
    $key = getAmapWebServiceKey();
    $encoded_address = urlencode($address);
    return "https://restapi.amap.com/v3/geocode/geo?key={$key}&address={$encoded_address}";
}

/**
 * 获取逆地理编码API URL
 * @param float $longitude 经度
 * @param float $latitude 纬度
 * @return string 逆地理编码API URL
 */
function getReverseGeocodingApiUrl($longitude, $latitude) {
    $key = getAmapWebServiceKey();
    $location = "{$longitude},{$latitude}";
    return "https://restapi.amap.com/v3/geocode/regeo?key={$key}&location={$location}";
}

/**
 * 获取IP定位API URL
 * @param string $ip IP地址（可选）
 * @return string IP定位API URL
 */
function getIpLocationApiUrl($ip = '') {
    $key = getAmapWebServiceKey();
    $url = "https://restapi.amap.com/v3/ip?key={$key}";
    if (!empty($ip)) {
        $url .= "&ip={$ip}";
    }
    return $url;
}

/**
 * 调用高德地图API并返回结果
 * @param string $url API URL
 * @return array|false API返回的数据或false
 */
function callAmapApi($url) {
    $context = stream_context_create([
        'http' => [
            'timeout' => 10,
            'method' => 'GET',
            'header' => [
                'User-Agent: Mozilla/5.0 (compatible; QuwanPlanet/1.0)',
                'Accept: application/json'
            ]
        ]
    ]);
    
    $response = @file_get_contents($url, false, $context);
    
    if ($response === false) {
        error_log("高德地图API调用失败: {$url}");
        return false;
    }
    
    $data = json_decode($response, true);
    
    if (json_last_error() !== JSON_ERROR_NONE) {
        error_log("高德地图API响应解析失败: " . json_last_error_msg());
        return false;
    }
    
    return $data;
}

/**
 * 根据IP获取城市信息
 * @param string $ip IP地址（可选，默认使用客户端IP）
 * @return string|false 城市名称或false
 */
function getCityByIp($ip = '') {
    $url = getIpLocationApiUrl($ip);
    $result = callAmapApi($url);
    
    if ($result && isset($result['status']) && $result['status'] == '1') {
        if (isset($result['city']) && !empty($result['city'])) {
            return $result['city'];
        } elseif (isset($result['province']) && !empty($result['province'])) {
            // 如果没有城市信息，返回省份（适用于直辖市）
            return $result['province'];
        }
    }
    
    return false;
}

/**
 * 获取客户端真实IP地址
 * @return string IP地址
 */
function getClientIp() {
    $ip_keys = ['HTTP_X_FORWARDED_FOR', 'HTTP_X_REAL_IP', 'HTTP_CLIENT_IP', 'REMOTE_ADDR'];
    
    foreach ($ip_keys as $key) {
        if (isset($_SERVER[$key]) && !empty($_SERVER[$key])) {
            $ips = explode(',', $_SERVER[$key]);
            $ip = trim($ips[0]);
            
            // 验证IP地址格式
            if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE)) {
                return $ip;
            }
        }
    }
    
    return $_SERVER['REMOTE_ADDR'] ?? '127.0.0.1';
}

/**
 * 智能获取用户城市
 * 优先使用IP定位，失败时返回默认城市
 * @param string $default_city 默认城市
 * @return string 城市名称
 */
function getUserCity($default_city = '北海市') {
    $client_ip = getClientIp();
    
    // 如果是本地IP，直接返回默认城市
    if (in_array($client_ip, ['127.0.0.1', '::1', 'localhost'])) {
        return $default_city;
    }
    
    $city = getCityByIp($client_ip);
    
    return $city ?: $default_city;
}

// 初始化检查
if (!validateAmapConfig()) {
    error_log("高德地图API配置不完整，请检查配置文件");
}
?>
