-- =====================================================
-- 简单版本：修复优惠券表缺失的 used_quantity 字段
-- 兼容所有MySQL版本
-- =====================================================

-- 方法1：直接添加字段（如果字段已存在会报错，但不影响功能）
ALTER TABLE camping_coupons 
ADD COLUMN used_quantity INT(11) DEFAULT 0 COMMENT '已使用数量';

-- 如果上面的语句报错"字段已存在"，请忽略错误继续执行下面的语句

-- 初始化现有数据的 used_quantity 字段
UPDATE camping_coupons cc
SET used_quantity = (
    SELECT COUNT(*)
    FROM user_camping_coupons ucc
    WHERE ucc.coupon_id = cc.id AND ucc.status = 'used'
);

-- 验证修复结果
SELECT 
    'used_quantity field processed successfully!' as message,
    COUNT(*) as total_coupons,
    SUM(COALESCE(used_quantity, 0)) as total_used
FROM camping_coupons;
