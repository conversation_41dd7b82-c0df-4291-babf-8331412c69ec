/* CSS变量定义 - 主题色系统 */
:root {
    --primary-color: #6F7BF5;
    --primary-light: #8A94F7;
    --primary-dark: #5A67E8;
    --secondary-color: #7C3AED;
    --accent-color: #FF6B9D;
    --text-primary: #333333;
    --text-secondary: #666666;
    --text-light: #999999;
    --bg-white: #FFFFFF;
    --bg-light: #F8F9FA;
    --shadow-sm: 0 2px 8px rgba(111, 123, 245, 0.08);
    --shadow-md: 0 4px 16px rgba(111, 123, 245, 0.12);
    --shadow-lg: 0 8px 32px rgba(111, 123, 245, 0.16);
    --radius-sm: 8px;
    --radius-md: 12px;
    --radius-lg: 16px;
    --transition-fast: 0.2s ease;
    --transition-normal: 0.3s ease;
}

/* 防止内容被选择 */
* {
    -webkit-touch-callout: none;
    -webkit-user-select: none;
    -khtml-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    height: 100%;
}

body {
    background: linear-gradient(135deg, #0a0a2e 0%, #16213e 25%, #1a1a3a 50%, #0f3460 75%, #0a0a2e 100%);
    position: relative;
    margin: 0;
    padding: 0;
    min-height: 100vh;
    overflow-x: hidden;
    font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;
}

/* 星空背景动画 */
.space-background {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 1;
    overflow: hidden;
}

/* 主星球和轨道系统 */
.planet-system {
    position: absolute;
    top: 40px;
    left: 50%;
    transform: translateX(-50%);
    width: 150px;
    height: 150px;
}

/* 轨道 */
.orbit {
    position: absolute;
    border: 1px solid rgba(64, 224, 208, 0.2);
    border-radius: 50%;
    animation: orbitRotate 30s linear infinite;
}

.orbit-1 {
    width: 60px;
    height: 60px;
    top: 45px;
    left: 45px;
}

.orbit-2 {
    width: 90px;
    height: 90px;
    top: 30px;
    left: 30px;
    animation-duration: 45s;
    animation-direction: reverse;
}

.orbit-3 {
    width: 120px;
    height: 120px;
    top: 15px;
    left: 15px;
    animation-duration: 60s;
}

/* 中心星球 */
.central-planet {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 25px;
    height: 25px;
    background: radial-gradient(circle at 30% 30%, var(--primary-light), var(--primary-color), var(--primary-dark));
    border-radius: 50%;
    box-shadow:
        inset -5px -5px 10px rgba(32, 178, 170, 0.4),
        0 0 15px rgba(64, 224, 208, 0.3),
        0 0 30px rgba(64, 224, 208, 0.1);
    animation: planetPulse 4s ease-in-out infinite;
}

/* 轨道上的圆角星星 */
.orbit-star {
    position: absolute;
    width: 3px;
    height: 3px;
    background: #FFD700;
    border-radius: 1px;
    box-shadow: 0 0 4px #FFD700, 0 0 8px #FFD700;
    animation: starTwinkle 2s ease-in-out infinite;
}

.orbit-1 .orbit-star {
    top: -1.5px;
    left: 50%;
    transform: translateX(-50%);
    animation-delay: 0s;
}

.orbit-2 .orbit-star {
    top: 50%;
    right: -1.5px;
    transform: translateY(-50%);
    animation-delay: 1s;
}

.orbit-3 .orbit-star {
    bottom: -1.5px;
    left: 30%;
    transform: translateX(-50%);
    animation-delay: 2s;
}

/* 容器 */
.container {
    position: relative;
    z-index: 10;
    margin: 0;
    padding: 70px 0 60px 0;
    min-height: 100vh;
    width: 100vw;
    background: rgba(255, 255, 255, 0.95);
    border-radius: 20px 20px 0 0;
    margin-top: 200px;
}

/* 头部 */
.header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 10px 20px;
    border-bottom: 1px solid rgba(64, 224, 208, 0.1);
    margin-bottom: 10px;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    z-index: 1000;
}

.back-btn {
    background: none;
    border: none;
    color: var(--primary-color);
    cursor: pointer;
    padding: 8px;
    border-radius: var(--radius-sm);
    transition: var(--transition-normal);
}

.back-btn:hover {
    background: rgba(64, 224, 208, 0.1);
}

.header h1 {
    font-size: 1.2rem;
    font-weight: 600;
    color: var(--text-primary);
}

.placeholder {
    width: 40px;
}

/* 搜索提示 */
.search-hint {
    padding: 10px 20px 15px;
    text-align: center;
}

.search-hint p {
    color: var(--text-secondary);
    font-size: 0.9rem;
    margin: 0;
}

/* 定位区域 */
.location-section {
    padding: 0 20px 20px;
}

.location-btn {
    width: 100%;
    display: flex;
    align-items: center;
    padding: 16px;
    background: var(--bg-white);
    border: 1px solid rgba(64, 224, 208, 0.2);
    border-radius: var(--radius-md);
    cursor: pointer;
    transition: var(--transition-normal);
    box-shadow: var(--shadow-sm);
}

.location-btn:hover {
    border-color: var(--primary-color);
    box-shadow: var(--shadow-md);
}

.location-icon {
    color: var(--primary-color);
    margin-right: 12px;
}

.location-text {
    flex: 1;
    text-align: left;
}

.location-title {
    font-size: 1rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 2px;
}

.location-subtitle {
    font-size: 0.85rem;
    color: var(--text-secondary);
}

.location-arrow {
    color: var(--text-light);
}

/* 搜索区域 */
.search-section {
    padding: 0 20px 20px;
}

.search-box {
    position: relative;
    display: flex;
    align-items: center;
}

.search-icon {
    position: absolute;
    left: 12px;
    color: var(--text-light);
    z-index: 2;
}

#search-input {
    width: 100%;
    padding: 12px 12px 12px 44px;
    border: 1px solid rgba(64, 224, 208, 0.2);
    border-radius: var(--radius-md);
    font-size: 1rem;
    outline: none;
    transition: var(--transition-normal);
    background: var(--bg-white);
}

#search-input:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(64, 224, 208, 0.1);
}

.clear-btn {
    position: absolute;
    right: 8px;
    background: none;
    border: none;
    color: var(--text-light);
    cursor: pointer;
    padding: 8px;
    border-radius: var(--radius-sm);
    display: none;
}

.clear-btn:hover {
    background: rgba(64, 224, 208, 0.1);
}

/* 搜索结果 */
.search-results {
    padding: 0 20px;
    margin-bottom: 20px;
}

.results-header {
    font-size: 0.9rem;
    font-weight: 600;
    color: var(--text-secondary);
    margin-bottom: 12px;
}

.results-list {
    background: var(--bg-white);
    border-radius: var(--radius-md);
    box-shadow: var(--shadow-sm);
    overflow: hidden;
}

.result-item {
    display: block;
    width: 100%;
    padding: 16px;
    border: none;
    background: none;
    text-align: left;
    cursor: pointer;
    transition: var(--transition-normal);
    border-bottom: 1px solid rgba(64, 224, 208, 0.1);
}

.result-item:last-child {
    border-bottom: none;
}

.result-item:hover {
    background: rgba(64, 224, 208, 0.05);
}

.result-name {
    font-size: 1rem;
    font-weight: 500;
    color: var(--text-primary);
    margin-bottom: 2px;
}

.result-address {
    font-size: 0.85rem;
    color: var(--text-secondary);
}

/* 热门城市 */
.popular-cities {
    padding: 0 20px;
    margin-bottom: 30px;
}

/* 全国城市 */
.all-cities {
    padding: 0 20px;
}

/* 地区分组 */
.region-group {
    margin-bottom: 30px;
}

.region-title {
    font-size: 1rem;
    font-weight: 600;
    color: var(--primary-color);
    margin-bottom: 16px;
    padding: 8px 12px;
    background: linear-gradient(135deg, rgba(64, 224, 208, 0.1), rgba(175, 251, 242, 0.1));
    border-radius: var(--radius-md);
    border-left: 4px solid var(--primary-color);
}

.section-title {
    font-size: 0.9rem;
    font-weight: 600;
    color: var(--text-secondary);
    margin-bottom: 16px;
}

.cities-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 12px;
}

.city-item {
    padding: 16px 12px;
    background: var(--bg-white);
    border: 1px solid rgba(64, 224, 208, 0.2);
    border-radius: var(--radius-md);
    font-size: 0.95rem;
    font-weight: 500;
    color: var(--text-primary);
    cursor: pointer;
    transition: var(--transition-normal);
    text-align: center;
}

.city-item:hover {
    border-color: var(--primary-color);
    background: rgba(64, 224, 208, 0.05);
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

/* Toast提示样式 */
.toast {
    position: fixed;
    top: 20px;
    left: 50%;
    transform: translateX(-50%);
    background-color: rgba(255, 255, 255, 0.95);
    color: #333333;
    padding: 14px 24px;
    border-radius: 12px;
    box-shadow: 0 6px 16px rgba(0, 0, 0, 0.12);
    z-index: 9999;
    display: none;
    border-left: 4px solid var(--primary-color);
    font-size: 15px;
    font-weight: 500;
    max-width: 90%;
    text-align: center;
    pointer-events: none;
}

/* 定位动画覆盖层 */
.location-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, #0a0a2e 0%, #16213e 25%, #1a1a3a 50%, #0f3460 75%, #0a0a2e 100%);
    display: none;
    align-items: center;
    justify-content: center;
    z-index: 2000;
    flex-direction: column;
}

/* 定位动画星球系统 */
.location-planet-system {
    width: 120px;
    height: 120px;
    position: relative;
    margin-bottom: 24px;
}

.location-orbit {
    position: absolute;
    border: 2px solid var(--primary-color);
    border-radius: 50%;
    animation: locationOrbitRotate 1.5s linear infinite;
}

.location-orbit-1 {
    width: 50px;
    height: 50px;
    top: 35px;
    left: 35px;
    border-color: var(--primary-color);
    box-shadow: 0 0 12px rgba(64, 224, 208, 0.5);
}

.location-orbit-2 {
    width: 75px;
    height: 75px;
    top: 22.5px;
    left: 22.5px;
    border-color: var(--secondary-color);
    animation-duration: 1.2s;
    animation-direction: reverse;
    box-shadow: 0 0 12px rgba(6, 214, 160, 0.5);
}

.location-orbit-3 {
    width: 100px;
    height: 100px;
    top: 10px;
    left: 10px;
    border-color: var(--accent-color);
    animation-duration: 1s;
    box-shadow: 0 0 12px rgba(255, 107, 157, 0.5);
}

.location-central-planet {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 25px;
    height: 25px;
    background: radial-gradient(circle at 30% 30%, var(--primary-light), var(--primary-color), var(--primary-dark));
    border-radius: 50%;
    box-shadow:
        inset -3px -3px 6px rgba(32, 178, 170, 0.4),
        0 0 15px rgba(64, 224, 208, 0.8),
        0 0 30px rgba(64, 224, 208, 0.4);
    animation: locationPlanetPulse 0.8s ease-in-out infinite;
}

.location-orbit-star {
    position: absolute;
    width: 4px;
    height: 4px;
    background: #FFD700;
    border-radius: 1px;
    box-shadow: 0 0 6px #FFD700, 0 0 12px #FFD700;
    animation: locationStarTwinkle 0.6s ease-in-out infinite;
}

.location-orbit-1 .location-orbit-star {
    top: -2px;
    left: 50%;
    transform: translateX(-50%);
}

.location-orbit-2 .location-orbit-star {
    top: 50%;
    right: -2px;
    transform: translateY(-50%);
    animation-delay: 0.2s;
}

.location-orbit-3 .location-orbit-star {
    bottom: -2px;
    left: 30%;
    transform: translateX(-50%);
    animation-delay: 0.4s;
}

.location-text {
    color: white;
    text-align: center;
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 8px;
    animation: textFadeIn 1s ease-out;
}

.location-subtext {
    color: rgba(255, 255, 255, 0.8);
    text-align: center;
    font-size: 0.9rem;
    animation: textFadeIn 1s ease-out 0.3s both;
}

/* 动画关键帧 */
@keyframes orbitRotate {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

@keyframes planetPulse {
    0%, 100% { transform: translate(-50%, -50%) scale(1); }
    50% { transform: translate(-50%, -50%) scale(1.05); }
}

@keyframes starTwinkle {
    0%, 100% { opacity: 0.3; transform: scale(0.8); }
    50% { opacity: 1; transform: scale(1.2); }
}

@keyframes locationOrbitRotate {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

@keyframes locationPlanetPulse {
    0%, 100% {
        transform: translate(-50%, -50%) scale(1);
        box-shadow:
            inset -3px -3px 6px rgba(32, 178, 170, 0.4),
            0 0 15px rgba(64, 224, 208, 0.8),
            0 0 30px rgba(64, 224, 208, 0.4);
    }
    50% {
        transform: translate(-50%, -50%) scale(1.2);
        box-shadow:
            inset -3px -3px 6px rgba(32, 178, 170, 0.6),
            0 0 20px rgba(64, 224, 208, 1),
            0 0 40px rgba(64, 224, 208, 0.6);
    }
}

@keyframes locationStarTwinkle {
    0%, 100% {
        opacity: 0.5;
        transform: scale(0.8);
        box-shadow: 0 0 4px #FFD700, 0 0 8px #FFD700;
    }
    50% {
        opacity: 1;
        transform: scale(1.5);
        box-shadow: 0 0 8px #FFD700, 0 0 16px #FFD700;
    }
}

@keyframes textFadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 响应式调整 */
@media (max-width: 480px) {
    .cities-grid {
        grid-template-columns: repeat(2, 1fr);
    }

    .container {
        padding: 180px 0 60px 0;
        margin-top: 180px;
    }

    .planet-system {
        width: 120px;
        height: 120px;
        top: 30px;
    }
}
