<?php
session_start();

// 检查用户是否已登录
if (!isset($_SESSION['user_id'])) {
    header('Location: ../login/index.php');
    exit;
}

$user_id = $_SESSION['user_id'];
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <meta name="theme-color" content="#40E0D0">
    <title>发布作品 - 趣玩星球</title>
    <link rel="stylesheet" href="css/publish.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <!-- 顶部导航栏 -->
    <div class="header-bar">
        <a href="../home/<USER>" class="back-button">
            <i class="fas fa-arrow-left"></i>
        </a>
        <div class="header-title">发布作品</div>
        <button class="publish-button" onclick="publishWork()">发布</button>
    </div>

    <div class="container">
        <form id="workForm" enctype="multipart/form-data">
            <!-- 图片上传区域 -->
            <div class="upload-section">
                <div class="upload-grid" id="uploadGrid">
                    <div class="upload-item add-photo" onclick="selectImages()">
                        <i class="fas fa-plus"></i>
                        <span>添加图片</span>
                        <small>最多9张</small>
                    </div>
                </div>
                <input type="file" id="imageInput" multiple accept="image/*" style="display: none;">
            </div>

            <!-- 标题输入 -->
            <div class="form-group">
                <label for="title">作品标题</label>
                <input type="text" id="title" name="title" placeholder="给你的作品起个标题吧..." maxlength="100" required>
                <div class="char-count">0/100</div>
            </div>

            <!-- 描述输入 -->
            <div class="form-group">
                <label for="description">作品描述</label>
                <textarea id="description" name="description" placeholder="分享你的创作故事..." maxlength="500" rows="4"></textarea>
                <div class="char-count">0/500</div>
            </div>

            <!-- 分类选择 -->
            <div class="form-group">
                <label for="category">作品分类</label>
                <select id="category" name="category" required>
                    <option value="">请选择分类</option>
                    <option value="photography">摄影</option>
                    <option value="art">艺术</option>
                    <option value="design">设计</option>
                    <option value="music">音乐</option>
                    <option value="video">视频</option>
                    <option value="writing">文字</option>
                    <option value="other">其他</option>
                </select>
            </div>

            <!-- 标签输入 -->
            <div class="form-group">
                <label for="tags">标签</label>
                <div class="tags-input-container">
                    <input type="text" id="tagsInput" placeholder="输入标签，按回车添加" maxlength="20">
                    <div class="tags-display" id="tagsDisplay"></div>
                </div>
                <small>最多添加5个标签，每个标签不超过20个字符</small>
            </div>

            <!-- 位置信息 -->
            <div class="form-group">
                <label for="location">位置</label>
                <div class="location-input">
                    <input type="text" id="location" name="location" placeholder="添加位置信息（可选）" readonly>
                    <button type="button" class="location-btn" onclick="getLocation()">
                        <i class="fas fa-map-marker-alt"></i>
                    </button>
                </div>
            </div>

            <!-- 隐私设置 -->
            <div class="form-group">
                <label>隐私设置</label>
                <div class="privacy-options">
                    <label class="radio-option">
                        <input type="radio" name="privacy" value="public" checked>
                        <span class="radio-custom"></span>
                        <div class="option-info">
                            <strong>公开</strong>
                            <small>所有人都可以看到</small>
                        </div>
                    </label>
                    <label class="radio-option">
                        <input type="radio" name="privacy" value="friends">
                        <span class="radio-custom"></span>
                        <div class="option-info">
                            <strong>仅好友</strong>
                            <small>只有好友可以看到</small>
                        </div>
                    </label>
                </div>
            </div>
        </form>
    </div>

    <!-- 加载提示 -->
    <div class="loading-overlay" id="loadingOverlay" style="display: none;">
        <div class="loading-content">
            <div class="loading-spinner"></div>
            <p>正在发布作品...</p>
        </div>
    </div>

    <!-- Toast提示 -->
    <div class="toast" id="toast"></div>

    <script src="js/work.js"></script>
</body>
</html>
