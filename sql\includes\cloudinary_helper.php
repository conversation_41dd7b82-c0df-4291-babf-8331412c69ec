<?php
/**
 * Cloudinary 集成助手类
 * 用于处理图片、视频和音频的上传和管理
 */
class CloudinaryHelper {
    private $cloud_name;
    private $api_key;
    private $api_secret;
    private $upload_url;
    
    /**
     * 构造函数，初始化Cloudinary配置
     */
    public function __construct() {
        // 从配置中获取Cloudinary凭证
        $this->cloud_name = 'dwcauq0wy';
        $this->api_key = '965165511998959';
        $this->api_secret = 'JYnkxTIAAC3GLuf3u6iiQpfqfMA';
        $this->upload_url = "https://api.cloudinary.com/v1_1/{$this->cloud_name}/auto/upload";
    }
    
    /**
     * 生成上传签名
     * @param array $params 上传参数
     * @return string 签名
     */
    private function generateSignature($params) {
        $params = array_filter($params);
        ksort($params);
        
        $signature_string = '';
        foreach ($params as $key => $value) {
            $signature_string .= $key . '=' . $value . '&';
        }
        $signature_string = rtrim($signature_string, '&');
        
        return hash('sha256', $signature_string . $this->api_secret);
    }
    
    /**
     * 上传文件到Cloudinary
     * @param string $file_path 文件路径
     * @param string $resource_type 资源类型 (image, video, auto)
     * @param array $options 上传选项
     * @return array 上传结果
     */
    public function upload($file_path, $resource_type = 'auto', $options = []) {
        // 检查文件是否存在
        if (!file_exists($file_path)) {
            return [
                'success' => false,
                'error' => 'File not found'
            ];
        }
        
        // 准备上传参数
        $timestamp = time();
        $upload_params = array_merge([
            'timestamp' => $timestamp,
            'api_key' => $this->api_key
        ], $options);
        
        // 生成签名
        $signature = $this->generateSignature($upload_params);
        
        // 准备表单数据
        $post_data = array_merge($upload_params, [
            'file' => new CURLFile($file_path),
            'signature' => $signature
        ]);
        
        // 发送请求
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $this->upload_url);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $post_data);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        
        $response = curl_exec($ch);
        $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);
        
        // 处理响应
        if ($http_code != 200) {
            return [
                'success' => false,
                'error' => 'Upload failed with HTTP code ' . $http_code,
                'response' => $response
            ];
        }
        
        $result = json_decode($response, true);
        
        if (!$result) {
            return [
                'success' => false,
                'error' => 'Failed to parse response',
                'response' => $response
            ];
        }
        
        return [
            'success' => true,
            'data' => $result
        ];
    }
    
    /**
     * 上传图片
     * @param string $file_path 文件路径
     * @param array $options 上传选项
     * @return array 上传结果
     */
    public function uploadImage($file_path, $options = []) {
        // 设置图片优化选项
        $default_options = [
            'folder' => 'quwanplanet/images',
            'transformation' => 'w_1200,c_limit'
        ];
        
        $options = array_merge($default_options, $options);
        return $this->upload($file_path, 'image', $options);
    }
    
    /**
     * 上传视频
     * @param string $file_path 文件路径
     * @param array $options 上传选项
     * @return array 上传结果
     */
    public function uploadVideo($file_path, $options = []) {
        // 设置视频优化选项
        $default_options = [
            'folder' => 'quwanplanet/videos',
            'resource_type' => 'video'
        ];
        
        $options = array_merge($default_options, $options);
        return $this->upload($file_path, 'video', $options);
    }
    
    /**
     * 上传音频
     * @param string $file_path 文件路径
     * @param array $options 上传选项
     * @return array 上传结果
     */
    public function uploadAudio($file_path, $options = []) {
        // 设置音频优化选项
        $default_options = [
            'folder' => 'quwanplanet/audio',
            'resource_type' => 'auto'
        ];
        
        $options = array_merge($default_options, $options);
        return $this->upload($file_path, 'auto', $options);
    }
    
    /**
     * 从Base64字符串上传图片
     * @param string $base64_string Base64编码的图片数据
     * @param array $options 上传选项
     * @return array 上传结果
     */
    public function uploadBase64Image($base64_string, $options = []) {
        // 准备上传参数
        $timestamp = time();
        $upload_params = array_merge([
            'timestamp' => $timestamp,
            'api_key' => $this->api_key,
            'folder' => 'quwanplanet/images'
        ], $options);
        
        // 生成签名
        $signature = $this->generateSignature($upload_params);
        
        // 准备表单数据
        $post_data = array_merge($upload_params, [
            'file' => $base64_string,
            'signature' => $signature
        ]);
        
        // 发送请求
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $this->upload_url);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($post_data));
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        
        $response = curl_exec($ch);
        $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);
        
        // 处理响应
        if ($http_code != 200) {
            return [
                'success' => false,
                'error' => 'Upload failed with HTTP code ' . $http_code,
                'response' => $response
            ];
        }
        
        $result = json_decode($response, true);
        
        if (!$result) {
            return [
                'success' => false,
                'error' => 'Failed to parse response',
                'response' => $response
            ];
        }
        
        return [
            'success' => true,
            'data' => $result
        ];
    }
    
    /**
     * 删除Cloudinary资源
     * @param string $public_id 资源公共ID
     * @param string $resource_type 资源类型
     * @return array 删除结果
     */
    public function delete($public_id, $resource_type = 'image') {
        // 准备删除参数
        $timestamp = time();
        $delete_params = [
            'public_id' => $public_id,
            'timestamp' => $timestamp,
            'api_key' => $this->api_key
        ];
        
        // 生成签名
        $signature = $this->generateSignature($delete_params);
        
        // 准备表单数据
        $post_data = array_merge($delete_params, [
            'signature' => $signature
        ]);
        
        // 发送请求
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, "https://api.cloudinary.com/v1_1/{$this->cloud_name}/{$resource_type}/destroy");
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($post_data));
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        
        $response = curl_exec($ch);
        $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);
        
        // 处理响应
        $result = json_decode($response, true);
        
        return [
            'success' => $http_code == 200,
            'data' => $result
        ];
    }
}
