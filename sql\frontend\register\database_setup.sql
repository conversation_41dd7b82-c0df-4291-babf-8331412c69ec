-- 趣玩星球用户表结构
-- 请在数据库中执行此SQL来创建或更新用户表

CREATE DATABASE IF NOT EXISTS quwanplanet CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

USE quwanplanet;

-- 创建用户表
CREATE TABLE IF NOT EXISTS users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    phone VARCHAR(11) NOT NULL UNIQUE COMMENT '手机号',
    quwan_id VARCHAR(7) NOT NULL UNIQUE COMMENT '7位趣玩ID',
    nickname VARCHAR(50) DEFAULT NULL COMMENT '昵称',
    email VARCHAR(100) DEFAULT NULL COMMENT '邮箱',
    password VARCHAR(255) DEFAULT NULL COMMENT '密码哈希',
    avatar VARCHAR(500) DEFAULT NULL COMMENT '头像URL',
    region VARCHAR(100) DEFAULT NULL COMMENT '地区',
    bio TEXT DEFAULT NULL COMMENT '个人简介',
    status ENUM('incomplete', 'active', 'banned') DEFAULT 'incomplete' COMMENT '用户状态',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    INDEX idx_phone (phone),
    INDEX idx_quwan_id (quwan_id),
    INDEX idx_email (email),
    INDEX idx_status (status),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户表';

-- 检查表是否存在必要字段，如果不存在则添加
-- 注意：在生产环境中运行前请先备份数据库

-- 添加quwan_id字段（如果不存在）
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
     WHERE table_name = 'users' 
     AND table_schema = 'quwanplanet' 
     AND column_name = 'quwan_id') > 0,
    'SELECT "quwan_id column already exists"',
    'ALTER TABLE users ADD COLUMN quwan_id VARCHAR(7) UNIQUE AFTER phone'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 添加status字段（如果不存在）
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
     WHERE table_name = 'users' 
     AND table_schema = 'quwanplanet' 
     AND column_name = 'status') > 0,
    'SELECT "status column already exists"',
    'ALTER TABLE users ADD COLUMN status ENUM("incomplete", "active", "banned") DEFAULT "incomplete" AFTER bio'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 为现有用户生成趣玩ID（如果quwan_id为空）
UPDATE users 
SET quwan_id = LPAD(FLOOR(1000000 + RAND() * 8999999), 7, '0')
WHERE quwan_id IS NULL OR quwan_id = '';

-- 确保所有现有完整用户的状态为active
UPDATE users 
SET status = 'active' 
WHERE nickname IS NOT NULL 
  AND email IS NOT NULL 
  AND password IS NOT NULL 
  AND status = 'incomplete';

-- 创建上传目录的说明
-- 请确保以下目录存在并有写入权限：
-- frontend/uploads/avatars/

-- 示例数据（可选，用于测试）
-- INSERT INTO users (phone, quwan_id, nickname, email, password, region, bio, status) VALUES
-- ('13800138000', '1000001', '测试用户', '<EMAIL>', '$2y$10$example_hash', '北京', '这是一个测试用户', 'active');
