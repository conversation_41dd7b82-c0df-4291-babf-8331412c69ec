-- =====================================================
-- 客服管理系统数据库脚本
-- 请在宝塔phpMyAdmin中执行此脚本
-- =====================================================

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- =====================================================
-- 1. 客服系统用户表 (customer_service_users)
-- =====================================================
CREATE TABLE IF NOT EXISTS `customer_service_users` (
    `id` INT PRIMARY KEY AUTO_INCREMENT,
    `employee_id` VARCHAR(20) UNIQUE NOT NULL COMMENT '工号',
    `name` VARCHAR(50) NOT NULL COMMENT '姓名',
    `password` VARCHAR(255) NOT NULL COMMENT '密码',
    `department` VARCHAR(100) NOT NULL COMMENT '部门',
    `team` VARCHAR(100) DEFAULT NULL COMMENT '组别',
    `role` ENUM('super_admin', 'customer_service') DEFAULT 'customer_service' COMMENT '角色',
    `status` ENUM('active', 'inactive') DEFAULT 'active' COMMENT '状态',
    `avatar` VARCHAR(500) DEFAULT NULL COMMENT '头像',
    `phone` VARCHAR(20) DEFAULT NULL COMMENT '手机号',
    `email` VARCHAR(100) DEFAULT NULL COMMENT '邮箱',
    `last_login` TIMESTAMP NULL COMMENT '最后登录时间',
    `login_count` INT DEFAULT 0 COMMENT '登录次数',
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX `idx_employee_id` (`employee_id`),
    INDEX `idx_role` (`role`),
    INDEX `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='客服系统用户表';

-- =====================================================
-- 2. 客服会话表 (customer_service_sessions)
-- =====================================================
CREATE TABLE IF NOT EXISTS `customer_service_sessions` (
    `id` INT PRIMARY KEY AUTO_INCREMENT,
    `session_id` VARCHAR(50) UNIQUE NOT NULL COMMENT '会话ID',
    `user_id` INT DEFAULT NULL COMMENT '用户ID',
    `user_phone` VARCHAR(20) DEFAULT NULL COMMENT '用户手机号',
    `user_name` VARCHAR(50) DEFAULT NULL COMMENT '用户昵称',
    `customer_service_id` INT DEFAULT NULL COMMENT '客服ID',
    `status` ENUM('waiting', 'active', 'closed', 'transferred') DEFAULT 'waiting' COMMENT '会话状态',
    `priority` ENUM('low', 'normal', 'high', 'urgent') DEFAULT 'normal' COMMENT '优先级',
    `category` VARCHAR(50) DEFAULT NULL COMMENT '问题分类',
    `source` ENUM('web', 'app', 'phone', 'email') DEFAULT 'web' COMMENT '来源渠道',
    `satisfaction_score` TINYINT DEFAULT NULL COMMENT '满意度评分(1-5)',
    `satisfaction_comment` TEXT DEFAULT NULL COMMENT '满意度评价',
    `started_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '开始时间',
    `ended_at` TIMESTAMP NULL COMMENT '结束时间',
    `duration` INT DEFAULT 0 COMMENT '会话时长(秒)',
    `message_count` INT DEFAULT 0 COMMENT '消息数量',
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (`customer_service_id`) REFERENCES `customer_service_users`(`id`),
    INDEX `idx_session_id` (`session_id`),
    INDEX `idx_user_id` (`user_id`),
    INDEX `idx_customer_service_id` (`customer_service_id`),
    INDEX `idx_status` (`status`),
    INDEX `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='客服会话表';

-- =====================================================
-- 3. 客服消息表 (customer_service_messages)
-- =====================================================
CREATE TABLE IF NOT EXISTS `customer_service_messages` (
    `id` INT PRIMARY KEY AUTO_INCREMENT,
    `session_id` VARCHAR(50) NOT NULL COMMENT '会话ID',
    `sender_type` ENUM('user', 'customer_service', 'system', 'bot') NOT NULL COMMENT '发送者类型',
    `sender_id` INT DEFAULT NULL COMMENT '发送者ID',
    `sender_name` VARCHAR(50) DEFAULT NULL COMMENT '发送者姓名',
    `message_type` ENUM('text', 'image', 'file', 'voice', 'video', 'system') DEFAULT 'text' COMMENT '消息类型',
    `content` TEXT NOT NULL COMMENT '消息内容',
    `file_url` VARCHAR(500) DEFAULT NULL COMMENT '文件URL',
    `file_name` VARCHAR(255) DEFAULT NULL COMMENT '文件名',
    `file_size` INT DEFAULT NULL COMMENT '文件大小',
    `is_read` BOOLEAN DEFAULT FALSE COMMENT '是否已读',
    `read_at` TIMESTAMP NULL COMMENT '阅读时间',
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (`session_id`) REFERENCES `customer_service_sessions`(`session_id`),
    INDEX `idx_session_id` (`session_id`),
    INDEX `idx_sender_type` (`sender_type`),
    INDEX `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='客服消息表';

-- =====================================================
-- 4. 客服质检表 (customer_service_quality_checks)
-- =====================================================
CREATE TABLE IF NOT EXISTS `customer_service_quality_checks` (
    `id` INT PRIMARY KEY AUTO_INCREMENT,
    `session_id` VARCHAR(50) NOT NULL COMMENT '会话ID',
    `customer_service_id` INT NOT NULL COMMENT '客服ID',
    `checker_id` INT NOT NULL COMMENT '质检员ID',
    `score` DECIMAL(3,1) NOT NULL COMMENT '质检得分',
    `response_time_score` DECIMAL(3,1) DEFAULT NULL COMMENT '响应时间得分',
    `service_attitude_score` DECIMAL(3,1) DEFAULT NULL COMMENT '服务态度得分',
    `problem_solving_score` DECIMAL(3,1) DEFAULT NULL COMMENT '问题解决得分',
    `professional_score` DECIMAL(3,1) DEFAULT NULL COMMENT '专业能力得分',
    `comments` TEXT DEFAULT NULL COMMENT '质检意见',
    `suggestions` TEXT DEFAULT NULL COMMENT '改进建议',
    `status` ENUM('pending', 'completed', 'disputed') DEFAULT 'pending' COMMENT '质检状态',
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (`session_id`) REFERENCES `customer_service_sessions`(`session_id`),
    FOREIGN KEY (`customer_service_id`) REFERENCES `customer_service_users`(`id`),
    FOREIGN KEY (`checker_id`) REFERENCES `customer_service_users`(`id`),
    INDEX `idx_session_id` (`session_id`),
    INDEX `idx_customer_service_id` (`customer_service_id`),
    INDEX `idx_checker_id` (`checker_id`),
    INDEX `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='客服质检表';

-- =====================================================
-- 5. 客服系统设置表 (customer_service_settings)
-- =====================================================
CREATE TABLE IF NOT EXISTS `customer_service_settings` (
    `id` INT PRIMARY KEY AUTO_INCREMENT,
    `setting_key` VARCHAR(100) UNIQUE NOT NULL COMMENT '设置键',
    `setting_value` TEXT NOT NULL COMMENT '设置值',
    `setting_type` ENUM('string', 'number', 'boolean', 'json') DEFAULT 'string' COMMENT '设置类型',
    `description` VARCHAR(255) DEFAULT NULL COMMENT '设置描述',
    `category` VARCHAR(50) DEFAULT NULL COMMENT '设置分类',
    `is_system` BOOLEAN DEFAULT FALSE COMMENT '是否系统设置',
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX `idx_setting_key` (`setting_key`),
    INDEX `idx_category` (`category`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='客服系统设置表';

-- =====================================================
-- 6. 智能客服配置表 (customer_service_bot_config)
-- =====================================================
CREATE TABLE IF NOT EXISTS `customer_service_bot_config` (
    `id` INT PRIMARY KEY AUTO_INCREMENT,
    `name` VARCHAR(100) NOT NULL COMMENT '机器人名称',
    `avatar` VARCHAR(500) DEFAULT NULL COMMENT '机器人头像',
    `welcome_message` TEXT DEFAULT NULL COMMENT '欢迎语',
    `default_reply` TEXT DEFAULT NULL COMMENT '默认回复',
    `status` ENUM('active', 'inactive') DEFAULT 'active' COMMENT '状态',
    `auto_reply_enabled` BOOLEAN DEFAULT TRUE COMMENT '是否启用自动回复',
    `transfer_threshold` INT DEFAULT 3 COMMENT '转人工阈值',
    `working_hours_start` TIME DEFAULT '09:00:00' COMMENT '工作时间开始',
    `working_hours_end` TIME DEFAULT '18:00:00' COMMENT '工作时间结束',
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='智能客服配置表';

-- =====================================================
-- 插入初始数据
-- =====================================================

-- 插入客服系统用户
INSERT IGNORE INTO `customer_service_users` (`employee_id`, `name`, `password`, `department`, `team`, `role`, `status`) VALUES
('12001', '姚家荣', 'Rong199312', '总经办', NULL, 'super_admin', 'active'),
('1207001', '赖武浩', '12345666', '综合在线客服部', '在线客服一组', 'customer_service', 'active');

-- 插入系统设置
INSERT IGNORE INTO `customer_service_settings` (`setting_key`, `setting_value`, `setting_type`, `description`, `category`) VALUES
('system_name', '趣玩星球客服系统', 'string', '系统名称', 'system'),
('max_concurrent_sessions', '10', 'number', '每个客服最大并发会话数', 'performance'),
('auto_assign_enabled', 'true', 'boolean', '是否启用自动分配', 'assignment'),
('session_timeout', '1800', 'number', '会话超时时间(秒)', 'session'),
('file_upload_max_size', '10485760', 'number', '文件上传最大大小(字节)', 'upload'),
('allowed_file_types', '["jpg","jpeg","png","gif","pdf","doc","docx","txt"]', 'json', '允许的文件类型', 'upload');

-- 插入智能客服配置
INSERT IGNORE INTO `customer_service_bot_config` (`name`, `welcome_message`, `default_reply`, `status`) VALUES
('趣玩小助手', '您好！欢迎来到趣玩星球，我是您的专属客服小助手，有什么可以帮助您的吗？', '抱歉，我暂时无法理解您的问题，正在为您转接人工客服，请稍候...', 'active');

SET FOREIGN_KEY_CHECKS = 1;
