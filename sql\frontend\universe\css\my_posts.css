/* 我的作品页面样式 */
.content-container {
    padding: 60px 15px 20px;
}

/* 内容卡片 */
.content-card {
    background-color: white;
    border-radius: 12px;
    margin-bottom: 15px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    overflow: hidden;
}

.card-content {
    padding: 15px;
    display: block;
}

.post-title {
    font-size: 17px;
    font-weight: 600;
    margin-bottom: 10px;
    color: #333;
    line-height: 1.4;
}

.post-cover {
    margin-bottom: 10px;
    border-radius: 8px;
    overflow: hidden;
    height: 180px;
}

.post-cover img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.post-excerpt {
    font-size: 14px;
    color: #666;
    line-height: 1.6;
    margin-bottom: 10px;
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
}

.card-footer {
    padding: 10px 15px;
    border-top: 1px solid #f0f0f0;
}

.post-meta {
    display: flex;
    justify-content: space-between;
    font-size: 12px;
    color: #999;
    margin-bottom: 10px;
}

.action-buttons {
    display: flex;
    justify-content: space-between;
}

.action-button {
    display: flex;
    align-items: center;
    color: #999;
    font-size: 14px;
}

.action-button i {
    margin-right: 5px;
}

.delete-button {
    color: #ff4d4f;
    cursor: pointer;
}

/* 空状态 */
.empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 50px 20px;
    text-align: center;
}

.empty-state i {
    font-size: 48px;
    color: #ccc;
    margin-bottom: 15px;
}

.empty-state p {
    font-size: 16px;
    color: #999;
    margin-bottom: 20px;
}

.btn {
    display: inline-block;
    padding: 10px 20px;
    background-color: #1E90FF;
    color: white;
    border-radius: 5px;
    font-weight: 500;
}

/* 删除确认弹窗 */
.delete-modal {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    display: none;
}

.modal-content {
    background-color: white;
    border-radius: 12px;
    padding: 20px;
    width: 80%;
    max-width: 320px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.modal-content h3 {
    font-size: 18px;
    font-weight: 600;
    margin-bottom: 10px;
    color: #333;
    text-align: center;
}

.modal-content p {
    font-size: 15px;
    color: #666;
    margin-bottom: 20px;
    text-align: center;
    line-height: 1.5;
}

.modal-buttons {
    display: flex;
    justify-content: space-between;
}

.modal-buttons button {
    flex: 1;
    padding: 10px 0;
    border: none;
    border-radius: 5px;
    font-size: 15px;
    font-weight: 500;
    cursor: pointer;
}

.cancel-button {
    background-color: #f0f0f0;
    color: #666;
    margin-right: 10px;
}

.confirm-button {
    background-color: #ff4d4f;
    color: white;
}
