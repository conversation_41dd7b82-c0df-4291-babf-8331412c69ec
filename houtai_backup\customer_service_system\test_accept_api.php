<?php
// 测试接受会话API
header('Content-Type: text/html; charset=UTF-8');
ini_set('default_charset', 'UTF-8');

session_start();

// 检查登录状态
if (!isset($_SESSION['cs_logged_in']) || !$_SESSION['cs_logged_in']) {
    echo '<h1>请先登录客服系统</h1>';
    echo '<a href="../login.php">点击登录</a>';
    exit;
}

echo '<h1>测试接受会话API</h1>';
echo '<h2>当前Session信息：</h2>';
echo '<pre>';
print_r($_SESSION);
echo '</pre>';

// 测试API调用
if ($_POST['test_api'] ?? false) {
    $sessionId = $_POST['session_id'] ?? '';
    
    if ($sessionId) {
        echo '<h2>API调用结果：</h2>';
        
        // 使用cURL调用API
        $url = 'http://' . $_SERVER['HTTP_HOST'] . dirname($_SERVER['REQUEST_URI']) . '/api/accept_session.php';
        $data = json_encode(['sessionId' => $sessionId]);
        
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $data);
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            'Content-Type: application/json',
            'Cookie: ' . $_SERVER['HTTP_COOKIE']
        ]);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);
        
        echo '<p><strong>HTTP状态码：</strong>' . $httpCode . '</p>';
        echo '<p><strong>响应内容：</strong></p>';
        echo '<pre>' . htmlspecialchars($response) . '</pre>';
    }
}
?>

<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>测试接受会话API</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        pre { background: #f5f5f5; padding: 10px; border-radius: 5px; }
        form { margin: 20px 0; }
        input, button { padding: 8px; margin: 5px; }
        button { background: #007cba; color: white; border: none; border-radius: 3px; cursor: pointer; }
    </style>
</head>
<body>
    <form method="POST">
        <h2>测试API调用：</h2>
        <label>会话ID：</label>
        <input type="text" name="session_id" value="session_test_waiting_001" placeholder="输入会话ID">
        <button type="submit" name="test_api" value="1">测试接受会话</button>
    </form>
    
    <h2>可用的测试会话ID：</h2>
    <ul>
        <li>session_test_waiting_001</li>
        <li>session_test_waiting_002</li>
        <li>session_test_waiting_003</li>
    </ul>
    
    <h2>直接JavaScript测试：</h2>
    <button onclick="testAPI()">JavaScript测试</button>
    <div id="result"></div>
    
    <script>
        async function testAPI() {
            const sessionId = 'session_test_waiting_001';
            const resultDiv = document.getElementById('result');
            
            try {
                const response = await fetch('api/accept_session.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        sessionId: sessionId
                    })
                });
                
                const data = await response.json();
                
                resultDiv.innerHTML = '<h3>JavaScript测试结果：</h3><pre>' + JSON.stringify(data, null, 2) + '</pre>';
            } catch (error) {
                resultDiv.innerHTML = '<h3>JavaScript测试错误：</h3><pre>' + error.message + '</pre>';
            }
        }
    </script>
</body>
</html>
