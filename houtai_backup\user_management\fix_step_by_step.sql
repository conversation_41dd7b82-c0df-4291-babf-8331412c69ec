-- =====================================================
-- 分步执行的修复脚本
-- 请逐条执行以下SQL语句
-- =====================================================

-- 第一步：为 admin_logs 表添加 employee_id 字段
ALTER TABLE admin_logs ADD COLUMN employee_id VARCHAR(50) DEFAULT NULL COMMENT '员工工号';

-- 第二步：为 admin_logs 表添加 department 字段
ALTER TABLE admin_logs ADD COLUMN department VARCHAR(100) DEFAULT NULL COMMENT '部门名称';

-- 第三步：创建 user_logs 表
CREATE TABLE IF NOT EXISTS user_logs (
    id INT(11) NOT NULL AUTO_INCREMENT COMMENT '日志ID',
    user_id INT(11) NOT NULL COMMENT '用户ID',
    operator_name VARCHAR(100) NOT NULL COMMENT '操作员姓名',
    employee_id VARCHAR(50) DEFAULT NULL COMMENT '员工工号',
    department VARCHAR(100) DEFAULT NULL COMMENT '部门名称',
    type VARCHAR(50) NOT NULL COMMENT '日志类型',
    content TEXT NOT NULL COMMENT '日志内容',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    PRIMARY KEY (id),
    KEY idx_user_id (user_id),
    KEY idx_type (type),
    KEY idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户管理日志表';

-- 第四步：为 admin_users 表添加 employee_id 字段
ALTER TABLE admin_users ADD COLUMN employee_id VARCHAR(50) DEFAULT NULL COMMENT '员工工号';

-- 第五步：为 admin_users 表添加 department 字段
ALTER TABLE admin_users ADD COLUMN department VARCHAR(100) DEFAULT NULL COMMENT '所属部门';

-- 第六步：为 admin_logs 表添加索引
ALTER TABLE admin_logs ADD INDEX idx_employee_id (employee_id);

-- 第七步：更新管理员员工信息
UPDATE admin_users SET 
    employee_id = CONCAT('EMP', LPAD(id, 3, '0')),
    department = '管理部门'
WHERE employee_id IS NULL OR employee_id = '';

-- 完成提示
SELECT 'All steps completed successfully!' as result;
