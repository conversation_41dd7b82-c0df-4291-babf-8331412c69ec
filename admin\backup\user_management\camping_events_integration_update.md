# 露营活动集成更新文档

## 📅 更新日期
**日期**: 2025年1月27日  
**目的**: 将用户详情页面的组局信息部分更新为显示露营活动数据

## 🔄 更新内容

### 1. 数据库查询更新
**文件**: `admin/backup/user_management/detail.php` 和 `admin_new/user_management/detail.php`

**更改内容**:
- 将组局信息查询从 `user_events` 表更改为 `camping_events` 表
- 更新查询条件从 `user_id` 更改为 `organizer_id`
- 添加注释说明这是露营活动数据

**代码变更**:
```php
// 旧代码
$stmt = $pdo->prepare("
    SELECT * FROM user_events
    WHERE user_id = ?
    ORDER BY created_at DESC
    LIMIT 10
");

// 新代码
$stmt = $pdo->prepare("
    SELECT * FROM camping_events
    WHERE organizer_id = ?
    ORDER BY created_at DESC
    LIMIT 10
");
```

### 2. 显示字段更新
**文件**: `admin/backup/user_management/detail.php` 和 `admin_new/user_management/detail.php`

**更改内容**:
- 修复时间字段从 `event_time` 更改为 `start_time`
- 添加露营活动类别显示
- 添加价格信息显示
- 更新状态标签以匹配露营活动状态

**新增显示字段**:
- 活动类别（山地露营、湖边露营、森林露营、海边露营）
- 活动价格（¥格式显示）
- 更完整的状态标签（草稿、招募中、已满员、进行中、已完成、已取消）

### 3. CSS样式更新
**文件**: `admin/backup/assets/css/users.css` 和 `admin_new/assets/css/users.css`

**新增样式**:
- `.event-category`: 活动类别标签样式
- `.event-details`: 活动详情布局样式
- `.badge.primary` 和 `.badge.info`: 新的状态徽章颜色
- 响应式设计支持

## 🎯 功能特性

### 露营活动显示特性
1. **活动类别标签**: 显示露营类型（山地、湖边、森林、海边）
2. **完整活动信息**: 时间、地点、参与人数、价格
3. **状态管理**: 支持6种活动状态的显示
4. **响应式设计**: 移动端友好的布局

### 数据兼容性
- 自动处理缺失字段
- 向后兼容原有数据结构
- 错误处理和默认值设置

## 📊 数据库字段映射

| 显示项目 | 数据库字段 | 说明 |
|---------|-----------|------|
| 活动标题 | `title` | 露营活动名称 |
| 活动类别 | `category` | mountain/lake/forest/beach |
| 开始时间 | `start_time` | 活动开始时间 |
| 活动地点 | `location` | 露营地点 |
| 参与人数 | `current_participants`/`max_participants` | 当前/最大参与人数 |
| 活动价格 | `price` | 活动费用 |
| 活动状态 | `status` | draft/recruiting/full/ongoing/completed/cancelled |

## 🔧 技术实现

### 状态映射
```php
$status_badges = [
    'draft' => '<span class="badge secondary">草稿</span>',
    'recruiting' => '<span class="badge primary">招募中</span>',
    'full' => '<span class="badge warning">已满员</span>',
    'ongoing' => '<span class="badge info">进行中</span>',
    'completed' => '<span class="badge success">已完成</span>',
    'cancelled' => '<span class="badge danger">已取消</span>'
];
```

### 类别映射
```php
$category_names = [
    'mountain' => '山地露营',
    'lake' => '湖边露营',
    'forest' => '森林露营',
    'beach' => '海边露营'
];
```

## ✅ 测试建议

1. **数据显示测试**: 确认露营活动数据正确显示
2. **状态测试**: 验证各种活动状态的正确显示
3. **响应式测试**: 检查移动端显示效果
4. **兼容性测试**: 确认与现有系统的兼容性

## 📝 注意事项

1. 确保 `camping_events` 表已正确创建并包含所需字段
2. 检查数据库连接和权限设置
3. 验证CSS文件正确加载
4. 测试不同用户角色的访问权限

## 🔄 后续优化建议

1. 添加活动详情页面链接
2. 实现活动状态的实时更新
3. 添加活动参与者列表显示
4. 集成活动管理功能
