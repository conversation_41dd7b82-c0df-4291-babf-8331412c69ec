<?php
/**
 * 权限管理 - 角色管理页面
 * 趣玩星球管理后台
 */

// 临时移除session检查，用于测试
// session_start();

// 登录检查 - 临时注释，允许直接访问测试
// if (!isset($_SESSION['admin_id'])) {
//     header('Location: ../login.php');
//     exit;
// }

$admin_name = $_SESSION['admin_name'] ?? '测试管理员';
$admin_id = $_SESSION['admin_id'] ?? '1';

// 模拟角色数据
$roles = [
    [
        'id' => 1,
        'name' => '超级管理员',
        'code' => 'super_admin',
        'description' => '拥有系统所有权限，可以管理所有功能模块',
        'level' => 10,
        'user_count' => 2,
        'is_system' => true,
        'created_at' => '2024-01-01 00:00:00'
    ],
    [
        'id' => 2,
        'name' => '部门管理员',
        'code' => 'dept_admin',
        'description' => '管理本部门员工和基础功能权限',
        'level' => 8,
        'user_count' => 5,
        'is_system' => true,
        'created_at' => '2024-01-01 00:00:00'
    ],
    [
        'id' => 3,
        'name' => '审核专员',
        'code' => 'reviewer',
        'description' => '负责用户认证审核和内容审核工作',
        'level' => 6,
        'user_count' => 8,
        'is_system' => false,
        'created_at' => '2024-01-15 10:30:00'
    ],
    [
        'id' => 4,
        'name' => '客服专员',
        'code' => 'customer_service',
        'description' => '处理用户咨询和客户服务相关工作',
        'level' => 4,
        'user_count' => 12,
        'is_system' => false,
        'created_at' => '2024-02-01 14:20:00'
    ],
    [
        'id' => 5,
        'name' => '普通员工',
        'code' => 'employee',
        'description' => '基础员工权限，可查看基本信息',
        'level' => 2,
        'user_count' => 25,
        'is_system' => true,
        'created_at' => '2024-01-01 00:00:00'
    ]
];

// 模拟权限数据
$permissions = [
    ['id' => 1, 'name' => '用户管理', 'code' => 'user.manage', 'module' => 'user'],
    ['id' => 2, 'name' => '用户查看', 'code' => 'user.view', 'module' => 'user'],
    ['id' => 3, 'name' => '认证审核', 'code' => 'verification.review', 'module' => 'verification'],
    ['id' => 4, 'name' => '数据统计', 'code' => 'stats.view', 'module' => 'stats'],
    ['id' => 5, 'name' => '系统设置', 'code' => 'system.config', 'module' => 'system'],
    ['id' => 6, 'name' => '权限管理', 'code' => 'permission.manage', 'module' => 'permission'],
    ['id' => 7, 'name' => '部门管理', 'code' => 'department.manage', 'module' => 'department'],
    ['id' => 8, 'name' => '角色管理', 'code' => 'role.manage', 'module' => 'role']
];
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>角色管理 - 趣玩星球管理后台</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="../assets/css/admin-layout.css">
    <style>
        /* 角色管理页面专用样式 */
        .roles-content {
            padding: 24px;
        }

        .page-actions {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 32px;
        }

        .add-role-btn {
            background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: var(--border-radius);
            font-weight: 600;
            cursor: pointer;
            transition: var(--transition);
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .add-role-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(64, 224, 208, 0.3);
        }

        .roles-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
            gap: 24px;
        }

        .role-card {
            background: white;
            border-radius: 16px;
            padding: 24px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
            border: 2px solid transparent;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .role-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, var(--primary-color), var(--primary-light));
        }

        .role-card:hover {
            border-color: var(--primary-color);
            transform: translateY(-4px);
            box-shadow: 0 12px 32px rgba(0, 0, 0, 0.15);
        }

        .role-card.system-role::before {
            background: linear-gradient(90deg, var(--warning-color), #FBBF24);
        }

        .role-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 16px;
        }

        .role-info h3 {
            font-size: 1.25rem;
            font-weight: 700;
            color: var(--gray-800);
            margin-bottom: 4px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .role-code {
            font-size: 0.75rem;
            color: var(--gray-500);
            background: var(--gray-100);
            padding: 2px 8px;
            border-radius: 12px;
            font-family: monospace;
        }

        .role-badges {
            display: flex;
            flex-direction: column;
            gap: 4px;
            align-items: flex-end;
        }

        .role-level {
            background: linear-gradient(135deg, var(--info-color), #60A5FA);
            color: white;
            font-size: 0.75rem;
            font-weight: 600;
            padding: 4px 8px;
            border-radius: 12px;
        }

        .system-badge {
            background: linear-gradient(135deg, var(--warning-color), #FBBF24);
            color: white;
            font-size: 0.75rem;
            font-weight: 600;
            padding: 4px 8px;
            border-radius: 12px;
        }

        .role-description {
            color: var(--gray-600);
            font-size: 0.875rem;
            line-height: 1.5;
            margin-bottom: 20px;
        }

        .role-stats {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding: 12px;
            background: var(--gray-50);
            border-radius: var(--border-radius);
        }

        .stat-item {
            text-align: center;
        }

        .stat-number {
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--primary-color);
        }

        .stat-label {
            font-size: 0.75rem;
            color: var(--gray-500);
        }

        .role-actions {
            display: flex;
            gap: 8px;
        }

        .action-btn {
            flex: 1;
            padding: 8px 12px;
            border: none;
            border-radius: var(--border-radius);
            font-size: 0.875rem;
            font-weight: 600;
            cursor: pointer;
            transition: var(--transition);
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 6px;
        }

        .btn-edit {
            background: var(--info-color);
            color: white;
        }

        .btn-edit:hover {
            background: #2563EB;
            transform: translateY(-1px);
        }

        .btn-permissions {
            background: var(--secondary-color);
            color: white;
        }

        .btn-permissions:hover {
            background: #5B21B6;
            transform: translateY(-1px);
        }

        .btn-delete {
            background: var(--error-color);
            color: white;
        }

        .btn-delete:hover {
            background: #DC2626;
            transform: translateY(-1px);
        }

        .btn-delete:disabled {
            background: var(--gray-300);
            cursor: not-allowed;
            transform: none;
        }

        /* 模态框样式 */
        .modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            display: none;
            align-items: center;
            justify-content: center;
            z-index: 1000;
            backdrop-filter: blur(5px);
        }

        .modal.show {
            display: flex;
        }

        .modal-content {
            background: white;
            border-radius: 16px;
            padding: 24px;
            max-width: 600px;
            width: 90%;
            max-height: 80vh;
            overflow-y: auto;
        }

        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 24px;
            padding-bottom: 16px;
            border-bottom: 2px solid var(--gray-100);
        }

        .modal-title {
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--gray-800);
        }

        .modal-close {
            background: none;
            border: none;
            font-size: 24px;
            color: var(--gray-400);
            cursor: pointer;
            transition: var(--transition);
        }

        .modal-close:hover {
            color: var(--gray-600);
        }

        .form-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-bottom: 20px;
        }

        .form-group {
            display: flex;
            flex-direction: column;
            gap: 8px;
        }

        .form-group.full-width {
            grid-column: 1 / -1;
        }

        .form-label {
            font-weight: 600;
            color: var(--gray-700);
            font-size: 0.875rem;
        }

        .form-input,
        .form-select,
        .form-textarea {
            padding: 12px 16px;
            border: 2px solid var(--gray-200);
            border-radius: var(--border-radius);
            font-size: 0.875rem;
            transition: var(--transition);
        }

        .form-input:focus,
        .form-select:focus,
        .form-textarea:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(64, 224, 208, 0.1);
        }

        .form-textarea {
            resize: vertical;
            min-height: 80px;
        }

        .permissions-section {
            margin-top: 24px;
        }

        .permissions-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 12px;
            margin-top: 16px;
        }

        .permission-item {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 8px 12px;
            background: var(--gray-50);
            border-radius: var(--border-radius);
            transition: var(--transition);
        }

        .permission-item:hover {
            background: var(--gray-100);
        }

        .permission-checkbox {
            width: 16px;
            height: 16px;
        }

        .permission-label {
            font-size: 0.875rem;
            color: var(--gray-700);
            cursor: pointer;
        }

        .modal-actions {
            display: flex;
            gap: 12px;
            justify-content: flex-end;
            margin-top: 24px;
            padding-top: 16px;
            border-top: 2px solid var(--gray-100);
        }

        .btn-cancel {
            background: var(--gray-200);
            color: var(--gray-700);
        }

        .btn-cancel:hover {
            background: var(--gray-300);
        }

        .btn-save {
            background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
            color: white;
        }

        .btn-save:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(64, 224, 208, 0.3);
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .roles-content {
                padding: 16px;
            }

            .roles-grid {
                grid-template-columns: 1fr;
            }

            .page-actions {
                flex-direction: column;
                gap: 16px;
                align-items: stretch;
            }

            .form-grid {
                grid-template-columns: 1fr;
            }

            .role-actions {
                flex-direction: column;
            }
        }
    </style>
</head>
<body>
    <div class="admin-layout">
        <!-- 左侧菜单 -->
        <?php include '../includes/sidebar.php'; ?>

        <!-- 主内容区域 -->
        <main class="main-content">
            <!-- 顶部导航栏 -->
            <?php include '../includes/topbar.php'; ?>

            <div class="roles-content">
                <!-- 页面标题和操作 -->
                <div class="page-actions">
                    <div>
                        <h1 class="page-title">
                            <i class="fas fa-users-cog" style="color: var(--primary-color);"></i>
                            角色管理
                        </h1>
                        <p class="page-subtitle">管理系统角色和权限分配</p>
                    </div>
                    <button class="add-role-btn" onclick="openRoleModal()">
                        <i class="fas fa-plus"></i>
                        新建角色
                    </button>
                </div>

                <!-- 角色列表 -->
                <div class="roles-grid">
                    <?php foreach ($roles as $role): ?>
                        <div class="role-card <?php echo $role['is_system'] ? 'system-role' : ''; ?>">
                            <div class="role-header">
                                <div class="role-info">
                                    <h3>
                                        <?php echo htmlspecialchars($role['name']); ?>
                                        <?php if ($role['is_system']): ?>
                                            <i class="fas fa-shield-alt" style="color: var(--warning-color); font-size: 0.875rem;" title="系统角色"></i>
                                        <?php endif; ?>
                                    </h3>
                                    <div class="role-code"><?php echo htmlspecialchars($role['code']); ?></div>
                                </div>
                                <div class="role-badges">
                                    <div class="role-level">等级 <?php echo $role['level']; ?></div>
                                    <?php if ($role['is_system']): ?>
                                        <div class="system-badge">系统角色</div>
                                    <?php endif; ?>
                                </div>
                            </div>

                            <div class="role-description">
                                <?php echo htmlspecialchars($role['description']); ?>
                            </div>

                            <div class="role-stats">
                                <div class="stat-item">
                                    <div class="stat-number"><?php echo $role['user_count']; ?></div>
                                    <div class="stat-label">用户数</div>
                                </div>
                                <div class="stat-item">
                                    <div class="stat-number"><?php echo rand(5, 15); ?></div>
                                    <div class="stat-label">权限数</div>
                                </div>
                                <div class="stat-item">
                                    <div class="stat-number"><?php echo date('m-d', strtotime($role['created_at'])); ?></div>
                                    <div class="stat-label">创建时间</div>
                                </div>
                            </div>

                            <div class="role-actions">
                                <button class="action-btn btn-edit" onclick="editRole(<?php echo $role['id']; ?>)">
                                    <i class="fas fa-edit"></i>
                                    编辑
                                </button>
                                <button class="action-btn btn-permissions" onclick="managePermissions(<?php echo $role['id']; ?>)">
                                    <i class="fas fa-key"></i>
                                    权限
                                </button>
                                <button class="action-btn btn-delete"
                                        onclick="deleteRole(<?php echo $role['id']; ?>)"
                                        <?php echo $role['is_system'] ? 'disabled title="系统角色不可删除"' : ''; ?>>
                                    <i class="fas fa-trash"></i>
                                    删除
                                </button>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            </div>
        </main>
    </div>

    <!-- 角色编辑模态框 -->
    <div class="modal" id="roleModal">
        <div class="modal-content">
            <div class="modal-header">
                <div class="modal-title" id="modalTitle">新建角色</div>
                <button class="modal-close" onclick="closeRoleModal()">&times;</button>
            </div>
            <form id="roleForm">
                <div class="form-grid">
                    <div class="form-group">
                        <label class="form-label">角色名称</label>
                        <input type="text" class="form-input" name="name" placeholder="请输入角色名称" required>
                    </div>
                    <div class="form-group">
                        <label class="form-label">角色代码</label>
                        <input type="text" class="form-input" name="code" placeholder="role_code" required>
                    </div>
                    <div class="form-group">
                        <label class="form-label">角色等级</label>
                        <select class="form-select" name="level" required>
                            <option value="">选择等级</option>
                            <option value="2">2 - 普通员工</option>
                            <option value="4">4 - 专员</option>
                            <option value="6">6 - 主管</option>
                            <option value="8">8 - 管理员</option>
                            <option value="10">10 - 超级管理员</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label class="form-label">角色类型</label>
                        <select class="form-select" name="is_system">
                            <option value="0">自定义角色</option>
                            <option value="1">系统角色</option>
                        </select>
                    </div>
                    <div class="form-group full-width">
                        <label class="form-label">角色描述</label>
                        <textarea class="form-textarea" name="description" placeholder="请输入角色描述"></textarea>
                    </div>
                </div>

                <div class="permissions-section">
                    <label class="form-label">角色权限</label>
                    <div class="permissions-grid">
                        <?php foreach ($permissions as $permission): ?>
                            <div class="permission-item">
                                <input type="checkbox" class="permission-checkbox"
                                       name="permissions[]" value="<?php echo $permission['id']; ?>"
                                       id="perm_<?php echo $permission['id']; ?>">
                                <label class="permission-label" for="perm_<?php echo $permission['id']; ?>">
                                    <?php echo htmlspecialchars($permission['name']); ?>
                                </label>
                            </div>
                        <?php endforeach; ?>
                    </div>
                </div>

                <div class="modal-actions">
                    <button type="button" class="action-btn btn-cancel" onclick="closeRoleModal()">取消</button>
                    <button type="submit" class="action-btn btn-save">保存角色</button>
                </div>
            </form>
        </div>
    </div>

    <script src="../assets/js/admin-layout.js"></script>
    <script>
        // 角色管理页面脚本
        document.addEventListener('DOMContentLoaded', function() {
            // 更新页面标题
            updatePageTitle('角色管理');

            // 动画效果
            const cards = document.querySelectorAll('.role-card');
            cards.forEach((card, index) => {
                card.style.opacity = '0';
                card.style.transform = 'translateY(20px)';
                setTimeout(() => {
                    card.style.transition = 'all 0.5s ease';
                    card.style.opacity = '1';
                    card.style.transform = 'translateY(0)';
                }, index * 100);
            });

            // 表单提交处理
            document.getElementById('roleForm').addEventListener('submit', function(e) {
                e.preventDefault();

                const formData = new FormData(this);
                const roleName = formData.get('name');

                // 模拟保存
                alert(`角色 "${roleName}" 已保存成功！`);
                closeRoleModal();

                // 刷新页面
                setTimeout(() => {
                    location.reload();
                }, 1000);
            });
        });

        function openRoleModal(roleId = null) {
            if (roleId) {
                document.getElementById('modalTitle').textContent = '编辑角色';
                // 这里可以加载角色数据填充表单
            } else {
                document.getElementById('modalTitle').textContent = '新建角色';
                document.getElementById('roleForm').reset();
            }
            document.getElementById('roleModal').classList.add('show');
        }

        function closeRoleModal() {
            document.getElementById('roleModal').classList.remove('show');
            document.getElementById('roleForm').reset();
        }

        function editRole(roleId) {
            openRoleModal(roleId);
        }

        function managePermissions(roleId) {
            alert(`管理角色权限功能开发中...\n\n将打开权限配置页面，可以详细设置角色的具体权限。`);
        }

        function deleteRole(roleId) {
            if (confirm('确定要删除这个角色吗？\n\n删除后，拥有此角色的用户将失去相应权限。')) {
                alert('角色删除成功！');
                // 这里添加删除逻辑
                setTimeout(() => {
                    location.reload();
                }, 1000);
            }
        }

        // 点击模态框外部关闭
        document.getElementById('roleModal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeRoleModal();
            }
        });

        console.log('👥 角色管理页面已加载');
    </script>
</body>
</html>
