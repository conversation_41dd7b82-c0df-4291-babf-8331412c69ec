// 发布组局页面JavaScript

let selectedImages = [];
let selectedTags = [];
let currentLocation = null;

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    initializeEventListeners();
    updateCharCounts();
    setMinDateTime();
});

// 初始化事件监听器
function initializeEventListeners() {
    // 图片选择
    document.getElementById('imageInput').addEventListener('change', handleImageSelect);
    
    // 字符计数
    document.getElementById('title').addEventListener('input', updateCharCounts);
    document.getElementById('description').addEventListener('input', updateCharCounts);
    document.getElementById('requirements').addEventListener('input', updateCharCounts);
    
    // 标签输入
    document.getElementById('tagsInput').addEventListener('keypress', handleTagInput);
    
    // 时间验证
    document.getElementById('startTime').addEventListener('change', validateDateTime);
    document.getElementById('endTime').addEventListener('change', validateDateTime);
}

// 设置最小日期时间
function setMinDateTime() {
    const now = new Date();
    const minDateTime = new Date(now.getTime() + 60 * 60 * 1000); // 至少1小时后
    const minString = minDateTime.toISOString().slice(0, 16);
    
    document.getElementById('startTime').min = minString;
    document.getElementById('endTime').min = minString;
}

// 验证日期时间
function validateDateTime() {
    const startTime = document.getElementById('startTime').value;
    const endTime = document.getElementById('endTime').value;
    
    if (startTime && endTime) {
        const start = new Date(startTime);
        const end = new Date(endTime);
        
        if (end <= start) {
            showToast('结束时间必须晚于开始时间');
            document.getElementById('endTime').value = '';
        }
    }
}

// 选择图片
function selectImages() {
    if (selectedImages.length >= 3) {
        showToast('最多只能上传3张图片');
        return;
    }
    document.getElementById('imageInput').click();
}

// 处理图片选择
function handleImageSelect(event) {
    const files = Array.from(event.target.files);
    
    if (selectedImages.length + files.length > 3) {
        showToast('最多只能上传3张图片');
        return;
    }
    
    files.forEach(file => {
        if (file.type.startsWith('image/')) {
            if (file.size > 10 * 1024 * 1024) { // 10MB限制
                showToast('图片大小不能超过10MB');
                return;
            }
            
            const reader = new FileReader();
            reader.onload = function(e) {
                selectedImages.push({
                    file: file,
                    url: e.target.result
                });
                updateImageGrid();
            };
            reader.readAsDataURL(file);
        }
    });
    
    // 清空input
    event.target.value = '';
}

// 更新图片网格
function updateImageGrid() {
    const grid = document.getElementById('uploadGrid');
    grid.innerHTML = '';
    
    // 添加已选择的图片
    selectedImages.forEach((image, index) => {
        const item = document.createElement('div');
        item.className = 'upload-item image-preview';
        item.style.backgroundImage = `url(${image.url})`;
        
        const removeBtn = document.createElement('button');
        removeBtn.className = 'remove-btn';
        removeBtn.innerHTML = '<i class="fas fa-times"></i>';
        removeBtn.onclick = () => removeImage(index);
        
        item.appendChild(removeBtn);
        grid.appendChild(item);
    });
    
    // 添加上传按钮（如果还没达到上限）
    if (selectedImages.length < 3) {
        const addItem = document.createElement('div');
        addItem.className = 'upload-item add-photo';
        addItem.onclick = selectImages;
        addItem.innerHTML = `
            <i class="fas fa-plus"></i>
            <span>添加图片</span>
            <small>${selectedImages.length}/3</small>
        `;
        grid.appendChild(addItem);
    }
}

// 移除图片
function removeImage(index) {
    selectedImages.splice(index, 1);
    updateImageGrid();
}

// 更新字符计数
function updateCharCounts() {
    const fields = [
        { id: 'title', max: 50 },
        { id: 'description', max: 500 },
        { id: 'requirements', max: 200 }
    ];
    
    fields.forEach(field => {
        const element = document.getElementById(field.id);
        if (element) {
            const countElement = element.parentElement.querySelector('.char-count');
            if (countElement) {
                countElement.textContent = `${element.value.length}/${field.max}`;
            }
        }
    });
}

// 处理标签输入
function handleTagInput(event) {
    if (event.key === 'Enter') {
        event.preventDefault();
        addTag();
    }
}

// 添加标签
function addTag() {
    const input = document.getElementById('tagsInput');
    const tag = input.value.trim();
    
    if (!tag) return;
    
    if (selectedTags.length >= 5) {
        showToast('最多只能添加5个标签');
        return;
    }
    
    if (selectedTags.includes(tag)) {
        showToast('标签已存在');
        return;
    }
    
    if (tag.length > 20) {
        showToast('标签长度不能超过20个字符');
        return;
    }
    
    selectedTags.push(tag);
    input.value = '';
    updateTagsDisplay();
}

// 更新标签显示
function updateTagsDisplay() {
    const display = document.getElementById('tagsDisplay');
    display.innerHTML = '';
    
    selectedTags.forEach((tag, index) => {
        const tagItem = document.createElement('div');
        tagItem.className = 'tag-item';
        tagItem.innerHTML = `
            <span>${tag}</span>
            <button class="tag-remove" onclick="removeTag(${index})">
                <i class="fas fa-times"></i>
            </button>
        `;
        display.appendChild(tagItem);
    });
}

// 移除标签
function removeTag(index) {
    selectedTags.splice(index, 1);
    updateTagsDisplay();
}

// 选择位置
function selectLocation() {
    // 这里应该打开地图选择器
    // 暂时使用简单的输入框
    const location = prompt('请输入活动地点：');
    if (location && location.trim()) {
        document.getElementById('location').value = location.trim();
        currentLocation = {
            address: location.trim(),
            latitude: null,
            longitude: null
        };
    }
}

// 改变数字
function changeNumber(fieldId, delta) {
    const field = document.getElementById(fieldId);
    const currentValue = parseInt(field.value) || 0;
    const newValue = Math.max(parseInt(field.min), Math.min(parseInt(field.max), currentValue + delta));
    field.value = newValue;
}

// 发布组局
async function publishActivity() {
    const form = document.getElementById('activityForm');
    const formData = new FormData();
    
    // 验证必填字段
    const title = document.getElementById('title').value.trim();
    const activityType = document.getElementById('activityType').value;
    const description = document.getElementById('description').value.trim();
    const startTime = document.getElementById('startTime').value;
    const location = document.getElementById('location').value.trim();
    
    if (!title) {
        showToast('请输入活动标题');
        return;
    }
    
    if (!activityType) {
        showToast('请选择活动类型');
        return;
    }
    
    if (!description) {
        showToast('请输入活动描述');
        return;
    }
    
    if (!startTime) {
        showToast('请选择开始时间');
        return;
    }
    
    if (!location) {
        showToast('请输入活动地点');
        return;
    }
    
    // 显示加载状态
    showLoading(true);
    
    try {
        // 添加表单数据
        formData.append('title', title);
        formData.append('activity_type', activityType);
        formData.append('description', description);
        formData.append('start_time', startTime);
        formData.append('end_time', document.getElementById('endTime').value);
        formData.append('location', location);
        formData.append('max_participants', document.getElementById('maxParticipants').value);
        formData.append('fee', document.getElementById('fee').value || '0');
        formData.append('contact_info', document.getElementById('contact').value.trim());
        formData.append('requirements', document.getElementById('requirements').value.trim());
        formData.append('tags', JSON.stringify(selectedTags));
        
        if (currentLocation && currentLocation.latitude) {
            formData.append('latitude', currentLocation.latitude);
            formData.append('longitude', currentLocation.longitude);
        }
        
        // 添加图片文件
        selectedImages.forEach((image, index) => {
            formData.append(`images[]`, image.file);
        });
        
        // 发送请求
        const response = await fetch('../api/publish_activity.php', {
            method: 'POST',
            body: formData
        });
        
        const result = await response.json();
        
        if (result.success) {
            showToast('组局发布成功！');
            setTimeout(() => {
                window.location.href = '../home/<USER>';
            }, 1500);
        } else {
            showToast(result.message || '发布失败，请重试');
        }
        
    } catch (error) {
        console.error('发布错误:', error);
        showToast('网络错误，请检查网络连接');
    } finally {
        showLoading(false);
    }
}

// 显示/隐藏加载状态
function showLoading(show) {
    const overlay = document.getElementById('loadingOverlay');
    const publishBtn = document.querySelector('.publish-button');
    
    if (show) {
        overlay.style.display = 'flex';
        publishBtn.disabled = true;
    } else {
        overlay.style.display = 'none';
        publishBtn.disabled = false;
    }
}

// 显示Toast提示
function showToast(message) {
    const toast = document.getElementById('toast');
    toast.textContent = message;
    toast.style.display = 'block';
    
    setTimeout(() => {
        toast.style.display = 'none';
    }, 3000);
}
