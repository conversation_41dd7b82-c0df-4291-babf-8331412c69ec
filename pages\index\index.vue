<template>
  <view class="home-container">
    <!-- 自定义导航栏 -->
    <view class="custom-navbar safe-area-top">
      <view class="navbar-content">
        <view class="navbar-left">
          <image class="logo-small" src="/static/images/logo.png" mode="aspectFit"></image>
          <text class="app-name">趣玩星球</text>
        </view>
        <view class="navbar-right">
          <view class="notification-icon" @click="showNotifications">
            <text class="icon">🔔</text>
            <view v-if="unreadNotifications > 0" class="badge">{{ unreadNotifications }}</view>
          </view>
        </view>
      </view>
    </view>

    <!-- 主要内容 -->
    <scroll-view class="main-content" scroll-y="true" @scrolltolower="loadMore">
      <!-- 欢迎横幅 -->
      <view class="welcome-banner">
        <view class="banner-content">
          <view class="welcome-text">
            <text class="greeting">{{ greeting }}</text>
            <text class="username">{{ userStore.userNickname }}</text>
          </view>
          <view class="user-avatar" @click="goToProfile">
            <image class="avatar-img" :src="userStore.userAvatar" mode="aspectFill"></image>
          </view>
        </view>
        <view class="banner-decoration">
          <view class="decoration-star star-1">⭐</view>
          <view class="decoration-star star-2">✨</view>
          <view class="decoration-star star-3">🌟</view>
        </view>
      </view>

      <!-- 快捷功能 -->
      <view class="quick-actions">
        <view class="section-title">
          <text class="title-text">快捷功能</text>
        </view>
        <view class="actions-grid">
          <view class="action-item" @click="goToCustomerService">
            <view class="action-icon customer-service">
              <text class="icon">💬</text>
            </view>
            <text class="action-text">客服中心</text>
            <view v-if="chatStore.unreadCount > 0" class="action-badge">{{ chatStore.unreadCount }}</view>
          </view>
          
          <view class="action-item" @click="goToProfile">
            <view class="action-icon profile">
              <text class="icon">👤</text>
            </view>
            <text class="action-text">个人中心</text>
          </view>
          
          <view class="action-item" @click="showComingSoon">
            <view class="action-icon community">
              <text class="icon">🌍</text>
            </view>
            <text class="action-text">社区动态</text>
          </view>
          
          <view class="action-item" @click="showComingSoon">
            <view class="action-icon games">
              <text class="icon">🎮</text>
            </view>
            <text class="action-text">趣味游戏</text>
          </view>
        </view>
      </view>

      <!-- 最新动态 -->
      <view class="latest-updates">
        <view class="section-title">
          <text class="title-text">最新动态</text>
          <text class="more-text" @click="showComingSoon">查看更多</text>
        </view>
        
        <view v-if="updates.length === 0" class="empty-state">
          <view class="empty-icon">📝</view>
          <text class="empty-text">暂无动态内容</text>
          <text class="empty-subtitle">敬请期待更多精彩内容</text>
        </view>
        
        <view v-else class="updates-list">
          <view 
            v-for="update in updates" 
            :key="update.id" 
            class="update-item"
            @click="viewUpdate(update)"
          >
            <view class="update-header">
              <image class="update-avatar" :src="update.avatar" mode="aspectFill"></image>
              <view class="update-info">
                <text class="update-author">{{ update.author }}</text>
                <text class="update-time">{{ formatTime(update.created_at) }}</text>
              </view>
            </view>
            <view class="update-content">
              <text class="update-text">{{ update.content }}</text>
              <view v-if="update.images && update.images.length > 0" class="update-images">
                <image 
                  v-for="(img, index) in update.images.slice(0, 3)" 
                  :key="index"
                  class="update-img"
                  :src="img"
                  mode="aspectFill"
                  @click.stop="previewImage(update.images, index)"
                ></image>
              </view>
            </view>
            <view class="update-actions">
              <view class="action-btn" @click.stop="likeUpdate(update)">
                <text class="action-icon">{{ update.liked ? '❤️' : '🤍' }}</text>
                <text class="action-count">{{ update.likes }}</text>
              </view>
              <view class="action-btn" @click.stop="commentUpdate(update)">
                <text class="action-icon">💬</text>
                <text class="action-count">{{ update.comments }}</text>
              </view>
            </view>
          </view>
        </view>
      </view>

      <!-- 加载更多 -->
      <view v-if="isLoading" class="loading-more">
        <view class="loading-star"></view>
        <text class="loading-text">加载中...</text>
      </view>
    </scroll-view>

    <!-- 浮动客服按钮 -->
    <view class="floating-service" @click="goToCustomerService">
      <view class="service-icon">
        <text class="icon">💬</text>
      </view>
      <view v-if="chatStore.unreadCount > 0" class="floating-badge">{{ chatStore.unreadCount }}</view>
    </view>
  </view>
</template>

<script>
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useUserStore } from '@/store/user'
import { useChatStore } from '@/store/chat'

export default {
  name: 'Home',
  setup() {
    const userStore = useUserStore()
    const chatStore = useChatStore()
    
    // 响应式数据
    const updates = ref([])
    const isLoading = ref(false)
    const unreadNotifications = ref(0)
    const currentPage = ref(1)
    const hasMore = ref(true)

    // 计算属性
    const greeting = computed(() => {
      const hour = new Date().getHours()
      if (hour < 6) return '夜深了'
      if (hour < 12) return '早上好'
      if (hour < 18) return '下午好'
      return '晚上好'
    })

    // 方法
    const loadUpdates = async (page = 1) => {
      if (isLoading.value) return

      try {
        isLoading.value = true
        
        // 模拟加载数据
        await new Promise(resolve => setTimeout(resolve, 1000))
        
        // 这里应该调用实际的API
        const mockUpdates = [
          {
            id: 1,
            author: '系统公告',
            avatar: '/static/images/system-avatar.png',
            content: '欢迎来到趣玩星球！这里是一个充满乐趣的社交平台。',
            images: [],
            likes: 128,
            comments: 32,
            liked: false,
            created_at: new Date().toISOString()
          }
        ]
        
        if (page === 1) {
          updates.value = mockUpdates
        } else {
          updates.value.push(...mockUpdates)
        }
        
        hasMore.value = false // 暂时没有更多数据
      } catch (error) {
        console.error('加载动态失败:', error)
        uni.showToast({
          title: '加载失败',
          icon: 'error'
        })
      } finally {
        isLoading.value = false
      }
    }

    const loadMore = () => {
      if (hasMore.value && !isLoading.value) {
        currentPage.value++
        loadUpdates(currentPage.value)
      }
    }

    const goToCustomerService = () => {
      uni.switchTab({
        url: '/pages/customer-service/chat'
      })
    }

    const goToProfile = () => {
      uni.switchTab({
        url: '/pages/profile/profile'
      })
    }

    const showNotifications = () => {
      uni.showToast({
        title: '通知功能开发中',
        icon: 'none'
      })
    }

    const showComingSoon = () => {
      uni.showToast({
        title: '功能开发中，敬请期待',
        icon: 'none'
      })
    }

    const formatTime = (timeStr) => {
      const time = new Date(timeStr)
      const now = new Date()
      const diff = now - time
      
      const minutes = Math.floor(diff / 60000)
      const hours = Math.floor(diff / 3600000)
      const days = Math.floor(diff / 86400000)
      
      if (minutes < 1) return '刚刚'
      if (minutes < 60) return `${minutes}分钟前`
      if (hours < 24) return `${hours}小时前`
      if (days < 7) return `${days}天前`
      
      return time.toLocaleDateString()
    }

    const viewUpdate = (update) => {
      uni.showToast({
        title: '详情页面开发中',
        icon: 'none'
      })
    }

    const likeUpdate = (update) => {
      update.liked = !update.liked
      update.likes += update.liked ? 1 : -1
    }

    const commentUpdate = (update) => {
      uni.showToast({
        title: '评论功能开发中',
        icon: 'none'
      })
    }

    const previewImage = (images, current) => {
      uni.previewImage({
        urls: images,
        current: current
      })
    }

    // 生命周期
    onMounted(() => {
      // 检查登录状态
      if (!userStore.isLoggedIn) {
        uni.reLaunch({
          url: '/pages/login/login'
        })
        return
      }
      
      // 加载首页数据
      loadUpdates()
    })

    return {
      userStore,
      chatStore,
      updates,
      isLoading,
      unreadNotifications,
      greeting,
      loadMore,
      goToCustomerService,
      goToProfile,
      showNotifications,
      showComingSoon,
      formatTime,
      viewUpdate,
      likeUpdate,
      commentUpdate,
      previewImage
    }
  }
}
</script>

<style lang="scss" scoped>
.home-container {
  height: 100vh;
  background: var(--bg-secondary);
}

.custom-navbar {
  background: linear-gradient(135deg, var(--primary-color) 0%, #9B59B6 100%);
  padding-bottom: var(--spacing-sm);
}

.navbar-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 var(--spacing-md);
  height: 44px;
}

.navbar-left {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.logo-small {
  width: 28px;
  height: 28px;
}

.app-name {
  color: white;
  font-size: var(--font-lg);
  font-weight: 600;
}

.navbar-right {
  display: flex;
  align-items: center;
}

.notification-icon {
  position: relative;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: var(--radius-full);
  background: rgba(255, 255, 255, 0.1);
}

.notification-icon .icon {
  font-size: var(--font-lg);
}

.badge {
  position: absolute;
  top: -4px;
  right: -4px;
  min-width: 16px;
  height: 16px;
  background: var(--warning-color);
  color: white;
  font-size: 10px;
  border-radius: var(--radius-full);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 4px;
}

.main-content {
  height: calc(100vh - 44px - env(safe-area-inset-top));
  padding: var(--spacing-md);
}

.welcome-banner {
  background: linear-gradient(135deg, var(--primary-color) 0%, #9B59B6 100%);
  border-radius: var(--radius-xl);
  padding: var(--spacing-lg);
  margin-bottom: var(--spacing-lg);
  position: relative;
  overflow: hidden;
}

.banner-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  position: relative;
  z-index: 1;
}

.welcome-text {
  flex: 1;
}

.greeting {
  display: block;
  color: rgba(255, 255, 255, 0.9);
  font-size: var(--font-md);
  margin-bottom: var(--spacing-xs);
}

.username {
  display: block;
  color: white;
  font-size: var(--font-xl);
  font-weight: 600;
}

.user-avatar {
  width: 60px;
  height: 60px;
  border-radius: var(--radius-full);
  overflow: hidden;
  border: 3px solid rgba(255, 255, 255, 0.3);
}

.avatar-img {
  width: 100%;
  height: 100%;
}

.banner-decoration {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
}

.decoration-star {
  position: absolute;
  font-size: var(--font-lg);
  opacity: 0.6;
  animation: float 3s ease-in-out infinite;
}

.star-1 {
  top: 20%;
  right: 20%;
  animation-delay: 0s;
}

.star-2 {
  bottom: 30%;
  left: 15%;
  animation-delay: 1s;
}

.star-3 {
  top: 60%;
  right: 40%;
  animation-delay: 2s;
}

@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
}

.quick-actions {
  margin-bottom: var(--spacing-lg);
}

.section-title {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: var(--spacing-md);
}

.title-text {
  font-size: var(--font-lg);
  font-weight: 600;
  color: var(--text-primary);
}

.more-text {
  font-size: var(--font-sm);
  color: var(--primary-color);
}

.actions-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: var(--spacing-md);
}

.action-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: var(--spacing-md);
  background: var(--bg-primary);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-sm);
  position: relative;
  transition: transform 0.2s ease;
}

.action-item:active {
  transform: scale(0.95);
}

.action-icon {
  width: 48px;
  height: 48px;
  border-radius: var(--radius-lg);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: var(--spacing-sm);
}

.action-icon.customer-service {
  background: linear-gradient(135deg, #4ECDC4 0%, #44A08D 100%);
}

.action-icon.profile {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.action-icon.community {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.action-icon.games {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.action-icon .icon {
  font-size: var(--font-xl);
}

.action-text {
  font-size: var(--font-sm);
  color: var(--text-primary);
  text-align: center;
}

.action-badge {
  position: absolute;
  top: 8px;
  right: 8px;
  min-width: 18px;
  height: 18px;
  background: var(--warning-color);
  color: white;
  font-size: 10px;
  border-radius: var(--radius-full);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 4px;
}

.latest-updates {
  margin-bottom: var(--spacing-xl);
}

.empty-state {
  text-align: center;
  padding: var(--spacing-xxl) var(--spacing-lg);
  background: var(--bg-primary);
  border-radius: var(--radius-lg);
}

.empty-icon {
  font-size: 48px;
  margin-bottom: var(--spacing-md);
}

.empty-text {
  display: block;
  font-size: var(--font-lg);
  color: var(--text-primary);
  margin-bottom: var(--spacing-sm);
}

.empty-subtitle {
  display: block;
  font-size: var(--font-sm);
  color: var(--text-secondary);
}

.updates-list {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
}

.update-item {
  background: var(--bg-primary);
  border-radius: var(--radius-lg);
  padding: var(--spacing-md);
  box-shadow: var(--shadow-sm);
}

.update-header {
  display: flex;
  align-items: center;
  margin-bottom: var(--spacing-md);
}

.update-avatar {
  width: 40px;
  height: 40px;
  border-radius: var(--radius-full);
  margin-right: var(--spacing-sm);
}

.update-info {
  flex: 1;
}

.update-author {
  display: block;
  font-size: var(--font-md);
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--spacing-xs);
}

.update-time {
  display: block;
  font-size: var(--font-sm);
  color: var(--text-secondary);
}

.update-content {
  margin-bottom: var(--spacing-md);
}

.update-text {
  font-size: var(--font-md);
  color: var(--text-primary);
  line-height: 1.6;
  margin-bottom: var(--spacing-sm);
}

.update-images {
  display: flex;
  gap: var(--spacing-sm);
}

.update-img {
  width: 80px;
  height: 80px;
  border-radius: var(--radius-md);
}

.update-actions {
  display: flex;
  gap: var(--spacing-lg);
}

.action-btn {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
}

.action-count {
  font-size: var(--font-sm);
  color: var(--text-secondary);
}

.loading-more {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-lg);
}

.loading-text {
  color: var(--text-secondary);
  font-size: var(--font-sm);
}

.floating-service {
  position: fixed;
  bottom: calc(var(--spacing-xl) + env(safe-area-inset-bottom));
  right: var(--spacing-lg);
  width: 56px;
  height: 56px;
  background: linear-gradient(135deg, var(--primary-color) 0%, #9B59B6 100%);
  border-radius: var(--radius-full);
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: var(--shadow-lg);
  z-index: 100;
}

.service-icon .icon {
  font-size: var(--font-xl);
  color: white;
}

.floating-badge {
  position: absolute;
  top: -4px;
  right: -4px;
  min-width: 20px;
  height: 20px;
  background: var(--warning-color);
  color: white;
  font-size: 10px;
  border-radius: var(--radius-full);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 4px;
  border: 2px solid white;
}
</style>
