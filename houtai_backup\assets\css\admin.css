/**
 * 趣玩星球管理后台 - 主样式文件
 * 包含侧边栏布局和统一主题样式
 */

/* ===== CSS变量定义 ===== */
:root {
    /* 主题色 */
    --primary-color: #6F7BF5;
    --primary-light: #8B93F7;
    --primary-dark: #5A67D8;

    /* 功能色彩 */
    --success-color: #10B981;
    --warning-color: #F59E0B;
    --error-color: #EF4444;
    --info-color: #3B82F6;

    /* 中性色 */
    --white: #FFFFFF;
    --gray-50: #F9FAFB;
    --gray-100: #F3F4F6;
    --gray-200: #E5E7EB;
    --gray-300: #D1D5DB;
    --gray-400: #9CA3AF;
    --gray-500: #6B7280;
    --gray-600: #4B5563;
    --gray-700: #374151;
    --gray-800: #1F2937;
    --gray-900: #111827;

    /* 侧边栏宽度 */
    --sidebar-width: 280px;
    --sidebar-collapsed-width: 80px;

    /* 阴影 */
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);

    /* 圆角 */
    --radius-sm: 0.375rem;
    --radius-md: 0.5rem;
    --radius-lg: 0.75rem;
    --radius-xl: 1rem;
    --radius-2xl: 1.5rem;

    /* 间距 */
    --spacing-xs: 0.5rem;
    --spacing-sm: 0.75rem;
    --spacing-md: 1rem;
    --spacing-lg: 1.5rem;
    --spacing-xl: 2rem;
    --spacing-2xl: 3rem;
}

/* ===== 基础重置 ===== */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    font-size: 16px;
    line-height: 1.5;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: var(--gray-800);
    min-height: 100vh;
    overflow-x: hidden;
}

/* ===== 滚动条样式 ===== */
::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

::-webkit-scrollbar-track {
    background: var(--gray-100);
    border-radius: var(--radius-md);
}

::-webkit-scrollbar-thumb {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
    border-radius: var(--radius-md);
    transition: all 0.3s ease;
}

::-webkit-scrollbar-thumb:hover {
    background: var(--primary-dark);
}

/* ===== 主容器布局 ===== */
.admin-container {
    display: flex;
    min-height: 100vh;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

/* ===== 侧边栏样式 ===== */
.sidebar {
    width: var(--sidebar-width);
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border-radius: 0 var(--radius-2xl) var(--radius-2xl) 0;
    box-shadow: var(--shadow-xl);
    border-right: 1px solid rgba(255, 255, 255, 0.2);
    position: fixed;
    left: 0;
    top: 0;
    height: 100vh;
    overflow-y: auto;
    z-index: 1000;
    transition: all 0.3s ease;
}

.sidebar-header {
    padding: var(--spacing-xl) var(--spacing-lg);
    border-bottom: 1px solid var(--gray-200);
    background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
    border-radius: 0 var(--radius-2xl) 0 0;
}

.logo {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
}

.logo-icon {
    width: 48px;
    height: 48px;
    border-radius: var(--radius-xl);
    overflow: hidden;
    box-shadow: var(--shadow-md);
}

.logo-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.logo-text h2 {
    color: white;
    font-size: 1.25rem;
    font-weight: 700;
    margin-bottom: 2px;
}

.logo-text p {
    color: rgba(255, 255, 255, 0.8);
    font-size: 0.875rem;
    font-weight: 500;
}

/* ===== 导航菜单 ===== */
.sidebar-nav {
    padding: var(--spacing-lg) 0;
}

.nav-list {
    list-style: none;
}

.nav-item {
    margin-bottom: var(--spacing-xs);
}

.nav-link {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    padding: var(--spacing-md) var(--spacing-lg);
    color: var(--gray-700);
    text-decoration: none;
    font-weight: 500;
    transition: all 0.3s ease;
    position: relative;
    border-radius: 0 var(--radius-xl) var(--radius-xl) 0;
    margin-right: var(--spacing-lg);
}

.nav-link:hover {
    background: linear-gradient(135deg, rgba(111, 123, 245, 0.1), rgba(139, 147, 247, 0.1));
    color: var(--primary-color);
    transform: translateX(4px);
}

.nav-item.active .nav-link {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
    color: white;
    box-shadow: var(--shadow-md);
}

.nav-link i {
    width: 20px;
    text-align: center;
    font-size: 1.125rem;
}

.nav-link-text {
    flex: 1;
    font-size: 0.9375rem;
}

/* ===== 子菜单样式 ===== */
.nav-item.has-submenu .nav-link {
    cursor: pointer;
}

.submenu-arrow {
    transition: transform 0.3s ease;
    font-size: 0.875rem;
}

.nav-item.expanded .submenu-arrow {
    transform: rotate(90deg);
}

.submenu {
    max-height: 0;
    overflow: hidden;
    transition: max-height 0.3s ease;
    background: rgba(111, 123, 245, 0.05);
    margin: 0 var(--spacing-lg) var(--spacing-xs) var(--spacing-lg);
    border-radius: var(--radius-lg);
}

.submenu.show {
    max-height: 500px;
    padding: var(--spacing-sm) 0;
}

.submenu-item {
    list-style: none;
}

.submenu-link {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-sm) var(--spacing-lg);
    color: var(--gray-600);
    text-decoration: none;
    font-size: 0.875rem;
    font-weight: 500;
    transition: all 0.3s ease;
    border-radius: var(--radius-md);
    margin: 0 var(--spacing-sm);
    position: relative;
}

.submenu-link:hover {
    background: rgba(111, 123, 245, 0.1);
    color: var(--primary-color);
    transform: translateX(4px);
}

.submenu-item.active .submenu-link {
    background: var(--primary-color);
    color: white;
    box-shadow: var(--shadow-sm);
}

.submenu-link i {
    width: 16px;
    text-align: center;
    font-size: 0.875rem;
}

/* ===== 徽章样式 ===== */
.nav-badge {
    background: var(--error-color);
    color: white;
    font-size: 0.75rem;
    font-weight: 600;
    padding: 2px 6px;
    border-radius: 10px;
    min-width: 18px;
    text-align: center;
    line-height: 1.2;
}

/* ===== 主内容区域 ===== */
.main-content {
    flex: 1;
    margin-left: var(--sidebar-width);
    padding: var(--spacing-xl);
    background: transparent;
    min-height: 100vh;
}

.content-wrapper {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border-radius: var(--radius-2xl);
    padding: var(--spacing-2xl);
    box-shadow: var(--shadow-xl);
    border: 1px solid rgba(255, 255, 255, 0.2);
    animation: fadeInUp 0.6s ease;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* ===== 内容头部 ===== */
.content-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-xl);
    padding-bottom: var(--spacing-lg);
    border-bottom: 2px solid var(--gray-100);
}

.content-header h1 {
    font-size: 1.875rem;
    font-weight: 700;
    color: var(--gray-800);
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
}

.content-header h1 i {
    color: var(--primary-color);
    font-size: 1.75rem;
}

.header-actions {
    display: flex;
    gap: var(--spacing-md);
}

/* ===== 按钮样式 ===== */
.btn {
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-xs);
    padding: var(--spacing-sm) var(--spacing-lg);
    border: none;
    border-radius: var(--radius-lg);
    font-size: 0.875rem;
    font-weight: 600;
    text-decoration: none;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s ease;
}

.btn:hover::before {
    left: 100%;
}

.btn.btn-primary {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
    color: white;
    box-shadow: var(--shadow-md);
}

.btn.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.btn.btn-success {
    background: linear-gradient(135deg, var(--success-color), #34D399);
    color: white;
    box-shadow: var(--shadow-md);
}

.btn.btn-success:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.btn.btn-warning {
    background: linear-gradient(135deg, var(--warning-color), #FBBF24);
    color: white;
    box-shadow: var(--shadow-md);
}

.btn.btn-danger {
    background: linear-gradient(135deg, var(--error-color), #F87171);
    color: white;
    box-shadow: var(--shadow-md);
}

/* ===== 响应式设计 ===== */
@media (max-width: 1024px) {
    .sidebar {
        transform: translateX(-100%);
    }

    .sidebar.open {
        transform: translateX(0);
    }

    .main-content {
        margin-left: 0;
    }
}

@media (max-width: 768px) {
    .main-content {
        padding: var(--spacing-md);
    }

    .content-wrapper {
        padding: var(--spacing-lg);
    }

    .content-header {
        flex-direction: column;
        gap: var(--spacing-md);
        align-items: flex-start;
    }

    .header-actions {
        width: 100%;
        justify-content: flex-start;
    }
}

/* ===== 信息网格布局 ===== */
.info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--spacing-lg);
    margin-bottom: var(--spacing-xl);
}

.info-item {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xs);
}

.info-item.full-width {
    grid-column: 1 / -1;
}

.info-item .label {
    font-size: 0.875rem;
    font-weight: 600;
    color: var(--gray-600);
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.info-item .value {
    font-size: 1rem;
    font-weight: 500;
    color: var(--gray-800);
    padding: var(--spacing-sm) var(--spacing-md);
    background: var(--gray-50);
    border-radius: var(--radius-md);
    border-left: 3px solid var(--primary-color);
}

/* ===== 状态徽章 ===== */
.status-badge {
    display: inline-flex;
    align-items: center;
    padding: 4px 12px;
    border-radius: var(--radius-xl);
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.status-badge.success {
    background: rgba(16, 185, 129, 0.1);
    color: var(--success-color);
    border: 1px solid rgba(16, 185, 129, 0.2);
}

.status-badge.warning {
    background: rgba(245, 158, 11, 0.1);
    color: var(--warning-color);
    border: 1px solid rgba(245, 158, 11, 0.2);
}

.status-badge.danger {
    background: rgba(239, 68, 68, 0.1);
    color: var(--error-color);
    border: 1px solid rgba(239, 68, 68, 0.2);
}

.status-badge.info {
    background: rgba(59, 130, 246, 0.1);
    color: var(--info-color);
    border: 1px solid rgba(59, 130, 246, 0.2);
}

/* ===== 文本样式 ===== */
.text-muted {
    color: var(--gray-500);
    font-style: italic;
}

.text-center {
    text-align: center;
}

.text-left {
    text-align: left;
}

.text-right {
    text-align: right;
}

/* ===== 工具类 ===== */
.font-bold {
    font-weight: 700;
}

.font-semibold {
    font-weight: 600;
}

.font-medium {
    font-weight: 500;
}

.text-sm {
    font-size: 0.875rem;
}

.text-lg {
    font-size: 1.125rem;
}

.text-xl {
    font-size: 1.25rem;
}

.text-2xl {
    font-size: 1.5rem;
}

.mb-2 {
    margin-bottom: var(--spacing-xs);
}

.mb-4 {
    margin-bottom: var(--spacing-md);
}

.mb-6 {
    margin-bottom: var(--spacing-lg);
}

.mb-8 {
    margin-bottom: var(--spacing-xl);
}

.mt-2 {
    margin-top: var(--spacing-xs);
}

.mt-4 {
    margin-top: var(--spacing-md);
}

.mt-6 {
    margin-top: var(--spacing-lg);
}

.mt-8 {
    margin-top: var(--spacing-xl);
}

/* ===== 加载动画 ===== */
.loading-spinner {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 2px solid rgba(111, 123, 245, 0.3);
    border-radius: 50%;
    border-top-color: var(--primary-color);
    animation: spin 1s linear infinite;
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}

/* ===== Toast 通知样式 ===== */
.toast {
    position: fixed;
    top: 20px;
    right: 20px;
    background: white;
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-xl);
    padding: var(--spacing-lg);
    border-left: 4px solid var(--primary-color);
    z-index: 9999;
    animation: slideInRight 0.3s ease;
    max-width: 400px;
}

.toast.success {
    border-left-color: var(--success-color);
}

.toast.warning {
    border-left-color: var(--warning-color);
}

.toast.error {
    border-left-color: var(--error-color);
}

@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}
