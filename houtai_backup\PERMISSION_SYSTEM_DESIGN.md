# 趣玩星球管理后台 - 权限管理系统设计

## 🎯 系统概述

基于您的需求，设计了一个完整的权限管理和部门管理系统，支持多级权限控制和部门层级管理。

## 📋 菜单结构

### 1. **权限管理** (父菜单)
- **权限申请** - 员工申请新权限
- **权限审批** - 管理员审批权限申请
- **角色管理** - 创建、编辑、删除角色及其权限
- **权限配置** - 系统权限的增删改查

### 2. **部门管理** (父菜单)
- **部门总览** - 查看所有部门结构（超级管理员）
- **我的部门** - 管理自己所在部门的员工
- **员工管理** - 部门内员工的增删改查
- **组织架构** - 可视化组织结构图

### 3. **用户管理** (现有菜单)
- **用户列表** - 平台用户管理
- **实名认证审核** - 用户认证审核

### 4. **数据统计** (现有菜单)
- **仪表盘** - 数据统计和图表

### 5. **系统设置** (扩展菜单)
- **系统配置** - 基础系统参数
- **日志管理** - 操作日志查看
- **系统监控** - 系统运行状态
- **备份恢复** - 数据备份管理

## 🏗️ 权限系统架构

### 权限层级设计
```
超级管理员 (Super Admin)
├── 部门总管理员 (Department Super Admin)
│   ├── 部门管理员 (Department Admin)
│   │   ├── 部门主管 (Department Supervisor)
│   │   │   └── 普通员工 (Employee)
```

### 权限分类
1. **系统权限** - 系统级别的操作权限
2. **部门权限** - 部门级别的管理权限
3. **功能权限** - 具体功能模块的操作权限
4. **数据权限** - 数据查看和操作范围

## 🔐 权限管理功能详解

### 1. 权限申请流程
```
员工提交申请 → 部门主管审核 → 部门管理员审批 → 系统生效
```

**功能特性：**
- 在线申请表单
- 申请理由说明
- 申请状态跟踪
- 邮件/站内信通知

### 2. 权限审批功能
```
待审批列表 → 查看申请详情 → 审批决定 → 通知申请人
```

**审批权限：**
- **部门主管**：审核本部门员工申请
- **部门管理员**：审批本部门所有申请
- **超级管理员**：审批所有申请

### 3. 角色管理系统
**预设角色：**
- 超级管理员
- 部门总管理员
- 部门管理员
- 部门主管
- 普通员工

**自定义角色：**
- 创建新角色
- 分配权限组合
- 角色继承关系
- 角色权限模板

### 4. 权限配置管理
**权限类型：**
- **查看权限** (View)
- **创建权限** (Create)
- **编辑权限** (Update)
- **删除权限** (Delete)
- **审批权限** (Approve)

## 🏢 部门管理功能详解

### 1. 部门总览 (超级管理员专用)
**功能范围：**
- 查看所有部门
- 创建新部门
- 编辑部门信息
- 删除部门
- 调整部门层级
- 分配部门负责人

**部门信息：**
- 部门名称
- 部门代码
- 上级部门
- 部门负责人
- 员工数量
- 创建时间

### 2. 我的部门 (部门管理员)
**功能范围：**
- 只能管理自己所在的部门
- 查看部门员工列表
- 编辑部门基本信息
- 管理部门内员工

**权限限制：**
- 不能删除部门
- 不能调整部门层级
- 不能管理其他部门

### 3. 员工管理
**管理功能：**
- 添加新员工
- 编辑员工信息
- 调整员工职位
- 分配员工角色
- 员工离职处理

**员工信息：**
- 基本信息（姓名、工号、邮箱、电话）
- 职位信息（部门、职位、级别）
- 权限信息（角色、权限列表）
- 入职信息（入职日期、试用期）

### 4. 组织架构
**可视化功能：**
- 树形组织结构图
- 部门员工分布
- 职位层级展示
- 权限关系图

## 📊 数据库设计建议

### 核心表结构

#### 1. 部门表 (departments)
```sql
CREATE TABLE departments (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(100) NOT NULL COMMENT '部门名称',
    code VARCHAR(50) UNIQUE COMMENT '部门代码',
    parent_id INT DEFAULT NULL COMMENT '上级部门ID',
    manager_id INT DEFAULT NULL COMMENT '部门负责人ID',
    description TEXT COMMENT '部门描述',
    status ENUM('active', 'inactive') DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (parent_id) REFERENCES departments(id),
    FOREIGN KEY (manager_id) REFERENCES admin_users(id)
);
```

#### 2. 角色表 (roles)
```sql
CREATE TABLE roles (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(50) NOT NULL COMMENT '角色名称',
    code VARCHAR(50) UNIQUE COMMENT '角色代码',
    description TEXT COMMENT '角色描述',
    level INT DEFAULT 0 COMMENT '角色级别',
    is_system BOOLEAN DEFAULT FALSE COMMENT '是否系统角色',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

#### 3. 权限表 (permissions)
```sql
CREATE TABLE permissions (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(100) NOT NULL COMMENT '权限名称',
    code VARCHAR(100) UNIQUE COMMENT '权限代码',
    module VARCHAR(50) COMMENT '所属模块',
    action VARCHAR(50) COMMENT '操作类型',
    description TEXT COMMENT '权限描述',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

#### 4. 角色权限关联表 (role_permissions)
```sql
CREATE TABLE role_permissions (
    id INT PRIMARY KEY AUTO_INCREMENT,
    role_id INT NOT NULL,
    permission_id INT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (role_id) REFERENCES roles(id) ON DELETE CASCADE,
    FOREIGN KEY (permission_id) REFERENCES permissions(id) ON DELETE CASCADE,
    UNIQUE KEY unique_role_permission (role_id, permission_id)
);
```

#### 5. 用户角色关联表 (user_roles)
```sql
CREATE TABLE user_roles (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    role_id INT NOT NULL,
    department_id INT DEFAULT NULL COMMENT '在该部门的角色',
    assigned_by INT COMMENT '分配人',
    assigned_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    expires_at TIMESTAMP NULL COMMENT '过期时间',
    FOREIGN KEY (user_id) REFERENCES admin_users(id) ON DELETE CASCADE,
    FOREIGN KEY (role_id) REFERENCES roles(id) ON DELETE CASCADE,
    FOREIGN KEY (department_id) REFERENCES departments(id),
    FOREIGN KEY (assigned_by) REFERENCES admin_users(id)
);
```

#### 6. 权限申请表 (permission_requests)
```sql
CREATE TABLE permission_requests (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL COMMENT '申请人',
    permission_id INT NOT NULL COMMENT '申请的权限',
    reason TEXT COMMENT '申请理由',
    status ENUM('pending', 'approved', 'rejected') DEFAULT 'pending',
    reviewer_id INT DEFAULT NULL COMMENT '审核人',
    review_comment TEXT COMMENT '审核意见',
    reviewed_at TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES admin_users(id),
    FOREIGN KEY (permission_id) REFERENCES permissions(id),
    FOREIGN KEY (reviewer_id) REFERENCES admin_users(id)
);
```

#### 7. 扩展admin_users表
```sql
ALTER TABLE admin_users ADD COLUMN department_id INT DEFAULT NULL COMMENT '所属部门';
ALTER TABLE admin_users ADD COLUMN position VARCHAR(100) DEFAULT NULL COMMENT '职位';
ALTER TABLE admin_users ADD COLUMN level INT DEFAULT 1 COMMENT '职级';
ALTER TABLE admin_users ADD COLUMN direct_manager_id INT DEFAULT NULL COMMENT '直属上级';
ALTER TABLE admin_users ADD FOREIGN KEY (department_id) REFERENCES departments(id);
ALTER TABLE admin_users ADD FOREIGN KEY (direct_manager_id) REFERENCES admin_users(id);
```

## 🔄 权限控制逻辑

### 1. 部门权限控制
```php
// 检查用户是否可以管理指定部门
function canManageDepartment($userId, $departmentId) {
    // 超级管理员可以管理所有部门
    if (isSuperAdmin($userId)) return true;
    
    // 部门管理员只能管理自己的部门
    $userDept = getUserDepartment($userId);
    return $userDept === $departmentId;
}
```

### 2. 数据权限控制
```php
// 根据用户权限过滤数据
function filterDataByPermission($userId, $data, $module) {
    $userPermissions = getUserPermissions($userId);
    $userDepartment = getUserDepartment($userId);
    
    // 根据权限级别过滤数据
    if (hasPermission($userPermissions, $module . '.view_all')) {
        return $data; // 查看所有数据
    } elseif (hasPermission($userPermissions, $module . '.view_department')) {
        return filterByDepartment($data, $userDepartment); // 只查看本部门数据
    } else {
        return filterByUser($data, $userId); // 只查看自己的数据
    }
}
```

## 🎯 实现优先级

### 第一阶段 (核心功能)
1. 数据库表结构创建
2. 基础权限系统
3. 部门管理基础功能
4. 角色管理系统

### 第二阶段 (扩展功能)
1. 权限申请流程
2. 审批工作流
3. 组织架构可视化
4. 权限报表统计

### 第三阶段 (优化功能)
1. 权限缓存优化
2. 操作日志记录
3. 权限变更通知
4. 移动端适配

---

**设计理念**：分层管理，权责明确，既保证系统安全性，又提供灵活的权限配置能力！
