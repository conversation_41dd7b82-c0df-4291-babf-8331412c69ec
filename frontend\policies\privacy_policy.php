<?php
session_start();

// 获取来源页面参数，决定返回链接
$from = isset($_GET['from']) ? $_GET['from'] : 'home';

// 根据来源设置返回链接和标题
switch($from) {
    case 'login':
        $back_url = '../login/index.php';
        $back_title = '返回登录';
        break;
    case 'register':
        $back_url = '../login/index.php';
        $back_title = '返回注册';
        break;
    case 'home':
    default:
        $back_url = '../home/<USER>';
        $back_title = '返回首页';
        break;
}
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0, user-scalable=no, shrink-to-fit=no">
    <meta name="format-detection" content="telephone=no">
    <style>
        * {
            -webkit-touch-callout: none;
            -webkit-user-select: none;
            -khtml-user-select: none;
            -moz-user-select: none;
            -ms-user-select: none;
            user-select: none;
            -webkit-tap-highlight-color: transparent;
        }

        /* 允许选择文本内容 */
        .policy-content, .policy-content p, .policy-content li, .policy-content h2 {
            -webkit-user-select: text;
            -khtml-user-select: text;
            -moz-user-select: text;
            -ms-user-select: text;
            user-select: text;
        }

        /* 禁止双击放大 */
        html {
            -ms-touch-action: manipulation;
            touch-action: manipulation;
        }
    </style>
    <title>隐私政策 - 趣玩星球</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="../css/page-protection.css">
    <style>
        /* CSS变量定义 - 主题色系统 */
        :root {
            --primary-color: #6F7BF5;
            --primary-light: #8A94F7;
            --primary-dark: #5A67E8;
            --secondary-color: #7C3AED;
            --accent-color: #FF6B9D;
            --gradient-primary: linear-gradient(135deg, #6F7BF5, #7C3AED);
            --gradient-secondary: linear-gradient(135deg, #8A94F7, #6F7BF5);
            --gradient-accent: linear-gradient(135deg, #FF6B9D, #FFB6C1);
            --text-primary: #333333;
            --text-secondary: #666666;
            --text-light: #999999;
            --bg-white: #FFFFFF;
            --bg-light: #F8F9FA;
            --bg-gradient: linear-gradient(135deg, #F0FFFF, #F8F9FA);
            --shadow-sm: 0 2px 8px rgba(111, 123, 245, 0.08);
            --shadow-md: 0 4px 16px rgba(111, 123, 245, 0.12);
            --shadow-lg: 0 8px 32px rgba(111, 123, 245, 0.16);
            --radius-sm: 8px;
            --radius-md: 12px;
            --radius-lg: 16px;
            --radius-xl: 24px;
            --transition-fast: 0.2s ease;
            --transition-normal: 0.3s ease;
            --transition-slow: 0.4s ease;
        }

        body {
            background: var(--bg-gradient);
            color: var(--text-primary);
            font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;
            line-height: 1.6;
            position: relative;
            overflow-x: hidden;
        }

        /* 背景装饰 */
        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="20" cy="20" r="3" fill="rgba(111,123,245,0.05)"/><circle cx="80" cy="40" r="2" fill="rgba(124,58,237,0.05)"/><circle cx="60" cy="80" r="1.5" fill="rgba(255,107,157,0.05)"/><circle cx="30" cy="70" r="2.5" fill="rgba(138,148,247,0.05)"/></svg>');
            pointer-events: none;
            z-index: -1;
        }

        .policy-container {
            max-width: 900px;
            margin: 30px auto;
            padding: 40px;
            background: var(--bg-white);
            border-radius: var(--radius-xl);
            box-shadow: var(--shadow-lg);
            position: relative;
            overflow: hidden;
        }

        .policy-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: var(--gradient-primary);
        }

        .policy-header {
            text-align: center;
            margin-bottom: 40px;
            padding-bottom: 30px;
            border-bottom: 2px solid rgba(111, 123, 245, 0.1);
            position: relative;
        }

        .policy-header::after {
            content: '';
            position: absolute;
            bottom: -2px;
            left: 50%;
            transform: translateX(-50%);
            width: 60px;
            height: 2px;
            background: var(--gradient-primary);
        }

        .policy-header h1 {
            background: var(--gradient-primary);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            font-size: 28px;
            font-weight: 700;
            margin-bottom: 16px;
            position: relative;
            white-space: nowrap;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .policy-header h1::before {
            content: '\f023';
            font-family: 'Font Awesome 6 Free';
            font-weight: 900;
            color: var(--primary-color);
            font-size: 20px;
            margin-right: 8px;
            -webkit-text-fill-color: var(--primary-color);
            flex-shrink: 0;
        }

        .policy-header p {
            color: var(--text-secondary);
            font-size: 15px;
            margin-bottom: 8px;
            font-weight: 500;
        }

        .policy-content h2 {
            color: var(--text-primary);
            font-size: 22px;
            font-weight: 600;
            margin-top: 40px;
            margin-bottom: 20px;
            padding: 16px 20px;
            background: linear-gradient(135deg, rgba(111, 123, 245, 0.05), rgba(138, 148, 247, 0.05));
            border-radius: var(--radius-md);
            border-left: 4px solid var(--primary-color);
            position: relative;
        }

        .policy-content h2::before {
            content: '\f0c9';
            font-family: 'Font Awesome 6 Free';
            font-weight: 900;
            color: var(--primary-color);
            font-size: 18px;
            margin-right: 10px;
        }

        .policy-content p {
            margin-bottom: 18px;
            text-align: justify;
            line-height: 1.7;
            color: var(--text-primary);
        }

        .policy-content ul, .policy-content ol {
            margin-bottom: 20px;
            padding-left: 24px;
        }

        .policy-content li {
            margin-bottom: 10px;
            line-height: 1.6;
            color: var(--text-primary);
            position: relative;
        }

        .policy-content ul li::before {
            content: '\f00c';
            font-family: 'Font Awesome 6 Free';
            font-weight: 900;
            color: var(--secondary-color);
            font-size: 12px;
            position: absolute;
            left: -20px;
            top: 4px;
        }

        .policy-footer {
            margin-top: 50px;
            padding: 30px;
            background: linear-gradient(135deg, rgba(111, 123, 245, 0.05), rgba(138, 148, 247, 0.05));
            border-radius: var(--radius-lg);
            text-align: center;
            font-size: 15px;
            color: var(--text-secondary);
            border: 2px solid rgba(111, 123, 245, 0.1);
        }

        .back-button {
            display: inline-flex;
            align-items: center;
            margin-top: 24px;
            padding: 12px 24px;
            background: var(--gradient-primary);
            color: white;
            border-radius: 50px;
            text-decoration: none;
            font-weight: 600;
            transition: var(--transition-normal);
            box-shadow: var(--shadow-md);
        }

        .back-button:hover {
            background: var(--gradient-secondary);
            transform: translateY(-3px);
            box-shadow: var(--shadow-lg);
        }

        .back-button i {
            margin-right: 8px;
            font-size: 14px;
        }

        .header-bar {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 64px;
            background: var(--bg-white);
            backdrop-filter: blur(10px);
            box-shadow: var(--shadow-sm);
            z-index: 1000;
            display: flex;
            align-items: center;
            border-bottom: 1px solid rgba(111, 123, 245, 0.1);
        }

        .header-content {
            width: 100%;
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
            display: flex;
            align-items: center;
            position: relative;
        }

        .page-title {
            position: absolute;
            left: 50%;
            transform: translateX(-50%);
            font-size: 18px;
            font-weight: 600;
            color: var(--text-primary);
            background: var(--gradient-primary);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .back-button-top {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 40px;
            height: 40px;
            background: var(--bg-white);
            color: var(--primary-color);
            border-radius: 50%;
            text-decoration: none;
            transition: var(--transition-normal);
            box-shadow: var(--shadow-sm);
            position: absolute;
            left: 15px;
            border: 2px solid rgba(111, 123, 245, 0.2);
        }

        .back-button-top svg {
            width: 20px;
            height: 20px;
        }

        .back-button-top:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-md);
            background: var(--primary-light);
            border-color: var(--primary-color);
        }

        body {
            padding-top: 84px;
        }

        .highlight {
            background: linear-gradient(135deg, rgba(255, 107, 157, 0.05), rgba(255, 182, 193, 0.05));
            padding: 20px;
            border-radius: var(--radius-md);
            border-left: 4px solid var(--accent-color);
            margin-bottom: 24px;
            position: relative;
        }

        .highlight::before {
            content: '\f071';
            font-family: 'Font Awesome 6 Free';
            font-weight: 900;
            color: var(--accent-color);
            font-size: 16px;
            position: absolute;
            top: 20px;
            left: -12px;
            background: var(--bg-white);
            padding: 4px;
            border-radius: 50%;
        }
    </style>
</head>
<body>
    <div class="header-bar">
        <div class="header-content">
            <a href="<?php echo $back_url; ?>" class="back-button-top" title="<?php echo $back_title; ?>">
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <path d="M15 18l-6-6 6-6"/>
                </svg>
            </a>
            <div class="page-title">隐私政策</div>
        </div>
    </div>

    <div class="policy-container">
        <div class="policy-header">
            <h1>趣玩星球隐私政策</h1>
            <p>版本号：v1.0</p>
            <p>生效日期：2025年5月15日</p>
        </div>

        <div class="policy-content">
            <h2>概要</h2>
            <p>趣玩星球（"我们"）非常重视您的隐私保护。本隐私政策旨在向您说明我们如何收集、使用、存储和共享您的个人信息，以及您享有的相关权利。请您在使用我们的服务前，仔细阅读并了解本隐私政策。</p>

            <div class="highlight">
                <p>根据《中华人民共和国网络安全法》、《中华人民共和国个人信息保护法》、《移动互联网应用程序个人信息保护管理暂行规定》等法律法规要求，我们将严格保护您的个人信息安全。</p>
            </div>

            <p><strong>重要提示</strong>：</p>
            <ul>
                <li>我们仅会出于本政策所述目的收集和使用您的个人信息</li>
                <li>我们采用业界先进的安全技术保护您的个人信息</li>
                <li>我们不会向第三方出售您的个人信息</li>
                <li>您有权随时查看、更正、删除您的个人信息</li>
                <li>如有疑问，您可以随时联系我们的客服团队</li>
            </ul>

            <h2>1. 我们如何收集和使用个人信息</h2>

            <h3>1.1 账号服务</h3>
            <p><strong>1.1.1 注册、登录</strong></p>
            <ul>
                <li><strong>收集信息</strong>：手机号码、密码、验证码、设备信息（设备型号、操作系统版本、设备标识符）</li>
                <li><strong>使用目的</strong>：创建和管理您的账号，验证您的身份，保障账号安全</li>
                <li><strong>法律依据</strong>：履行合同必要、合法利益</li>
            </ul>

            <p><strong>1.1.2 实名认证</strong></p>
            <ul>
                <li><strong>收集信息</strong>：真实姓名、身份证号码、身份证照片</li>
                <li><strong>使用目的</strong>：完成实名认证，遵守法律法规要求，防范欺诈行为</li>
                <li><strong>法律依据</strong>：法律义务、合法利益</li>
            </ul>

            <p><strong>1.1.3 账号公开信息</strong></p>
            <ul>
                <li><strong>收集信息</strong>：昵称、头像、个人简介、兴趣标签、地区信息</li>
                <li><strong>使用目的</strong>：展示您的公开资料，帮助其他用户了解您，提供社交匹配服务</li>
                <li><strong>法律依据</strong>：履行合同必要、用户同意</li>
            </ul>

            <p><strong>1.1.4 趣玩星球统一账号和关联版本</strong></p>
            <ul>
                <li><strong>收集信息</strong>：账号关联信息、登录记录、设备信息</li>
                <li><strong>使用目的</strong>：提供跨平台的统一账号体验，同步用户数据</li>
                <li><strong>法律依据</strong>：履行合同必要、用户同意</li>
            </ul>

            <h3>1.2 内容浏览和搜索</h3>
            <ul>
                <li><strong>收集信息</strong>：浏览记录、搜索关键词、点击行为、停留时间、位置信息</li>
                <li><strong>使用目的</strong>：为您推荐感兴趣的内容，优化搜索结果，改进产品功能</li>
                <li><strong>法律依据</strong>：合法利益、用户同意</li>
            </ul>

            <h3>1.3 信息发布</h3>
            <p><strong>1.3.1 音视频、图文</strong></p>
            <ul>
                <li><strong>收集信息</strong>：您发布的文字、图片、音频、视频内容，发布时间、位置信息</li>
                <li><strong>使用目的</strong>：展示您的内容，提供社交分享功能，内容审核</li>
                <li><strong>法律依据</strong>：履行合同必要、用户同意</li>
            </ul>

            <p><strong>1.3.2 工具</strong></p>
            <ul>
                <li><strong>收集信息</strong>：使用工具时产生的数据，如拍照、录音、编辑等操作记录</li>
                <li><strong>使用目的</strong>：提供内容创作工具，优化工具功能</li>
                <li><strong>法律依据</strong>：履行合同必要、用户同意</li>
            </ul>

            <p>1.2 我们在您使用服务过程中收集的信息：</p>
            <ul>
                <li><strong>设备信息</strong>：
                    <ul>
                        <li>设备型号、品牌、制造商信息</li>
                        <li>操作系统类型和版本号</li>
                        <li>设备识别码（IMEI、IDFA、OAID、Android ID等）</li>
                        <li>MAC地址、SIM卡信息</li>
                        <li>屏幕分辨率、屏幕密度</li>
                        <li>设备内存、存储空间信息</li>
                        <li>处理器信息、电池状态</li>
                        <li>传感器信息（如重力感应器、陀螺仪等）</li>
                    </ul>
                </li>
                <li><strong>网络信息</strong>：
                    <ul>
                        <li>IP地址及其对应的大致位置</li>
                        <li>网络类型（WiFi、4G、5G等）</li>
                        <li>网络运营商信息</li>
                        <li>网络信号强度</li>
                        <li>网络连接状态</li>
                        <li>网络延迟和带宽信息</li>
                    </ul>
                </li>
                <li><strong>日志信息</strong>：
                    <ul>
                        <li>搜索查询内容和时间</li>
                        <li>访问日期和时间</li>
                        <li>页面浏览记录和停留时间</li>
                        <li>点击和交互行为记录</li>
                        <li>Cookie ID和会话信息</li>
                        <li>崩溃日志和错误报告</li>
                        <li>应用启动和退出时间</li>
                    </ul>
                </li>
                <li><strong>位置信息</strong>：经您授权，我们会收集：
                    <ul>
                        <li>GPS位置信息（经纬度坐标）</li>
                        <li>基站定位信息</li>
                        <li>WiFi接入点信息</li>
                        <li>蓝牙信标信息</li>
                        <li>位置变化轨迹</li>
                        <li>常用位置信息</li>
                    </ul>
                    <p>我们使用这些信息为您提供基于位置的服务，如附近的活动推荐、城市玩伴匹配等。</p>
                </li>
                <li><strong>使用情况</strong>：
                    <ul>
                        <li>应用使用频率和时长</li>
                        <li>功能使用情况统计</li>
                        <li>页面访问路径和深度</li>
                        <li>交互行为模式</li>
                        <li>内容偏好和兴趣标签</li>
                        <li>社交互动数据</li>
                    </ul>
                </li>
                <li><strong>其他设备信息</strong>：
                    <ul>
                        <li>相机参数信息（仅在您使用相机功能时）</li>
                        <li>麦克风参数信息（仅在您使用语音功能时）</li>
                        <li>传感器数据（如计步器数据，仅用于活动相关功能）</li>
                        <li>设备电量信息（用于优化应用性能）</li>
                        <li>设备存储空间信息（用于缓存管理）</li>
                    </ul>
                </li>
            </ul>

            <p>1.3 我们如何使用设备信息：</p>
            <ul>
                <li><strong>设备识别码</strong>：用于识别您的设备，防止欺诈行为，保障账号安全，提供设备间的无缝体验。</li>
                <li><strong>设备型号和操作系统</strong>：用于优化应用性能，确保功能兼容性，解决特定设备的技术问题。</li>
                <li><strong>网络信息</strong>：用于分析网络质量，优化数据传输，提供更流畅的服务体验。</li>
                <li><strong>位置信息</strong>：用于提供基于位置的服务，如附近的活动、玩伴推荐，以及地图导航功能。</li>
                <li><strong>传感器数据</strong>：用于支持特定功能，如计步器数据用于活动统计，重力感应器用于屏幕旋转等。</li>
            </ul>

            <p>1.4 信息收集的最小化原则：</p>
            <p>我们严格遵循"最小必要"原则收集您的信息，仅收集为实现服务功能所必需的信息。对于非必要的信息收集，我们会事先获得您的明确同意。</p>

            <h2>2. 我们如何使用您的信息</h2>
            <p>2.1 提供、维护和改进我们的服务：</p>
            <ul>
                <li>创建和管理您的账号；</li>
                <li>向您提供城市玩伴、游戏玩伴、旅行玩伴、组织活动等服务；</li>
                <li>处理您的支付请求和交易；</li>
                <li>改进和开发新的服务功能；</li>
                <li>提供客户支持和响应您的请求。</li>
            </ul>

            <p>2.2 个性化您的体验：</p>
            <ul>
                <li>根据您的兴趣和偏好推荐内容和活动；</li>
                <li>根据您的位置信息提供附近的服务；</li>
                <li>向您展示定制化的广告。</li>
            </ul>

            <p>2.3 安全和保障：</p>
            <ul>
                <li>验证您的身份，防止欺诈和未经授权的访问；</li>
                <li>监测和防止平台上的违规行为；</li>
                <li>保障平台和用户的安全。</li>
            </ul>

            <p>2.4 通信和营销：</p>
            <ul>
                <li>向您发送服务通知，如系统维护通知、账号验证信息等；</li>
                <li>经您同意后，向您发送营销信息，如活动推广、优惠信息等。</li>
            </ul>

            <h2>3. 设备权限调用说明</h2>
            <p>我们可能需要调用您设备的以下权限：</p>
            <ul>
                <li>相机权限：用于上传头像、发布内容时添加图片等功能。</li>
                <li>相册权限：用于上传和保存图片。</li>
                <li>麦克风权限：用于语音聊天、语音输入等功能。</li>
                <li>位置权限：用于提供基于位置的服务，如附近的活动推荐。</li>
                <li>存储权限：用于缓存应用数据，提升使用体验。</li>
                <li>通讯录权限：用于帮助您找到同样使用趣玩星球的联系人（仅在您主动授权的情况下）。</li>
                <li>通知权限：用于向您推送消息和通知。</li>
            </ul>
            <p>您可以在设备的设置功能中随时开启或关闭这些权限。关闭权限可能会导致相应功能无法正常使用，但不会影响其他功能的正常使用。</p>

            <h2>4. 信息的共享与披露</h2>
            <p>4.1 我们不会向第三方出售您的个人信息。</p>
            <p>4.2 在以下情况下，我们可能会共享您的个人信息：</p>
            <ul>
                <li>经您明确同意的情况下；</li>
                <li>与我们的关联公司共享，用于提供和改进我们的服务；</li>
                <li>与提供服务的供应商和合作伙伴共享，如支付处理商、云服务提供商等；</li>
                <li>根据法律法规、法律程序或政府强制性要求；</li>
                <li>在涉及合并、收购、资产转让或类似的交易时；</li>
                <li>为保护趣玩星球、我们的用户或公众的权利、财产或安全。</li>
            </ul>
            <p>4.3 我们会要求第三方对您的个人信息采取保护措施，并且遵守相关的隐私法规。</p>

            <h2>5. 信息的存储与保护</h2>
            <p>5.1 信息存储：</p>
            <ul>
                <li>我们会在中华人民共和国境内存储您的个人信息。</li>
                <li>我们只会在实现本政策所述目的所必需的期间内保留您的个人信息，除非法律要求或允许更长的保留期。</li>
            </ul>
            <p>5.2 信息安全：</p>
            <ul>
                <li>我们采取各种安全技术和程序，以防信息的丢失、不当使用、未经授权阅览或披露。</li>
                <li>我们使用加密技术确保数据的保密性；我们使用受信赖的保护机制防止数据遭到恶意攻击。</li>
                <li>我们设立了数据安全部门，专门负责个人信息安全；我们对员工进行数据安全培训。</li>
            </ul>
            <p>5.3 安全事件处置：</p>
            <p>如不幸发生个人信息安全事件，我们将按照法律法规的要求，及时向您告知：安全事件的基本情况和可能的影响、我们已采取或将要采取的处置措施、您可自主防范和降低风险的建议、对您的补救措施等。</p>

            <h2>6. 您的权利</h2>
            <p>根据中国相关的法律、法规、标准，以及其他国家、地区的通行做法，我们保障您对自己的个人信息行使以下权利：</p>
            <ul>
                <li>访问和更正您的个人信息；</li>
                <li>删除您的个人信息；</li>
                <li>撤回同意；</li>
                <li>注销账号；</li>
                <li>获取个人信息副本；</li>
                <li>限制个人信息处理；</li>
                <li>投诉举报。</li>
            </ul>
            <p>您可以通过趣玩星球的设置功能或联系我们的客服行使上述权利。</p>

            <h2>7. 未成年人保护</h2>
            <p><strong>趣玩星球仅面向18周岁及以上的成年人提供服务。</strong>我们非常重视对未成年人个人信息的保护：</p>

            <div class="highlight">
                <p>根据《中华人民共和国未成年人保护法》《网络保护条例》等法律法规的规定，趣玩星球明确声明：本平台仅向年满18周岁的成年人提供服务。</p>
            </div>

            <p>7.1 年龄限制及验证：</p>
            <ul>
                <li>我们在用户注册环节设置了年龄验证机制，要求用户确认其年满18周岁。</li>
                <li>我们可能会通过实名认证等方式验证用户年龄，以确保用户符合使用条件。</li>
                <li>若我们发现用户提供虚假年龄信息，我们有权立即终止对该用户提供服务并删除其账号。</li>
            </ul>

            <p>7.2 未成年人信息处理：</p>
            <ul>
                <li>若我们发现自己在未事先获得可证实的父母或监护人同意的情况下收集了未成年人的个人信息，我们会立即删除相关数据。</li>
                <li>若您发现有未成年人注册使用我们的服务，请立即通知我们，以便我们采取措施予以处理。</li>
                <li>我们会根据国家相关法律法规的规定保护未成年人的个人信息。</li>
            </ul>

            <p>7.3 监护人责任：</p>
            <ul>
                <li>监护人应当正确履行监护职责，教育引导未成年人健康上网，防止未成年人沉迷网络。</li>
                <li>监护人应当预防和制止未成年人使用本平台服务。</li>
                <li>若因监护人疏于监管导致未成年人使用本平台服务而产生的一切后果，由监护人承担。</li>
            </ul>

            <p>7.4 举报与投诉：</p>
            <ul>
                <li>我们设立了专门的未成年人保护通道，若您发现平台上存在未成年人账号，请通过本隐私政策"联系我们"章节中的联系方式向我们举报。</li>
                <li>我们将在收到举报后24小时内处理，并在处理完毕后10个工作日内向举报人反馈处理结果。</li>
            </ul>

            <h2>8. 隐私政策的更新</h2>
            <p>我们可能会不时更新本隐私政策。当我们对隐私政策作出重大变更时，我们会在应用程序上发布通知，并在更新生效前通过应用程序通知或其他方式通知您。</p>
            <p>您继续使用我们的服务，即表示您同意修订后的隐私政策。我们建议您定期查阅本隐私政策。</p>

            <h2>9. 联系我们</h2>
            <p>如您对本隐私政策有任何疑问、意见或建议，可通过以下方式与我们联系：</p>
            <ul>
                <li>电子邮件：<EMAIL></li>
                <li>在线客服：通过趣玩星球应用内的"客服中心"联系我们</li>
                <li>信函邮寄：请寄往我们的注册地址，信封上请注明"隐私政策咨询"</li>
            </ul>
            <p>我们将在收到您的问题后15个工作日内回复。</p>

            <h2>10. 本隐私政策的解释和管辖</h2>
            <p>本隐私政策的解释权归趣玩星球所有。本隐私政策受中华人民共和国法律管辖并按其解释。因本隐私政策引起的或与本隐私政策有关的任何争议，应友好协商解决；协商不成的，您同意将争议提交至趣玩星球住所地有管辖权的人民法院诉讼解决。</p>
        </div>

        <div class="policy-footer">
            <p>感谢您对趣玩星球的信任和支持！</p>
        </div>
    </div>
    <script src="../js/page-protection.js"></script>
</body>
</html>
