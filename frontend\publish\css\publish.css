/* 发布页面样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background-color: #f8f9fa;
    color: #333;
    line-height: 1.6;
}

/* 顶部导航栏 */
.header-bar {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    height: 56px;
    background-color: #40E0D0;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 16px;
    z-index: 100;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.back-button {
    color: white;
    font-size: 20px;
    text-decoration: none;
    padding: 8px;
    border-radius: 50%;
    transition: background-color 0.2s;
}

.back-button:hover {
    background-color: rgba(255, 255, 255, 0.1);
}

.header-title {
    color: white;
    font-size: 18px;
    font-weight: 600;
    flex: 1;
    text-align: center;
    margin: 0 16px;
}

.publish-button {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: 20px;
    padding: 8px 16px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
}

.publish-button:hover {
    background: rgba(255, 255, 255, 0.3);
}

.publish-button:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

/* 主容器 */
.container {
    margin-top: 56px;
    padding: 20px;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
    padding-bottom: 40px;
}

/* 表单组 */
.form-group {
    margin-bottom: 24px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    color: #333;
    font-weight: 500;
    font-size: 16px;
}

.form-group input,
.form-group textarea,
.form-group select {
    width: 100%;
    padding: 12px 16px;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    font-size: 16px;
    transition: border-color 0.3s;
    background: white;
}

.form-group input:focus,
.form-group textarea:focus,
.form-group select:focus {
    outline: none;
    border-color: #40E0D0;
}

.form-group textarea {
    resize: vertical;
    min-height: 100px;
}

.form-group small {
    display: block;
    margin-top: 4px;
    color: #666;
    font-size: 14px;
}

/* 字符计数 */
.char-count {
    text-align: right;
    font-size: 12px;
    color: #999;
    margin-top: 4px;
}

/* 图片上传区域 */
.upload-section {
    margin-bottom: 24px;
}

.upload-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 8px;
}

.upload-item {
    aspect-ratio: 1;
    border-radius: 8px;
    overflow: hidden;
    position: relative;
    cursor: pointer;
}

.add-photo {
    border: 2px dashed #40E0D0;
    background: rgba(64, 224, 208, 0.05);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    color: #40E0D0;
    transition: all 0.3s ease;
}

.add-photo:hover {
    background: rgba(64, 224, 208, 0.1);
    border-color: #36C5B0;
}

.add-photo i {
    font-size: 24px;
    margin-bottom: 4px;
}

.add-photo span {
    font-size: 14px;
    font-weight: 500;
}

.add-photo small {
    font-size: 12px;
    opacity: 0.7;
}

.image-preview {
    background-size: cover;
    background-position: center;
    border: 2px solid #e9ecef;
}

.image-preview .remove-btn {
    position: absolute;
    top: 4px;
    right: 4px;
    width: 24px;
    height: 24px;
    background: rgba(0, 0, 0, 0.6);
    color: white;
    border: none;
    border-radius: 50%;
    font-size: 12px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* 位置输入 */
.location-input {
    display: flex;
    gap: 8px;
}

.location-input input {
    flex: 1;
}

.location-btn {
    background: #40E0D0;
    color: white;
    border: none;
    border-radius: 8px;
    padding: 12px 16px;
    cursor: pointer;
    transition: background-color 0.3s;
}

.location-btn:hover {
    background: #36C5B0;
}

/* 数字输入 */
.number-input {
    display: flex;
    align-items: center;
    gap: 8px;
    max-width: 200px;
}

.number-btn {
    width: 40px;
    height: 40px;
    background: #40E0D0;
    color: white;
    border: none;
    border-radius: 8px;
    font-size: 18px;
    font-weight: bold;
    cursor: pointer;
    transition: background-color 0.3s;
}

.number-btn:hover {
    background: #36C5B0;
}

.number-input input {
    text-align: center;
    flex: 1;
    min-width: 80px;
}

/* 费用输入 */
.fee-input {
    display: flex;
    align-items: center;
    gap: 8px;
}

.fee-input input {
    flex: 1;
}

.fee-unit {
    color: #666;
    font-size: 16px;
    white-space: nowrap;
}

/* 标签输入 */
.tags-input-container {
    border: 2px solid #e9ecef;
    border-radius: 8px;
    padding: 8px;
    background: white;
    transition: border-color 0.3s;
}

.tags-input-container:focus-within {
    border-color: #40E0D0;
}

.tags-input-container input {
    border: none;
    padding: 4px 0;
    font-size: 16px;
    width: 100%;
}

.tags-input-container input:focus {
    outline: none;
}

.tags-display {
    display: flex;
    flex-wrap: wrap;
    gap: 6px;
    margin-top: 8px;
}

.tag-item {
    background: #40E0D0;
    color: white;
    padding: 4px 8px;
    border-radius: 16px;
    font-size: 14px;
    display: flex;
    align-items: center;
    gap: 4px;
}

.tag-remove {
    background: none;
    border: none;
    color: white;
    cursor: pointer;
    font-size: 12px;
    padding: 0;
    width: 16px;
    height: 16px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.tag-remove:hover {
    background: rgba(255, 255, 255, 0.2);
}

/* 隐私选项 */
.privacy-options {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.radio-option {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.radio-option:hover {
    border-color: #40E0D0;
    background: rgba(64, 224, 208, 0.05);
}

.radio-option input[type="radio"] {
    display: none;
}

.radio-custom {
    width: 20px;
    height: 20px;
    border: 2px solid #ddd;
    border-radius: 50%;
    position: relative;
    transition: all 0.3s ease;
}

.radio-option input[type="radio"]:checked + .radio-custom {
    border-color: #40E0D0;
    background: #40E0D0;
}

.radio-option input[type="radio"]:checked + .radio-custom::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 8px;
    height: 8px;
    background: white;
    border-radius: 50%;
}

.option-info {
    flex: 1;
}

.option-info strong {
    display: block;
    color: #333;
    font-size: 16px;
}

.option-info small {
    color: #666;
    font-size: 14px;
}

/* 加载覆盖层 */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

.loading-content {
    background: white;
    padding: 30px;
    border-radius: 12px;
    text-align: center;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
}

.loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid #40E0D0;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 16px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.loading-content p {
    color: #333;
    font-size: 16px;
    margin: 0;
}

/* Toast提示 */
.toast {
    position: fixed;
    top: 80px;
    left: 50%;
    transform: translateX(-50%);
    background: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 12px 20px;
    border-radius: 8px;
    font-size: 14px;
    z-index: 1001;
    display: none;
}

/* 响应式设计 */
@media (max-width: 480px) {
    .container {
        padding: 16px;
    }
    
    .upload-grid {
        grid-template-columns: repeat(3, 1fr);
        gap: 6px;
    }
    
    .form-group {
        margin-bottom: 20px;
    }
    
    .privacy-options {
        gap: 8px;
    }
    
    .radio-option {
        padding: 10px;
    }
}
