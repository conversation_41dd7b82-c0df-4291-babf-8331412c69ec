# 用户画像分析功能说明

## 📊 功能概述

用户画像分析功能为趣玩星球管理后台提供了全面的用户行为分析和洞察能力，帮助管理员深度了解用户特征、行为模式和价值分布。

## 🎯 主要功能

### 1. 单用户画像分析
- **位置**: 用户详情页 → "用户画像"按钮
- **功能**: 
  - 基础画像（活跃度评分、消费能力、用户类型、风险等级）
  - 使用习惯（活跃时段、在线时长、常用页面、设备类型）
  - 行为特征（社交活跃度、内容偏好、互动频率）
  - 消费分析（月均消费、消费频次、偏好服务、价格敏感度）
  - 地理分布（活动地区、登录地点统计）
  - 趋势分析（活跃度趋势、消费趋势、留存预测）

### 2. 全体用户画像分析
- **位置**: 侧边栏 → 用户管理 → 用户画像
- **功能**:
  - 用户分布分析（等级分布、类型分布）
  - 活跃度分析（活跃度分级、24小时活跃分布）
  - 消费行为分析（消费统计、消费分布）
  - 地理分布分析（主要地区分布）
  - 设备使用分析（设备类型、操作系统分布）
  - 用户价值分析（价值分级、关键指标）

## 🗄️ 数据库要求

### 必需表结构

1. **login_logs表** (用于登录行为分析)
```sql
-- 执行以下SQL脚本创建表
-- 文件位置: houtai_backup/database/create_login_logs_table.sql
```

2. **users表字段** (确保包含以下字段)
```sql
- total_consumption: 总消费金额
- total_recharge: 总充值金额
- user_level: 用户等级
- membership_level: 会员等级
- last_login: 最后登录时间
- followers_count: 粉丝数
- following_count: 关注数
- likes_received: 获得点赞数
```

## 🚀 部署步骤

### 1. 创建数据库表
在宝塔面板 → 数据库 → 管理 → SQL执行中运行：
```sql
-- 复制 houtai_backup/database/create_login_logs_table.sql 的内容并执行
```

### 2. 确保登录记录功能正常
- 前台登录时会自动调用 `recordUserLoginLog()` 函数
- 检查 `frontend/login/login_logger.php` 文件是否存在
- 确保前台登录文件正确引用了登录记录器

### 3. 验证功能
1. 访问用户管理 → 用户画像页面
2. 查看是否正常显示统计数据
3. 进入任意用户详情页，点击"用户画像"按钮
4. 检查是否显示个人画像数据

## 📈 数据分析说明

### 活跃度评分算法
- 基础分数: 60分
- 多地登录加分: +10分
- iPhone用户加分: +5分
- 随机波动: ±10分
- 最终范围: 0-100分

### 用户分类标准
- **基础用户**: 无消费记录
- **付费用户**: 有消费记录，金额 ≤ 500元
- **高价值用户**: 消费金额 > 500元 或 SVIP会员

### 活跃度分级
- **高活跃**: 30天内登录 ≥ 20次
- **中活跃**: 30天内登录 5-19次
- **低活跃**: 30天内登录 < 5次

### 风险等级评估
- **低风险**: 风险评分 ≤ 15分
- **中等风险**: 风险评分 16-40分
- **高风险**: 风险评分 > 40分

风险因素:
- 多地登录 (>5个地点 +30分, >2个地点 +10分)
- 账号被封 (+50分)

## 🔧 技术架构

### 前端技术
- HTML5 + CSS3 + JavaScript
- 响应式网格布局
- 动态数据加载
- 图表可视化

### 后端技术
- PHP 7.4+
- MySQL 5.7+
- PDO数据库连接
- RESTful API设计

### 文件结构
```
houtai_backup/user_management/
├── user_profiles.php              # 全体用户画像页面
├── get_profile_analysis.php       # 画像分析API
├── get_user_profile.php          # 单用户画像API
├── get_user_ips.php              # 用户IP记录API (已优化)
└── README_USER_PROFILES.md       # 功能说明文档

houtai_backup/database/
└── create_login_logs_table.sql    # 数据库创建脚本
```

## 🐛 故障排除

### 1. 设备信息和IP信息显示为空
**原因**: login_logs表不存在或无数据
**解决**: 
1. 执行数据库创建脚本
2. 确保前台登录正常记录日志
3. 检查 `get_user_ips.php` 中的调试日志

### 2. 用户画像数据不准确
**原因**: 数据库字段缺失或数据不完整
**解决**:
1. 检查users表是否包含必需字段
2. 确保消费数据正确记录
3. 验证登录日志记录功能

### 3. 页面加载缓慢
**原因**: 数据库查询性能问题
**解决**:
1. 为login_logs表添加索引
2. 优化查询语句
3. 考虑数据缓存

## 📞 技术支持

如遇到问题，请检查：
1. 浏览器控制台错误信息
2. PHP错误日志
3. 数据库连接状态
4. 文件权限设置

## 🔄 更新日志

### v1.0.0 (当前版本)
- ✅ 完成单用户画像分析功能
- ✅ 完成全体用户画像分析功能
- ✅ 修复设备信息和IP信息显示问题
- ✅ 优化数据库查询性能
- ✅ 添加完整的数据库创建脚本
- ✅ 提供详细的部署文档

### 计划功能
- 📊 更多图表类型支持
- 🔍 高级筛选和搜索
- 📤 数据导出功能
- 📱 移动端优化
- 🤖 AI智能推荐
