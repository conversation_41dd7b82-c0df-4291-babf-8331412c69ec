/**
 * 趣玩星球管理后台 - 布局交互脚本
 * 现代化交互体验
 */

class AdminLayout {
    constructor() {
        this.init();
    }

    init() {
        this.setupMobileMenu();
        this.setupTooltips();
        this.setupKeyboardShortcuts();
        this.setupPageTransitions();
        this.initTheme();

        console.log('🎯 管理后台布局已初始化');
    }

    // 移动端菜单
    setupMobileMenu() {
        // 创建移动端菜单按钮
        if (window.innerWidth <= 768) {
            this.createMobileMenuButton();
        }

        // 监听窗口大小变化
        window.addEventListener('resize', () => {
            if (window.innerWidth <= 768) {
                this.createMobileMenuButton();
            } else {
                this.removeMobileMenuButton();
                this.closeMobileMenu();
            }
        });

        // 点击遮罩关闭菜单
        document.addEventListener('click', (e) => {
            const sidebar = document.querySelector('.sidebar');
            const mobileBtn = document.querySelector('.mobile-menu-btn');

            if (window.innerWidth <= 768 &&
                sidebar && sidebar.classList.contains('mobile-open') &&
                !sidebar.contains(e.target) &&
                !mobileBtn?.contains(e.target)) {
                this.closeMobileMenu();
            }
        });
    }

    createMobileMenuButton() {
        if (document.querySelector('.mobile-menu-btn')) return;

        const button = document.createElement('button');
        button.className = 'mobile-menu-btn';
        button.innerHTML = '<i class="fas fa-bars"></i>';
        button.addEventListener('click', () => this.toggleMobileMenu());

        document.body.appendChild(button);
    }

    removeMobileMenuButton() {
        const button = document.querySelector('.mobile-menu-btn');
        if (button) {
            button.remove();
        }
    }

    toggleMobileMenu() {
        const sidebar = document.querySelector('.sidebar');
        if (sidebar) {
            sidebar.classList.toggle('mobile-open');

            // 更新按钮图标
            const btn = document.querySelector('.mobile-menu-btn i');
            if (btn) {
                btn.className = sidebar.classList.contains('mobile-open')
                    ? 'fas fa-times'
                    : 'fas fa-bars';
            }
        }
    }

    closeMobileMenu() {
        const sidebar = document.querySelector('.sidebar');
        const btn = document.querySelector('.mobile-menu-btn i');

        if (sidebar) {
            sidebar.classList.remove('mobile-open');
        }

        if (btn) {
            btn.className = 'fas fa-bars';
        }
    }

    // 工具提示
    setupTooltips() {
        const navLinks = document.querySelectorAll('.nav-link, .submenu-link');

        navLinks.forEach(link => {
            link.addEventListener('mouseenter', (e) => {
                const text = e.target.querySelector('span')?.textContent;
                if (text && window.innerWidth <= 768) {
                    this.showTooltip(e.target, text);
                }
            });

            link.addEventListener('mouseleave', () => {
                this.hideTooltip();
            });
        });
    }

    showTooltip(element, text) {
        this.hideTooltip(); // 先隐藏已存在的提示

        const tooltip = document.createElement('div');
        tooltip.className = 'tooltip';
        tooltip.textContent = text;
        tooltip.style.cssText = `
            position: fixed;
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 8px 12px;
            border-radius: 6px;
            font-size: 12px;
            z-index: 10000;
            pointer-events: none;
            white-space: nowrap;
        `;

        document.body.appendChild(tooltip);

        const rect = element.getBoundingClientRect();
        tooltip.style.left = rect.right + 10 + 'px';
        tooltip.style.top = rect.top + (rect.height / 2) - (tooltip.offsetHeight / 2) + 'px';
    }

    hideTooltip() {
        const tooltip = document.querySelector('.tooltip');
        if (tooltip) {
            tooltip.remove();
        }
    }

    // 键盘快捷键
    setupKeyboardShortcuts() {
        document.addEventListener('keydown', (e) => {
            // Alt + H: 首页
            if (e.altKey && e.key === 'h') {
                e.preventDefault();
                window.location.href = '/houtai_backup/home.php';
            }
            // Alt + U: 用户管理
            else if (e.altKey && e.key === 'u') {
                e.preventDefault();
                window.location.href = '/houtai_backup/user_management/index.php';
            }
            // Alt + V: 实名认证
            else if (e.altKey && e.key === 'v') {
                e.preventDefault();
                window.location.href = '/houtai_backup/verification/index.php';
            }
            // Alt + D: 数据统计
            else if (e.altKey && e.key === 'd') {
                e.preventDefault();
                window.location.href = '/houtai_backup/dashboard.php';
            }
            // Esc: 关闭移动端菜单
            else if (e.key === 'Escape') {
                this.closeMobileMenu();
            }
        });
    }

    // 页面过渡效果
    setupPageTransitions() {
        // 为所有内部链接添加过渡效果
        const internalLinks = document.querySelectorAll('a[href^="/houtai_backup/home.php"], a[href^="/houtai_backup/user_management/"], a[href^="/houtai_backup/verification/"], a[href^="/houtai_backup/dashboard.php"]');

        internalLinks.forEach(link => {
            link.addEventListener('click', (e) => {
                if (e.ctrlKey || e.metaKey) return; // 允许在新标签页打开

                e.preventDefault();
                this.navigateWithTransition(link.href);
            });
        });
    }

    navigateWithTransition(url) {
        // 添加加载效果
        const loader = document.createElement('div');
        loader.className = 'page-loader';
        loader.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(255, 255, 255, 0.9);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 10000;
            backdrop-filter: blur(5px);
        `;

        loader.innerHTML = `
            <div style="text-align: center;">
                <div style="width: 40px; height: 40px; border: 3px solid #40E0D0; border-top: 3px solid transparent; border-radius: 50%; animation: spin 1s linear infinite; margin: 0 auto 16px;"></div>
                <p style="color: #6B7280; font-size: 14px;">加载中...</p>
            </div>
        `;

        // 添加旋转动画
        const style = document.createElement('style');
        style.textContent = `
            @keyframes spin {
                0% { transform: rotate(0deg); }
                100% { transform: rotate(360deg); }
            }
        `;
        document.head.appendChild(style);

        document.body.appendChild(loader);

        // 延迟跳转以显示加载效果
        setTimeout(() => {
            window.location.href = url;
        }, 300);
    }

    // 主题管理
    initTheme() {
        // 检查用户主题偏好
        const savedTheme = localStorage.getItem('admin-theme');
        if (savedTheme) {
            document.documentElement.setAttribute('data-theme', savedTheme);
        }

        // 监听系统主题变化
        if (window.matchMedia) {
            const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
            mediaQuery.addListener(() => {
                if (!localStorage.getItem('admin-theme')) {
                    this.updateTheme(mediaQuery.matches ? 'dark' : 'light');
                }
            });
        }
    }

    updateTheme(theme) {
        document.documentElement.setAttribute('data-theme', theme);
        localStorage.setItem('admin-theme', theme);
    }

    // 通知系统
    showNotification(message, type = 'info', duration = 3000) {
        const notification = document.createElement('div');
        notification.className = `notification notification-${type}`;
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: ${this.getNotificationColor(type)};
            color: white;
            padding: 16px 20px;
            border-radius: 12px;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
            z-index: 10000;
            max-width: 400px;
            transform: translateX(100%);
            transition: transform 0.3s ease;
        `;

        notification.innerHTML = `
            <div style="display: flex; align-items: center; gap: 12px;">
                <i class="fas ${this.getNotificationIcon(type)}"></i>
                <span>${message}</span>
                <button onclick="this.parentElement.parentElement.remove()" style="background: none; border: none; color: white; cursor: pointer; margin-left: auto;">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        `;

        document.body.appendChild(notification);

        // 显示动画
        setTimeout(() => {
            notification.style.transform = 'translateX(0)';
        }, 100);

        // 自动隐藏
        setTimeout(() => {
            notification.style.transform = 'translateX(100%)';
            setTimeout(() => {
                if (notification.parentElement) {
                    notification.remove();
                }
            }, 300);
        }, duration);
    }

    getNotificationColor(type) {
        const colors = {
            success: '#10B981',
            error: '#EF4444',
            warning: '#F59E0B',
            info: '#3B82F6'
        };
        return colors[type] || colors.info;
    }

    getNotificationIcon(type) {
        const icons = {
            success: 'fa-check-circle',
            error: 'fa-exclamation-circle',
            warning: 'fa-exclamation-triangle',
            info: 'fa-info-circle'
        };
        return icons[type] || icons.info;
    }

    // 确认对话框
    confirm(message, callback) {
        const modal = document.createElement('div');
        modal.className = 'confirm-modal';
        modal.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 10000;
            backdrop-filter: blur(5px);
        `;

        modal.innerHTML = `
            <div style="background: white; border-radius: 16px; padding: 24px; max-width: 400px; width: 90%; text-align: center;">
                <div style="margin-bottom: 20px;">
                    <i class="fas fa-question-circle" style="font-size: 48px; color: #F59E0B; margin-bottom: 16px;"></i>
                    <p style="font-size: 16px; color: #374151;">${message}</p>
                </div>
                <div style="display: flex; gap: 12px; justify-content: center;">
                    <button class="confirm-btn" style="background: #EF4444; color: white; border: none; padding: 10px 20px; border-radius: 8px; cursor: pointer;">确认</button>
                    <button class="cancel-btn" style="background: #6B7280; color: white; border: none; padding: 10px 20px; border-radius: 8px; cursor: pointer;">取消</button>
                </div>
            </div>
        `;

        document.body.appendChild(modal);

        modal.querySelector('.confirm-btn').addEventListener('click', () => {
            modal.remove();
            callback(true);
        });

        modal.querySelector('.cancel-btn').addEventListener('click', () => {
            modal.remove();
            callback(false);
        });

        modal.addEventListener('click', (e) => {
            if (e.target === modal) {
                modal.remove();
                callback(false);
            }
        });
    }
}

// 全局实例
let adminLayout;

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    adminLayout = new AdminLayout();

    // 显示快捷键提示
    console.log('⌨️ 快捷键提示：');
    console.log('   Alt + H: 首页');
    console.log('   Alt + U: 用户管理');
    console.log('   Alt + V: 实名认证');
    console.log('   Alt + D: 数据统计');
    console.log('   Esc: 关闭移动端菜单');
});

// 导出全局函数
window.showNotification = (message, type, duration) => {
    if (adminLayout) {
        adminLayout.showNotification(message, type, duration);
    }
};

window.confirmAction = (message, callback) => {
    if (adminLayout) {
        adminLayout.confirm(message, callback);
    }
};
