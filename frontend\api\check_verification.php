<?php
session_start();
header('Content-Type: application/json');

// 检查用户是否登录
if (!isset($_SESSION['user_id'])) {
    echo json_encode([
        'success' => false,
        'message' => '用户未登录'
    ]);
    exit;
}

// 数据库连接
require_once '../../sql/db_config.php';

// 获取数据库连接
$pdo = getDbConnection();
if (!$pdo) {
    echo json_encode([
        'success' => false,
        'message' => '数据库连接失败'
    ]);
    exit;
}

try {
    $user_id = $_SESSION['user_id'];

    // 先检查用户表是否有实名认证字段，如果没有则默认未认证
    $stmt = $pdo->prepare("SELECT * FROM users WHERE id = ?");
    $stmt->execute([$user_id]);
    $user = $stmt->fetch(PDO::FETCH_ASSOC);

    if (!$user) {
        echo json_encode([
            'success' => false,
            'message' => '用户不存在'
        ]);
        exit;
    }

    // 尝试查询实名认证表（优先使用实名认证表的状态）
    $verification_status = 'none';
    $real_name = null;
    $submitted_at = null;
    $verified_at = null;
    $is_verified = false;

    try {
        $stmt = $pdo->prepare("
            SELECT verification_status, real_name, submitted_at, verified_at
            FROM realname_verification
            WHERE user_id = ?
        ");
        $stmt->execute([$user_id]);
        $verification = $stmt->fetch(PDO::FETCH_ASSOC);

        if ($verification) {
            $verification_status = $verification['verification_status'];
            $real_name = $verification['real_name'];
            $submitted_at = $verification['submitted_at'];
            $verified_at = $verification['verified_at'];

            // 根据实名认证表的状态确定是否已认证
            $is_verified = ($verification_status === 'approved');
        }
    } catch (PDOException $e) {
        // 实名认证表不存在，检查用户表的字段
        error_log("Realname verification table not found: " . $e->getMessage());
        $is_verified = isset($user['is_verified']) ? (bool)$user['is_verified'] : false;
    }

    // 如果实名认证表没有记录，但用户表有is_verified字段且为true，也认为已认证
    if (!$verification && isset($user['is_verified']) && $user['is_verified']) {
        $is_verified = true;
        $verification_status = 'approved';
    }

    $verification_level = isset($user['verification_level']) ? $user['verification_level'] : 'none';

    echo json_encode([
        'success' => true,
        'is_verified' => $is_verified,
        'verification_status' => $verification_status,
        'verification_level' => $verification_level,
        'real_name' => $real_name,
        'submitted_at' => $submitted_at,
        'verified_at' => $verified_at
    ]);

} catch (PDOException $e) {
    error_log("Database error in check_verification.php: " . $e->getMessage());
    echo json_encode([
        'success' => false,
        'message' => '数据库错误'
    ]);
} catch (Exception $e) {
    error_log("Error in check_verification.php: " . $e->getMessage());
    echo json_encode([
        'success' => false,
        'message' => '系统错误'
    ]);
}
?>
