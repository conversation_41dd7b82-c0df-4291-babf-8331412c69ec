<?php
/**
 * 智能回复规则管理页面
 */

session_start();

// 检查管理员登录状态
if (!isset($_SESSION['admin_id'])) {
    header('Location: ../login.php');
    exit;
}

$admin_id = $_SESSION['admin_id'];
$admin_username = $_SESSION['admin_username'] ?? '管理员';

// 处理AJAX请求
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    header('Content-Type: application/json; charset=utf-8');

    try {
        $pdo = new PDO(
            "mysql:host=localhost;dbname=quwanplanet;charset=utf8mb4",
            "quwanplanet",
            "nJmJm23FB4Xn6Fc3",
            [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC
            ]
        );

        switch ($_POST['action']) {
            case 'add_rule':
                $rule_name = trim($_POST['rule_name'] ?? '');
                $keywords = trim($_POST['keywords'] ?? '');
                $reply_content = trim($_POST['reply_content'] ?? '');
                $priority = intval($_POST['priority'] ?? 1);
                $category = trim($_POST['category'] ?? 'general');

                if (empty($keywords) || empty($reply_content)) {
                    echo json_encode(['success' => false, 'message' => '关键词和回复内容不能为空']);
                    exit;
                }

                // 处理关键词
                $keyword_array = array_map('trim', explode(',', $keywords));
                $keyword_array = array_filter($keyword_array);

                $stmt = $pdo->prepare("
                    INSERT INTO customer_service_replies
                    (rule_name, keywords, reply_content, priority, category, admin_id, created_at)
                    VALUES (?, ?, ?, ?, ?, ?, NOW())
                ");

                $stmt->execute([
                    $rule_name,
                    json_encode($keyword_array),
                    $reply_content,
                    $priority,
                    $category,
                    $admin_id
                ]);

                echo json_encode(['success' => true, 'message' => '规则添加成功']);
                exit;

            case 'edit_rule':
                $rule_id = intval($_POST['rule_id'] ?? 0);
                $rule_name = trim($_POST['rule_name'] ?? '');
                $keywords = trim($_POST['keywords'] ?? '');
                $reply_content = trim($_POST['reply_content'] ?? '');
                $priority = intval($_POST['priority'] ?? 1);
                $category = trim($_POST['category'] ?? 'general');
                $is_enabled = intval($_POST['is_enabled'] ?? 1);

                if (empty($keywords) || empty($reply_content)) {
                    echo json_encode(['success' => false, 'message' => '关键词和回复内容不能为空']);
                    exit;
                }

                // 处理关键词
                $keyword_array = array_map('trim', explode(',', $keywords));
                $keyword_array = array_filter($keyword_array);

                $stmt = $pdo->prepare("
                    UPDATE customer_service_replies
                    SET rule_name = ?, keywords = ?, reply_content = ?, priority = ?,
                        category = ?, is_enabled = ?, updated_at = NOW()
                    WHERE id = ?
                ");

                $stmt->execute([
                    $rule_name,
                    json_encode($keyword_array),
                    $reply_content,
                    $priority,
                    $category,
                    $is_enabled,
                    $rule_id
                ]);

                echo json_encode(['success' => true, 'message' => '规则更新成功']);
                exit;

            case 'delete_rule':
                $rule_id = intval($_POST['rule_id'] ?? 0);

                $stmt = $pdo->prepare("DELETE FROM customer_service_replies WHERE id = ?");
                $stmt->execute([$rule_id]);

                echo json_encode(['success' => true, 'message' => '规则删除成功']);
                exit;

            case 'toggle_rule':
                $rule_id = intval($_POST['rule_id'] ?? 0);
                $is_enabled = intval($_POST['is_enabled'] ?? 0);

                $stmt = $pdo->prepare("UPDATE customer_service_replies SET is_enabled = ? WHERE id = ?");
                $stmt->execute([$is_enabled, $rule_id]);

                echo json_encode(['success' => true, 'message' => '状态更新成功']);
                exit;

            case 'get_rule':
                $rule_id = intval($_POST['rule_id'] ?? 0);

                $stmt = $pdo->prepare("SELECT * FROM customer_service_replies WHERE id = ?");
                $stmt->execute([$rule_id]);
                $rule = $stmt->fetch();

                if ($rule) {
                    $keywords = json_decode($rule['keywords'], true);
                    $rule['keywords_text'] = implode(', ', $keywords);
                    echo json_encode(['success' => true, 'rule' => $rule]);
                } else {
                    echo json_encode(['success' => false, 'message' => '规则不存在']);
                }
                exit;

            default:
                echo json_encode(['success' => false, 'message' => '未知操作']);
                exit;
        }

    } catch (Exception $e) {
        echo json_encode(['success' => false, 'message' => '操作失败：' . $e->getMessage()]);
        exit;
    }
}

// 获取回复规则列表
try {
    $pdo = new PDO(
        "mysql:host=localhost;dbname=quwanplanet;charset=utf8mb4",
        "quwanplanet",
        "nJmJm23FB4Xn6Fc3",
        [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC
        ]
    );

    // 获取分页参数
    $page = intval($_GET['page'] ?? 1);
    $limit = 20;
    $offset = ($page - 1) * $limit;

    // 获取搜索参数
    $search = trim($_GET['search'] ?? '');
    $category = trim($_GET['category'] ?? '');

    // 构建查询条件
    $where_conditions = [];
    $params = [];

    if (!empty($search)) {
        $where_conditions[] = "(rule_name LIKE ? OR reply_content LIKE ? OR JSON_SEARCH(keywords, 'one', ?) IS NOT NULL)";
        $params[] = "%$search%";
        $params[] = "%$search%";
        $params[] = "%$search%";
    }

    if (!empty($category)) {
        $where_conditions[] = "category = ?";
        $params[] = $category;
    }

    $where_clause = !empty($where_conditions) ? 'WHERE ' . implode(' AND ', $where_conditions) : '';

    // 获取总数
    $count_sql = "SELECT COUNT(*) FROM customer_service_replies $where_clause";
    $stmt = $pdo->prepare($count_sql);
    $stmt->execute($params);
    $total_count = $stmt->fetchColumn();

    // 获取规则列表
    $sql = "
        SELECT * FROM customer_service_replies
        $where_clause
        ORDER BY priority DESC, id DESC
        LIMIT $limit OFFSET $offset
    ";
    $stmt = $pdo->prepare($sql);
    $stmt->execute($params);
    $rules = $stmt->fetchAll();

    // 获取分类列表
    $stmt = $pdo->query("SELECT DISTINCT category FROM customer_service_replies WHERE category IS NOT NULL ORDER BY category");
    $categories = $stmt->fetchAll(PDO::FETCH_COLUMN);

    $total_pages = ceil($total_count / $limit);

} catch (Exception $e) {
    $error_message = '数据库连接失败：' . $e->getMessage();
    $rules = [];
    $categories = [];
    $total_count = 0;
    $total_pages = 0;
}
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>回复规则管理 - 趣玩星球管理后台</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        .main-content {
            margin-left: 250px;
            padding: 20px;
        }
        .rule-card {
            background: white;
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            border-left: 4px solid #667eea;
            transition: all 0.3s ease;
        }
        .rule-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 16px rgba(0,0,0,0.15);
        }
        .rule-card.disabled {
            opacity: 0.6;
            border-left-color: #ccc;
        }
        .keyword-tag {
            background: #667eea;
            color: white;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 0.75rem;
            margin: 2px;
            display: inline-block;
        }
        .priority-badge {
            background: linear-gradient(135deg, #f59e0b, #f97316);
            color: white;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 0.75rem;
            font-weight: bold;
        }
        .category-badge {
            background: #10b981;
            color: white;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 0.75rem;
        }
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
        }
        .search-box {
            background: white;
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
    </style>
</head>
<body>
    <?php include '../includes/sidebar.php'; ?>

    <div class="main-content">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <div>
                <h2><i class="bi bi-chat-quote"></i> 回复规则管理</h2>
                <p class="text-muted mb-0">管理智能客服的自动回复规则和关键词触发</p>
            </div>
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="../dashboard.php">首页</a></li>
                    <li class="breadcrumb-item"><a href="index.php">智能客服管理</a></li>
                    <li class="breadcrumb-item active">回复规则管理</li>
                </ol>
            </nav>
        </div>

        <?php if (isset($error_message)): ?>
            <div class="alert alert-danger">
                <i class="bi bi-exclamation-triangle"></i> <?= htmlspecialchars($error_message) ?>
            </div>
        <?php endif; ?>

        <!-- 搜索和筛选 -->
        <div class="search-box">
            <form method="GET" class="row g-3">
                <div class="col-md-4">
                    <label class="form-label">搜索规则</label>
                    <input type="text" class="form-control" name="search"
                           value="<?= htmlspecialchars($search) ?>"
                           placeholder="搜索规则名称、关键词或回复内容">
                </div>
                <div class="col-md-3">
                    <label class="form-label">分类筛选</label>
                    <select class="form-select" name="category">
                        <option value="">全部分类</option>
                        <?php foreach ($categories as $cat): ?>
                            <option value="<?= htmlspecialchars($cat) ?>"
                                    <?= $category === $cat ? 'selected' : '' ?>>
                                <?= htmlspecialchars($cat) ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                </div>
                <div class="col-md-3 d-flex align-items-end">
                    <button type="submit" class="btn btn-primary me-2">
                        <i class="bi bi-search"></i> 搜索
                    </button>
                    <a href="?" class="btn btn-outline-secondary">
                        <i class="bi bi-arrow-clockwise"></i> 重置
                    </a>
                </div>
                <div class="col-md-2 d-flex align-items-end">
                    <button type="button" class="btn btn-success" onclick="showAddModal()">
                        <i class="bi bi-plus-lg"></i> 添加规则
                    </button>
                </div>
            </form>
        </div>

        <!-- 统计信息 -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="card text-center">
                    <div class="card-body">
                        <h5 class="card-title text-primary"><?= $total_count ?></h5>
                        <p class="card-text">总规则数</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card text-center">
                    <div class="card-body">
                        <h5 class="card-title text-success">
                            <?= count(array_filter($rules, function($r) { return $r['is_enabled']; })) ?>
                        </h5>
                        <p class="card-text">启用规则</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card text-center">
                    <div class="card-body">
                        <h5 class="card-title text-info"><?= count($categories) ?></h5>
                        <p class="card-text">分类数量</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card text-center">
                    <div class="card-body">
                        <h5 class="card-title text-warning"><?= $page ?>/<?= max(1, $total_pages) ?></h5>
                        <p class="card-text">当前页</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- 规则列表 -->
        <div class="rules-container">
            <?php if (!empty($rules)): ?>
                <?php foreach ($rules as $rule): ?>
                    <?php
                    $keywords = json_decode($rule['keywords'], true) ?: [];
                    ?>
                    <div class="rule-card <?= $rule['is_enabled'] ? '' : 'disabled' ?>">
                        <div class="d-flex justify-content-between align-items-start mb-3">
                            <div class="flex-grow-1">
                                <div class="d-flex align-items-center gap-2 mb-2">
                                    <h6 class="mb-0"><?= htmlspecialchars($rule['rule_name'] ?: '未命名规则') ?></h6>
                                    <span class="priority-badge">优先级 <?= $rule['priority'] ?></span>
                                    <?php if ($rule['category']): ?>
                                        <span class="category-badge"><?= htmlspecialchars($rule['category']) ?></span>
                                    <?php endif; ?>
                                    <div class="form-check form-switch">
                                        <input class="form-check-input" type="checkbox"
                                               <?= $rule['is_enabled'] ? 'checked' : '' ?>
                                               onchange="toggleRule(<?= $rule['id'] ?>, this.checked)">
                                    </div>
                                </div>
                                <div class="mb-2">
                                    <strong>关键词：</strong>
                                    <?php foreach ($keywords as $keyword): ?>
                                        <span class="keyword-tag"><?= htmlspecialchars($keyword) ?></span>
                                    <?php endforeach; ?>
                                </div>
                                <div class="mb-2">
                                    <strong>回复内容：</strong>
                                    <div class="text-muted" style="max-height: 100px; overflow-y: auto;">
                                        <?= nl2br(htmlspecialchars($rule['reply_content'])) ?>
                                    </div>
                                </div>
                                <small class="text-muted">
                                    创建时间：<?= date('Y-m-d H:i:s', strtotime($rule['created_at'])) ?>
                                    <?php if ($rule['usage_count'] > 0): ?>
                                        | 使用次数：<?= $rule['usage_count'] ?>
                                    <?php endif; ?>
                                </small>
                            </div>
                            <div class="d-flex flex-column gap-1">
                                <button class="btn btn-sm btn-outline-primary" onclick="editRule(<?= $rule['id'] ?>)">
                                    <i class="bi bi-pencil"></i>
                                </button>
                                <button class="btn btn-sm btn-outline-danger" onclick="deleteRule(<?= $rule['id'] ?>)">
                                    <i class="bi bi-trash"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>
            <?php else: ?>
                <div class="text-center py-5">
                    <i class="bi bi-chat-quote" style="font-size: 3rem; color: #ccc;"></i>
                    <h5 class="mt-3 text-muted">暂无回复规则</h5>
                    <p class="text-muted">点击"添加规则"按钮创建第一个回复规则</p>
                    <button class="btn btn-primary" onclick="showAddModal()">
                        <i class="bi bi-plus-lg"></i> 添加规则
                    </button>
                </div>
            <?php endif; ?>
        </div>

        <!-- 分页 -->
        <?php if ($total_pages > 1): ?>
            <nav aria-label="规则分页">
                <ul class="pagination justify-content-center">
                    <?php if ($page > 1): ?>
                        <li class="page-item">
                            <a class="page-link" href="?page=<?= $page - 1 ?>&search=<?= urlencode($search) ?>&category=<?= urlencode($category) ?>">
                                <i class="bi bi-chevron-left"></i>
                            </a>
                        </li>
                    <?php endif; ?>

                    <?php for ($i = max(1, $page - 2); $i <= min($total_pages, $page + 2); $i++): ?>
                        <li class="page-item <?= $i === $page ? 'active' : '' ?>">
                            <a class="page-link" href="?page=<?= $i ?>&search=<?= urlencode($search) ?>&category=<?= urlencode($category) ?>">
                                <?= $i ?>
                            </a>
                        </li>
                    <?php endfor; ?>

                    <?php if ($page < $total_pages): ?>
                        <li class="page-item">
                            <a class="page-link" href="?page=<?= $page + 1 ?>&search=<?= urlencode($search) ?>&category=<?= urlencode($category) ?>">
                                <i class="bi bi-chevron-right"></i>
                            </a>
                        </li>
                    <?php endif; ?>
                </ul>
            </nav>
        <?php endif; ?>
    </div>

    <!-- 添加/编辑规则模态框 -->
    <div class="modal fade" id="ruleModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="ruleModalTitle">添加回复规则</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="ruleForm">
                        <input type="hidden" id="ruleId" name="rule_id">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">规则名称</label>
                                    <input type="text" class="form-control" id="ruleName" name="rule_name"
                                           placeholder="给规则起个名字">
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="mb-3">
                                    <label class="form-label">优先级</label>
                                    <input type="number" class="form-control" id="rulePriority" name="priority"
                                           value="1" min="1" max="100">
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="mb-3">
                                    <label class="form-label">分类</label>
                                    <input type="text" class="form-control" id="ruleCategory" name="category"
                                           placeholder="general" value="general">
                                </div>
                            </div>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">触发关键词</label>
                            <input type="text" class="form-control" id="ruleKeywords" name="keywords"
                                   placeholder="用逗号分隔多个关键词，如：账号,登录,密码" required>
                            <div class="form-text">用户消息包含任一关键词时会触发此规则</div>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">回复内容</label>
                            <textarea class="form-control" id="ruleReply" name="reply_content"
                                      rows="6" placeholder="输入机器人的回复内容..." required></textarea>
                            <div class="form-text">支持换行，可以使用表情符号</div>
                        </div>
                        <div class="mb-3">
                            <div class="form-check form-switch">
                                <input class="form-check-input" type="checkbox" id="ruleEnabled" name="is_enabled" value="1" checked>
                                <label class="form-check-label">启用此规则</label>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" onclick="saveRule()">保存规则</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        let ruleModal;

        document.addEventListener('DOMContentLoaded', function() {
            ruleModal = new bootstrap.Modal(document.getElementById('ruleModal'));
        });

        // 显示添加规则模态框
        function showAddModal() {
            document.getElementById('ruleModalTitle').textContent = '添加回复规则';
            document.getElementById('ruleForm').reset();
            document.getElementById('ruleId').value = '';
            document.getElementById('ruleEnabled').checked = true;
            ruleModal.show();
        }

        // 编辑规则
        async function editRule(ruleId) {
            try {
                const formData = new FormData();
                formData.append('action', 'get_rule');
                formData.append('rule_id', ruleId);

                const response = await fetch('', {
                    method: 'POST',
                    body: formData
                });

                const data = await response.json();

                if (data.success) {
                    const rule = data.rule;
                    document.getElementById('ruleModalTitle').textContent = '编辑回复规则';
                    document.getElementById('ruleId').value = rule.id;
                    document.getElementById('ruleName').value = rule.rule_name || '';
                    document.getElementById('ruleKeywords').value = rule.keywords_text || '';
                    document.getElementById('ruleReply').value = rule.reply_content || '';
                    document.getElementById('rulePriority').value = rule.priority || 1;
                    document.getElementById('ruleCategory').value = rule.category || 'general';
                    document.getElementById('ruleEnabled').checked = rule.is_enabled == 1;

                    ruleModal.show();
                } else {
                    alert('获取规则信息失败：' + data.message);
                }
            } catch (error) {
                alert('获取规则信息失败：' + error.message);
            }
        }

        // 保存规则
        async function saveRule() {
            const form = document.getElementById('ruleForm');
            const formData = new FormData(form);

            const ruleId = document.getElementById('ruleId').value;
            formData.append('action', ruleId ? 'edit_rule' : 'add_rule');

            // 处理复选框
            if (!document.getElementById('ruleEnabled').checked) {
                formData.set('is_enabled', '0');
            }

            try {
                const response = await fetch('', {
                    method: 'POST',
                    body: formData
                });

                const data = await response.json();

                if (data.success) {
                    alert(data.message);
                    ruleModal.hide();
                    location.reload();
                } else {
                    alert('保存失败：' + data.message);
                }
            } catch (error) {
                alert('保存失败：' + error.message);
            }
        }

        // 切换规则状态
        async function toggleRule(ruleId, isEnabled) {
            try {
                const formData = new FormData();
                formData.append('action', 'toggle_rule');
                formData.append('rule_id', ruleId);
                formData.append('is_enabled', isEnabled ? '1' : '0');

                const response = await fetch('', {
                    method: 'POST',
                    body: formData
                });

                const data = await response.json();

                if (data.success) {
                    // 更新卡片样式
                    const card = event.target.closest('.rule-card');
                    if (isEnabled) {
                        card.classList.remove('disabled');
                    } else {
                        card.classList.add('disabled');
                    }
                } else {
                    alert('状态更新失败：' + data.message);
                    // 恢复复选框状态
                    event.target.checked = !isEnabled;
                }
            } catch (error) {
                alert('状态更新失败：' + error.message);
                event.target.checked = !isEnabled;
            }
        }

        // 删除规则
        async function deleteRule(ruleId) {
            if (!confirm('确定要删除这个回复规则吗？此操作不可恢复。')) {
                return;
            }

            try {
                const formData = new FormData();
                formData.append('action', 'delete_rule');
                formData.append('rule_id', ruleId);

                const response = await fetch('', {
                    method: 'POST',
                    body: formData
                });

                const data = await response.json();

                if (data.success) {
                    alert(data.message);
                    location.reload();
                } else {
                    alert('删除失败：' + data.message);
                }
            } catch (error) {
                alert('删除失败：' + error.message);
            }
        }
    </script>
</body>
</html>