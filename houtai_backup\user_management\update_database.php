<?php
// 数据库更新脚本
require_once '../db_config.php';

try {
    $pdo = getDbConnection();
    
    echo "<h2>开始执行数据库更新...</h2>\n";
    
    // 读取SQL文件内容
    $sqlFile = __DIR__ . '/additional_tables.sql';
    if (!file_exists($sqlFile)) {
        throw new Exception("SQL文件不存在: $sqlFile");
    }
    
    $sql = file_get_contents($sqlFile);
    if ($sql === false) {
        throw new Exception("无法读取SQL文件");
    }
    
    // 分割SQL语句
    $statements = array_filter(
        array_map('trim', explode(';', $sql)),
        function($stmt) {
            return !empty($stmt) && !preg_match('/^\s*--/', $stmt);
        }
    );
    
    $successCount = 0;
    $errorCount = 0;
    
    foreach ($statements as $statement) {
        if (empty(trim($statement))) continue;
        
        try {
            $pdo->exec($statement);
            $successCount++;
            
            // 提取操作类型
            if (preg_match('/^\s*(CREATE TABLE|ALTER TABLE|INSERT)/i', $statement, $matches)) {
                echo "<p style='color: green;'>✅ 成功执行: " . $matches[1] . "</p>\n";
            } else {
                echo "<p style='color: green;'>✅ 成功执行SQL语句</p>\n";
            }
            
        } catch (PDOException $e) {
            $errorCount++;
            echo "<p style='color: orange;'>⚠️ 跳过: " . $e->getMessage() . "</p>\n";
        }
    }
    
    echo "<h3>执行完成</h3>\n";
    echo "<p>成功: $successCount 条</p>\n";
    echo "<p>跳过: $errorCount 条</p>\n";
    
    // 验证表是否创建成功
    echo "<h3>验证新表</h3>\n";
    $tables = [
        'user_badges', 'user_inventory', 'user_checkins', 'user_follows',
        'user_favorites', 'user_earnings', 'system_messages', 'sms_logs',
        'user_level_config', 'admin_operation_logs'
    ];
    
    foreach ($tables as $table) {
        try {
            $stmt = $pdo->query("SHOW TABLES LIKE '$table'");
            if ($stmt->rowCount() > 0) {
                echo "<p style='color: green;'>✅ 表 $table 存在</p>\n";
            } else {
                echo "<p style='color: red;'>❌ 表 $table 不存在</p>\n";
            }
        } catch (Exception $e) {
            echo "<p style='color: red;'>❌ 检查表 $table 失败: " . $e->getMessage() . "</p>\n";
        }
    }
    
    // 验证新字段
    echo "<h3>验证新字段</h3>\n";
    $fields = [
        'level', 'experience', 'points', 'followers_count', 'following_count',
        'likes_received', 'favorites_count', 'is_companion', 'total_earnings'
    ];
    
    foreach ($fields as $field) {
        try {
            $stmt = $pdo->query("SHOW COLUMNS FROM users LIKE '$field'");
            if ($stmt->rowCount() > 0) {
                echo "<p style='color: green;'>✅ 字段 users.$field 存在</p>\n";
            } else {
                echo "<p style='color: red;'>❌ 字段 users.$field 不存在</p>\n";
            }
        } catch (Exception $e) {
            echo "<p style='color: red;'>❌ 检查字段 users.$field 失败: " . $e->getMessage() . "</p>\n";
        }
    }
    
} catch (Exception $e) {
    echo "<h3 style='color: red;'>错误</h3>\n";
    echo "<p style='color: red;'>" . $e->getMessage() . "</p>\n";
}
?>

<style>
body {
    font-family: Arial, sans-serif;
    max-width: 800px;
    margin: 20px auto;
    padding: 20px;
    background: #f5f5f5;
}
h2, h3 {
    color: #333;
}
p {
    margin: 5px 0;
    padding: 5px;
    background: white;
    border-radius: 4px;
}
</style>
