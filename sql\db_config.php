<?php
/**
 * 数据库连接配置文件
 */

// 数据库连接配置
$db_config = [
    'host' => 'localhost',
    'username' => 'quwanplanet',
    'password' => 'nJmJm23FB4Xn6Fc3',
    'dbname' => 'quwanplanet',
    'port' => 3306,
    'charset' => 'utf8mb4'
];

/**
 * 获取数据库连接
 *
 * @return PDO|null 返回PDO连接对象，连接失败返回null
 */
function getDbConnection() {
    global $db_config;

    try {
        $dsn = "mysql:host={$db_config['host']};dbname={$db_config['dbname']};charset={$db_config['charset']};port={$db_config['port']}";
        $pdo = new PDO($dsn, $db_config['username'], $db_config['password']);
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        return $pdo;
    } catch (PDOException $e) {
        // 记录错误日志
        error_log("数据库连接失败: " . $e->getMessage());
        return null;
    }
}

/**
 * 生成唯一的趣玩星球ID
 *
 * @return string 7位数字ID
 */
function generateQuwanplanetId() {
    $pdo = getDbConnection();
    if (!$pdo) {
        return false;
    }

    $attempts = 0;
    $max_attempts = 10;

    while ($attempts < $max_attempts) {
        // 生成7位数字ID，不以0开头
        $id = mt_rand(1000000, 9999999);

        // 检查是否有重复数字或特殊模式
        $digits = str_split((string)$id);
        $has_repeating = false;

        // 检查是否有连续3个相同数字
        for ($i = 0; $i < 5; $i++) {
            if ($digits[$i] == $digits[$i+1] && $digits[$i+1] == $digits[$i+2]) {
                $has_repeating = true;
                break;
            }
        }

        // 如果有重复模式，重新生成
        if ($has_repeating) {
            $attempts++;
            continue;
        }

        // 检查数据库中是否已存在
        $stmt = $pdo->prepare("SELECT COUNT(*) FROM users WHERE quwan_id = :id");
        $stmt->execute(['id' => $id]);
        $exists = (int)$stmt->fetchColumn();

        if ($exists === 0) {
            return (string)$id;
        }

        $attempts++;
    }

    // 如果多次尝试后仍未生成有效ID，返回错误
    error_log("无法生成唯一的趣玩星球ID");
    return false;
}
