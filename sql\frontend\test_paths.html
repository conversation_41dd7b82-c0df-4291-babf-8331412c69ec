<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>路径测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-item {
            margin: 15px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .test-item h4 {
            margin: 0 0 10px 0;
            color: #333;
        }
        .status {
            padding: 5px 10px;
            border-radius: 4px;
            font-weight: bold;
            margin-left: 10px;
        }
        .status.success { background: #d4edda; color: #155724; }
        .status.error { background: #f8d7da; color: #721c24; }
        .status.loading { background: #d1ecf1; color: #0c5460; }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        .url {
            font-family: monospace;
            background: #f8f9fa;
            padding: 5px;
            border-radius: 3px;
            margin: 5px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 路径测试工具</h1>
        <p>测试各种API路径是否可访问</p>

        <div class="test-item">
            <h4>1. 检查通知API (前台代理)</h4>
            <div class="url">api/check_notifications_proxy.php</div>
            <button onclick="testPath('api/check_notifications_proxy.php?user_id=4', 'check1')">测试</button>
            <span id="check1" class="status loading">等待测试</span>
        </div>

        <div class="test-item">
            <h4>2. 检查通知API (后台直连 - 预期失败)</h4>
            <div class="url">https://vansmrz.vancrest.xyz/houtai_backup/user_management/check_notifications.php</div>
            <button onclick="testPath('https://vansmrz.vancrest.xyz/houtai_backup/user_management/check_notifications.php?user_id=4', 'check2')">测试</button>
            <span id="check2" class="status loading">等待测试</span>
        </div>

        <div class="test-item">
            <h4>3. 发送验证码API (前台代理)</h4>
            <div class="url">api/send_verification_code_proxy.php</div>
            <button onclick="testSendCode('api/send_verification_code_proxy.php', 'send1')">测试</button>
            <span id="send1" class="status loading">等待测试</span>
        </div>

        <div class="test-item">
            <h4>4. 发送验证码API (后台直连 - 预期失败)</h4>
            <div class="url">https://vansmrz.vancrest.xyz/houtai_backup/user_management/send_verification_code_debug.php</div>
            <button onclick="testSendCode('https://vansmrz.vancrest.xyz/houtai_backup/user_management/send_verification_code_debug.php', 'send2')">测试</button>
            <span id="send2" class="status loading">等待测试</span>
        </div>

        <div class="test-item">
            <h4>5. SSE通知API (相对路径)</h4>
            <div class="url">api/realtime_notifications.php</div>
            <button onclick="testSSE('api/realtime_notifications.php?user_id=4', 'sse1')">测试</button>
            <span id="sse1" class="status loading">等待测试</span>
        </div>

        <div class="test-item">
            <h4>6. SSE通知API (绝对路径)</h4>
            <div class="url">/frontend/api/realtime_notifications.php</div>
            <button onclick="testSSE('/frontend/api/realtime_notifications.php?user_id=4', 'sse2')">测试</button>
            <span id="sse2" class="status loading">等待测试</span>
        </div>

        <div class="test-item">
            <h4>7. 当前页面信息</h4>
            <div id="pageInfo"></div>
        </div>
    </div>

    <script>
        // 显示当前页面信息
        document.getElementById('pageInfo').innerHTML = `
            <strong>当前URL:</strong> ${window.location.href}<br>
            <strong>协议:</strong> ${window.location.protocol}<br>
            <strong>主机:</strong> ${window.location.host}<br>
            <strong>路径:</strong> ${window.location.pathname}<br>
            <strong>基础URL:</strong> ${window.location.origin}
        `;

        // 测试普通API路径
        async function testPath(url, statusId) {
            const statusEl = document.getElementById(statusId);
            statusEl.textContent = '测试中...';
            statusEl.className = 'status loading';

            try {
                const response = await fetch(url);
                const text = await response.text();

                if (response.ok) {
                    try {
                        JSON.parse(text);
                        statusEl.textContent = '✅ 成功 (JSON)';
                        statusEl.className = 'status success';
                    } catch (e) {
                        statusEl.textContent = '⚠️ 成功 (非JSON)';
                        statusEl.className = 'status success';
                    }
                } else {
                    statusEl.textContent = `❌ HTTP ${response.status}`;
                    statusEl.className = 'status error';
                }

                console.log(`测试 ${url}:`, {
                    status: response.status,
                    ok: response.ok,
                    text: text.substring(0, 200)
                });

            } catch (error) {
                statusEl.textContent = `❌ ${error.message}`;
                statusEl.className = 'status error';
                console.error(`测试 ${url} 失败:`, error);
            }
        }

        // 测试发送验证码API
        async function testSendCode(url, statusId) {
            const statusEl = document.getElementById(statusId);
            statusEl.textContent = '测试中...';
            statusEl.className = 'status loading';

            const testData = {
                user_id: 4,
                phone: '13800138000',
                type: 'admin_send',
                note: '路径测试',
                expiry: 5
            };

            try {
                const response = await fetch(url, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(testData)
                });

                const text = await response.text();

                if (response.ok) {
                    try {
                        const data = JSON.parse(text);
                        if (data.success) {
                            statusEl.textContent = '✅ 发送成功';
                            statusEl.className = 'status success';
                        } else {
                            statusEl.textContent = `⚠️ ${data.message}`;
                            statusEl.className = 'status error';
                        }
                    } catch (e) {
                        statusEl.textContent = '⚠️ 响应非JSON';
                        statusEl.className = 'status error';
                    }
                } else {
                    statusEl.textContent = `❌ HTTP ${response.status}`;
                    statusEl.className = 'status error';
                }

                console.log(`测试发送 ${url}:`, {
                    status: response.status,
                    ok: response.ok,
                    text: text.substring(0, 200)
                });

            } catch (error) {
                statusEl.textContent = `❌ ${error.message}`;
                statusEl.className = 'status error';
                console.error(`测试发送 ${url} 失败:`, error);
            }
        }

        // 测试SSE连接
        function testSSE(url, statusId) {
            const statusEl = document.getElementById(statusId);
            statusEl.textContent = '连接中...';
            statusEl.className = 'status loading';

            try {
                const eventSource = new EventSource(url);

                const timeout = setTimeout(() => {
                    eventSource.close();
                    statusEl.textContent = '⏰ 连接超时';
                    statusEl.className = 'status error';
                }, 10000);

                eventSource.onopen = function(event) {
                    clearTimeout(timeout);
                    statusEl.textContent = '✅ SSE连接成功';
                    statusEl.className = 'status success';
                    console.log(`SSE连接成功: ${url}`);

                    // 5秒后关闭连接
                    setTimeout(() => {
                        eventSource.close();
                    }, 5000);
                };

                eventSource.onmessage = function(event) {
                    console.log(`SSE消息 ${url}:`, event.data);
                };

                eventSource.onerror = function(event) {
                    clearTimeout(timeout);
                    eventSource.close();
                    statusEl.textContent = '❌ SSE连接失败';
                    statusEl.className = 'status error';
                    console.error(`SSE连接失败: ${url}`, event);
                };

            } catch (error) {
                statusEl.textContent = `❌ ${error.message}`;
                statusEl.className = 'status error';
                console.error(`SSE测试失败 ${url}:`, error);
            }
        }

        // 自动测试所有路径
        function testAll() {
            setTimeout(() => testPath('../houtai_backup/user_management/check_notifications.php?user_id=4', 'check1'), 500);
            setTimeout(() => testPath('/houtai_backup/user_management/check_notifications.php?user_id=4', 'check2'), 1000);
            setTimeout(() => testSSE('api/realtime_notifications.php?user_id=4', 'sse1'), 1500);
            setTimeout(() => testSSE('/frontend/api/realtime_notifications.php?user_id=4', 'sse2'), 2000);
        }

        // 页面加载后提示
        window.addEventListener('load', function() {
            console.log('路径测试页面加载完成');
            console.log('点击各个测试按钮来检查API路径是否正确');
        });
    </script>
</body>
</html>
