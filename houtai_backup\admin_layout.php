<?php
/**
 * 管理后台布局文件
 */

function startAdminPage($title, $breadcrumb = '') {
    $admin_name = $_SESSION['admin_name'] ?? '管理员';
    echo '<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>' . htmlspecialchars($title) . ' - 趣玩星球管理后台</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif; background: #f5f7fa; color: #333; line-height: 1.6; }
        .admin-layout { display: flex; min-height: 100vh; }
        .sidebar { width: 260px; background: white; box-shadow: 2px 0 10px rgba(0,0,0,0.1); position: fixed; height: 100vh; overflow-y: auto; z-index: 1000; }
        .main-content { flex: 1; margin-left: 260px; background: #f5f7fa; }
        .top-header { background: white; padding: 16px 24px; box-shadow: 0 1px 3px rgba(0,0,0,0.1); display: flex; justify-content: space-between; align-items: center; }
        .page-header h1 { font-size: 24px; font-weight: 600; color: #1f2937; }
        .breadcrumb { font-size: 14px; color: #6b7280; margin-top: 4px; }
        .user-info { display: flex; align-items: center; gap: 12px; }
        .user-avatar { width: 36px; height: 36px; border-radius: 50%; }
        .admin-content { padding: 24px; }
        .btn { display: inline-flex; align-items: center; gap: 6px; padding: 8px 16px; border: none; border-radius: 6px; font-size: 14px; font-weight: 500; cursor: pointer; text-decoration: none; transition: all 0.2s; }
        .btn-primary { background: #6F7BF5; color: white; }
        .btn-primary:hover { background: #5a67d8; }
        .btn-success { background: #10b981; color: white; }
        .btn-success:hover { background: #059669; }
        .btn-danger { background: #ef4444; color: white; }
        .btn-danger:hover { background: #dc2626; }
        .btn-secondary { background: #6b7280; color: white; }
        .btn-secondary:hover { background: #4b5563; }
        .btn-sm { padding: 6px 12px; font-size: 12px; }
        .form-control { width: 100%; padding: 8px 12px; border: 1px solid #d1d5db; border-radius: 6px; font-size: 14px; }
        .form-control:focus { outline: none; border-color: #6F7BF5; box-shadow: 0 0 0 3px rgba(111, 123, 245, 0.1); }
        .table-container { background: white; border-radius: 8px; overflow: hidden; box-shadow: 0 1px 3px rgba(0,0,0,0.1); }
        .data-table { width: 100%; border-collapse: collapse; }
        .data-table th, .data-table td { padding: 12px; text-align: left; border-bottom: 1px solid #e5e7eb; }
        .data-table th { background: #f9fafb; font-weight: 600; color: #374151; }
        .data-table tr:hover { background: #f9fafb; }
        .text-center { text-align: center; }
        .pagination-container { display: flex; justify-content: space-between; align-items: center; margin-top: 20px; padding: 16px; background: white; border-radius: 8px; }
        .pagination { display: flex; gap: 4px; }
        .page-btn { padding: 8px 12px; border: 1px solid #d1d5db; background: white; color: #374151; text-decoration: none; border-radius: 4px; transition: all 0.2s; }
        .page-btn:hover { background: #f3f4f6; }
        .page-btn.active { background: #6F7BF5; color: white; border-color: #6F7BF5; }

        /* 侧边栏样式 */
        .sidebar-header { padding: 24px 20px; border-bottom: 1px solid #E5E7EB; background: linear-gradient(135deg, #6F7BF5 0%, #9C88FF 100%); }
        .logo { display: flex; align-items: center; gap: 12px; }
        .logo-icon { width: 40px; height: 40px; border-radius: 8px; overflow: hidden; }
        .logo-image { width: 100%; height: 100%; object-fit: cover; }
        .logo-text h2 { color: white; font-size: 18px; font-weight: 700; margin: 0; }
        .logo-text p { color: rgba(255,255,255,0.8); font-size: 12px; margin: 0; }

        .sidebar-nav { padding: 20px 0; }
        .nav-list { list-style: none; }
        .nav-item { margin-bottom: 4px; }
        .nav-link { display: flex; align-items: center; gap: 12px; padding: 12px 20px; color: #6B7280; text-decoration: none; transition: all 0.2s; position: relative; }
        .nav-link:hover { background: rgba(111, 123, 245, 0.1); color: #6F7BF5; }
        .nav-item.active .nav-link { background: rgba(111, 123, 245, 0.1); color: #6F7BF5; font-weight: 600; }
        .nav-item.active .nav-link::before { content: ""; position: absolute; left: 0; top: 0; bottom: 0; width: 3px; background: #6F7BF5; }
        .nav-link-text { flex: 1; }
        .submenu-arrow { transition: transform 0.2s; }
        .nav-item.expanded .submenu-arrow { transform: rotate(90deg); }

        .submenu { list-style: none; max-height: 0; overflow: hidden; transition: max-height 0.3s ease; }
        .submenu.show { max-height: 500px; }
        .submenu-item { margin-bottom: 2px; }
        .submenu-link { display: flex; align-items: center; gap: 12px; padding: 10px 20px 10px 52px; color: #9CA3AF; text-decoration: none; transition: all 0.2s; font-size: 14px; }
        .submenu-link:hover { background: rgba(111, 123, 245, 0.05); color: #6F7BF5; }
        .submenu-item.active .submenu-link { background: rgba(111, 123, 245, 0.1); color: #6F7BF5; font-weight: 500; }
        .submenu-link-text { flex: 1; }

        .nav-badge { background: #EF4444; color: white; font-size: 10px; padding: 2px 6px; border-radius: 10px; min-width: 18px; text-align: center; }

        @media (max-width: 768px) {
            .sidebar { transform: translateX(-100%); transition: transform 0.3s; }
            .sidebar.mobile-open { transform: translateX(0); }
            .main-content { margin-left: 0; }
        }
    </style>
</head>
<body>
    <div class="admin-layout">
        <div class="sidebar" id="sidebar">';

        include_once __DIR__ . '/includes/sidebar.php';

        echo '</div>
        <div class="main-content">
            <div class="top-header">
                <div class="page-header">
                    <h1>' . htmlspecialchars($title) . '</h1>';

        if ($breadcrumb) {
            echo '<div class="breadcrumb">' . htmlspecialchars($breadcrumb) . '</div>';
        }

        echo '</div>
                <div class="user-info">
                    <span>' . htmlspecialchars($admin_name) . '</span>
                </div>
            </div>';
}

function endAdminPage() {
    echo '        </div>
    </div>
    <script>
        function toggleSidebar() {
            document.getElementById("sidebar").classList.toggle("mobile-open");
        }
    </script>
</body>
</html>';
}
?>
