<?php
/**
 * 新版主菜单页面
 * 趣玩星球管理后台 v2.0
 */

session_start();

// 简单的登录检查
if (!isset($_SESSION['admin_id'])) {
    header('Location: login.php');
    exit;
}

$admin_name = $_SESSION['admin_name'] ?? '管理员';
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>管理后台主菜单 - 趣玩星球</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        :root {
            --primary-color: #40E0D0;
            --primary-light: #AFFBF2;
            --secondary-color: #667eea;
            --success-color: #10B981;
            --warning-color: #F59E0B;
            --error-color: #EF4444;
            --gray-100: #F3F4F6;
            --gray-600: #4B5563;
            --gray-800: #1F2937;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            min-height: 100vh;
        }

        .top-nav {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 16px;
            padding: 16px 24px;
            margin-bottom: 24px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
        }

        .brand {
            display: flex;
            align-items: center;
            gap: 12px;
            font-size: 1.25rem;
            font-weight: 700;
            color: var(--gray-800);
        }

        .brand-icon {
            width: 32px;
            height: 32px;
            background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
        }

        .user-info {
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .user-avatar {
            width: 36px;
            height: 36px;
            background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: 600;
        }

        .main-content {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 16px;
            padding: 32px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }

        .welcome {
            text-align: center;
            margin-bottom: 32px;
        }

        .welcome h1 {
            font-size: 2.5rem;
            font-weight: 700;
            background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            margin-bottom: 8px;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 32px;
        }

        .stat-card {
            background: white;
            border-radius: 16px;
            padding: 24px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .stat-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
        }

        .stat-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }

        .stat-icon {
            width: 48px;
            height: 48px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 20px;
            color: white;
        }

        .stat-number {
            font-size: 2rem;
            font-weight: 700;
            color: var(--gray-800);
            margin-bottom: 4px;
        }

        .stat-label {
            font-size: 0.875rem;
            color: var(--gray-600);
        }

        .modules-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 24px;
        }

        .module-card {
            background: white;
            border-radius: 16px;
            padding: 24px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
            transition: all 0.3s ease;
        }

        .module-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
        }

        .module-header {
            display: flex;
            align-items: center;
            gap: 16px;
            margin-bottom: 20px;
            padding-bottom: 16px;
            border-bottom: 2px solid var(--gray-100);
        }

        .module-icon {
            width: 56px;
            height: 56px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            color: white;
        }

        .function-item {
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 12px;
            border-radius: 12px;
            background: var(--gray-100);
            text-decoration: none;
            color: var(--gray-800);
            transition: all 0.3s ease;
            margin-bottom: 8px;
        }

        .function-item:hover {
            background: rgba(64, 224, 208, 0.1);
            transform: translateX(4px);
            text-decoration: none;
            color: var(--gray-800);
        }

        .function-icon {
            width: 32px;
            height: 32px;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 14px;
            color: white;
            background: linear-gradient(135deg, var(--secondary-color), #764ba2);
        }

        .btn {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            padding: 8px 16px;
            border: none;
            border-radius: 8px;
            text-decoration: none;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .btn-danger {
            background: linear-gradient(135deg, var(--error-color), #F87171);
            color: white;
        }

        .btn-danger:hover {
            transform: translateY(-2px);
            text-decoration: none;
            color: white;
        }

        @media (max-width: 768px) {
            .container {
                padding: 16px;
            }

            .top-nav {
                flex-direction: column;
                gap: 16px;
            }

            .stats-grid,
            .modules-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 顶部导航 -->
        <nav class="top-nav">
            <div class="brand">
                <div class="brand-icon">
                    <i class="fas fa-star"></i>
                </div>
                <span>趣玩星球管理后台</span>
            </div>

            <div class="user-info">
                <div class="user-avatar">
                    <?php echo mb_substr($admin_name, 0, 1, 'UTF-8'); ?>
                </div>
                <span><?php echo htmlspecialchars($admin_name); ?></span>
                <a href="logout.php" class="btn btn-danger">
                    <i class="fas fa-sign-out-alt"></i>
                    退出
                </a>
            </div>
        </nav>

        <!-- 主内容 -->
        <main class="main-content">
            <!-- 欢迎区域 -->
            <div class="welcome">
                <h1>欢迎来到趣玩星球管理后台</h1>
                <p>现代化、年轻化的管理系统，让管理变得简单高效</p>
            </div>

            <!-- 统计卡片 -->
            <div class="stats-grid">
                <div class="stat-card" onclick="location.href='new_user_management.php'">
                    <div class="stat-header">
                        <div class="stat-icon" style="background: linear-gradient(135deg, var(--primary-color), var(--primary-light));">
                            <i class="fas fa-users"></i>
                        </div>
                    </div>
                    <div class="stat-number">--</div>
                    <div class="stat-label">总用户数</div>
                </div>

                <div class="stat-card" onclick="location.href='verification_list.php'">
                    <div class="stat-header">
                        <div class="stat-icon" style="background: linear-gradient(135deg, var(--warning-color), #FBBF24);">
                            <i class="fas fa-clock"></i>
                        </div>
                    </div>
                    <div class="stat-number">--</div>
                    <div class="stat-label">待审核认证</div>
                </div>

                <div class="stat-card">
                    <div class="stat-header">
                        <div class="stat-icon" style="background: linear-gradient(135deg, var(--success-color), #34D399);">
                            <i class="fas fa-check-circle"></i>
                        </div>
                    </div>
                    <div class="stat-number">100%</div>
                    <div class="stat-label">系统状态</div>
                </div>
            </div>

            <!-- 功能模块 -->
            <div class="modules-grid">
                <!-- 用户管理 -->
                <div class="module-card">
                    <div class="module-header">
                        <div class="module-icon" style="background: linear-gradient(135deg, var(--primary-color), var(--primary-light));">
                            <i class="fas fa-users"></i>
                        </div>
                        <div>
                            <h3>用户管理</h3>
                            <p>管理平台用户信息和权限</p>
                        </div>
                    </div>
                    <div>
                        <a href="new_user_management.php" class="function-item">
                            <div class="function-icon">
                                <i class="fas fa-user-cog"></i>
                            </div>
                            <div>
                                <div style="font-weight: 600;">用户列表</div>
                                <div style="font-size: 12px; color: var(--gray-600);">查看和管理用户信息</div>
                            </div>
                        </a>
                    </div>
                </div>

                <!-- 实名认证 -->
                <div class="module-card">
                    <div class="module-header">
                        <div class="module-icon" style="background: linear-gradient(135deg, var(--warning-color), #FBBF24);">
                            <i class="fas fa-id-card"></i>
                        </div>
                        <div>
                            <h3>实名认证</h3>
                            <p>审核用户实名认证申请</p>
                        </div>
                    </div>
                    <div>
                        <a href="verification_list.php" class="function-item">
                            <div class="function-icon">
                                <i class="fas fa-list-alt"></i>
                            </div>
                            <div>
                                <div style="font-weight: 600;">认证列表</div>
                                <div style="font-size: 12px; color: var(--gray-600);">查看所有认证申请</div>
                            </div>
                        </a>
                    </div>
                </div>

                <!-- 系统管理 -->
                <div class="module-card">
                    <div class="module-header">
                        <div class="module-icon" style="background: linear-gradient(135deg, var(--secondary-color), #764ba2);">
                            <i class="fas fa-cogs"></i>
                        </div>
                        <div>
                            <h3>系统管理</h3>
                            <p>系统设置和日志管理</p>
                        </div>
                    </div>
                    <div>
                        <a href="dashboard.php" class="function-item">
                            <div class="function-icon">
                                <i class="fas fa-chart-line"></i>
                            </div>
                            <div>
                                <div style="font-weight: 600;">数据仪表盘</div>
                                <div style="font-size: 12px; color: var(--gray-600);">查看详细统计数据</div>
                            </div>
                        </a>

                        <a href="export_logs.php" class="function-item">
                            <div class="function-icon">
                                <i class="fas fa-file-export"></i>
                            </div>
                            <div>
                                <div style="font-weight: 600;">日志导出</div>
                                <div style="font-size: 12px; color: var(--gray-600);">导出系统操作日志</div>
                            </div>
                        </a>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <script>
        // 简单的交互效果
        document.querySelectorAll('.stat-card').forEach(card => {
            card.addEventListener('mouseenter', function() {
                this.style.transform = 'translateY(-4px) scale(1.02)';
            });

            card.addEventListener('mouseleave', function() {
                this.style.transform = 'translateY(0) scale(1)';
            });
        });
    </script>
</body>
</html>
