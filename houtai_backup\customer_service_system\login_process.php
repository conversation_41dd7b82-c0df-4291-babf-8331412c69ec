<?php
// 客服系统登录处理
header('Content-Type: text/html; charset=UTF-8');
ini_set('default_charset', 'UTF-8');

session_start();

// 引用数据库配置文件
require_once '../db_config.php';

$error_message = '';

// 处理登录
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $employee_id = trim($_POST['employee_id'] ?? '');
    $password = $_POST['password'] ?? '';
    $captcha = $_POST['captcha'] ?? '';

    // 验证验证码
    if (empty($captcha) || strtoupper($captcha) !== ($_SESSION['captcha'] ?? '')) {
        $error_message = '验证码错误';
    } elseif (empty($employee_id) || empty($password)) {
        $error_message = '请输入工号和密码';
    } else {
        try {
            $pdo = getDbConnection();

            // 查询客服账号
            $stmt = $pdo->prepare("
                SELECT * FROM customer_service_users
                WHERE employee_id = ? AND status = 'active'
            ");
            $stmt->execute([$employee_id]);
            $customer_service = $stmt->fetch(PDO::FETCH_ASSOC);

            // 检查密码（支持明文和哈希两种方式）
            $password_valid = false;
            if ($customer_service) {
                if (password_verify($password, $customer_service['password'])) {
                    // 哈希密码验证成功
                    $password_valid = true;
                } elseif ($password === $customer_service['password']) {
                    // 明文密码验证成功
                    $password_valid = true;
                }
            }

            if ($customer_service && $password_valid) {
                // 登录成功
                $_SESSION['cs_logged_in'] = true;
                $_SESSION['cs_user_id'] = $customer_service['id'];
                $_SESSION['cs_employee_id'] = $customer_service['employee_id'];
                $_SESSION['cs_name'] = $customer_service['name'];
                $_SESSION['cs_role'] = $customer_service['role'];
                $_SESSION['cs_department'] = $customer_service['department'];
                $_SESSION['cs_team'] = $customer_service['team'];

                // 更新最后登录时间和登录次数
                $stmt = $pdo->prepare("
                    UPDATE customer_service_users 
                    SET last_login = NOW(), login_count = login_count + 1 
                    WHERE id = ?
                ");
                $stmt->execute([$customer_service['id']]);

                // 跳转到客服管理后台
                header('Location: index.php');
                exit;
            } else {
                $error_message = '工号或密码错误';
            }
        } catch (PDOException $e) {
            $error_message = '系统错误，请稍后重试';
            error_log("客服登录错误: " . $e->getMessage());
        }
    }
}

// 如果有错误，返回登录页面并显示错误
if ($error_message) {
    $_SESSION['cs_login_error'] = $error_message;
    header('Location: ../login.php');
    exit;
}
?>
