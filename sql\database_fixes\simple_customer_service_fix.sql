-- 简化版客服系统数据库修复
-- 执行时间：2025-05-26

-- 1. 创建客服机器人配置表
CREATE TABLE IF NOT EXISTS customer_service_bot (
    id INT AUTO_INCREMENT PRIMARY KEY,
    bot_name VARCHAR(100) NOT NULL DEFAULT '趣玩小助手' COMMENT '机器人名称',
    bot_avatar VARCHAR(500) DEFAULT 'https://s1.imagehub.cc/images/2025/05/26/2cdd63b39f96072fc60cd733027d2a30.png' COMMENT '机器人头像',
    welcome_message TEXT NOT NULL COMMENT '欢迎消息',
    default_reply TEXT NOT NULL COMMENT '默认回复',
    is_enabled TINYINT(1) DEFAULT 1 COMMENT '是否启用',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='客服机器人配置表';

-- 2. 创建客服回复规则表
CREATE TABLE IF NOT EXISTS customer_service_replies (
    id INT AUTO_INCREMENT PRIMARY KEY,
    keywords JSON NOT NULL COMMENT '关键词列表',
    reply_content TEXT NOT NULL COMMENT '回复内容',
    priority INT DEFAULT 1 COMMENT '优先级，数字越大优先级越高',
    is_enabled TINYINT(1) DEFAULT 1 COMMENT '是否启用',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_priority (priority),
    INDEX idx_enabled (is_enabled)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='客服回复规则表';

-- 3. 创建客服对话记录表
CREATE TABLE IF NOT EXISTS customer_service_conversations (
    id INT AUTO_INCREMENT PRIMARY KEY,
    session_id VARCHAR(100) NOT NULL COMMENT '会话ID',
    user_id INT COMMENT '用户ID（如果已登录）',
    user_message TEXT NOT NULL COMMENT '用户消息',
    bot_reply TEXT COMMENT '机器人回复',
    is_human_service TINYINT(1) DEFAULT 0 COMMENT '是否转人工客服',
    admin_id INT COMMENT '客服人员ID',
    admin_reply TEXT COMMENT '人工客服回复',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_session_id (session_id),
    INDEX idx_user_id (user_id),
    INDEX idx_created_at (created_at),
    INDEX idx_admin_id (admin_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='客服对话记录表';

-- 4. 创建用户申诉表
CREATE TABLE IF NOT EXISTS user_appeals (
    id INT AUTO_INCREMENT PRIMARY KEY,
    phone VARCHAR(20) NOT NULL COMMENT '申诉手机号',
    appeal_reason TEXT NOT NULL COMMENT '申诉原因',
    evidence_files JSON COMMENT '证据文件列表',
    status ENUM('pending', 'processing', 'approved', 'rejected') DEFAULT 'pending' COMMENT '申诉状态',
    admin_id INT COMMENT '处理管理员ID',
    admin_reply TEXT COMMENT '管理员回复',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '申诉时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    processed_at TIMESTAMP NULL COMMENT '处理时间',
    INDEX idx_phone (phone),
    INDEX idx_status (status),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户申诉表';

-- 5. 插入默认的机器人配置
INSERT IGNORE INTO customer_service_bot (bot_name, bot_avatar, welcome_message, default_reply) VALUES 
('趣玩小助手', 'https://s1.imagehub.cc/images/2025/05/26/2cdd63b39f96072fc60cd733027d2a30.png',
'您好！我是趣玩星球智能客服小助手🤖

我可以帮助您解决以下问题：
• 账号相关问题
• 功能使用指导
• 常见问题解答

请描述您遇到的问题，我会尽力为您解答！',
'抱歉，我暂时无法理解您的问题😅

您可以：
• 换个方式描述问题
• 联系人工客服
• 查看帮助文档

如需人工客服，请回复"人工客服"');

-- 6. 插入默认的机器人回复规则
INSERT IGNORE INTO customer_service_replies (keywords, reply_content, priority) VALUES 
('["账号", "封号", "封禁", "被封"]', '关于账号封禁问题：

如果您的账号被误封，可以通过以下方式申诉：
• 点击登录页面的"提交申诉"按钮
• 详细说明情况并提供证明材料
• 我们会在3-5个工作日内处理

申诉时请如实填写信息，虚假信息将影响申诉结果。', 10),

('["申诉", "解封", "误封"]', '账号申诉流程：

1️⃣ 访问申诉页面
2️⃣ 填写手机号和申诉原因
3️⃣ 上传相关证明材料
4️⃣ 等待审核结果（3-5个工作日）

申诉提交后无法修改，请仔细核对信息。', 9),

('["密码", "忘记密码", "重置密码"]', '忘记密码解决方案：

1️⃣ 在登录页面点击"忘记密码"
2️⃣ 输入手机号获取验证码
3️⃣ 验证成功后设置新密码

如果手机号无法接收验证码，请联系人工客服。', 8),

('["注册", "注册不了", "无法注册"]', '注册问题解决：

常见原因：
• 手机号已被注册
• 验证码接收失败
• 网络连接问题

解决方法：
• 检查手机号是否正确
• 稍后重试获取验证码
• 联系客服协助处理', 7),

('["人工客服", "人工", "客服", "联系客服"]', '正在为您转接人工客服...

请稍等片刻，客服人员会尽快为您服务。

工作时间：9:00-18:00
非工作时间请留言，我们会及时回复。', 15),

('["你好", "您好", "hi", "hello"]', '您好！欢迎使用趣玩星球客服系统👋

我是您的专属智能助手，可以帮您解决各种问题。请告诉我您需要什么帮助？', 5);

-- 完成提示
SELECT '客服系统数据库修复完成！' as message;
