<?php
// 创建客服会话API
header('Content-Type: application/json; charset=UTF-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// 处理预检请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

session_start();

// 检查用户登录状态
if (!isset($_SESSION['user_id'])) {
    http_response_code(401);
    echo json_encode(['error' => '用户未登录']);
    exit;
}

// 引用数据库配置文件
require_once '../../../houtai_backup/db_config.php';

try {
    $pdo = getDbConnection();
    
    // 获取POST数据
    $input = json_decode(file_get_contents('php://input'), true);
    $source = $input['source'] ?? 'web';
    
    // 检查是否已有活跃会话
    $stmt = $pdo->prepare("
        SELECT session_id, status 
        FROM customer_service_sessions 
        WHERE user_id = ? AND status IN ('waiting', 'active') 
        ORDER BY started_at DESC 
        LIMIT 1
    ");
    $stmt->execute([$_SESSION['user_id']]);
    $existingSession = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($existingSession) {
        // 返回现有会话
        echo json_encode([
            'success' => true,
            'session_id' => $existingSession['session_id'],
            'status' => $existingSession['status'],
            'message' => '使用现有会话',
            'is_new' => false
        ]);
        exit;
    }
    
    // 生成新的会话ID
    $sessionId = 'session_' . time() . '_' . substr(md5(uniqid()), 0, 8);
    
    // 创建新会话
    $stmt = $pdo->prepare("
        INSERT INTO customer_service_sessions 
        (session_id, user_id, user_phone, user_name, status, source, priority, category, started_at, created_at, updated_at) 
        VALUES (?, ?, ?, ?, 'waiting', ?, 'normal', 'general', NOW(), NOW(), NOW())
    ");
    
    $result = $stmt->execute([
        $sessionId,
        $_SESSION['user_id'],
        $_SESSION['phone'] ?? '',
        $_SESSION['nickname'] ?? '用户',
        $source
    ]);
    
    if ($result) {
        // 发送欢迎消息（机器人）
        $welcomeMessage = "您好！欢迎使用趣玩星球客服服务！我是您的专属客服助手，有什么可以帮助您的吗？";
        
        $stmt = $pdo->prepare("
            INSERT INTO customer_service_messages 
            (session_id, sender_type, sender_id, sender_name, message_type, content, created_at) 
            VALUES (?, 'bot', NULL, '趣玩小助手', 'text', ?, NOW())
        ");
        $stmt->execute([$sessionId, $welcomeMessage]);
        
        // 更新会话消息数量
        $stmt = $pdo->prepare("
            UPDATE customer_service_sessions 
            SET message_count = message_count + 1, updated_at = NOW() 
            WHERE session_id = ?
        ");
        $stmt->execute([$sessionId]);
        
        echo json_encode([
            'success' => true,
            'session_id' => $sessionId,
            'status' => 'waiting',
            'message' => '会话创建成功',
            'is_new' => true,
            'welcome_message' => $welcomeMessage
        ]);
    } else {
        throw new Exception('创建会话失败');
    }
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => '创建会话失败: ' . $e->getMessage()
    ]);
}
?>
