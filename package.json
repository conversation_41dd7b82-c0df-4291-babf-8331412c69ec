{"name": "quwanplanet-vue", "version": "1.0.0", "description": "趣玩星球 - Vue + uniCloud 社交平台", "main": "main.js", "scripts": {"dev": "uni", "build": "uni build", "build:h5": "uni build -p h5", "build:mp-weixin": "uni build -p mp-weixin", "build:app": "uni build -p app", "serve": "uni serve", "lint": "eslint --ext .js,.vue src", "lint:fix": "eslint --ext .js,.vue src --fix"}, "dependencies": {"@dcloudio/uni-app": "^3.0.0", "@dcloudio/uni-components": "^3.0.0", "@dcloudio/uni-h5": "^3.0.0", "@dcloudio/uni-mp-weixin": "^3.0.0", "@dcloudio/uni-app-plus": "^3.0.0", "pinia": "^2.1.7", "vue": "^3.3.4", "vue-router": "^4.2.4"}, "devDependencies": {"@dcloudio/uni-cli-shared": "^3.0.0", "@dcloudio/vite-plugin-uni": "^3.0.0", "@vue/eslint-config-typescript": "^11.0.3", "eslint": "^8.45.0", "eslint-plugin-vue": "^9.15.1", "sass": "^1.64.1", "typescript": "^5.1.6", "vite": "^4.4.7"}, "uni-app": {"scripts": {}}, "keywords": ["uniapp", "vue3", "unicloud", "社交平台", "客服系统"], "author": "Augment Agent", "license": "MIT"}