-- =====================================================
-- 增强版智能客服机器人数据库结构
-- 参考大厂智能客服设计
-- =====================================================

-- 1. 扩展机器人配置表
-- =====================================================
ALTER TABLE `customer_service_bot` 
ADD COLUMN IF NOT EXISTS `bot_description` TEXT COMMENT '机器人描述',
ADD COLUMN IF NOT EXISTS `working_hours` JSON COMMENT '工作时间配置',
ADD COLUMN IF NOT EXISTS `auto_transfer_keywords` JSON COMMENT '自动转人工关键词',
ADD COLUMN IF NOT EXISTS `max_session_duration` INT DEFAULT 1800 COMMENT '最大会话时长(秒)',
ADD COLUMN IF NOT EXISTS `greeting_delay` INT DEFAULT 2 COMMENT '问候语延迟(秒)',
ADD COLUMN IF NOT EXISTS `typing_delay` INT DEFAULT 1 COMMENT '打字延迟(秒)',
ADD COLUMN IF NOT EXISTS `enable_smart_suggestions` TINYINT(1) DEFAULT 1 COMMENT '启用智能建议',
ADD COLUMN IF NOT EXISTS `enable_satisfaction_survey` TINYINT(1) DEFAULT 1 COMMENT '启用满意度调查',
ADD COLUMN IF NOT EXISTS `fallback_to_human` TINYINT(1) DEFAULT 1 COMMENT '无法回答时转人工',
ADD COLUMN IF NOT EXISTS `collect_user_info` TINYINT(1) DEFAULT 0 COMMENT '是否收集用户信息';

-- 2. 创建机器人回复规则增强表
-- =====================================================
ALTER TABLE `customer_service_replies` 
ADD COLUMN IF NOT EXISTS `rule_name` VARCHAR(100) COMMENT '规则名称',
ADD COLUMN IF NOT EXISTS `rule_description` TEXT COMMENT '规则描述',
ADD COLUMN IF NOT EXISTS `reply_type` ENUM('text', 'rich_text', 'quick_reply', 'card', 'form') DEFAULT 'text' COMMENT '回复类型',
ADD COLUMN IF NOT EXISTS `reply_data` JSON COMMENT '回复数据(富文本、快捷回复等)',
ADD COLUMN IF NOT EXISTS `trigger_conditions` JSON COMMENT '触发条件',
ADD COLUMN IF NOT EXISTS `follow_up_actions` JSON COMMENT '后续动作',
ADD COLUMN IF NOT EXISTS `category` VARCHAR(50) DEFAULT 'general' COMMENT '分类',
ADD COLUMN IF NOT EXISTS `tags` JSON COMMENT '标签',
ADD COLUMN IF NOT EXISTS `usage_count` INT DEFAULT 0 COMMENT '使用次数',
ADD COLUMN IF NOT EXISTS `success_rate` DECIMAL(5,2) DEFAULT 0.00 COMMENT '成功率',
ADD COLUMN IF NOT EXISTS `admin_id` INT COMMENT '创建者ID';

-- 3. 创建快捷回复模板表
-- =====================================================
CREATE TABLE IF NOT EXISTS `customer_service_quick_replies` (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `title` VARCHAR(100) NOT NULL COMMENT '快捷回复标题',
    `content` TEXT NOT NULL COMMENT '回复内容',
    `category` VARCHAR(50) DEFAULT 'general' COMMENT '分类',
    `sort_order` INT DEFAULT 0 COMMENT '排序',
    `is_enabled` TINYINT(1) DEFAULT 1 COMMENT '是否启用',
    `usage_count` INT DEFAULT 0 COMMENT '使用次数',
    `admin_id` INT COMMENT '创建者ID',
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX `idx_category` (`category`),
    INDEX `idx_enabled` (`is_enabled`),
    INDEX `idx_sort` (`sort_order`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='快捷回复模板表';

-- 4. 创建用户信息收集表
-- =====================================================
CREATE TABLE IF NOT EXISTS `customer_service_user_info` (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `session_id` VARCHAR(100) NOT NULL COMMENT '会话ID',
    `user_id` INT COMMENT '用户ID',
    `collected_data` JSON COMMENT '收集的数据',
    `collection_status` ENUM('pending', 'completed', 'failed') DEFAULT 'pending' COMMENT '收集状态',
    `form_template_id` INT COMMENT '表单模板ID',
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX `idx_session_id` (`session_id`),
    INDEX `idx_user_id` (`user_id`),
    INDEX `idx_status` (`collection_status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户信息收集表';

-- 5. 创建表单模板表
-- =====================================================
CREATE TABLE IF NOT EXISTS `customer_service_form_templates` (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `template_name` VARCHAR(100) NOT NULL COMMENT '模板名称',
    `template_description` TEXT COMMENT '模板描述',
    `form_fields` JSON NOT NULL COMMENT '表单字段配置',
    `trigger_keywords` JSON COMMENT '触发关键词',
    `success_message` TEXT COMMENT '成功提示',
    `is_enabled` TINYINT(1) DEFAULT 1 COMMENT '是否启用',
    `admin_id` INT COMMENT '创建者ID',
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX `idx_enabled` (`is_enabled`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='表单模板表';

-- 6. 创建满意度调查表
-- =====================================================
CREATE TABLE IF NOT EXISTS `customer_service_satisfaction` (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `session_id` VARCHAR(100) NOT NULL COMMENT '会话ID',
    `user_id` INT COMMENT '用户ID',
    `rating` TINYINT(1) COMMENT '评分(1-5)',
    `feedback` TEXT COMMENT '反馈内容',
    `tags` JSON COMMENT '标签',
    `admin_id` INT COMMENT '客服ID',
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX `idx_session_id` (`session_id`),
    INDEX `idx_rating` (`rating`),
    INDEX `idx_admin_id` (`admin_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='满意度调查表';

-- 7. 创建智能建议表
-- =====================================================
CREATE TABLE IF NOT EXISTS `customer_service_suggestions` (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `suggestion_text` VARCHAR(200) NOT NULL COMMENT '建议文本',
    `trigger_keywords` JSON COMMENT '触发关键词',
    `category` VARCHAR(50) DEFAULT 'general' COMMENT '分类',
    `click_count` INT DEFAULT 0 COMMENT '点击次数',
    `is_enabled` TINYINT(1) DEFAULT 1 COMMENT '是否启用',
    `admin_id` INT COMMENT '创建者ID',
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX `idx_category` (`category`),
    INDEX `idx_enabled` (`is_enabled`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='智能建议表';

-- 8. 创建会话统计表
-- =====================================================
CREATE TABLE IF NOT EXISTS `customer_service_session_stats` (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `session_id` VARCHAR(100) NOT NULL COMMENT '会话ID',
    `user_id` INT COMMENT '用户ID',
    `start_time` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '开始时间',
    `end_time` TIMESTAMP NULL COMMENT '结束时间',
    `duration` INT DEFAULT 0 COMMENT '持续时间(秒)',
    `message_count` INT DEFAULT 0 COMMENT '消息数量',
    `bot_message_count` INT DEFAULT 0 COMMENT '机器人消息数',
    `user_message_count` INT DEFAULT 0 COMMENT '用户消息数',
    `transferred_to_human` TINYINT(1) DEFAULT 0 COMMENT '是否转人工',
    `satisfaction_rating` TINYINT(1) COMMENT '满意度评分',
    `resolved` TINYINT(1) DEFAULT 0 COMMENT '是否解决',
    `admin_id` INT COMMENT '客服ID',
    `ip_address` VARCHAR(45) COMMENT 'IP地址',
    `user_agent` TEXT COMMENT '用户代理',
    INDEX `idx_session_id` (`session_id`),
    INDEX `idx_user_id` (`user_id`),
    INDEX `idx_start_time` (`start_time`),
    INDEX `idx_admin_id` (`admin_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='会话统计表';

-- 9. 插入默认配置数据
-- =====================================================

-- 更新机器人配置
UPDATE `customer_service_bot` SET 
    `bot_description` = '趣玩星球智能客服助手，7x24小时为您服务',
    `working_hours` = JSON_OBJECT(
        'enabled', true,
        'timezone', 'Asia/Shanghai',
        'schedule', JSON_ARRAY(
            JSON_OBJECT('day', 'monday', 'start', '09:00', 'end', '18:00'),
            JSON_OBJECT('day', 'tuesday', 'start', '09:00', 'end', '18:00'),
            JSON_OBJECT('day', 'wednesday', 'start', '09:00', 'end', '18:00'),
            JSON_OBJECT('day', 'thursday', 'start', '09:00', 'end', '18:00'),
            JSON_OBJECT('day', 'friday', 'start', '09:00', 'end', '18:00')
        )
    ),
    `auto_transfer_keywords` = JSON_ARRAY('人工客服', '转人工', '人工', '客服', '投诉', '退款'),
    `max_session_duration` = 1800,
    `greeting_delay` = 2,
    `typing_delay` = 1,
    `enable_smart_suggestions` = 1,
    `enable_satisfaction_survey` = 1,
    `fallback_to_human` = 1,
    `collect_user_info` = 0
WHERE `id` = 1;

-- 插入快捷回复模板
INSERT IGNORE INTO `customer_service_quick_replies` (`title`, `content`, `category`, `sort_order`) VALUES
('问候语', '您好！欢迎来到趣玩星球，我是您的专属客服，有什么可以帮助您的吗？', 'greeting', 1),
('感谢语', '感谢您的咨询，如果还有其他问题，随时联系我们！', 'closing', 1),
('稍等提示', '请稍等片刻，我正在为您查询相关信息...', 'processing', 1),
('转人工', '正在为您转接人工客服，请稍等...', 'transfer', 1),
('工作时间', '我们的客服工作时间是：周一至周五 9:00-18:00', 'info', 1);

-- 插入智能建议
INSERT IGNORE INTO `customer_service_suggestions` (`suggestion_text`, `trigger_keywords`, `category`) VALUES
('账号登录问题', JSON_ARRAY('登录', '密码', '账号'), 'account'),
('功能使用指导', JSON_ARRAY('怎么用', '如何', '教程'), 'guide'),
('申诉相关问题', JSON_ARRAY('申诉', '封号', '误封'), 'appeal'),
('联系人工客服', JSON_ARRAY('人工', '客服', '投诉'), 'service');

-- 插入表单模板
INSERT IGNORE INTO `customer_service_form_templates` (`template_name`, `template_description`, `form_fields`, `trigger_keywords`, `success_message`) VALUES
('用户反馈表单', '收集用户反馈信息', 
JSON_OBJECT(
    'fields', JSON_ARRAY(
        JSON_OBJECT('name', 'contact', 'label', '联系方式', 'type', 'text', 'required', true),
        JSON_OBJECT('name', 'issue_type', 'label', '问题类型', 'type', 'select', 'options', JSON_ARRAY('功能问题', '账号问题', '其他'), 'required', true),
        JSON_OBJECT('name', 'description', 'label', '问题描述', 'type', 'textarea', 'required', true)
    )
),
JSON_ARRAY('反馈', '建议', '问题'),
'感谢您的反馈，我们会认真处理您的意见！');

-- 10. 创建索引优化
-- =====================================================
ALTER TABLE `customer_service_conversations` 
ADD INDEX IF NOT EXISTS `idx_message_type` (`message_type`),
ADD INDEX IF NOT EXISTS `idx_user_phone` (`user_phone`);

-- 验证表创建
SELECT 'enhanced_customer_service_database_setup_completed' as status, NOW() as completed_at;
