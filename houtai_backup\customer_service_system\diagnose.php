<?php
// 客服系统诊断页面
header('Content-Type: text/html; charset=UTF-8');
ini_set('default_charset', 'UTF-8');

session_start();

echo '<h1>客服系统诊断</h1>';

// 检查登录状态
echo '<h2>1. 登录状态检查</h2>';
if (isset($_SESSION['cs_logged_in']) && $_SESSION['cs_logged_in']) {
    echo '<p style="color: green;">✓ 客服已登录</p>';
    echo '<ul>';
    echo '<li>客服ID: ' . ($_SESSION['cs_user_id'] ?? 'N/A') . '</li>';
    echo '<li>客服姓名: ' . ($_SESSION['cs_name'] ?? 'N/A') . '</li>';
    echo '<li>工号: ' . ($_SESSION['cs_employee_id'] ?? 'N/A') . '</li>';
    echo '<li>角色: ' . ($_SESSION['cs_role'] ?? 'N/A') . '</li>';
    echo '</ul>';
} else {
    echo '<p style="color: red;">✗ 客服未登录</p>';
    echo '<p><a href="../login.php">点击登录</a></p>';
    echo '<h3>Session内容：</h3>';
    echo '<pre>' . print_r($_SESSION, true) . '</pre>';
    exit;
}

// 检查数据库连接
echo '<h2>2. 数据库连接检查</h2>';
try {
    require_once '../db_config.php';
    $pdo = getDbConnection();
    echo '<p style="color: green;">✓ 数据库连接成功</p>';
} catch (Exception $e) {
    echo '<p style="color: red;">✗ 数据库连接失败: ' . $e->getMessage() . '</p>';
    exit;
}

// 检查必要的表
echo '<h2>3. 数据表检查</h2>';
$tables = ['customer_service_sessions', 'customer_service_messages', 'customer_service_users', 'realtime_notifications'];
foreach ($tables as $table) {
    try {
        $stmt = $pdo->query("SHOW TABLES LIKE '$table'");
        if ($stmt->rowCount() > 0) {
            echo '<p style="color: green;">✓ ' . $table . ' 表存在</p>';
        } else {
            echo '<p style="color: red;">✗ ' . $table . ' 表不存在</p>';
        }
    } catch (Exception $e) {
        echo '<p style="color: red;">✗ 检查 ' . $table . ' 表时出错: ' . $e->getMessage() . '</p>';
    }
}

// 检查API文件
echo '<h2>4. API文件检查</h2>';
$apiFiles = [
    'api/accept_session.php',
    'api/close_session.php', 
    'api/send_cs_message.php',
    'api/get_new_user_messages.php',
    'api/get_session_status.php'
];

foreach ($apiFiles as $file) {
    if (file_exists($file)) {
        echo '<p style="color: green;">✓ ' . $file . ' 存在</p>';
    } else {
        echo '<p style="color: red;">✗ ' . $file . ' 不存在</p>';
    }
}

// 检查测试数据
echo '<h2>5. 测试数据检查</h2>';
try {
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM customer_service_sessions WHERE status = 'waiting'");
    $result = $stmt->fetch();
    $waitingCount = $result['count'];
    
    if ($waitingCount > 0) {
        echo '<p style="color: green;">✓ 有 ' . $waitingCount . ' 个等待中的会话</p>';
        
        // 显示等待中的会话
        $stmt = $pdo->query("SELECT session_id, user_name, priority, started_at FROM customer_service_sessions WHERE status = 'waiting' ORDER BY started_at DESC LIMIT 3");
        $sessions = $stmt->fetchAll();
        
        echo '<h3>等待中的会话：</h3>';
        echo '<ul>';
        foreach ($sessions as $session) {
            echo '<li>' . $session['session_id'] . ' - ' . $session['user_name'] . ' (' . $session['priority'] . ')</li>';
        }
        echo '</ul>';
    } else {
        echo '<p style="color: orange;">⚠ 没有等待中的会话</p>';
        echo '<p><a href="create_test_data.php">创建测试数据</a></p>';
    }
} catch (Exception $e) {
    echo '<p style="color: red;">✗ 检查测试数据时出错: ' . $e->getMessage() . '</p>';
}

// API测试
echo '<h2>6. API直接测试</h2>';
if ($waitingCount > 0) {
    $stmt = $pdo->query("SELECT session_id FROM customer_service_sessions WHERE status = 'waiting' LIMIT 1");
    $testSession = $stmt->fetch();
    $testSessionId = $testSession['session_id'];
    
    echo '<p>测试会话ID: ' . $testSessionId . '</p>';
    echo '<button onclick="testAcceptAPI(\'' . $testSessionId . '\')">测试接受API</button>';
    echo '<div id="apiResult"></div>';
} else {
    echo '<p>没有可测试的会话</p>';
}

// 检查权限
echo '<h2>7. 文件权限检查</h2>';
$checkFiles = ['api/accept_session.php', '../db_config.php'];
foreach ($checkFiles as $file) {
    if (file_exists($file)) {
        if (is_readable($file)) {
            echo '<p style="color: green;">✓ ' . $file . ' 可读</p>';
        } else {
            echo '<p style="color: red;">✗ ' . $file . ' 不可读</p>';
        }
    }
}

// 检查PHP错误日志
echo '<h2>8. PHP配置检查</h2>';
echo '<p>错误报告级别: ' . error_reporting() . '</p>';
echo '<p>显示错误: ' . (ini_get('display_errors') ? '开启' : '关闭') . '</p>';
echo '<p>错误日志: ' . (ini_get('log_errors') ? '开启' : '关闭') . '</p>';
echo '<p>错误日志文件: ' . ini_get('error_log') . '</p>';

?>

<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>客服系统诊断</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        h1, h2, h3 { color: #333; }
        button { padding: 8px 16px; background: #007cba; color: white; border: none; border-radius: 3px; cursor: pointer; }
        button:hover { background: #005a87; }
        pre { background: #f5f5f5; padding: 10px; border-radius: 5px; overflow-x: auto; }
        ul { margin: 10px 0; }
        li { margin: 5px 0; }
    </style>
</head>
<body>
    <script>
        async function testAcceptAPI(sessionId) {
            const resultDiv = document.getElementById('apiResult');
            resultDiv.innerHTML = '<p>测试中...</p>';
            
            try {
                console.log('测试接受API，会话ID:', sessionId);
                
                const response = await fetch('api/accept_session.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        sessionId: sessionId
                    })
                });
                
                console.log('响应状态:', response.status);
                console.log('响应头:', response.headers);
                
                const responseText = await response.text();
                console.log('原始响应:', responseText);
                
                let data;
                try {
                    data = JSON.parse(responseText);
                } catch (e) {
                    resultDiv.innerHTML = '<h3>API测试结果：</h3><p style="color: red;">响应不是有效的JSON</p><pre>' + responseText + '</pre>';
                    return;
                }
                
                resultDiv.innerHTML = '<h3>API测试结果：</h3><pre>' + JSON.stringify(data, null, 2) + '</pre>';
                
                if (data.success) {
                    resultDiv.innerHTML += '<p style="color: green;">✓ API调用成功</p>';
                } else {
                    resultDiv.innerHTML += '<p style="color: red;">✗ API调用失败: ' + (data.error || '未知错误') + '</p>';
                }
                
            } catch (error) {
                console.error('API测试失败:', error);
                resultDiv.innerHTML = '<h3>API测试结果：</h3><p style="color: red;">网络错误: ' + error.message + '</p>';
            }
        }
    </script>
    
    <hr>
    <h2>快速操作</h2>
    <p>
        <a href="sessions.php">返回会话列表</a> | 
        <a href="create_test_data.php">创建测试数据</a> | 
        <a href="test_db.php">数据库测试</a> | 
        <a href="test_accept_api.php">API测试</a>
    </p>
</body>
</html>
