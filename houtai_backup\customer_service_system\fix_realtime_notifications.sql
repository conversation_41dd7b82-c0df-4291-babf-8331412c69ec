-- 修复客服系统数据库表结构
-- 解决接受会话API的字段问题

-- 1. 检查 realtime_notifications 表结构
DESCRIBE realtime_notifications;

-- 2. 添加缺失的 message 字段（如果不存在）
-- 注意：MySQL 5.7 不支持 IF NOT EXISTS，所以需要手动检查
ALTER TABLE realtime_notifications
ADD COLUMN message TEXT COMMENT '通知消息内容'
AFTER title;

-- 3. 检查 customer_service_messages 表结构
DESCRIBE customer_service_messages;

-- 4. 修复 content 字段（允许NULL，TEXT类型不能有默认值）
ALTER TABLE customer_service_messages
MODIFY COLUMN content TEXT NULL COMMENT '消息内容';

-- 5. 检查修复后的表结构
DESCRIBE realtime_notifications;
DESCRIBE customer_service_messages;

-- 6. 显示表中的数据示例
SELECT * FROM realtime_notifications LIMIT 3;
SELECT * FROM customer_service_messages LIMIT 3;

-- 7. 检查等待中的会话
SELECT session_id, user_name, status, priority, started_at
FROM customer_service_sessions
WHERE status = 'waiting'
ORDER BY started_at DESC
LIMIT 5;
