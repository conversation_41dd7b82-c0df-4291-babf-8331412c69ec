<?php
// 详细API测试工具
header('Content-Type: text/html; charset=UTF-8');
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>客服API详细测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .test-section h3 { margin-top: 0; color: #333; }
        .result { margin: 10px 0; padding: 10px; border-radius: 4px; }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        .warning { background: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
        button { padding: 10px 20px; margin: 5px; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer; }
        button:hover { background: #0056b3; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 4px; overflow-x: auto; }
        .session-info { background: #e9ecef; padding: 10px; border-radius: 4px; margin: 10px 0; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 客服API详细测试工具</h1>
        
        <div class="test-section">
            <h3>📋 测试环境信息</h3>
            <div id="env-info">
                <div class="info">
                    <strong>当前时间:</strong> <?php echo date('Y-m-d H:i:s'); ?><br>
                    <strong>PHP版本:</strong> <?php echo PHP_VERSION; ?><br>
                    <strong>服务器:</strong> <?php echo $_SERVER['SERVER_NAME']; ?><br>
                    <strong>请求URI:</strong> <?php echo $_SERVER['REQUEST_URI']; ?>
                </div>
            </div>
        </div>

        <div class="test-section">
            <h3>🔐 会话状态检查</h3>
            <button onclick="checkSession()">检查登录状态</button>
            <div id="session-result"></div>
        </div>

        <div class="test-section">
            <h3>🗄️ 数据库连接测试</h3>
            <button onclick="testDatabase()">测试数据库</button>
            <div id="db-result"></div>
        </div>

        <div class="test-section">
            <h3>📝 会话创建测试</h3>
            <button onclick="createTestSession()">创建测试会话</button>
            <div id="session-create-result"></div>
            <div class="session-info" id="current-session" style="display: none;">
                <strong>当前测试会话:</strong> <span id="session-id"></span>
            </div>
        </div>

        <div class="test-section">
            <h3>💬 消息发送测试</h3>
            <input type="text" id="test-message" placeholder="输入测试消息" style="width: 300px; padding: 8px; margin-right: 10px;">
            <button onclick="sendTestMessage()">发送消息</button>
            <div id="message-result"></div>
        </div>

        <div class="test-section">
            <h3>📨 消息接收测试</h3>
            <button onclick="getMessages()">获取消息</button>
            <div id="get-messages-result"></div>
        </div>

        <div class="test-section">
            <h3>🔄 完整流程测试</h3>
            <button onclick="runFullTest()">运行完整测试</button>
            <div id="full-test-result"></div>
        </div>
    </div>

    <script>
        let currentSessionId = null;

        // 检查会话状态
        async function checkSession() {
            const resultDiv = document.getElementById('session-result');
            resultDiv.innerHTML = '<div class="info">🔄 检查会话状态...</div>';

            try {
                const response = await fetch('api/check_session.php');
                const data = await response.json();
                
                if (data.success) {
                    resultDiv.innerHTML = `
                        <div class="success">
                            ✅ 用户已登录<br>
                            <strong>用户ID:</strong> ${data.user_id}<br>
                            <strong>昵称:</strong> ${data.nickname}<br>
                            <strong>登录时间:</strong> ${data.login_time || '未知'}
                        </div>
                    `;
                } else {
                    resultDiv.innerHTML = `<div class="error">❌ ${data.error}</div>`;
                }
            } catch (error) {
                resultDiv.innerHTML = `<div class="error">❌ 请求失败: ${error.message}</div>`;
            }
        }

        // 测试数据库连接
        async function testDatabase() {
            const resultDiv = document.getElementById('db-result');
            resultDiv.innerHTML = '<div class="info">🔄 测试数据库连接...</div>';

            try {
                const response = await fetch('test_db_connection.php');
                const text = await response.text();
                
                // 解析多行JSON响应
                const lines = text.trim().split('\n');
                let results = [];
                
                for (let line of lines) {
                    try {
                        const data = JSON.parse(line);
                        results.push(data);
                    } catch (e) {
                        console.warn('解析JSON失败:', line);
                    }
                }
                
                let html = '';
                for (let result of results) {
                    const className = result.success ? 'success' : 'error';
                    html += `<div class="${className}">${result.message}</div>`;
                }
                
                resultDiv.innerHTML = html;
            } catch (error) {
                resultDiv.innerHTML = `<div class="error">❌ 数据库测试失败: ${error.message}</div>`;
            }
        }

        // 创建测试会话
        async function createTestSession() {
            const resultDiv = document.getElementById('session-create-result');
            resultDiv.innerHTML = '<div class="info">🔄 创建测试会话...</div>';

            try {
                const response = await fetch('api/create_session.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        source: 'api_test'
                    })
                });
                
                const data = await response.json();
                
                if (data.success) {
                    currentSessionId = data.session_id;
                    document.getElementById('session-id').textContent = currentSessionId;
                    document.getElementById('current-session').style.display = 'block';
                    
                    resultDiv.innerHTML = `
                        <div class="success">
                            ✅ 会话创建成功<br>
                            <strong>会话ID:</strong> ${data.session_id}<br>
                            <strong>状态:</strong> ${data.status}
                        </div>
                    `;
                } else {
                    resultDiv.innerHTML = `<div class="error">❌ ${data.error}</div>`;
                }
            } catch (error) {
                resultDiv.innerHTML = `<div class="error">❌ 创建会话失败: ${error.message}</div>`;
            }
        }

        // 发送测试消息
        async function sendTestMessage() {
            if (!currentSessionId) {
                alert('请先创建测试会话');
                return;
            }

            const message = document.getElementById('test-message').value.trim();
            if (!message) {
                alert('请输入测试消息');
                return;
            }

            const resultDiv = document.getElementById('message-result');
            resultDiv.innerHTML = '<div class="info">🔄 发送消息...</div>';

            try {
                const response = await fetch('api/send_message.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        sessionId: currentSessionId,
                        message: message
                    })
                });
                
                const data = await response.json();
                
                if (data.success) {
                    resultDiv.innerHTML = `
                        <div class="success">
                            ✅ 消息发送成功<br>
                            <strong>消息ID:</strong> ${data.message_id || '未知'}<br>
                            <strong>机器人回复:</strong> ${data.bot_reply || '无'}
                        </div>
                    `;
                    document.getElementById('test-message').value = '';
                } else {
                    resultDiv.innerHTML = `<div class="error">❌ ${data.error}</div>`;
                }
            } catch (error) {
                resultDiv.innerHTML = `<div class="error">❌ 发送消息失败: ${error.message}</div>`;
            }
        }

        // 获取消息
        async function getMessages() {
            if (!currentSessionId) {
                alert('请先创建测试会话');
                return;
            }

            const resultDiv = document.getElementById('get-messages-result');
            resultDiv.innerHTML = '<div class="info">🔄 获取消息...</div>';

            try {
                const response = await fetch(`api/get_new_messages.php?session_id=${currentSessionId}&last_message_id=0`);
                const data = await response.json();
                
                if (data.success) {
                    let html = `<div class="success">✅ 获取消息成功，共 ${data.messages.length} 条消息</div>`;
                    
                    if (data.messages.length > 0) {
                        html += '<pre>' + JSON.stringify(data.messages, null, 2) + '</pre>';
                    }
                    
                    resultDiv.innerHTML = html;
                } else {
                    resultDiv.innerHTML = `<div class="error">❌ ${data.error}</div>`;
                }
            } catch (error) {
                resultDiv.innerHTML = `<div class="error">❌ 获取消息失败: ${error.message}</div>`;
            }
        }

        // 运行完整测试
        async function runFullTest() {
            const resultDiv = document.getElementById('full-test-result');
            resultDiv.innerHTML = '<div class="info">🔄 开始完整测试流程...</div>';

            let results = [];

            try {
                // 1. 检查会话
                results.push('1️⃣ 检查登录状态...');
                await checkSession();
                await sleep(1000);

                // 2. 测试数据库
                results.push('2️⃣ 测试数据库连接...');
                await testDatabase();
                await sleep(1000);

                // 3. 创建会话
                results.push('3️⃣ 创建测试会话...');
                await createTestSession();
                await sleep(1000);

                // 4. 发送消息
                if (currentSessionId) {
                    results.push('4️⃣ 发送测试消息...');
                    document.getElementById('test-message').value = '这是完整测试的消息';
                    await sendTestMessage();
                    await sleep(1000);

                    // 5. 获取消息
                    results.push('5️⃣ 获取消息列表...');
                    await getMessages();
                }

                results.push('✅ 完整测试流程完成！');
                resultDiv.innerHTML = `<div class="success">${results.join('<br>')}</div>`;

            } catch (error) {
                results.push(`❌ 测试失败: ${error.message}`);
                resultDiv.innerHTML = `<div class="error">${results.join('<br>')}</div>`;
            }
        }

        function sleep(ms) {
            return new Promise(resolve => setTimeout(resolve, ms));
        }

        // 页面加载时自动检查会话
        window.onload = function() {
            checkSession();
        };
    </script>
</body>
</html>
