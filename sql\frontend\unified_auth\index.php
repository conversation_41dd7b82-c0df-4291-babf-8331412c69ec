<?php
/**
 * 统一登录注册页面
 * 根据手机号自动判断登录或注册流程
 */
session_start();

// 如果已经登录，跳转到首页
if (isset($_SESSION['user_id']) && isset($_SESSION['logged_in']) && $_SESSION['logged_in'] === true) {
    header('Location: ../home/<USER>');
    exit;
}
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0, user-scalable=no, shrink-to-fit=no">
    <meta name="theme-color" content="#40E0D0">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="default">
    <meta name="msapplication-navbutton-color" content="#40E0D0">

    <title>趣玩星球 - 登录注册</title>

    <!-- 引入字体图标 -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">

    <style>
        :root {
            --primary-color: #40E0D0;
            --primary-dark: #36C5B5;
            --primary-light: #5EEADB;
            --bg-primary: #f8fafc;
            --bg-white: #ffffff;
            --text-primary: #1a202c;
            --text-secondary: #718096;
            --text-muted: #a0aec0;
            --border-color: #e2e8f0;
            --border-focus: var(--primary-color);
            --shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.1);
            --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.1);
            --shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.1);
            --radius-sm: 6px;
            --radius-md: 12px;
            --radius-lg: 16px;
            --transition-fast: 0.15s ease;
            --transition-normal: 0.3s ease;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
            color: var(--text-primary);
            line-height: 1.6;
        }

        .auth-container {
            width: 100%;
            max-width: 400px;
            background: var(--bg-white);
            border-radius: var(--radius-lg);
            box-shadow: var(--shadow-lg);
            overflow: hidden;
            position: relative;
        }

        .auth-header {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
            color: white;
            padding: 32px 24px;
            text-align: center;
            position: relative;
        }

        .auth-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="20" cy="20" r="1" fill="white" opacity="0.3"><animate attributeName="opacity" values="0.3;1;0.3" dur="2s" repeatCount="indefinite"/></circle><circle cx="80" cy="30" r="1" fill="white" opacity="0.5"><animate attributeName="opacity" values="0.5;1;0.5" dur="3s" repeatCount="indefinite"/></circle><circle cx="60" cy="70" r="1" fill="white" opacity="0.4"><animate attributeName="opacity" values="0.4;1;0.4" dur="2.5s" repeatCount="indefinite"/></circle></svg>') repeat;
            pointer-events: none;
        }

        .auth-header h1 {
            font-size: 28px;
            font-weight: 700;
            margin-bottom: 8px;
            position: relative;
            z-index: 1;
        }

        .auth-header p {
            font-size: 16px;
            opacity: 0.9;
            position: relative;
            z-index: 1;
        }

        .auth-content {
            padding: 32px 24px;
        }

        .step-indicator {
            display: flex;
            justify-content: center;
            margin-bottom: 32px;
        }

        .step {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            background: var(--border-color);
            color: var(--text-muted);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 14px;
            font-weight: 600;
            margin: 0 8px;
            transition: var(--transition-normal);
        }

        .step.active {
            background: var(--primary-color);
            color: white;
        }

        .step.completed {
            background: var(--primary-dark);
            color: white;
        }

        .form-group {
            margin-bottom: 24px;
        }

        .form-label {
            display: block;
            font-size: 14px;
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 8px;
        }

        .form-input {
            width: 100%;
            padding: 16px;
            border: 2px solid var(--border-color);
            border-radius: var(--radius-md);
            font-size: 16px;
            transition: var(--transition-normal);
            background: var(--bg-white);
        }

        .form-input:focus {
            outline: none;
            border-color: var(--border-focus);
            box-shadow: 0 0 0 3px rgba(64, 224, 208, 0.1);
        }

        .btn {
            width: 100%;
            padding: 16px;
            border: none;
            border-radius: var(--radius-md);
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: var(--transition-normal);
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
            text-decoration: none;
        }

        .btn-primary {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
            color: white;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-md);
        }

        .btn-primary:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .btn-secondary {
            background: var(--bg-primary);
            color: var(--text-secondary);
            border: 2px solid var(--border-color);
        }

        .btn-secondary:hover {
            background: var(--border-color);
        }

        .loading {
            display: none;
            text-align: center;
            padding: 40px 20px;
        }

        .spinner {
            width: 40px;
            height: 40px;
            border: 4px solid var(--border-color);
            border-top: 4px solid var(--primary-color);
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto 16px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .error-message {
            background: #fee;
            color: #c53030;
            padding: 12px 16px;
            border-radius: var(--radius-md);
            margin-bottom: 16px;
            display: none;
            font-size: 14px;
        }

        .success-message {
            background: #f0fff4;
            color: #38a169;
            padding: 12px 16px;
            border-radius: var(--radius-md);
            margin-bottom: 16px;
            display: none;
            font-size: 14px;
        }

        .back-btn {
            position: absolute;
            top: 16px;
            left: 16px;
            background: rgba(255, 255, 255, 0.2);
            color: white;
            border: none;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition-normal);
            z-index: 2;
        }

        .back-btn:hover {
            background: rgba(255, 255, 255, 0.3);
        }

        /* 隐藏步骤 */
        .step-hidden {
            display: none;
        }

        /* 用户信息样式 */
        .user-info {
            display: flex;
            align-items: center;
            padding: 20px;
            background: var(--bg-primary);
            border-radius: var(--radius-md);
            margin-bottom: 24px;
        }

        .user-avatar {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            overflow: hidden;
            margin-right: 16px;
            border: 3px solid var(--primary-color);
        }

        .user-avatar img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .user-details {
            flex: 1;
        }

        .user-name {
            font-size: 18px;
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 4px;
        }

        .user-id {
            font-size: 14px;
            color: var(--text-secondary);
        }

        /* 安全状态样式 */
        .security-status {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
            padding: 12px 16px;
            border-radius: var(--radius-md);
            margin-bottom: 24px;
            font-size: 14px;
            font-weight: 600;
        }

        .security-status.trusted {
            background: rgba(72, 187, 120, 0.1);
            color: var(--success-color);
            border: 1px solid rgba(72, 187, 120, 0.2);
        }

        .security-status.risk {
            background: rgba(245, 101, 101, 0.1);
            color: var(--error-color);
            border: 1px solid rgba(245, 101, 101, 0.2);
        }

        /* 响应式设计 */
        @media (max-width: 480px) {
            .auth-container {
                margin: 10px;
                border-radius: var(--radius-md);
            }

            .auth-header {
                padding: 24px 20px;
            }

            .auth-content {
                padding: 24px 20px;
            }

            .user-info {
                padding: 16px;
            }

            .user-avatar {
                width: 50px;
                height: 50px;
                margin-right: 12px;
            }

            .user-name {
                font-size: 16px;
            }
        }
    </style>
</head>
<body>
    <div class="auth-container">
        <!-- 返回按钮 -->
        <button class="back-btn" id="backBtn" onclick="goBack()" style="display: none;">
            <i class="fas fa-arrow-left"></i>
        </button>

        <!-- 头部 -->
        <div class="auth-header">
            <h1>趣玩星球</h1>
            <p>探索有趣的生活</p>
        </div>

        <!-- 内容区域 -->
        <div class="auth-content">
            <!-- 步骤指示器 -->
            <div class="step-indicator">
                <div class="step active" id="step1">1</div>
                <div class="step" id="step2">2</div>
                <div class="step" id="step3">3</div>
            </div>

            <!-- 错误和成功消息 -->
            <div class="error-message" id="errorMessage"></div>
            <div class="success-message" id="successMessage"></div>

            <!-- 第一步：输入手机号 -->
            <div class="auth-step" id="phoneStep">
                <div class="form-group">
                    <label class="form-label" for="phoneInput">手机号</label>
                    <input type="tel" id="phoneInput" class="form-input" placeholder="请输入手机号" maxlength="11" inputmode="numeric">
                </div>
                <button type="button" class="btn btn-primary" id="continueBtn">
                    <i class="fas fa-arrow-right"></i>
                    继续
                </button>
            </div>

            <!-- 加载状态 -->
            <div class="loading" id="loadingStep">
                <div class="spinner"></div>
                <p id="loadingText">正在检测账号状态...</p>
            </div>

            <!-- 其他步骤将通过JavaScript动态加载 -->
        </div>
    </div>

    <script>
        // 全局变量
        let currentStep = 1;
        let userPhone = '';
        let userExists = false;
        let securityResult = null;

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            initializeAuth();
        });

        function initializeAuth() {
            // 绑定事件
            document.getElementById('continueBtn').addEventListener('click', handleContinue);
            document.getElementById('phoneInput').addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    handleContinue();
                }
            });

            // 手机号输入限制
            document.getElementById('phoneInput').addEventListener('input', function(e) {
                let value = e.target.value.replace(/[^0-9]/g, '');
                e.target.value = value;
            });
        }

        async function handleContinue() {
            const phone = document.getElementById('phoneInput').value.trim();

            if (!validatePhone(phone)) {
                showError('请输入正确的手机号');
                return;
            }

            userPhone = phone;
            showLoading('正在检测账号状态...');

            try {
                // 检测用户是否存在
                const response = await fetch('check_user.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ phone: phone })
                });

                const result = await response.json();

                if (result.success) {
                    userExists = result.exists;

                    if (userExists) {
                        // 用户已存在，进入登录流程
                        await handleExistingUser(result.user);
                    } else {
                        // 用户不存在，进入注册流程
                        handleNewUser();
                    }
                } else {
                    showError(result.message || '检测失败，请重试');
                    hideLoading();
                }
            } catch (error) {
                console.error('检测失败:', error);
                showError('网络错误，请重试');
                hideLoading();
            }
        }

        function validatePhone(phone) {
            const phoneRegex = /^1[3-9]\d{9}$/;
            return phoneRegex.test(phone);
        }

        function showError(message) {
            const errorEl = document.getElementById('errorMessage');
            errorEl.textContent = message;
            errorEl.style.display = 'block';
            setTimeout(() => {
                errorEl.style.display = 'none';
            }, 3000);
        }

        function showSuccess(message) {
            const successEl = document.getElementById('successMessage');
            successEl.textContent = message;
            successEl.style.display = 'block';
            setTimeout(() => {
                successEl.style.display = 'none';
            }, 3000);
        }

        function showLoading(text) {
            document.getElementById('loadingText').textContent = text;
            document.getElementById('phoneStep').style.display = 'none';
            document.getElementById('loadingStep').style.display = 'block';
        }

        function hideLoading() {
            document.getElementById('loadingStep').style.display = 'none';
            document.getElementById('phoneStep').style.display = 'block';
        }

        function goBack() {
            // 返回逻辑将在后续步骤中实现
            window.history.back();
        }

        // 处理已存在用户（登录流程）
        async function handleExistingUser(user) {
            showLoading('正在进行安全检测...');

            try {
                // 调用安全检测API（复用智能登录的逻辑）
                const response = await fetch('../login/security_check_simple.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ identifier: userPhone })
                });

                const result = await response.json();

                if (result.success) {
                    securityResult = result.security;

                    if (securityResult.trusted) {
                        // 环境安全，显示快速登录
                        showQuickLogin(user);
                    } else {
                        // 环境异常，需要验证码登录
                        showSecureLogin(user);
                    }
                } else {
                    showError(result.message || '安全检测失败');
                    hideLoading();
                }
            } catch (error) {
                console.error('安全检测失败:', error);
                showError('网络错误，请重试');
                hideLoading();
            }
        }

        // 显示快速登录界面
        function showQuickLogin(user) {
            hideLoading();
            updateStep(2);

            const content = `
                <div class="auth-step" id="quickLoginStep">
                    <div class="user-info">
                        <div class="user-avatar">
                            <img src="${user.avatar}" alt="头像" onerror="this.src='https://s1.imagehub.cc/images/2025/04/26/f5c2f26c4564aabcbb56c5fd30f1d652.jpg'">
                        </div>
                        <div class="user-details">
                            <div class="user-name">${user.nickname || user.username}</div>
                            <div class="user-id">趣玩ID: ${user.quwan_id}</div>
                        </div>
                    </div>

                    <div class="security-status trusted">
                        <i class="fas fa-shield-check"></i>
                        <span>可信环境</span>
                    </div>

                    <button type="button" class="btn btn-primary" onclick="quickLogin(${user.id})">
                        <i class="fas fa-bolt"></i>
                        快速登录
                    </button>
                </div>
            `;

            document.querySelector('.auth-content').innerHTML = content;
            document.getElementById('backBtn').style.display = 'block';
        }

        // 显示安全登录界面
        function showSecureLogin(user) {
            hideLoading();
            updateStep(2);

            const content = `
                <div class="auth-step" id="secureLoginStep">
                    <div class="user-info">
                        <div class="user-avatar">
                            <img src="${user.avatar}" alt="头像" onerror="this.src='https://s1.imagehub.cc/images/2025/04/26/f5c2f26c4564aabcbb56c5fd30f1d652.jpg'">
                        </div>
                        <div class="user-details">
                            <div class="user-name">${user.nickname || user.username}</div>
                            <div class="user-id">趣玩ID: ${user.quwan_id}</div>
                        </div>
                    </div>

                    <div class="security-status risk">
                        <i class="fas fa-shield-exclamation"></i>
                        <span>需要验证</span>
                    </div>

                    <button type="button" class="btn btn-primary" onclick="sendLoginVerificationCode()">
                        <i class="fas fa-paper-plane"></i>
                        发送验证码
                    </button>
                </div>
            `;

            document.querySelector('.auth-content').innerHTML = content;
            document.getElementById('backBtn').style.display = 'block';
        }

        // 快速登录
        async function quickLogin(userId) {
            try {
                const response = await fetch('../login/quick_login_simple.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ user_id: userId })
                });

                const result = await response.json();

                if (result.success) {
                    showSuccess('登录成功！');
                    setTimeout(() => {
                        window.location.href = '../home/<USER>';
                    }, 1000);
                } else {
                    showError(result.message || '登录失败');
                }
            } catch (error) {
                console.error('快速登录失败:', error);
                showError('网络错误，请重试');
            }
        }

        // 发送登录验证码
        function sendLoginVerificationCode() {
            // 跳转到智能登录的验证码页面
            window.location.href = `../login/smart_login.php?phone=${encodeURIComponent(userPhone)}`;
        }

        // 处理新用户（注册流程）
        function handleNewUser() {
            // 跳转到新的验证码页面
            window.location.href = `verify.php?phone=${encodeURIComponent(userPhone)}`;
        }

        // 更新步骤指示器
        function updateStep(step) {
            currentStep = step;

            for (let i = 1; i <= 3; i++) {
                const stepEl = document.getElementById(`step${i}`);
                stepEl.classList.remove('active', 'completed');

                if (i < step) {
                    stepEl.classList.add('completed');
                } else if (i === step) {
                    stepEl.classList.add('active');
                }
            }
        }
    </script>
</body>
</html>
