<?php
// 完整的数据库修复方案
header('Content-Type: text/html; charset=UTF-8');
ini_set('default_charset', 'UTF-8');

session_start();

// 检查登录状态
if (!isset($_SESSION['cs_logged_in']) || !$_SESSION['cs_logged_in']) {
    echo '<h1>请先登录客服系统</h1>';
    exit;
}

require_once '../db_config.php';

echo '<h1>完整数据库修复</h1>';

try {
    $pdo = getDbConnection();
    
    if ($_POST['complete_fix'] ?? false) {
        echo '<h2>正在执行完整修复...</h2>';
        
        // 1. 修复 realtime_notifications 表
        echo '<h3>1. 修复 realtime_notifications 表</h3>';
        try {
            $pdo->exec("ALTER TABLE realtime_notifications ADD COLUMN message TEXT COMMENT '通知消息内容' AFTER title");
            echo '<p style="color: green;">✓ 添加 message 字段成功</p>';
        } catch (Exception $e) {
            if (strpos($e->getMessage(), 'Duplicate column name') !== false) {
                echo '<p style="color: blue;">message 字段已存在</p>';
            } else {
                echo '<p style="color: red;">添加 message 字段失败: ' . $e->getMessage() . '</p>';
            }
        }
        
        // 2. 完整修复 customer_service_messages 表
        echo '<h3>2. 修复 customer_service_messages 表</h3>';
        
        // 检查当前表结构
        $stmt = $pdo->query("DESCRIBE customer_service_messages");
        $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        $problematicFields = [];
        foreach ($columns as $column) {
            if ($column['Null'] === 'NO' && $column['Default'] === null && $column['Extra'] !== 'auto_increment') {
                $problematicFields[] = $column['Field'];
            }
        }
        
        if (!empty($problematicFields)) {
            echo '<p>发现问题字段：' . implode(', ', $problematicFields) . '</p>';
            
            // 修复每个问题字段
            foreach ($problematicFields as $field) {
                try {
                    switch ($field) {
                        case 'content':
                            $pdo->exec("ALTER TABLE customer_service_messages MODIFY COLUMN content TEXT NULL COMMENT '消息内容'");
                            echo '<p style="color: green;">✓ 修复 content 字段</p>';
                            break;
                        case 'message_type':
                            $pdo->exec("ALTER TABLE customer_service_messages MODIFY COLUMN message_type VARCHAR(50) NULL DEFAULT 'text' COMMENT '消息类型'");
                            echo '<p style="color: green;">✓ 修复 message_type 字段</p>';
                            break;
                        case 'sender_type':
                            $pdo->exec("ALTER TABLE customer_service_messages MODIFY COLUMN sender_type VARCHAR(50) NULL DEFAULT 'user' COMMENT '发送者类型'");
                            echo '<p style="color: green;">✓ 修复 sender_type 字段</p>';
                            break;
                        case 'sender_name':
                            $pdo->exec("ALTER TABLE customer_service_messages MODIFY COLUMN sender_name VARCHAR(100) NULL DEFAULT '' COMMENT '发送者姓名'");
                            echo '<p style="color: green;">✓ 修复 sender_name 字段</p>';
                            break;
                        case 'session_id':
                            $pdo->exec("ALTER TABLE customer_service_messages MODIFY COLUMN session_id VARCHAR(255) NOT NULL COMMENT '会话ID'");
                            echo '<p style="color: green;">✓ 修复 session_id 字段</p>';
                            break;
                        default:
                            echo '<p style="color: orange;">跳过字段: ' . $field . '</p>';
                    }
                } catch (Exception $e) {
                    echo '<p style="color: red;">修复 ' . $field . ' 字段失败: ' . $e->getMessage() . '</p>';
                }
            }
        } else {
            echo '<p style="color: green;">customer_service_messages 表结构正常</p>';
        }
        
        // 3. 测试插入
        echo '<h3>3. 测试数据插入</h3>';
        try {
            $testSessionId = 'test_complete_' . time();
            $testMessage = '完整修复测试消息';
            
            $stmt = $pdo->prepare("
                INSERT INTO customer_service_messages
                (session_id, sender_type, sender_id, sender_name, message_type, content, created_at)
                VALUES (?, 'system', NULL, '系统', 'system', ?, NOW())
            ");
            
            $stmt->execute([$testSessionId, $testMessage]);
            echo '<p style="color: green;">✓ 数据插入测试成功</p>';
            
            // 清理测试数据
            $stmt = $pdo->prepare("DELETE FROM customer_service_messages WHERE session_id = ?");
            $stmt->execute([$testSessionId]);
            echo '<p>测试数据已清理</p>';
            
        } catch (Exception $e) {
            echo '<p style="color: red;">✗ 数据插入测试失败: ' . $e->getMessage() . '</p>';
        }
        
        echo '<h2 style="color: green;">完整修复完成！</h2>';
    }
    
    // 显示当前状态
    echo '<h2>当前表结构状态</h2>';
    
    // realtime_notifications
    echo '<h3>realtime_notifications 表</h3>';
    $stmt = $pdo->query("SHOW COLUMNS FROM realtime_notifications LIKE 'message'");
    if ($stmt->rowCount() > 0) {
        echo '<p style="color: green;">✓ message 字段存在</p>';
    } else {
        echo '<p style="color: red;">✗ message 字段不存在</p>';
    }
    
    // customer_service_messages
    echo '<h3>customer_service_messages 表</h3>';
    $stmt = $pdo->query("DESCRIBE customer_service_messages");
    $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    $hasProblems = false;
    foreach ($columns as $column) {
        if ($column['Null'] === 'NO' && $column['Default'] === null && $column['Extra'] !== 'auto_increment') {
            if (!$hasProblems) {
                echo '<p style="color: red;">发现问题字段：</p><ul>';
                $hasProblems = true;
            }
            echo '<li>' . $column['Field'] . ' (不允许NULL且无默认值)</li>';
        }
    }
    
    if ($hasProblems) {
        echo '</ul>';
    } else {
        echo '<p style="color: green;">✓ 所有字段结构正常</p>';
    }
    
    // API测试
    echo '<h2>API功能测试</h2>';
    $stmt = $pdo->query("SELECT session_id FROM customer_service_sessions WHERE status = 'waiting' LIMIT 1");
    $testSession = $stmt->fetch();
    
    if ($testSession) {
        $testSessionId = $testSession['session_id'];
        echo '<p>测试会话ID: ' . $testSessionId . '</p>';
        echo '<button onclick="testAPI(\'' . $testSessionId . '\')" style="padding: 8px 16px; background: #28a745; color: white; border: none; border-radius: 3px; cursor: pointer;">测试接受API</button>';
        echo '<div id="result"></div>';
    } else {
        echo '<p>没有等待中的会话可供测试</p>';
    }
    
} catch (Exception $e) {
    echo '<p style="color: red;">错误: ' . $e->getMessage() . '</p>';
}
?>

<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>完整数据库修复</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        button { margin: 5px; padding: 8px 16px; }
        .fix-btn { background: #dc3545; color: white; border: none; border-radius: 3px; cursor: pointer; font-size: 16px; padding: 12px 24px; }
        pre { background: #f5f5f5; padding: 10px; border-radius: 5px; }
        h1, h2, h3 { color: #333; }
    </style>
</head>
<body>
    <?php if (!($_POST['complete_fix'] ?? false)): ?>
    <div style="background: #fff3cd; padding: 15px; border-radius: 5px; margin: 20px 0;">
        <h3>⚠️ 注意</h3>
        <p>这将执行完整的数据库修复，包括：</p>
        <ul>
            <li>添加 realtime_notifications.message 字段</li>
            <li>修复所有 customer_service_messages 表中不允许NULL且无默认值的字段</li>
            <li>测试数据插入功能</li>
        </ul>
        <p><strong>建议在执行前备份数据库</strong></p>
    </div>
    
    <form method="POST">
        <button type="submit" name="complete_fix" value="1" class="fix-btn">执行完整修复</button>
    </form>
    
    <p><a href="debug_table_structure.php">查看详细表结构</a></p>
    <?php endif; ?>
    
    <script>
        async function testAPI(sessionId) {
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = '<p>测试中...</p>';
            
            try {
                const response = await fetch('api/accept_session.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        sessionId: sessionId
                    })
                });
                
                const data = await response.json();
                
                if (data.success) {
                    resultDiv.innerHTML = '<h3 style="color: green;">🎉 API测试成功！</h3><pre>' + JSON.stringify(data, null, 2) + '</pre><p><a href="sessions.php">查看会话列表</a></p>';
                } else {
                    resultDiv.innerHTML = '<h3 style="color: red;">❌ API测试失败</h3><pre>' + JSON.stringify(data, null, 2) + '</pre>';
                }
                
            } catch (error) {
                resultDiv.innerHTML = '<h3 style="color: red;">❌ 网络错误</h3><p>' + error.message + '</p>';
            }
        }
    </script>
    
    <hr>
    <p>
        <a href="sessions.php">返回会话列表</a> | 
        <a href="simple_test.php">简化测试</a> | 
        <a href="debug_table_structure.php">表结构调试</a>
    </p>
</body>
</html>
