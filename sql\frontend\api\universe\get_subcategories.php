<?php
// 设置响应头
header('Content-Type: application/json');

// 启动会话
session_start();

// 检查用户是否已登录
if (!isset($_SESSION['user_id'])) {
    echo json_encode([
        'success' => false,
        'message' => '请先登录'
    ]);
    exit;
}

// 检查是否提供了分类ID
if (!isset($_GET['category_id']) || empty($_GET['category_id'])) {
    echo json_encode([
        'success' => false,
        'message' => '缺少分类ID'
    ]);
    exit;
}

$category_id = intval($_GET['category_id']);

// 引入数据库配置
require_once '../../../includes/db_config.php';

try {
    // 连接数据库
    $dsn = "mysql:host={$db_config['host']};dbname={$db_config['dbname']};charset={$db_config['charset']};port={$db_config['port']}";
    $pdo = new PDO($dsn, $db_config['username'], $db_config['password']);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

    // 查询子分类
    $stmt = $pdo->prepare("SELECT * FROM universe_subcategories WHERE category_id = :category_id AND status = 1 ORDER BY sort_order ASC");
    $stmt->execute(['category_id' => $category_id]);
    $subcategories = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // 返回结果
    echo json_encode([
        'success' => true,
        'subcategories' => $subcategories
    ]);
} catch (PDOException $e) {
    // 记录错误
    error_log("获取子分类错误: " . $e->getMessage());

    // 返回错误信息
    echo json_encode([
        'success' => false,
        'message' => '获取子分类失败，请稍后再试'
    ]);
}
