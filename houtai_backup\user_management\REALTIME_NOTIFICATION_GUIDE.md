# 实时通知功能使用指南

## 🎯 功能概述

现在您可以在用户详情页面直接发送验证码，用户前台会实时收到验证码弹窗通知！

## 🚀 使用步骤

### 1. 准备工作

#### 修复数据库字段（首次使用必须）
访问：`https://planet.vancrest.xyz/frontend/api/fix_database_fields.php`

这会自动添加缺失的数据库字段，包括：
- `realtime_notifications.priority`
- `realtime_notifications.expires_at`
- `realtime_notifications.delivered_at`
- `realtime_notifications.read_at`
- `users.online_status`
- `users.last_activity`
- `users.last_notification_check`

### 2. 后台发送验证码

1. **访问用户详情页面**：
   ```
   https://vansmrz.vancrest.xyz/houtai_backup/user_management/detail.php?id=用户ID
   ```

2. **点击"发送验证码"按钮**（在页面右侧操作区域）

3. **填写验证码信息**：
   - 手机号：用户的手机号
   - 验证码类型：选择合适的类型
   - 备注说明：用户会在弹窗中看到
   - 有效期：验证码有效时间

4. **点击发送**，系统会：
   - 生成6位数验证码
   - 保存到数据库
   - 创建实时通知记录
   - 推送到用户前台

### 3. 前台接收通知

用户在前台页面会：
1. **自动连接WebSocket通知服务**
2. **实时接收验证码弹窗**，包含：
   - 验证码（大字体显示）
   - 有效期倒计时
   - 管理员备注
   - 点击复制功能

## 🧪 测试方法

### 方法1：使用演示页面
1. 打开前台演示页面：
   ```
   https://planet.vancrest.xyz/frontend/demo_realtime_notifications.html
   ```

2. 确认WebSocket连接状态为"已连接"

3. 在后台发送验证码给用户ID为4的用户

4. 观察前台是否弹出验证码通知

### 方法2：集成到现有页面
在任何前台页面添加：
```html
<meta name="user-id" content="用户ID">
<script src="/frontend/js/realtime_notifications_integration.js"></script>
```

## 🔧 故障排除

### 问题1：发送失败 - 数据库操作失败
**解决方案**：访问 `fix_database_fields.php` 修复数据库字段

### 问题2：前台没有收到通知
**检查步骤**：
1. 确认WebSocket连接状态
2. 检查用户ID是否正确
3. 查看浏览器控制台是否有错误
4. 使用调试工具：`test_websocket_debug.html`

### 问题3：通知重复显示
**解决方案**：访问 `reset_notifications.php?user_id=用户ID` 重置通知状态

## 📁 相关文件

### 后台文件
- `detail.php` - 用户详情页面（包含发送验证码功能）
- `send_verification_code_debug.php` - 发送验证码API
- `action.php` - 其他用户操作API

### 前台文件
- `components/websocket_notifications.js` - WebSocket通知组件
- `js/realtime_notifications_integration.js` - 自动集成脚本
- `api/websocket_server.php` - WebSocket服务器
- `api/fix_database_fields.php` - 数据库修复工具

### 调试工具
- `demo_realtime_notifications.html` - 演示页面
- `test_websocket_debug.html` - 调试工具
- `test_complete_flow.html` - 完整流程测试

## 🎨 自定义配置

### 修改轮询频率
在 `websocket_notifications.js` 中：
```javascript
this.pollFrequency = 2000; // 2秒轮询一次
```

### 修改通知样式
在 `websocket_notifications.js` 的 `createVerificationModal` 方法中自定义CSS

### 添加新的通知类型
在 `handleNotification` 方法中添加新的 case 分支

## 🔒 安全说明

1. **权限验证**：只有登录的管理员才能发送验证码
2. **数据验证**：手机号格式、用户存在性等都会验证
3. **操作日志**：所有发送操作都会记录到 `admin_operation_logs`
4. **过期机制**：验证码有时效性，过期自动失效

## 📞 技术支持

如果遇到问题：
1. 查看浏览器控制台错误信息
2. 检查服务器PHP错误日志
3. 使用提供的调试工具进行排查
4. 确认数据库表结构完整

---

**现在您可以在用户详情页面直接使用实时验证码发送功能了！** 🎉
