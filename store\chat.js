import { defineStore } from 'pinia'
import { ref, computed } from 'vue'

export const useChatStore = defineStore('chat', () => {
  // 状态
  const currentSession = ref(null)
  const messages = ref([])
  const isConnected = ref(false)
  const isTyping = ref(false)
  const unreadCount = ref(0)
  const customerServiceInfo = ref(null)

  // 计算属性
  const hasActiveSession = computed(() => {
    return currentSession.value && currentSession.value.status === 'active'
  })

  const lastMessage = computed(() => {
    return messages.value.length > 0 ? messages.value[messages.value.length - 1] : null
  })

  const sessionStatus = computed(() => {
    return currentSession.value?.status || 'waiting'
  })

  // 方法
  const createSession = async () => {
    try {
      const result = await uniCloud.callFunction({
        name: 'chat-create-session',
        data: {}
      })

      if (result.result.success) {
        currentSession.value = result.result.data
        messages.value = []
        unreadCount.value = 0
        
        // 连接实时消息推送
        await connectRealtime()
        
        return { success: true, sessionId: currentSession.value.id }
      } else {
        return { success: false, message: result.result.message }
      }
    } catch (error) {
      console.error('创建会话失败:', error)
      return { success: false, message: '创建会话失败，请重试' }
    }
  }

  const sendMessage = async (content, type = 'text') => {
    if (!currentSession.value) {
      throw new Error('没有活动的会话')
    }

    try {
      // 先添加到本地消息列表（乐观更新）
      const tempMessage = {
        id: Date.now(),
        content,
        type,
        sender_type: 'user',
        created_at: new Date().toISOString(),
        status: 'sending'
      }
      
      messages.value.push(tempMessage)

      // 发送到服务器
      const result = await uniCloud.callFunction({
        name: 'chat-send-message',
        data: {
          sessionId: currentSession.value.id,
          content,
          type
        }
      })

      if (result.result.success) {
        // 更新消息状态
        const messageIndex = messages.value.findIndex(msg => msg.id === tempMessage.id)
        if (messageIndex !== -1) {
          messages.value[messageIndex] = {
            ...result.result.data,
            status: 'sent'
          }
        }
        
        return { success: true }
      } else {
        // 发送失败，移除临时消息
        const messageIndex = messages.value.findIndex(msg => msg.id === tempMessage.id)
        if (messageIndex !== -1) {
          messages.value.splice(messageIndex, 1)
        }
        
        return { success: false, message: result.result.message }
      }
    } catch (error) {
      console.error('发送消息失败:', error)
      return { success: false, message: '发送失败，请重试' }
    }
  }

  const loadMessages = async (sessionId, page = 1, pageSize = 20) => {
    try {
      const result = await uniCloud.callFunction({
        name: 'chat-get-messages',
        data: {
          sessionId,
          page,
          pageSize
        }
      })

      if (result.result.success) {
        const newMessages = result.result.data.messages
        
        if (page === 1) {
          messages.value = newMessages
        } else {
          // 历史消息插入到开头
          messages.value.unshift(...newMessages)
        }
        
        return { success: true, hasMore: result.result.data.hasMore }
      } else {
        return { success: false, message: result.result.message }
      }
    } catch (error) {
      console.error('加载消息失败:', error)
      return { success: false, message: '加载消息失败' }
    }
  }

  const connectRealtime = async () => {
    try {
      // 使用uniCloud实时数据推送
      const db = uniCloud.database()
      
      // 监听当前会话的新消息
      const watcher = db.collection('messages')
        .where({
          session_id: currentSession.value.id
        })
        .orderBy('created_at', 'desc')
        .limit(1)
        .watch({
          onChange: (snapshot) => {
            if (snapshot.docs.length > 0) {
              const newMessage = snapshot.docs[0]
              
              // 检查是否是新消息（避免重复）
              const exists = messages.value.find(msg => msg.id === newMessage.id)
              if (!exists && newMessage.sender_type !== 'user') {
                messages.value.push(newMessage)
                
                // 如果不是用户发送的消息，增加未读计数
                if (newMessage.sender_type === 'customer_service' || newMessage.sender_type === 'bot') {
                  unreadCount.value++
                  
                  // 播放提示音
                  playNotificationSound()
                  
                  // 显示通知
                  showMessageNotification(newMessage)
                }
              }
            }
          },
          onError: (error) => {
            console.error('实时消息监听错误:', error)
            isConnected.value = false
          }
        })

      isConnected.value = true
      
      // 保存watcher引用以便后续关闭
      currentSession.value.watcher = watcher
      
    } catch (error) {
      console.error('连接实时消息失败:', error)
      isConnected.value = false
    }
  }

  const disconnectRealtime = () => {
    if (currentSession.value?.watcher) {
      currentSession.value.watcher.close()
      currentSession.value.watcher = null
    }
    isConnected.value = false
  }

  const endSession = async () => {
    if (!currentSession.value) return

    try {
      const result = await uniCloud.callFunction({
        name: 'chat-end-session',
        data: {
          sessionId: currentSession.value.id
        }
      })

      if (result.result.success) {
        disconnectRealtime()
        currentSession.value = null
        messages.value = []
        unreadCount.value = 0
        
        return { success: true }
      } else {
        return { success: false, message: result.result.message }
      }
    } catch (error) {
      console.error('结束会话失败:', error)
      return { success: false, message: '结束会话失败' }
    }
  }

  const rateService = async (rating, comment = '') => {
    if (!currentSession.value) return

    try {
      const result = await uniCloud.callFunction({
        name: 'chat-rate-service',
        data: {
          sessionId: currentSession.value.id,
          rating,
          comment
        }
      })

      return result.result
    } catch (error) {
      console.error('评价服务失败:', error)
      return { success: false, message: '评价失败，请重试' }
    }
  }

  const markAsRead = async () => {
    if (!currentSession.value || unreadCount.value === 0) return

    try {
      await uniCloud.callFunction({
        name: 'chat-mark-read',
        data: {
          sessionId: currentSession.value.id
        }
      })

      unreadCount.value = 0
    } catch (error) {
      console.error('标记已读失败:', error)
    }
  }

  const playNotificationSound = () => {
    try {
      // 创建简单的提示音
      const audioContext = new (window.AudioContext || window.webkitAudioContext)()
      const oscillator = audioContext.createOscillator()
      const gainNode = audioContext.createGain()

      oscillator.connect(gainNode)
      gainNode.connect(audioContext.destination)

      oscillator.frequency.setValueAtTime(800, audioContext.currentTime)
      oscillator.frequency.setValueAtTime(1000, audioContext.currentTime + 0.1)

      gainNode.gain.setValueAtTime(0.1, audioContext.currentTime)
      gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.2)

      oscillator.start(audioContext.currentTime)
      oscillator.stop(audioContext.currentTime + 0.2)
    } catch (error) {
      console.log('无法播放提示音:', error)
    }
  }

  const showMessageNotification = (message) => {
    const senderName = message.sender_name || '客服'
    const content = message.content.length > 20 ? 
      message.content.substring(0, 20) + '...' : 
      message.content

    uni.showToast({
      title: `${senderName}: ${content}`,
      icon: 'none',
      duration: 3000
    })
  }

  const clearMessages = () => {
    messages.value = []
    unreadCount.value = 0
  }

  const setTyping = (typing) => {
    isTyping.value = typing
  }

  return {
    // 状态
    currentSession,
    messages,
    isConnected,
    isTyping,
    unreadCount,
    customerServiceInfo,
    
    // 计算属性
    hasActiveSession,
    lastMessage,
    sessionStatus,
    
    // 方法
    createSession,
    sendMessage,
    loadMessages,
    connectRealtime,
    disconnectRealtime,
    endSession,
    rateService,
    markAsRead,
    clearMessages,
    setTyping
  }
})
