-- =====================================================
-- 简化版管理日志表结构修复脚本
-- 兼容性更好，避免语法错误
-- =====================================================

-- 1. 为 admin_logs 表添加缺失字段
-- 检查字段是否存在，如果不存在则添加
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
     WHERE TABLE_SCHEMA = DATABASE() 
     AND TABLE_NAME = 'admin_logs' 
     AND COLUMN_NAME = 'employee_id') = 0,
    'ALTER TABLE admin_logs ADD COLUMN employee_id VARCHAR(50) DEFAULT NULL COMMENT "员工工号"',
    'SELECT "employee_id field already exists" as message'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
     WHERE TABLE_SCHEMA = DATABASE() 
     AND TABLE_NAME = 'admin_logs' 
     AND COLUMN_NAME = 'department') = 0,
    'ALTER TABLE admin_logs ADD COLUMN department VARCHAR(100) DEFAULT NULL COMMENT "部门名称"',
    'SELECT "department field already exists" as message'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 2. 创建 user_logs 表（如果不存在）
CREATE TABLE IF NOT EXISTS user_logs (
    id INT(11) NOT NULL AUTO_INCREMENT COMMENT '日志ID',
    user_id INT(11) NOT NULL COMMENT '用户ID',
    operator_name VARCHAR(100) NOT NULL COMMENT '操作员姓名',
    employee_id VARCHAR(50) DEFAULT NULL COMMENT '员工工号',
    department VARCHAR(100) DEFAULT NULL COMMENT '部门名称',
    type VARCHAR(50) NOT NULL COMMENT '日志类型',
    content TEXT NOT NULL COMMENT '日志内容',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    PRIMARY KEY (id),
    KEY idx_user_id (user_id),
    KEY idx_type (type),
    KEY idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户管理日志表';

-- 3. 为 admin_users 表添加员工信息字段
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
     WHERE TABLE_SCHEMA = DATABASE() 
     AND TABLE_NAME = 'admin_users' 
     AND COLUMN_NAME = 'employee_id') = 0,
    'ALTER TABLE admin_users ADD COLUMN employee_id VARCHAR(50) DEFAULT NULL COMMENT "员工工号"',
    'SELECT "admin_users employee_id field already exists" as message'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
     WHERE TABLE_SCHEMA = DATABASE() 
     AND TABLE_NAME = 'admin_users' 
     AND COLUMN_NAME = 'department') = 0,
    'ALTER TABLE admin_users ADD COLUMN department VARCHAR(100) DEFAULT NULL COMMENT "所属部门"',
    'SELECT "admin_users department field already exists" as message'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 4. 添加索引优化查询性能
-- 为 admin_logs 表添加索引
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS 
     WHERE TABLE_SCHEMA = DATABASE() 
     AND TABLE_NAME = 'admin_logs' 
     AND INDEX_NAME = 'idx_employee_id') = 0,
    'ALTER TABLE admin_logs ADD INDEX idx_employee_id (employee_id)',
    'SELECT "admin_logs employee_id index already exists" as message'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 5. 更新现有管理员的员工信息（示例数据）
UPDATE admin_users SET 
    employee_id = CASE 
        WHEN id = 1 THEN 'A001'
        WHEN id = 2 THEN 'A002'
        WHEN id = 3 THEN 'A003'
        ELSE CONCAT('EMP', LPAD(id, 3, '0'))
    END,
    department = CASE 
        WHEN id = 1 THEN '系统管理部'
        WHEN id = 2 THEN '用户运营部'
        WHEN id = 3 THEN '客户服务部'
        ELSE '综合管理部'
    END
WHERE employee_id IS NULL OR employee_id = '';

-- 6. 验证修复结果
SELECT 'Admin logs table structure fixed successfully!' as message;
