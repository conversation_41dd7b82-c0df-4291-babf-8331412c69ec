<?php
/**
 * IP地理位置API测试
 */

require_once 'login_logger.php';

header('Content-Type: application/json');

$ip = $_GET['ip'] ?? '';

if (empty($ip)) {
    echo json_encode(['error' => 'IP地址不能为空']);
    exit;
}

// 测试API调用
$result = getLocationFromAPI($ip);
$fallback = getLocationFromIPRange($ip);

// 同时调用API获取详细信息
try {
    $url = "http://ip-api.com/json/{$ip}?lang=zh-CN&fields=status,country,regionName,city,lat,lon,isp,org";
    
    $context = stream_context_create([
        'http' => [
            'timeout' => 5,
            'method' => 'GET',
            'header' => 'User-Agent: Mozilla/5.0 (compatible; LocationBot/1.0)'
        ]
    ]);
    
    $response = @file_get_contents($url, false, $context);
    
    if ($response !== false) {
        $data = json_decode($response, true);
        $data['final_location'] = $result;
        $data['fallback_location'] = $fallback;
        echo json_encode($data);
    } else {
        echo json_encode([
            'status' => 'fail',
            'message' => 'API调用失败',
            'final_location' => $result,
            'fallback_location' => $fallback
        ]);
    }
} catch (Exception $e) {
    echo json_encode([
        'status' => 'error',
        'message' => $e->getMessage(),
        'final_location' => $result,
        'fallback_location' => $fallback
    ]);
}
?>
