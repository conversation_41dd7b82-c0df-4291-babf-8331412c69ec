-- 创建数据库
CREATE DATABASE IF NOT EXISTS `quwanplanet` DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci;
USE `quwanplanet`;

-- 用户表
CREATE TABLE IF NOT EXISTS `users` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `quwanplanet_id` varchar(7) NOT NULL COMMENT '趣玩星球ID',
  `username` varchar(50) NOT NULL COMMENT '用户名',
  `password` varchar(255) NOT NULL COMMENT '密码',
  `email` varchar(100) DEFAULT NULL COMMENT '邮箱',
  `phone` varchar(20) DEFAULT NULL COMMENT '手机号',
  `avatar` varchar(255) DEFAULT NULL COMMENT '头像URL',
  `gender` enum('male','female','other') DEFAULT NULL COMMENT '性别',
  `birth_date` date DEFAULT NULL COMMENT '出生日期',
  `region` varchar(100) DEFAULT NULL COMMENT '地区',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `last_login` timestamp NULL DEFAULT NULL COMMENT '最后登录时间',
  `status` enum('active','banned','deleted') NOT NULL DEFAULT 'active' COMMENT '用户状态',
  PRIMARY KEY (`id`),
  UNIQUE KEY `quwanplanet_id` (`quwanplanet_id`),
  UNIQUE KEY `username` (`username`),
  UNIQUE KEY `email` (`email`),
  UNIQUE KEY `phone` (`phone`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户表';

-- 用户标签表
CREATE TABLE IF NOT EXISTS `user_tags` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `tag_id` int(11) NOT NULL COMMENT '标签ID',
  `tag_type` enum('skill','personality','region','entertainment') NOT NULL COMMENT '标签类型',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `user_id` (`user_id`),
  KEY `tag_id` (`tag_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户标签表';

-- 标签表
CREATE TABLE IF NOT EXISTS `tags` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(50) NOT NULL COMMENT '标签名称',
  `type` enum('skill','personality','region','entertainment') NOT NULL COMMENT '标签类型',
  `category` enum('city','game','travel') DEFAULT NULL COMMENT '标签分类',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `name_type` (`name`,`type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='标签表';

-- 活动表
CREATE TABLE IF NOT EXISTS `activities` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `title` varchar(100) NOT NULL COMMENT '活动标题',
  `description` text NOT NULL COMMENT '活动描述',
  `cover_image` varchar(255) DEFAULT NULL COMMENT '封面图片URL',
  `location` varchar(255) DEFAULT NULL COMMENT '活动地点',
  `start_time` datetime NOT NULL COMMENT '开始时间',
  `end_time` datetime NOT NULL COMMENT '结束时间',
  `max_participants` int(11) DEFAULT NULL COMMENT '最大参与人数',
  `price` decimal(10,2) DEFAULT NULL COMMENT '活动价格',
  `organizer_id` int(11) NOT NULL COMMENT '组织者ID',
  `status` enum('draft','published','cancelled','completed') NOT NULL DEFAULT 'draft' COMMENT '活动状态',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `organizer_id` (`organizer_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='活动表';

-- 订单表
CREATE TABLE IF NOT EXISTS `orders` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `order_no` varchar(20) NOT NULL COMMENT '订单编号',
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `activity_id` int(11) DEFAULT NULL COMMENT '活动ID',
  `playmate_id` int(11) DEFAULT NULL COMMENT '玩伴ID',
  `amount` decimal(10,2) NOT NULL COMMENT '订单金额',
  `status` enum('pending','paid','cancelled','refunded','completed') NOT NULL DEFAULT 'pending' COMMENT '订单状态',
  `payment_method` varchar(50) DEFAULT NULL COMMENT '支付方式',
  `payment_time` datetime DEFAULT NULL COMMENT '支付时间',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `order_no` (`order_no`),
  KEY `user_id` (`user_id`),
  KEY `activity_id` (`activity_id`),
  KEY `playmate_id` (`playmate_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='订单表';

-- 好友关系表
CREATE TABLE IF NOT EXISTS `friendships` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `friend_id` int(11) NOT NULL COMMENT '好友ID',
  `status` enum('pending','accepted','blocked') NOT NULL DEFAULT 'pending' COMMENT '好友状态',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `user_friend` (`user_id`,`friend_id`),
  KEY `user_id` (`user_id`),
  KEY `friend_id` (`friend_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='好友关系表';

-- 群聊表
CREATE TABLE IF NOT EXISTS `groups` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL COMMENT '群名称',
  `description` text DEFAULT NULL COMMENT '群描述',
  `avatar` varchar(255) DEFAULT NULL COMMENT '群头像',
  `owner_id` int(11) NOT NULL COMMENT '群主ID',
  `member_count` int(11) DEFAULT 0 COMMENT '成员数量',
  `max_members` int(11) DEFAULT 500 COMMENT '最大成员数',
  `is_public` tinyint(1) DEFAULT 1 COMMENT '是否公开群',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `owner_id` (`owner_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='群聊表';

-- 群成员表
CREATE TABLE IF NOT EXISTS `group_members` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `group_id` int(11) NOT NULL COMMENT '群ID',
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `role` enum('member','admin','owner') NOT NULL DEFAULT 'member' COMMENT '角色',
  `nickname` varchar(50) DEFAULT NULL COMMENT '群昵称',
  `joined_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '加入时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `group_user` (`group_id`,`user_id`),
  KEY `group_id` (`group_id`),
  KEY `user_id` (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='群成员表';

-- 消息表（重新设计支持群聊和私聊）
CREATE TABLE IF NOT EXISTS `messages` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `sender_id` int(11) NOT NULL COMMENT '发送者ID',
  `chat_type` enum('private','group') NOT NULL COMMENT '聊天类型',
  `chat_id` int(11) NOT NULL COMMENT '聊天ID（私聊时为接收者ID，群聊时为群ID）',
  `content` text NOT NULL COMMENT '消息内容',
  `message_type` enum('text','image','video','audio','file') NOT NULL DEFAULT 'text' COMMENT '消息类型',
  `file_url` varchar(255) DEFAULT NULL COMMENT '文件URL',
  `file_name` varchar(255) DEFAULT NULL COMMENT '文件名',
  `file_size` int(11) DEFAULT NULL COMMENT '文件大小',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `sender_id` (`sender_id`),
  KEY `chat_type_id` (`chat_type`,`chat_id`),
  KEY `created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='消息表';

-- 消息已读状态表
CREATE TABLE IF NOT EXISTS `message_reads` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `message_id` int(11) NOT NULL COMMENT '消息ID',
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `read_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '已读时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `message_user` (`message_id`,`user_id`),
  KEY `message_id` (`message_id`),
  KEY `user_id` (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='消息已读状态表';

-- 管理员表
CREATE TABLE IF NOT EXISTS `admins` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `admin_id` varchar(10) NOT NULL COMMENT '管理员ID',
  `name` varchar(50) NOT NULL COMMENT '姓名',
  `department` varchar(50) NOT NULL COMMENT '部门',
  `role` enum('admin','super_admin') NOT NULL DEFAULT 'admin' COMMENT '角色',
  `password` varchar(255) NOT NULL COMMENT '密码',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `last_login` timestamp NULL DEFAULT NULL COMMENT '最后登录时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `admin_id` (`admin_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='管理员表';

-- 初始化超级管理员
INSERT INTO `admins` (`admin_id`, `name`, `department`, `role`, `password`) VALUES
('12001', '姚家荣', '总经办', 'super_admin', '$2y$10$8MNjTMxMoJQsEHFLzIaKxeOgwzKEAcHYV9JYjYrCaW4pEkOPPY7Hy'); -- 密码: 122388
