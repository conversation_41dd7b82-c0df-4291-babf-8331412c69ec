<?php
session_start();

// 检查用户是否已登录
if (!isset($_SESSION['user_id'])) {
    header('Location: ../login/index.php');
    exit;
}
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <meta name="theme-color" content="#1E90FF">
    <title>订单 - 趣玩星球</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'PingFang SC', 'Helvetica Neue', Arial, sans-serif;
        }
        
        body {
            background-color: #f8f9fa;
            color: #333333;
            line-height: 1.6;
            min-height: 100vh;
        }
        
        .header-bar {
            display: flex;
            align-items: center;
            padding: 15px;
            background-color: #1E90FF;
            color: white;
            position: sticky;
            top: 0;
            z-index: 100;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }
        
        .back-button {
            margin-right: 15px;
            font-size: 18px;
            color: white;
            text-decoration: none;
        }
        
        .header-title {
            font-size: 18px;
            font-weight: bold;
        }
        
        .tabs {
            display: flex;
            background-color: white;
            margin-bottom: 15px;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
            position: sticky;
            top: 54px;
            z-index: 99;
        }
        
        .tab {
            flex: 1;
            text-align: center;
            padding: 12px 0;
            font-size: 15px;
            color: #666;
            position: relative;
            cursor: pointer;
        }
        
        .tab.active {
            color: #1E90FF;
            font-weight: 500;
        }
        
        .tab.active:after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 20px;
            height: 3px;
            background-color: #1E90FF;
            border-radius: 3px;
        }
        
        .order-container {
            padding: 15px;
        }
        
        .order-list {
            display: flex;
            flex-direction: column;
            gap: 15px;
        }
        
        .order-card {
            background-color: white;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
        }
        
        .order-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px 15px;
            border-bottom: 1px solid #f0f0f0;
        }
        
        .order-number {
            font-size: 13px;
            color: #999;
        }
        
        .order-status {
            font-size: 14px;
            font-weight: 500;
        }
        
        .status-pending {
            color: #FF9800;
        }
        
        .status-paid {
            color: #4CAF50;
        }
        
        .status-completed {
            color: #1E90FF;
        }
        
        .status-cancelled {
            color: #F44336;
        }
        
        .order-content {
            display: flex;
            padding: 15px;
            border-bottom: 1px solid #f0f0f0;
        }
        
        .order-image {
            width: 80px;
            height: 80px;
            border-radius: 8px;
            overflow: hidden;
            margin-right: 15px;
            background-color: #f5f5f5;
        }
        
        .order-image img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }
        
        .order-info {
            flex: 1;
        }
        
        .order-title {
            font-size: 16px;
            font-weight: 500;
            margin-bottom: 5px;
            color: #333;
        }
        
        .order-desc {
            font-size: 13px;
            color: #666;
            margin-bottom: 8px;
        }
        
        .order-time {
            font-size: 12px;
            color: #999;
        }
        
        .order-price {
            font-size: 16px;
            font-weight: bold;
            color: #FF6B6B;
        }
        
        .order-footer {
            display: flex;
            justify-content: flex-end;
            align-items: center;
            padding: 12px 15px;
            gap: 10px;
        }
        
        .order-btn {
            padding: 8px 15px;
            border-radius: 20px;
            font-size: 13px;
            font-weight: 500;
            cursor: pointer;
            text-decoration: none;
        }
        
        .btn-primary {
            background-color: #1E90FF;
            color: white;
            border: none;
        }
        
        .btn-secondary {
            background-color: white;
            color: #666;
            border: 1px solid #ddd;
        }
        
        .empty-state {
            text-align: center;
            padding: 50px 20px;
            color: #999;
        }
        
        .empty-state i {
            font-size: 50px;
            margin-bottom: 15px;
            color: #ddd;
        }
        
        .empty-state p {
            font-size: 15px;
            margin-bottom: 20px;
        }
        
        .empty-state .btn {
            display: inline-block;
            padding: 10px 20px;
            background-color: #1E90FF;
            color: white;
            border-radius: 20px;
            text-decoration: none;
            font-size: 14px;
            font-weight: 500;
            box-shadow: 0 4px 10px rgba(30, 144, 255, 0.3);
        }
    </style>
    <!-- 引入字体图标库 -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <!-- 顶部导航栏 -->
    <div class="header-bar">
        <a href="../profile/index.php" class="back-button">
            <i class="fas fa-arrow-left"></i>
        </a>
        <div class="header-title">订单</div>
    </div>
    
    <!-- 标签页 -->
    <div class="tabs">
        <div class="tab active" data-tab="all">全部</div>
        <div class="tab" data-tab="pending">待付款</div>
        <div class="tab" data-tab="ongoing">进行中</div>
        <div class="tab" data-tab="completed">已完成</div>
    </div>
    
    <div class="order-container">
        <div class="tab-content active" id="all-content">
            <div class="empty-state">
                <i class="fas fa-clipboard-list"></i>
                <p>暂无订单记录</p>
                <a href="../home/<USER>" class="btn">去首页看看</a>
            </div>
            
            <!-- 订单示例（当有数据时显示）
            <div class="order-list">
                <div class="order-card">
                    <div class="order-header">
                        <div class="order-number">订单号：2025051500001</div>
                        <div class="order-status status-paid">已支付</div>
                    </div>
                    <div class="order-content">
                        <div class="order-image">
                            <img src="https://s1.imagehub.cc/images/2025/05/15/activity1.jpg" alt="订单图片">
                        </div>
                        <div class="order-info">
                            <div class="order-title">周末城市探索之旅</div>
                            <div class="order-desc">预约时间：2025-05-20 14:00</div>
                            <div class="order-time">下单时间：2025-05-15 10:30</div>
                        </div>
                        <div class="order-price">¥198.00</div>
                    </div>
                    <div class="order-footer">
                        <a href="#" class="order-btn btn-secondary">联系客服</a>
                        <a href="#" class="order-btn btn-primary">查看详情</a>
                    </div>
                </div>
                
                <div class="order-card">
                    <div class="order-header">
                        <div class="order-number">订单号：2025051400001</div>
                        <div class="order-status status-completed">已完成</div>
                    </div>
                    <div class="order-content">
                        <div class="order-image">
                            <img src="https://s1.imagehub.cc/images/2025/05/15/activity2.jpg" alt="订单图片">
                        </div>
                        <div class="order-info">
                            <div class="order-title">王者荣耀排位陪玩</div>
                            <div class="order-desc">服务时长：2小时</div>
                            <div class="order-time">下单时间：2025-05-14 20:15</div>
                        </div>
                        <div class="order-price">¥100.00</div>
                    </div>
                    <div class="order-footer">
                        <a href="#" class="order-btn btn-secondary">删除订单</a>
                        <a href="#" class="order-btn btn-secondary">再次购买</a>
                        <a href="#" class="order-btn btn-primary">评价</a>
                    </div>
                </div>
            </div>
            -->
        </div>
        
        <div class="tab-content" id="pending-content" style="display: none;">
            <div class="empty-state">
                <i class="fas fa-hourglass-half"></i>
                <p>暂无待付款订单</p>
            </div>
        </div>
        
        <div class="tab-content" id="ongoing-content" style="display: none;">
            <div class="empty-state">
                <i class="fas fa-spinner"></i>
                <p>暂无进行中订单</p>
            </div>
        </div>
        
        <div class="tab-content" id="completed-content" style="display: none;">
            <div class="empty-state">
                <i class="fas fa-check-circle"></i>
                <p>暂无已完成订单</p>
            </div>
        </div>
    </div>
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 标签页切换
            const tabs = document.querySelectorAll('.tab');
            const tabContents = document.querySelectorAll('.tab-content');
            
            tabs.forEach(tab => {
                tab.addEventListener('click', function() {
                    const tabId = this.getAttribute('data-tab');
                    
                    // 移除所有标签和内容的active类
                    tabs.forEach(t => t.classList.remove('active'));
                    tabContents.forEach(c => c.style.display = 'none');
                    
                    // 添加active类到当前标签和对应内容
                    this.classList.add('active');
                    document.getElementById(`${tabId}-content`).style.display = 'block';
                });
            });
        });
    </script>
</body>
</html>
