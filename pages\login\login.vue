<template>
  <view class="login-container">
    <!-- 背景装饰 -->
    <view class="bg-decoration">
      <view class="decoration-circle circle-1"></view>
      <view class="decoration-circle circle-2"></view>
      <view class="decoration-circle circle-3"></view>
    </view>

    <!-- 主要内容 -->
    <view class="login-content">
      <!-- Logo和标题 -->
      <view class="header">
        <image class="logo" src="/static/images/logo.png" mode="aspectFit"></image>
        <text class="title">趣玩星球</text>
        <text class="subtitle">欢迎来到我们的社交平台</text>
      </view>

      <!-- 登录表单 -->
      <view class="form-container">
        <!-- 手机号输入 -->
        <view class="input-group">
          <view class="input-label">
            <text class="label-icon">📱</text>
            <text class="label-text">手机号</text>
          </view>
          <input 
            class="phone-input"
            type="number"
            placeholder="请输入手机号"
            v-model="phone"
            maxlength="11"
            @input="onPhoneInput"
          />
          <view v-if="phoneError" class="error-text">{{ phoneError }}</view>
        </view>

        <!-- 验证码输入 -->
        <view class="input-group">
          <view class="input-label">
            <text class="label-icon">🔑</text>
            <text class="label-text">验证码</text>
          </view>
          
          <!-- 6位验证码输入框 -->
          <view class="code-input-container">
            <input
              v-for="(digit, index) in codeDigits"
              :key="index"
              class="code-digit"
              type="number"
              maxlength="1"
              v-model="codeDigits[index]"
              @input="onCodeInput(index, $event)"
              @focus="onCodeFocus(index)"
              :ref="el => codeInputRefs[index] = el"
            />
          </view>
          
          <!-- 发送验证码按钮 -->
          <view class="send-code-container">
            <button 
              class="send-code-btn"
              :class="{ disabled: !canSendCode }"
              :disabled="!canSendCode"
              @click="sendCode"
            >
              {{ sendCodeText }}
            </button>
          </view>
          
          <view v-if="codeError" class="error-text">{{ codeError }}</view>
        </view>

        <!-- 登录按钮 -->
        <button 
          class="login-btn"
          :class="{ disabled: !canLogin }"
          :disabled="!canLogin"
          @click="handleLogin"
        >
          <view v-if="isLogging" class="loading-star"></view>
          <text>{{ isLogging ? '登录中...' : '登录' }}</text>
        </button>

        <!-- 提示信息 -->
        <view class="tips">
          <text class="tip-text">
            首次登录将自动注册账号
          </text>
          <text class="tip-text">
            登录即表示同意《用户协议》和《隐私政策》
          </text>
        </view>
      </view>
    </view>

    <!-- 管理员入口 -->
    <view class="admin-entrance" @click="goToAdminLogin">
      <text class="admin-text">管理员登录</text>
    </view>
  </view>
</template>

<script>
import { ref, computed, onMounted } from 'vue'
import { useUserStore } from '@/store/user'

export default {
  name: 'Login',
  setup() {
    const userStore = useUserStore()
    
    // 响应式数据
    const phone = ref('')
    const codeDigits = ref(['', '', '', '', '', ''])
    const phoneError = ref('')
    const codeError = ref('')
    const isLogging = ref(false)
    const countdown = ref(0)
    const codeInputRefs = ref([])

    // 计算属性
    const verificationCode = computed(() => {
      return codeDigits.value.join('')
    })

    const canSendCode = computed(() => {
      const phoneValid = userStore.validatePhone(phone.value)
      return phoneValid.valid && countdown.value === 0
    })

    const canLogin = computed(() => {
      const phoneValid = userStore.validatePhone(phone.value)
      return phoneValid.valid && verificationCode.value.length === 6 && !isLogging.value
    })

    const sendCodeText = computed(() => {
      return countdown.value > 0 ? `${countdown.value}s后重发` : '发送验证码'
    })

    // 方法
    const onPhoneInput = () => {
      phoneError.value = ''
      
      // 验证手机号
      if (phone.value.length === 11) {
        const validation = userStore.validatePhone(phone.value)
        if (!validation.valid) {
          phoneError.value = validation.message
        }
      }
    }

    const onCodeInput = (index, event) => {
      const value = event.detail.value
      codeDigits.value[index] = value
      
      codeError.value = ''
      
      // 自动跳转到下一个输入框
      if (value && index < 5) {
        const nextInput = codeInputRefs.value[index + 1]
        if (nextInput) {
          nextInput.focus()
        }
      }
      
      // 如果删除内容，跳转到上一个输入框
      if (!value && index > 0) {
        const prevInput = codeInputRefs.value[index - 1]
        if (prevInput) {
          prevInput.focus()
        }
      }
    }

    const onCodeFocus = (index) => {
      // 如果前面的输入框为空，自动聚焦到第一个空的输入框
      for (let i = 0; i < index; i++) {
        if (!codeDigits.value[i]) {
          const emptyInput = codeInputRefs.value[i]
          if (emptyInput) {
            emptyInput.focus()
          }
          return
        }
      }
    }

    const sendCode = async () => {
      if (!canSendCode.value) return

      const validation = userStore.validatePhone(phone.value)
      if (!validation.valid) {
        phoneError.value = validation.message
        return
      }

      try {
        uni.showLoading({ title: '发送中...' })
        
        const result = await userStore.sendVerificationCode(phone.value)
        
        uni.hideLoading()
        
        if (result.success) {
          uni.showToast({
            title: '验证码已发送',
            icon: 'success'
          })
          
          // 开始倒计时
          startCountdown()
        } else {
          uni.showToast({
            title: result.message || '发送失败',
            icon: 'error'
          })
        }
      } catch (error) {
        uni.hideLoading()
        uni.showToast({
          title: '发送失败，请重试',
          icon: 'error'
        })
      }
    }

    const startCountdown = () => {
      countdown.value = 60
      const timer = setInterval(() => {
        countdown.value--
        if (countdown.value <= 0) {
          clearInterval(timer)
        }
      }, 1000)
    }

    const handleLogin = async () => {
      if (!canLogin.value) return

      // 验证手机号
      const phoneValidation = userStore.validatePhone(phone.value)
      if (!phoneValidation.valid) {
        phoneError.value = phoneValidation.message
        return
      }

      // 验证验证码
      if (verificationCode.value.length !== 6) {
        codeError.value = '请输入6位验证码'
        return
      }

      try {
        isLogging.value = true
        
        const result = await userStore.login(phone.value, verificationCode.value)
        
        if (result.success) {
          uni.showToast({
            title: '登录成功',
            icon: 'success'
          })
          
          // 检查是否需要完善信息
          if (!result.data.nickname || !result.data.avatar) {
            // 跳转到完善信息页面
            uni.redirectTo({
              url: '/pages/login/register'
            })
          } else {
            // 跳转到首页
            uni.switchTab({
              url: '/pages/index/index'
            })
          }
        } else {
          codeError.value = result.message || '登录失败'
        }
      } catch (error) {
        console.error('登录失败:', error)
        codeError.value = '登录失败，请重试'
      } finally {
        isLogging.value = false
      }
    }

    const goToAdminLogin = () => {
      uni.navigateTo({
        url: '/pages/admin/login'
      })
    }

    // 页面加载时检查登录状态
    onMounted(() => {
      if (userStore.isLoggedIn) {
        uni.switchTab({
          url: '/pages/index/index'
        })
      }
    })

    return {
      phone,
      codeDigits,
      phoneError,
      codeError,
      isLogging,
      countdown,
      codeInputRefs,
      verificationCode,
      canSendCode,
      canLogin,
      sendCodeText,
      onPhoneInput,
      onCodeInput,
      onCodeFocus,
      sendCode,
      handleLogin,
      goToAdminLogin
    }
  }
}
</script>

<style lang="scss" scoped>
.login-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #6F7BF5 0%, #9B59B6 100%);
  position: relative;
  overflow: hidden;
}

.bg-decoration {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
}

.decoration-circle {
  position: absolute;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
}

.circle-1 {
  width: 200px;
  height: 200px;
  top: -100px;
  right: -100px;
}

.circle-2 {
  width: 150px;
  height: 150px;
  bottom: 100px;
  left: -75px;
}

.circle-3 {
  width: 100px;
  height: 100px;
  top: 50%;
  right: 20px;
}

.login-content {
  position: relative;
  z-index: 1;
  padding: var(--spacing-xl) var(--spacing-lg);
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.header {
  text-align: center;
  margin-bottom: var(--spacing-xxl);
}

.logo {
  width: 80px;
  height: 80px;
  margin-bottom: var(--spacing-md);
}

.title {
  display: block;
  font-size: var(--font-xxxl);
  font-weight: 700;
  color: white;
  margin-bottom: var(--spacing-sm);
}

.subtitle {
  display: block;
  font-size: var(--font-md);
  color: rgba(255, 255, 255, 0.8);
}

.form-container {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: var(--radius-xl);
  padding: var(--spacing-xl);
  box-shadow: var(--shadow-xl);
}

.input-group {
  margin-bottom: var(--spacing-lg);
}

.input-label {
  display: flex;
  align-items: center;
  margin-bottom: var(--spacing-sm);
}

.label-icon {
  font-size: var(--font-lg);
  margin-right: var(--spacing-sm);
}

.label-text {
  font-size: var(--font-md);
  font-weight: 600;
  color: var(--text-primary);
}

.phone-input {
  width: 100%;
  height: 48px;
  padding: 0 var(--spacing-md);
  border: 2px solid var(--border-color);
  border-radius: var(--radius-md);
  font-size: var(--font-md);
  background: var(--bg-primary);
  transition: border-color 0.2s ease;
}

.phone-input:focus {
  border-color: var(--primary-color);
}

.code-input-container {
  display: flex;
  gap: var(--spacing-sm);
  margin-bottom: var(--spacing-md);
}

.code-digit {
  flex: 1;
  height: 48px;
  text-align: center;
  border: 2px solid var(--border-color);
  border-radius: var(--radius-md);
  font-size: var(--font-lg);
  font-weight: 600;
  background: var(--bg-primary);
  transition: border-color 0.2s ease;
}

.code-digit:focus {
  border-color: var(--primary-color);
}

.send-code-container {
  margin-bottom: var(--spacing-sm);
}

.send-code-btn {
  width: 100%;
  height: 40px;
  background: var(--secondary-color);
  color: var(--primary-color);
  border: none;
  border-radius: var(--radius-md);
  font-size: var(--font-sm);
  font-weight: 600;
  transition: all 0.2s ease;
}

.send-code-btn:not(.disabled):active {
  transform: scale(0.98);
}

.send-code-btn.disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.login-btn {
  width: 100%;
  height: 48px;
  background: linear-gradient(135deg, var(--primary-color) 0%, #9B59B6 100%);
  color: white;
  border: none;
  border-radius: var(--radius-md);
  font-size: var(--font-md);
  font-weight: 600;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-sm);
  margin-bottom: var(--spacing-lg);
  transition: all 0.2s ease;
}

.login-btn:not(.disabled):active {
  transform: scale(0.98);
}

.login-btn.disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.error-text {
  color: var(--warning-color);
  font-size: var(--font-sm);
  margin-top: var(--spacing-xs);
}

.tips {
  text-align: center;
}

.tip-text {
  display: block;
  font-size: var(--font-xs);
  color: var(--text-secondary);
  line-height: 1.5;
  margin-bottom: var(--spacing-xs);
}

.admin-entrance {
  position: absolute;
  bottom: var(--spacing-lg);
  right: var(--spacing-lg);
  z-index: 2;
}

.admin-text {
  color: rgba(255, 255, 255, 0.8);
  font-size: var(--font-sm);
  text-decoration: underline;
}
</style>
