<?php
session_start();
require_once '../db_config.php';

// 检查管理员登录状态
if (!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
    header('Location: ../login.php');
    exit;
}

echo "<h1>优惠券表结构检查和自动修复</h1>";
echo "<style>
    .success { color: green; font-weight: bold; }
    .error { color: red; font-weight: bold; }
    .warning { color: orange; font-weight: bold; }
    .info { color: blue; font-weight: bold; }
    pre { background: #f5f5f5; padding: 10px; border-radius: 5px; }
</style>";

try {
    $pdo = getDbConnection();
    echo "<p class='success'>✅ 数据库连接成功！</p>";
    
    // 检查 camping_coupons 表是否存在
    $stmt = $pdo->query("SHOW TABLES LIKE 'camping_coupons'");
    if ($stmt->rowCount() == 0) {
        echo "<p class='error'>❌ camping_coupons 表不存在！</p>";
        echo "<p>请先创建优惠券表。</p>";
        exit;
    }
    
    echo "<p class='success'>✅ camping_coupons 表存在</p>";
    
    // 检查 used_quantity 字段是否存在
    $stmt = $pdo->query("SHOW COLUMNS FROM camping_coupons LIKE 'used_quantity'");
    $field_exists = $stmt->rowCount() > 0;
    
    if ($field_exists) {
        echo "<p class='success'>✅ used_quantity 字段已存在</p>";
        
        // 检查数据是否正确
        $stmt = $pdo->query("SELECT COUNT(*) as count FROM camping_coupons WHERE used_quantity IS NULL");
        $null_count = $stmt->fetch()['count'];
        
        if ($null_count > 0) {
            echo "<p class='warning'>⚠️ 发现 {$null_count} 条记录的 used_quantity 为 NULL，正在修复...</p>";
            
            // 修复 NULL 值
            $stmt = $pdo->prepare("
                UPDATE camping_coupons cc
                SET used_quantity = (
                    SELECT COUNT(*)
                    FROM user_camping_coupons ucc
                    WHERE ucc.coupon_id = cc.id AND ucc.status = 'used'
                )
                WHERE used_quantity IS NULL
            ");
            $stmt->execute();
            
            echo "<p class='success'>✅ NULL 值已修复</p>";
        }
        
    } else {
        echo "<p class='error'>❌ used_quantity 字段不存在，正在自动添加...</p>";
        
        try {
            // 添加字段
            $stmt = $pdo->exec("ALTER TABLE camping_coupons ADD COLUMN used_quantity INT(11) DEFAULT 0 COMMENT '已使用数量'");
            echo "<p class='success'>✅ used_quantity 字段添加成功</p>";
            
            // 初始化数据
            echo "<p class='info'>🔄 正在初始化数据...</p>";
            $stmt = $pdo->prepare("
                UPDATE camping_coupons cc
                SET used_quantity = (
                    SELECT COUNT(*)
                    FROM user_camping_coupons ucc
                    WHERE ucc.coupon_id = cc.id AND ucc.status = 'used'
                )
            ");
            $stmt->execute();
            
            echo "<p class='success'>✅ 数据初始化完成</p>";
            
        } catch (PDOException $e) {
            echo "<p class='error'>❌ 自动修复失败：" . htmlspecialchars($e->getMessage()) . "</p>";
            echo "<p>请手动执行以下SQL语句：</p>";
            echo "<pre>ALTER TABLE camping_coupons ADD COLUMN used_quantity INT(11) DEFAULT 0;</pre>";
        }
    }
    
    // 显示统计信息
    echo "<h2>统计信息</h2>";
    $stmt = $pdo->query("
        SELECT 
            COUNT(*) as total_coupons,
            SUM(total_quantity) as total_issued,
            SUM(claimed_quantity) as total_claimed,
            SUM(COALESCE(used_quantity, 0)) as total_used
        FROM camping_coupons
    ");
    $stats = $stmt->fetch(PDO::FETCH_ASSOC);
    
    echo "<table border='1' style='border-collapse: collapse;'>";
    echo "<tr><th>指标</th><th>数值</th></tr>";
    echo "<tr><td>总优惠券数</td><td>" . number_format($stats['total_coupons']) . "</td></tr>";
    echo "<tr><td>总发放量</td><td>" . number_format($stats['total_issued']) . "</td></tr>";
    echo "<tr><td>已领取量</td><td>" . number_format($stats['total_claimed']) . "</td></tr>";
    echo "<tr><td>已使用量</td><td>" . number_format($stats['total_used']) . "</td></tr>";
    echo "</table>";
    
    // 显示前5条记录
    echo "<h2>数据样例</h2>";
    $stmt = $pdo->query("
        SELECT 
            id,
            title,
            total_quantity,
            claimed_quantity,
            COALESCE(used_quantity, 0) as used_quantity
        FROM camping_coupons
        LIMIT 5
    ");
    $samples = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (!empty($samples)) {
        echo "<table border='1' style='border-collapse: collapse;'>";
        echo "<tr><th>ID</th><th>标题</th><th>总量</th><th>已领取</th><th>已使用</th></tr>";
        foreach ($samples as $sample) {
            echo "<tr>";
            echo "<td>" . $sample['id'] . "</td>";
            echo "<td>" . htmlspecialchars($sample['title']) . "</td>";
            echo "<td>" . number_format($sample['total_quantity']) . "</td>";
            echo "<td>" . number_format($sample['claimed_quantity']) . "</td>";
            echo "<td>" . number_format($sample['used_quantity']) . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
    // 测试统计查询
    echo "<h2>测试统计查询</h2>";
    try {
        $stmt = $pdo->query("
            SELECT
                type,
                COUNT(*) as coupon_count,
                SUM(total_quantity) as total_issued,
                SUM(claimed_quantity) as total_claimed,
                SUM(COALESCE(used_quantity, 0)) as total_used
            FROM camping_coupons
            GROUP BY type
            ORDER BY total_issued DESC
        ");
        $type_stats = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo "<p class='success'>✅ 按类型统计查询成功</p>";
        
        if (!empty($type_stats)) {
            echo "<table border='1' style='border-collapse: collapse;'>";
            echo "<tr><th>类型</th><th>数量</th><th>总发放</th><th>已领取</th><th>已使用</th></tr>";
            foreach ($type_stats as $stat) {
                echo "<tr>";
                echo "<td>" . htmlspecialchars($stat['type']) . "</td>";
                echo "<td>" . number_format($stat['coupon_count']) . "</td>";
                echo "<td>" . number_format($stat['total_issued']) . "</td>";
                echo "<td>" . number_format($stat['total_claimed']) . "</td>";
                echo "<td>" . number_format($stat['total_used']) . "</td>";
                echo "</tr>";
            }
            echo "</table>";
        } else {
            echo "<p class='warning'>⚠️ 暂无数据</p>";
        }
        
    } catch (PDOException $e) {
        echo "<p class='error'>❌ 统计查询失败：" . htmlspecialchars($e->getMessage()) . "</p>";
    }
    
    echo "<h2>✅ 修复完成</h2>";
    echo "<p class='success'>数据库表结构已修复，现在可以正常使用统计分析功能了！</p>";
    echo "<p><a href='statistics.php'>访问统计分析页面</a> | <a href='test_statistics.php'>运行完整测试</a></p>";
    
} catch (PDOException $e) {
    echo "<p class='error'>❌ 数据库错误：" . htmlspecialchars($e->getMessage()) . "</p>";
}

echo "<br><a href='index.php'>返回优惠券管理</a>";
?>
