<?php
/**
 * 重置通知状态 - 用于调试
 * 将已送达的通知重置为未送达状态
 */

header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// 处理OPTIONS预检请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

try {
    $pdo = new PDO(
        "mysql:host=localhost;dbname=quwanplanet;charset=utf8mb4",
        "quwanplanet",
        "nJmJm23FB4Xn6Fc3",
        [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC
        ]
    );
    
    $user_id = intval($_GET['user_id'] ?? 0);
    
    $response = [
        'success' => true,
        'message' => '通知状态重置完成',
        'user_id' => $user_id,
        'operations' => []
    ];
    
    // 重置指定用户的通知状态
    if ($user_id > 0) {
        // 1. 重置为未送达状态
        $stmt = $pdo->prepare("
            UPDATE realtime_notifications 
            SET delivered_at = NULL, read_at = NULL, status = 'pending'
            WHERE user_id = ? AND status IN ('delivered', 'read')
        ");
        $stmt->execute([$user_id]);
        $affected = $stmt->rowCount();
        $response['operations'][] = "重置了 {$affected} 条通知为未送达状态";
        
        // 2. 查询当前状态
        $stmt = $pdo->prepare("
            SELECT id, type, title, status, delivered_at, read_at, created_at
            FROM realtime_notifications 
            WHERE user_id = ?
            ORDER BY created_at DESC
            LIMIT 10
        ");
        $stmt->execute([$user_id]);
        $notifications = $stmt->fetchAll();
        
        $response['data']['notifications'] = $notifications;
        $response['data']['count'] = count($notifications);
        
        // 3. 统计信息
        $stmt = $pdo->prepare("
            SELECT 
                COUNT(*) as total,
                SUM(CASE WHEN status = 'pending' THEN 1 ELSE 0 END) as pending,
                SUM(CASE WHEN status = 'delivered' THEN 1 ELSE 0 END) as delivered,
                SUM(CASE WHEN status = 'read' THEN 1 ELSE 0 END) as read,
                SUM(CASE WHEN delivered_at IS NULL THEN 1 ELSE 0 END) as not_delivered
            FROM realtime_notifications 
            WHERE user_id = ?
        ");
        $stmt->execute([$user_id]);
        $stats = $stmt->fetch();
        
        $response['data']['stats'] = $stats;
        $response['operations'][] = "当前统计: 总计{$stats['total']}条, 待处理{$stats['pending']}条, 未送达{$stats['not_delivered']}条";
        
    } else {
        // 重置所有通知
        $stmt = $pdo->query("
            UPDATE realtime_notifications 
            SET delivered_at = NULL, read_at = NULL, status = 'pending'
            WHERE status IN ('delivered', 'read')
        ");
        $affected = $stmt->rowCount();
        $response['operations'][] = "重置了所有 {$affected} 条通知为未送达状态";
    }
    
} catch (Exception $e) {
    $response = [
        'success' => false,
        'error' => $e->getMessage()
    ];
}

echo json_encode($response, JSON_UNESCAPED_UNICODE);
?>
