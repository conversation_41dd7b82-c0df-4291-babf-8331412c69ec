/* 全局样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    font-family: 'PingFang SC', 'Helvetica Neue', Arial, sans-serif;
}

body {
    background-color: #f8f9fa;
    color: #333333;
    line-height: 1.6;
    min-height: 100vh;
    position: relative;
    padding-bottom: 60px; /* 为底部导航栏留出空间 */
}

a {
    text-decoration: none;
    color: inherit;
}

/* Toast提示样式 */
.toast {
    position: fixed;
    top: 20px;
    left: 50%;
    transform: translateX(-50%);
    background-color: rgba(255, 255, 255, 0.95);
    color: #333333;
    padding: 14px 24px;
    border-radius: 12px;
    box-shadow: 0 6px 16px rgba(0, 0, 0, 0.12);
    z-index: 9999;
    display: none;
    border-left: 4px solid #1E90FF;
    font-size: 15px;
    font-weight: 500;
    max-width: 90%;
    text-align: center;
    pointer-events: none;
}

/* 顶部导航栏 */
.header-bar {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    height: 50px;
    background-color: #1E90FF;
    color: white;
    display: flex;
    align-items: center;
    padding: 0 15px;
    z-index: 100;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.back-button {
    font-size: 18px;
    color: white;
    margin-right: 15px;
}

.header-title {
    font-size: 18px;
    font-weight: 500;
    flex: 1;
    text-align: center;
    margin-right: 30px;
}

/* 内容容器 */
.content-container {
    padding: 60px 0 20px;
}

/* 搜索栏 */
.search-bar {
    background-color: white;
    border-radius: 8px;
    padding: 12px 15px;
    margin: 0 15px 15px;
    display: flex;
    align-items: center;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.search-bar i {
    color: #999;
    margin-right: 10px;
    font-size: 16px;
}

.search-bar input {
    border: none;
    outline: none;
    flex: 1;
    font-size: 15px;
    color: #333;
}

/* 轮播图 */
.universe-banner {
    margin: 0 15px 20px;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.banner-image {
    position: relative;
    height: 180px;
}

.banner-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.banner-overlay {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    padding: 20px;
    background: linear-gradient(to top, rgba(0, 0, 0, 0.7), transparent);
    color: white;
}

.banner-overlay h3 {
    font-size: 20px;
    font-weight: 600;
    margin-bottom: 5px;
    text-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
}

.banner-overlay p {
    font-size: 14px;
    opacity: 0.9;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

/* 内容卡片 */
.content-cards {
    display: flex;
    flex-direction: column;
    gap: 15px;
    margin: 0 15px 20px;
}

.content-card {
    background-color: white;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.card-image {
    position: relative;
    height: 150px;
}

.card-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.card-tag {
    position: absolute;
    top: 10px;
    left: 10px;
    background-color: rgba(30, 144, 255, 0.8);
    color: white;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
}

.card-info {
    padding: 15px;
}

.card-info h3 {
    font-size: 16px;
    font-weight: 500;
    margin-bottom: 8px;
    color: #333;
    line-height: 1.4;
}

.card-desc {
    font-size: 14px;
    color: #666;
    margin-bottom: 10px;
    line-height: 1.4;
}

.card-meta {
    display: flex;
    justify-content: space-between;
    font-size: 13px;
    color: #999;
}

/* 分类导航 */
.section-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin: 0 15px 15px;
}

.section-header h2 {
    font-size: 18px;
    font-weight: 600;
    color: #333;
}

.section-header.with-icon {
    margin-bottom: 15px;
}

.section-icon {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background-color: rgba(30, 144, 255, 0.1);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 10px;
    font-size: 16px;
    color: #1E90FF;
}

.more-link {
    font-size: 14px;
    color: #999;
    display: flex;
    align-items: center;
}

.more-link i {
    margin-left: 3px;
    font-size: 12px;
}

.category-nav {
    background-color: white;
    padding: 15px;
    margin-bottom: 20px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.category-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 15px;
}

.category-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-decoration: none;
}

.category-icon {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background-color: rgba(30, 144, 255, 0.1);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 8px;
    font-size: 22px;
    color: #1E90FF;
}

.category-label {
    font-size: 13px;
    color: #666;
    text-align: center;
}

/* 内容区块 */
.content-section {
    margin-bottom: 25px;
}

/* 子分类标签 */
.subcategory-tags {
    display: flex;
    overflow-x: auto;
    padding: 0 15px 5px;
    margin-bottom: 10px;
    -webkit-overflow-scrolling: touch;
    scrollbar-width: none; /* Firefox */
}

.subcategory-tags::-webkit-scrollbar {
    display: none; /* Chrome, Safari, Edge */
}

.subcategory-tag {
    flex-shrink: 0;
    background-color: #f0f0f0;
    border-radius: 20px;
    padding: 8px 15px;
    margin-right: 10px;
    font-size: 13px;
    color: #666;
    white-space: nowrap;
}

/* 子分类列表 */
.subcategory-list {
    background-color: white;
    margin-bottom: 15px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.subcategory-header {
    display: flex;
    align-items: center;
    padding: 15px;
    border-bottom: 1px solid #f0f0f0;
}

.subcategory-icon {
    width: 30px;
    height: 30px;
    border-radius: 50%;
    background-color: rgba(30, 144, 255, 0.1);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 10px;
    font-size: 16px;
    color: #1E90FF;
}

.subcategory-title {
    font-size: 16px;
    font-weight: 500;
    color: #333;
}

.subcategory-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    padding: 15px;
    gap: 10px;
}

.subcategory-item {
    background-color: #f8f9fa;
    border-radius: 8px;
    padding: 10px;
    text-align: center;
    font-size: 13px;
    color: #666;
}

/* 底部导航栏样式 */
.bottom-nav {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background-color: white;
    display: flex;
    justify-content: space-around;
    padding: 8px 0;
    box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.05);
    z-index: 100;
}

.nav-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 5px 0;
    width: 20%;
    text-decoration: none;
}

.nav-item i {
    font-size: 24px;
    margin-bottom: 3px;
    color: #999;
}

.nav-item span {
    font-size: 12px;
    color: #999;
}

.nav-item.active i,
.nav-item.active span {
    color: #1E90FF;
}
