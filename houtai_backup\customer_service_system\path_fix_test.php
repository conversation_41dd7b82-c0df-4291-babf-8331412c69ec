<?php
// 路径修复测试页面
header('Content-Type: text/html; charset=UTF-8');
ini_set('default_charset', 'UTF-8');

session_start();

// 检查登录状态
if (!isset($_SESSION['cs_logged_in']) || !$_SESSION['cs_logged_in']) {
    echo '<h1>请先登录客服系统</h1>';
    exit;
}

require_once '../db_config.php';

echo '<h1>🔧 路径修复测试</h1>';

try {
    $pdo = getDbConnection();
    
    // 获取等待中的会话
    $stmt = $pdo->query("SELECT session_id, user_name FROM customer_service_sessions WHERE status = 'waiting' ORDER BY started_at DESC LIMIT 1");
    $testSession = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($testSession) {
        $testSessionId = $testSession['session_id'];
        echo '<div style="background: #e7f3ff; padding: 15px; border-radius: 8px; margin: 10px 0;">';
        echo '<h3>✅ 路径修复完成</h3>';
        echo '<p>数据库配置文件路径已修复为：<code>../../db_config.php</code></p>';
        echo '<p>测试会话ID: <strong>' . htmlspecialchars($testSessionId) . '</strong></p>';
        echo '<p>用户: <strong>' . htmlspecialchars($testSession['user_name']) . '</strong></p>';
        echo '</div>';
        
        echo '<div style="margin: 20px 0;">';
        echo '<h3>🧪 测试不同的API版本</h3>';
        echo '<button onclick="testAPI(\'debug\')" style="margin: 5px; padding: 8px 16px; background: #17a2b8; color: white; border: none; border-radius: 5px; cursor: pointer;">测试调试版API</button>';
        echo '<button onclick="testAPI(\'minimal\')" style="margin: 5px; padding: 8px 16px; background: #28a745; color: white; border: none; border-radius: 5px; cursor: pointer;">测试最简版API</button>';
        echo '<button onclick="testAPI(\'simple\')" style="margin: 5px; padding: 8px 16px; background: #ffc107; color: black; border: none; border-radius: 5px; cursor: pointer;">测试简化版API</button>';
        echo '<button onclick="testAPI(\'original\')" style="margin: 5px; padding: 8px 16px; background: #6f42c1; color: white; border: none; border-radius: 5px; cursor: pointer;">测试原版API</button>';
        echo '</div>';
        
        echo '<div id="result" style="margin: 20px 0;"></div>';
        
    } else {
        echo '<div style="background: #fff3cd; padding: 15px; border-radius: 8px; margin: 10px 0;">';
        echo '<h3>⚠️ 没有等待中的会话</h3>';
        echo '<p>需要创建测试数据</p>';
        echo '<a href="create_test_data.php" style="background: #007cba; color: white; padding: 8px 16px; text-decoration: none; border-radius: 5px;">创建测试数据</a>';
        echo '</div>';
    }
    
} catch (Exception $e) {
    echo '<div style="background: #f8d7da; padding: 15px; border-radius: 8px; margin: 10px 0; color: #721c24;">';
    echo '<h3>❌ 数据库连接错误</h3>';
    echo '<p>' . htmlspecialchars($e->getMessage()) . '</p>';
    echo '</div>';
}
?>

<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>路径修复测试</title>
    <style>
        body { 
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; 
            margin: 20px; 
            background: #f5f5f5; 
        }
        .container { 
            max-width: 900px; 
            margin: 0 auto; 
            background: white; 
            padding: 20px; 
            border-radius: 10px; 
            box-shadow: 0 2px 10px rgba(0,0,0,0.1); 
        }
        .result { 
            margin: 10px 0; 
            padding: 15px; 
            border-radius: 8px; 
            border-left: 4px solid #28a745; 
        }
        .success { background: #d4edda; color: #155724; border-left-color: #28a745; }
        .error { background: #f8d7da; color: #721c24; border-left-color: #dc3545; }
        .info { background: #d1ecf1; color: #0c5460; border-left-color: #17a2b8; }
        .warning { background: #fff3cd; color: #856404; border-left-color: #ffc107; }
        pre { 
            background: #f8f9fa; 
            padding: 10px; 
            border-radius: 5px; 
            overflow-x: auto; 
            font-size: 12px; 
            max-height: 300px; 
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="result info">
            <h3>📝 修复说明</h3>
            <ul>
                <li><strong>问题</strong>：API文件中数据库配置路径错误</li>
                <li><strong>修复</strong>：将 <code>../db_config.php</code> 改为 <code>../../db_config.php</code></li>
                <li><strong>影响文件</strong>：所有 API 文件</li>
                <li><strong>测试</strong>：现在可以正常调用API了</li>
            </ul>
        </div>
        
        <p>
            <a href="sessions.php">返回会话列表</a> | 
            <a href="final_test.php">最终测试</a> | 
            <a href="create_test_data.php">创建测试数据</a>
        </p>
    </div>
    
    <script>
        const sessionId = '<?php echo $testSessionId ?? ''; ?>';
        
        async function testAPI(version) {
            if (!sessionId) {
                document.getElementById('result').innerHTML = '<div class="result error">没有可测试的会话ID</div>';
                return;
            }
            
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = '<div class="result info">🔄 测试 ' + version + ' 版本API...</div>';
            
            let apiUrl;
            switch(version) {
                case 'debug':
                    apiUrl = 'api/accept_session_debug.php';
                    break;
                case 'minimal':
                    apiUrl = 'api/accept_session_minimal.php';
                    break;
                case 'simple':
                    apiUrl = 'api/accept_session_simple.php';
                    break;
                case 'original':
                    apiUrl = 'api/accept_session.php';
                    break;
                default:
                    apiUrl = 'api/accept_session_debug.php';
            }
            
            try {
                const response = await fetch(apiUrl, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        sessionId: sessionId
                    })
                });
                
                const responseText = await response.text();
                console.log(version + ' API 原始响应:', responseText);
                
                let data;
                try {
                    data = JSON.parse(responseText);
                } catch (parseError) {
                    resultDiv.innerHTML = `
                        <div class="result error">
                            <h4>❌ ${version} API - JSON解析失败</h4>
                            <p><strong>解析错误：</strong>${parseError.message}</p>
                            <p><strong>原始响应：</strong></p>
                            <pre>${responseText}</pre>
                        </div>
                    `;
                    return;
                }
                
                if (data.success) {
                    resultDiv.innerHTML = `
                        <div class="result success">
                            <h4>🎉 ${version} API 测试成功！</h4>
                            <p><strong>会话状态：</strong>已更新为进行中</p>
                            <p><strong>分配客服：</strong>${data.session.cs_name}</p>
                            <pre>${JSON.stringify(data, null, 2)}</pre>
                            <p>
                                <a href="sessions.php" style="background: #28a745; color: white; padding: 8px 16px; text-decoration: none; border-radius: 5px; margin-right: 10px;">查看会话列表</a>
                                <button onclick="location.reload()" style="background: #6c757d; color: white; padding: 8px 16px; border: none; border-radius: 5px; cursor: pointer;">刷新页面</button>
                            </p>
                        </div>
                    `;
                } else {
                    resultDiv.innerHTML = `
                        <div class="result error">
                            <h4>❌ ${version} API 测试失败</h4>
                            <p><strong>错误：</strong>${data.error}</p>
                            <p><strong>消息：</strong>${data.message}</p>
                            <pre>${JSON.stringify(data, null, 2)}</pre>
                        </div>
                    `;
                }
                
            } catch (error) {
                console.error(version + ' API 测试失败:', error);
                resultDiv.innerHTML = `
                    <div class="result error">
                        <h4>❌ ${version} API 网络错误</h4>
                        <p>${error.message}</p>
                    </div>
                `;
            }
        }
    </script>
</body>
</html>
