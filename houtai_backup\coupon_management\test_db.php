<?php
session_start();
require_once '../db_config.php';

// 检查管理员登录状态
if (!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
    header('Location: ../login.php');
    exit;
}

echo "<h1>数据库连接测试</h1>";

try {
    $pdo = getDbConnection();
    echo "<p style='color: green;'>✅ 数据库连接成功！</p>";
    
    // 测试优惠券表是否存在
    $stmt = $pdo->query("SHOW TABLES LIKE 'camping_coupons'");
    if ($stmt->rowCount() > 0) {
        echo "<p style='color: green;'>✅ camping_coupons 表存在</p>";
        
        // 获取表结构
        $stmt = $pdo->query("DESCRIBE camping_coupons");
        $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
        echo "<h3>camping_coupons 表结构：</h3>";
        echo "<ul>";
        foreach ($columns as $column) {
            echo "<li>{$column['Field']} - {$column['Type']}</li>";
        }
        echo "</ul>";
        
        // 获取数据数量
        $stmt = $pdo->query("SELECT COUNT(*) as count FROM camping_coupons");
        $count = $stmt->fetch()['count'];
        echo "<p>表中共有 {$count} 条记录</p>";
        
    } else {
        echo "<p style='color: red;'>❌ camping_coupons 表不存在</p>";
    }
    
    // 测试用户优惠券表是否存在
    $stmt = $pdo->query("SHOW TABLES LIKE 'user_camping_coupons'");
    if ($stmt->rowCount() > 0) {
        echo "<p style='color: green;'>✅ user_camping_coupons 表存在</p>";
        
        // 获取数据数量
        $stmt = $pdo->query("SELECT COUNT(*) as count FROM user_camping_coupons");
        $count = $stmt->fetch()['count'];
        echo "<p>表中共有 {$count} 条记录</p>";
        
    } else {
        echo "<p style='color: red;'>❌ user_camping_coupons 表不存在</p>";
    }
    
    // 测试用户表是否存在
    $stmt = $pdo->query("SHOW TABLES LIKE 'users'");
    if ($stmt->rowCount() > 0) {
        echo "<p style='color: green;'>✅ users 表存在</p>";
        
        // 获取数据数量
        $stmt = $pdo->query("SELECT COUNT(*) as count FROM users");
        $count = $stmt->fetch()['count'];
        echo "<p>表中共有 {$count} 条记录</p>";
        
    } else {
        echo "<p style='color: red;'>❌ users 表不存在</p>";
    }
    
} catch (PDOException $e) {
    echo "<p style='color: red;'>❌ 数据库错误：" . $e->getMessage() . "</p>";
}

echo "<br><a href='statistics.php'>返回统计页面</a>";
?>
