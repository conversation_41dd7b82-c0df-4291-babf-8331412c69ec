<?php
// 引入session配置
require_once '../../sql/session_config.php';
initLoginSession();

// 检查用户是否已登录
if (!isUserLoggedIn()) {
    header('Location: ../login/index.php');
    exit;
}
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <meta name="theme-color" content="#1E90FF">
    <title>活动 - 趣玩星球</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'PingFang SC', 'Helvetica Neue', Arial, sans-serif;
        }

        body {
            background-color: #f8f9fa;
            color: #333333;
            line-height: 1.6;
            min-height: 100vh;
        }

        .header-bar {
            display: flex;
            align-items: center;
            padding: 15px;
            background-color: #1E90FF;
            color: white;
            position: sticky;
            top: 0;
            z-index: 100;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        .back-button {
            margin-right: 15px;
            font-size: 18px;
            color: white;
            text-decoration: none;
        }

        .header-title {
            font-size: 18px;
            font-weight: bold;
        }

        .tabs {
            display: flex;
            background-color: white;
            margin-bottom: 15px;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
            position: sticky;
            top: 54px;
            z-index: 99;
        }

        .tab {
            flex: 1;
            text-align: center;
            padding: 12px 0;
            font-size: 15px;
            color: #666;
            position: relative;
            cursor: pointer;
        }

        .tab.active {
            color: #1E90FF;
            font-weight: 500;
        }

        .tab.active:after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 20px;
            height: 3px;
            background-color: #1E90FF;
            border-radius: 3px;
        }

        .activity-container {
            padding: 15px;
        }

        .activity-list {
            display: flex;
            flex-direction: column;
            gap: 15px;
        }

        .activity-card {
            background-color: white;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
        }

        .activity-image {
            width: 100%;
            height: 150px;
            position: relative;
        }

        .activity-image img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .activity-status {
            position: absolute;
            top: 10px;
            right: 10px;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 500;
            color: white;
        }

        .status-upcoming {
            background-color: #FF9800;
        }

        .status-ongoing {
            background-color: #4CAF50;
        }

        .status-ended {
            background-color: #9E9E9E;
        }

        .activity-content {
            padding: 15px;
        }

        .activity-title {
            font-size: 16px;
            font-weight: 500;
            margin-bottom: 8px;
            color: #333;
        }

        .activity-info {
            display: flex;
            align-items: center;
            margin-bottom: 5px;
            font-size: 13px;
            color: #666;
        }

        .activity-info i {
            margin-right: 5px;
            width: 16px;
            text-align: center;
            color: #999;
        }

        .activity-footer {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding-top: 10px;
            border-top: 1px solid #f0f0f0;
            margin-top: 10px;
        }

        .activity-participants {
            display: flex;
            align-items: center;
        }

        .participant-avatars {
            display: flex;
            margin-right: 5px;
        }

        .participant-avatar {
            width: 24px;
            height: 24px;
            border-radius: 50%;
            border: 2px solid white;
            overflow: hidden;
            margin-left: -8px;
        }

        .participant-avatar:first-child {
            margin-left: 0;
        }

        .participant-avatar img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .participant-count {
            font-size: 12px;
            color: #999;
        }

        .activity-action {
            font-size: 13px;
            font-weight: 500;
            color: #1E90FF;
            text-decoration: none;
        }

        .empty-state {
            text-align: center;
            padding: 50px 20px;
            color: #999;
        }

        .empty-state i {
            font-size: 50px;
            margin-bottom: 15px;
            color: #ddd;
        }

        .empty-state p {
            font-size: 15px;
            margin-bottom: 20px;
        }

        .empty-state .btn {
            display: inline-block;
            padding: 10px 20px;
            background-color: #1E90FF;
            color: white;
            border-radius: 20px;
            text-decoration: none;
            font-size: 14px;
            font-weight: 500;
            box-shadow: 0 4px 10px rgba(30, 144, 255, 0.3);
        }
    </style>
    <!-- 引入字体图标库 -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <!-- 顶部导航栏 -->
    <div class="header-bar">
        <a href="../profile/index.php" class="back-button">
            <i class="fas fa-arrow-left"></i>
        </a>
        <div class="header-title">活动</div>
    </div>

    <!-- 标签页 -->
    <div class="tabs">
        <div class="tab active" data-tab="all">全部</div>
        <div class="tab" data-tab="upcoming">即将开始</div>
        <div class="tab" data-tab="ongoing">进行中</div>
        <div class="tab" data-tab="ended">已结束</div>
    </div>

    <div class="activity-container">
        <div class="tab-content active" id="all-content">
            <div class="empty-state">
                <i class="fas fa-calendar-alt"></i>
                <p>暂无活动记录</p>
                <a href="../activity/index.php" class="btn">去参加活动</a>
            </div>

            <!-- 活动示例（当有数据时显示）
            <div class="activity-list">
                <div class="activity-card">
                    <div class="activity-image">
                        <img src="https://s1.imagehub.cc/images/2025/05/15/activity1.jpg" alt="活动图片">
                        <div class="activity-status status-upcoming">即将开始</div>
                    </div>
                    <div class="activity-content">
                        <h3 class="activity-title">周末城市探索之旅</h3>
                        <div class="activity-info">
                            <i class="fas fa-map-marker-alt"></i>
                            <span>北京市朝阳区</span>
                        </div>
                        <div class="activity-info">
                            <i class="fas fa-clock"></i>
                            <span>2025-05-20 14:00</span>
                        </div>
                        <div class="activity-footer">
                            <div class="activity-participants">
                                <div class="participant-avatars">
                                    <div class="participant-avatar">
                                        <img src="https://s1.imagehub.cc/images/2025/04/26/avatar1.jpg" alt="参与者">
                                    </div>
                                    <div class="participant-avatar">
                                        <img src="https://s1.imagehub.cc/images/2025/04/26/avatar2.jpg" alt="参与者">
                                    </div>
                                    <div class="participant-avatar">
                                        <img src="https://s1.imagehub.cc/images/2025/04/26/avatar3.jpg" alt="参与者">
                                    </div>
                                </div>
                                <div class="participant-count">12人参与</div>
                            </div>
                            <a href="#" class="activity-action">查看详情 ></a>
                        </div>
                    </div>
                </div>

                <div class="activity-card">
                    <div class="activity-image">
                        <img src="https://s1.imagehub.cc/images/2025/05/15/activity2.jpg" alt="活动图片">
                        <div class="activity-status status-ended">已结束</div>
                    </div>
                    <div class="activity-content">
                        <h3 class="activity-title">王者荣耀排位赛</h3>
                        <div class="activity-info">
                            <i class="fas fa-gamepad"></i>
                            <span>线上活动</span>
                        </div>
                        <div class="activity-info">
                            <i class="fas fa-clock"></i>
                            <span>2025-05-10 20:00</span>
                        </div>
                        <div class="activity-footer">
                            <div class="activity-participants">
                                <div class="participant-avatars">
                                    <div class="participant-avatar">
                                        <img src="https://s1.imagehub.cc/images/2025/04/26/avatar4.jpg" alt="参与者">
                                    </div>
                                    <div class="participant-avatar">
                                        <img src="https://s1.imagehub.cc/images/2025/04/26/avatar5.jpg" alt="参与者">
                                    </div>
                                </div>
                                <div class="participant-count">5人参与</div>
                            </div>
                            <a href="#" class="activity-action">查看详情 ></a>
                        </div>
                    </div>
                </div>
            </div>
            -->
        </div>

        <div class="tab-content" id="upcoming-content" style="display: none;">
            <div class="empty-state">
                <i class="fas fa-hourglass-start"></i>
                <p>暂无即将开始的活动</p>
            </div>
        </div>

        <div class="tab-content" id="ongoing-content" style="display: none;">
            <div class="empty-state">
                <i class="fas fa-play-circle"></i>
                <p>暂无进行中的活动</p>
            </div>
        </div>

        <div class="tab-content" id="ended-content" style="display: none;">
            <div class="empty-state">
                <i class="fas fa-flag-checkered"></i>
                <p>暂无已结束的活动</p>
            </div>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 标签页切换
            const tabs = document.querySelectorAll('.tab');
            const tabContents = document.querySelectorAll('.tab-content');

            tabs.forEach(tab => {
                tab.addEventListener('click', function() {
                    const tabId = this.getAttribute('data-tab');

                    // 移除所有标签和内容的active类
                    tabs.forEach(t => t.classList.remove('active'));
                    tabContents.forEach(c => c.style.display = 'none');

                    // 添加active类到当前标签和对应内容
                    this.classList.add('active');
                    document.getElementById(`${tabId}-content`).style.display = 'block';
                });
            });
        });
    </script>
</body>
</html>
