<?php
/**
 * 登录记录测试API
 */

require_once 'login_logger.php';

header('Content-Type: application/json');

// 数据库配置
$host = 'localhost';
$dbname = 'quwanplanet';
$db_username = 'quwanplanet';
$db_password = 'nJmJm23FB4Xn6Fc3';

try {
    $pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $db_username, $db_password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // 模拟用户ID为999的测试记录
    $testUserId = 999;
    
    // 记录测试登录
    $result = recordUserLoginLog($pdo, $testUserId, 'normal_login', 'success');
    
    if ($result) {
        // 获取刚刚插入的记录
        $stmt = $pdo->prepare("
            SELECT id, ip_address, location, user_agent 
            FROM login_logs 
            WHERE user_id = ? 
            ORDER BY login_time DESC 
            LIMIT 1
        ");
        $stmt->execute([$testUserId]);
        $record = $stmt->fetch();
        
        if ($record) {
            $deviceInfo = parseUserAgent($record['user_agent']);
            
            echo json_encode([
                'success' => true,
                'record_id' => $record['id'],
                'ip' => $record['ip_address'],
                'location' => $record['location'],
                'device' => $deviceInfo['device'],
                'os' => $deviceInfo['os'],
                'browser' => $deviceInfo['browser'],
                'is_wechat' => $deviceInfo['is_wechat']
            ]);
        } else {
            echo json_encode(['success' => false, 'message' => '无法获取插入的记录']);
        }
    } else {
        echo json_encode(['success' => false, 'message' => '登录记录插入失败']);
    }
    
} catch (PDOException $e) {
    echo json_encode(['success' => false, 'message' => '数据库错误: ' . $e->getMessage()]);
}
?>
