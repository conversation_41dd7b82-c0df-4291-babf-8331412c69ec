<?php
/**
 * 实名认证审核管理页面
 * 趣玩星球管理后台
 */

session_start();

// 检查登录状态
if (!isset($_SESSION['admin_id'])) {
    header('Location: ../login.php');
    exit;
}

$admin_name = $_SESSION['admin_name'] ?? '管理员';
$admin_id = $_SESSION['admin_id'] ?? '';
$admin_employee_id = $_SESSION['admin_employee_id'] ?? '';

// 引用上级目录的数据库配置文件
require_once '../db_config.php';

try {
    $pdo = getDbConnection();

    // 获取筛选条件
    $status_filter = $_GET['status'] ?? 'all';
    $search = $_GET['search'] ?? '';
    $page = max(1, intval($_GET['page'] ?? 1));
    $per_page = 20;
    $offset = ($page - 1) * $per_page;

    // 构建查询条件
    $where_conditions = [];
    $params = [];

    if ($status_filter !== 'all') {
        $where_conditions[] = "rv.verification_status = ?";
        $params[] = $status_filter;
    }

    if (!empty($search)) {
        $where_conditions[] = "(rv.real_name LIKE ? OR rv.id_card_number LIKE ? OR u.username LIKE ?)";
        $search_param = "%$search%";
        $params[] = $search_param;
        $params[] = $search_param;
        $params[] = $search_param;
    }

    $where_clause = !empty($where_conditions) ? 'WHERE ' . implode(' AND ', $where_conditions) : '';

    // 获取总数
    $count_sql = "
        SELECT COUNT(*)
        FROM realname_verification rv
        LEFT JOIN users u ON rv.user_id = u.id
        $where_clause
    ";
    $count_stmt = $pdo->prepare($count_sql);
    $count_stmt->execute($params);
    $total_count = $count_stmt->fetchColumn();

    // 获取列表数据
    $sql = "
        SELECT
            rv.*,
            u.username,
            u.phone,
            au.name as admin_name
        FROM realname_verification rv
        LEFT JOIN users u ON rv.user_id = u.id
        LEFT JOIN admin_users au ON rv.verified_by = au.id
        $where_clause
        ORDER BY rv.submitted_at DESC
        LIMIT $per_page OFFSET $offset
    ";

    $stmt = $pdo->prepare($sql);
    $stmt->execute($params);
    $verifications = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // 计算分页
    $total_pages = ceil($total_count / $per_page);

    // 获取统计数据
    $stats_sql = "
        SELECT
            verification_status,
            COUNT(*) as count
        FROM realname_verification
        GROUP BY verification_status
    ";
    $stats_stmt = $pdo->query($stats_sql);
    $stats = [];
    while ($row = $stats_stmt->fetch(PDO::FETCH_ASSOC)) {
        $stats[$row['verification_status']] = $row['count'];
    }

} catch (PDOException $e) {
    $error_message = '数据库错误：' . $e->getMessage();
}
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>实名认证审核 - 趣玩星球管理后台</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="../assets/css/admin-layout.css">
    <style>
        /* 实名认证审核页面专用样式 */
        .verification-content {
            padding: 24px;
            background: var(--gray-50);
            min-height: calc(100vh - 60px);
        }

        .page-header {
            margin-bottom: 32px;
        }

        .page-title {
            font-size: 2rem;
            font-weight: 700;
            color: var(--gray-800);
            margin-bottom: 8px;
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .page-subtitle {
            color: var(--gray-600);
            font-size: 1.1rem;
        }

        /* 统计卡片区域 */
        .stats-overview {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 24px;
            margin-bottom: 32px;
        }

        .stat-card {
            background: white;
            border-radius: 20px;
            padding: 28px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
            border: 2px solid transparent;
            transition: all 0.4s ease;
            position: relative;
            overflow: hidden;
        }

        .stat-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: var(--gradient);
            transition: height 0.3s ease;
        }

        .stat-card:hover {
            transform: translateY(-8px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
            border-color: var(--primary-color);
        }

        .stat-card:hover::before {
            height: 6px;
        }

        .stat-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 16px;
        }

        .stat-icon {
            width: 60px;
            height: 60px;
            border-radius: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            color: white;
            background: var(--gradient);
            box-shadow: 0 8px 20px rgba(64, 224, 208, 0.3);
        }

        .stat-number {
            font-size: 2.5rem;
            font-weight: 800;
            color: var(--gray-800);
            margin-bottom: 4px;
            line-height: 1;
        }

        .stat-label {
            color: var(--gray-600);
            font-size: 1rem;
            font-weight: 500;
        }

        .stat-trend {
            font-size: 0.875rem;
            margin-top: 8px;
            display: flex;
            align-items: center;
            gap: 4px;
        }

        .trend-up {
            color: var(--success-color);
        }

        .trend-down {
            color: var(--error-color);
        }

        /* 不同状态的配色 */
        .stat-pending {
            --gradient: linear-gradient(135deg, #F59E0B, #FBBF24);
        }

        .stat-approved {
            --gradient: linear-gradient(135deg, #10B981, #34D399);
        }

        .stat-rejected {
            --gradient: linear-gradient(135deg, #EF4444, #F87171);
        }

        .stat-total {
            --gradient: linear-gradient(135deg, var(--primary-color), var(--primary-light));
        }

        /* 筛选器区域 */
        .filters-section {
            background: white;
            border-radius: 20px;
            padding: 28px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
            margin-bottom: 32px;
            border: 2px solid transparent;
            transition: all 0.3s ease;
        }

        .filters-section:hover {
            border-color: var(--primary-color);
            box-shadow: 0 12px 40px rgba(0, 0, 0, 0.12);
        }

        .filters-header {
            display: flex;
            align-items: center;
            gap: 12px;
            margin-bottom: 24px;
            padding-bottom: 16px;
            border-bottom: 2px solid var(--gray-100);
        }

        .filters-icon {
            width: 48px;
            height: 48px;
            background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 20px;
        }

        .filters-title {
            font-size: 1.25rem;
            font-weight: 700;
            color: var(--gray-800);
        }

        .filter-row {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            align-items: end;
        }

        .filter-group {
            display: flex;
            flex-direction: column;
            gap: 8px;
        }

        .filter-label {
            font-weight: 600;
            color: var(--gray-700);
            font-size: 0.875rem;
        }

        .filter-group select,
        .filter-group input {
            padding: 12px 16px;
            border: 2px solid var(--gray-200);
            border-radius: 12px;
            font-size: 0.875rem;
            transition: all 0.3s ease;
            background: white;
        }

        .filter-group select:focus,
        .filter-group input:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(64, 224, 208, 0.1);
            transform: translateY(-2px);
        }

        .filter-actions {
            display: flex;
            gap: 12px;
        }

        .filter-btn {
            background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 12px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 0.875rem;
        }

        .filter-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(64, 224, 208, 0.3);
        }

        .filter-btn-secondary {
            background: var(--gray-100);
            color: var(--gray-700);
        }

        .filter-btn-secondary:hover {
            background: var(--gray-200);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }

        /* 数据表格区域 */
        .table-section {
            background: white;
            border-radius: 20px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
            overflow: hidden;
            border: 2px solid transparent;
            transition: all 0.3s ease;
        }

        .table-section:hover {
            border-color: var(--primary-color);
            box-shadow: 0 12px 40px rgba(0, 0, 0, 0.12);
        }

        .table-header {
            background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
            padding: 24px 28px;
            color: white;
        }

        .table-title {
            font-size: 1.25rem;
            font-weight: 700;
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .table-container {
            overflow-x: auto;
        }

        .verification-table {
            width: 100%;
            border-collapse: collapse;
        }

        .verification-table th {
            background: var(--gray-50);
            padding: 16px 20px;
            text-align: left;
            font-weight: 600;
            color: var(--gray-700);
            font-size: 0.875rem;
            border-bottom: 2px solid var(--gray-200);
            white-space: nowrap;
        }

        .verification-table td {
            padding: 16px 12px;
            border-bottom: 1px solid var(--gray-100);
            vertical-align: middle;
            white-space: nowrap;
        }

        /* 用户信息列允许换行 */
        .verification-table td:nth-child(2) {
            white-space: normal;
            min-width: 150px;
        }

        /* 身份证照片列允许换行 */
        .verification-table td:nth-child(4) {
            white-space: normal;
        }

        /* 操作列允许换行 */
        .verification-table td:last-child {
            white-space: normal;
        }

        .verification-table tr {
            transition: all 0.3s ease;
        }

        .verification-table tr:hover {
            background: var(--gray-50);
            transform: scale(1.01);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
        }

        /* 用户信息卡片 */
        .user-info-card {
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .user-avatar {
            width: 40px;
            height: 40px;
            background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: 600;
            font-size: 14px;
        }

        .user-details h4 {
            font-weight: 600;
            color: var(--gray-800);
            margin-bottom: 2px;
            font-size: 0.875rem;
        }

        .user-phone {
            font-size: 0.75rem;
            color: var(--gray-500);
        }

        /* 身份证照片预览 */
        .id-card-photos {
            display: flex;
            gap: 8px;
        }

        .id-card-thumb {
            width: 60px;
            height: 40px;
            object-fit: cover;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
            border: 2px solid var(--gray-200);
        }

        .id-card-thumb:hover {
            transform: scale(1.1);
            border-color: var(--primary-color);
            box-shadow: 0 4px 12px rgba(64, 224, 208, 0.3);
        }

        .no-image {
            color: var(--gray-400);
            font-size: 0.75rem;
            font-style: italic;
        }

        /* 状态徽章 */
        .status-badge {
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 0.75rem;
            font-weight: 600;
            display: inline-flex;
            align-items: center;
            gap: 6px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .status-pending {
            background: linear-gradient(135deg, #FEF3C7, #FDE68A);
            color: #92400E;
            border: 1px solid #F59E0B;
        }

        .status-approved {
            background: linear-gradient(135deg, #D1FAE5, #A7F3D0);
            color: #065F46;
            border: 1px solid #10B981;
        }

        .status-rejected {
            background: linear-gradient(135deg, #FEE2E2, #FECACA);
            color: #991B1B;
            border: 1px solid #EF4444;
        }

        /* 操作按钮 */
        .actions {
            display: flex;
            gap: 8px;
        }

        .action-btn {
            padding: 8px 12px;
            border: none;
            border-radius: 8px;
            font-size: 0.75rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 4px;
            text-decoration: none;
            min-width: 80px;
            justify-content: center;
        }

        .btn-view {
            background: linear-gradient(135deg, #3B82F6, #60A5FA);
            color: white;
        }

        .btn-view:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
        }

        .btn-approve {
            background: linear-gradient(135deg, #10B981, #34D399);
            color: white;
        }

        .btn-approve:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
        }

        .btn-reject {
            background: linear-gradient(135deg, #EF4444, #F87171);
            color: white;
        }

        .btn-reject:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(239, 68, 68, 0.3);
        }

        /* ID卡号显示 */
        .id-card-number {
            font-family: 'Courier New', monospace;
            font-size: 0.875rem;
            color: var(--gray-600);
            background: var(--gray-100);
            padding: 4px 8px;
            border-radius: 6px;
            letter-spacing: 1px;
        }

        /* 分页样式 */
        .pagination-section {
            background: white;
            border-radius: 16px;
            padding: 24px;
            margin-top: 24px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
        }

        .pagination {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 8px;
        }

        .pagination a,
        .pagination span {
            padding: 10px 16px;
            border: 2px solid var(--gray-200);
            border-radius: 12px;
            text-decoration: none;
            color: var(--gray-600);
            font-weight: 500;
            transition: all 0.3s ease;
            min-width: 44px;
            text-align: center;
        }

        .pagination a:hover {
            border-color: var(--primary-color);
            color: var(--primary-color);
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(64, 224, 208, 0.2);
        }

        .pagination .current {
            background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
            color: white;
            border-color: var(--primary-color);
            box-shadow: 0 4px 12px rgba(64, 224, 208, 0.3);
        }

        /* 空状态 */
        .empty-state {
            text-align: center;
            padding: 60px 20px;
            color: var(--gray-500);
        }

        .empty-icon {
            font-size: 64px;
            margin-bottom: 20px;
            opacity: 0.5;
            color: var(--gray-400);
        }

        .empty-text {
            font-size: 1.1rem;
            margin-bottom: 8px;
            color: var(--gray-600);
        }

        .empty-subtext {
            font-size: 0.875rem;
            color: var(--gray-500);
        }

        /* 响应式设计 */
        @media (max-width: 1024px) {
            .verification-content {
                padding: 16px;
            }

            .stats-overview {
                grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
                gap: 16px;
            }

            .filter-row {
                grid-template-columns: 1fr;
                gap: 16px;
            }
        }

        @media (max-width: 768px) {
            .page-title {
                font-size: 1.5rem;
            }

            .stat-card {
                padding: 20px;
            }

            .stat-number {
                font-size: 2rem;
            }

            .filters-section {
                padding: 20px;
            }

            .table-container {
                overflow-x: auto;
            }

            .verification-table th,
            .verification-table td {
                padding: 12px 8px;
                font-size: 0.75rem;
            }

            .actions {
                flex-direction: column;
                gap: 4px;
            }

            .action-btn {
                font-size: 0.7rem;
                padding: 6px 8px;
                min-width: 60px;
            }

            .user-info-card {
                flex-direction: column;
                align-items: flex-start;
                gap: 8px;
            }

            .user-avatar {
                width: 32px;
                height: 32px;
                font-size: 12px;
            }

            .pagination a,
            .pagination span {
                padding: 8px 12px;
                font-size: 0.875rem;
            }
        }

        @media (max-width: 480px) {
            .stats-overview {
                grid-template-columns: 1fr;
            }

            .id-card-photos {
                flex-direction: column;
                gap: 4px;
            }

            .id-card-thumb {
                width: 100%;
                height: 60px;
            }
        }
    </style>
</head>
<body>
    <div class="admin-layout">
        <!-- 左侧菜单 -->
        <?php include '../includes/sidebar.php'; ?>

        <!-- 主内容区域 -->
        <main class="main-content">
            <!-- 顶部导航栏 -->
            <?php include '../includes/topbar.php'; ?>

            <div class="verification-content">
                <!-- 页面标题 -->
                <div class="page-header">
                    <h1 class="page-title">
                        <i class="fas fa-user-check" style="color: var(--primary-color);"></i>
                        实名认证审核
                    </h1>
                    <p class="page-subtitle">管理用户实名认证申请，确保平台用户身份真实性</p>
                </div>

                <!-- 统计概览 -->
                <div class="stats-overview">
                    <div class="stat-card stat-pending">
                        <div class="stat-header">
                            <div>
                                <div class="stat-number"><?php echo $stats['pending'] ?? 0; ?></div>
                                <div class="stat-label">待审核</div>
                            </div>
                            <div class="stat-icon">
                                <i class="fas fa-clock"></i>
                            </div>
                        </div>
                        <div class="stat-trend trend-up">
                            <i class="fas fa-arrow-up"></i>
                            <span>+12% 本周</span>
                        </div>
                    </div>

                    <div class="stat-card stat-approved">
                        <div class="stat-header">
                            <div>
                                <div class="stat-number"><?php echo $stats['approved'] ?? 0; ?></div>
                                <div class="stat-label">已通过</div>
                            </div>
                            <div class="stat-icon">
                                <i class="fas fa-check-circle"></i>
                            </div>
                        </div>
                        <div class="stat-trend trend-up">
                            <i class="fas fa-arrow-up"></i>
                            <span>+8% 本周</span>
                        </div>
                    </div>

                    <div class="stat-card stat-rejected">
                        <div class="stat-header">
                            <div>
                                <div class="stat-number"><?php echo $stats['rejected'] ?? 0; ?></div>
                                <div class="stat-label">已拒绝</div>
                            </div>
                            <div class="stat-icon">
                                <i class="fas fa-times-circle"></i>
                            </div>
                        </div>
                        <div class="stat-trend trend-down">
                            <i class="fas fa-arrow-down"></i>
                            <span>-3% 本周</span>
                        </div>
                    </div>

                    <div class="stat-card stat-total">
                        <div class="stat-header">
                            <div>
                                <div class="stat-number"><?php echo $total_count; ?></div>
                                <div class="stat-label">总申请</div>
                            </div>
                            <div class="stat-icon">
                                <i class="fas fa-users"></i>
                            </div>
                        </div>
                        <div class="stat-trend trend-up">
                            <i class="fas fa-arrow-up"></i>
                            <span>+15% 本月</span>
                        </div>
                    </div>
                </div>

                <!-- 筛选器 -->
                <div class="filters-section">
                    <div class="filters-header">
                        <div class="filters-icon">
                            <i class="fas fa-filter"></i>
                        </div>
                        <div class="filters-title">筛选与搜索</div>
                    </div>

                    <form method="GET" class="filter-row">
                        <div class="filter-group">
                            <label class="filter-label">审核状态</label>
                            <select name="status">
                                <option value="all" <?php echo $status_filter === 'all' ? 'selected' : ''; ?>>全部状态</option>
                                <option value="pending" <?php echo $status_filter === 'pending' ? 'selected' : ''; ?>>⏳ 待审核</option>
                                <option value="approved" <?php echo $status_filter === 'approved' ? 'selected' : ''; ?>>✅ 已通过</option>
                                <option value="rejected" <?php echo $status_filter === 'rejected' ? 'selected' : ''; ?>>❌ 已拒绝</option>
                            </select>
                        </div>

                        <div class="filter-group">
                            <label class="filter-label">关键词搜索</label>
                            <input type="text" name="search" placeholder="🔍 搜索姓名、身份证号、用户名..."
                                   value="<?php echo htmlspecialchars($search); ?>">
                        </div>

                        <div class="filter-actions">
                            <button type="submit" class="filter-btn">
                                <i class="fas fa-search"></i>
                                搜索
                            </button>
                            <a href="index.php" class="filter-btn filter-btn-secondary">
                                <i class="fas fa-refresh"></i>
                                重置
                            </a>
                        </div>
                    </form>
                </div>

                <!-- 数据表格 -->
                <div class="table-section">
                    <div class="table-header">
                        <div class="table-title">
                            <i class="fas fa-table"></i>
                            认证申请列表
                        </div>
                    </div>

                    <div class="table-container">
                        <table class="verification-table">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>用户信息</th>
                                    <th>真实姓名</th>
                                    <th>身份证照片</th>
                                    <th>状态</th>
                                    <th>提交时间</th>
                                    <th>审核人</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php if (empty($verifications)): ?>
                                    <tr>
                                        <td colspan="8">
                                            <div class="empty-state">
                                                <div class="empty-icon">
                                                    <i class="fas fa-inbox"></i>
                                                </div>
                                                <div class="empty-text">暂无认证申请</div>
                                                <div class="empty-subtext">当前筛选条件下没有找到相关数据</div>
                                            </div>
                                        </td>
                                    </tr>
                                <?php else: ?>
                                    <?php foreach ($verifications as $item): ?>
                                        <tr>
                                            <td>
                                                <span style="font-weight: 600; color: var(--primary-color);">
                                                    #<?php echo $item['id']; ?>
                                                </span>
                                            </td>
                                            <td>
                                                <div class="user-info-card">
                                                    <div class="user-avatar">
                                                        <?php echo mb_substr($item['username'], 0, 1, 'UTF-8'); ?>
                                                    </div>
                                                    <div class="user-details">
                                                        <h4><?php echo htmlspecialchars($item['username']); ?></h4>
                                                        <div class="user-phone"><?php echo htmlspecialchars($item['phone'] ?? '未绑定手机'); ?></div>
                                                    </div>
                                                </div>
                                            </td>
                                            <td>
                                                <span style="font-weight: 600; color: var(--gray-800);">
                                                    <?php echo htmlspecialchars($item['real_name']); ?>
                                                </span>
                                            </td>
                                            <td>
                                                <div class="id-card-photos">
                                                    <?php if (!empty($item['id_card_front_url'])): ?>
                                                        <img src="<?php echo htmlspecialchars($item['id_card_front_url']); ?>"
                                                             alt="身份证正面" class="id-card-thumb"
                                                             onclick="showImageModal('<?php echo htmlspecialchars($item['id_card_front_url']); ?>', '身份证正面')">
                                                    <?php endif; ?>
                                                    <?php if (!empty($item['id_card_back_url'])): ?>
                                                        <img src="<?php echo htmlspecialchars($item['id_card_back_url']); ?>"
                                                             alt="身份证反面" class="id-card-thumb"
                                                             onclick="showImageModal('<?php echo htmlspecialchars($item['id_card_back_url']); ?>', '身份证反面')">
                                                    <?php endif; ?>
                                                    <?php if (empty($item['id_card_front_url']) && empty($item['id_card_back_url'])): ?>
                                                        <span class="no-image">暂无照片</span>
                                                    <?php endif; ?>
                                                </div>
                                            </td>
                                            <td>
                                                <?php
                                                $status = $item['verification_status'];
                                                $status_class = 'status-' . $status;
                                                $status_text = [
                                                    'pending' => '待审核',
                                                    'approved' => '已通过',
                                                    'rejected' => '已拒绝'
                                                ];
                                                $status_icon = [
                                                    'pending' => 'fas fa-clock',
                                                    'approved' => 'fas fa-check-circle',
                                                    'rejected' => 'fas fa-times-circle'
                                                ];
                                                ?>
                                                <span class="status-badge <?php echo $status_class; ?>">
                                                    <i class="<?php echo $status_icon[$status] ?? 'fas fa-question'; ?>"></i>
                                                    <?php echo $status_text[$status] ?? $status; ?>
                                                </span>
                                            </td>
                                            <td>
                                                <div style="font-size: 0.875rem; color: var(--gray-600);">
                                                    <?php
                                                    if ($item['submitted_at'] && $item['submitted_at'] !== '0000-00-00 00:00:00') {
                                                        echo date('Y-m-d H:i', strtotime($item['submitted_at']));
                                                    } else {
                                                        echo '-';
                                                    }
                                                    ?>
                                                </div>
                                            </td>
                                            <td>
                                                <span style="font-size: 0.875rem; color: var(--gray-600);">
                                                    <?php echo htmlspecialchars($item['admin_name'] ?? '未审核'); ?>
                                                </span>
                                            </td>
                                            <td>
                                                <div class="actions">
                                                    <a href="detail.php?id=<?php echo $item['id']; ?>" class="action-btn btn-view">
                                                        <i class="fas fa-eye"></i>
                                                        查看
                                                    </a>
                                                    <?php if ($item['verification_status'] === 'pending'): ?>
                                                        <a href="approve.php?id=<?php echo $item['id']; ?>" class="action-btn btn-approve"
                                                           onclick="return confirm('确定要通过这个认证申请吗？')">
                                                            <i class="fas fa-check"></i>
                                                            通过
                                                        </a>
                                                        <a href="reject.php?id=<?php echo $item['id']; ?>" class="action-btn btn-reject"
                                                           onclick="return confirm('确定要拒绝这个认证申请吗？')">
                                                            <i class="fas fa-times"></i>
                                                            拒绝
                                                        </a>
                                                    <?php endif; ?>
                                                </div>
                                            </td>
                            </tr>
                        <?php endforeach; ?>
                    <?php endif; ?>
                </tbody>
            </table>
                </div>

                <!-- 分页 -->
                <?php if ($total_pages > 1): ?>
                    <div class="pagination-section">
                        <div class="pagination">
                            <?php if ($page > 1): ?>
                                <a href="?page=<?php echo $page - 1; ?>&status=<?php echo $status_filter; ?>&search=<?php echo urlencode($search); ?>">
                                    <i class="fas fa-chevron-left"></i>
                                </a>
                            <?php endif; ?>

                            <?php for ($i = max(1, $page - 2); $i <= min($total_pages, $page + 2); $i++): ?>
                                <?php if ($i === $page): ?>
                                    <span class="current"><?php echo $i; ?></span>
                                <?php else: ?>
                                    <a href="?page=<?php echo $i; ?>&status=<?php echo $status_filter; ?>&search=<?php echo urlencode($search); ?>">
                                        <?php echo $i; ?>
                                    </a>
                                <?php endif; ?>
                            <?php endfor; ?>

                            <?php if ($page < $total_pages): ?>
                                <a href="?page=<?php echo $page + 1; ?>&status=<?php echo $status_filter; ?>&search=<?php echo urlencode($search); ?>">
                                    <i class="fas fa-chevron-right"></i>
                                </a>
                            <?php endif; ?>
                        </div>
                    </div>
                <?php endif; ?>
            </div>
        </main>
    </div>

    <!-- 图片查看模态框 -->
    <div id="imageModal" class="modal" style="display: none;">
        <div class="modal-content">
            <span class="close" onclick="closeImageModal()">&times;</span>
            <h3 id="modalTitle">图片查看</h3>
            <img id="modalImage" src="" alt="身份证照片">
        </div>
    </div>

    <script>
        function showImageModal(imageUrl, title) {
            document.getElementById('modalImage').src = imageUrl;
            document.getElementById('modalTitle').textContent = title;
            document.getElementById('imageModal').style.display = 'block';
        }

        function closeImageModal() {
            document.getElementById('imageModal').style.display = 'none';
        }

        // 点击模态框外部关闭
        window.onclick = function(event) {
            const modal = document.getElementById('imageModal');
            if (event.target === modal) {
                closeImageModal();
            }
        }

        // ESC键关闭模态框
        document.addEventListener('keydown', function(event) {
            if (event.key === 'Escape') {
                closeImageModal();
            }
        });
    </script>

    <!-- 引入管理后台布局脚本 -->
    <script src="../assets/js/admin-layout.js"></script>

    <style>
        /* 身份证照片缩略图样式 */
        .id-card-photos {
            display: flex;
            gap: 5px;
            align-items: center;
        }

        .id-card-thumb {
            width: 40px;
            height: 25px;
            object-fit: cover;
            border-radius: 4px;
            cursor: pointer;
            border: 1px solid #ddd;
            transition: all 0.3s ease;
        }

        .id-card-thumb:hover {
            transform: scale(1.1);
            border-color: #40E0D0;
            box-shadow: 0 2px 8px rgba(64, 224, 208, 0.3);
        }

        .no-image {
            color: #999;
            font-size: 12px;
            font-style: italic;
        }

        /* 模态框样式 */
        .modal {
            position: fixed;
            z-index: 10000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.8);
            backdrop-filter: blur(5px);
        }

        .modal-content {
            position: relative;
            background-color: white;
            margin: 5% auto;
            padding: 20px;
            border-radius: 12px;
            width: 80%;
            max-width: 600px;
            max-height: 80vh;
            overflow: auto;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
        }

        .modal-content h3 {
            margin-top: 0;
            color: #333;
            text-align: center;
            margin-bottom: 20px;
        }

        .modal-content img {
            width: 100%;
            height: auto;
            border-radius: 8px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        }

        .close {
            position: absolute;
            right: 15px;
            top: 15px;
            color: #aaa;
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
            transition: color 0.3s ease;
        }

        .close:hover {
            color: #ff4757;
        }
    </style>
</body>
</html>
