<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>定位功能测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 600px;
            margin: 50px auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
        }
        .test-btn {
            background: #40E0D0;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
            font-size: 16px;
        }
        .test-btn:hover {
            background: #20B2AA;
        }
        .test-btn:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        .result {
            margin-top: 15px;
            padding: 15px;
            border-radius: 5px;
            white-space: pre-wrap;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .loading {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🌍 定位功能测试</h1>
        
        <!-- 浏览器原生定位测试 -->
        <div class="test-section">
            <h3>📍 浏览器原生定位测试</h3>
            <button class="test-btn" onclick="testBrowserLocation()">测试浏览器定位</button>
            <div id="browser-result" class="result info">等待测试...</div>
        </div>

        <!-- 高德地图API测试 -->
        <div class="test-section">
            <h3>🗺️ 高德地图API测试</h3>
            <button class="test-btn" onclick="testAmapAPI()">测试高德API加载</button>
            <button class="test-btn" onclick="testAmapLocation()">测试高德定位</button>
            <button class="test-btn" onclick="testAmapIPLocation()">测试高德IP定位</button>
            <div id="amap-result" class="result info">等待测试...</div>
        </div>

        <!-- 第三方IP定位测试 -->
        <div class="test-section">
            <h3>🌐 第三方IP定位测试</h3>
            <button class="test-btn" onclick="testThirdPartyIP()">测试第三方IP定位</button>
            <div id="ip-result" class="result info">等待测试...</div>
        </div>

        <!-- 综合定位测试 -->
        <div class="test-section">
            <h3>🎯 综合定位测试</h3>
            <button class="test-btn" onclick="testAllLocation()">运行所有定位测试</button>
            <div id="all-result" class="result info">等待测试...</div>
        </div>
    </div>

    <!-- 高德地图API -->
    <script src="https://webapi.amap.com/maps?v=2.0&key=e318a539d606974c5f13654e905cf555"></script>
    
    <script>
        // 测试浏览器原生定位
        function testBrowserLocation() {
            const result = document.getElementById('browser-result');
            result.className = 'result loading';
            result.textContent = '🔄 测试浏览器定位中...';

            if (!navigator.geolocation) {
                result.className = 'result error';
                result.textContent = '❌ 浏览器不支持定位功能';
                return;
            }

            navigator.geolocation.getCurrentPosition(
                function(position) {
                    result.className = 'result success';
                    result.textContent = `✅ 浏览器定位成功！
                    
经度: ${position.coords.longitude}
纬度: ${position.coords.latitude}
精度: ${position.coords.accuracy}米
时间: ${new Date(position.timestamp).toLocaleString()}`;
                },
                function(error) {
                    result.className = 'result error';
                    result.textContent = `❌ 浏览器定位失败: ${error.message}
                    
错误代码: ${error.code}
错误类型: ${getLocationErrorType(error.code)}`;
                },
                {
                    enableHighAccuracy: true,
                    timeout: 10000,
                    maximumAge: 300000
                }
            );
        }

        // 获取定位错误类型
        function getLocationErrorType(code) {
            switch(code) {
                case 1: return 'PERMISSION_DENIED - 用户拒绝定位';
                case 2: return 'POSITION_UNAVAILABLE - 位置不可用';
                case 3: return 'TIMEOUT - 定位超时';
                default: return '未知错误';
            }
        }

        // 测试高德地图API
        function testAmapAPI() {
            const result = document.getElementById('amap-result');
            
            if (typeof AMap === 'undefined') {
                result.className = 'result error';
                result.textContent = '❌ 高德地图API未加载';
                return;
            }

            result.className = 'result success';
            result.textContent = `✅ 高德地图API加载成功！
            
API版本: ${AMap.version || '未知'}
可用插件: ${Object.keys(AMap).length}个`;
        }

        // 测试高德定位
        function testAmapLocation() {
            const result = document.getElementById('amap-result');
            
            if (typeof AMap === 'undefined') {
                result.className = 'result error';
                result.textContent = '❌ 高德地图API未加载';
                return;
            }

            result.className = 'result loading';
            result.textContent = '🔄 测试高德定位中...';

            AMap.plugin('AMap.Geolocation', function() {
                const geolocation = new AMap.Geolocation({
                    enableHighAccuracy: true,
                    timeout: 15000,
                    needAddress: true,
                    extensions: 'all'
                });

                geolocation.getCurrentPosition(function(status, data) {
                    if (status === 'complete') {
                        result.className = 'result success';
                        result.textContent = `✅ 高德定位成功！
                        
经度: ${data.position.lng}
纬度: ${data.position.lat}
精度: ${data.accuracy}米
地址: ${data.formattedAddress || '未获取到地址'}
城市: ${data.addressComponent?.city || '未知'}
省份: ${data.addressComponent?.province || '未知'}`;
                    } else {
                        result.className = 'result error';
                        result.textContent = `❌ 高德定位失败: ${data.message || '未知错误'}
                        
状态: ${status}
详细信息: ${JSON.stringify(data, null, 2)}`;
                    }
                });
            });
        }

        // 测试高德IP定位
        function testAmapIPLocation() {
            const result = document.getElementById('amap-result');
            
            if (typeof AMap === 'undefined') {
                result.className = 'result error';
                result.textContent = '❌ 高德地图API未加载';
                return;
            }

            result.className = 'result loading';
            result.textContent = '🔄 测试高德IP定位中...';

            AMap.plugin('AMap.CitySearch', function() {
                const citySearch = new AMap.CitySearch();
                citySearch.getLocalCity(function(status, data) {
                    if (status === 'complete' && data.info === 'OK') {
                        result.className = 'result success';
                        result.textContent = `✅ 高德IP定位成功！
                        
城市: ${data.city}
省份: ${data.province}
城市编码: ${data.citycode}
行政区编码: ${data.adcode}`;
                    } else {
                        result.className = 'result error';
                        result.textContent = `❌ 高德IP定位失败
                        
状态: ${status}
详细信息: ${JSON.stringify(data, null, 2)}`;
                    }
                });
            });
        }

        // 测试第三方IP定位
        function testThirdPartyIP() {
            const result = document.getElementById('ip-result');
            result.className = 'result loading';
            result.textContent = '🔄 测试第三方IP定位中...';

            fetch('https://ipapi.co/json/')
                .then(response => response.json())
                .then(data => {
                    result.className = 'result success';
                    result.textContent = `✅ 第三方IP定位成功！
                    
IP地址: ${data.ip || '未知'}
城市: ${data.city || '未知'}
地区: ${data.region || '未知'}
国家: ${data.country_name || '未知'}
时区: ${data.timezone || '未知'}
ISP: ${data.org || '未知'}`;
                })
                .catch(error => {
                    result.className = 'result error';
                    result.textContent = `❌ 第三方IP定位失败: ${error.message}`;
                });
        }

        // 运行所有定位测试
        function testAllLocation() {
            const result = document.getElementById('all-result');
            result.className = 'result loading';
            result.textContent = '🔄 运行所有定位测试中...';

            let results = [];
            let completed = 0;
            const total = 4;

            function checkComplete() {
                completed++;
                if (completed === total) {
                    result.className = 'result info';
                    result.textContent = `📊 所有定位测试完成！
                    
${results.join('\n\n')}`;
                }
            }

            // 测试1: 浏览器定位
            if (navigator.geolocation) {
                navigator.geolocation.getCurrentPosition(
                    function(position) {
                        results.push('✅ 浏览器定位: 成功');
                        checkComplete();
                    },
                    function(error) {
                        results.push(`❌ 浏览器定位: 失败 (${error.message})`);
                        checkComplete();
                    },
                    { timeout: 5000 }
                );
            } else {
                results.push('❌ 浏览器定位: 不支持');
                checkComplete();
            }

            // 测试2: 高德API
            if (typeof AMap !== 'undefined') {
                results.push('✅ 高德API: 已加载');
            } else {
                results.push('❌ 高德API: 未加载');
            }
            checkComplete();

            // 测试3: 高德IP定位
            if (typeof AMap !== 'undefined') {
                AMap.plugin('AMap.CitySearch', function() {
                    const citySearch = new AMap.CitySearch();
                    citySearch.getLocalCity(function(status, data) {
                        if (status === 'complete') {
                            results.push(`✅ 高德IP定位: 成功 (${data.city})`);
                        } else {
                            results.push('❌ 高德IP定位: 失败');
                        }
                        checkComplete();
                    });
                });
            } else {
                results.push('❌ 高德IP定位: API未加载');
                checkComplete();
            }

            // 测试4: 第三方IP定位
            fetch('https://ipapi.co/json/')
                .then(response => response.json())
                .then(data => {
                    results.push(`✅ 第三方IP定位: 成功 (${data.city})`);
                    checkComplete();
                })
                .catch(error => {
                    results.push('❌ 第三方IP定位: 失败');
                    checkComplete();
                });
        }
    </script>
</body>
</html>
