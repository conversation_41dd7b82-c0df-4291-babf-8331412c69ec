# 管理后台 Apache 配置

# 启用重写引擎
RewriteEngine On

# 设置默认首页
DirectoryIndex index.php

# 允许访问PHP文件
<Files "*.php">
    Order allow,deny
    Allow from all
</Files>

# 允许访问CSS和JS文件
<FilesMatch "\.(css|js|png|jpg|jpeg|gif|ico|svg)$">
    Order allow,deny
    Allow from all
</FilesMatch>

# 禁止访问敏感文件
<Files ".htaccess">
    Order deny,allow
    Deny from all
</Files>

<Files "*.md">
    Order deny,allow
    Deny from all
</Files>

# 设置错误页面
ErrorDocument 403 /houtai_backup/test.php
ErrorDocument 404 /houtai_backup/test.php

# 防止目录浏览
Options -Indexes

# 设置字符编码
AddDefaultCharset UTF-8
