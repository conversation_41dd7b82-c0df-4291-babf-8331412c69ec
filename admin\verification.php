<?php
session_start();

// 检查是否已登录
if (!isset($_SESSION['admin_id'])) {
    header('Location: login.php');
    exit;
}

// 数据库连接
require_once '../sql/db_config.php';
$pdo = getDbConnection();

// 处理审核操作
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    $verification_id = intval($_POST['verification_id']);
    $action = $_POST['action'];
    $reason = trim($_POST['reason'] ?? '');
    
    try {
        if ($action === 'approve') {
            // 通过审核
            $stmt = $pdo->prepare("
                UPDATE realname_verification 
                SET verification_status = 'approved', 
                    verified_at = NOW(),
                    verification_reason = NULL,
                    admin_id = ?
                WHERE id = ?
            ");
            $stmt->execute([$_SESSION['admin_id'], $verification_id]);
            
            // 更新用户表的认证状态
            $stmt = $pdo->prepare("
                UPDATE users u
                JOIN realname_verification rv ON u.id = rv.user_id
                SET u.is_verified = 1
                WHERE rv.id = ?
            ");
            $stmt->execute([$verification_id]);
            
            $message = '审核通过成功';
            $message_type = 'success';
            
        } elseif ($action === 'reject') {
            // 拒绝审核
            if (empty($reason)) {
                $message = '拒绝审核必须填写原因';
                $message_type = 'error';
            } else {
                $stmt = $pdo->prepare("
                    UPDATE realname_verification 
                    SET verification_status = 'rejected', 
                        verification_reason = ?,
                        admin_id = ?
                    WHERE id = ?
                ");
                $stmt->execute([$reason, $_SESSION['admin_id'], $verification_id]);
                
                $message = '审核拒绝成功';
                $message_type = 'success';
            }
        }
    } catch (PDOException $e) {
        error_log("Verification action error: " . $e->getMessage());
        $message = '操作失败，请重试';
        $message_type = 'error';
    }
}

// 获取筛选参数
$status_filter = $_GET['status'] ?? 'pending';
$page = max(1, intval($_GET['page'] ?? 1));
$per_page = 20;
$offset = ($page - 1) * $per_page;

// 构建查询条件
$where_clause = "WHERE rv.verification_status = ?";
$params = [$status_filter];

// 获取总数
$count_sql = "
    SELECT COUNT(*) as total
    FROM realname_verification rv
    JOIN users u ON rv.user_id = u.id
    $where_clause
";
$stmt = $pdo->prepare($count_sql);
$stmt->execute($params);
$total = $stmt->fetch(PDO::FETCH_ASSOC)['total'];

// 获取认证列表
$sql = "
    SELECT 
        rv.*,
        u.username,
        u.phone,
        u.created_at as user_created_at,
        au.name as admin_name
    FROM realname_verification rv
    JOIN users u ON rv.user_id = u.id
    LEFT JOIN admin_users au ON rv.admin_id = au.id
    $where_clause
    ORDER BY rv.submitted_at DESC
    LIMIT $per_page OFFSET $offset
";

$stmt = $pdo->prepare($sql);
$stmt->execute($params);
$verifications = $stmt->fetchAll(PDO::FETCH_ASSOC);

// 计算分页
$total_pages = ceil($total / $per_page);
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>实名认证审核 - 趣玩星球后台</title>
    <link rel="stylesheet" href="css/admin.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <div class="admin-container">
        <!-- 侧边栏 -->
        <?php include 'includes/sidebar.php'; ?>

        <!-- 主内容区 -->
        <main class="main-content">
            <!-- 顶部栏 -->
            <header class="top-header">
                <div class="header-left">
                    <h1>实名认证审核</h1>
                    <p>管理用户实名认证申请</p>
                </div>
            </header>

            <?php if (isset($message)): ?>
                <div class="alert alert-<?php echo $message_type; ?>">
                    <i class="fas fa-<?php echo $message_type === 'success' ? 'check-circle' : 'exclamation-circle'; ?>"></i>
                    <?php echo htmlspecialchars($message); ?>
                </div>
            <?php endif; ?>

            <!-- 筛选和统计 -->
            <section class="filter-section">
                <div class="filter-tabs">
                    <a href="?status=pending" class="filter-tab <?php echo $status_filter === 'pending' ? 'active' : ''; ?>">
                        待审核
                    </a>
                    <a href="?status=approved" class="filter-tab <?php echo $status_filter === 'approved' ? 'active' : ''; ?>">
                        已通过
                    </a>
                    <a href="?status=rejected" class="filter-tab <?php echo $status_filter === 'rejected' ? 'active' : ''; ?>">
                        已拒绝
                    </a>
                </div>
                
                <div class="filter-info">
                    <span>共 <?php echo $total; ?> 条记录</span>
                </div>
            </section>

            <!-- 认证列表 -->
            <section class="verification-list">
                <?php if (empty($verifications)): ?>
                    <div class="empty-state">
                        <i class="fas fa-inbox"></i>
                        <h3>暂无数据</h3>
                        <p>当前没有<?php echo $status_filter === 'pending' ? '待审核的' : ($status_filter === 'approved' ? '已通过的' : '已拒绝的'); ?>认证申请</p>
                    </div>
                <?php else: ?>
                    <div class="verification-grid">
                        <?php foreach ($verifications as $verification): ?>
                            <div class="verification-card">
                                <div class="card-header">
                                    <div class="user-info">
                                        <h3><?php echo htmlspecialchars($verification['real_name']); ?></h3>
                                        <p>@<?php echo htmlspecialchars($verification['username']); ?></p>
                                    </div>
                                    <div class="status-badge status-<?php echo $verification['verification_status']; ?>">
                                        <?php
                                        $status_text = [
                                            'pending' => '待审核',
                                            'approved' => '已通过',
                                            'rejected' => '已拒绝'
                                        ];
                                        echo $status_text[$verification['verification_status']];
                                        ?>
                                    </div>
                                </div>
                                
                                <div class="card-content">
                                    <div class="info-row">
                                        <span class="label">身份证号：</span>
                                        <span class="value">
                                            <?php 
                                            $id_card = $verification['id_card_number'];
                                            echo substr($id_card, 0, 6) . '********' . substr($id_card, -4);
                                            ?>
                                        </span>
                                    </div>
                                    <div class="info-row">
                                        <span class="label">手机号：</span>
                                        <span class="value"><?php echo htmlspecialchars($verification['phone'] ?? '未绑定'); ?></span>
                                    </div>
                                    <div class="info-row">
                                        <span class="label">提交时间：</span>
                                        <span class="value"><?php echo date('Y-m-d H:i', strtotime($verification['submitted_at'])); ?></span>
                                    </div>
                                    
                                    <?php if ($verification['verification_status'] === 'approved'): ?>
                                        <div class="info-row">
                                            <span class="label">审核时间：</span>
                                            <span class="value"><?php echo date('Y-m-d H:i', strtotime($verification['verified_at'])); ?></span>
                                        </div>
                                        <div class="info-row">
                                            <span class="label">审核人员：</span>
                                            <span class="value"><?php echo htmlspecialchars($verification['admin_name'] ?? '系统'); ?></span>
                                        </div>
                                    <?php elseif ($verification['verification_status'] === 'rejected'): ?>
                                        <div class="info-row">
                                            <span class="label">拒绝原因：</span>
                                            <span class="value text-danger"><?php echo htmlspecialchars($verification['verification_reason']); ?></span>
                                        </div>
                                        <div class="info-row">
                                            <span class="label">审核人员：</span>
                                            <span class="value"><?php echo htmlspecialchars($verification['admin_name'] ?? '系统'); ?></span>
                                        </div>
                                    <?php endif; ?>
                                </div>
                                
                                <?php if ($verification['verification_status'] === 'pending'): ?>
                                    <div class="card-actions">
                                        <button class="btn btn-success" onclick="approveVerification(<?php echo $verification['id']; ?>)">
                                            <i class="fas fa-check"></i> 通过
                                        </button>
                                        <button class="btn btn-danger" onclick="rejectVerification(<?php echo $verification['id']; ?>)">
                                            <i class="fas fa-times"></i> 拒绝
                                        </button>
                                    </div>
                                <?php endif; ?>
                            </div>
                        <?php endforeach; ?>
                    </div>
                    
                    <!-- 分页 -->
                    <?php if ($total_pages > 1): ?>
                        <div class="pagination">
                            <?php if ($page > 1): ?>
                                <a href="?status=<?php echo $status_filter; ?>&page=<?php echo $page - 1; ?>" class="page-btn">
                                    <i class="fas fa-chevron-left"></i> 上一页
                                </a>
                            <?php endif; ?>
                            
                            <span class="page-info">
                                第 <?php echo $page; ?> 页，共 <?php echo $total_pages; ?> 页
                            </span>
                            
                            <?php if ($page < $total_pages): ?>
                                <a href="?status=<?php echo $status_filter; ?>&page=<?php echo $page + 1; ?>" class="page-btn">
                                    下一页 <i class="fas fa-chevron-right"></i>
                                </a>
                            <?php endif; ?>
                        </div>
                    <?php endif; ?>
                <?php endif; ?>
            </section>
        </main>
    </div>

    <!-- 审核弹窗 -->
    <div class="modal" id="approveModal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>通过审核</h3>
                <button class="modal-close" onclick="closeModal('approveModal')">&times;</button>
            </div>
            <div class="modal-body">
                <p>确认通过该用户的实名认证申请吗？</p>
                <form id="approveForm" method="POST">
                    <input type="hidden" name="verification_id" id="approveVerificationId">
                    <input type="hidden" name="action" value="approve">
                </form>
            </div>
            <div class="modal-footer">
                <button class="btn btn-secondary" onclick="closeModal('approveModal')">取消</button>
                <button class="btn btn-success" onclick="submitApprove()">确认通过</button>
            </div>
        </div>
    </div>

    <div class="modal" id="rejectModal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>拒绝审核</h3>
                <button class="modal-close" onclick="closeModal('rejectModal')">&times;</button>
            </div>
            <div class="modal-body">
                <form id="rejectForm" method="POST">
                    <input type="hidden" name="verification_id" id="rejectVerificationId">
                    <input type="hidden" name="action" value="reject">
                    <div class="form-group">
                        <label for="rejectReason">拒绝原因：</label>
                        <textarea name="reason" id="rejectReason" rows="4" placeholder="请详细说明拒绝原因..." required></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button class="btn btn-secondary" onclick="closeModal('rejectModal')">取消</button>
                <button class="btn btn-danger" onclick="submitReject()">确认拒绝</button>
            </div>
        </div>
    </div>

    <script src="js/verification.js"></script>
</body>
</html>
