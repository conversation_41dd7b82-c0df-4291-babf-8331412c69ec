<?php
// 设置字符编码
header('Content-Type: text/html; charset=UTF-8');
ini_set('default_charset', 'UTF-8');

session_start();

// 如果已登录，跳转到首页
if (isset($_SESSION['admin_id'])) {
    header('Location: home.php');
    exit;
}

// 直接引用当前目录的数据库配置文件
require_once 'db_config.php';

$error_message = '';

// 检查客服登录错误消息
if (isset($_SESSION['cs_login_error'])) {
    $error_message = $_SESSION['cs_login_error'];
    unset($_SESSION['cs_login_error']);
}

// 处理登录
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $login_field = trim($_POST['login_field'] ?? '');
    $password = $_POST['password'] ?? '';
    $captcha = $_POST['captcha'] ?? '';

    // 验证验证码
    if (empty($captcha) || strtoupper($captcha) !== ($_SESSION['captcha'] ?? '')) {
        $error_message = '验证码错误';
    } elseif (empty($login_field) || empty($password)) {
        $error_message = '请输入工号/邮箱和密码';
    } else {
        try {
            $pdo = getDbConnection();

            // 查询管理员账号
            $stmt = $pdo->prepare("
                SELECT * FROM admin_users
                WHERE (employee_id = ? OR email = ?) AND status = 1
            ");
            $stmt->execute([$login_field, $login_field]);
            $admin = $stmt->fetch(PDO::FETCH_ASSOC);

            // 检查密码（支持明文和哈希两种方式）
            $password_valid = false;
            if (password_verify($password, $admin['password'])) {
                // 哈希密码验证成功
                $password_valid = true;
            } elseif ($password === $admin['password']) {
                // 明文密码验证成功（临时调试用）
                $password_valid = true;
            }

            if ($admin && $password_valid) {
                // 登录成功
                $_SESSION['admin_logged_in'] = true;
                $_SESSION['admin_id'] = $admin['id'];
                $_SESSION['admin_name'] = $admin['name'] ?? $admin['nickname'] ?? $admin['username'];
                $_SESSION['admin_role'] = $admin['role_name'] ?? '审核员';
                $_SESSION['admin_employee_id'] = $admin['employee_id'];
                $_SESSION['admin_department'] = $admin['department'] ?? '总经办';

                // 更新最后登录时间
                $stmt = $pdo->prepare("UPDATE admin_users SET last_login = NOW() WHERE id = ?");
                $stmt->execute([$admin['id']]);

                header('Location: home.php');
                exit;
            } else {
                $error_message = '工号/邮箱或密码错误';
                // 调试信息（生产环境请删除）
                if ($admin) {
                    error_log("登录失败 - 用户存在，密码不匹配。输入密码: $password, 数据库密码: " . $admin['password']);
                } else {
                    error_log("登录失败 - 用户不存在。登录字段: $login_field");
                }
            }
        } catch (PDOException $e) {
            $error_message = '系统错误，请稍后重试';
        }
    }
}
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>趣玩星球管理后台 - 登录</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #6F7BF5 0%, #4D5DFB 50%, #FF6B6B 100%);
            height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            overflow: hidden;
            background-attachment: fixed;
        }

        /* 动态背景装饰 */
        body::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="20" cy="20" r="2" fill="rgba(255,255,255,0.1)"/><circle cx="80" cy="40" r="1.5" fill="rgba(255,255,255,0.15)"/><circle cx="40" cy="80" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="90" cy="90" r="2.5" fill="rgba(255,255,255,0.08)"/><circle cx="10" cy="60" r="1.2" fill="rgba(255,255,255,0.12)"/></svg>') repeat;
            animation: float 20s infinite linear;
            pointer-events: none;
        }

        @keyframes float {
            0% { transform: translate(0, 0) rotate(0deg); }
            100% { transform: translate(-50px, -50px) rotate(360deg); }
        }

        .login-container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            padding: 25px 25px;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.15), 0 0 0 1px rgba(255,255,255,0.2);
            width: 420px;
            height: 85vh;
            max-height: 650px;
            position: relative;
            z-index: 1;
            border: 1px solid rgba(255,255,255,0.3);
            display: flex;
            flex-direction: column;
            overflow-y: auto;
        }

        /* 自定义滚动条样式 */
        .login-container::-webkit-scrollbar {
            width: 6px;
        }

        .login-container::-webkit-scrollbar-track {
            background: rgba(0,0,0,0.1);
            border-radius: 10px;
        }

        .login-container::-webkit-scrollbar-thumb {
            background: rgba(111,123,245,0.3);
            border-radius: 10px;
        }

        .login-container::-webkit-scrollbar-thumb:hover {
            background: rgba(111,123,245,0.5);
        }

        .login-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, rgba(111,123,245,0.1) 0%, rgba(77,93,251,0.1) 100%);
            border-radius: 20px;
            z-index: -1;
        }

        .login-header {
            text-align: center;
            margin-bottom: 15px;
        }

        /* 登录方式切换标签 */
        .login-tabs {
            display: flex;
            background: rgba(248, 249, 250, 0.8);
            border-radius: 12px;
            padding: 4px;
            margin-bottom: 15px;
            backdrop-filter: blur(10px);
        }

        .login-tab {
            flex: 1;
            padding: 10px 8px;
            text-align: center;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 13px;
            font-weight: 500;
            color: #666666;
            position: relative;
        }

        .login-tab.active {
            background: linear-gradient(135deg, #6F7BF5, #4D5DFB);
            color: white;
            box-shadow: 0 4px 12px rgba(111,123,245,0.3);
        }

        .login-tab:not(.active):hover {
            background: rgba(111,123,245,0.1);
            color: #6F7BF5;
        }

        .login-tab-icon {
            display: block;
            font-size: 16px;
            margin-bottom: 4px;
        }

        .login-tab-text {
            font-size: 11px;
            line-height: 1.2;
        }

        .login-header .logo {
            width: 45px;
            height: 45px;
            background: linear-gradient(135deg, #6F7BF5, #4D5DFB);
            border-radius: 12px;
            margin: 0 auto 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 20px;
            color: white;
            box-shadow: 0 6px 20px rgba(111,123,245,0.3);
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        .login-header h1 {
            color: #333333;
            margin-bottom: 4px;
            font-size: 18px;
            font-weight: 700;
            background: linear-gradient(135deg, #6F7BF5, #4D5DFB);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .login-header p {
            color: #666666;
            font-size: 12px;
            font-weight: 500;
        }

        /* 登录内容区域 */
        .login-content {
            display: none;
        }

        .login-content.active {
            display: block;
        }

        .login-type-title {
            text-align: center;
            margin-bottom: 8px;
            color: #333333;
            font-size: 16px;
            font-weight: 600;
        }

        .login-type-desc {
            text-align: center;
            margin-bottom: 15px;
            color: #666666;
            font-size: 13px;
            line-height: 1.4;
        }

        .coming-soon {
            text-align: center;
            padding: 40px 20px;
            color: #999999;
        }

        .coming-soon i {
            font-size: 48px;
            margin-bottom: 16px;
            color: #6F7BF5;
            opacity: 0.6;
        }

        .coming-soon h3 {
            margin-bottom: 8px;
            color: #666666;
            font-size: 18px;
        }

        .coming-soon p {
            font-size: 14px;
            line-height: 1.5;
        }

        .form-group {
            margin-bottom: 15px;
        }

        .form-group label {
            display: block;
            margin-bottom: 6px;
            color: #333333;
            font-weight: 500;
            font-size: 14px;
        }

        .input-group {
            position: relative;
        }

        .input-group i {
            position: absolute;
            left: 15px;
            top: 50%;
            transform: translateY(-50%);
            color: #999;
        }

        .input-group input {
            width: 100%;
            padding: 14px 18px 14px 45px;
            border: 2px solid #E2E8F0;
            border-radius: 12px;
            font-size: 15px;
            transition: all 0.3s ease;
            background: rgba(255,255,255,0.8);
        }

        .input-group input:focus {
            outline: none;
            border-color: #6F7BF5;
            background: white;
            box-shadow: 0 0 0 3px rgba(111,123,245,0.1);
            transform: translateY(-2px);
        }

        .input-group input::placeholder {
            color: #A0AEC0;
        }

        .captcha-group {
            display: flex;
            gap: 10px;
            align-items: flex-end;
        }

        .captcha-group .input-group {
            flex: 1;
        }

        .captcha-image {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 5px;
        }

        .captcha-image img {
            width: 120px;
            height: 40px;
            border: 2px solid #e1e5e9;
            border-radius: 8px;
            cursor: pointer;
        }

        .refresh-captcha {
            background: #667eea;
            color: white;
            border: none;
            border-radius: 6px;
            padding: 6px 8px;
            cursor: pointer;
            font-size: 12px;
        }

        .login-btn {
            width: 100%;
            background: linear-gradient(135deg, #6F7BF5 0%, #4D5DFB 100%);
            color: white;
            padding: 16px;
            border: none;
            border-radius: 12px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 8px 20px rgba(111,123,245,0.3);
            position: relative;
            overflow: hidden;
        }

        .login-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.5s;
        }

        .login-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 12px 30px rgba(111,123,245,0.4);
        }

        .login-btn:hover::before {
            left: 100%;
        }

        .login-btn:active {
            transform: translateY(-1px);
        }

        .error-message {
            background: #fff5f5;
            border: 1px solid #fed7d7;
            color: #e53e3e;
            padding: 12px;
            border-radius: 8px;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
        }

        .error-message i {
            margin-right: 8px;
        }

        .login-footer {
            text-align: center;
            margin-top: 20px;
            color: #999999;
            font-size: 13px;
            font-weight: 500;
        }

        /* 响应式设计 */
        @media (max-width: 480px) {
            .login-container {
                margin: 20px;
                padding: 20px 15px;
                width: calc(100% - 40px);
                height: 90vh;
                max-height: 90vh;
            }

            .login-header .logo {
                width: 50px;
                height: 50px;
                font-size: 20px;
            }

            .login-header h1 {
                font-size: 20px;
            }

            .login-tab {
                padding: 8px 4px;
                font-size: 12px;
            }

            .login-tab-icon {
                font-size: 14px;
                margin-bottom: 2px;
            }

            .login-tab-text {
                font-size: 10px;
            }

            .login-type-title {
                font-size: 15px;
            }

            .login-type-desc {
                font-size: 12px;
            }

            .coming-soon {
                padding: 30px 15px;
            }

            .coming-soon i {
                font-size: 36px;
            }

            .coming-soon h3 {
                font-size: 16px;
            }

            .coming-soon p {
                font-size: 13px;
            }
        }

        /* 加载动画 */
        .login-container {
            animation: slideUp 0.6s ease-out;
        }

        @keyframes slideUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="login-header">
            <div class="logo">
                <img src="https://s1.imagehub.cc/images/2025/05/26/2cdd63b39f96072fc60cd733027d2a30.png" alt="趣玩星球" style="width: 100%; height: 100%; object-fit: contain;">
            </div>
            <h1>趣玩星球</h1>
            <p>管理后台系统</p>
        </div>

        <!-- 登录方式切换标签 -->
        <div class="login-tabs">
            <div class="login-tab active" data-tab="admin">
                <span class="login-tab-icon"><i class="fas fa-user-shield"></i></span>
                <span class="login-tab-text">主后台<br>管理员</span>
            </div>
            <div class="login-tab" data-tab="service">
                <span class="login-tab-icon"><i class="fas fa-headset"></i></span>
                <span class="login-tab-text">客服<br>系统</span>
            </div>
            <div class="login-tab" data-tab="merchant">
                <span class="login-tab-icon"><i class="fas fa-store"></i></span>
                <span class="login-tab-text">陪玩<br>商家</span>
            </div>
            <div class="login-tab" data-tab="workspace">
                <span class="login-tab-icon"><i class="fas fa-gamepad"></i></span>
                <span class="login-tab-text">陪玩<br>工作台</span>
            </div>
        </div>

        <?php if ($error_message): ?>
            <div class="error-message">
                <i class="fas fa-exclamation-triangle"></i>
                <?php echo htmlspecialchars($error_message); ?>
            </div>
        <?php endif; ?>

        <!-- 主后台管理员登录 -->
        <div class="login-content active" id="admin-login">
            <div class="login-type-title">主后台管理员登录</div>
            <div class="login-type-desc">管理整个趣玩星球平台的核心功能</div>

            <form method="POST" action="">
            <div class="form-group">
                <label for="login_field">工号/邮箱</label>
                <div class="input-group">
                    <i class="fas fa-user"></i>
                    <input type="text" id="login_field" name="login_field"
                           placeholder="请输入工号或邮箱" required>
                </div>
            </div>

            <div class="form-group">
                <label for="password">密码</label>
                <div class="input-group">
                    <i class="fas fa-lock"></i>
                    <input type="password" id="password" name="password"
                           placeholder="请输入密码" required>
                </div>
            </div>

            <div class="form-group">
                <label for="captcha">验证码</label>
                <div class="captcha-group">
                    <div class="input-group">
                        <i class="fas fa-shield-alt"></i>
                        <input type="text" id="captcha" name="captcha"
                               placeholder="请输入验证码" required maxlength="4">
                    </div>
                    <div class="captcha-image">
                        <img src="captcha.php" alt="验证码" id="captchaImg" onclick="refreshCaptcha()">
                        <button type="button" class="refresh-captcha" onclick="refreshCaptcha()">
                            <i class="fas fa-sync-alt"></i>
                        </button>
                    </div>
                </div>
            </div>

                <button type="submit" class="login-btn">
                    <i class="fas fa-sign-in-alt"></i>
                    登录
                </button>
            </form>
        </div>

        <!-- 客服系统登录 -->
        <div class="login-content" id="service-login">
            <div class="login-type-title">客服系统登录</div>
            <div class="login-type-desc">处理用户咨询和客户服务相关工作</div>

            <form method="POST" action="customer_service_system/login_process.php">
                <div class="form-group">
                    <label for="service_employee_id">工号</label>
                    <div class="input-group">
                        <i class="fas fa-id-badge"></i>
                        <input type="text" id="service_employee_id" name="employee_id"
                               placeholder="请输入客服工号" required>
                    </div>
                </div>

                <div class="form-group">
                    <label for="service_password">密码</label>
                    <div class="input-group">
                        <i class="fas fa-lock"></i>
                        <input type="password" id="service_password" name="password"
                               placeholder="请输入密码" required>
                    </div>
                </div>

                <div class="form-group">
                    <label for="service_captcha">验证码</label>
                    <div class="captcha-group">
                        <div class="input-group">
                            <i class="fas fa-shield-alt"></i>
                            <input type="text" id="service_captcha" name="captcha"
                                   placeholder="请输入验证码" required maxlength="4">
                        </div>
                        <div class="captcha-image">
                            <img src="captcha.php" alt="验证码" id="serviceCaptchaImg" onclick="refreshServiceCaptcha()">
                            <button type="button" class="refresh-captcha" onclick="refreshServiceCaptcha()">
                                <i class="fas fa-sync-alt"></i>
                            </button>
                        </div>
                    </div>
                </div>

                <button type="submit" class="login-btn">
                    <i class="fas fa-headset"></i>
                    登录客服系统
                </button>
            </form>
        </div>

        <!-- 陪玩商家后台登录 -->
        <div class="login-content" id="merchant-login">
            <div class="login-type-title">陪玩商家后台</div>
            <div class="login-type-desc">管理陪玩服务、订单和收益</div>

            <div class="coming-soon">
                <i class="fas fa-store"></i>
                <h3>陪玩商家后台</h3>
                <p>商家管理系统正在开发中<br>敬请期待...</p>
            </div>
        </div>

        <!-- 陪玩工作台登录 -->
        <div class="login-content" id="workspace-login">
            <div class="login-type-title">陪玩工作台</div>
            <div class="login-type-desc">陪玩师接单和服务管理工作台</div>

            <div class="coming-soon">
                <i class="fas fa-gamepad"></i>
                <h3>陪玩工作台</h3>
                <p>陪玩师工作台正在开发中<br>敬请期待...</p>
            </div>
        </div>

        <div class="login-footer">
            <p>© 2024 趣玩星球管理后台系统</p>
        </div>
    </div>

    <script>
        function refreshCaptcha() {
            const captchaImg = document.getElementById('captchaImg');
            captchaImg.src = 'captcha.php?' + new Date().getTime();
        }

        function refreshServiceCaptcha() {
            const captchaImg = document.getElementById('serviceCaptchaImg');
            captchaImg.src = 'captcha.php?' + new Date().getTime();
        }

        // 登录方式切换功能
        function switchLoginTab(tabName) {
            // 移除所有标签的active状态
            document.querySelectorAll('.login-tab').forEach(tab => {
                tab.classList.remove('active');
            });

            // 隐藏所有登录内容
            document.querySelectorAll('.login-content').forEach(content => {
                content.classList.remove('active');
            });

            // 激活选中的标签
            document.querySelector(`[data-tab="${tabName}"]`).classList.add('active');

            // 显示对应的登录内容
            document.getElementById(`${tabName}-login`).classList.add('active');
        }

        document.addEventListener('DOMContentLoaded', function() {
            // 绑定标签点击事件
            document.querySelectorAll('.login-tab').forEach(tab => {
                tab.addEventListener('click', function() {
                    const tabName = this.getAttribute('data-tab');
                    switchLoginTab(tabName);
                });
            });

            // 默认聚焦到用户名输入框（仅主后台）
            const loginField = document.getElementById('login_field');
            if (loginField) {
                loginField.focus();
            }
        });
    </script>
</body>
</html>
