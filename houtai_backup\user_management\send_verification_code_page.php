<?php
/**
 * 后台发送验证码管理页面
 * 集成WebSocket实时通知功能
 */

session_start();

// 检查管理员登录状态
if (!isset($_SESSION['admin_id'])) {
    header('Location: ../login.php');
    exit;
}

$admin_id = $_SESSION['admin_id'];
$admin_username = $_SESSION['admin_username'] ?? '管理员';

// 获取最近的验证码记录
try {
    $pdo = new PDO(
        "mysql:host=localhost;dbname=quwanplanet;charset=utf8mb4",
        "quwanplanet",
        "nJmJm23FB4Xn6Fc3",
        [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC
        ]
    );
    
    $stmt = $pdo->prepare("
        SELECT v.*, u.username, a.username as admin_username
        FROM verification_codes v
        LEFT JOIN users u ON v.user_id = u.id
        LEFT JOIN admin_users a ON v.sent_by_admin = a.id
        WHERE v.sent_by_admin IS NOT NULL
        ORDER BY v.created_at DESC
        LIMIT 20
    ");
    $stmt->execute();
    $recent_codes = $stmt->fetchAll();
    
} catch (Exception $e) {
    $recent_codes = [];
}
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>发送验证码 - 趣玩星球管理后台</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        .main-content {
            margin-left: 250px;
            padding: 20px;
        }
        .card {
            border: none;
            box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
            border-radius: 0.5rem;
        }
        .card-header {
            background: linear-gradient(135deg, #6F7BF5 0%, #9B59B6 100%);
            color: white;
            border-radius: 0.5rem 0.5rem 0 0 !important;
        }
        .btn-primary {
            background: linear-gradient(135deg, #6F7BF5 0%, #9B59B6 100%);
            border: none;
        }
        .btn-primary:hover {
            background: linear-gradient(135deg, #5A67D8 0%, #805AD5 100%);
            transform: translateY(-1px);
        }
        .user-search-result {
            cursor: pointer;
            transition: all 0.2s;
            border-radius: 0.25rem;
        }
        .user-search-result:hover {
            background-color: #f8f9fa;
        }
        .verification-code {
            font-family: 'Courier New', monospace;
            font-size: 1.1rem;
            font-weight: bold;
            color: #6F7BF5;
            background: #f8f9fa;
            padding: 2px 6px;
            border-radius: 4px;
        }
        .status-badge {
            font-size: 0.75rem;
        }
        .notification-status {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 1050;
            min-width: 300px;
        }
        .realtime-indicator {
            display: inline-block;
            width: 8px;
            height: 8px;
            background: #28a745;
            border-radius: 50%;
            margin-right: 5px;
            animation: pulse 2s infinite;
        }
        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }
    </style>
</head>
<body>
    <?php include '../includes/sidebar.php'; ?>
    
    <!-- 实时通知状态 -->
    <div id="notificationStatus" class="notification-status">
        <div class="alert alert-info alert-dismissible fade show" role="alert">
            <span class="realtime-indicator"></span>
            <strong>实时通知系统</strong> 已启用
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    </div>
    
    <div class="main-content">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h2><i class="bi bi-send"></i> 发送验证码</h2>
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="../dashboard.php">首页</a></li>
                    <li class="breadcrumb-item"><a href="index.php">用户管理</a></li>
                    <li class="breadcrumb-item active">发送验证码</li>
                </ol>
            </nav>
        </div>

        <div class="row">
            <!-- 发送验证码表单 -->
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="bi bi-send-fill"></i> 发送验证码到前台</h5>
                    </div>
                    <div class="card-body">
                        <div class="alert alert-info">
                            <i class="bi bi-info-circle"></i>
                            <strong>实时通知功能：</strong>发送的验证码将通过WebSocket实时推送到用户前台，用户会立即收到弹窗通知。
                        </div>
                        
                        <form id="sendCodeForm">
                            <!-- 用户搜索 -->
                            <div class="mb-3">
                                <label class="form-label">
                                    <i class="bi bi-search"></i> 搜索用户
                                </label>
                                <div class="input-group">
                                    <input type="text" class="form-control" id="userSearch" placeholder="输入用户名、手机号或邮箱搜索">
                                    <button type="button" class="btn btn-outline-secondary" onclick="searchUsers()">
                                        <i class="bi bi-search"></i> 搜索
                                    </button>
                                </div>
                                <div id="searchResults" class="mt-2"></div>
                            </div>

                            <!-- 选中的用户 -->
                            <div class="mb-3" id="selectedUserDiv" style="display: none;">
                                <label class="form-label">选中用户</label>
                                <div class="alert alert-success" id="selectedUserInfo"></div>
                                <input type="hidden" id="selectedUserId">
                            </div>

                            <!-- 手机号 -->
                            <div class="mb-3">
                                <label class="form-label">
                                    <i class="bi bi-phone"></i> 手机号
                                </label>
                                <input type="text" class="form-control" id="phone" placeholder="请输入手机号" required>
                                <div class="form-text">验证码将发送到此手机号对应的用户前台</div>
                            </div>

                            <!-- 验证码类型 -->
                            <div class="mb-3">
                                <label class="form-label">
                                    <i class="bi bi-tag"></i> 验证码类型
                                </label>
                                <select class="form-select" id="codeType" required>
                                    <option value="">请选择类型</option>
                                    <option value="admin_send">管理员发送</option>
                                    <option value="security_verify">安全验证</option>
                                    <option value="system_notice">系统通知</option>
                                    <option value="account_verify">账户验证</option>
                                    <option value="password_reset">密码重置</option>
                                    <option value="login_verify">登录验证</option>
                                </select>
                            </div>

                            <!-- 备注 -->
                            <div class="mb-3">
                                <label class="form-label">
                                    <i class="bi bi-chat-text"></i> 备注说明
                                </label>
                                <textarea class="form-control" id="note" rows="3" placeholder="请输入发送原因或备注信息，用户会在弹窗中看到此信息" required></textarea>
                            </div>

                            <!-- 有效期 -->
                            <div class="mb-3">
                                <label class="form-label">
                                    <i class="bi bi-clock"></i> 有效期
                                </label>
                                <select class="form-select" id="expiry">
                                    <option value="5">5分钟</option>
                                    <option value="10">10分钟</option>
                                    <option value="15">15分钟</option>
                                    <option value="30">30分钟</option>
                                    <option value="60">60分钟</option>
                                </select>
                            </div>

                            <div class="d-grid">
                                <button type="submit" class="btn btn-primary btn-lg">
                                    <i class="bi bi-send"></i> 发送验证码到前台
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>

            <!-- 最近发送记录 -->
            <div class="col-md-4">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="bi bi-clock-history"></i> 最近发送记录</h5>
                    </div>
                    <div class="card-body" style="max-height: 600px; overflow-y: auto;">
                        <?php if (empty($recent_codes)): ?>
                            <p class="text-muted text-center">
                                <i class="bi bi-inbox"></i><br>
                                暂无发送记录
                            </p>
                        <?php else: ?>
                            <?php foreach ($recent_codes as $code): ?>
                                <div class="border-bottom pb-3 mb-3">
                                    <div class="d-flex justify-content-between align-items-start">
                                        <div class="flex-grow-1">
                                            <div class="d-flex align-items-center mb-1">
                                                <i class="bi bi-person-circle me-2"></i>
                                                <strong><?= htmlspecialchars($code['username']) ?></strong>
                                            </div>
                                            <div class="verification-code mb-2"><?= htmlspecialchars($code['code']) ?></div>
                                            <div class="d-flex align-items-center gap-2 mb-1">
                                                <span class="badge bg-secondary"><?= htmlspecialchars($code['type']) ?></span>
                                                <span class="badge status-badge <?= $code['status'] === 'pending' ? 'bg-warning' : ($code['status'] === 'used' ? 'bg-success' : 'bg-secondary') ?>">
                                                    <?= htmlspecialchars($code['status']) ?>
                                                </span>
                                            </div>
                                            <small class="text-muted">
                                                <i class="bi bi-clock"></i> <?= date('m-d H:i', strtotime($code['created_at'])) ?>
                                                | <i class="bi bi-person-badge"></i> <?= htmlspecialchars($code['admin_username'] ?? '未知') ?>
                                            </small>
                                        </div>
                                    </div>
                                    <?php if ($code['admin_note']): ?>
                                        <div class="mt-2 p-2 bg-light rounded">
                                            <small class="text-muted">
                                                <i class="bi bi-chat-quote"></i> <?= htmlspecialchars($code['admin_note']) ?>
                                            </small>
                                        </div>
                                    <?php endif; ?>
                                </div>
                            <?php endforeach; ?>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // 搜索用户
        async function searchUsers() {
            const search = document.getElementById('userSearch').value.trim();
            if (!search) {
                showAlert('请输入搜索关键词', 'warning');
                return;
            }

            try {
                const response = await fetch('search_users_api.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ search: search })
                });

                const data = await response.json();
                
                if (data.success) {
                    displaySearchResults(data.users);
                } else {
                    showAlert(data.message, 'danger');
                }
            } catch (error) {
                showAlert('搜索失败：' + error.message, 'danger');
            }
        }

        // 显示搜索结果
        function displaySearchResults(users) {
            const resultsDiv = document.getElementById('searchResults');
            
            if (users.length === 0) {
                resultsDiv.innerHTML = '<div class="alert alert-warning"><i class="bi bi-exclamation-triangle"></i> 未找到匹配的用户</div>';
                return;
            }

            let html = '<div class="border rounded p-2 bg-light">';
            html += '<small class="text-muted mb-2 d-block"><i class="bi bi-info-circle"></i> 点击选择用户</small>';
            users.forEach(user => {
                html += `
                    <div class="user-search-result p-2 mb-1 border-bottom" onclick="selectUser(${user.id}, '${user.username}', '${user.phone || ''}')">
                        <div class="d-flex align-items-center">
                            <i class="bi bi-person-circle me-2"></i>
                            <div>
                                <strong>${user.username}</strong>
                                ${user.phone ? `<span class="text-muted"> - ${user.phone}</span>` : ''}
                                ${user.email ? `<br><small class="text-muted">${user.email}</small>` : ''}
                                <small class="text-muted d-block">ID: ${user.id} | 注册: ${user.created_at}</small>
                            </div>
                        </div>
                    </div>
                `;
            });
            html += '</div>';
            
            resultsDiv.innerHTML = html;
        }

        // 选择用户
        function selectUser(userId, username, phone) {
            document.getElementById('selectedUserId').value = userId;
            document.getElementById('selectedUserInfo').innerHTML = `
                <i class="bi bi-person-check-fill"></i> 已选择用户：<strong>${username}</strong> (ID: ${userId})
                <br><small class="text-muted">验证码将实时推送到该用户的前台页面</small>
            `;
            document.getElementById('selectedUserDiv').style.display = 'block';
            
            if (phone) {
                document.getElementById('phone').value = phone;
            }
            
            // 清空搜索结果
            document.getElementById('searchResults').innerHTML = '';
            document.getElementById('userSearch').value = '';
        }

        // 发送验证码
        document.getElementById('sendCodeForm').addEventListener('submit', async function(e) {
            e.preventDefault();

            const userId = document.getElementById('selectedUserId').value;
            const phone = document.getElementById('phone').value;
            const type = document.getElementById('codeType').value;
            const note = document.getElementById('note').value;
            const expiry = document.getElementById('expiry').value;

            if (!userId) {
                showAlert('请先选择用户', 'warning');
                return;
            }

            if (!phone || !type || !note) {
                showAlert('请填写完整信息', 'warning');
                return;
            }

            const submitBtn = e.target.querySelector('button[type="submit"]');
            const originalText = submitBtn.innerHTML;
            submitBtn.innerHTML = '<i class="bi bi-hourglass-split"></i> 发送中...';
            submitBtn.disabled = true;

            try {
                const response = await fetch('send_verification_code.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        user_id: parseInt(userId),
                        phone: phone,
                        type: type,
                        note: note,
                        expiry: parseInt(expiry)
                    })
                });

                const data = await response.json();
                
                if (data.success) {
                    showAlert(`验证码发送成功！验证码：${data.data.code}，有效期：${expiry}分钟`, 'success');
                    
                    // 重置表单
                    document.getElementById('sendCodeForm').reset();
                    document.getElementById('selectedUserDiv').style.display = 'none';
                    
                    // 刷新页面显示最新记录
                    setTimeout(() => {
                        location.reload();
                    }, 3000);
                } else {
                    showAlert('发送失败：' + data.message, 'danger');
                }
            } catch (error) {
                showAlert('发送失败：' + error.message, 'danger');
            } finally {
                submitBtn.innerHTML = originalText;
                submitBtn.disabled = false;
            }
        });

        // 显示提示信息
        function showAlert(message, type) {
            const alertDiv = document.createElement('div');
            alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
            alertDiv.innerHTML = `
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;
            
            const container = document.querySelector('.main-content');
            container.insertBefore(alertDiv, container.firstChild);
            
            // 自动关闭
            setTimeout(() => {
                if (alertDiv.parentNode) {
                    alertDiv.remove();
                }
            }, 5000);
        }

        // 回车搜索
        document.getElementById('userSearch').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                searchUsers();
            }
        });

        // 页面加载完成
        document.addEventListener('DOMContentLoaded', function() {
            console.log('验证码发送页面加载完成');
            
            // 3秒后隐藏通知状态
            setTimeout(() => {
                const statusDiv = document.getElementById('notificationStatus');
                if (statusDiv) {
                    statusDiv.style.display = 'none';
                }
            }, 3000);
        });
    </script>
</body>
</html>
