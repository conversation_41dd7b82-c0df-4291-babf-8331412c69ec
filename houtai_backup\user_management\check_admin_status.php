<?php
/**
 * 检查管理员登录状态
 */

session_start();

echo "=== 管理员登录状态检查 ===\n\n";

echo "Session ID: " . session_id() . "\n";
echo "Session 数据:\n";
print_r($_SESSION);

echo "\n=== 检查结果 ===\n";

if (isset($_SESSION['admin_id'])) {
    echo "✅ 管理员已登录\n";
    echo "管理员ID: " . $_SESSION['admin_id'] . "\n";
    if (isset($_SESSION['admin_name'])) {
        echo "管理员姓名: " . $_SESSION['admin_name'] . "\n";
    }
    if (isset($_SESSION['employee_id'])) {
        echo "员工ID: " . $_SESSION['employee_id'] . "\n";
    }
} else {
    echo "❌ 管理员未登录\n";
    echo "需要先登录管理员账户\n";
}

echo "\n=== Cookie 信息 ===\n";
if (!empty($_COOKIE)) {
    foreach ($_COOKIE as $key => $value) {
        echo "$key: $value\n";
    }
} else {
    echo "没有Cookie信息\n";
}

echo "\n=== 服务器信息 ===\n";
echo "PHP版本: " . PHP_VERSION . "\n";
echo "当前时间: " . date('Y-m-d H:i:s') . "\n";
echo "时区: " . date_default_timezone_get() . "\n";
?>
