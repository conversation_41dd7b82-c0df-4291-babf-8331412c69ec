/**
 * WebSocket风格的实时通知系统
 * 使用轮询模拟WebSocket功能，更稳定可靠
 */

class WebSocketNotifications {
    constructor(userId) {
        this.userId = userId;
        this.isConnected = false;
        this.pollInterval = null;
        this.heartbeatInterval = null;
        this.pollFrequency = 2000; // 2秒轮询一次
        this.heartbeatFrequency = 30000; // 30秒心跳一次
        this.lastCheck = null;
        this.reconnectAttempts = 0;
        this.maxReconnectAttempts = 5;

        this.init();
    }

    init() {
        this.createNotificationContainer();
        console.log('WebSocket通知系统初始化完成，用户ID:', this.userId);
    }

    createNotificationContainer() {
        if (!document.getElementById('notification-container')) {
            const container = document.createElement('div');
            container.id = 'notification-container';
            container.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                z-index: 10000;
                pointer-events: none;
            `;
            document.body.appendChild(container);
        }
    }

    connect() {
        if (this.isConnected) {
            console.log('WebSocket通知已连接');
            return;
        }

        console.log('开始连接WebSocket通知服务...');
        this.isConnected = true;
        this.reconnectAttempts = 0;

        // 开始轮询
        this.startPolling();

        // 开始心跳
        this.startHeartbeat();

        // 发送连接确认
        this.showToastNotification('系统提示', 'WebSocket通知服务已连接', 'success');
    }

    disconnect() {
        console.log('断开WebSocket通知连接...');
        this.isConnected = false;

        // 停止轮询
        if (this.pollInterval) {
            clearInterval(this.pollInterval);
            this.pollInterval = null;
        }

        // 停止心跳
        if (this.heartbeatInterval) {
            clearInterval(this.heartbeatInterval);
            this.heartbeatInterval = null;
        }

        // 通知服务器断开连接
        this.sendRequest('disconnect');
    }

    startPolling() {
        this.pollInterval = setInterval(() => {
            this.pollNotifications();
        }, this.pollFrequency);

        // 立即执行一次
        this.pollNotifications();
    }

    startHeartbeat() {
        this.heartbeatInterval = setInterval(() => {
            this.sendHeartbeat();
        }, this.heartbeatFrequency);

        // 立即发送一次心跳
        this.sendHeartbeat();
    }

    async pollNotifications() {
        if (!this.isConnected) return;

        try {
            const params = new URLSearchParams({
                action: 'poll',
                user_id: this.userId
            });

            const response = await fetch(`api/websocket_server.php?${params}`);
            const data = await response.json();

            if (data.success) {
                this.lastCheck = data.timestamp;

                // 添加调试日志
                if (data.debug) {
                    console.log('轮询调试信息:', data.debug);
                }

                if (data.data.notifications && data.data.notifications.length > 0) {
                    console.log(`收到 ${data.data.count} 条新通知`);
                    data.data.notifications.forEach(notification => {
                        console.log('处理通知:', notification);
                        this.handleNotification(notification);
                    });
                } else {
                    console.log('轮询完成，无新通知');
                }

                // 重置重连计数
                this.reconnectAttempts = 0;
            } else {
                console.error('轮询通知失败:', data.error);
                this.handleConnectionError();
            }

        } catch (error) {
            console.error('轮询请求失败:', error);
            this.handleConnectionError();
        }
    }

    async sendHeartbeat() {
        if (!this.isConnected) return;

        try {
            const response = await fetch(`api/websocket_server.php?action=heartbeat&user_id=${this.userId}`);
            const data = await response.json();

            if (data.success) {
                console.log('心跳发送成功');
            }
        } catch (error) {
            console.error('心跳发送失败:', error);
        }
    }

    async sendRequest(action, params = {}) {
        try {
            const urlParams = new URLSearchParams({
                action: action,
                user_id: this.userId,
                ...params
            });

            const response = await fetch(`api/websocket_server.php?${urlParams}`);
            return await response.json();
        } catch (error) {
            console.error(`请求失败 (${action}):`, error);
            return { success: false, error: error.message };
        }
    }

    handleNotification(notification) {
        console.log('处理通知:', notification);

        switch (notification.type) {
            case 'verification_code':
                this.showVerificationCodeModal(notification);
                break;

            case 'system_message':
                this.showToastNotification(notification.title, notification.content, 'info');
                break;

            case 'admin_notice':
                this.showToastNotification(notification.title, notification.content, 'warning');
                break;

            default:
                this.showToastNotification(notification.title || '系统通知', notification.content, 'info');
        }

        // 标记为已读
        this.markAsRead(notification.id);
    }

    async markAsRead(notificationId) {
        try {
            await this.sendRequest('mark_read', { notification_id: notificationId });
        } catch (error) {
            console.error('标记已读失败:', error);
        }
    }

    showVerificationCodeModal(notification) {
        try {
            const data = typeof notification.data === 'string' ?
                JSON.parse(notification.data) : notification.data;

            const modal = this.createVerificationModal({
                title: notification.title,
                data: data
            });

            document.body.appendChild(modal);

            // 显示弹窗
            setTimeout(() => {
                modal.style.display = 'flex';
                modal.classList.add('show');
            }, 100);

            // 播放提示音
            this.playNotificationSound();

            // 自动关闭定时器
            setTimeout(() => {
                if (document.body.contains(modal)) {
                    this.closeModal(modal);
                }
            }, 60000);

        } catch (error) {
            console.error('显示验证码弹窗失败:', error);
            this.showToastNotification('验证码通知', notification.content, 'info');
        }
    }

    createVerificationModal(data) {
        const modal = document.createElement('div');
        modal.className = 'verification-modal';
        modal.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.6);
            z-index: 10001;
            display: none;
            align-items: center;
            justify-content: center;
            padding: 20px;
            backdrop-filter: blur(5px);
            opacity: 0;
            transition: opacity 0.3s ease;
        `;

        const code = data.data?.code || '******';
        const expiresAt = data.data?.expires_at || '';
        const adminNote = data.data?.admin_note || '';
        const sentBy = data.data?.sent_by || '管理员';

        // 计算剩余时间
        const expiryTime = new Date(expiresAt);
        const now = new Date();
        const remainingMinutes = Math.max(0, Math.ceil((expiryTime - now) / 60000));

        modal.innerHTML = `
            <div class="verification-content" style="
                background: white;
                border-radius: 16px;
                padding: 32px 24px;
                max-width: 400px;
                width: 100%;
                box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
                animation: modalSlideIn 0.3s ease;
                text-align: center;
            ">
                <div class="verification-header" style="margin-bottom: 24px;">
                    <div style="
                        width: 60px;
                        height: 60px;
                        background: linear-gradient(135deg, #6F7BF5 0%, #9B59B6 100%);
                        border-radius: 50%;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        margin: 0 auto 16px;
                        color: white;
                        font-size: 24px;
                    ">🔑</div>
                    <h3 style="
                        font-size: 20px;
                        font-weight: 700;
                        color: #2D3748;
                        margin: 0 0 8px 0;
                    ">${data.title}</h3>
                    <p style="
                        color: #718096;
                        font-size: 14px;
                        margin: 0;
                    ">来自 ${sentBy}</p>
                </div>

                <div class="verification-code-display" style="
                    background: linear-gradient(135deg, #6F7BF5 0%, #9B59B6 100%);
                    color: white;
                    padding: 20px;
                    border-radius: 12px;
                    font-size: 32px;
                    font-weight: 700;
                    letter-spacing: 8px;
                    font-family: 'Courier New', monospace;
                    margin: 24px 0;
                    box-shadow: 0 8px 16px rgba(111, 123, 245, 0.3);
                    cursor: pointer;
                    user-select: all;
                " onclick="this.select(); document.execCommand('copy'); alert('验证码已复制到剪贴板');">${code}</div>

                <div class="verification-info" style="
                    background: #F7FAFC;
                    border-radius: 8px;
                    padding: 16px;
                    margin: 20px 0;
                    text-align: left;
                ">
                    <div style="
                        display: flex;
                        align-items: center;
                        gap: 8px;
                        margin-bottom: 8px;
                        color: #4A5568;
                        font-size: 14px;
                    ">
                        <span>⏰</span>
                        <span>有效期：${remainingMinutes} 分钟</span>
                    </div>
                    ${adminNote ? `
                    <div style="
                        display: flex;
                        align-items: flex-start;
                        gap: 8px;
                        color: #4A5568;
                        font-size: 14px;
                        line-height: 1.4;
                    ">
                        <span>📝</span>
                        <span>备注：${adminNote}</span>
                    </div>
                    ` : ''}
                </div>

                <button class="close-btn" style="
                    width: 100%;
                    padding: 12px 16px;
                    background: linear-gradient(135deg, #6F7BF5 0%, #9B59B6 100%);
                    color: white;
                    border: none;
                    border-radius: 8px;
                    font-size: 14px;
                    font-weight: 600;
                    cursor: pointer;
                    transition: all 0.2s ease;
                " onclick="this.closest('.verification-modal').remove();">
                    我知道了
                </button>
            </div>
        `;

        // 添加样式
        if (!document.getElementById('modal-styles')) {
            const style = document.createElement('style');
            style.id = 'modal-styles';
            style.textContent = `
                @keyframes modalSlideIn {
                    from {
                        opacity: 0;
                        transform: translateY(20px) scale(0.95);
                    }
                    to {
                        opacity: 1;
                        transform: translateY(0) scale(1);
                    }
                }

                .verification-modal.show {
                    opacity: 1;
                }

                .close-btn:hover {
                    transform: translateY(-1px);
                    box-shadow: 0 4px 12px rgba(111, 123, 245, 0.4);
                }
            `;
            document.head.appendChild(style);
        }

        return modal;
    }

    closeModal(modal) {
        modal.style.opacity = '0';
        setTimeout(() => {
            if (document.body.contains(modal)) {
                modal.remove();
            }
        }, 300);
    }

    showToastNotification(title, message, type = 'info') {
        const container = document.getElementById('notification-container');
        const toast = document.createElement('div');

        const colors = {
            info: { bg: '#3182CE', icon: 'ℹ️' },
            warning: { bg: '#D69E2E', icon: '⚠️' },
            error: { bg: '#E53E3E', icon: '❌' },
            success: { bg: '#38A169', icon: '✅' }
        };

        const color = colors[type] || colors.info;

        toast.style.cssText = `
            background: ${color.bg};
            color: white;
            padding: 16px 20px;
            border-radius: 8px;
            margin-bottom: 12px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            pointer-events: auto;
            cursor: pointer;
            transition: all 0.3s ease;
            transform: translateX(100%);
            opacity: 0;
            max-width: 350px;
        `;

        toast.innerHTML = `
            <div style="display: flex; align-items: flex-start; gap: 12px;">
                <span style="font-size: 18px;">${color.icon}</span>
                <div style="flex: 1;">
                    <div style="font-weight: 600; margin-bottom: 4px;">${title}</div>
                    <div style="font-size: 14px; opacity: 0.9; line-height: 1.4;">${message}</div>
                </div>
            </div>
        `;

        container.appendChild(toast);

        // 显示动画
        setTimeout(() => {
            toast.style.transform = 'translateX(0)';
            toast.style.opacity = '1';
        }, 100);

        // 点击关闭
        toast.addEventListener('click', () => {
            this.removeToast(toast);
        });

        // 自动关闭
        setTimeout(() => {
            this.removeToast(toast);
        }, 5000);
    }

    removeToast(toast) {
        if (toast.parentNode) {
            toast.style.transform = 'translateX(100%)';
            toast.style.opacity = '0';
            setTimeout(() => {
                if (toast.parentNode) {
                    toast.parentNode.removeChild(toast);
                }
            }, 300);
        }
    }

    playNotificationSound() {
        try {
            const audioContext = new (window.AudioContext || window.webkitAudioContext)();
            const oscillator = audioContext.createOscillator();
            const gainNode = audioContext.createGain();

            oscillator.connect(gainNode);
            gainNode.connect(audioContext.destination);

            oscillator.frequency.setValueAtTime(800, audioContext.currentTime);
            oscillator.frequency.setValueAtTime(1000, audioContext.currentTime + 0.1);

            gainNode.gain.setValueAtTime(0.1, audioContext.currentTime);
            gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.2);

            oscillator.start(audioContext.currentTime);
            oscillator.stop(audioContext.currentTime + 0.2);
        } catch (error) {
            console.log('无法播放提示音:', error);
        }
    }

    handleConnectionError() {
        if (this.reconnectAttempts >= this.maxReconnectAttempts) {
            console.error('达到最大重连次数，停止重连');
            this.disconnect();
            this.showToastNotification('连接错误', 'WebSocket通知服务连接失败，请刷新页面', 'error');
            return;
        }

        this.reconnectAttempts++;
        console.log(`连接错误，${this.reconnectAttempts}/${this.maxReconnectAttempts} 次重试`);

        // 短暂延迟后继续轮询
        setTimeout(() => {
            if (this.isConnected) {
                this.pollNotifications();
            }
        }, 5000);
    }
}

// 自动初始化
document.addEventListener('DOMContentLoaded', function() {
    const userId = window.currentUserId ||
                   document.querySelector('meta[name="user-id"]')?.content ||
                   localStorage.getItem('user_id') ||
                   sessionStorage.getItem('user_id');

    if (userId && userId !== '0') {
        console.log('初始化WebSocket通知系统，用户ID:', userId);
        window.webSocketNotifications = new WebSocketNotifications(userId);
    } else {
        console.log('未找到用户ID，跳过WebSocket通知初始化');
    }
});

// 导出类
window.WebSocketNotifications = WebSocketNotifications;
