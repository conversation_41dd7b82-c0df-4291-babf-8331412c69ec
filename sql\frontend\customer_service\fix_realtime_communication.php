<?php
// 修复前台实时通信
header('Content-Type: text/html; charset=UTF-8');
ini_set('default_charset', 'UTF-8');

session_start();

// 模拟用户登录（用于测试）
if (!isset($_SESSION['user_id'])) {
    $_SESSION['user_id'] = 4; // 默认用户ID
    $_SESSION['user_name'] = '测试用户';
}

require_once '../../houtai_backup/db_config.php';

echo '<h1>🔧 修复前台实时通信</h1>';

// 获取用户的会话
try {
    $pdo = getDbConnection();
    
    $stmt = $pdo->prepare("
        SELECT session_id, status, customer_service_id, started_at, updated_at
        FROM customer_service_sessions 
        WHERE user_id = ? 
        ORDER BY updated_at DESC 
        LIMIT 5
    ");
    $stmt->execute([$_SESSION['user_id']]);
    $sessions = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (!empty($sessions)) {
        echo '<h2>📋 您的客服会话</h2>';
        
        foreach ($sessions as $session) {
            $statusColor = $session['status'] === 'active' ? 'green' : ($session['status'] === 'waiting' ? 'orange' : 'gray');
            
            echo '<div style="border: 1px solid #ddd; padding: 15px; margin: 10px 0; border-radius: 8px; background: #f9f9f9;">';
            echo '<h4>会话: ' . htmlspecialchars($session['session_id']) . '</h4>';
            echo '<p><strong>状态:</strong> <span style="color: ' . $statusColor . ';">' . htmlspecialchars($session['status']) . '</span></p>';
            echo '<p><strong>客服ID:</strong> ' . htmlspecialchars($session['customer_service_id'] ?? '未分配') . '</p>';
            echo '<p><strong>开始时间:</strong> ' . htmlspecialchars($session['started_at']) . '</p>';
            echo '<p><strong>更新时间:</strong> ' . htmlspecialchars($session['updated_at']) . '</p>';
            
            if ($session['status'] === 'active') {
                echo '<a href="chat.php?session_id=' . urlencode($session['session_id']) . '" style="background: #6F7BF5; color: white; padding: 8px 16px; text-decoration: none; border-radius: 5px; margin-right: 10px;">打开聊天</a>';
                echo '<button onclick="testRealtime(\'' . $session['session_id'] . '\')" style="background: #28a745; color: white; padding: 8px 16px; border: none; border-radius: 5px; cursor: pointer;">测试实时通信</button>';
            }
            
            echo '</div>';
        }
    } else {
        echo '<div style="background: #fff3cd; padding: 15px; border-radius: 8px; margin: 10px 0;">';
        echo '<h3>⚠️ 没有找到客服会话</h3>';
        echo '<p>请先在前台发起客服对话，或者在客服后台创建测试会话</p>';
        echo '<a href="../../houtai_backup/customer_service_system/create_test_data.php" style="background: #007cba; color: white; padding: 8px 16px; text-decoration: none; border-radius: 5px;">创建测试会话</a>';
        echo '</div>';
    }
    
} catch (Exception $e) {
    echo '<div style="background: #f8d7da; padding: 15px; border-radius: 8px; margin: 10px 0; color: #721c24;">';
    echo '<h3>❌ 数据库错误</h3>';
    echo '<p>' . htmlspecialchars($e->getMessage()) . '</p>';
    echo '</div>';
}
?>

<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>修复前台实时通信</title>
    <style>
        body { 
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; 
            margin: 20px; 
            background: #f5f5f5; 
        }
        .container { 
            max-width: 900px; 
            margin: 0 auto; 
            background: white; 
            padding: 20px; 
            border-radius: 10px; 
            box-shadow: 0 2px 10px rgba(0,0,0,0.1); 
        }
        .result { 
            margin: 10px 0; 
            padding: 15px; 
            border-radius: 8px; 
            border-left: 4px solid #28a745; 
        }
        .success { background: #d4edda; color: #155724; border-left-color: #28a745; }
        .error { background: #f8d7da; color: #721c24; border-left-color: #dc3545; }
        .info { background: #d1ecf1; color: #0c5460; border-left-color: #17a2b8; }
        .warning { background: #fff3cd; color: #856404; border-left-color: #ffc107; }
        pre { 
            background: #f8f9fa; 
            padding: 10px; 
            border-radius: 5px; 
            overflow-x: auto; 
            font-size: 12px; 
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="result info">
            <h3>🔍 问题分析</h3>
            <p><strong>现状：</strong>客服后台可以发送消息，但前台用户收不到</p>
            <p><strong>原因：</strong>前台聊天页面缺少实时轮询机制</p>
            <p><strong>解决方案：</strong></p>
            <ul>
                <li>修复前台聊天页面的轮询功能</li>
                <li>确保API路径正确</li>
                <li>添加错误处理和重连机制</li>
            </ul>
        </div>
        
        <div class="result warning">
            <h3>⚠️ 需要修复的问题</h3>
            <ol>
                <li><strong>数据库字段长度：</strong>先访问 <a href="../../houtai_backup/customer_service_system/fix_field_length.php" target="_blank">修复字段长度</a></li>
                <li><strong>前台轮询：</strong>修复前台聊天页面的消息轮询</li>
                <li><strong>API路径：</strong>确保前台API能正确访问后台数据库</li>
            </ol>
        </div>
        
        <div id="test-result"></div>
        
        <p style="margin-top: 20px;">
            <a href="index.php">返回客服首页</a> | 
            <a href="../../houtai_backup/customer_service_system/sessions.php">客服后台</a> | 
            <a href="../../houtai_backup/customer_service_system/fix_field_length.php">修复字段长度</a>
        </p>
    </div>
    
    <script>
        async function testRealtime(sessionId) {
            const resultDiv = document.getElementById('test-result');
            resultDiv.innerHTML = '<div class="result info">🔄 测试实时通信功能...</div>';
            
            try {
                // 测试获取消息API
                const response = await fetch(`api/get_new_messages.php?session_id=${sessionId}&last_check=0`);
                const data = await response.json();
                
                if (data.success) {
                    resultDiv.innerHTML = `
                        <div class="result success">
                            <h3>✅ 实时通信API测试成功</h3>
                            <p><strong>会话ID：</strong>${sessionId}</p>
                            <p><strong>消息数量：</strong>${data.count}</p>
                            <p><strong>时间戳：</strong>${data.timestamp}</p>
                            <pre>${JSON.stringify(data, null, 2)}</pre>
                            <p>
                                <a href="chat.php?session_id=${sessionId}" style="background: #6F7BF5; color: white; padding: 8px 16px; text-decoration: none; border-radius: 5px;">打开聊天页面</a>
                            </p>
                        </div>
                    `;
                } else {
                    resultDiv.innerHTML = `
                        <div class="result error">
                            <h3>❌ 实时通信API测试失败</h3>
                            <p><strong>错误：</strong>${data.error}</p>
                            <pre>${JSON.stringify(data, null, 2)}</pre>
                        </div>
                    `;
                }
                
            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="result error">
                        <h3>❌ 网络错误</h3>
                        <p>${error.message}</p>
                    </div>
                `;
            }
        }
    </script>
</body>
</html>
