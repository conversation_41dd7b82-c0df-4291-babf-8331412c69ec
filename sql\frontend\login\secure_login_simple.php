<?php
/**
 * 简化版安全登录API
 */

// 引入session配置
require_once '../../sql/session_config.php';
initLoginSession();
header('Content-Type: application/json; charset=utf-8');

// 错误处理
error_reporting(0);
ini_set('display_errors', 0);

// 捕获所有错误并返回JSON
set_error_handler(function($severity, $message, $file, $line) {
    echo json_encode(['success' => false, 'message' => '系统错误，请稍后重试']);
    exit;
});

set_exception_handler(function($exception) {
    echo json_encode(['success' => false, 'message' => '系统错误，请稍后重试']);
    exit;
});

// 数据库连接配置
$db_config = [
    'host' => 'localhost',
    'username' => 'quwanplanet',
    'password' => 'nJmJm23FB4Xn6Fc3',
    'dbname' => 'quwanplanet',
    'port' => 3306,
    'charset' => 'utf8mb4'
];

try {
    $pdo = new PDO(
        "mysql:host={$db_config['host']};port={$db_config['port']};dbname={$db_config['dbname']};charset={$db_config['charset']}",
        $db_config['username'],
        $db_config['password'],
        [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
            PDO::ATTR_EMULATE_PREPARES => false
        ]
    );
} catch (PDOException $e) {
    echo json_encode(['success' => false, 'message' => '数据库连接失败']);
    exit;
}

/**
 * 获取用户封号详细信息
 */
function getBanInfo($pdo, $user_id) {
    try {
        // 查询最新的封号记录，兼容不同的字段名
        $stmt = $pdo->prepare("
            SELECT reason, admin_name, created_at,
                   COALESCE(end_time, expire_at) as end_time
            FROM user_bans
            WHERE user_id = ? AND status = 'active'
            AND (COALESCE(end_time, expire_at) IS NULL OR COALESCE(end_time, expire_at) > NOW())
            ORDER BY created_at DESC
            LIMIT 1
        ");
        $stmt->execute([$user_id]);
        $banRecord = $stmt->fetch(PDO::FETCH_ASSOC);

        if ($banRecord) {
            $endTime = $banRecord['end_time'];
            $isPermanent = empty($endTime) || $endTime === '0000-00-00 00:00:00';

            return [
                'reason' => $banRecord['reason'] ?: '违反平台规定',
                'admin_name' => $banRecord['admin_name'] ?: '系统管理员',
                'ban_time' => $banRecord['created_at'], // 封号时间（固定的时间点）
                'end_time' => $isPermanent ? null : $endTime, // 到期时间
                'is_permanent' => $isPermanent
            ];
        }
    } catch (PDOException $e) {
        // 如果user_bans表不存在，返回默认信息
        error_log("获取封号信息失败: " . $e->getMessage());
    }

    // 默认封号信息
    return [
        'reason' => '违反平台规定',
        'admin_name' => '系统管理员',
        'ban_time' => date('Y-m-d H:i:s'),
        'end_time' => null,
        'is_permanent' => true
    ];
}

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode(['success' => false, 'message' => '请求方法错误']);
    exit;
}

$input = json_decode(file_get_contents('php://input'), true);
$user_id = intval($input['user_id'] ?? 0);
$verification_code = trim($input['verification_code'] ?? '');
$password = trim($input['password'] ?? '');

if (!$user_id) {
    echo json_encode(['success' => false, 'message' => '用户ID无效']);
    exit;
}

if (empty($verification_code)) {
    echo json_encode(['success' => false, 'message' => '请输入验证码']);
    exit;
}

if (empty($password)) {
    echo json_encode(['success' => false, 'message' => '请输入密码']);
    exit;
}

try {
    // 验证用户存在且状态正常
    $stmt = $pdo->prepare("SELECT id, username, quwan_id, password, status FROM users WHERE id = ?");
    $stmt->execute([$user_id]);
    $user = $stmt->fetch();

    if (!$user) {
        echo json_encode(['success' => false, 'message' => '用户不存在']);
        exit;
    }

    if ($user['status'] === 'banned') {
        // 获取封号详细信息
        $banInfo = getBanInfo($pdo, $user['id']);

        echo json_encode([
            'success' => false,
            'message' => '账户已被封禁',
            'banned' => true,
            'banInfo' => $banInfo
        ]);
        exit;
    }

    // 验证密码
    if (!password_verify($password, $user['password'])) {
        echo json_encode(['success' => false, 'message' => '密码错误']);
        exit;
    }

    // 验证验证码（从session中获取）
    $session_code = $_SESSION['verification_code'] ?? '';
    $session_phone = $_SESSION['verification_phone'] ?? '';
    $session_time = $_SESSION['verification_time'] ?? 0;

    // 检查验证码是否过期（5分钟有效期）
    if (time() - $session_time > 300) {
        echo json_encode(['success' => false, 'message' => '验证码已过期，请重新获取']);
        exit;
    }

    // 验证手机号是否匹配（通过用户ID获取手机号进行验证）
    $stmt = $pdo->prepare("SELECT phone FROM users WHERE id = ?");
    $stmt->execute([$user_id]);
    $user_phone = $stmt->fetchColumn();

    if ($user_phone !== $session_phone) {
        echo json_encode(['success' => false, 'message' => '验证码与当前用户不匹配']);
        exit;
    }

    if ($verification_code !== $session_code) {
        echo json_encode(['success' => false, 'message' => '验证码错误']);
        exit;
    }

    // 清除验证码
    unset($_SESSION['verification_code']);
    unset($_SESSION['verification_phone']);
    unset($_SESSION['verification_time']);
    unset($_SESSION['verification_user']);

    // 设置登录会话
    setUserLoginSession($user);

    // 更新最后登录时间
    $stmt = $pdo->prepare("UPDATE users SET last_login = NOW() WHERE id = ?");
    $stmt->execute([$user_id]);

    // 记录登录日志（如果表存在）
    try {
        $stmt = $pdo->prepare("INSERT INTO login_logs (user_id, login_time, ip_address, user_agent, status, login_type) VALUES (?, NOW(), ?, ?, 'success', 'secure_login')");
        $stmt->execute([
            $user_id,
            $_SERVER['REMOTE_ADDR'],
            $_SERVER['HTTP_USER_AGENT']
        ]);
    } catch (PDOException $e) {
        // 忽略日志记录错误
    }

    echo json_encode([
        'success' => true,
        'message' => '登录成功',
        'redirect' => '../home/<USER>'
    ]);

} catch (PDOException $e) {
    echo json_encode(['success' => false, 'message' => '登录失败，请稍后重试']);
}
?>
