-- =====================================================
-- 分步执行：修复优惠券表
-- 请逐条执行以下SQL语句
-- =====================================================

-- 第一步：检查表结构
DESCRIBE camping_coupons;

-- 第二步：添加 used_quantity 字段（如果报错"字段已存在"请跳过）
ALTER TABLE camping_coupons ADD COLUMN used_quantity INT(11) DEFAULT 0;

-- 第三步：初始化数据
UPDATE camping_coupons cc
SET used_quantity = (
    SELECT COUNT(*)
    FROM user_camping_coupons ucc
    WHERE ucc.coupon_id = cc.id AND ucc.status = 'used'
);

-- 第四步：验证结果
SELECT 
    id,
    title,
    total_quantity,
    claimed_quantity,
    used_quantity
FROM camping_coupons
LIMIT 5;

-- 第五步：检查统计数据
SELECT 
    COUNT(*) as total_coupons,
    SUM(total_quantity) as total_issued,
    SUM(claimed_quantity) as total_claimed,
    SUM(used_quantity) as total_used
FROM camping_coupons;
