/**
 * 每日签到页面交互脚本
 */

// 隐藏成功弹窗
function hideSuccessModal() {
    const modal = document.getElementById('successModal');
    if (modal) {
        modal.style.animation = 'fadeOut 0.3s ease';
        setTimeout(() => {
            modal.remove();
        }, 300);
    }
}

// 添加fadeOut动画
const style = document.createElement('style');
style.textContent = `
    @keyframes fadeOut {
        from { opacity: 1; }
        to { opacity: 0; }
    }
`;
document.head.appendChild(style);

// Toast提示函数
function showToast(message, duration = 3000) {
    // 移除已存在的toast
    const existingToast = document.querySelector('.toast');
    if (existingToast) {
        existingToast.remove();
    }

    // 创建新的toast
    const toast = document.createElement('div');
    toast.className = 'toast';
    toast.textContent = message;

    // 添加样式
    toast.style.cssText = `
        position: fixed;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        background: var(--bg-card);
        color: var(--text-primary);
        padding: 16px 24px;
        border-radius: 12px;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
        z-index: 10000;
        font-size: 14px;
        font-weight: 500;
        max-width: 90%;
        text-align: center;
        border: 1px solid var(--border-color);
        opacity: 0;
        transition: opacity 0.3s ease;
    `;

    document.body.appendChild(toast);

    // 显示动画
    setTimeout(() => {
        toast.style.opacity = '1';
    }, 10);

    // 自动隐藏
    setTimeout(() => {
        toast.style.opacity = '0';
        setTimeout(() => {
            if (toast.parentNode) {
                toast.parentNode.removeChild(toast);
            }
        }, 300);
    }, duration);
}

// 签到按钮点击效果
function initCheckinButton() {
    const checkinBtn = document.querySelector('.checkin-btn:not(.checked)');
    if (checkinBtn) {
        checkinBtn.addEventListener('click', function(e) {
            // 添加点击动画
            this.style.transform = 'scale(0.95)';
            setTimeout(() => {
                this.style.transform = '';
            }, 150);

            // 添加加载状态
            const originalContent = this.innerHTML;
            this.innerHTML = '<i class="fas fa-spinner fa-spin"></i><span>签到中...</span>';
            this.disabled = true;

            // 表单提交后页面会刷新，不需要恢复按钮状态
        });
    }
}

// 日历天数点击效果
function initCalendarDays() {
    const calendarDays = document.querySelectorAll('.calendar-day:not(.empty):not(.future)');

    calendarDays.forEach(day => {
        day.addEventListener('click', function() {
            if (this.classList.contains('checked')) {
                showToast('这一天已经签到过了');
            } else if (this.classList.contains('today')) {
                showToast('今天还没有签到，快去签到吧！');
            } else {
                showToast('这一天还没有到来');
            }

            // 添加点击动画
            this.style.transform = 'scale(0.9)';
            setTimeout(() => {
                this.style.transform = '';
            }, 150);
        });
    });
}

// 奖励规则项点击效果
function initRuleItems() {
    const ruleItems = document.querySelectorAll('.rule-item');

    ruleItems.forEach(item => {
        item.addEventListener('click', function() {
            const title = this.querySelector('.rule-title').textContent;
            const desc = this.querySelector('.rule-desc').textContent;
            showToast(`${title}: ${desc}`);

            // 添加点击动画
            this.style.transform = 'scale(0.98)';
            setTimeout(() => {
                this.style.transform = '';
            }, 150);
        });
    });
}

// 用户统计项点击效果
function initStatItems() {
    const statItems = document.querySelectorAll('.stat-item');

    statItems.forEach(item => {
        item.addEventListener('click', function() {
            const label = this.querySelector('.stat-label').textContent;
            const value = this.querySelector('.stat-value').textContent;

            if (label === '连续签到') {
                showToast(`您已连续签到${value}天，继续保持！`);
            } else if (label === '总积分') {
                showToast(`您的总积分为${value}，可以用于兑换奖品`);
            }

            // 添加点击动画
            this.style.transform = 'scale(0.95)';
            setTimeout(() => {
                this.style.transform = '';
            }, 150);
        });
    });
}

// 返回按钮功能
function initBackButton() {
    const backBtn = document.querySelector('.back-btn');
    if (backBtn) {
        backBtn.addEventListener('click', function(e) {
            e.preventDefault();

            // 添加点击动画
            this.style.transform = 'scale(0.9)';
            setTimeout(() => {
                this.style.transform = '';
                // 返回上一页
                window.history.back();
            }, 150);
        });
    }
}

// 历史按钮功能
function initHistoryButton() {
    const historyBtn = document.querySelector('.history-btn');
    if (historyBtn) {
        historyBtn.addEventListener('click', function(e) {
            e.preventDefault();
            showToast('签到历史功能即将上线');
        });
    }
}

// 签到卡片悬停效果
function initCheckinCard() {
    const checkinCard = document.querySelector('.checkin-card');
    if (checkinCard) {
        checkinCard.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-2px)';
            this.style.boxShadow = '0 8px 32px rgba(111, 123, 245, 0.15)';
        });

        checkinCard.addEventListener('mouseleave', function() {
            this.style.transform = '';
            this.style.boxShadow = '';
        });
    }
}

// 用户信息卡片悬停效果
function initUserInfoCard() {
    const userInfoCard = document.querySelector('.user-info-card');
    if (userInfoCard) {
        userInfoCard.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-2px)';
        });

        userInfoCard.addEventListener('mouseleave', function() {
            this.style.transform = '';
        });
    }
}

// 自动隐藏成功弹窗
function autoHideSuccessModal() {
    const successModal = document.getElementById('successModal');
    if (successModal) {
        // 3秒后自动隐藏
        setTimeout(() => {
            hideSuccessModal();
        }, 3000);

        // 点击弹窗外部关闭
        successModal.addEventListener('click', function(e) {
            if (e.target === this) {
                hideSuccessModal();
            }
        });
    }
}

// 签到图标动画
function initCheckinIconAnimation() {
    const checkinIcon = document.querySelector('.checkin-icon');
    if (checkinIcon) {
        // 鼠标悬停时停止动画
        checkinIcon.addEventListener('mouseenter', function() {
            this.style.animationPlayState = 'paused';
        });

        checkinIcon.addEventListener('mouseleave', function() {
            this.style.animationPlayState = 'running';
        });
    }
}

// 页面加载动画
function initPageLoadAnimation() {
    const elements = document.querySelectorAll('.user-info-card, .checkin-main, .checkin-calendar, .reward-rules');

    elements.forEach((element, index) => {
        element.style.opacity = '0';
        element.style.transform = 'translateY(20px)';

        setTimeout(() => {
            element.style.transition = 'all 0.6s ease';
            element.style.opacity = '1';
            element.style.transform = 'translateY(0)';
        }, index * 100);
    });
}

// 滚动效果
function initScrollEffects() {
    let ticking = false;

    function updateScrollEffects() {
        const scrollY = window.scrollY;
        const header = document.querySelector('.checkin-header');

        if (header) {
            if (scrollY > 50) {
                header.style.boxShadow = '0 2px 20px rgba(0, 0, 0, 0.1)';
            } else {
                header.style.boxShadow = '';
            }
        }

        ticking = false;
    }

    window.addEventListener('scroll', function() {
        if (!ticking) {
            requestAnimationFrame(updateScrollEffects);
            ticking = true;
        }
    });
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    console.log('每日签到页面加载完成');

    // 初始化各种交互功能
    initCheckinButton();
    initCalendarDays();
    initRuleItems();
    initStatItems();
    initBackButton();
    initHistoryButton();
    initCheckinCard();
    initUserInfoCard();
    initCheckinIconAnimation();
    initScrollEffects();

    // 页面加载动画
    initPageLoadAnimation();

    // 自动隐藏成功弹窗
    autoHideSuccessModal();

    console.log('每日签到交互功能初始化完成');
});

// 键盘事件处理
document.addEventListener('keydown', function(e) {
    // ESC键关闭弹窗
    if (e.key === 'Escape') {
        hideSuccessModal();
    }

    // 空格键签到（如果还没签到）
    if (e.key === ' ' || e.code === 'Space') {
        const checkinBtn = document.querySelector('.checkin-btn:not(.checked)');
        if (checkinBtn && document.activeElement !== checkinBtn) {
            e.preventDefault();
            checkinBtn.click();
        }
    }
});

// 导出函数供全局使用
window.checkin = {
    hideSuccessModal,
    showToast
};
