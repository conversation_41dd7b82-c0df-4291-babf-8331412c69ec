<?php
// 测试消息流程
header('Content-Type: text/html; charset=UTF-8');
ini_set('default_charset', 'UTF-8');

session_start();

// 检查登录状态
if (!isset($_SESSION['cs_logged_in']) || !$_SESSION['cs_logged_in']) {
    echo '<h1>请先登录客服系统</h1>';
    exit;
}

require_once '../db_config.php';

echo '<h1>🔍 测试消息流程</h1>';

// 如果有POST请求，发送测试消息
if ($_POST['send_test'] ?? false) {
    $sessionId = $_POST['session_id'] ?? '';
    $testMessage = $_POST['test_message'] ?? '这是一条测试消息';
    
    if ($sessionId) {
        echo '<h2>📤 发送测试消息</h2>';
        echo '<p>会话ID: ' . htmlspecialchars($sessionId) . '</p>';
        echo '<p>消息内容: ' . htmlspecialchars($testMessage) . '</p>';
        
        try {
            $pdo = getDbConnection();
            
            // 获取会话信息
            $stmt = $pdo->prepare("SELECT * FROM customer_service_sessions WHERE session_id = ?");
            $stmt->execute([$sessionId]);
            $session = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if ($session) {
                echo '<p>用户ID: ' . ($session['user_id'] ?? 'N/A') . '</p>';
                echo '<p>会话状态: ' . $session['status'] . '</p>';
                
                // 插入消息
                $stmt = $pdo->prepare("
                    INSERT INTO customer_service_messages
                    (session_id, sender_type, sender_id, sender_name, message_type, content, created_at)
                    VALUES (?, 'customer_service', ?, ?, 'text', ?, NOW())
                ");
                $stmt->execute([
                    $sessionId,
                    $_SESSION['cs_user_id'],
                    $_SESSION['cs_name'],
                    $testMessage
                ]);
                
                $messageId = $pdo->lastInsertId();
                echo '<p style="color: green;">✅ 消息插入成功，ID: ' . $messageId . '</p>';
                
                // 如果有用户ID，插入通知
                if ($session['user_id']) {
                    $notificationData = [
                        'type' => 'customer_service_message',
                        'title' => '客服回复',
                        'message' => $testMessage,
                        'session_id' => $sessionId,
                        'cs_name' => $_SESSION['cs_name'],
                        'timestamp' => time()
                    ];
                    
                    // 检查通知表结构
                    $stmt = $pdo->query("SHOW COLUMNS FROM realtime_notifications LIKE 'message'");
                    $hasMessageColumn = $stmt->rowCount() > 0;
                    
                    if ($hasMessageColumn) {
                        $stmt = $pdo->prepare("
                            INSERT INTO realtime_notifications
                            (user_id, type, title, message, data, status, created_at)
                            VALUES (?, 'customer_service_message', '客服回复', ?, ?, 'unread', NOW())
                        ");
                        $stmt->execute([
                            $session['user_id'],
                            '您有新的客服回复消息',
                            json_encode($notificationData, JSON_UNESCAPED_UNICODE)
                        ]);
                    } else {
                        $stmt = $pdo->prepare("
                            INSERT INTO realtime_notifications
                            (user_id, type, title, data, status, created_at)
                            VALUES (?, 'customer_service_message', '客服回复', ?, 'unread', NOW())
                        ");
                        $stmt->execute([
                            $session['user_id'],
                            json_encode($notificationData, JSON_UNESCAPED_UNICODE)
                        ]);
                    }
                    
                    $notificationId = $pdo->lastInsertId();
                    echo '<p style="color: green;">✅ 通知插入成功，ID: ' . $notificationId . '</p>';
                } else {
                    echo '<p style="color: orange;">⚠️ 会话没有用户ID，跳过通知</p>';
                }
                
                // 更新会话
                $stmt = $pdo->prepare("
                    UPDATE customer_service_sessions
                    SET message_count = message_count + 1, updated_at = NOW()
                    WHERE session_id = ?
                ");
                $stmt->execute([$sessionId]);
                echo '<p style="color: green;">✅ 会话信息更新成功</p>';
                
            } else {
                echo '<p style="color: red;">❌ 会话不存在</p>';
            }
            
        } catch (Exception $e) {
            echo '<p style="color: red;">❌ 发送失败: ' . $e->getMessage() . '</p>';
        }
    }
}

try {
    $pdo = getDbConnection();
    
    // 获取进行中的会话
    $stmt = $pdo->query("
        SELECT session_id, user_name, user_id, customer_service_id, status, message_count
        FROM customer_service_sessions 
        WHERE status = 'active' 
        ORDER BY updated_at DESC 
        LIMIT 5
    ");
    $activeSessions = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (!empty($activeSessions)) {
        echo '<h2>📋 进行中的会话</h2>';
        
        foreach ($activeSessions as $session) {
            echo '<div style="border: 1px solid #ddd; padding: 15px; margin: 10px 0; border-radius: 8px; background: #f9f9f9;">';
            echo '<h4>会话: ' . htmlspecialchars($session['session_id']) . '</h4>';
            echo '<p><strong>用户：</strong>' . htmlspecialchars($session['user_name']) . '</p>';
            echo '<p><strong>用户ID：</strong>' . htmlspecialchars($session['user_id'] ?? 'N/A') . '</p>';
            echo '<p><strong>状态：</strong>' . htmlspecialchars($session['status']) . '</p>';
            echo '<p><strong>消息数量：</strong>' . htmlspecialchars($session['message_count']) . '</p>';
            
            // 显示最近的消息
            $stmt = $pdo->prepare("
                SELECT sender_type, sender_name, content, created_at 
                FROM customer_service_messages 
                WHERE session_id = ? 
                ORDER BY created_at DESC 
                LIMIT 3
            ");
            $stmt->execute([$session['session_id']]);
            $messages = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            if (!empty($messages)) {
                echo '<h5>最近消息:</h5>';
                echo '<div style="background: #f8f9fa; padding: 10px; border-radius: 5px; max-height: 150px; overflow-y: auto;">';
                foreach ($messages as $msg) {
                    $senderColor = $msg['sender_type'] === 'customer_service' ? '#007cba' : '#28a745';
                    echo '<div style="margin: 5px 0; padding: 5px; border-left: 3px solid ' . $senderColor . ';">';
                    echo '<small style="color: #666;">' . $msg['created_at'] . ' - ' . $msg['sender_name'] . '</small><br>';
                    echo htmlspecialchars($msg['content']);
                    echo '</div>';
                }
                echo '</div>';
            }
            
            // 发送测试消息表单
            echo '<form method="POST" style="margin-top: 10px; padding: 10px; background: #e7f3ff; border-radius: 5px;">';
            echo '<input type="hidden" name="session_id" value="' . htmlspecialchars($session['session_id']) . '">';
            echo '<input type="text" name="test_message" placeholder="输入测试消息" style="width: 300px; padding: 5px; margin-right: 10px;" value="测试消息 ' . date('H:i:s') . '">';
            echo '<button type="submit" name="send_test" value="1" style="padding: 5px 15px; background: #28a745; color: white; border: none; border-radius: 3px;">发送测试消息</button>';
            echo '</form>';
            
            echo '</div>';
        }
        
        // 检查通知表
        echo '<h2>📬 检查通知表</h2>';
        $stmt = $pdo->query("
            SELECT user_id, type, title, message, created_at 
            FROM realtime_notifications 
            WHERE type = 'customer_service_message' 
            ORDER BY created_at DESC 
            LIMIT 5
        ");
        $notifications = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        if (!empty($notifications)) {
            echo '<div style="background: #f8f9fa; padding: 15px; border-radius: 8px;">';
            echo '<h4>最近的通知记录:</h4>';
            foreach ($notifications as $notif) {
                echo '<div style="margin: 5px 0; padding: 8px; border: 1px solid #ddd; border-radius: 5px;">';
                echo '<strong>用户ID:</strong> ' . $notif['user_id'] . ' | ';
                echo '<strong>类型:</strong> ' . $notif['type'] . ' | ';
                echo '<strong>时间:</strong> ' . $notif['created_at'] . '<br>';
                echo '<strong>标题:</strong> ' . htmlspecialchars($notif['title']) . '<br>';
                if (isset($notif['message'])) {
                    echo '<strong>消息:</strong> ' . htmlspecialchars($notif['message']);
                }
                echo '</div>';
            }
            echo '</div>';
        } else {
            echo '<p style="color: orange;">没有找到通知记录</p>';
        }
        
    } else {
        echo '<div style="background: #fff3cd; padding: 15px; border-radius: 8px; margin: 10px 0;">';
        echo '<h3>⚠️ 没有进行中的会话</h3>';
        echo '<p>需要先接受一个会话</p>';
        echo '<a href="sessions.php" style="background: #007cba; color: white; padding: 8px 16px; text-decoration: none; border-radius: 5px;">返回会话列表</a>';
        echo '</div>';
    }
    
} catch (Exception $e) {
    echo '<div style="background: #f8d7da; padding: 15px; border-radius: 8px; margin: 10px 0; color: #721c24;">';
    echo '<h3>❌ 错误</h3>';
    echo '<p>' . htmlspecialchars($e->getMessage()) . '</p>';
    echo '</div>';
}
?>

<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>测试消息流程</title>
    <style>
        body { 
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; 
            margin: 20px; 
            background: #f5f5f5; 
        }
        .container { 
            max-width: 1000px; 
            margin: 0 auto; 
            background: white; 
            padding: 20px; 
            border-radius: 10px; 
            box-shadow: 0 2px 10px rgba(0,0,0,0.1); 
        }
    </style>
</head>
<body>
    <div class="container">
        <div style="background: #e7f3ff; padding: 15px; border-radius: 8px; margin: 10px 0;">
            <h3>📝 测试说明</h3>
            <ul>
                <li><strong>目的</strong>：测试完整的消息发送流程</li>
                <li><strong>检查</strong>：消息是否正确插入数据库</li>
                <li><strong>验证</strong>：通知是否正确发送给前台用户</li>
                <li><strong>分析</strong>：找出前台收不到消息的原因</li>
            </ul>
        </div>
        
        <div style="background: #fff3cd; padding: 15px; border-radius: 8px; margin: 10px 0;">
            <h3>🔍 可能的问题</h3>
            <ul>
                <li><strong>前台页面</strong>：没有正确的实时通信代码</li>
                <li><strong>轮询机制</strong>：前台没有定期检查新消息</li>
                <li><strong>用户ID</strong>：会话中的用户ID为空</li>
                <li><strong>通知系统</strong>：前台没有监听通知</li>
            </ul>
        </div>
        
        <p>
            <a href="sessions.php">返回会话列表</a> | 
            <a href="session_detail.php">会话详情</a> | 
            <a href="test_message_send.php">消息发送测试</a>
        </p>
    </div>
</body>
</html>
