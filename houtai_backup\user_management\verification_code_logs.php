<?php
/**
 * 验证码发送日志查看页面
 * 显示所有管理员发送验证码的操作记录
 */

session_start();

// 检查管理员登录状态
if (!isset($_SESSION['admin_id'])) {
    header('Location: ../login.php');
    exit;
}

$admin_id = $_SESSION['admin_id'];
$admin_username = $_SESSION['admin_username'] ?? '管理员';

// 分页参数
$page = max(1, intval($_GET['page'] ?? 1));
$limit = 20;
$offset = ($page - 1) * $limit;

// 搜索参数
$search_user = trim($_GET['search_user'] ?? '');
$search_admin = trim($_GET['search_admin'] ?? '');
$date_from = $_GET['date_from'] ?? '';
$date_to = $_GET['date_to'] ?? '';

try {
    $pdo = new PDO(
        "mysql:host=localhost;dbname=quwanplanet;charset=utf8mb4",
        "quwanplanet",
        "nJmJm23FB4Xn6Fc3",
        [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC
        ]
    );
    
    // 构建查询条件
    $where_conditions = ["ul.type = '发送验证码'"];
    $params = [];
    
    if (!empty($search_user)) {
        $where_conditions[] = "(u.username LIKE ? OR u.quwan_id LIKE ? OR u.phone LIKE ?)";
        $search_term = "%{$search_user}%";
        $params[] = $search_term;
        $params[] = $search_term;
        $params[] = $search_term;
    }
    
    if (!empty($search_admin)) {
        $where_conditions[] = "(ul.operator_name LIKE ? OR ul.employee_id LIKE ?)";
        $admin_term = "%{$search_admin}%";
        $params[] = $admin_term;
        $params[] = $admin_term;
    }
    
    if (!empty($date_from)) {
        $where_conditions[] = "ul.created_at >= ?";
        $params[] = $date_from . ' 00:00:00';
    }
    
    if (!empty($date_to)) {
        $where_conditions[] = "ul.created_at <= ?";
        $params[] = $date_to . ' 23:59:59';
    }
    
    $where_clause = implode(' AND ', $where_conditions);
    
    // 查询总数
    $count_sql = "
        SELECT COUNT(*) as total
        FROM user_logs ul
        LEFT JOIN users u ON ul.user_id = u.id
        WHERE {$where_clause}
    ";
    $stmt = $pdo->prepare($count_sql);
    $stmt->execute($params);
    $total_records = $stmt->fetch()['total'];
    $total_pages = ceil($total_records / $limit);
    
    // 查询日志记录
    $sql = "
        SELECT 
            ul.id,
            ul.user_id,
            u.username,
            u.quwan_id,
            u.phone as user_phone,
            ul.operator_name,
            ul.employee_id,
            ul.department,
            ul.content,
            ul.ip_address,
            ul.created_at,
            vc.code,
            vc.phone as code_phone,
            vc.type as code_type,
            vc.status as code_status,
            vc.expires_at
        FROM user_logs ul
        LEFT JOIN users u ON ul.user_id = u.id
        LEFT JOIN verification_codes vc ON ul.user_id = vc.user_id 
            AND ul.created_at BETWEEN DATE_SUB(vc.created_at, INTERVAL 10 SECOND) AND DATE_ADD(vc.created_at, INTERVAL 10 SECOND)
        WHERE {$where_clause}
        ORDER BY ul.created_at DESC
        LIMIT {$limit} OFFSET {$offset}
    ";
    
    $stmt = $pdo->prepare($sql);
    $stmt->execute($params);
    $logs = $stmt->fetchAll();
    
} catch (Exception $e) {
    $error_message = '数据库查询失败：' . $e->getMessage();
    $logs = [];
    $total_records = 0;
    $total_pages = 0;
}
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>验证码发送日志 - 趣玩星球管理后台</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        .main-content {
            margin-left: 250px;
            padding: 20px;
        }
        .card {
            border: none;
            box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
            border-radius: 0.5rem;
        }
        .card-header {
            background: linear-gradient(135deg, #6F7BF5 0%, #9B59B6 100%);
            color: white;
            border-radius: 0.5rem 0.5rem 0 0 !important;
        }
        .log-item {
            border-bottom: 1px solid #eee;
            padding: 15px 0;
        }
        .log-item:last-child {
            border-bottom: none;
        }
        .log-header {
            display: flex;
            justify-content: between;
            align-items: center;
            margin-bottom: 8px;
        }
        .log-user {
            font-weight: 600;
            color: #2d3748;
        }
        .log-time {
            color: #718096;
            font-size: 0.875rem;
        }
        .log-operator {
            color: #6F7BF5;
            font-size: 0.875rem;
        }
        .log-content {
            color: #4a5568;
            margin: 8px 0;
            line-height: 1.5;
        }
        .log-details {
            display: flex;
            gap: 15px;
            flex-wrap: wrap;
            margin-top: 8px;
        }
        .log-detail-item {
            font-size: 0.8rem;
            color: #718096;
        }
        .verification-code {
            font-family: 'Courier New', monospace;
            font-weight: bold;
            color: #6F7BF5;
            background: #f7fafc;
            padding: 2px 6px;
            border-radius: 4px;
        }
        .status-badge {
            font-size: 0.75rem;
            padding: 2px 8px;
            border-radius: 12px;
        }
        .status-pending {
            background: #fff3cd;
            color: #856404;
        }
        .status-used {
            background: #d4edda;
            color: #155724;
        }
        .status-expired {
            background: #f8d7da;
            color: #721c24;
        }
        .search-form {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <?php include '../includes/sidebar.php'; ?>
    
    <div class="main-content">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h2><i class="bi bi-journal-text"></i> 验证码发送日志</h2>
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="../dashboard.php">首页</a></li>
                    <li class="breadcrumb-item"><a href="index.php">用户管理</a></li>
                    <li class="breadcrumb-item active">验证码发送日志</li>
                </ol>
            </nav>
        </div>

        <!-- 搜索表单 -->
        <div class="search-form">
            <form method="GET" class="row g-3">
                <div class="col-md-3">
                    <label class="form-label">用户搜索</label>
                    <input type="text" class="form-control" name="search_user" 
                           value="<?= htmlspecialchars($search_user) ?>" 
                           placeholder="用户名/趣玩ID/手机号">
                </div>
                <div class="col-md-3">
                    <label class="form-label">管理员搜索</label>
                    <input type="text" class="form-control" name="search_admin" 
                           value="<?= htmlspecialchars($search_admin) ?>" 
                           placeholder="管理员姓名/工号">
                </div>
                <div class="col-md-2">
                    <label class="form-label">开始日期</label>
                    <input type="date" class="form-control" name="date_from" 
                           value="<?= htmlspecialchars($date_from) ?>">
                </div>
                <div class="col-md-2">
                    <label class="form-label">结束日期</label>
                    <input type="date" class="form-control" name="date_to" 
                           value="<?= htmlspecialchars($date_to) ?>">
                </div>
                <div class="col-md-2">
                    <label class="form-label">&nbsp;</label>
                    <div>
                        <button type="submit" class="btn btn-primary">
                            <i class="bi bi-search"></i> 搜索
                        </button>
                        <a href="?" class="btn btn-outline-secondary">
                            <i class="bi bi-arrow-clockwise"></i> 重置
                        </a>
                    </div>
                </div>
            </form>
        </div>

        <!-- 统计信息 -->
        <div class="row mb-4">
            <div class="col-md-12">
                <div class="alert alert-info">
                    <i class="bi bi-info-circle"></i>
                    共找到 <strong><?= number_format($total_records) ?></strong> 条验证码发送记录
                    <?php if (!empty($search_user) || !empty($search_admin) || !empty($date_from) || !empty($date_to)): ?>
                        （已应用筛选条件）
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <!-- 日志列表 -->
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="bi bi-list-ul"></i> 发送记录</h5>
            </div>
            <div class="card-body">
                <?php if (isset($error_message)): ?>
                    <div class="alert alert-danger">
                        <i class="bi bi-exclamation-triangle"></i> <?= htmlspecialchars($error_message) ?>
                    </div>
                <?php elseif (empty($logs)): ?>
                    <div class="text-center py-5">
                        <i class="bi bi-inbox" style="font-size: 3rem; color: #dee2e6;"></i>
                        <p class="text-muted mt-3">暂无验证码发送记录</p>
                    </div>
                <?php else: ?>
                    <?php foreach ($logs as $log): ?>
                        <div class="log-item">
                            <div class="log-header">
                                <div class="log-user">
                                    <i class="bi bi-person-circle"></i>
                                    <?= htmlspecialchars($log['username'] ?? '未知用户') ?>
                                    <?php if ($log['quwan_id']): ?>
                                        <small class="text-muted">(趣玩ID: <?= htmlspecialchars($log['quwan_id']) ?>)</small>
                                    <?php endif; ?>
                                </div>
                                <div class="log-time">
                                    <i class="bi bi-clock"></i>
                                    <?= date('Y-m-d H:i:s', strtotime($log['created_at'])) ?>
                                </div>
                            </div>
                            
                            <div class="log-operator">
                                <i class="bi bi-person-badge"></i>
                                操作员：<?= htmlspecialchars($log['operator_name']) ?>
                                <?php if ($log['employee_id']): ?>
                                    (工号: <?= htmlspecialchars($log['employee_id']) ?>)
                                <?php endif; ?>
                                <?php if ($log['department']): ?>
                                    - <?= htmlspecialchars($log['department']) ?>
                                <?php endif; ?>
                            </div>
                            
                            <div class="log-content">
                                <?= htmlspecialchars($log['content']) ?>
                            </div>
                            
                            <div class="log-details">
                                <?php if ($log['code']): ?>
                                    <div class="log-detail-item">
                                        <strong>验证码：</strong>
                                        <span class="verification-code"><?= htmlspecialchars($log['code']) ?></span>
                                    </div>
                                <?php endif; ?>
                                
                                <?php if ($log['code_phone']): ?>
                                    <div class="log-detail-item">
                                        <strong>手机号：</strong>
                                        <?= htmlspecialchars($log['code_phone']) ?>
                                    </div>
                                <?php endif; ?>
                                
                                <?php if ($log['code_type']): ?>
                                    <div class="log-detail-item">
                                        <strong>类型：</strong>
                                        <?= htmlspecialchars($log['code_type']) ?>
                                    </div>
                                <?php endif; ?>
                                
                                <?php if ($log['code_status']): ?>
                                    <div class="log-detail-item">
                                        <strong>状态：</strong>
                                        <span class="status-badge status-<?= htmlspecialchars($log['code_status']) ?>">
                                            <?= htmlspecialchars($log['code_status']) ?>
                                        </span>
                                    </div>
                                <?php endif; ?>
                                
                                <?php if ($log['ip_address']): ?>
                                    <div class="log-detail-item">
                                        <strong>IP：</strong>
                                        <?= htmlspecialchars($log['ip_address']) ?>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    <?php endforeach; ?>
                <?php endif; ?>
            </div>
        </div>

        <!-- 分页 -->
        <?php if ($total_pages > 1): ?>
            <nav aria-label="日志分页" class="mt-4">
                <ul class="pagination justify-content-center">
                    <?php if ($page > 1): ?>
                        <li class="page-item">
                            <a class="page-link" href="?<?= http_build_query(array_merge($_GET, ['page' => $page - 1])) ?>">
                                <i class="bi bi-chevron-left"></i> 上一页
                            </a>
                        </li>
                    <?php endif; ?>
                    
                    <?php for ($i = max(1, $page - 2); $i <= min($total_pages, $page + 2); $i++): ?>
                        <li class="page-item <?= $i == $page ? 'active' : '' ?>">
                            <a class="page-link" href="?<?= http_build_query(array_merge($_GET, ['page' => $i])) ?>">
                                <?= $i ?>
                            </a>
                        </li>
                    <?php endfor; ?>
                    
                    <?php if ($page < $total_pages): ?>
                        <li class="page-item">
                            <a class="page-link" href="?<?= http_build_query(array_merge($_GET, ['page' => $page + 1])) ?>">
                                下一页 <i class="bi bi-chevron-right"></i>
                            </a>
                        </li>
                    <?php endif; ?>
                </ul>
                
                <div class="text-center text-muted">
                    第 <?= $page ?> 页，共 <?= $total_pages ?> 页，总计 <?= number_format($total_records) ?> 条记录
                </div>
            </nav>
        <?php endif; ?>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
