<?php
// 引入session配置
require_once '../../sql/session_config.php';
initLoginSession();

// 检查用户是否已登录
if (!isUserLoggedIn()) {
    header('Location: ../login/index.php');
    exit;
}

$user_id = $_SESSION['user_id'];

// 数据库连接配置
$db_config = [
    'host' => 'localhost',
    'username' => 'quwanplanet',
    'password' => 'nJmJm23FB4Xn6Fc3',
    'dbname' => 'quwanplanet',
    'port' => 3306,
    'charset' => 'utf8mb4'
];

// 获取用户发布的活动
$my_activities = [];

try {
    $dsn = "mysql:host={$db_config['host']};dbname={$db_config['dbname']};charset={$db_config['charset']};port={$db_config['port']}";
    $pdo = new PDO($dsn, $db_config['username'], $db_config['password']);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

    // 检查表是否存在
    $stmt = $pdo->query("SHOW TABLES LIKE 'camping_activities'");
    if ($stmt->rowCount() > 0) {
        // 获取用户发布的活动
        $sql = "SELECT * FROM camping_activities 
                WHERE organizer_id = ? 
                ORDER BY created_at DESC";
        
        $stmt = $pdo->prepare($sql);
        $stmt->execute([$user_id]);
        $my_activities = $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
} catch (PDOException $e) {
    error_log("获取组局活动错误: " . $e->getMessage());
}

// 获取活动类型中文名称
function getActivityTypeName($type) {
    $types = [
        'mountain' => '山地露营',
        'lake' => '湖边露营',
        'forest' => '森林露营',
        'beach' => '海边露营'
    ];
    return $types[$type] ?? $type;
}

// 获取活动状态中文名称
function getActivityStatusName($status) {
    $statuses = [
        'draft' => '草稿',
        'recruiting' => '招募中',
        'full' => '已满员',
        'ongoing' => '进行中',
        'completed' => '已完成',
        'cancelled' => '已取消'
    ];
    return $statuses[$status] ?? $status;
}

// 获取活动状态样式类
function getActivityStatusClass($status) {
    $classes = [
        'draft' => 'status-draft',
        'recruiting' => 'status-recruiting',
        'full' => 'status-full',
        'ongoing' => 'status-ongoing',
        'completed' => 'status-completed',
        'cancelled' => 'status-cancelled'
    ];
    return $classes[$status] ?? 'status-default';
}
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <meta name="theme-color" content="#6F7BF5">
    <title>组局 - 趣玩星球</title>
    <link rel="stylesheet" href="css/groups.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <!-- Toast提示 -->
    <div class="toast" id="toast"></div>

    <!-- 顶部导航栏 -->
    <div class="header-bar">
        <a href="../profile/index.php" class="back-button">
            <i class="fas fa-arrow-left"></i>
        </a>
        <div class="header-title">我的组局</div>
        <a href="../camping/create-activity.php" class="create-button">
            <i class="fas fa-plus"></i>
        </a>
    </div>

    <!-- 统计信息 -->
    <div class="stats-section">
        <div class="stats-container">
            <div class="stat-item">
                <div class="stat-value"><?php echo count($my_activities); ?></div>
                <div class="stat-label">总组局</div>
            </div>
            <div class="stat-item">
                <div class="stat-value"><?php echo count(array_filter($my_activities, function($a) { return $a['status'] === 'recruiting'; })); ?></div>
                <div class="stat-label">招募中</div>
            </div>
            <div class="stat-item">
                <div class="stat-value"><?php echo count(array_filter($my_activities, function($a) { return $a['status'] === 'completed'; })); ?></div>
                <div class="stat-label">已完成</div>
            </div>
            <div class="stat-item">
                <div class="stat-value"><?php echo array_sum(array_column($my_activities, 'current_participants')); ?></div>
                <div class="stat-label">总参与人数</div>
            </div>
        </div>
    </div>

    <!-- 活动列表 -->
    <div class="activities-container">
        <?php if (empty($my_activities)): ?>
        <div class="empty-state">
            <i class="fas fa-campground"></i>
            <p>还没有发起过活动</p>
            <p class="empty-desc">快来发起你的第一个露营活动吧！</p>
            <a href="../camping/create-activity.php" class="btn">发起活动</a>
        </div>
        <?php else: ?>
        <div class="activities-list">
            <?php foreach ($my_activities as $activity): ?>
            <div class="activity-card">
                <div class="activity-header">
                    <div class="activity-type"><?php echo getActivityTypeName($activity['category']); ?></div>
                    <div class="activity-status <?php echo getActivityStatusClass($activity['status']); ?>">
                        <?php echo getActivityStatusName($activity['status']); ?>
                    </div>
                </div>
                
                <div class="activity-content">
                    <h3 class="activity-title"><?php echo htmlspecialchars($activity['title']); ?></h3>
                    <div class="activity-info">
                        <div class="info-item">
                            <i class="fas fa-map-marker-alt"></i>
                            <span><?php echo htmlspecialchars($activity['location']); ?></span>
                        </div>
                        <div class="info-item">
                            <i class="fas fa-calendar"></i>
                            <span><?php echo date('m月d日', strtotime($activity['start_date'])); ?> - <?php echo date('m月d日', strtotime($activity['end_date'])); ?></span>
                        </div>
                        <div class="info-item">
                            <i class="fas fa-users"></i>
                            <span><?php echo $activity['current_participants']; ?>/<?php echo $activity['max_participants']; ?>人</span>
                        </div>
                        <div class="info-item">
                            <i class="fas fa-yen-sign"></i>
                            <span>¥<?php echo number_format($activity['price'], 2); ?></span>
                        </div>
                    </div>
                    
                    <?php if (!empty($activity['features'])): ?>
                    <div class="activity-features">
                        <?php 
                        $features = explode(',', $activity['features']);
                        foreach (array_slice($features, 0, 3) as $feature): 
                        ?>
                        <span class="feature-tag"><?php echo htmlspecialchars(trim($feature)); ?></span>
                        <?php endforeach; ?>
                        <?php if (count($features) > 3): ?>
                        <span class="feature-more">+<?php echo count($features) - 3; ?></span>
                        <?php endif; ?>
                    </div>
                    <?php endif; ?>
                </div>
                
                <div class="activity-footer">
                    <div class="activity-time">
                        发布于 <?php echo date('Y-m-d H:i', strtotime($activity['created_at'])); ?>
                    </div>
                    <div class="activity-actions">
                        <?php if ($activity['status'] === 'draft'): ?>
                        <button class="action-btn edit-btn" onclick="editActivity(<?php echo $activity['id']; ?>)">
                            <i class="fas fa-edit"></i> 编辑
                        </button>
                        <button class="action-btn publish-btn" onclick="publishActivity(<?php echo $activity['id']; ?>)">
                            <i class="fas fa-rocket"></i> 发布
                        </button>
                        <?php elseif ($activity['status'] === 'recruiting'): ?>
                        <button class="action-btn view-btn" onclick="viewActivity(<?php echo $activity['id']; ?>)">
                            <i class="fas fa-eye"></i> 查看
                        </button>
                        <button class="action-btn manage-btn" onclick="manageActivity(<?php echo $activity['id']; ?>)">
                            <i class="fas fa-cog"></i> 管理
                        </button>
                        <?php else: ?>
                        <button class="action-btn view-btn" onclick="viewActivity(<?php echo $activity['id']; ?>)">
                            <i class="fas fa-eye"></i> 查看
                        </button>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
            <?php endforeach; ?>
        </div>
        <?php endif; ?>
    </div>

    <script src="js/groups.js"></script>
</body>
</html>
