<?php
session_start();

// 简单的登录检查
if (!isset($_SESSION['admin_id'])) {
    header('Location: ../login.php');
    exit;
}

// 获取用户ID
$user_id = isset($_GET['id']) ? (int)$_GET['id'] : 0;

if (!$user_id) {
    header('Location: index.php');
    exit;
}

try {
    require_once '../db_config.php';
    $pdo = getDbConnection();

    // 获取用户详细信息
    $stmt = $pdo->prepare("
        SELECT u.*,
               rv.verification_status,
               rv.real_name,
               rv.id_card_number,
               rv.id_card_front_url,
               rv.id_card_back_url,
               rv.verification_reason,
               rv.submitted_at,
               rv.verified_at,
               uw.balance,
               uw.frozen_balance,
               uw.total_recharge,
               uw.total_withdraw,
               uw.total_consumption,
               um.level as membership_level,
               um.expire_time as membership_expire,
               ulc.level_name,
               ulc.level_icon
        FROM users u
        LEFT JOIN realname_verification rv ON u.id = rv.user_id
        LEFT JOIN user_wallets uw ON u.id = uw.user_id
        LEFT JOIN user_memberships um ON u.id = um.user_id
        LEFT JOIN user_level_config ulc ON u.level = ulc.level
        WHERE u.id = ?
    ");
    $stmt->execute([$user_id]);
    $user = $stmt->fetch(PDO::FETCH_ASSOC);

    // 检查用户封号状态
    $user['is_banned'] = 0;
    try {
        $ban_stmt = $pdo->prepare("
            SELECT COUNT(*) as ban_count
            FROM user_bans
            WHERE user_id = ? AND status = 'active'
        ");
        $ban_stmt->execute([$user_id]);
        $ban_result = $ban_stmt->fetch(PDO::FETCH_ASSOC);
        $user['is_banned'] = $ban_result['ban_count'] > 0 ? 1 : 0;
    } catch (Exception $e) {
        // 如果user_bans表不存在，根据用户status字段判断
        $user['is_banned'] = ($user['status'] === 'banned') ? 1 : 0;
    }

    if (!$user) {
        header('Location: index.php');
        exit;
    }

    // 安全地获取扩展数据（如果表不存在则使用默认值）
    $badges = [];
    $inventory = [];
    $checkins = [];
    $earnings = [];
    $works_count = 0;
    $orders_count = 0;
    $coupons_count = 0;
    $operation_logs = [];

    // 尝试获取用户勋章
    try {
        $stmt = $pdo->prepare("SELECT * FROM user_badges WHERE user_id = ? ORDER BY earned_at DESC");
        $stmt->execute([$user_id]);
        $badges = $stmt->fetchAll(PDO::FETCH_ASSOC);
    } catch (Exception $e) {
        // 表不存在，使用默认值
    }

    // 尝试获取用户背包物品
    try {
        $stmt = $pdo->prepare("SELECT * FROM user_inventory WHERE user_id = ? AND status = 'active' ORDER BY obtained_at DESC");
        $stmt->execute([$user_id]);
        $inventory = $stmt->fetchAll(PDO::FETCH_ASSOC);
    } catch (Exception $e) {
        // 表不存在，使用默认值
    }

    // 尝试获取签到信息
    try {
        $stmt = $pdo->prepare("SELECT * FROM user_checkins WHERE user_id = ? ORDER BY checkin_date DESC LIMIT 7");
        $stmt->execute([$user_id]);
        $checkins = $stmt->fetchAll(PDO::FETCH_ASSOC);
    } catch (Exception $e) {
        // 表不存在，使用默认值
    }

    // 尝试获取收益记录
    try {
        $stmt = $pdo->prepare("SELECT * FROM user_earnings WHERE user_id = ? ORDER BY created_at DESC LIMIT 10");
        $stmt->execute([$user_id]);
        $earnings = $stmt->fetchAll(PDO::FETCH_ASSOC);
    } catch (Exception $e) {
        // 表不存在，使用默认值
    }

    // 尝试获取作品统计
    try {
        $stmt = $pdo->prepare("SELECT COUNT(*) as count FROM user_works WHERE user_id = ? AND status != 'deleted'");
        $stmt->execute([$user_id]);
        $works_count = $stmt->fetchColumn();
    } catch (Exception $e) {
        // 表不存在，使用默认值
    }

    // 尝试获取订单统计
    try {
        $stmt = $pdo->prepare("SELECT COUNT(*) as count FROM user_orders WHERE user_id = ?");
        $stmt->execute([$user_id]);
        $orders_count = $stmt->fetchColumn();
    } catch (Exception $e) {
        // 表不存在，使用默认值
    }

    // 尝试获取管理员操作日志
    try {
        $stmt = $pdo->prepare("SELECT * FROM admin_operation_logs WHERE user_id = ? ORDER BY created_at DESC LIMIT 20");
        $stmt->execute([$user_id]);
        $operation_logs = $stmt->fetchAll(PDO::FETCH_ASSOC);
    } catch (Exception $e) {
        // 表不存在，使用默认值
    }

} catch (Exception $e) {
    $error = "数据库查询失败：" . $e->getMessage();
    // 如果数据库查询失败，创建一个空的用户数组以避免错误
    $user = [
        'id' => 0,
        'username' => '数据加载失败',
        'phone' => '',
        'email' => '',
        'created_at' => date('Y-m-d H:i:s'),
        'last_login' => '',
        'status' => 'inactive',
        'verification_status' => null,
        'quwan_id' => '',
        'quwanplanet_id' => '',
        'gender' => '',
        'birthday' => '',
        'region' => '',
        'bio' => '',
        'level' => 1,
        'experience' => 0,
        'points' => 0,
        'followers_count' => 0,
        'following_count' => 0,
        'likes_received' => 0,
        'favorites_count' => 0,
        'is_companion' => 0,
        'total_earnings' => 0,
        'balance' => 0,
        'frozen_balance' => 0,
        'membership_level' => 'normal',
        'level_name' => '新手',
        'level_icon' => '🌱'
    ];
    $badges = [];
    $inventory = [];
    $checkins = [];
    $earnings = [];
    $works_count = 0;
    $orders_count = 0;
    $coupons_count = 0;
    $operation_logs = [];
}

// 敏感信息格式化函数
function formatSensitiveInfo($info, $type) {
    if (empty($info)) return '未设置';

    switch ($type) {
        case 'phone':
            return substr($info, 0, 3) . '****' . substr($info, 7);
        case 'email':
            $parts = explode('@', $info);
            return substr($parts[0], 0, 2) . '***@' . $parts[1];
        case 'id_card':
        case 'idcard':
            return substr($info, 0, 6) . '********' . substr($info, -4);
        default:
            return $info;
    }
}

// 状态标签函数
function getVerificationBadge($user, $clickable = false) {
    $class = $clickable ? 'status-badge clickable-badge' : 'status-badge';
    $onclick = $clickable ? 'onclick="showVerificationDetails(' . $user['id'] . ')"' : '';

    if ($user['verification_status'] === 'approved') {
        return '<span class="' . $class . ' active" ' . $onclick . '><i class="fas fa-check-circle"></i> 已认证</span>';
    } elseif ($user['verification_status'] === 'pending') {
        return '<span class="' . $class . ' pending" ' . $onclick . '><i class="fas fa-clock"></i> 审核中</span>';
    } elseif ($user['verification_status'] === 'rejected') {
        return '<span class="' . $class . ' error" ' . $onclick . '><i class="fas fa-times-circle"></i> 已拒绝</span>';
    } else {
        return '<span class="' . $class . ' inactive" ' . $onclick . '><i class="fas fa-user"></i> 未认证</span>';
    }
}

// 获取当前页面信息用于侧边栏高亮
$current_page = basename($_SERVER['PHP_SELF']);
$current_dir = basename(dirname($_SERVER['PHP_SELF']));
?>

<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>用户详情 - <?php echo htmlspecialchars($user['username']); ?> - 趣玩星球管理后台</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="../assets/css/admin-layout.css">
    <link rel="stylesheet" href="../assets/css/modern-components.css">
    <style>
        /* 用户详情页面特定样式 */
        .user-detail-container {
            display: flex;
            flex-direction: column;
            gap: var(--spacing-xl);
        }

        .detail-sections {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: var(--spacing-xl);
        }

        .full-width-section {
            grid-column: 1 / -1;
        }

        /* 用户头像和基本信息样式 */
        .user-profile-container {
            margin-bottom: var(--spacing-2xl);
        }

        .user-profile-header {
            display: flex;
            gap: var(--spacing-2xl);
            align-items: flex-start;
        }

        .user-avatar-section {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: var(--spacing-md);
            flex-shrink: 0;
        }

        .user-avatar-large {
            position: relative;
        }

        .avatar-img {
            width: 120px;
            height: 120px;
            border-radius: var(--border-radius-xl);
            object-fit: cover;
            border: 4px solid var(--primary-color);
            box-shadow: var(--shadow-primary);
            transition: var(--transition);
        }

        .avatar-img:hover {
            transform: scale(1.05);
            box-shadow: var(--shadow-primary-lg);
        }

        .user-status-badges {
            display: flex;
            flex-direction: column;
            gap: var(--spacing-sm);
            align-items: center;
        }

        .user-basic-info {
            flex: 1;
            display: flex;
            flex-direction: column;
            gap: var(--spacing-xl);
        }

        .user-name-section {
            margin-bottom: var(--spacing-lg);
        }

        .user-name-row {
            display: flex;
            align-items: center;
            gap: var(--spacing-md);
            flex-wrap: wrap;
        }

        .user-name {
            font-size: var(--font-size-4xl);
            font-weight: 800;
            color: var(--gray-900);
            margin: 0;
            line-height: 1.2;
        }

        .user-id-badge {
            display: inline-flex;
            align-items: center;
            gap: var(--spacing-sm);
            background: var(--primary-gradient);
            color: white;
            padding: var(--spacing-xs) var(--spacing-md);
            border-radius: var(--border-radius-lg);
            font-weight: 600;
            box-shadow: var(--shadow-primary);
            font-size: var(--font-size-sm);
        }

        .id-label {
            font-size: var(--font-size-sm);
        }

        .id-value {
            font-size: var(--font-size-lg);
            font-weight: 700;
        }

        .user-meta-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: var(--spacing-md);
        }

        .meta-card {
            display: flex;
            align-items: center;
            gap: var(--spacing-md);
            background: var(--gray-50);
            padding: var(--spacing-lg) var(--spacing-md);
            border-radius: var(--border-radius-lg);
            border: 2px solid transparent;
            transition: var(--transition);
            min-height: 80px;
        }

        .meta-card:hover {
            border-color: var(--primary-color);
            transform: translateY(-2px);
            box-shadow: var(--shadow-md);
        }

        .meta-icon {
            font-size: 24px;
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            background: white;
            border-radius: var(--border-radius);
            box-shadow: var(--shadow-sm);
        }

        .meta-content {
            display: flex;
            flex-direction: column;
            gap: var(--spacing-xs);
            flex: 1;
        }

        .meta-label {
            font-size: var(--font-size-sm);
            font-weight: 600;
            color: var(--gray-600);
        }

        .meta-value {
            font-size: var(--font-size-base);
            font-weight: 700;
            color: var(--gray-900);
        }

        .user-bio-section {
            display: flex;
            gap: var(--spacing-md);
            background: var(--primary-gradient-light);
            padding: var(--spacing-lg);
            border-radius: var(--border-radius-lg);
            border: 2px solid rgba(111, 123, 245, 0.1);
        }

        .bio-icon {
            font-size: 24px;
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            background: var(--primary-color);
            color: white;
            border-radius: var(--border-radius);
            flex-shrink: 0;
        }

        .bio-content {
            flex: 1;
        }

        .bio-label {
            font-size: var(--font-size-sm);
            font-weight: 600;
            color: var(--gray-600);
            display: block;
            margin-bottom: var(--spacing-sm);
        }

        .bio-text {
            font-size: var(--font-size-base);
            color: var(--gray-900);
            margin: 0;
            line-height: 1.5;
        }

        /* 信息网格 */
        .info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: var(--spacing-2xl);
        }

        .info-section h4 {
            font-size: var(--font-size-xl);
            font-weight: 700;
            color: var(--gray-900);
            margin: 0 0 var(--spacing-md) 0;
            padding-bottom: var(--spacing-sm);
            border-bottom: 2px solid var(--primary-color);
        }

        .info-list {
            display: flex;
            flex-direction: column;
            gap: var(--spacing-md);
        }

        .info-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: var(--spacing-md) 0;
            border-bottom: 1px solid var(--gray-200);
        }

        .info-item:last-child {
            border-bottom: none;
        }

        .info-item .label {
            font-weight: 600;
            color: var(--gray-600);
            min-width: 100px;
        }

        .info-item .value {
            font-weight: 600;
            color: var(--gray-900);
            text-align: right;
        }

        /* 敏感信息容器 */
        .sensitive-info-container {
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);
        }

        .toggle-sensitive-btn {
            background: var(--gray-200);
            border: none;
            border-radius: var(--border-radius);
            padding: var(--spacing-xs);
            cursor: pointer;
            transition: var(--transition);
            color: var(--gray-600);
        }

        .toggle-sensitive-btn:hover {
            background: var(--primary-color);
            color: white;
        }

        /* 功能按钮组 */
        .action-buttons {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: var(--spacing-md);
        }

        .action-btn {
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);
            padding: var(--spacing-md);
            border: none;
            border-radius: var(--border-radius-lg);
            font-weight: 600;
            cursor: pointer;
            transition: var(--transition);
            text-decoration: none;
            justify-content: center;
        }

        .action-btn.danger {
            background: rgba(239, 68, 68, 0.1);
            color: var(--error-color);
            border: 1px solid rgba(239, 68, 68, 0.2);
        }

        .action-btn.danger:hover {
            background: var(--error-color);
            color: white;
        }

        .action-btn.warning {
            background: rgba(245, 158, 11, 0.1);
            color: var(--warning-color);
            border: 1px solid rgba(245, 158, 11, 0.2);
        }

        .action-btn.warning:hover {
            background: var(--warning-color);
            color: white;
        }

        .action-btn.info {
            background: rgba(59, 130, 246, 0.1);
            color: var(--info-color);
            border: 1px solid rgba(59, 130, 246, 0.2);
        }

        .action-btn.info:hover {
            background: var(--info-color);
            color: white;
        }

        /* 按钮样式 */
        .btn {
            display: inline-flex;
            align-items: center;
            gap: var(--spacing-xs);
            padding: var(--spacing-sm) var(--spacing-md);
            border: none;
            border-radius: var(--border-radius);
            font-size: var(--font-size-sm);
            font-weight: 500;
            cursor: pointer;
            transition: var(--transition);
            text-decoration: none;
        }

        .btn-sm {
            padding: 4px 8px;
            font-size: var(--font-size-xs);
        }

        .btn-success {
            background: var(--success-color);
            color: white;
        }

        .btn-success:hover {
            background: var(--success-dark);
            transform: translateY(-1px);
            box-shadow: var(--shadow-md);
        }



        /* 弹窗样式 */
        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            z-index: 10000;
            backdrop-filter: blur(4px);
        }

        .modal.show {
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .modal-content {
            background: white;
            border-radius: var(--border-radius-xl);
            padding: var(--spacing-2xl);
            max-width: 600px;
            width: 90%;
            max-height: 80vh;
            overflow-y: auto;
            box-shadow: var(--shadow-2xl);
        }

        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: var(--spacing-xl);
            padding-bottom: var(--spacing-md);
            border-bottom: 2px solid var(--gray-200);
        }

        .modal-title {
            font-size: var(--font-size-xl);
            font-weight: 700;
            color: var(--gray-900);
        }

        .modal-close {
            background: none;
            border: none;
            font-size: 24px;
            color: var(--gray-500);
            cursor: pointer;
            padding: var(--spacing-xs);
            border-radius: var(--border-radius);
            transition: var(--transition);
        }

        .modal-close:hover {
            background: var(--gray-100);
            color: var(--gray-700);
        }

        /* 状态徽章可点击样式 */
        .clickable-badge {
            cursor: pointer;
            transition: var(--transition);
        }

        .clickable-badge:hover {
            transform: scale(1.05);
            box-shadow: var(--shadow-md);
        }

        /* 响应式设计 */
        @media (max-width: 1024px) {
            .user-detail-container {
                grid-template-columns: 1fr;
                gap: var(--spacing-xl);
            }
        }

        @media (max-width: 768px) {
            .user-profile-header {
                flex-direction: column;
                align-items: center;
                text-align: center;
                gap: var(--spacing-xl);
            }

            .user-meta-grid {
                grid-template-columns: 1fr;
            }

            .info-grid {
                grid-template-columns: 1fr;
            }
        }

        /* 数据表格样式 */
        .data-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: var(--spacing-md);
            background: white;
            border-radius: var(--border-radius-lg);
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        .data-table th,
        .data-table td {
            padding: var(--spacing-md);
            text-align: left;
            border-bottom: 1px solid var(--gray-200);
            vertical-align: top;
        }

        .data-table th {
            background: var(--gray-50);
            font-weight: 600;
            color: var(--gray-700);
            font-size: var(--font-size-sm);
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .data-table tr:hover {
            background: var(--gray-50);
        }

        .data-table td {
            font-size: var(--font-size-sm);
            color: var(--gray-600);
        }

        /* 全宽度板块样式 */
        .full-width-section {
            grid-column: 1 / -1;
            margin-top: var(--spacing-xl);
        }

        /* 状态徽章样式扩展 */
        .status-badge.secondary {
            background: var(--gray-100);
            color: var(--gray-700);
        }

        /* Toast 提示样式 */
        .toast-container {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 10000;
            pointer-events: none;
        }

        .toast {
            background: white;
            border-radius: 12px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
            margin-bottom: 12px;
            padding: 16px 20px;
            min-width: 300px;
            max-width: 400px;
            border-left: 4px solid;
            display: flex;
            align-items: center;
            gap: 12px;
            transform: translateX(100%);
            opacity: 0;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            pointer-events: auto;
            position: relative;
            overflow: hidden;
        }

        .toast.show {
            transform: translateX(0);
            opacity: 1;
        }

        .toast.success {
            border-left-color: #10B981;
        }

        .toast.danger {
            border-left-color: #EF4444;
        }

        .toast.warning {
            border-left-color: #F59E0B;
        }

        .toast.info {
            border-left-color: #3B82F6;
        }

        .toast-icon {
            font-size: 20px;
            flex-shrink: 0;
        }

        .toast.success .toast-icon {
            color: #10B981;
        }

        .toast.danger .toast-icon {
            color: #EF4444;
        }

        .toast.warning .toast-icon {
            color: #F59E0B;
        }

        .toast.info .toast-icon {
            color: #3B82F6;
        }

        .toast-content {
            flex: 1;
        }

        .toast-message {
            font-weight: 600;
            color: #1F2937;
            margin: 0;
            font-size: 14px;
            line-height: 1.4;
        }

        .toast-close {
            background: none;
            border: none;
            color: #6B7280;
            cursor: pointer;
            font-size: 18px;
            padding: 0;
            width: 24px;
            height: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 4px;
            transition: all 0.2s;
            flex-shrink: 0;
        }

        .toast-close:hover {
            background: #F3F4F6;
            color: #374151;
        }

        .toast-progress {
            position: absolute;
            bottom: 0;
            left: 0;
            height: 3px;
            background: currentColor;
            opacity: 0.3;
            transition: width linear;
        }

        /* 用户画像样式 */
        .profile-card {
            background: white;
            border-radius: var(--border-radius-lg);
            box-shadow: var(--shadow-sm);
            border: 1px solid var(--gray-200);
            overflow: hidden;
            transition: var(--transition);
        }

        .profile-card:hover {
            box-shadow: var(--shadow-md);
            transform: translateY(-2px);
        }

        .profile-card-header {
            background: linear-gradient(135deg, var(--primary-color) 0%, #667eea 100%);
            color: white;
            padding: var(--spacing-md);
            border-bottom: 1px solid var(--gray-200);
        }

        .profile-card-header h4 {
            margin: 0;
            font-size: var(--font-size-lg);
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);
        }

        .profile-card-body {
            padding: var(--spacing-lg);
        }

        .profile-metric {
            margin-bottom: var(--spacing-lg);
        }

        .profile-metric:last-child {
            margin-bottom: 0;
        }

        .metric-label {
            display: block;
            font-size: var(--font-size-sm);
            font-weight: 600;
            color: var(--gray-600);
            margin-bottom: var(--spacing-xs);
        }

        .metric-value {
            display: flex;
            flex-direction: column;
            gap: var(--spacing-xs);
        }

        .score {
            font-size: var(--font-size-lg);
            font-weight: 700;
            padding: 4px 8px;
            border-radius: var(--border-radius);
        }

        .score.excellent {
            background: var(--success-color);
            color: white;
        }

        .score.good {
            background: var(--info-color);
            color: white;
        }

        .score.average {
            background: var(--warning-color);
            color: white;
        }

        .score.poor {
            background: var(--error-color);
            color: white;
        }

        .score-bar {
            width: 100%;
            height: 8px;
            background: var(--gray-200);
            border-radius: 4px;
            overflow: hidden;
        }

        .score-fill {
            height: 100%;
            background: linear-gradient(90deg, var(--primary-color), var(--success-color));
            transition: width 0.6s ease;
        }

        .level, .user-type, .risk-level, .trend, .prediction {
            padding: 4px 12px;
            border-radius: var(--border-radius);
            font-weight: 600;
            font-size: var(--font-size-sm);
        }

        .level.high, .user-type.premium {
            background: var(--success-color);
            color: white;
        }

        .level.medium, .user-type.regular {
            background: var(--warning-color);
            color: white;
        }

        .level.low, .user-type.basic {
            background: var(--gray-400);
            color: white;
        }

        .risk-level.low {
            background: var(--success-color);
            color: white;
        }

        .risk-level.medium {
            background: var(--warning-color);
            color: white;
        }

        .risk-level.high {
            background: var(--error-color);
            color: white;
        }

        .trend.up, .prediction.high {
            background: var(--success-color);
            color: white;
        }

        .trend.stable, .prediction.medium {
            background: var(--info-color);
            color: white;
        }

        .trend.down, .prediction.low {
            background: var(--error-color);
            color: white;
        }

        .preference-tags, .service-tags {
            display: flex;
            flex-wrap: wrap;
            gap: var(--spacing-xs);
        }

        .preference-tag, .service-tag {
            background: var(--primary-color);
            color: white;
            padding: 2px 8px;
            border-radius: var(--border-radius);
            font-size: var(--font-size-xs);
            font-weight: 500;
        }

        .service-tag {
            background: var(--info-color);
        }

        .location-list {
            display: flex;
            flex-direction: column;
            gap: var(--spacing-xs);
        }

        .location-item {
            background: var(--gray-100);
            padding: var(--spacing-xs) var(--spacing-sm);
            border-radius: var(--border-radius);
            font-size: var(--font-size-sm);
        }

        .chart-container {
            background: white;
            border-radius: var(--border-radius-lg);
            padding: var(--spacing-lg);
            box-shadow: var(--shadow-sm);
            border: 1px solid var(--gray-200);
        }

        .chart-container h5 {
            margin: 0 0 var(--spacing-md) 0;
            color: var(--gray-900);
            font-weight: 600;
        }

        .time-chart {
            display: flex;
            align-items: end;
            gap: 2px;
            height: 100px;
            padding: var(--spacing-sm) 0;
        }

        .time-bar {
            flex: 1;
            display: flex;
            flex-direction: column;
            align-items: center;
            height: 100%;
        }

        .time-fill {
            background: linear-gradient(to top, var(--primary-color), var(--info-color));
            width: 100%;
            min-height: 2px;
            border-radius: 2px 2px 0 0;
            transition: height 0.6s ease;
        }

        .time-label {
            font-size: 10px;
            color: var(--gray-600);
            margin-top: 4px;
        }

        .page-stats {
            display: flex;
            flex-direction: column;
            gap: var(--spacing-sm);
        }

        .page-stat-item {
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);
        }

        .page-name {
            min-width: 120px;
            font-size: var(--font-size-sm);
            color: var(--gray-700);
        }

        .page-bar {
            flex: 1;
            height: 20px;
            background: var(--gray-200);
            border-radius: 10px;
            position: relative;
            overflow: hidden;
        }

        .page-fill {
            height: 100%;
            background: linear-gradient(90deg, var(--primary-color), var(--info-color));
            border-radius: 10px;
            transition: width 0.6s ease;
        }

        .page-percentage {
            position: absolute;
            right: 8px;
            top: 50%;
            transform: translateY(-50%);
            font-size: var(--font-size-xs);
            color: white;
            font-weight: 600;
        }

        .sensitivity.low {
            background: var(--success-color);
            color: white;
        }

        .sensitivity.medium {
            background: var(--warning-color);
            color: white;
        }

        .sensitivity.high {
            background: var(--error-color);
            color: white;
        }

        .time-period, .duration, .page-name, .device-type, .frequency, .amount, .location, .range {
            font-weight: 600;
            color: var(--gray-900);
        }

        .amount {
            color: var(--success-color);
            font-size: var(--font-size-lg);
        }
    </style>
</head>
<body>
    <div class="admin-layout">
        <!-- 引入侧边栏 -->
        <?php include '../includes/sidebar.php'; ?>

        <!-- 主内容区域 -->
        <div class="main-content">
            <!-- 页面头部 -->
            <div class="page-header">
                <div class="page-header-content">
                    <div class="page-title">
                        <div class="page-title-icon">
                            <i class="fas fa-user-circle"></i>
                        </div>
                        <div>
                            <h1>用户详情</h1>
                            <p class="page-subtitle"><?php echo htmlspecialchars($user['username']); ?> 的详细信息</p>
                        </div>
                    </div>
                    <div class="page-actions">
                        <a href="index.php" class="btn btn-secondary">
                            <i class="fas fa-arrow-left"></i>
                            返回列表
                        </a>
                    </div>
                </div>
            </div>

            <!-- 页面内容 -->
            <div class="page-content">
                <?php if (isset($error)): ?>
                    <div class="card" style="border-left: 4px solid var(--error-color); margin-bottom: var(--spacing-xl);">
                        <div class="card-body">
                            <div style="display: flex; align-items: center; gap: var(--spacing-md); color: var(--error-color);">
                                <i class="fas fa-exclamation-triangle" style="font-size: 24px;"></i>
                                <div>
                                    <h4 style="margin: 0; color: var(--error-color);">数据加载错误</h4>
                                    <p style="margin: var(--spacing-xs) 0 0 0; color: var(--gray-600);"><?php echo htmlspecialchars($error); ?></p>
                                </div>
                            </div>
                        </div>
                    </div>
                <?php endif; ?>
                <!-- 用户基本信息卡片 -->
                <div class="card user-profile-container">
                    <div class="card-header">
                        <h3 class="card-title">
                            <i class="fas fa-user"></i>
                            基本信息
                        </h3>
                    </div>
                    <div class="card-body">
                        <div class="user-profile-header">
                            <div class="user-avatar-section">
                                <div class="user-avatar-large">
                                    <?php if (!empty($user['avatar'])): ?>
                                        <img src="<?php echo htmlspecialchars($user['avatar']); ?>"
                                             alt="用户头像" class="avatar-img">
                                    <?php else: ?>
                                        <div class="user-avatar">
                                            <?php echo strtoupper(substr($user['username'], 0, 1)); ?>
                                        </div>
                                    <?php endif; ?>
                                </div>
                                <div class="user-status-badges">
                                    <?php echo getVerificationBadge($user, true); ?>
                                </div>
                            </div>

                            <div class="user-basic-info">
                                <div class="user-name-section">
                                    <div class="user-name-row">
                                        <h2 class="user-name"><?php echo htmlspecialchars($user['username']); ?></h2>
                                        <?php
                                        $display_id = $user['quwan_id'] ?? $user['quwanplanet_id'] ?? '';
                                        if (!empty($display_id)):
                                        ?>
                                            <div class="user-id-badge">
                                                <span class="id-label">趣玩ID:</span>
                                                <span class="id-value"><?php echo htmlspecialchars($display_id); ?></span>
                                            </div>
                                        <?php endif; ?>
                                    </div>
                                </div>

                                <div class="user-meta-grid">
                                    <div class="meta-card">
                                        <div class="meta-icon">
                                            <i class="fas fa-phone" style="color: var(--primary-color);"></i>
                                        </div>
                                        <div class="meta-content">
                                            <div class="meta-label">手机号码</div>
                                            <div class="meta-value">
                                                <div class="sensitive-info-container">
                                                    <span class="sensitive-info" data-type="phone">
                                                        <?php echo formatSensitiveInfo($user['phone'], 'phone'); ?>
                                                    </span>
                                                    <button class="toggle-sensitive-btn" onclick="toggleSensitiveInfo(this, '<?php echo htmlspecialchars($user['phone']); ?>')">
                                                        <i class="fas fa-eye"></i>
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="meta-card">
                                        <div class="meta-icon">
                                            <i class="fas fa-envelope" style="color: var(--info-color);"></i>
                                        </div>
                                        <div class="meta-content">
                                            <div class="meta-label">邮箱地址</div>
                                            <div class="meta-value">
                                                <?php if (!empty($user['email'])): ?>
                                                    <div class="sensitive-info-container">
                                                        <span class="sensitive-info" data-type="email">
                                                            <?php echo formatSensitiveInfo($user['email'], 'email'); ?>
                                                        </span>
                                                        <button class="toggle-sensitive-btn" onclick="toggleSensitiveInfo(this, '<?php echo htmlspecialchars($user['email']); ?>')">
                                                            <i class="fas fa-eye"></i>
                                                        </button>
                                                    </div>
                                                <?php else: ?>
                                                    <span style="color: var(--gray-500);">未设置</span>
                                                <?php endif; ?>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="meta-card">
                                        <div class="meta-icon">
                                            <i class="fas fa-calendar" style="color: var(--success-color);"></i>
                                        </div>
                                        <div class="meta-content">
                                            <div class="meta-label">注册时间</div>
                                            <div class="meta-value"><?php echo date('Y-m-d H:i', strtotime($user['created_at'])); ?></div>
                                        </div>
                                    </div>

                                    <div class="meta-card">
                                        <div class="meta-icon">
                                            <i class="fas fa-clock" style="color: var(--warning-color);"></i>
                                        </div>
                                        <div class="meta-content">
                                            <div class="meta-label">最后登录</div>
                                            <div class="meta-value">
                                                <?php if (!empty($user['last_login'])): ?>
                                                    <?php echo date('Y-m-d H:i', strtotime($user['last_login'])); ?>
                                                <?php else: ?>
                                                    <span style="color: var(--gray-500);">从未登录</span>
                                                <?php endif; ?>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <?php if (!empty($user['bio'])): ?>
                                    <div class="user-bio-section">
                                        <div class="bio-icon">
                                            <i class="fas fa-quote-left"></i>
                                        </div>
                                        <div class="bio-content">
                                            <span class="bio-label">个人简介</span>
                                            <p class="bio-text"><?php echo htmlspecialchars($user['bio']); ?></p>
                                        </div>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 功能模块网格 -->
                <div class="user-detail-container">
                    <!-- 管理操作区域 -->
                    <div class="card full-width-section">
                        <div class="card-header">
                            <h3 class="card-title">
                                <i class="fas fa-tools"></i>
                                管理操作
                            </h3>
                        </div>
                        <div class="card-body">
                            <div class="action-buttons">
                                <button class="action-btn info" onclick="editUserInfo(<?php echo $user['id']; ?>)">
                                    <i class="fas fa-edit"></i>
                                    编辑用户信息
                                </button>
                                <button class="action-btn warning" onclick="resetPassword(<?php echo $user['id']; ?>)">
                                    <i class="fas fa-key"></i>
                                    重置密码
                                </button>

                                <!-- 封禁操作 -->
                                <button class="action-btn danger" onclick="banUser(<?php echo $user['id']; ?>)">
                                    <i class="fas fa-ban"></i>
                                    封号
                                </button>

                                <button class="action-btn danger" onclick="muteUser(<?php echo $user['id']; ?>)">
                                    <i class="fas fa-comment-slash"></i>
                                    禁言
                                </button>

                                <button class="action-btn danger" onclick="banIP(<?php echo $user['id']; ?>)">
                                    <i class="fas fa-shield-alt"></i>
                                    封IP
                                </button>

                                <button class="action-btn warning" onclick="banActivity(<?php echo $user['id']; ?>)">
                                    <i class="fas fa-calendar-times"></i>
                                    封活动
                                </button>
                                <button class="action-btn warning" onclick="banOrder(<?php echo $user['id']; ?>)">
                                    <i class="fas fa-shopping-cart"></i>
                                    封接单
                                </button>
                                <button class="action-btn warning" onclick="banGroup(<?php echo $user['id']; ?>)">
                                    <i class="fas fa-users"></i>
                                    封组局
                                </button>
                                <button class="action-btn warning" onclick="banPlaceOrder(<?php echo $user['id']; ?>)">
                                    <i class="fas fa-file-invoice"></i>
                                    封下单
                                </button>
                                <button class="action-btn info" onclick="writeLog(<?php echo $user['id']; ?>)">
                                    <i class="fas fa-clipboard-list"></i>
                                    写操作日志
                                </button>
                                <button class="action-btn info" onclick="viewDeviceInfo(<?php echo $user['id']; ?>)">
                                    <i class="fas fa-mobile-alt"></i>
                                    设备信息
                                </button>
                                <button class="action-btn info" onclick="viewIPInfo(<?php echo $user['id']; ?>)">
                                    <i class="fas fa-globe"></i>
                                    IP信息
                                </button>
                                <button class="action-btn info" onclick="sendSMS(<?php echo $user['id']; ?>)">
                                    <i class="fas fa-sms"></i>
                                    发送短信
                                </button>
                                <button class="action-btn primary" onclick="sendVerificationCode(<?php echo $user['id']; ?>)" style="background: linear-gradient(135deg, #FF6B6B 0%, #FF8E53 100%); color: white; border: none;">
                                    <i class="fas fa-key"></i>
                                    发送验证码
                                </button>
                                <button class="action-btn secondary" onclick="viewVerificationLogs(<?php echo $user['id']; ?>)" style="background: linear-gradient(135deg, #6F7BF5 0%, #9B59B6 100%); color: white; border: none;">
                                    <i class="fas fa-history"></i>
                                    验证码日志
                                </button>
                                <button class="action-btn info" onclick="sendSystemMessage(<?php echo $user['id']; ?>)">
                                    <i class="fas fa-envelope"></i>
                                    发送系统消息
                                </button>
                                <button class="action-btn info" onclick="viewUserProfile(<?php echo $user['id']; ?>)" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; border: none;">
                                    <i class="fas fa-user-chart"></i>
                                    用户画像
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- 详细信息模块 -->
                    <div class="detail-sections">
                        <!-- 个人信息 -->
                        <div class="card">
                            <div class="card-header">
                                <h3 class="card-title">
                                    <i class="fas fa-id-card"></i>
                                    个人信息
                                </h3>
                            </div>
                            <div class="card-body">
                                <div class="info-list">
                                    <div class="info-item">
                                        <span class="label">性别</span>
                                        <span class="value">
                                            <?php
                                            if ($user['gender'] === 'male') echo '👨 男';
                                            elseif ($user['gender'] === 'female') echo '👩 女';
                                            else echo '🤷 未设置';
                                            ?>
                                        </span>
                                    </div>
                                    <div class="info-item">
                                        <span class="label">生日</span>
                                        <span class="value">
                                            <?php if (!empty($user['birthday'])): ?>
                                                🎂 <?php echo date('Y年m月d日', strtotime($user['birthday'])); ?>
                                            <?php else: ?>
                                                <span style="color: var(--gray-500);">未设置</span>
                                            <?php endif; ?>
                                        </span>
                                    </div>
                                    <div class="info-item">
                                        <span class="label">地区</span>
                                        <span class="value">
                                            <?php if (!empty($user['region'])): ?>
                                                📍 <?php echo htmlspecialchars($user['region']); ?>
                                            <?php else: ?>
                                                <span style="color: var(--gray-500);">未设置</span>
                                            <?php endif; ?>
                                        </span>
                                    </div>
                                    <div class="info-item">
                                        <span class="label">账户状态</span>
                                        <span class="value">
                                            <?php if ($user['status'] === 'active' && !$user['is_banned']): ?>
                                                <span class="status-badge active">✅ 正常</span>
                                            <?php elseif ($user['is_banned']): ?>
                                                <span class="status-badge inactive">🚫 已封号</span>
                                                <button class="btn btn-sm btn-success" style="margin-left: 10px;" onclick="unbanUser(<?php echo $user['id']; ?>)">
                                                    <i class="fas fa-unlock"></i>
                                                    解封
                                                </button>
                                            <?php else: ?>
                                                <span class="status-badge inactive">❌ 禁用</span>
                                            <?php endif; ?>
                                        </span>
                                    </div>
                                    <div class="info-item">
                                        <span class="label">实名状态</span>
                                        <span class="value"><?php echo getVerificationBadge($user, true); ?></span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 实名认证信息 -->
                        <div class="card">
                            <div class="card-header">
                                <h3 class="card-title">
                                    <i class="fas fa-shield-check"></i>
                                    实名认证
                                </h3>
                            </div>
                            <div class="card-body">
                                <div class="info-list">
                                    <div class="info-item">
                                        <span class="label">认证状态</span>
                                        <span class="value"><?php echo getVerificationBadge($user, true); ?></span>
                                    </div>
                                    <div class="info-item">
                                        <span class="label">真实姓名</span>
                                        <span class="value">
                                            <?php if (!empty($user['real_name'])): ?>
                                                <?php echo htmlspecialchars($user['real_name']); ?>
                                            <?php else: ?>
                                                <span style="color: var(--gray-500);">未提交</span>
                                            <?php endif; ?>
                                        </span>
                                    </div>
                                    <div class="info-item">
                                        <span class="label">身份证号</span>
                                        <span class="value">
                                            <?php if (!empty($user['id_card_number'])): ?>
                                                <div class="sensitive-info-container">
                                                    <span class="sensitive-info" data-type="idcard">
                                                        <?php echo formatSensitiveInfo($user['id_card_number'], 'idcard'); ?>
                                                    </span>
                                                    <button class="toggle-sensitive-btn" onclick="toggleSensitiveInfo(this, '<?php echo htmlspecialchars($user['id_card_number']); ?>')">
                                                        <i class="fas fa-eye"></i>
                                                    </button>
                                                </div>
                                            <?php else: ?>
                                                <span style="color: var(--gray-500);">未提交</span>
                                            <?php endif; ?>
                                        </span>
                                    </div>
                                    <div class="info-item">
                                        <span class="label">提交时间</span>
                                        <span class="value">
                                            <?php if (!empty($user['submitted_at'])): ?>
                                                <?php echo date('Y-m-d H:i', strtotime($user['submitted_at'])); ?>
                                            <?php else: ?>
                                                <span style="color: var(--gray-500);">未提交</span>
                                            <?php endif; ?>
                                        </span>
                                    </div>
                                    <div class="info-item">
                                        <span class="label">认证时间</span>
                                        <span class="value">
                                            <?php if (!empty($user['verified_at'])): ?>
                                                <?php echo date('Y-m-d H:i', strtotime($user['verified_at'])); ?>
                                            <?php else: ?>
                                                <span style="color: var(--gray-500);">未认证</span>
                                            <?php endif; ?>
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 会员信息 -->
                        <div class="card">
                            <div class="card-header">
                                <h3 class="card-title">
                                    <i class="fas fa-crown"></i>
                                    会员信息
                                </h3>
                            </div>
                            <div class="card-body">
                                <div class="info-list">
                                    <div class="info-item">
                                        <span class="label">会员等级</span>
                                        <span class="value">
                                            <?php
                                            $membership_level = $user['membership_level'] ?? 'normal';
                                            if ($membership_level === 'vip') {
                                                echo '<span class="status-badge active"><i class="fas fa-crown"></i> VIP会员</span>';
                                            } elseif ($membership_level === 'svip') {
                                                echo '<span class="status-badge active" style="background: linear-gradient(135deg, #FFD700, #FFA500);"><i class="fas fa-gem"></i> 超级VIP</span>';
                                            } else {
                                                echo '<span class="status-badge inactive"><i class="fas fa-user"></i> 普通用户</span>';
                                            }
                                            ?>
                                        </span>
                                    </div>
                                    <div class="info-item">
                                        <span class="label">会员到期</span>
                                        <span class="value">
                                            <?php if (!empty($user['membership_expire']) && strtotime($user['membership_expire']) > time()): ?>
                                                <?php echo date('Y-m-d', strtotime($user['membership_expire'])); ?>
                                            <?php else: ?>
                                                <span style="color: var(--gray-500);">非会员</span>
                                            <?php endif; ?>
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 钱包信息 -->
                        <div class="card">
                            <div class="card-header">
                                <h3 class="card-title">
                                    <i class="fas fa-wallet"></i>
                                    钱包信息
                                </h3>
                            </div>
                            <div class="card-body">
                                <div class="info-list">
                                    <div class="info-item">
                                        <span class="label">账户余额</span>
                                        <span class="value">
                                            <span style="color: var(--success-color); font-weight: 700;">¥<?php echo number_format($user['balance'] ?? 0, 2); ?></span>
                                        </span>
                                    </div>
                                    <div class="info-item">
                                        <span class="label">冻结金额</span>
                                        <span class="value">
                                            <span style="color: var(--warning-color); font-weight: 700;">¥<?php echo number_format($user['frozen_balance'] ?? 0, 2); ?></span>
                                        </span>
                                    </div>
                                    <div class="info-item">
                                        <span class="label">累计充值</span>
                                        <span class="value">¥<?php echo number_format($user['total_recharge'] ?? 0, 2); ?></span>
                                    </div>
                                    <div class="info-item">
                                        <span class="label">累计提现</span>
                                        <span class="value">¥<?php echo number_format($user['total_withdraw'] ?? 0, 2); ?></span>
                                    </div>
                                    <div class="info-item">
                                        <span class="label">累计消费</span>
                                        <span class="value">¥<?php echo number_format($user['total_consumption'] ?? 0, 2); ?></span>
                                    </div>
                                    <div class="info-item">
                                        <span class="label">总收益</span>
                                        <span class="value">
                                            <span style="color: var(--primary-color); font-weight: 700;">¥<?php echo number_format($user['total_earnings'] ?? 0, 2); ?></span>
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 用户等级体系 -->
                        <div class="card">
                            <div class="card-header">
                                <h3 class="card-title">
                                    <i class="fas fa-star"></i>
                                    用户等级
                                </h3>
                            </div>
                            <div class="card-body">
                                <div class="info-list">
                                    <div class="info-item">
                                        <span class="label">当前等级</span>
                                        <span class="value">
                                            <?php echo htmlspecialchars($user['level_icon'] ?? '🌱'); ?>
                                            Lv.<?php echo $user['level'] ?? 1; ?>
                                            <?php echo htmlspecialchars($user['level_name'] ?? '新手'); ?>
                                        </span>
                                    </div>
                                    <div class="info-item">
                                        <span class="label">经验值</span>
                                        <span class="value"><?php echo number_format($user['experience'] ?? 0); ?> EXP</span>
                                    </div>
                                    <div class="info-item">
                                        <span class="label">积分</span>
                                        <span class="value"><?php echo number_format($user['points'] ?? 0); ?> 分</span>
                                    </div>
                                    <div class="info-item">
                                        <span class="label">陪玩认证</span>
                                        <span class="value">
                                            <?php if ($user['is_companion']): ?>
                                                <span class="status-badge active">🎮 已认证</span>
                                            <?php else: ?>
                                                <span class="status-badge inactive">❌ 未认证</span>
                                            <?php endif; ?>
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 社交数据 -->
                        <div class="card">
                            <div class="card-header">
                                <h3 class="card-title">
                                    <i class="fas fa-users"></i>
                                    社交数据
                                </h3>
                            </div>
                            <div class="card-body">
                                <div class="info-list">
                                    <div class="info-item">
                                        <span class="label">粉丝数</span>
                                        <span class="value"><?php echo number_format($user['followers_count'] ?? 0); ?> 人</span>
                                    </div>
                                    <div class="info-item">
                                        <span class="label">关注数</span>
                                        <span class="value"><?php echo number_format($user['following_count'] ?? 0); ?> 人</span>
                                    </div>
                                    <div class="info-item">
                                        <span class="label">获赞数</span>
                                        <span class="value"><?php echo number_format($user['likes_received'] ?? 0); ?> 个</span>
                                    </div>
                                    <div class="info-item">
                                        <span class="label">收藏数</span>
                                        <span class="value"><?php echo number_format($user['favorites_count'] ?? 0); ?> 个</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 统计信息 -->
                        <div class="card">
                            <div class="card-header">
                                <h3 class="card-title">
                                    <i class="fas fa-chart-bar"></i>
                                    统计信息
                                </h3>
                            </div>
                            <div class="card-body">
                                <div class="info-list">
                                    <div class="info-item">
                                        <span class="label">注册天数</span>
                                        <span class="value">
                                            <?php
                                            $days = floor((time() - strtotime($user['created_at'])) / 86400);
                                            echo $days . ' 天';
                                            ?>
                                        </span>
                                    </div>
                                    <div class="info-item">
                                        <span class="label">作品数量</span>
                                        <span class="value"><?php echo number_format($works_count ?? 0); ?> 个</span>
                                    </div>
                                    <div class="info-item">
                                        <span class="label">订单数量</span>
                                        <span class="value"><?php echo number_format($orders_count ?? 0); ?> 个</span>
                                    </div>
                                    <div class="info-item">
                                        <span class="label">优惠券</span>
                                        <span class="value"><?php echo number_format($coupons_count ?? 0); ?> 张</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 用户勋章 -->
                        <div class="card">
                            <div class="card-header">
                                <h3 class="card-title">
                                    <i class="fas fa-medal"></i>
                                    用户勋章
                                </h3>
                            </div>
                            <div class="card-body">
                                <?php if (!empty($badges)): ?>
                                    <div style="display: grid; grid-template-columns: repeat(auto-fill, minmax(200px, 1fr)); gap: var(--spacing-md);">
                                        <?php foreach ($badges as $badge): ?>
                                            <div style="display: flex; align-items: center; gap: var(--spacing-sm); padding: var(--spacing-md); background: var(--gray-50); border-radius: var(--border-radius-lg); border: 2px solid transparent; transition: var(--transition);" onmouseover="this.style.borderColor='var(--primary-color)'" onmouseout="this.style.borderColor='transparent'">
                                                <div style="font-size: 24px;"><?php echo htmlspecialchars($badge['badge_icon']); ?></div>
                                                <div>
                                                    <div style="font-weight: 600; color: var(--gray-900);"><?php echo htmlspecialchars($badge['badge_name']); ?></div>
                                                    <div style="font-size: var(--font-size-sm); color: var(--gray-600);"><?php echo date('Y-m-d', strtotime($badge['earned_at'])); ?></div>
                                                </div>
                                            </div>
                                        <?php endforeach; ?>
                                    </div>
                                <?php else: ?>
                                    <p style="color: var(--gray-500); text-align: center; padding: var(--spacing-lg);">
                                        暂无勋章
                                    </p>
                                <?php endif; ?>
                            </div>
                        </div>

                        <!-- 用户背包 -->
                        <div class="card">
                            <div class="card-header">
                                <h3 class="card-title">
                                    <i class="fas fa-box"></i>
                                    用户背包
                                </h3>
                            </div>
                            <div class="card-body">
                                <?php if (!empty($inventory)): ?>
                                    <div style="display: grid; grid-template-columns: repeat(auto-fill, minmax(200px, 1fr)); gap: var(--spacing-md);">
                                        <?php foreach ($inventory as $item): ?>
                                            <div style="display: flex; align-items: center; gap: var(--spacing-sm); padding: var(--spacing-md); background: var(--gray-50); border-radius: var(--border-radius-lg); border: 2px solid transparent; transition: var(--transition);" onmouseover="this.style.borderColor='var(--primary-color)'" onmouseout="this.style.borderColor='transparent'">
                                                <div style="font-size: 24px;"><?php echo htmlspecialchars($item['item_icon']); ?></div>
                                                <div style="flex: 1;">
                                                    <div style="font-weight: 600; color: var(--gray-900);"><?php echo htmlspecialchars($item['item_name']); ?></div>
                                                    <div style="font-size: var(--font-size-sm); color: var(--gray-600);">数量: <?php echo $item['quantity']; ?></div>
                                                </div>
                                            </div>
                                        <?php endforeach; ?>
                                    </div>
                                <?php else: ?>
                                    <p style="color: var(--gray-500); text-align: center; padding: var(--spacing-lg);">
                                        背包为空
                                    </p>
                                <?php endif; ?>
                            </div>
                        </div>

                        <!-- 签到信息 -->
                        <div class="card">
                            <div class="card-header">
                                <h3 class="card-title">
                                    <i class="fas fa-calendar-check"></i>
                                    签到信息
                                </h3>
                            </div>
                            <div class="card-body">
                                <?php if (!empty($checkins)): ?>
                                    <div class="info-list">
                                        <div class="info-item">
                                            <span class="label">最近签到</span>
                                            <span class="value"><?php echo date('Y-m-d', strtotime($checkins[0]['checkin_date'])); ?></span>
                                        </div>
                                        <div class="info-item">
                                            <span class="label">连续签到</span>
                                            <span class="value"><?php echo $checkins[0]['consecutive_days']; ?> 天</span>
                                        </div>
                                        <div class="info-item">
                                            <span class="label">签到次数</span>
                                            <span class="value"><?php echo count($checkins); ?> 次</span>
                                        </div>
                                    </div>
                                    <div style="margin-top: var(--spacing-md);">
                                        <h5 style="margin-bottom: var(--spacing-sm); color: var(--gray-700);">最近签到记录</h5>
                                        <div style="display: flex; gap: var(--spacing-xs); flex-wrap: wrap;">
                                            <?php foreach (array_slice($checkins, 0, 7) as $checkin): ?>
                                                <div style="padding: var(--spacing-xs) var(--spacing-sm); background: var(--success-color); color: white; border-radius: var(--border-radius); font-size: var(--font-size-sm);">
                                                    <?php echo date('m-d', strtotime($checkin['checkin_date'])); ?>
                                                </div>
                                            <?php endforeach; ?>
                                        </div>
                                    </div>
                                <?php else: ?>
                                    <p style="color: var(--gray-500); text-align: center; padding: var(--spacing-lg);">
                                        暂无签到记录
                                    </p>
                                <?php endif; ?>
                            </div>
                        </div>

                        <!-- 收益详情 -->
                        <div class="card">
                            <div class="card-header">
                                <h3 class="card-title">
                                    <i class="fas fa-coins"></i>
                                    收益详情
                                </h3>
                            </div>
                            <div class="card-body">
                                <?php if (!empty($earnings)): ?>
                                    <div style="max-height: 300px; overflow-y: auto;">
                                        <?php foreach ($earnings as $earning): ?>
                                            <div style="display: flex; justify-content: space-between; align-items: center; padding: var(--spacing-md) 0; border-bottom: 1px solid var(--gray-200);">
                                                <div>
                                                    <div style="font-weight: 600; color: var(--gray-900);"><?php echo htmlspecialchars($earning['description']); ?></div>
                                                    <div style="font-size: var(--font-size-sm); color: var(--gray-600);"><?php echo date('Y-m-d H:i', strtotime($earning['created_at'])); ?></div>
                                                </div>
                                                <div style="text-align: right;">
                                                    <div style="font-weight: 700; color: var(--success-color);">+¥<?php echo number_format($earning['amount'], 2); ?></div>
                                                    <div style="font-size: var(--font-size-sm); color: var(--gray-600);">
                                                        <?php
                                                        $status_text = ['pending' => '待确认', 'confirmed' => '已确认', 'cancelled' => '已取消'];
                                                        echo $status_text[$earning['status']] ?? $earning['status'];
                                                        ?>
                                                    </div>
                                                </div>
                                            </div>
                                        <?php endforeach; ?>
                                    </div>
                                <?php else: ?>
                                    <p style="color: var(--gray-500); text-align: center; padding: var(--spacing-lg);">
                                        暂无收益记录
                                    </p>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>

                    <!-- 操作日志 - 独立的全宽度板块 -->
                    <div class="card full-width-section">
                        <div class="card-header" style="position: relative; display: flex; justify-content: space-between; align-items: center;">
                            <h3 class="card-title">
                                <i class="fas fa-history"></i>
                                日志记录
                            </h3>
                            <button class="btn btn-primary" onclick="writeLog(<?php echo $user['id']; ?>)" style="background: var(--primary-gradient); border: none; padding: 8px 16px; border-radius: 6px; color: white; font-weight: 600; cursor: pointer; transition: var(--transition); font-size: 14px;">
                                <i class="fas fa-plus"></i>
                                添加日志
                            </button>
                        </div>
                        <div class="card-body">
                            <?php if (!empty($operation_logs)): ?>
                                <div style="overflow-x: auto;">
                                    <table class="data-table" style="white-space: nowrap;">
                                        <thead>
                                            <tr>
                                                <th style="width: 140px;">操作时间</th>
                                                <th style="width: 80px;">操作人员</th>
                                                <th style="width: 60px;">工号</th>
                                                <th style="width: 80px;">部门</th>
                                                <th style="width: 100px;">操作类型</th>
                                                <th style="width: 200px;">操作原因</th>
                                                <th style="width: 120px;">IP地址</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php foreach ($operation_logs as $log): ?>
                                                <tr>
                                                    <td style="white-space: nowrap;"><?php echo date('Y-m-d H:i:s', strtotime($log['created_at'])); ?></td>
                                                    <td style="white-space: nowrap;"><?php echo htmlspecialchars($log['admin_name']); ?></td>
                                                    <td style="white-space: nowrap;"><?php echo htmlspecialchars($log['employee_id'] ?? '-'); ?></td>
                                                    <td style="white-space: nowrap;"><?php echo htmlspecialchars($log['department'] ?? '-'); ?></td>
                                                    <td style="white-space: nowrap;">
                                                        <?php
                                                        $operationType = $log['operation_type'];
                                                        $badgeClass = 'secondary';

                                                        // 根据操作类型设置不同颜色
                                                        switch($operationType) {
                                                            case '封号':
                                                            case '封IP':
                                                            case '禁言':
                                                                $badgeClass = 'danger';
                                                                break;
                                                            case '解封':
                                                            case '解禁':
                                                                $badgeClass = 'success';
                                                                break;
                                                            case '编辑用户信息':
                                                            case '重置密码':
                                                                $badgeClass = 'warning';
                                                                break;
                                                            case '查看用户详情':
                                                            case '查看敏感信息':
                                                                $badgeClass = 'info';
                                                                break;
                                                            case '发送短信':
                                                            case '发送消息':
                                                                $badgeClass = 'primary';
                                                                break;
                                                            default:
                                                                $badgeClass = 'secondary';
                                                        }
                                                        ?>
                                                        <span class="status-badge <?php echo $badgeClass; ?>"><?php echo htmlspecialchars($operationType); ?></span>
                                                    </td>
                                                    <td style="max-width: 200px; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;" title="<?php echo htmlspecialchars($log['operation_reason'] ?? '-'); ?>">
                                                        <?php echo htmlspecialchars($log['operation_reason'] ?? '-'); ?>
                                                    </td>
                                                    <td style="white-space: nowrap;"><?php echo htmlspecialchars($log['ip_address'] ?? '-'); ?></td>
                                                </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                    </table>
                                </div>
                            <?php else: ?>
                                <p style="color: var(--gray-500); text-align: center; padding: var(--spacing-lg);">
                                    暂无操作日志
                                </p>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 弹窗模态框 -->
    <!-- 实名认证详情弹窗 -->
    <div id="verificationModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title">实名认证详情</h3>
                <button class="modal-close" onclick="closeModal('verificationModal')">&times;</button>
            </div>
            <div class="modal-body">
                <div class="info-list">
                    <div class="info-item">
                        <span class="label">认证状态</span>
                        <span class="value" id="modalVerificationStatus"></span>
                    </div>
                    <div class="info-item">
                        <span class="label">真实姓名</span>
                        <span class="value" id="modalRealName"></span>
                    </div>
                    <div class="info-item">
                        <span class="label">身份证号</span>
                        <span class="value" id="modalIdCard"></span>
                    </div>
                    <div class="info-item">
                        <span class="label">提交时间</span>
                        <span class="value" id="modalSubmitTime"></span>
                    </div>
                    <div class="info-item">
                        <span class="label">审核时间</span>
                        <span class="value" id="modalReviewTime"></span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 设备信息弹窗 -->
    <div id="deviceModal" class="modal">
        <div class="modal-content" style="max-width: 800px;">
            <div class="modal-header">
                <h3 class="modal-title">
                    <i class="fas fa-mobile-alt"></i>
                    设备信息记录
                </h3>
                <button class="modal-close" onclick="closeModal('deviceModal')">&times;</button>
            </div>
            <div class="modal-body">
                <div id="deviceInfoLoading" style="text-align: center; padding: var(--spacing-xl);">
                    <i class="fas fa-spinner fa-spin" style="font-size: 24px; color: var(--primary-color);"></i>
                    <p style="margin-top: var(--spacing-md); color: var(--gray-600);">正在加载设备信息...</p>
                </div>
                <div id="deviceInfoContent" style="display: none;">
                    <!-- 设备信息内容将通过JavaScript动态加载 -->
                </div>
                <div id="deviceInfoEmpty" style="display: none; text-align: center; padding: var(--spacing-xl);">
                    <i class="fas fa-mobile-alt" style="font-size: 48px; color: var(--gray-300); margin-bottom: var(--spacing-md);"></i>
                    <p style="color: var(--gray-500); margin: 0;">暂无设备信息记录</p>
                </div>
            </div>
        </div>
    </div>

    <!-- IP信息弹窗 -->
    <div id="ipModal" class="modal">
        <div class="modal-content" style="max-width: 900px;">
            <div class="modal-header">
                <h3 class="modal-title">
                    <i class="fas fa-globe"></i>
                    IP记录与分析
                </h3>
                <button class="modal-close" onclick="closeModal('ipModal')">&times;</button>
            </div>
            <div class="modal-body">
                <div id="ipInfoLoading" style="text-align: center; padding: var(--spacing-xl);">
                    <i class="fas fa-spinner fa-spin" style="font-size: 24px; color: var(--primary-color);"></i>
                    <p style="margin-top: var(--spacing-md); color: var(--gray-600);">正在加载IP信息...</p>
                </div>
                <div id="ipInfoContent" style="display: none;">
                    <!-- IP信息内容将通过JavaScript动态加载 -->
                </div>
                <div id="ipInfoEmpty" style="display: none; text-align: center; padding: var(--spacing-xl);">
                    <i class="fas fa-globe" style="font-size: 48px; color: var(--gray-300); margin-bottom: var(--spacing-md);"></i>
                    <p style="color: var(--gray-500); margin: 0;">暂无IP记录</p>
                </div>
            </div>
        </div>
    </div>

    <!-- 编辑用户信息弹窗 -->
    <div id="editModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title">编辑用户信息</h3>
                <button class="modal-close" onclick="closeModal('editModal')">&times;</button>
            </div>
            <div class="modal-body">
                <form id="editUserForm">
                    <div class="form-group">
                        <label class="form-label">用户名</label>
                        <input type="text" class="form-control" id="editUsername" value="<?php echo htmlspecialchars($user['username']); ?>">
                    </div>
                    <div class="form-group">
                        <label class="form-label">趣玩ID</label>
                        <input type="text" class="form-control" id="editQuwanId" value="<?php echo htmlspecialchars($user['quwan_id'] ?? $user['quwanplanet_id'] ?? ''); ?>" placeholder="7位数字ID">
                    </div>
                    <div class="form-group">
                        <label class="form-label">手机号</label>
                        <input type="text" class="form-control" id="editPhone" value="<?php echo htmlspecialchars($user['phone']); ?>">
                    </div>
                    <div class="form-group">
                        <label class="form-label">邮箱</label>
                        <input type="email" class="form-control" id="editEmail" value="<?php echo htmlspecialchars($user['email']); ?>">
                    </div>
                    <div class="form-group">
                        <label class="form-label">性别</label>
                        <select class="form-control" id="editGender">
                            <option value="">未设置</option>
                            <option value="male" <?php echo $user['gender'] === 'male' ? 'selected' : ''; ?>>男</option>
                            <option value="female" <?php echo $user['gender'] === 'female' ? 'selected' : ''; ?>>女</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label class="form-label">地区</label>
                        <input type="text" class="form-control" id="editRegion" value="<?php echo htmlspecialchars($user['region']); ?>">
                    </div>
                    <div class="form-group">
                        <label class="form-label">个人简介</label>
                        <textarea class="form-control" id="editBio" rows="3"><?php echo htmlspecialchars($user['bio']); ?></textarea>
                    </div>
                    <div style="display: flex; gap: var(--spacing-md); justify-content: flex-end; margin-top: var(--spacing-xl);">
                        <button type="button" class="btn btn-secondary" onclick="closeModal('editModal')">取消</button>
                        <button type="submit" class="btn btn-primary">保存</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- 发送短信弹窗 -->
    <div id="smsModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title">发送短信</h3>
                <button class="modal-close" onclick="closeModal('smsModal')">&times;</button>
            </div>
            <div class="modal-body">
                <form id="smsForm">
                    <div class="form-group">
                        <label class="form-label">接收手机号</label>
                        <input type="text" class="form-control" id="smsPhone" value="<?php echo htmlspecialchars($user['phone']); ?>" readonly>
                    </div>
                    <div class="form-group">
                        <label class="form-label">短信类型</label>
                        <select class="form-control" id="smsType" required>
                            <option value="">请选择短信类型</option>
                            <option value="notification">通知短信</option>
                            <option value="marketing">营销短信</option>
                            <option value="system">系统短信</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label class="form-label">短信内容</label>
                        <textarea class="form-control" id="smsContent" rows="4" placeholder="请输入短信内容..." required></textarea>
                        <div style="font-size: var(--font-size-sm); color: var(--gray-600); margin-top: var(--spacing-xs);">
                            字数统计: <span id="smsCount">0</span>/70
                        </div>
                    </div>
                    <div style="display: flex; gap: var(--spacing-md); justify-content: flex-end; margin-top: var(--spacing-xl);">
                        <button type="button" class="btn btn-secondary" onclick="closeModal('smsModal')">取消</button>
                        <button type="submit" class="btn btn-primary">发送短信</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- 发送系统消息弹窗 -->
    <div id="messageModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title">发送系统消息</h3>
                <button class="modal-close" onclick="closeModal('messageModal')">&times;</button>
            </div>
            <div class="modal-body">
                <form id="messageForm">
                    <div class="form-group">
                        <label class="form-label">接收用户</label>
                        <input type="text" class="form-control" value="<?php echo htmlspecialchars($user['username']); ?> (ID: <?php echo $user['id']; ?>)" readonly>
                    </div>
                    <div class="form-group">
                        <label class="form-label">消息类型</label>
                        <select class="form-control" id="messageType" required>
                            <option value="">请选择消息类型</option>
                            <option value="system">系统消息</option>
                            <option value="notification">通知消息</option>
                            <option value="warning">警告消息</option>
                            <option value="promotion">推广消息</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label class="form-label">消息标题</label>
                        <input type="text" class="form-control" id="messageTitle" placeholder="请输入消息标题..." required>
                    </div>
                    <div class="form-group">
                        <label class="form-label">消息内容</label>
                        <textarea class="form-control" id="messageContent" rows="5" placeholder="请输入消息内容..." required></textarea>
                    </div>
                    <div style="display: flex; gap: var(--spacing-md); justify-content: flex-end; margin-top: var(--spacing-xl);">
                        <button type="button" class="btn btn-secondary" onclick="closeModal('messageModal')">取消</button>
                        <button type="submit" class="btn btn-primary">发送消息</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- 发送验证码弹窗 -->
    <div id="verificationCodeModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title">
                    <i class="fas fa-key"></i>
                    发送验证码
                </h3>
                <button class="modal-close" onclick="closeModal('verificationCodeModal')">&times;</button>
            </div>
            <div class="modal-body">
                <form id="verificationCodeForm">
                    <div class="form-group">
                        <label class="form-label">接收用户</label>
                        <input type="text" class="form-control" value="<?php echo htmlspecialchars($user['username']); ?> (ID: <?php echo $user['id']; ?>)" readonly>
                    </div>
                    <div class="form-group">
                        <label class="form-label">接收手机号</label>
                        <input type="text" class="form-control" id="verificationPhone" value="<?php echo htmlspecialchars($user['phone']); ?>" readonly>
                    </div>
                    <div class="form-group">
                        <label class="form-label">验证码类型</label>
                        <select class="form-control" id="verificationType" required>
                            <option value="">请选择验证码类型</option>
                            <option value="admin_send">管理员发送</option>
                            <option value="security_verify">安全验证</option>
                            <option value="system_notice">系统通知</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label class="form-label">管理员备注</label>
                        <textarea class="form-control" id="verificationNote" rows="3" placeholder="请输入发送验证码的原因或备注..." required></textarea>
                        <div style="font-size: var(--font-size-sm); color: var(--gray-600); margin-top: var(--spacing-xs);">
                            此备注将记录在系统日志中，便于后续追踪
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="form-label">验证码有效期</label>
                        <select class="form-control" id="verificationExpiry" required>
                            <option value="5">5分钟</option>
                            <option value="10">10分钟</option>
                            <option value="15">15分钟</option>
                            <option value="30">30分钟</option>
                        </select>
                    </div>
                    <div style="background: #FEF3C7; border: 1px solid #F59E0B; border-radius: 8px; padding: 16px; margin: 16px 0;">
                        <div style="display: flex; align-items: center; gap: 8px; color: #92400E;">
                            <i class="fas fa-exclamation-triangle"></i>
                            <strong>重要提醒</strong>
                        </div>
                        <div style="color: #92400E; font-size: 14px; margin-top: 8px; line-height: 1.5;">
                            • 验证码将实时推送到用户前台界面<br>
                            • 用户会立即收到验证码弹窗提醒<br>
                            • 请确保发送验证码的必要性和合理性
                        </div>
                    </div>
                    <div style="display: flex; gap: var(--spacing-md); justify-content: flex-end; margin-top: var(--spacing-xl);">
                        <button type="button" class="btn btn-secondary" onclick="closeModal('verificationCodeModal')">取消</button>
                        <button type="submit" class="btn btn-primary" style="background: linear-gradient(135deg, #FF6B6B 0%, #FF8E53 100%); border: none;">
                            <i class="fas fa-paper-plane"></i>
                            立即发送
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- 用户画像弹窗 -->
    <div id="profileModal" class="modal">
        <div class="modal-content" style="max-width: 1200px; max-height: 90vh;">
            <div class="modal-header">
                <h3 class="modal-title">
                    <i class="fas fa-user-chart"></i>
                    用户画像分析
                </h3>
                <button class="modal-close" onclick="closeModal('profileModal')">&times;</button>
            </div>
            <div class="modal-body">
                <div id="profileLoading" style="text-align: center; padding: var(--spacing-xl);">
                    <i class="fas fa-spinner fa-spin" style="font-size: 24px; color: var(--primary-color);"></i>
                    <p style="margin-top: var(--spacing-md); color: var(--gray-600);">正在分析用户画像...</p>
                </div>
                <div id="profileContent" style="display: none;">
                    <!-- 用户画像内容将通过JavaScript动态加载 -->
                </div>
                <div id="profileEmpty" style="display: none; text-align: center; padding: var(--spacing-xl);">
                    <i class="fas fa-chart-line" style="font-size: 48px; color: var(--gray-400); margin-bottom: var(--spacing-md);"></i>
                    <p style="color: var(--gray-500);">暂无足够数据进行画像分析</p>
                </div>
            </div>
        </div>
    </div>

    <!-- 添加操作日志弹窗 -->
    <div id="logModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title">添加操作日志</h3>
                <button class="modal-close" onclick="closeModal('logModal')">&times;</button>
            </div>
            <div class="modal-body">
                <form id="logForm">
                    <div class="form-group">
                        <label class="form-label">操作类型</label>
                        <select class="form-control" id="logType" required>
                            <option value="">请选择操作类型</option>
                            <option value="查看用户详情">查看用户详情</option>
                            <option value="编辑用户信息">编辑用户信息</option>
                            <option value="重置密码">重置密码</option>
                            <option value="封号操作">封号操作</option>
                            <option value="禁言操作">禁言操作</option>
                            <option value="发送短信">发送短信</option>
                            <option value="发送消息">发送消息</option>
                            <option value="其他操作">其他操作</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label class="form-label">操作内容</label>
                        <textarea class="form-control" id="logContent" rows="3" placeholder="请详细描述操作内容..." required></textarea>
                    </div>
                    <div class="form-group">
                        <label class="form-label">操作原因</label>
                        <textarea class="form-control" id="logReason" rows="2" placeholder="请说明操作原因..."></textarea>
                    </div>
                    <div style="display: flex; gap: var(--spacing-md); justify-content: flex-end; margin-top: var(--spacing-xl);">
                        <button type="button" class="btn btn-secondary" onclick="closeModal('logModal')">取消</button>
                        <button type="submit" class="btn btn-primary">保存日志</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- 封号弹窗 -->
    <div id="banModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title" id="banModalTitle">封号操作</h3>
                <button class="modal-close" onclick="closeModal('banModal')">&times;</button>
            </div>
            <div class="modal-body">
                <!-- 封号详情显示区域 -->
                <div id="banDetailsSection" style="display: none;">
                    <div style="background: #FEF2F2; border: 1px solid #FECACA; border-radius: 8px; padding: 16px; margin-bottom: 20px;">
                        <h4 style="color: #DC2626; margin: 0 0 12px 0; font-size: 16px;">
                            <i class="fas fa-exclamation-triangle"></i>
                            该用户已被封号
                        </h4>
                        <div style="color: #7F1D1D; font-size: 14px; line-height: 1.5;">
                            <p style="margin: 4px 0;"><strong>封号时间：</strong><span id="banStartTime">-</span></p>
                            <p style="margin: 4px 0;"><strong>到期时间：</strong><span id="banEndTime">-</span></p>
                            <p style="margin: 4px 0;"><strong>封号管理员：</strong><span id="banAdminName">-</span></p>
                            <p style="margin: 4px 0;"><strong>封号原因：</strong><span id="banReason">-</span></p>
                        </div>
                    </div>

                    <!-- 解封表单 -->
                    <form id="unbanForm">
                        <div class="form-group">
                            <label class="form-label">解封原因</label>
                            <textarea class="form-control" id="unbanReason" rows="3" placeholder="请详细说明解封原因..." required></textarea>
                        </div>
                        <div style="display: flex; gap: var(--spacing-md); justify-content: flex-end; margin-top: var(--spacing-xl);">
                            <button type="button" class="btn btn-secondary" onclick="closeModal('banModal')">取消</button>
                            <button type="submit" class="btn btn-success">
                                <i class="fas fa-unlock"></i>
                                确认解封
                            </button>
                        </div>
                    </form>
                </div>

                <!-- 封号表单 -->
                <form id="banForm">
                    <div class="form-group">
                        <label class="form-label">封号时长</label>
                        <select class="form-control" id="banDuration" required>
                            <option value="">请选择封号时长</option>
                            <option value="1">1天</option>
                            <option value="3">3天</option>
                            <option value="7">7天</option>
                            <option value="15">15天</option>
                            <option value="30">30天</option>
                            <option value="90">90天</option>
                            <option value="365">1年</option>
                            <option value="permanent">永久封号</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label class="form-label">封号原因</label>
                        <textarea class="form-control" id="banReasonInput" rows="3" placeholder="请详细说明封号原因..." required></textarea>
                    </div>
                    <div style="display: flex; gap: var(--spacing-md); justify-content: flex-end; margin-top: var(--spacing-xl);">
                        <button type="button" class="btn btn-secondary" onclick="closeModal('banModal')">取消</button>
                        <button type="submit" class="btn btn-danger">确认封号</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- 禁言弹窗 -->
    <div id="muteModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title" id="muteModalTitle">禁言操作</h3>
                <button class="modal-close" onclick="closeModal('muteModal')">&times;</button>
            </div>
            <div class="modal-body">
                <!-- 禁言详情显示区域 -->
                <div id="muteDetailsSection" style="display: none;">
                    <div style="background: #FFFBEB; border: 1px solid #FDE68A; border-radius: 8px; padding: 16px; margin-bottom: 20px;">
                        <h4 style="color: #D97706; margin: 0 0 12px 0; font-size: 16px;">
                            <i class="fas fa-comment-slash"></i>
                            该用户已被禁言
                        </h4>
                        <div style="color: #92400E; font-size: 14px; line-height: 1.5;">
                            <p style="margin: 4px 0;"><strong>禁言时间：</strong><span id="muteStartTime">-</span></p>
                            <p style="margin: 4px 0;"><strong>到期时间：</strong><span id="muteEndTime">-</span></p>
                            <p style="margin: 4px 0;"><strong>禁言管理员：</strong><span id="muteAdminName">-</span></p>
                            <p style="margin: 4px 0;"><strong>禁言原因：</strong><span id="muteReasonDisplay">-</span></p>
                        </div>
                    </div>

                    <!-- 解禁表单 -->
                    <form id="unmuteForm">
                        <div class="form-group">
                            <label class="form-label">解禁原因</label>
                            <textarea class="form-control" id="unmuteReason" rows="3" placeholder="请详细说明解禁原因..." required></textarea>
                        </div>
                        <div style="display: flex; gap: var(--spacing-md); justify-content: flex-end; margin-top: var(--spacing-xl);">
                            <button type="button" class="btn btn-secondary" onclick="closeModal('muteModal')">取消</button>
                            <button type="submit" class="btn btn-success">
                                <i class="fas fa-comment"></i>
                                确认解禁
                            </button>
                        </div>
                    </form>
                </div>

                <!-- 禁言表单 -->
                <form id="muteForm">
                    <div class="form-group">
                        <label class="form-label">禁言时长</label>
                        <select class="form-control" id="muteDuration" required>
                            <option value="">请选择禁言时长</option>
                            <option value="1">1小时</option>
                            <option value="6">6小时</option>
                            <option value="24">1天</option>
                            <option value="72">3天</option>
                            <option value="168">7天</option>
                            <option value="720">30天</option>
                            <option value="permanent">永久禁言</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label class="form-label">禁言原因</label>
                        <textarea class="form-control" id="muteReasonInput" rows="3" placeholder="请详细说明禁言原因..." required></textarea>
                    </div>
                    <div style="display: flex; gap: var(--spacing-md); justify-content: flex-end; margin-top: var(--spacing-xl);">
                        <button type="button" class="btn btn-secondary" onclick="closeModal('muteModal')">取消</button>
                        <button type="submit" class="btn btn-warning">确认禁言</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- 封IP弹窗 -->
    <div id="banIPModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title" id="banIPModalTitle">封IP操作</h3>
                <button class="modal-close" onclick="closeModal('banIPModal')">&times;</button>
            </div>
            <div class="modal-body">
                <!-- 封IP详情显示区域 -->
                <div id="banIPDetailsSection" style="display: none;">
                    <div style="background: #FEF2F2; border: 1px solid #FECACA; border-radius: 8px; padding: 16px; margin-bottom: 20px;">
                        <h4 style="color: #DC2626; margin: 0 0 12px 0; font-size: 16px;">
                            <i class="fas fa-shield-alt"></i>
                            该用户IP已被封禁
                        </h4>
                        <div style="color: #7F1D1D; font-size: 14px; line-height: 1.5;">
                            <p style="margin: 4px 0;"><strong>封IP时间：</strong><span id="banIPStartTime">-</span></p>
                            <p style="margin: 4px 0;"><strong>到期时间：</strong><span id="banIPEndTime">-</span></p>
                            <p style="margin: 4px 0;"><strong>封IP管理员：</strong><span id="banIPAdminName">-</span></p>
                            <p style="margin: 4px 0;"><strong>封IP原因：</strong><span id="banIPReasonDisplay">-</span></p>
                            <p style="margin: 4px 0;"><strong>被封IP：</strong><span id="bannedIPAddress">-</span></p>
                        </div>
                    </div>

                    <!-- 解封IP表单 -->
                    <form id="unbanIPForm">
                        <div class="form-group">
                            <label class="form-label">解封IP原因</label>
                            <textarea class="form-control" id="unbanIPReason" rows="3" placeholder="请详细说明解封IP原因..." required></textarea>
                        </div>
                        <div style="display: flex; gap: var(--spacing-md); justify-content: flex-end; margin-top: var(--spacing-xl);">
                            <button type="button" class="btn btn-secondary" onclick="closeModal('banIPModal')">取消</button>
                            <button type="submit" class="btn btn-success">
                                <i class="fas fa-shield"></i>
                                确认解封IP
                            </button>
                        </div>
                    </form>
                </div>

                <!-- 封IP表单 -->
                <form id="banIPForm">
                    <div class="form-group">
                        <label class="form-label">封IP时长</label>
                        <select class="form-control" id="banIPDuration" required>
                            <option value="">请选择封IP时长</option>
                            <option value="1">1天</option>
                            <option value="7">7天</option>
                            <option value="30">30天</option>
                            <option value="90">90天</option>
                            <option value="365">1年</option>
                            <option value="permanent">永久封IP</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label class="form-label">封IP原因</label>
                        <textarea class="form-control" id="banIPReasonInput" rows="3" placeholder="请详细说明封IP原因..." required></textarea>
                    </div>
                    <div style="display: flex; gap: var(--spacing-md); justify-content: flex-end; margin-top: var(--spacing-xl);">
                        <button type="button" class="btn btn-secondary" onclick="closeModal('banIPModal')">取消</button>
                        <button type="submit" class="btn btn-danger">确认封IP</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Toast 容器 -->
    <div class="toast-container" id="toastContainer"></div>

    <script>
        // Toast 提示功能
        function showToast(message, type = 'info', duration = 4000) {
            const container = document.getElementById('toastContainer');
            const toast = document.createElement('div');
            toast.className = `toast ${type}`;

            const icons = {
                success: 'fas fa-check-circle',
                danger: 'fas fa-exclamation-circle',
                warning: 'fas fa-exclamation-triangle',
                info: 'fas fa-info-circle'
            };

            toast.innerHTML = `
                <div class="toast-icon">
                    <i class="${icons[type] || icons.info}"></i>
                </div>
                <div class="toast-content">
                    <p class="toast-message">${message}</p>
                </div>
                <button class="toast-close" onclick="removeToast(this.parentElement)">
                    <i class="fas fa-times"></i>
                </button>
                <div class="toast-progress" style="width: 100%;"></div>
            `;

            container.appendChild(toast);

            // 触发显示动画
            setTimeout(() => {
                toast.classList.add('show');
            }, 10);

            // 进度条动画
            const progress = toast.querySelector('.toast-progress');
            setTimeout(() => {
                progress.style.width = '0%';
                progress.style.transitionDuration = duration + 'ms';
            }, 100);

            // 自动移除
            setTimeout(() => {
                removeToast(toast);
            }, duration);
        }

        function removeToast(toast) {
            toast.classList.remove('show');
            setTimeout(() => {
                if (toast.parentElement) {
                    toast.parentElement.removeChild(toast);
                }
            }, 300);
        }
        // 敏感信息显示切换
        function toggleSensitiveInfo(button, fullInfo) {
            const span = button.previousElementSibling;
            const icon = button.querySelector('i');

            if (span.textContent.includes('*')) {
                span.textContent = fullInfo;
                icon.className = 'fas fa-eye-slash';

                // 记录查看敏感信息日志
                logSensitiveView(span.getAttribute('data-type'));
            } else {
                const type = span.getAttribute('data-type');
                if (type === 'phone') {
                    span.textContent = fullInfo.substring(0, 3) + '****' + fullInfo.substring(7);
                } else if (type === 'email') {
                    const parts = fullInfo.split('@');
                    span.textContent = parts[0].substring(0, 2) + '***@' + parts[1];
                } else if (type === 'idcard') {
                    span.textContent = fullInfo.substring(0, 6) + '********' + fullInfo.substring(-4);
                }
                icon.className = 'fas fa-eye';
            }
        }

        // 记录查看敏感信息日志
        function logSensitiveView(infoType) {
            const formData = new FormData();
            formData.append('action', 'view_sensitive');
            formData.append('user_id', <?php echo $user['id']; ?>);
            formData.append('info_type', infoType);

            fetch('action.php', {
                method: 'POST',
                body: formData
            }).catch(error => {
                console.error('日志记录失败:', error);
            });
        }

        // 弹窗管理
        function showModal(modalId) {
            document.getElementById(modalId).classList.add('show');
        }

        function closeModal(modalId) {
            document.getElementById(modalId).classList.remove('show');
        }

        // 点击弹窗外部关闭
        document.addEventListener('click', function(e) {
            if (e.target.classList.contains('modal')) {
                e.target.classList.remove('show');
            }
        });

        // 实名认证详情
        function showVerificationDetails(userId) {
            // 填充实名认证信息
            document.getElementById('modalVerificationStatus').innerHTML = '<?php echo getVerificationBadge($user); ?>';
            document.getElementById('modalRealName').textContent = '<?php echo htmlspecialchars($user['real_name'] ?? '未提交'); ?>';
            document.getElementById('modalIdCard').textContent = '<?php echo !empty($user['id_card_number']) ? formatSensitiveInfo($user['id_card_number'], 'idcard') : '未提交'; ?>';
            document.getElementById('modalSubmitTime').textContent = '<?php echo !empty($user['submitted_at']) ? date('Y-m-d H:i', strtotime($user['submitted_at'])) : '未提交'; ?>';
            document.getElementById('modalReviewTime').textContent = '<?php echo !empty($user['verified_at']) ? date('Y-m-d H:i', strtotime($user['verified_at'])) : '未审核'; ?>';

            showModal('verificationModal');
        }

        // 编辑用户信息
        function editUserInfo(userId) {
            showModal('editModal');
        }

        // 查看设备信息
        function viewDeviceInfo(userId) {
            showModal('deviceModal');
            loadDeviceInfo(userId);
        }

        // 查看IP信息
        function viewIPInfo(userId) {
            showModal('ipModal');
            loadIPInfo(userId);
        }

        // 加载设备信息
        function loadDeviceInfo(userId) {
            // 显示加载状态
            document.getElementById('deviceInfoLoading').style.display = 'block';
            document.getElementById('deviceInfoContent').style.display = 'none';
            document.getElementById('deviceInfoEmpty').style.display = 'none';

            fetch(`get_user_ips.php?user_id=${userId}`)
                .then(response => response.json())
                .then(data => {
                    document.getElementById('deviceInfoLoading').style.display = 'none';

                    if (data.success && data.records && data.records.length > 0) {
                        displayDeviceInfo(data.records);
                        document.getElementById('deviceInfoContent').style.display = 'block';
                    } else {
                        document.getElementById('deviceInfoEmpty').style.display = 'block';
                    }
                })
                .catch(error => {
                    console.error('Error loading device info:', error);
                    document.getElementById('deviceInfoLoading').style.display = 'none';
                    document.getElementById('deviceInfoEmpty').style.display = 'block';
                });
        }

        // 加载IP信息
        function loadIPInfo(userId) {
            // 显示加载状态
            document.getElementById('ipInfoLoading').style.display = 'block';
            document.getElementById('ipInfoContent').style.display = 'none';
            document.getElementById('ipInfoEmpty').style.display = 'none';

            fetch(`get_user_ips.php?user_id=${userId}`)
                .then(response => response.json())
                .then(data => {
                    document.getElementById('ipInfoLoading').style.display = 'none';

                    if (data.success && data.records && data.records.length > 0) {
                        displayIPInfo(data.records);
                        document.getElementById('ipInfoContent').style.display = 'block';
                    } else {
                        document.getElementById('ipInfoEmpty').style.display = 'block';
                    }
                })
                .catch(error => {
                    console.error('Error loading IP info:', error);
                    document.getElementById('ipInfoLoading').style.display = 'none';
                    document.getElementById('ipInfoEmpty').style.display = 'block';
                });
        }

        // 显示设备信息
        function displayDeviceInfo(records) {
            const container = document.getElementById('deviceInfoContent');

            // 统计设备类型
            const deviceStats = {};
            const osStats = {};
            const browserStats = {};

            records.forEach(record => {
                deviceStats[record.device] = (deviceStats[record.device] || 0) + parseInt(record.login_count);
                osStats[record.os] = (osStats[record.os] || 0) + parseInt(record.login_count);
                browserStats[record.browser] = (browserStats[record.browser] || 0) + parseInt(record.login_count);
            });

            let html = `
                <div style="margin-bottom: var(--spacing-xl);">
                    <h4 style="color: var(--gray-900); margin-bottom: var(--spacing-md); display: flex; align-items: center; gap: var(--spacing-sm);">
                        <i class="fas fa-chart-pie" style="color: var(--primary-color);"></i>
                        设备统计分析
                    </h4>
                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: var(--spacing-md);">
                        <div style="background: var(--gray-50); padding: var(--spacing-md); border-radius: var(--border-radius-lg); border: 2px solid var(--primary-color);">
                            <h5 style="color: var(--gray-700); margin: 0 0 var(--spacing-sm) 0; font-size: var(--font-size-sm);">设备类型</h5>
                            ${Object.entries(deviceStats).map(([device, count]) =>
                                `<div style="display: flex; justify-content: space-between; margin-bottom: 4px;">
                                    <span style="color: var(--gray-600); font-size: var(--font-size-sm);">${device}</span>
                                    <span style="color: var(--primary-color); font-weight: 600; font-size: var(--font-size-sm);">${count}次</span>
                                </div>`
                            ).join('')}
                        </div>
                        <div style="background: var(--gray-50); padding: var(--spacing-md); border-radius: var(--border-radius-lg); border: 2px solid var(--success-color);">
                            <h5 style="color: var(--gray-700); margin: 0 0 var(--spacing-sm) 0; font-size: var(--font-size-sm);">操作系统</h5>
                            ${Object.entries(osStats).map(([os, count]) =>
                                `<div style="display: flex; justify-content: space-between; margin-bottom: 4px;">
                                    <span style="color: var(--gray-600); font-size: var(--font-size-sm);">${os}</span>
                                    <span style="color: var(--success-color); font-weight: 600; font-size: var(--font-size-sm);">${count}次</span>
                                </div>`
                            ).join('')}
                        </div>
                        <div style="background: var(--gray-50); padding: var(--spacing-md); border-radius: var(--border-radius-lg); border: 2px solid var(--info-color);">
                            <h5 style="color: var(--gray-700); margin: 0 0 var(--spacing-sm) 0; font-size: var(--font-size-sm);">浏览器</h5>
                            ${Object.entries(browserStats).map(([browser, count]) =>
                                `<div style="display: flex; justify-content: space-between; margin-bottom: 4px;">
                                    <span style="color: var(--gray-600); font-size: var(--font-size-sm);">${browser}</span>
                                    <span style="color: var(--info-color); font-weight: 600; font-size: var(--font-size-sm);">${count}次</span>
                                </div>`
                            ).join('')}
                        </div>
                    </div>
                </div>

                <div>
                    <h4 style="color: var(--gray-900); margin-bottom: var(--spacing-md); display: flex; align-items: center; gap: var(--spacing-sm);">
                        <i class="fas fa-list" style="color: var(--primary-color);"></i>
                        详细记录
                    </h4>
                    <div class="data-table-container" style="max-height: 400px; overflow-y: auto;">
                        <table class="data-table">
                            <thead>
                                <tr>
                                    <th>设备类型</th>
                                    <th>操作系统</th>
                                    <th>浏览器</th>
                                    <th>登录次数</th>
                                    <th>最后登录</th>
                                    <th>地理位置</th>
                                </tr>
                            </thead>
                            <tbody>
                                ${records.map(record => `
                                    <tr>
                                        <td>
                                            <div style="display: flex; align-items: center; gap: var(--spacing-sm);">
                                                <i class="fas fa-${record.device_icon}" style="color: var(--primary-color);"></i>
                                                <span>${record.device}</span>
                                            </div>
                                        </td>
                                        <td>${record.os}</td>
                                        <td>${record.browser}</td>
                                        <td>
                                            <span style="background: var(--primary-color); color: white; padding: 2px 8px; border-radius: 12px; font-size: var(--font-size-xs); font-weight: 600;">
                                                ${record.login_count}次
                                            </span>
                                        </td>
                                        <td>${record.login_time}</td>
                                        <td>
                                            <span style="color: var(--gray-600);">
                                                <i class="fas fa-map-marker-alt" style="color: var(--error-color); margin-right: 4px;"></i>
                                                ${record.location}
                                            </span>
                                        </td>
                                    </tr>
                                `).join('')}
                            </tbody>
                        </table>
                    </div>
                </div>
            `;

            container.innerHTML = html;
        }

        // 重置密码
        function resetPassword(userId) {
            if (confirm('确定要重置该用户的密码吗？新密码将发送到用户手机。')) {
                // 这里可以添加AJAX请求
                showToast('密码重置功能开发中...', 'info');
            }
        }

        // 显示IP信息
        function displayIPInfo(records) {
            const container = document.getElementById('ipInfoContent');

            // 统计IP地址和地理位置
            const ipStats = {};
            const locationStats = {};

            records.forEach(record => {
                ipStats[record.ip_address] = (ipStats[record.ip_address] || 0) + parseInt(record.login_count);
                locationStats[record.location] = (locationStats[record.location] || 0) + parseInt(record.login_count);
            });

            // 获取唯一IP数量和风险分析
            const uniqueIPs = Object.keys(ipStats).length;
            const totalLogins = records.reduce((sum, record) => sum + parseInt(record.login_count), 0);
            const riskLevel = analyzeIPRisk(uniqueIPs, locationStats);

            let html = `
                <div style="margin-bottom: var(--spacing-xl);">
                    <h4 style="color: var(--gray-900); margin-bottom: var(--spacing-md); display: flex; align-items: center; gap: var(--spacing-sm);">
                        <i class="fas fa-shield-alt" style="color: var(--primary-color);"></i>
                        IP安全分析
                    </h4>
                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: var(--spacing-md);">
                        <div style="background: var(--gray-50); padding: var(--spacing-md); border-radius: var(--border-radius-lg); border: 2px solid var(--primary-color);">
                            <h5 style="color: var(--gray-700); margin: 0 0 var(--spacing-sm) 0; font-size: var(--font-size-sm);">基础统计</h5>
                            <div style="display: flex; justify-content: space-between; margin-bottom: 4px;">
                                <span style="color: var(--gray-600); font-size: var(--font-size-sm);">唯一IP数</span>
                                <span style="color: var(--primary-color); font-weight: 600; font-size: var(--font-size-sm);">${uniqueIPs}个</span>
                            </div>
                            <div style="display: flex; justify-content: space-between; margin-bottom: 4px;">
                                <span style="color: var(--gray-600); font-size: var(--font-size-sm);">总登录次数</span>
                                <span style="color: var(--primary-color); font-weight: 600; font-size: var(--font-size-sm);">${totalLogins}次</span>
                            </div>
                        </div>
                        <div style="background: var(--gray-50); padding: var(--spacing-md); border-radius: var(--border-radius-lg); border: 2px solid ${riskLevel.color};">
                            <h5 style="color: var(--gray-700); margin: 0 0 var(--spacing-sm) 0; font-size: var(--font-size-sm);">风险评估</h5>
                            <div style="display: flex; align-items: center; gap: var(--spacing-sm); margin-bottom: 4px;">
                                <i class="fas fa-${riskLevel.icon}" style="color: ${riskLevel.color};"></i>
                                <span style="color: ${riskLevel.color}; font-weight: 600; font-size: var(--font-size-sm);">${riskLevel.level}</span>
                            </div>
                            <div style="color: var(--gray-600); font-size: var(--font-size-xs); line-height: 1.4;">
                                ${riskLevel.description}
                            </div>
                        </div>
                        <div style="background: var(--gray-50); padding: var(--spacing-md); border-radius: var(--border-radius-lg); border: 2px solid var(--info-color);">
                            <h5 style="color: var(--gray-700); margin: 0 0 var(--spacing-sm) 0; font-size: var(--font-size-sm);">地理分布</h5>
                            ${Object.entries(locationStats).slice(0, 3).map(([location, count]) =>
                                `<div style="display: flex; justify-content: space-between; margin-bottom: 4px;">
                                    <span style="color: var(--gray-600); font-size: var(--font-size-sm);">${location}</span>
                                    <span style="color: var(--info-color); font-weight: 600; font-size: var(--font-size-sm);">${count}次</span>
                                </div>`
                            ).join('')}
                        </div>
                    </div>
                </div>

                <div>
                    <h4 style="color: var(--gray-900); margin-bottom: var(--spacing-md); display: flex; align-items: center; gap: var(--spacing-sm);">
                        <i class="fas fa-list" style="color: var(--primary-color);"></i>
                        IP详细记录
                    </h4>
                    <div class="data-table-container" style="max-height: 400px; overflow-y: auto;">
                        <table class="data-table">
                            <thead>
                                <tr>
                                    <th>IP地址</th>
                                    <th>地理位置</th>
                                    <th>设备信息</th>
                                    <th>登录次数</th>
                                    <th>最后登录</th>
                                    <th>风险等级</th>
                                </tr>
                            </thead>
                            <tbody>
                                ${records.map(record => {
                                    const ipRisk = getIPRiskLevel(record.ip_address, record.location);
                                    return `
                                        <tr>
                                            <td>
                                                <div style="font-family: monospace; font-weight: 600; color: var(--gray-900);">
                                                    ${record.ip_address}
                                                </div>
                                            </td>
                                            <td>
                                                <span style="color: var(--gray-600);">
                                                    <i class="fas fa-map-marker-alt" style="color: var(--error-color); margin-right: 4px;"></i>
                                                    ${record.location}
                                                </span>
                                            </td>
                                            <td>
                                                <div style="display: flex; align-items: center; gap: var(--spacing-sm);">
                                                    <i class="fas fa-${record.device_icon}" style="color: var(--primary-color);"></i>
                                                    <div style="font-size: var(--font-size-xs); color: var(--gray-600);">
                                                        <div>${record.device}</div>
                                                        <div>${record.os} / ${record.browser}</div>
                                                    </div>
                                                </div>
                                            </td>
                                            <td>
                                                <span style="background: var(--primary-color); color: white; padding: 2px 8px; border-radius: 12px; font-size: var(--font-size-xs); font-weight: 600;">
                                                    ${record.login_count}次
                                                </span>
                                            </td>
                                            <td>${record.login_time}</td>
                                            <td>
                                                <span style="background: ${ipRisk.color}; color: white; padding: 2px 8px; border-radius: 12px; font-size: var(--font-size-xs); font-weight: 600;">
                                                    <i class="fas fa-${ipRisk.icon}"></i>
                                                    ${ipRisk.level}
                                                </span>
                                            </td>
                                        </tr>
                                    `;
                                }).join('')}
                            </tbody>
                        </table>
                    </div>
                </div>
            `;

            container.innerHTML = html;
        }

        // IP风险分析
        function analyzeIPRisk(uniqueIPs, locationStats) {
            const locationCount = Object.keys(locationStats).length;

            if (uniqueIPs <= 2 && locationCount <= 2) {
                return {
                    level: '低风险',
                    color: 'var(--success-color)',
                    icon: 'shield-check',
                    description: 'IP地址稳定，地理位置集中，账户安全性较高'
                };
            } else if (uniqueIPs <= 5 && locationCount <= 3) {
                return {
                    level: '中风险',
                    color: 'var(--warning-color)',
                    icon: 'shield-exclamation',
                    description: 'IP地址较多，建议关注账户使用情况'
                };
            } else {
                return {
                    level: '高风险',
                    color: 'var(--error-color)',
                    icon: 'shield-times',
                    description: 'IP地址频繁变化，地理位置分散，需要重点关注'
                };
            }
        }

        // 获取单个IP风险等级
        function getIPRiskLevel(ip, location) {
            // 检查是否为内网IP
            if (location === '内网IP') {
                return {
                    level: '内网',
                    color: 'var(--info-color)',
                    icon: 'home'
                };
            }

            // 检查是否为常见地区
            const commonLocations = ['北京市', '上海市', '广东省', '江苏省', '浙江省'];
            if (commonLocations.includes(location)) {
                return {
                    level: '正常',
                    color: 'var(--success-color)',
                    icon: 'check'
                };
            }

            // 其他情况
            return {
                level: '关注',
                color: 'var(--warning-color)',
                icon: 'eye'
            };
        }

        // 封号功能
        function banUser(userId) {
            // 检查用户是否已被封号
            checkUserBanStatus(userId, 'ban');
        }

        // 禁言功能
        function muteUser(userId) {
            // 检查用户是否已被禁言
            checkUserBanStatus(userId, 'mute');
        }

        // 封IP功能
        function banIP(userId) {
            // 检查用户IP是否已被封
            checkUserBanStatus(userId, 'ip');
        }

        // 检查用户封禁状态
        function checkUserBanStatus(userId, type) {
            const formData = new FormData();
            formData.append('action', 'check_ban_status');
            formData.append('user_id', userId);
            formData.append('type', type);

            fetch('action.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    if (type === 'ban') {
                        showBanModal(data.banned, data.banInfo);
                    } else if (type === 'mute') {
                        showMuteModal(data.banned, data.banInfo);
                    } else if (type === 'ip') {
                        showBanIPModal(data.banned, data.banInfo);
                    }
                } else {
                    showToast('检查状态失败：' + data.message, 'danger');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                // 如果检查失败，直接显示封禁表单
                if (type === 'ban') {
                    showBanModal(false, null);
                } else if (type === 'mute') {
                    showMuteModal(false, null);
                } else if (type === 'ip') {
                    showBanIPModal(false, null);
                }
            });
        }

        // 显示封号弹窗
        function showBanModal(isBanned, banInfo) {
            const modal = document.getElementById('banModal');
            const title = document.getElementById('banModalTitle');
            const detailsSection = document.getElementById('banDetailsSection');
            const banForm = document.getElementById('banForm');

            if (isBanned && banInfo) {
                // 显示封号详情和解封表单
                title.textContent = '用户封号详情';
                detailsSection.style.display = 'block';
                banForm.style.display = 'none';

                // 填充封号信息
                document.getElementById('banStartTime').textContent = banInfo.start_time || '-';
                document.getElementById('banEndTime').textContent = banInfo.end_time || '永久';
                document.getElementById('banAdminName').textContent = banInfo.admin_name || '-';
                document.getElementById('banReason').textContent = banInfo.reason || '-';
            } else {
                // 显示封号表单
                title.textContent = '封号操作';
                detailsSection.style.display = 'none';
                banForm.style.display = 'block';

                // 清空表单
                document.getElementById('banDuration').value = '';
                document.getElementById('banReasonInput').value = '';
            }

            showModal('banModal');
        }

        // 显示禁言弹窗
        function showMuteModal(isMuted, muteInfo) {
            const modal = document.getElementById('muteModal');
            const title = document.getElementById('muteModalTitle');
            const detailsSection = document.getElementById('muteDetailsSection');
            const muteForm = document.getElementById('muteForm');

            if (isMuted && muteInfo) {
                // 显示禁言详情和解禁表单
                title.textContent = '用户禁言详情';
                detailsSection.style.display = 'block';
                muteForm.style.display = 'none';

                // 填充禁言信息
                document.getElementById('muteStartTime').textContent = muteInfo.start_time || '-';
                document.getElementById('muteEndTime').textContent = muteInfo.end_time || '永久';
                document.getElementById('muteAdminName').textContent = muteInfo.admin_name || '-';
                document.getElementById('muteReasonDisplay').textContent = muteInfo.reason || '-';
            } else {
                // 显示禁言表单
                title.textContent = '禁言操作';
                detailsSection.style.display = 'none';
                muteForm.style.display = 'block';

                // 清空表单
                document.getElementById('muteDuration').value = '';
                document.getElementById('muteReasonInput').value = '';
            }

            showModal('muteModal');
        }

        // 显示封IP弹窗
        function showBanIPModal(isBanned, banInfo) {
            const modal = document.getElementById('banIPModal');
            const title = document.getElementById('banIPModalTitle');
            const detailsSection = document.getElementById('banIPDetailsSection');
            const banIPForm = document.getElementById('banIPForm');

            if (isBanned && banInfo) {
                // 显示封IP详情和解封表单
                title.textContent = '用户IP封禁详情';
                detailsSection.style.display = 'block';
                banIPForm.style.display = 'none';

                // 填充封IP信息
                document.getElementById('banIPStartTime').textContent = banInfo.start_time || '-';
                document.getElementById('banIPEndTime').textContent = banInfo.end_time || '永久';
                document.getElementById('banIPAdminName').textContent = banInfo.admin_name || '-';
                document.getElementById('banIPReasonDisplay').textContent = banInfo.reason || '-';
                document.getElementById('bannedIPAddress').textContent = banInfo.ip_address || '-';
            } else {
                // 显示封IP表单
                title.textContent = '封IP操作';
                detailsSection.style.display = 'none';
                banIPForm.style.display = 'block';

                // 清空表单
                document.getElementById('banIPDuration').value = '';
                document.getElementById('banIPReasonInput').value = '';
            }

            showModal('banIPModal');
        }

        // 封活动功能
        function banActivity(userId) {
            const reason = prompt('请输入封活动原因：');
            if (reason) {
                if (confirm('确定要禁止该用户参与活动吗？')) {
                    showToast('封活动功能开发中...', 'warning');
                }
            }
        }

        // 封接单功能
        function banOrder(userId) {
            const reason = prompt('请输入封接单原因：');
            if (reason) {
                if (confirm('确定要禁止该用户接单吗？')) {
                    showToast('封接单功能开发中...', 'warning');
                }
            }
        }

        // 封组局功能
        function banGroup(userId) {
            const reason = prompt('请输入封组局原因：');
            if (reason) {
                if (confirm('确定要禁止该用户组局吗？')) {
                    showToast('封组局功能开发中...', 'warning');
                }
            }
        }

        // 封下单功能
        function banPlaceOrder(userId) {
            const reason = prompt('请输入封下单原因：');
            if (reason) {
                if (confirm('确定要禁止该用户下单吗？')) {
                    showToast('封下单功能开发中...', 'warning');
                }
            }
        }

        // 发送短信
        function sendSMS(userId) {
            showModal('smsModal');
        }

        // 发送系统消息
        function sendSystemMessage(userId) {
            showModal('messageModal');
        }

        // 发送验证码
        function sendVerificationCode(userId) {
            showModal('verificationCodeModal');
        }

        // 查看验证码日志
        function viewVerificationLogs(userId) {
            // 在新窗口中打开验证码日志页面
            const url = `verification_code_logs.php?search_user=${userId}&page=1`;
            window.open(url, '_blank', 'width=1200,height=800,scrollbars=yes,resizable=yes');
        }

        // 写操作日志
        function writeLog(userId) {
            showModal('logModal');
        }

        // 查看用户画像
        function viewUserProfile(userId) {
            showModal('profileModal');
            loadUserProfile(userId);
        }

        // 加载用户画像数据
        function loadUserProfile(userId) {
            // 显示加载状态
            document.getElementById('profileLoading').style.display = 'block';
            document.getElementById('profileContent').style.display = 'none';
            document.getElementById('profileEmpty').style.display = 'none';

            fetch(`get_user_profile.php?user_id=${userId}`)
                .then(response => response.json())
                .then(data => {
                    document.getElementById('profileLoading').style.display = 'none';

                    if (data.success && data.profile) {
                        displayUserProfile(data.profile);
                        document.getElementById('profileContent').style.display = 'block';
                    } else {
                        document.getElementById('profileEmpty').style.display = 'block';
                    }
                })
                .catch(error => {
                    console.error('Error loading user profile:', error);
                    document.getElementById('profileLoading').style.display = 'none';
                    document.getElementById('profileEmpty').style.display = 'block';
                });
        }

        // 显示用户画像数据
        function displayUserProfile(profile) {
            const content = document.getElementById('profileContent');

            content.innerHTML = `
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: var(--spacing-lg);">
                    <!-- 基础画像 -->
                    <div class="profile-card">
                        <div class="profile-card-header">
                            <h4><i class="fas fa-user-circle"></i> 基础画像</h4>
                        </div>
                        <div class="profile-card-body">
                            <div class="profile-metric">
                                <span class="metric-label">活跃度评分</span>
                                <div class="metric-value">
                                    <span class="score ${getScoreClass(profile.activity_score)}">${profile.activity_score}/100</span>
                                    <div class="score-bar">
                                        <div class="score-fill" style="width: ${profile.activity_score}%"></div>
                                    </div>
                                </div>
                            </div>
                            <div class="profile-metric">
                                <span class="metric-label">消费能力</span>
                                <div class="metric-value">
                                    <span class="level ${profile.consumption_level.toLowerCase()}">${profile.consumption_level_text}</span>
                                    <small>累计消费 ¥${profile.total_consumption}</small>
                                </div>
                            </div>
                            <div class="profile-metric">
                                <span class="metric-label">用户类型</span>
                                <div class="metric-value">
                                    <span class="user-type ${profile.user_type.toLowerCase()}">${profile.user_type_text}</span>
                                </div>
                            </div>
                            <div class="profile-metric">
                                <span class="metric-label">风险等级</span>
                                <div class="metric-value">
                                    <span class="risk-level ${profile.risk_level.toLowerCase()}">${profile.risk_level_text}</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 使用习惯 -->
                    <div class="profile-card">
                        <div class="profile-card-header">
                            <h4><i class="fas fa-clock"></i> 使用习惯</h4>
                        </div>
                        <div class="profile-card-body">
                            <div class="profile-metric">
                                <span class="metric-label">最常用时段</span>
                                <div class="metric-value">
                                    <span class="time-period">${profile.peak_hours}</span>
                                </div>
                            </div>
                            <div class="profile-metric">
                                <span class="metric-label">平均在线时长</span>
                                <div class="metric-value">
                                    <span class="duration">${profile.avg_session_duration}</span>
                                </div>
                            </div>
                            <div class="profile-metric">
                                <span class="metric-label">最常用页面</span>
                                <div class="metric-value">
                                    <span class="page-name">${profile.most_visited_page}</span>
                                    <small>占比 ${profile.most_visited_percentage}%</small>
                                </div>
                            </div>
                            <div class="profile-metric">
                                <span class="metric-label">使用设备</span>
                                <div class="metric-value">
                                    <span class="device-type">${profile.primary_device}</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 行为特征 -->
                    <div class="profile-card">
                        <div class="profile-card-header">
                            <h4><i class="fas fa-chart-line"></i> 行为特征</h4>
                        </div>
                        <div class="profile-card-body">
                            <div class="profile-metric">
                                <span class="metric-label">社交活跃度</span>
                                <div class="metric-value">
                                    <span class="score ${getScoreClass(profile.social_score)}">${profile.social_score}/100</span>
                                    <div class="score-bar">
                                        <div class="score-fill" style="width: ${profile.social_score}%"></div>
                                    </div>
                                </div>
                            </div>
                            <div class="profile-metric">
                                <span class="metric-label">内容偏好</span>
                                <div class="metric-value">
                                    <div class="preference-tags">
                                        ${profile.content_preferences.map(pref => `<span class="preference-tag">${pref}</span>`).join('')}
                                    </div>
                                </div>
                            </div>
                            <div class="profile-metric">
                                <span class="metric-label">互动频率</span>
                                <div class="metric-value">
                                    <span class="frequency">${profile.interaction_frequency}</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 消费分析 -->
                    <div class="profile-card">
                        <div class="profile-card-header">
                            <h4><i class="fas fa-shopping-cart"></i> 消费分析</h4>
                        </div>
                        <div class="profile-card-body">
                            <div class="profile-metric">
                                <span class="metric-label">月均消费</span>
                                <div class="metric-value">
                                    <span class="amount">¥${profile.avg_monthly_spending}</span>
                                </div>
                            </div>
                            <div class="profile-metric">
                                <span class="metric-label">消费频次</span>
                                <div class="metric-value">
                                    <span class="frequency">${profile.purchase_frequency}</span>
                                </div>
                            </div>
                            <div class="profile-metric">
                                <span class="metric-label">偏好服务</span>
                                <div class="metric-value">
                                    <div class="service-tags">
                                        ${profile.preferred_services.map(service => `<span class="service-tag">${service}</span>`).join('')}
                                    </div>
                                </div>
                            </div>
                            <div class="profile-metric">
                                <span class="metric-label">价格敏感度</span>
                                <div class="metric-value">
                                    <span class="sensitivity ${profile.price_sensitivity.toLowerCase()}">${profile.price_sensitivity_text}</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 地理分布 -->
                    <div class="profile-card">
                        <div class="profile-card-header">
                            <h4><i class="fas fa-map-marker-alt"></i> 地理分布</h4>
                        </div>
                        <div class="profile-card-body">
                            <div class="profile-metric">
                                <span class="metric-label">主要活动地区</span>
                                <div class="metric-value">
                                    <span class="location">${profile.primary_location}</span>
                                </div>
                            </div>
                            <div class="profile-metric">
                                <span class="metric-label">活动范围</span>
                                <div class="metric-value">
                                    <span class="range">${profile.location_range}</span>
                                </div>
                            </div>
                            <div class="profile-metric">
                                <span class="metric-label">登录地点</span>
                                <div class="metric-value">
                                    <div class="location-list">
                                        ${profile.login_locations.map(loc => `<span class="location-item">${loc.city} (${loc.count}次)</span>`).join('')}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 趋势分析 -->
                    <div class="profile-card">
                        <div class="profile-card-header">
                            <h4><i class="fas fa-trending-up"></i> 趋势分析</h4>
                        </div>
                        <div class="profile-card-body">
                            <div class="profile-metric">
                                <span class="metric-label">活跃度趋势</span>
                                <div class="metric-value">
                                    <span class="trend ${profile.activity_trend.toLowerCase()}">${profile.activity_trend_text}</span>
                                </div>
                            </div>
                            <div class="profile-metric">
                                <span class="metric-label">消费趋势</span>
                                <div class="metric-value">
                                    <span class="trend ${profile.spending_trend.toLowerCase()}">${profile.spending_trend_text}</span>
                                </div>
                            </div>
                            <div class="profile-metric">
                                <span class="metric-label">留存预测</span>
                                <div class="metric-value">
                                    <span class="prediction ${profile.retention_prediction.toLowerCase()}">${profile.retention_prediction_text}</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 详细统计图表区域 -->
                <div style="margin-top: var(--spacing-xl); padding-top: var(--spacing-xl); border-top: 2px solid var(--gray-200);">
                    <h4 style="margin-bottom: var(--spacing-lg); color: var(--gray-900);">
                        <i class="fas fa-chart-bar"></i> 详细统计
                    </h4>
                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(400px, 1fr)); gap: var(--spacing-lg);">
                        <!-- 活跃时间分布 -->
                        <div class="chart-container">
                            <h5>24小时活跃分布</h5>
                            <div class="time-chart">
                                ${generateTimeChart(profile.hourly_activity)}
                            </div>
                        </div>

                        <!-- 页面访问统计 -->
                        <div class="chart-container">
                            <h5>页面访问统计</h5>
                            <div class="page-stats">
                                ${profile.page_stats.map(page => `
                                    <div class="page-stat-item">
                                        <span class="page-name">${page.name}</span>
                                        <div class="page-bar">
                                            <div class="page-fill" style="width: ${page.percentage}%"></div>
                                            <span class="page-percentage">${page.percentage}%</span>
                                        </div>
                                    </div>
                                `).join('')}
                            </div>
                        </div>
                    </div>
                </div>
            `;
        }

        // 获取评分等级样式
        function getScoreClass(score) {
            if (score >= 80) return 'excellent';
            if (score >= 60) return 'good';
            if (score >= 40) return 'average';
            return 'poor';
        }

        // 生成时间图表
        function generateTimeChart(hourlyData) {
            return hourlyData.map((value, hour) => `
                <div class="time-bar" title="${hour}:00 - ${value}%">
                    <div class="time-fill" style="height: ${value}%"></div>
                    <span class="time-label">${hour}</span>
                </div>
            `).join('');
        }

        // 解封用户（直接调用）
        function unbanUser(userId) {
            const reason = prompt('请输入解封原因：');
            if (reason && reason.trim()) {
                if (confirm('确定要解封该用户吗？')) {
                    // 显示加载状态
                    showToast('正在解封用户...', 'info');

                    const formData = new FormData();
                    formData.append('action', 'unban_user');
                    formData.append('user_id', userId);
                    formData.append('reason', reason.trim());

                    fetch('action.php', {
                        method: 'POST',
                        body: formData
                    })
                    .then(response => {
                        console.log('Response status:', response.status);
                        return response.json();
                    })
                    .then(data => {
                        console.log('Response data:', data);
                        if (data.success) {
                            showToast('用户解封成功！正在刷新页面...', 'success');
                            // 立即刷新页面，不等待
                            setTimeout(() => {
                                window.location.href = window.location.href + '?t=' + Date.now();
                            }, 500);
                        } else {
                            showToast('解封失败：' + data.message, 'danger');
                        }
                    })
                    .catch(error => {
                        console.error('Error:', error);
                        showToast('解封操作失败，请稍后重试', 'danger');
                        // 即使出错也尝试刷新页面
                        setTimeout(() => {
                            window.location.reload();
                        }, 2000);
                    });
                }
            }
        }

        // 封号表单提交
        document.getElementById('banForm').addEventListener('submit', function(e) {
            e.preventDefault();

            const duration = document.getElementById('banDuration').value;
            const reason = document.getElementById('banReasonInput').value;

            if (!duration || !reason.trim()) {
                showToast('请填写完整信息', 'warning');
                return;
            }

            const formData = new FormData();
            formData.append('action', 'ban_user');
            formData.append('user_id', <?php echo $user['id']; ?>);
            formData.append('duration', duration);
            formData.append('reason', reason.trim());

            fetch('action.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showToast('用户封号成功！', 'success');
                    closeModal('banModal');
                    setTimeout(() => {
                        window.location.reload();
                    }, 1000);
                } else {
                    showToast('封号失败：' + data.message, 'danger');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showToast('封号操作失败，请稍后重试', 'danger');
            });
        });

        // 解封表单提交
        document.getElementById('unbanForm').addEventListener('submit', function(e) {
            e.preventDefault();

            const reason = document.getElementById('unbanReason').value;

            if (!reason.trim()) {
                showToast('请填写解封原因', 'warning');
                return;
            }

            const formData = new FormData();
            formData.append('action', 'unban_user');
            formData.append('user_id', <?php echo $user['id']; ?>);
            formData.append('reason', reason.trim());

            fetch('action.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showToast('用户解封成功！', 'success');
                    closeModal('banModal');
                    setTimeout(() => {
                        window.location.reload();
                    }, 1000);
                } else {
                    showToast('解封失败：' + data.message, 'danger');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showToast('解封操作失败，请稍后重试', 'danger');
            });
        });

        // 禁言表单提交
        document.getElementById('muteForm').addEventListener('submit', function(e) {
            e.preventDefault();

            const duration = document.getElementById('muteDuration').value;
            const reason = document.getElementById('muteReasonInput').value;

            if (!duration || !reason.trim()) {
                showToast('请填写完整信息', 'warning');
                return;
            }

            const formData = new FormData();
            formData.append('action', 'mute_user');
            formData.append('user_id', <?php echo $user['id']; ?>);
            formData.append('duration', duration);
            formData.append('reason', reason.trim());

            fetch('action.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showToast('用户禁言成功！', 'warning');
                    closeModal('muteModal');
                    setTimeout(() => {
                        window.location.reload();
                    }, 1000);
                } else {
                    showToast('禁言失败：' + data.message, 'danger');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showToast('禁言操作失败，请稍后重试', 'danger');
            });
        });

        // 解禁表单提交
        document.getElementById('unmuteForm').addEventListener('submit', function(e) {
            e.preventDefault();

            const reason = document.getElementById('unmuteReason').value;

            if (!reason.trim()) {
                showToast('请填写解禁原因', 'warning');
                return;
            }

            const formData = new FormData();
            formData.append('action', 'unmute_user');
            formData.append('user_id', <?php echo $user['id']; ?>);
            formData.append('reason', reason.trim());

            fetch('action.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showToast('用户解禁成功！', 'success');
                    closeModal('muteModal');
                    setTimeout(() => {
                        window.location.reload();
                    }, 1000);
                } else {
                    showToast('解禁失败：' + data.message, 'danger');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showToast('解禁操作失败，请稍后重试', 'danger');
            });
        });

        // 封IP表单提交
        document.getElementById('banIPForm').addEventListener('submit', function(e) {
            e.preventDefault();

            const duration = document.getElementById('banIPDuration').value;
            const reason = document.getElementById('banIPReasonInput').value;

            if (!duration || !reason.trim()) {
                showToast('请填写完整信息', 'warning');
                return;
            }

            const formData = new FormData();
            formData.append('action', 'ban_ip');
            formData.append('user_id', <?php echo $user['id']; ?>);
            formData.append('duration', duration);
            formData.append('reason', reason.trim());

            fetch('action.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showToast('IP封禁成功！', 'danger');
                    closeModal('banIPModal');
                    setTimeout(() => {
                        window.location.reload();
                    }, 1000);
                } else {
                    showToast('封IP失败：' + data.message, 'danger');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showToast('封IP操作失败，请稍后重试', 'danger');
            });
        });

        // 解封IP表单提交
        document.getElementById('unbanIPForm').addEventListener('submit', function(e) {
            e.preventDefault();

            const reason = document.getElementById('unbanIPReason').value;

            if (!reason.trim()) {
                showToast('请填写解封IP原因', 'warning');
                return;
            }

            const formData = new FormData();
            formData.append('action', 'unban_ip');
            formData.append('user_id', <?php echo $user['id']; ?>);
            formData.append('reason', reason.trim());

            fetch('action.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showToast('IP解封成功！', 'success');
                    closeModal('banIPModal');
                    setTimeout(() => {
                        window.location.reload();
                    }, 1000);
                } else {
                    showToast('IP解封失败：' + data.message, 'danger');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showToast('IP解封操作失败，请稍后重试', 'danger');
            });
        });

        // 编辑用户表单提交
        document.getElementById('editUserForm').addEventListener('submit', function(e) {
            e.preventDefault();

            const formData = new FormData();
            formData.append('action', 'edit_user');
            formData.append('user_id', <?php echo $user['id']; ?>);
            formData.append('username', document.getElementById('editUsername').value);
            formData.append('quwan_id', document.getElementById('editQuwanId').value);
            formData.append('phone', document.getElementById('editPhone').value);
            formData.append('email', document.getElementById('editEmail').value);
            formData.append('gender', document.getElementById('editGender').value);
            formData.append('region', document.getElementById('editRegion').value);
            formData.append('bio', document.getElementById('editBio').value);

            // 验证趣玩ID格式
            const quwanId = document.getElementById('editQuwanId').value;
            if (quwanId && !/^\d{7}$/.test(quwanId)) {
                showToast('趣玩ID必须是7位数字', 'warning');
                return;
            }

            // 提交表单
            fetch('action.php', {
                method: 'POST',
                body: formData
            })
            .then(response => {
                console.log('Response status:', response.status);
                return response.json();
            })
            .then(data => {
                console.log('Response data:', data);
                if (data.success) {
                    showToast('用户信息更新成功！', 'success');
                    closeModal('editModal');
                    // 刷新页面以显示更新后的数据
                    setTimeout(() => {
                        window.location.reload();
                    }, 1000);
                } else {
                    showToast('更新失败：' + (data.message || '未知错误'), 'danger');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showToast('更新失败，请稍后重试', 'danger');
            });
        });

        // 发送短信表单提交
        document.getElementById('smsForm').addEventListener('submit', function(e) {
            e.preventDefault();

            const smsData = {
                phone: document.getElementById('smsPhone').value,
                type: document.getElementById('smsType').value,
                content: document.getElementById('smsContent').value
            };

            if (!smsData.type || !smsData.content) {
                showToast('请填写完整的短信信息', 'warning');
                return;
            }

            // 这里可以添加AJAX请求发送短信
            showToast('短信发送功能开发中...', 'info');
            closeModal('smsModal');
        });

        // 发送系统消息表单提交
        document.getElementById('messageForm').addEventListener('submit', function(e) {
            e.preventDefault();

            const messageData = {
                type: document.getElementById('messageType').value,
                title: document.getElementById('messageTitle').value,
                content: document.getElementById('messageContent').value
            };

            if (!messageData.type || !messageData.title || !messageData.content) {
                showToast('请填写完整的消息信息', 'warning');
                return;
            }

            // 这里可以添加AJAX请求发送消息
            showToast('系统消息发送功能开发中...', 'info');
            closeModal('messageModal');
        });

        // 发送验证码表单提交
        document.getElementById('verificationCodeForm').addEventListener('submit', function(e) {
            e.preventDefault();

            const verificationData = {
                user_id: <?php echo $user['id']; ?>,
                phone: document.getElementById('verificationPhone').value,
                type: document.getElementById('verificationType').value,
                note: document.getElementById('verificationNote').value,
                expiry: document.getElementById('verificationExpiry').value
            };

            if (!verificationData.type || !verificationData.note) {
                showToast('请填写完整的验证码信息', 'warning');
                return;
            }

            // 显示发送中状态
            const submitBtn = document.querySelector('#verificationCodeForm button[type="submit"]');
            const originalText = submitBtn.innerHTML;
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 发送中...';
            submitBtn.disabled = true;

            // 发送验证码请求（使用调试版本）
            fetch('send_verification_code_debug.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(verificationData)
            })
            .then(response => {
                console.log('Response status:', response.status);
                console.log('Response headers:', response.headers);
                return response.text(); // 先获取原始文本
            })
            .then(text => {
                console.log('Raw response:', text);
                try {
                    const data = JSON.parse(text);
                    console.log('Parsed data:', data);

                    if (data.success) {
                        showToast('验证码发送成功！用户将实时收到验证码弹窗', 'success');

                        // 显示发送成功的详细信息
                        if (data.data && data.data.code) {
                            showToast(`验证码：${data.data.code}，已推送到前台`, 'info');
                        }

                        closeModal('verificationCodeModal');
                        // 清空表单
                        document.getElementById('verificationCodeForm').reset();

                        // 显示实时通知提示
                        setTimeout(() => {
                            showToast('用户前台应该已收到验证码弹窗通知', 'success');
                        }, 2000);
                    } else {
                        showToast('发送失败：' + (data.message || '未知错误'), 'danger');
                        // 显示调试信息
                        if (data.debug) {
                            console.log('Debug info:', data.debug);
                            // 如果是数据库字段问题，提供修复建议
                            if (data.debug.error && data.debug.error.includes('priority')) {
                                showToast('数据库字段缺失，请先访问前台修复数据库字段', 'warning');
                            }
                        }
                    }
                } catch (parseError) {
                    console.error('JSON parse error:', parseError);
                    console.error('Response text:', text);
                    showToast('服务器返回格式错误：' + text.substring(0, 100), 'danger');
                }
            })
            .catch(error => {
                console.error('Fetch error:', error);
                showToast('网络请求失败，请稍后重试', 'danger');
            })
            .finally(() => {
                // 恢复按钮状态
                submitBtn.innerHTML = originalText;
                submitBtn.disabled = false;
            });
        });

        // 添加操作日志表单提交
        document.getElementById('logForm').addEventListener('submit', function(e) {
            e.preventDefault();

            const formData = new FormData();
            formData.append('action', 'add_log');
            formData.append('user_id', <?php echo $user['id']; ?>);
            formData.append('log_type', document.getElementById('logType').value);
            formData.append('log_content', document.getElementById('logContent').value);
            formData.append('log_reason', document.getElementById('logReason').value);

            if (!formData.get('log_type') || !formData.get('log_content')) {
                showToast('请填写完整的日志信息', 'warning');
                return;
            }

            // 提交日志
            fetch('action.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.text())
            .then(data => {
                showToast('日志添加成功！', 'success');
                closeModal('logModal');
                // 清空表单
                document.getElementById('logForm').reset();
                // 刷新页面以显示新日志
                setTimeout(() => {
                    window.location.reload();
                }, 1000);
            })
            .catch(error => {
                console.error('Error:', error);
                showToast('日志添加失败，请稍后重试', 'danger');
            });
        });

        // 短信内容字数统计
        document.getElementById('smsContent').addEventListener('input', function() {
            const count = this.value.length;
            document.getElementById('smsCount').textContent = count;

            if (count > 70) {
                document.getElementById('smsCount').style.color = 'var(--danger-color)';
            } else {
                document.getElementById('smsCount').style.color = 'var(--gray-600)';
            }
        });

        // Toast 提示函数
        function showToast(message, type = 'info') {
            const colors = {
                info: '#3B82F6',
                success: '#10B981',
                warning: '#F59E0B',
                danger: '#EF4444'
            };

            const toast = document.createElement('div');
            toast.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: ${colors[type]};
                color: white;
                padding: 16px 24px;
                border-radius: 12px;
                box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
                z-index: 10001;
                font-weight: 600;
                transform: translateX(100%);
                transition: transform 0.3s ease;
                max-width: 300px;
            `;
            toast.textContent = message;
            document.body.appendChild(toast);

            setTimeout(() => toast.style.transform = 'translateX(0)', 100);
            setTimeout(() => {
                toast.style.transform = 'translateX(100%)';
                setTimeout(() => document.body.removeChild(toast), 300);
            }, 3000);
        }

        // 页面加载动画
        document.addEventListener('DOMContentLoaded', function() {
            const elements = document.querySelectorAll('.card');
            elements.forEach((el, index) => {
                el.style.opacity = '0';
                el.style.transform = 'translateY(20px)';
                setTimeout(() => {
                    el.style.transition = 'all 0.6s ease';
                    el.style.opacity = '1';
                    el.style.transform = 'translateY(0)';
                }, index * 150);
            });
        });
    </script>
</body>
</html>
