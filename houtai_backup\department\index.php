<?php
/**
 * 部门管理 - 部门总览页面
 * 趣玩星球管理后台
 */

session_start();

// 登录检查
if (!isset($_SESSION['admin_id'])) {
    header('Location: ../login.php');
    exit;
}

$admin_name = $_SESSION['admin_name'] ?? '管理员';
$admin_id = $_SESSION['admin_id'] ?? '';
$admin_employee_id = $_SESSION['admin_employee_id'] ?? '';

// 模拟部门数据
$departments = [
    [
        'id' => 1,
        'name' => '客户服务中心',
        'code' => 'CS',
        'parent_id' => null,
        'manager_name' => '张小明',
        'manager_id' => 'CS001',
        'employee_count' => 15,
        'description' => '负责用户咨询、投诉处理和客户关系维护',
        'status' => 'active',
        'created_at' => '2024-01-01',
        'children' => [
            [
                'id' => 11,
                'name' => '在线客服组',
                'code' => 'CS-ONLINE',
                'manager_name' => '李小红',
                'employee_count' => 8
            ],
            [
                'id' => 12,
                'name' => '电话客服组',
                'code' => 'CS-PHONE',
                'manager_name' => '王小强',
                'employee_count' => 7
            ]
        ]
    ],
    [
        'id' => 2,
        'name' => '行政管理中心',
        'code' => 'ADMIN',
        'parent_id' => null,
        'manager_name' => '陈小华',
        'manager_id' => 'AD001',
        'employee_count' => 8,
        'description' => '负责公司行政事务、办公环境和后勤保障',
        'status' => 'active',
        'created_at' => '2024-01-01',
        'children' => []
    ],
    [
        'id' => 3,
        'name' => '人资与组织运营中心',
        'code' => 'HR',
        'parent_id' => null,
        'manager_name' => '刘小丽',
        'manager_id' => 'HR001',
        'employee_count' => 6,
        'description' => '负责人力资源管理、组织架构优化和员工发展',
        'status' => 'active',
        'created_at' => '2024-01-01',
        'children' => []
    ],
    [
        'id' => 4,
        'name' => 'MCN直播运营中心',
        'code' => 'MCN',
        'parent_id' => null,
        'manager_name' => '赵小军',
        'manager_id' => 'MCN001',
        'employee_count' => 12,
        'description' => '负责直播内容策划、主播管理和直播运营',
        'status' => 'active',
        'created_at' => '2024-01-15',
        'children' => [
            [
                'id' => 41,
                'name' => '主播管理组',
                'code' => 'MCN-ANCHOR',
                'manager_name' => '孙小美',
                'employee_count' => 6
            ],
            [
                'id' => 42,
                'name' => '内容策划组',
                'code' => 'MCN-CONTENT',
                'manager_name' => '周小亮',
                'employee_count' => 6
            ]
        ]
    ],
    [
        'id' => 5,
        'name' => 'UGC视频创作中心',
        'code' => 'UGC',
        'parent_id' => null,
        'manager_name' => '吴小芳',
        'manager_id' => 'UGC001',
        'employee_count' => 10,
        'description' => '负责用户生成内容的审核、推广和创作者扶持',
        'status' => 'active',
        'created_at' => '2024-02-01',
        'children' => []
    ]
];
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>部门总览 - 趣玩星球管理后台</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="../assets/css/admin-layout.css">
    <style>
        /* 部门总览页面专用样式 */
        .department-content {
            padding: 24px;
        }

        .page-actions {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 32px;
        }

        .view-toggle {
            display: flex;
            background: white;
            border-radius: var(--border-radius);
            padding: 4px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        .toggle-btn {
            padding: 8px 16px;
            border: none;
            background: transparent;
            border-radius: var(--border-radius);
            cursor: pointer;
            transition: var(--transition);
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 0.875rem;
        }

        .toggle-btn.active {
            background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
            color: white;
        }

        .add-dept-btn {
            background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: var(--border-radius);
            font-weight: 600;
            cursor: pointer;
            transition: var(--transition);
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .add-dept-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(64, 224, 208, 0.3);
        }

        /* 统计概览 */
        .stats-overview {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 32px;
        }

        .stat-card {
            background: white;
            border-radius: 16px;
            padding: 20px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
            text-align: center;
            transition: all 0.3s ease;
        }

        .stat-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15);
        }

        .stat-icon {
            width: 60px;
            height: 60px;
            margin: 0 auto 16px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            color: white;
        }

        .stat-number {
            font-size: 2rem;
            font-weight: 700;
            color: var(--gray-800);
            margin-bottom: 4px;
        }

        .stat-label {
            color: var(--gray-600);
            font-size: 0.875rem;
        }

        /* 部门网格视图 */
        .departments-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
            gap: 24px;
        }

        .dept-card {
            background: white;
            border-radius: 16px;
            padding: 24px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
            border: 2px solid transparent;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .dept-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, var(--primary-color), var(--primary-light));
        }

        .dept-card:hover {
            border-color: var(--primary-color);
            transform: translateY(-4px);
            box-shadow: 0 12px 32px rgba(0, 0, 0, 0.15);
        }

        .dept-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 16px;
        }

        .dept-info h3 {
            font-size: 1.25rem;
            font-weight: 700;
            color: var(--gray-800);
            margin-bottom: 4px;
        }

        .dept-code {
            font-size: 0.75rem;
            color: var(--gray-500);
            background: var(--gray-100);
            padding: 2px 8px;
            border-radius: 12px;
            font-family: monospace;
        }

        .dept-status {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 0.75rem;
            font-weight: 600;
        }

        .status-active {
            background: rgba(16, 185, 129, 0.1);
            color: var(--success-color);
        }

        .dept-description {
            color: var(--gray-600);
            font-size: 0.875rem;
            line-height: 1.5;
            margin-bottom: 20px;
        }

        .dept-manager {
            display: flex;
            align-items: center;
            gap: 12px;
            margin-bottom: 16px;
            padding: 12px;
            background: var(--gray-50);
            border-radius: var(--border-radius);
        }

        .manager-avatar {
            width: 40px;
            height: 40px;
            background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
            border-radius: var(--border-radius);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: 600;
        }

        .manager-info h4 {
            font-weight: 600;
            color: var(--gray-800);
            margin-bottom: 2px;
        }

        .manager-title {
            font-size: 0.75rem;
            color: var(--gray-500);
        }

        .dept-stats {
            display: flex;
            justify-content: space-between;
            margin-bottom: 20px;
        }

        .dept-stat {
            text-align: center;
        }

        .dept-stat-number {
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--primary-color);
        }

        .dept-stat-label {
            font-size: 0.75rem;
            color: var(--gray-500);
        }

        .dept-children {
            margin-bottom: 20px;
        }

        .children-title {
            font-size: 0.875rem;
            font-weight: 600;
            color: var(--gray-700);
            margin-bottom: 8px;
        }

        .child-dept {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 12px;
            background: var(--gray-50);
            border-radius: 8px;
            margin-bottom: 4px;
            font-size: 0.875rem;
        }

        .child-name {
            color: var(--gray-700);
        }

        .child-count {
            color: var(--gray-500);
            font-size: 0.75rem;
        }

        .dept-actions {
            display: flex;
            gap: 8px;
        }

        .action-btn {
            flex: 1;
            padding: 8px 12px;
            border: none;
            border-radius: var(--border-radius);
            font-size: 0.875rem;
            font-weight: 600;
            cursor: pointer;
            transition: var(--transition);
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 6px;
        }

        .btn-view {
            background: var(--info-color);
            color: white;
        }

        .btn-view:hover {
            background: #2563EB;
            transform: translateY(-1px);
        }

        .btn-edit {
            background: var(--secondary-color);
            color: white;
        }

        .btn-edit:hover {
            background: #5B21B6;
            transform: translateY(-1px);
        }

        .btn-delete {
            background: var(--error-color);
            color: white;
        }

        .btn-delete:hover {
            background: #DC2626;
            transform: translateY(-1px);
        }

        /* 树形视图 */
        .departments-tree {
            background: white;
            border-radius: 16px;
            padding: 24px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
            display: none;
        }

        .departments-tree.active {
            display: block;
        }

        .tree-node {
            margin-bottom: 16px;
        }

        .tree-item {
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 12px;
            border-radius: var(--border-radius);
            transition: var(--transition);
            cursor: pointer;
        }

        .tree-item:hover {
            background: var(--gray-50);
        }

        .tree-icon {
            width: 32px;
            height: 32px;
            background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 14px;
        }

        .tree-info {
            flex: 1;
        }

        .tree-name {
            font-weight: 600;
            color: var(--gray-800);
            margin-bottom: 2px;
        }

        .tree-meta {
            font-size: 0.75rem;
            color: var(--gray-500);
        }

        .tree-children {
            margin-left: 44px;
            border-left: 2px solid var(--gray-200);
            padding-left: 16px;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .department-content {
                padding: 16px;
            }

            .departments-grid {
                grid-template-columns: 1fr;
            }

            .page-actions {
                flex-direction: column;
                gap: 16px;
                align-items: stretch;
            }

            .dept-actions {
                flex-direction: column;
            }
        }
    </style>
</head>
<body>
    <div class="admin-layout">
        <!-- 左侧菜单 -->
        <?php include '../includes/sidebar.php'; ?>

        <!-- 主内容区域 -->
        <main class="main-content">
            <!-- 顶部导航栏 -->
            <?php include '../includes/topbar.php'; ?>

            <div class="department-content">
                <!-- 页面标题和操作 -->
                <div class="page-actions">
                    <div>
                        <h1 class="page-title">
                            <i class="fas fa-sitemap" style="color: var(--primary-color);"></i>
                            部门总览
                        </h1>
                        <p class="page-subtitle">管理公司组织架构和部门信息</p>
                    </div>
                    <div style="display: flex; gap: 16px; align-items: center;">
                        <div class="view-toggle">
                            <button class="toggle-btn active" onclick="switchView('grid')">
                                <i class="fas fa-th-large"></i>
                                网格视图
                            </button>
                            <button class="toggle-btn" onclick="switchView('tree')">
                                <i class="fas fa-sitemap"></i>
                                树形视图
                            </button>
                        </div>
                        <button class="add-dept-btn" onclick="openDeptModal()">
                            <i class="fas fa-plus"></i>
                            新建部门
                        </button>
                    </div>
                </div>

                <!-- 统计概览 -->
                <div class="stats-overview">
                    <div class="stat-card">
                        <div class="stat-icon" style="background: linear-gradient(135deg, var(--primary-color), var(--primary-light));">
                            <i class="fas fa-building"></i>
                        </div>
                        <div class="stat-number"><?php echo count($departments); ?></div>
                        <div class="stat-label">总部门数</div>
                    </div>

                    <div class="stat-card">
                        <div class="stat-icon" style="background: linear-gradient(135deg, var(--success-color), #34D399);">
                            <i class="fas fa-users"></i>
                        </div>
                        <div class="stat-number">
                            <?php
                            $total_employees = 0;
                            foreach ($departments as $dept) {
                                $total_employees += $dept['employee_count'];
                            }
                            echo $total_employees;
                            ?>
                        </div>
                        <div class="stat-label">总员工数</div>
                    </div>

                    <div class="stat-card">
                        <div class="stat-icon" style="background: linear-gradient(135deg, var(--info-color), #60A5FA);">
                            <i class="fas fa-user-tie"></i>
                        </div>
                        <div class="stat-number"><?php echo count($departments); ?></div>
                        <div class="stat-label">部门经理</div>
                    </div>

                    <div class="stat-card">
                        <div class="stat-icon" style="background: linear-gradient(135deg, var(--warning-color), #FBBF24);">
                            <i class="fas fa-chart-line"></i>
                        </div>
                        <div class="stat-number">
                            <?php
                            $avg_size = $total_employees / count($departments);
                            echo round($avg_size, 1);
                            ?>
                        </div>
                        <div class="stat-label">平均规模</div>
                    </div>
                </div>

                <!-- 网格视图 -->
                <div class="departments-grid" id="gridView">
                    <?php foreach ($departments as $dept): ?>
                        <div class="dept-card">
                            <div class="dept-header">
                                <div class="dept-info">
                                    <h3><?php echo htmlspecialchars($dept['name']); ?></h3>
                                    <div class="dept-code"><?php echo htmlspecialchars($dept['code']); ?></div>
                                </div>
                                <div class="dept-status status-<?php echo $dept['status']; ?>">
                                    <?php echo $dept['status'] === 'active' ? '正常' : '停用'; ?>
                                </div>
                            </div>

                            <div class="dept-description">
                                <?php echo htmlspecialchars($dept['description']); ?>
                            </div>

                            <div class="dept-manager">
                                <div class="manager-avatar">
                                    <?php echo mb_substr($dept['manager_name'], 0, 1, 'UTF-8'); ?>
                                </div>
                                <div class="manager-info">
                                    <h4><?php echo htmlspecialchars($dept['manager_name']); ?></h4>
                                    <div class="manager-title">部门经理</div>
                                </div>
                            </div>

                            <div class="dept-stats">
                                <div class="dept-stat">
                                    <div class="dept-stat-number"><?php echo $dept['employee_count']; ?></div>
                                    <div class="dept-stat-label">员工数</div>
                                </div>
                                <div class="dept-stat">
                                    <div class="dept-stat-number"><?php echo count($dept['children']); ?></div>
                                    <div class="dept-stat-label">子部门</div>
                                </div>
                                <div class="dept-stat">
                                    <div class="dept-stat-number"><?php echo date('m-d', strtotime($dept['created_at'])); ?></div>
                                    <div class="dept-stat-label">创建时间</div>
                                </div>
                            </div>

                            <?php if (!empty($dept['children'])): ?>
                                <div class="dept-children">
                                    <div class="children-title">子部门</div>
                                    <?php foreach ($dept['children'] as $child): ?>
                                        <div class="child-dept">
                                            <span class="child-name"><?php echo htmlspecialchars($child['name']); ?></span>
                                            <span class="child-count"><?php echo $child['employee_count']; ?>人</span>
                                        </div>
                                    <?php endforeach; ?>
                                </div>
                            <?php endif; ?>

                            <div class="dept-actions">
                                <button class="action-btn btn-view" onclick="viewDept(<?php echo $dept['id']; ?>)">
                                    <i class="fas fa-eye"></i>
                                    查看
                                </button>
                                <button class="action-btn btn-edit" onclick="editDept(<?php echo $dept['id']; ?>)">
                                    <i class="fas fa-edit"></i>
                                    编辑
                                </button>
                                <button class="action-btn btn-delete" onclick="deleteDept(<?php echo $dept['id']; ?>)">
                                    <i class="fas fa-trash"></i>
                                    删除
                                </button>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>

                <!-- 树形视图 -->
                <div class="departments-tree" id="treeView">
                    <?php foreach ($departments as $dept): ?>
                        <div class="tree-node">
                            <div class="tree-item">
                                <div class="tree-icon">
                                    <i class="fas fa-building"></i>
                                </div>
                                <div class="tree-info">
                                    <div class="tree-name"><?php echo htmlspecialchars($dept['name']); ?></div>
                                    <div class="tree-meta">
                                        <?php echo htmlspecialchars($dept['manager_name']); ?> | <?php echo $dept['employee_count']; ?>人
                                    </div>
                                </div>
                            </div>

                            <?php if (!empty($dept['children'])): ?>
                                <div class="tree-children">
                                    <?php foreach ($dept['children'] as $child): ?>
                                        <div class="tree-item">
                                            <div class="tree-icon" style="background: linear-gradient(135deg, var(--info-color), #60A5FA);">
                                                <i class="fas fa-users"></i>
                                            </div>
                                            <div class="tree-info">
                                                <div class="tree-name"><?php echo htmlspecialchars($child['name']); ?></div>
                                                <div class="tree-meta">
                                                    <?php echo htmlspecialchars($child['manager_name']); ?> | <?php echo $child['employee_count']; ?>人
                                                </div>
                                            </div>
                                        </div>
                                    <?php endforeach; ?>
                                </div>
                            <?php endif; ?>
                        </div>
                    <?php endforeach; ?>
                </div>
            </div>
        </main>
    </div>

    <script src="../assets/js/admin-layout.js"></script>
    <script>
        // 部门总览页面脚本
        document.addEventListener('DOMContentLoaded', function() {
            // 更新页面标题
            updatePageTitle('部门总览');

            // 动画效果
            const cards = document.querySelectorAll('.dept-card, .stat-card');
            cards.forEach((card, index) => {
                card.style.opacity = '0';
                card.style.transform = 'translateY(20px)';
                setTimeout(() => {
                    card.style.transition = 'all 0.5s ease';
                    card.style.opacity = '1';
                    card.style.transform = 'translateY(0)';
                }, index * 50);
            });
        });

        function switchView(viewType) {
            const gridView = document.getElementById('gridView');
            const treeView = document.getElementById('treeView');
            const toggleBtns = document.querySelectorAll('.toggle-btn');

            toggleBtns.forEach(btn => btn.classList.remove('active'));

            if (viewType === 'grid') {
                gridView.style.display = 'grid';
                treeView.classList.remove('active');
                toggleBtns[0].classList.add('active');
            } else {
                gridView.style.display = 'none';
                treeView.classList.add('active');
                toggleBtns[1].classList.add('active');
            }
        }

        function openDeptModal() {
            alert('新建部门功能开发中...\n\n将打开部门创建表单，可以设置部门名称、代码、描述、负责人等信息。');
        }

        function viewDept(deptId) {
            alert(`查看部门详情功能开发中...\n\n将显示部门 ${deptId} 的详细信息和员工列表。`);
        }

        function editDept(deptId) {
            alert(`编辑部门功能开发中...\n\n将打开部门 ${deptId} 的编辑表单。`);
        }

        function deleteDept(deptId) {
            if (confirm('确定要删除这个部门吗？\n\n删除后，该部门下的员工将需要重新分配。')) {
                alert('部门删除成功！');
                setTimeout(() => {
                    location.reload();
                }, 1000);
            }
        }

        console.log('🏢 部门总览页面已加载');
    </script>
</body>
</html>
