<?php
// 设置响应头
header('Content-Type: application/json');

// 启动会话
session_start();

// 检查用户是否已登录
if (!isset($_SESSION['user_id'])) {
    echo json_encode([
        'success' => false,
        'message' => '请先登录'
    ]);
    exit;
}

// 检查必填参数
if (!isset($_POST['post_id']) || empty($_POST['post_id'])) {
    echo json_encode([
        'success' => false,
        'message' => '缺少内容ID'
    ]);
    exit;
}

// 获取参数
$post_id = intval($_POST['post_id']);
$user_id = $_SESSION['user_id'];

// 引入数据库配置
require_once '../../../includes/db_config.php';

try {
    // 连接数据库
    $dsn = "mysql:host={$db_config['host']};dbname={$db_config['dbname']};charset={$db_config['charset']};port={$db_config['port']}";
    $pdo = new PDO($dsn, $db_config['username'], $db_config['password']);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

    // 开始事务
    $pdo->beginTransaction();

    // 检查内容是否存在且属于当前用户
    $stmt = $pdo->prepare("SELECT id FROM universe_posts WHERE id = :id AND user_id = :user_id AND status = 1");
    $stmt->execute([
        'id' => $post_id,
        'user_id' => $user_id
    ]);

    if (!$stmt->fetch()) {
        echo json_encode([
            'success' => false,
            'message' => '内容不存在或无权删除'
        ]);
        exit;
    }

    // 软删除内容（将状态设为2表示已删除）
    $stmt = $pdo->prepare("UPDATE universe_posts SET status = 2 WHERE id = :id");
    $stmt->execute(['id' => $post_id]);

    // 提交事务
    $pdo->commit();

    // 返回成功信息
    echo json_encode([
        'success' => true,
        'message' => '删除成功'
    ]);
} catch (PDOException $e) {
    // 回滚事务
    if ($pdo->inTransaction()) {
        $pdo->rollBack();
    }

    // 记录错误
    error_log("删除内容错误: " . $e->getMessage());

    // 返回错误信息
    echo json_encode([
        'success' => false,
        'message' => '删除失败，请稍后再试'
    ]);
}
