<?php
// 结束会话API
header('Content-Type: application/json; charset=UTF-8');
ini_set('default_charset', 'UTF-8');

session_start();

// 检查客服登录状态
if (!isset($_SESSION['cs_logged_in']) || !$_SESSION['cs_logged_in']) {
    http_response_code(401);
    echo json_encode(['error' => '客服未登录']);
    exit;
}

// 只接受POST请求
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['error' => '方法不允许']);
    exit;
}

// 获取POST数据
$input = json_decode(file_get_contents('php://input'), true);
$sessionId = $input['sessionId'] ?? '';
$reason = trim($input['reason'] ?? '');

if (empty($sessionId)) {
    http_response_code(400);
    echo json_encode(['error' => '会话ID不能为空']);
    exit;
}

// 引用数据库配置文件
require_once '../../db_config.php';

try {
    $pdo = getDbConnection();

    // 检查会话是否存在
    $stmt = $pdo->prepare("SELECT * FROM customer_service_sessions WHERE session_id = ?");
    $stmt->execute([$sessionId]);
    $session = $stmt->fetch(PDO::FETCH_ASSOC);

    if (!$session) {
        http_response_code(404);
        echo json_encode(['error' => '会话不存在']);
        exit;
    }

    if ($session['status'] === 'closed') {
        http_response_code(400);
        echo json_encode(['error' => '会话已经结束']);
        exit;
    }

    // 检查权限（只有分配的客服或超级管理员可以结束会话）
    if ($session['customer_service_id'] != $_SESSION['cs_user_id'] && $_SESSION['cs_role'] !== 'super_admin') {
        http_response_code(403);
        echo json_encode(['error' => '无权限结束此会话']);
        exit;
    }

    // 开始事务
    $pdo->beginTransaction();

    try {
        // 更新会话状态为已结束
        $stmt = $pdo->prepare("
            UPDATE customer_service_sessions
            SET status = 'closed', ended_at = NOW(), updated_at = NOW()
            WHERE session_id = ?
        ");
        $stmt->execute([$sessionId]);

        // 添加系统消息记录会话结束
        $systemMessage = "客服 " . $_SESSION['cs_name'] . " 已结束会话";
        if (!empty($reason)) {
            $systemMessage .= "，原因：" . $reason;
        }

        $stmt = $pdo->prepare("
            INSERT INTO customer_service_messages
            (session_id, sender_type, sender_id, sender_name, message_type, content, created_at)
            VALUES (?, 'system', NULL, '系统', 'system', ?, NOW())
        ");
        $stmt->execute([$sessionId, $systemMessage]);

        // 更新会话消息数量
        $stmt = $pdo->prepare("
            UPDATE customer_service_sessions
            SET message_count = message_count + 1
            WHERE session_id = ?
        ");
        $stmt->execute([$sessionId]);

        // 如果会话有用户ID，发送实时通知给前台用户
        if ($session['user_id']) {
            $notificationData = [
                'type' => 'session_closed',
                'session_id' => $sessionId,
                'cs_name' => $_SESSION['cs_name'],
                'reason' => $reason,
                'timestamp' => time()
            ];

            // 检查 realtime_notifications 表是否有 message 字段
            $stmt = $pdo->query("SHOW COLUMNS FROM realtime_notifications LIKE 'message'");
            $hasMessageColumn = $stmt->rowCount() > 0;

            if ($hasMessageColumn) {
                // 有 message 字段的版本
                $stmt = $pdo->prepare("
                    INSERT INTO realtime_notifications
                    (user_id, type, title, message, data, status, created_at)
                    VALUES (?, 'session_closed', '会话已结束', ?, ?, 'unread', NOW())
                ");
                $stmt->execute([
                    $session['user_id'],
                    '客服服务已结束，感谢您的使用！',
                    json_encode($notificationData, JSON_UNESCAPED_UNICODE)
                ]);
            } else {
                // 没有 message 字段的版本
                $stmt = $pdo->prepare("
                    INSERT INTO realtime_notifications
                    (user_id, type, title, data, status, created_at)
                    VALUES (?, 'session_closed', '会话已结束', ?, 'unread', NOW())
                ");
                $stmt->execute([
                    $session['user_id'],
                    json_encode($notificationData, JSON_UNESCAPED_UNICODE)
                ]);
            }
        }

        // 提交事务
        $pdo->commit();

        echo json_encode([
            'success' => true,
            'message' => '会话已成功结束',
            'session' => [
                'session_id' => $sessionId,
                'status' => 'closed',
                'ended_at' => date('Y-m-d H:i:s')
            ]
        ]);

    } catch (Exception $e) {
        $pdo->rollBack();
        throw $e;
    }

} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => '结束会话失败',
        'message' => $e->getMessage()
    ]);
}
?>
