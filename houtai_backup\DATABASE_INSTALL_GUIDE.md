# 🗄️ 数据库安装指南

## 📋 安装步骤

### 1. 执行数据库结构创建
请在您的MySQL数据库中执行以下SQL文件：

```sql
-- 第一步：创建数据库表结构
SOURCE /path/to/houtai_backup/DATABASE_SETUP.sql;
```

或者通过phpMyAdmin、Navicat等工具导入 `DATABASE_SETUP.sql` 文件。

### 2. 插入初始数据
执行初始数据插入脚本：

```sql
-- 第二步：插入初始数据
SOURCE /path/to/houtai_backup/DATABASE_INIT_DATA.sql;
```

或者通过数据库管理工具导入 `DATABASE_INIT_DATA.sql` 文件。

## 📊 创建的数据库表

### 核心表结构
1. **departments** - 部门表
2. **roles** - 角色表
3. **permissions** - 权限表
4. **role_permissions** - 角色权限关联表
5. **user_roles** - 用户角色关联表
6. **permission_requests** - 权限申请表
7. **user_permissions** - 用户权限表

### 扩展字段
对现有的 `admin_users` 表添加以下字段：
- `department_id` - 所属部门
- `position` - 职位
- `level` - 职级
- `direct_manager_id` - 直属上级
- `hire_date` - 入职日期

## 🏢 初始部门数据

系统将创建以下15个部门：
1. 客户服务中心 (CS)
2. 行政管理中心 (ADMIN)
3. 人资与组织运营中心 (HR)
4. MCN直播运营中心 (MCN)
5. UGC视频创作中心 (UGC)
6. 财务与商分中心 (FINANCE)
7. 战略发展中心 (STRATEGY)
8. 职能支持中心 (SUPPORT)
9. 用户体验中心 (UX)
10. 市场与商务中心 (MARKETING)
11. 城市玩伴中心 (CITY)
12. 游戏瓦板中心 (GAME)
13. 组局搭子中心 (TEAM)
14. 景点门票中心 (TICKET)
15. 攻略内容运营中心 (CONTENT)

## 👥 初始角色数据

系统将创建以下角色：
1. **超级管理员** (super_admin) - 级别10
2. **部门总管理员** (dept_super_admin) - 级别9
3. **部门管理员** (dept_admin) - 级别8
4. **部门主管** (dept_supervisor) - 级别6
5. **审核专员** (reviewer) - 级别5
6. **客服专员** (customer_service) - 级别4
7. **数据分析师** (data_analyst) - 级别5
8. **内容运营** (content_operator) - 级别4
9. **普通员工** (employee) - 级别2
10. **实习生** (intern) - 级别1

## 🔐 初始权限数据

系统将创建27个权限，涵盖以下模块：
- **用户管理** (user) - 5个权限
- **认证审核** (verification) - 3个权限
- **权限管理** (permission) - 4个权限
- **部门管理** (department) - 5个权限
- **系统管理** (system) - 4个权限
- **数据统计** (stats) - 3个权限
- **内容管理** (content) - 3个权限

## ⚠️ 重要说明

### 安全注意事项
1. 请在生产环境中修改默认的超级管理员密码
2. 根据实际需要调整权限分配
3. 定期备份数据库

### 兼容性检查
- 脚本会自动检查现有表结构，避免重复创建
- 使用 `INSERT IGNORE` 避免重复插入数据
- 兼容现有的 `admin_users` 表结构

### 故障排除
如果遇到外键约束错误：
1. 确保 `admin_users` 表已存在
2. 检查字符集设置是否为 `utf8mb4`
3. 确认MySQL版本支持外键约束

## 🔗 相关文件

- `DATABASE_SETUP.sql` - 数据库表结构
- `DATABASE_INIT_DATA.sql` - 初始数据
- `PERMISSION_SYSTEM_DESIGN.md` - 权限系统设计文档
- `db_config.php` - 数据库连接配置

## 📞 技术支持

如果在安装过程中遇到问题，请检查：
1. 数据库连接配置是否正确
2. MySQL用户是否有足够的权限
3. 数据库字符集是否设置为 utf8mb4

---

**安装完成后，权限管理和部门管理功能将完全可用！** 🎉
