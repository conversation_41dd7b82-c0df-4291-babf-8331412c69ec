<?php
/**
 * 简化版登录安全检测API
 * 用于测试和调试
 */

session_start();
header('Content-Type: application/json; charset=utf-8');

// 错误处理
error_reporting(0);
ini_set('display_errors', 0);

// 捕获所有错误并返回JSON
set_error_handler(function($severity, $message, $file, $line) {
    echo json_encode(['success' => false, 'message' => '系统错误，请稍后重试']);
    exit;
});

set_exception_handler(function($exception) {
    echo json_encode(['success' => false, 'message' => '系统错误，请稍后重试']);
    exit;
});

// 数据库连接配置
$db_config = [
    'host' => 'localhost',
    'username' => 'quwanplanet',
    'password' => 'nJmJm23FB4Xn6Fc3',
    'dbname' => 'quwanplanet',
    'port' => 3306,
    'charset' => 'utf8mb4'
];

try {
    $pdo = new PDO(
        "mysql:host={$db_config['host']};port={$db_config['port']};dbname={$db_config['dbname']};charset={$db_config['charset']}",
        $db_config['username'],
        $db_config['password'],
        [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
            PDO::ATTR_EMULATE_PREPARES => false
        ]
    );
} catch (PDOException $e) {
    echo json_encode(['success' => false, 'message' => '数据库连接失败']);
    exit;
}

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode(['success' => false, 'message' => '请求方法错误']);
    exit;
}

$input = json_decode(file_get_contents('php://input'), true);
$identifier = trim($input['identifier'] ?? '');

if (empty($identifier)) {
    echo json_encode(['success' => false, 'message' => '请输入手机号或趣玩ID']);
    exit;
}

// 查找用户
$user = null;
try {
    if (preg_match('/^\d{11}$/', $identifier)) {
        // 手机号格式
        $stmt = $pdo->prepare("SELECT id, username, quwan_id, phone, avatar, status FROM users WHERE phone = ?");
        $stmt->execute([$identifier]);
        $user = $stmt->fetch();
    } elseif (preg_match('/^\d{7,9}$/', $identifier)) {
        // 趣玩ID格式
        $stmt = $pdo->prepare("SELECT id, username, quwan_id, phone, avatar, status FROM users WHERE quwan_id = ?");
        $stmt->execute([$identifier]);
        $user = $stmt->fetch();
    }
} catch (PDOException $e) {
    echo json_encode(['success' => false, 'message' => '查询用户失败']);
    exit;
}

if (!$user) {
    echo json_encode(['success' => false, 'message' => '用户不存在']);
    exit;
}

// 检查用户状态
if ($user['status'] === 'banned') {
    echo json_encode(['success' => false, 'message' => '账户已被封禁，请联系客服']);
    exit;
}

// 简化的安全检测 - 随机决定是否需要验证
$current_ip = $_SERVER['REMOTE_ADDR'];
$is_trusted = false;

// 如果是本地IP，认为是可信的
if ($current_ip === '127.0.0.1' || $current_ip === '::1') {
    $is_trusted = true;
} else {
    // 其他情况随机决定（实际项目中应该基于真实的安全检测）
    $is_trusted = (rand(1, 100) <= 30); // 30%概率为可信
}

// 返回结果
echo json_encode([
    'success' => true,
    'user' => [
        'id' => $user['id'],
        'username' => $user['username'],
        'quwan_id' => $user['quwan_id'],
        'phone' => $user['phone'],
        'avatar' => $user['avatar'] ?? 'https://s1.imagehub.cc/images/2025/04/26/f5c2f26c4564aabcbb56c5fd30f1d652.jpg'
    ],
    'security' => [
        'risk_level' => $is_trusted ? 'low' : 'medium',
        'risks' => $is_trusted ? [] : ['unknown_device'],
        'trusted' => $is_trusted,
        'details' => [
            'ip' => ['trusted' => $is_trusted, 'reason' => $is_trusted ? 'known_ip' : 'new_ip'],
            'device' => ['trusted' => $is_trusted, 'reason' => $is_trusted ? 'known_device' : 'new_device']
        ]
    ]
]);
?>
