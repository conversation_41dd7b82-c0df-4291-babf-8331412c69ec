<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数据库结构检查</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            max-width: 1000px;
            margin: 0 auto;
        }
        .section {
            margin-bottom: 30px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .section h3 {
            margin-top: 0;
            color: #333;
            border-bottom: 2px solid #40E0D0;
            padding-bottom: 10px;
        }
        .btn {
            background: #40E0D0;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        .btn:hover {
            background: #36c7b8;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            background: #f9f9f9;
            border-radius: 5px;
            white-space: pre-wrap;
            font-family: monospace;
        }
        .recommendation {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
        }
        .success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .warning {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
        }
        .error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 10px;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        th {
            background-color: #f2f2f2;
        }
        .loading {
            text-align: center;
            padding: 20px;
            color: #666;
        }
    </style>
</head>
<body>
    <div class="container">
        <h2>数据库结构检查工具</h2>
        <p>此工具用于检查数据库中的字段结构，帮助诊断趣玩ID字段的问题。</p>

        <button class="btn" onclick="checkDatabase()">检查数据库结构</button>
        <button class="btn" onclick="clearResults()">清除结果</button>

        <div id="loading" class="loading" style="display: none;">
            正在检查数据库结构...
        </div>

        <div id="results" style="display: none;">
            <div class="section">
                <h3>🔍 检查结果</h3>
                <div id="recommendations"></div>
            </div>

            <div class="section">
                <h3>📊 数据库信息</h3>
                <div id="database-info"></div>
            </div>

            <div class="section">
                <h3>🏗️ 表结构</h3>
                <div id="table-structure"></div>
            </div>

            <div class="section">
                <h3>🔑 ID字段检查</h3>
                <div id="field-check"></div>
            </div>

            <div class="section">
                <h3>📝 样本数据</h3>
                <div id="sample-data"></div>
            </div>

            <div class="section">
                <h3>📋 索引信息</h3>
                <div id="indexes"></div>
            </div>
        </div>
    </div>

    <script>
        async function checkDatabase() {
            const loading = document.getElementById('loading');
            const results = document.getElementById('results');

            loading.style.display = 'block';
            results.style.display = 'none';

            try {
                const response = await fetch('check_database.php');
                const data = await response.json();

                loading.style.display = 'none';
                results.style.display = 'block';

                if (data.success) {
                    displayResults(data);
                } else {
                    showError(data.error);
                }
            } catch (error) {
                loading.style.display = 'none';
                showError('检查失败: ' + error.message);
            }
        }

        function displayResults(data) {
            // 显示建议
            const recommendations = document.getElementById('recommendations');
            if (data.recommendations && data.recommendations.length > 0) {
                let html = '';
                data.recommendations.forEach(rec => {
                    let className = 'recommendation';
                    if (rec.includes('✅')) className += ' success';
                    else if (rec.includes('⚠️') || rec.includes('🔧')) className += ' warning';
                    else if (rec.includes('❌')) className += ' error';

                    html += `<div class="${className}">${rec}</div>`;
                });
                recommendations.innerHTML = html;
            }

            // 显示数据库信息
            const dbInfo = document.getElementById('database-info');
            if (data.database_info) {
                dbInfo.innerHTML = `
                    <p><strong>当前数据库:</strong> ${data.database_info.current_db}</p>
                    <p><strong>MySQL版本:</strong> ${data.database_info.mysql_version}</p>
                `;
            }

            // 显示表结构
            const tableStructure = document.getElementById('table-structure');
            if (data.table_structure) {
                let html = '<table><tr><th>字段名</th><th>类型</th><th>允许NULL</th><th>键</th><th>默认值</th><th>额外</th></tr>';
                data.table_structure.forEach(field => {
                    html += `<tr>
                        <td>${field.Field}</td>
                        <td>${field.Type}</td>
                        <td>${field.Null}</td>
                        <td>${field.Key}</td>
                        <td>${field.Default || ''}</td>
                        <td>${field.Extra || ''}</td>
                    </tr>`;
                });
                html += '</table>';
                tableStructure.innerHTML = html;
            }

            // 显示字段检查
            const fieldCheck = document.getElementById('field-check');
            if (data.field_check) {
                if (data.field_check.length > 0) {
                    let html = '<table><tr><th>字段名</th><th>数据类型</th><th>允许NULL</th><th>默认值</th><th>额外</th></tr>';
                    data.field_check.forEach(field => {
                        html += `<tr>
                            <td>${field.COLUMN_NAME}</td>
                            <td>${field.DATA_TYPE}</td>
                            <td>${field.IS_NULLABLE}</td>
                            <td>${field.COLUMN_DEFAULT || ''}</td>
                            <td>${field.EXTRA || ''}</td>
                        </tr>`;
                    });
                    html += '</table>';
                    fieldCheck.innerHTML = html;
                } else {
                    fieldCheck.innerHTML = '<p class="error">❌ 没有找到 quwan_id 或 quwanplanet_id 字段</p>';
                }
            }

            // 显示样本数据
            const sampleData = document.getElementById('sample-data');
            if (data.sample_data && data.sample_data.length > 0) {
                let html = '<table><tr><th>ID</th><th>手机号</th><th>quwan_id</th><th>昵称</th></tr>';
                data.sample_data.forEach(row => {
                    html += `<tr>
                        <td>${row.id}</td>
                        <td>${row.phone || ''}</td>
                        <td>${row.quwan_id || ''}</td>
                        <td>${row.nickname || ''}</td>
                    </tr>`;
                });
                html += '</table>';
                sampleData.innerHTML = html;
            } else {
                sampleData.innerHTML = '<p>没有找到用户数据</p>';
            }

            // 显示索引信息
            const indexes = document.getElementById('indexes');
            if (data.indexes && data.indexes.length > 0) {
                let html = '<table><tr><th>表名</th><th>索引名</th><th>字段名</th><th>唯一性</th></tr>';
                data.indexes.forEach(index => {
                    html += `<tr>
                        <td>${index.Table}</td>
                        <td>${index.Key_name}</td>
                        <td>${index.Column_name}</td>
                        <td>${index.Non_unique == 0 ? '唯一' : '非唯一'}</td>
                    </tr>`;
                });
                html += '</table>';
                indexes.innerHTML = html;
            } else {
                indexes.innerHTML = '<p class="warning">⚠️ 没有找到相关索引</p>';
            }
        }

        function showError(message) {
            const results = document.getElementById('results');
            results.style.display = 'block';
            results.innerHTML = `<div class="error">错误: ${message}</div>`;
        }

        function clearResults() {
            document.getElementById('results').style.display = 'none';
            document.getElementById('loading').style.display = 'none';
        }
    </script>
</body>
</html>
