<?php
// 正确修复 content 字段问题
header('Content-Type: text/html; charset=UTF-8');
ini_set('default_charset', 'UTF-8');

session_start();

// 检查登录状态
if (!isset($_SESSION['cs_logged_in']) || !$_SESSION['cs_logged_in']) {
    echo '<h1>请先登录客服系统</h1>';
    exit;
}

require_once '../db_config.php';

echo '<h1>🔧 正确修复 content 字段问题</h1>';

if ($_POST['fix_content'] ?? false) {
    echo '<h2>正在修复字段...</h2>';
    
    try {
        $pdo = getDbConnection();
        
        $fixes = [
            // TEXT字段不能有默认值，只能设置为NULL
            "ALTER TABLE customer_service_messages MODIFY COLUMN content TEXT NULL COMMENT '消息内容'",
            "ALTER TABLE realtime_notifications MODIFY COLUMN message TEXT NULL COMMENT '通知消息内容'",
            "ALTER TABLE realtime_notifications MODIFY COLUMN data TEXT NULL COMMENT '通知数据'"
        ];
        
        $successCount = 0;
        $errorCount = 0;
        
        foreach ($fixes as $sql) {
            try {
                $pdo->exec($sql);
                $successCount++;
                echo '<p style="color: green;">✓ ' . substr($sql, 0, 80) . '...</p>';
            } catch (Exception $e) {
                $errorCount++;
                echo '<p style="color: red;">✗ ' . substr($sql, 0, 80) . '... 错误: ' . $e->getMessage() . '</p>';
            }
        }
        
        echo '<h3>修复完成</h3>';
        echo '<p>成功: ' . $successCount . ' 条</p>';
        echo '<p>失败: ' . $errorCount . ' 条</p>';
        
        if ($successCount > 0) {
            echo '<div style="background: #d4edda; padding: 15px; border-radius: 8px; margin: 10px 0;">';
            echo '<h3>🎉 字段修复成功！</h3>';
            echo '<p>现在需要修复代码中的插入逻辑，确保 content 字段总是有值。</p>';
            echo '<a href="test_fixed_insert.php" style="background: #28a745; color: white; padding: 8px 16px; text-decoration: none; border-radius: 5px; margin-right: 10px;">🧪 测试修复后的插入</a>';
            echo '</div>';
        }
        
    } catch (Exception $e) {
        echo '<p style="color: red;">数据库连接失败: ' . $e->getMessage() . '</p>';
    }
}

// 显示当前表结构
try {
    $pdo = getDbConnection();
    
    echo '<h2>当前表结构</h2>';
    
    $tables = ['customer_service_messages', 'realtime_notifications'];
    
    foreach ($tables as $table) {
        echo '<h3>' . $table . '</h3>';
        try {
            $stmt = $pdo->query("DESCRIBE $table");
            $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            echo '<table border="1" style="border-collapse: collapse; margin: 10px 0;">';
            echo '<tr><th>字段名</th><th>类型</th><th>是否为空</th><th>默认值</th><th>注释</th></tr>';
            
            foreach ($columns as $column) {
                $typeColor = 'black';
                $nullColor = 'black';
                
                // 标记TEXT字段
                if (strpos($column['Type'], 'text') !== false) {
                    $typeColor = $column['Null'] === 'YES' ? 'green' : 'red';
                }
                
                echo '<tr>';
                echo '<td><strong>' . htmlspecialchars($column['Field']) . '</strong></td>';
                echo '<td style="color: ' . $typeColor . ';">' . htmlspecialchars($column['Type']) . '</td>';
                echo '<td style="color: ' . $nullColor . ';">' . htmlspecialchars($column['Null']) . '</td>';
                echo '<td>' . htmlspecialchars($column['Default'] ?? 'NULL') . '</td>';
                echo '<td>' . htmlspecialchars($column['Comment'] ?? '') . '</td>';
                echo '</tr>';
            }
            echo '</table>';
            
        } catch (Exception $e) {
            echo '<p style="color: red;">获取 ' . $table . ' 表结构失败: ' . $e->getMessage() . '</p>';
        }
    }
    
} catch (Exception $e) {
    echo '<p style="color: red;">数据库连接失败: ' . $e->getMessage() . '</p>';
}
?>

<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>正确修复 content 字段</title>
    <style>
        body { 
            font-family: Arial, sans-serif; 
            margin: 20px; 
            background: #f5f5f5; 
        }
        .container { 
            max-width: 1000px; 
            margin: 0 auto; 
            background: white; 
            padding: 20px; 
            border-radius: 10px; 
            box-shadow: 0 2px 10px rgba(0,0,0,0.1); 
        }
        table { 
            width: 100%; 
            margin: 10px 0; 
        }
        th, td { 
            padding: 8px; 
            text-align: left; 
        }
        th { 
            background-color: #f2f2f2; 
        }
        .fix-btn { 
            background: #dc3545; 
            color: white; 
            border: none; 
            padding: 12px 24px; 
            border-radius: 5px; 
            cursor: pointer; 
            font-size: 16px; 
        }
        .warning { 
            background: #fff3cd; 
            padding: 15px; 
            border-radius: 5px; 
            margin: 10px 0; 
            border-left: 4px solid #ffc107; 
        }
        .error-box {
            background: #f8d7da;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
            border-left: 4px solid #dc3545;
            color: #721c24;
        }
        .info-box {
            background: #d1ecf1;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
            border-left: 4px solid #17a2b8;
            color: #0c5460;
        }
    </style>
</head>
<body>
    <div class="container">
        <?php if (!($_POST['fix_content'] ?? false)): ?>
        <div class="error-box">
            <h3>🚨 MySQL TEXT字段限制</h3>
            <p><strong>错误原因：</strong>MySQL的TEXT字段不能设置默认值</p>
            <p><strong>正确解决方案：</strong>将字段设置为允许NULL，然后在代码中确保总是插入有效值</p>
        </div>
        
        <div class="info-box">
            <h3>💡 修复策略</h3>
            <ol>
                <li><strong>数据库层面：</strong>将TEXT字段设置为允许NULL</li>
                <li><strong>代码层面：</strong>在插入前检查并确保content有值</li>
                <li><strong>API层面：</strong>添加内容验证和默认处理</li>
            </ol>
        </div>
        
        <div class="warning">
            <h3>⚠️ 将要执行的修复</h3>
            <ul>
                <li>customer_service_messages.content → TEXT NULL</li>
                <li>realtime_notifications.message → TEXT NULL</li>
                <li>realtime_notifications.data → TEXT NULL</li>
            </ul>
            <p><strong>这个操作是安全的，不会丢失数据。</strong></p>
        </div>
        
        <form method="POST">
            <button type="submit" name="fix_content" value="1" class="fix-btn">修复TEXT字段为允许NULL</button>
        </form>
        <?php endif; ?>
        
        <p style="margin-top: 20px;">
            <a href="sessions.php">返回会话列表</a> | 
            <a href="quick_message_test.php">测试发送消息</a> | 
            <a href="../../frontend/customer_service/complete_test.php">前台测试</a>
        </p>
    </div>
</body>
</html>
