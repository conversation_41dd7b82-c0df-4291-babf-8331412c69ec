<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>引导页面调试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 20px auto;
            padding: 20px;
            line-height: 1.6;
        }
        .test-section {
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }
        .test-btn {
            background: #40E0D0;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        .test-btn:hover {
            background: #20B2AA;
        }
        .result {
            margin-top: 10px;
            padding: 10px;
            border-radius: 5px;
            background: #f8f9fa;
        }
        .success { color: #28a745; }
        .error { color: #dc3545; }
        .warning { color: #ffc107; }
    </style>
</head>
<body>
    <h1>引导页面功能调试</h1>

    <div class="test-section">
        <h2>1. 文件检查</h2>
        <div id="file-check">
            <?php
            $files_to_check = [
                'css/style.css' => '样式文件',
                'js/script.js' => '主JavaScript文件',
                'css/city_selector.css' => '城市选择样式',
                'js/city_selector.js' => '城市选择JavaScript',
                'upload_avatar.php' => '头像上传处理',
                'complete_registration.php' => '注册完成处理',
                'city_selector.php' => '城市选择页面',
                '../includes/cloudinary_helper.php' => 'Cloudinary助手',
                'img/default-avatar.jpg' => '默认头像'
            ];

            foreach ($files_to_check as $file => $desc) {
                $exists = file_exists($file);
                $status = $exists ? 'success' : 'error';
                $icon = $exists ? '✅' : '❌';
                echo "<div class='$status'>$icon $desc ($file)</div>";
            }
            ?>
        </div>
    </div>

    <div class="test-section">
        <h2>2. JavaScript功能测试</h2>
        <button class="test-btn" onclick="testToast()">测试Toast提示</button>
        <button class="test-btn" onclick="testCitySelector()">测试城市选择</button>
        <button class="test-btn" onclick="testFormValidation()">测试表单验证</button>
        <div id="js-result" class="result"></div>
    </div>

    <div class="test-section">
        <h2>3. 头像上传测试</h2>
        <input type="file" id="test-avatar" accept="image/*">
        <button class="test-btn" onclick="testAvatarUpload()">测试头像上传</button>
        <div id="avatar-result" class="result"></div>
    </div>

    <div class="test-section">
        <h2>4. 高德地图API测试</h2>
        <button class="test-btn" onclick="testAmapAPI()">测试高德地图API</button>
        <div id="amap-result" class="result"></div>
    </div>

    <div class="test-section">
        <h2>5. 数据库连接测试</h2>
        <button class="test-btn" onclick="testDatabase()">测试数据库连接</button>
        <div id="db-result" class="result"></div>
    </div>

    <script src="https://webapi.amap.com/maps?v=2.0&key=e318a539d606974c5f13654e905cf555"></script>
    <script>
        function testToast() {
            const result = document.getElementById('js-result');
            try {
                // 创建一个简单的toast
                const toast = document.createElement('div');
                toast.style.cssText = `
                    position: fixed; top: 20px; left: 50%; transform: translateX(-50%);
                    background: #40E0D0; color: white; padding: 10px 20px;
                    border-radius: 5px; z-index: 9999;
                `;
                toast.textContent = 'Toast测试成功！';
                document.body.appendChild(toast);
                
                setTimeout(() => {
                    document.body.removeChild(toast);
                }, 2000);
                
                result.innerHTML = '<div class="success">✅ Toast功能正常</div>';
            } catch (error) {
                result.innerHTML = `<div class="error">❌ Toast测试失败: ${error.message}</div>`;
            }
        }

        function testCitySelector() {
            const result = document.getElementById('js-result');
            try {
                // 测试城市选择页面是否可以打开
                const testWindow = window.open('city_selector.php', '_blank', 'width=400,height=600');
                if (testWindow) {
                    result.innerHTML = '<div class="success">✅ 城市选择页面可以打开</div>';
                    setTimeout(() => {
                        testWindow.close();
                    }, 3000);
                } else {
                    result.innerHTML = '<div class="error">❌ 无法打开城市选择页面（可能被弹窗拦截）</div>';
                }
            } catch (error) {
                result.innerHTML = `<div class="error">❌ 城市选择测试失败: ${error.message}</div>`;
            }
        }

        function testFormValidation() {
            const result = document.getElementById('js-result');
            try {
                // 测试邮箱验证
                const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                const testEmail = '<EMAIL>';
                const isValid = emailRegex.test(testEmail);
                
                if (isValid) {
                    result.innerHTML = '<div class="success">✅ 表单验证功能正常</div>';
                } else {
                    result.innerHTML = '<div class="error">❌ 表单验证失败</div>';
                }
            } catch (error) {
                result.innerHTML = `<div class="error">❌ 表单验证测试失败: ${error.message}</div>`;
            }
        }

        async function testAvatarUpload() {
            const result = document.getElementById('avatar-result');
            const fileInput = document.getElementById('test-avatar');
            
            if (!fileInput.files[0]) {
                result.innerHTML = '<div class="warning">⚠️ 请先选择图片文件</div>';
                return;
            }
            
            try {
                const formData = new FormData();
                formData.append('avatar', fileInput.files[0]);
                
                result.innerHTML = '<div>测试上传中...</div>';
                
                const response = await fetch('upload_avatar.php', {
                    method: 'POST',
                    body: formData
                });
                
                const data = await response.json();
                
                if (data.success) {
                    result.innerHTML = `<div class="success">✅ 头像上传成功！<br>审核结果: ${data.moderation.approved ? '通过' : '未通过'}</div>`;
                } else {
                    result.innerHTML = `<div class="error">❌ 头像上传失败: ${data.error}</div>`;
                }
            } catch (error) {
                result.innerHTML = `<div class="error">❌ 头像上传测试失败: ${error.message}</div>`;
            }
        }

        function testAmapAPI() {
            const result = document.getElementById('amap-result');
            try {
                if (typeof AMap !== 'undefined') {
                    result.innerHTML = '<div class="success">✅ 高德地图API加载成功</div>';
                } else {
                    result.innerHTML = '<div class="error">❌ 高德地图API未加载</div>';
                }
            } catch (error) {
                result.innerHTML = `<div class="error">❌ 高德地图API测试失败: ${error.message}</div>`;
            }
        }

        async function testDatabase() {
            const result = document.getElementById('db-result');
            try {
                result.innerHTML = '<div>测试数据库连接中...</div>';
                
                const response = await fetch('test_db.php');
                const data = await response.json();
                
                if (data.success) {
                    result.innerHTML = '<div class="success">✅ 数据库连接正常</div>';
                } else {
                    result.innerHTML = `<div class="error">❌ 数据库连接失败: ${data.error}</div>`;
                }
            } catch (error) {
                result.innerHTML = `<div class="error">❌ 数据库测试失败: ${error.message}</div>`;
            }
        }
    </script>
</body>
</html>
