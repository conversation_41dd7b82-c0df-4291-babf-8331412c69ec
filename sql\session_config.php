<?php
/**
 * Session配置文件
 * 统一管理session超时时间设置
 */

/**
 * 初始化登录session配置
 * 设置登录状态3天超时，其他功能保持原有时间
 */
function initLoginSession() {
    // 只在还没有启动session时进行配置
    if (session_status() === PHP_SESSION_NONE) {
        // 设置session超时时间为3天（259200秒）
        ini_set('session.gc_maxlifetime', 259200);
        
        // 设置session cookie的生命周期为3天
        ini_set('session.cookie_lifetime', 259200);
        
        // 设置session cookie的路径和安全性
        ini_set('session.cookie_path', '/');
        ini_set('session.cookie_httponly', 1);
        
        // 启动session
        session_start();
        
        // 如果是新的session或者需要更新超时时间
        if (!isset($_SESSION['login_time'])) {
            $_SESSION['login_time'] = time();
        }
        
        // 检查session是否过期（3天 = 259200秒）
        if (isset($_SESSION['login_time']) && (time() - $_SESSION['login_time'] > 259200)) {
            // 清除过期的登录session
            unset($_SESSION['user_id']);
            unset($_SESSION['username']);
            unset($_SESSION['quwan_id']);
            unset($_SESSION['logged_in']);
            unset($_SESSION['login_time']);
        } else if (isset($_SESSION['user_id'])) {
            // 更新最后活动时间
            $_SESSION['login_time'] = time();
        }
    }
}

/**
 * 检查用户是否已登录
 * @return bool
 */
function isUserLoggedIn() {
    return isset($_SESSION['user_id']) && isset($_SESSION['login_time']);
}

/**
 * 设置用户登录状态
 * @param array $user 用户信息
 */
function setUserLoginSession($user) {
    $_SESSION['user_id'] = $user['id'];
    $_SESSION['username'] = $user['username'];
    $_SESSION['quwan_id'] = $user['quwan_id'];
    $_SESSION['logged_in'] = true;
    $_SESSION['login_time'] = time(); // 记录登录时间
}

/**
 * 清除用户登录状态
 */
function clearUserLoginSession() {
    unset($_SESSION['user_id']);
    unset($_SESSION['username']);
    unset($_SESSION['quwan_id']);
    unset($_SESSION['logged_in']);
    unset($_SESSION['login_time']);
}

/**
 * 初始化管理员session配置
 */
function initAdminSession() {
    // 只在还没有启动session时进行配置
    if (session_status() === PHP_SESSION_NONE) {
        // 设置session超时时间为3天
        ini_set('session.gc_maxlifetime', 259200);
        ini_set('session.cookie_lifetime', 259200);
        ini_set('session.cookie_path', '/');
        ini_set('session.cookie_httponly', 1);
        
        session_start();
        
        if (!isset($_SESSION['admin_login_time'])) {
            $_SESSION['admin_login_time'] = time();
        }
        
        // 检查管理员session是否过期
        if (isset($_SESSION['admin_login_time']) && (time() - $_SESSION['admin_login_time'] > 259200)) {
            unset($_SESSION['admin_id']);
            unset($_SESSION['admin_logged_in']);
            unset($_SESSION['admin_name']);
            unset($_SESSION['admin_role']);
            unset($_SESSION['admin_employee_id']);
            unset($_SESSION['admin_login_time']);
        } else if (isset($_SESSION['admin_id'])) {
            $_SESSION['admin_login_time'] = time();
        }
    }
}

/**
 * 检查管理员是否已登录
 * @return bool
 */
function isAdminLoggedIn() {
    return isset($_SESSION['admin_id']) && isset($_SESSION['admin_login_time']);
}

/**
 * 设置管理员登录状态
 * @param array $admin 管理员信息
 */
function setAdminLoginSession($admin) {
    $_SESSION['admin_id'] = $admin['id'];
    $_SESSION['admin_logged_in'] = true;
    $_SESSION['admin_name'] = $admin['name'] ?? $admin['username'];
    $_SESSION['admin_role'] = $admin['role_name'] ?? '管理员';
    $_SESSION['admin_employee_id'] = $admin['employee_id'];
    $_SESSION['admin_login_time'] = time();
}

/**
 * 清除管理员登录状态
 */
function clearAdminLoginSession() {
    unset($_SESSION['admin_id']);
    unset($_SESSION['admin_logged_in']);
    unset($_SESSION['admin_name']);
    unset($_SESSION['admin_role']);
    unset($_SESSION['admin_employee_id']);
    unset($_SESSION['admin_login_time']);
}
