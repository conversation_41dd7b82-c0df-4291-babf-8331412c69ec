// 消息页面JavaScript

document.addEventListener('DOMContentLoaded', function() {
    // 初始化页面
    initializePage();
});

// 初始化页面
function initializePage() {
    // 标签切换功能
    initTabSwitching();
    
    // 添加按钮功能
    initAddButton();
    
    // 底部导航栏点击效果
    initBottomNav();
}

// 标签切换功能
function initTabSwitching() {
    const tabItems = document.querySelectorAll('.tab-item');
    const tabContents = document.querySelectorAll('.tab-content');
    
    tabItems.forEach(item => {
        item.addEventListener('click', function() {
            const targetTab = this.dataset.tab;
            
            // 移除所有活动状态
            tabItems.forEach(tab => tab.classList.remove('active'));
            tabContents.forEach(content => content.classList.remove('active'));
            
            // 添加活动状态
            this.classList.add('active');
            document.getElementById(targetTab).classList.add('active');
        });
    });
}

// 添加按钮功能
function initAddButton() {
    const addButton = document.getElementById('addButton');
    const addMenu = document.getElementById('addMenu');
    
    addButton.addEventListener('click', function() {
        showAddMenu();
    });
}

// 显示添加菜单
function showAddMenu() {
    const addMenu = document.getElementById('addMenu');
    addMenu.classList.add('show');
    
    // 阻止背景滚动
    document.body.style.overflow = 'hidden';
}

// 关闭添加菜单
function closeAddMenu() {
    const addMenu = document.getElementById('addMenu');
    addMenu.classList.remove('show');
    
    // 恢复背景滚动
    document.body.style.overflow = '';
}

// 扫一扫功能
function scanQR() {
    closeAddMenu();
    showToast('扫一扫功能开发中...');
}

// 创建群聊功能
function createGroup() {
    closeAddMenu();
    showToast('创建群聊功能开发中...');
}

// 添加好友功能
function addFriend() {
    closeAddMenu();
    showToast('添加好友功能开发中...');
}

// 打开聊天
function openChat(type, id) {
    if (type === 'group') {
        window.location.href = `chat.php?type=group&id=${id}`;
    } else if (type === 'private') {
        window.location.href = `chat.php?type=private&id=${id}`;
    }
}

// 底部导航栏点击效果
function initBottomNav() {
    const navItems = document.querySelectorAll('.nav-item');
    navItems.forEach(item => {
        item.addEventListener('click', function() {
            // 添加点击效果
            this.style.transform = 'scale(0.95)';
            setTimeout(() => {
                this.style.transform = 'scale(1)';
            }, 100);
        });
    });
}

// Toast提示函数
function showToast(message) {
    const toast = document.getElementById('toast');
    toast.textContent = message;
    toast.style.display = 'block';

    // 添加动画
    toast.style.animation = 'fadeIn 0.3s forwards';

    // 3秒后隐藏
    setTimeout(() => {
        toast.style.animation = 'fadeOut 0.3s forwards';
        setTimeout(() => {
            toast.style.display = 'none';
        }, 300);
    }, 3000);
}

// 处理键盘事件
document.addEventListener('keydown', function(e) {
    // ESC键关闭弹窗
    if (e.key === 'Escape') {
        closeAddMenu();
    }
});

// 处理触摸事件（移动端）
let touchStartY = 0;
let touchEndY = 0;

document.addEventListener('touchstart', function(e) {
    touchStartY = e.changedTouches[0].screenY;
});

document.addEventListener('touchend', function(e) {
    touchEndY = e.changedTouches[0].screenY;
    handleSwipe();
});

function handleSwipe() {
    const swipeThreshold = 50;
    const diff = touchStartY - touchEndY;
    
    // 向上滑动显示添加菜单
    if (diff > swipeThreshold) {
        // 可以在这里添加向上滑动的逻辑
    }
    
    // 向下滑动关闭添加菜单
    if (diff < -swipeThreshold) {
        const addMenu = document.getElementById('addMenu');
        if (addMenu.classList.contains('show')) {
            closeAddMenu();
        }
    }
}

// 页面可见性变化处理
document.addEventListener('visibilitychange', function() {
    if (document.visibilityState === 'visible') {
        // 页面变为可见时，可以刷新消息列表
        // refreshMessageList();
    }
});

// 刷新消息列表（预留接口）
function refreshMessageList() {
    // 这里可以添加AJAX请求来刷新消息列表
    console.log('刷新消息列表...');
}

// 长按事件处理（用于消息操作）
function addLongPressEvent(element, callback) {
    let timer;
    let isLongPress = false;
    
    element.addEventListener('touchstart', function(e) {
        timer = setTimeout(() => {
            isLongPress = true;
            callback(e);
        }, 500);
    });
    
    element.addEventListener('touchend', function(e) {
        clearTimeout(timer);
        if (!isLongPress) {
            // 短按事件
        }
        isLongPress = false;
    });
    
    element.addEventListener('touchmove', function(e) {
        clearTimeout(timer);
        isLongPress = false;
    });
}

// 格式化时间显示
function formatTime(timestamp) {
    const now = new Date();
    const messageTime = new Date(timestamp);
    const diff = now - messageTime;
    
    // 小于1分钟显示"刚刚"
    if (diff < 60000) {
        return '刚刚';
    }
    
    // 小于1小时显示分钟
    if (diff < 3600000) {
        return Math.floor(diff / 60000) + '分钟前';
    }
    
    // 小于24小时显示小时
    if (diff < 86400000) {
        return Math.floor(diff / 3600000) + '小时前';
    }
    
    // 超过24小时显示日期
    if (messageTime.toDateString() === now.toDateString()) {
        return messageTime.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' });
    } else {
        return messageTime.toLocaleDateString('zh-CN', { month: 'short', day: 'numeric' });
    }
}

// 防抖函数
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// 节流函数
function throttle(func, limit) {
    let inThrottle;
    return function() {
        const args = arguments;
        const context = this;
        if (!inThrottle) {
            func.apply(context, args);
            inThrottle = true;
            setTimeout(() => inThrottle = false, limit);
        }
    };
}
