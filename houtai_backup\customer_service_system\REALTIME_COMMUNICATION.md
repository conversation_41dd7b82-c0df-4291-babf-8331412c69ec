# 客服系统双向实时通信功能说明

## 🎯 功能概述

客服系统现已实现前台用户与后台客服的双向实时通信，基于现有的实时通知系统架构，确保消息的即时传递和良好的用户体验。

## 🔄 通信架构

### 技术方案
- **主要方案**: 基于现有的Server-Sent Events (SSE) + 轮询混合架构
- **降级方案**: 纯轮询机制，确保在各种环境下的兼容性
- **数据存储**: 复用现有的 `realtime_notifications` 表和 `customer_service_messages` 表

### 通信流程
1. **前台 → 后台**: 用户发送消息 → 存储到数据库 → 后台轮询获取
2. **后台 → 前台**: 客服发送消息 → 存储到数据库 → 创建实时通知 → 前台接收

## 📱 前台功能

### 消息接收机制
- **实时通知集成**: 复用现有的 `WebSocketNotifications` 系统
- **消息类型过滤**: 专门监听 `customer_service_message` 类型通知
- **降级轮询**: 当实时通知不可用时，自动切换到轮询模式

### 用户体验优化
- **即时显示**: 客服回复消息立即显示在聊天界面
- **声音提醒**: 收到客服消息时播放提示音
- **发送者标识**: 显示客服姓名和头像
- **时间戳**: 精确显示消息发送时间

### 代码实现
```javascript
// 初始化实时通知
function initializeRealTimeNotifications() {
    const userId = <?php echo $_SESSION['user_id']; ?>;
    
    if (typeof WebSocketNotifications !== 'undefined') {
        const notifications = new WebSocketNotifications(userId);
        notifications.connect();
        
        notifications.onMessage = function(data) {
            if (data.type === 'customer_service_message' && data.session_id === sessionId) {
                addMessage('customer_service', data.message, data.cs_name);
                playNotificationSound();
            }
        };
    } else {
        startPollingForMessages();
    }
}
```

## 🖥️ 后台功能

### 会话详情页面增强
- **表情支持**: 80个常用emoji表情，8×10网格布局
- **快捷回复**: 4个预设快捷回复按钮（问候语、询问、解决、结束语）
- **文件上传**: 支持附件上传（10MB限制）
- **实时输入**: 自动调整输入框高度，回车发送

### 消息发送功能
- **权限控制**: 只有分配的客服或超级管理员可以发送消息
- **自动分配**: 等待中的会话在客服发送消息时自动分配
- **状态更新**: 发送消息后自动更新会话状态
- **实时通知**: 消息发送后立即推送给前台用户

### 消息接收功能
- **轮询机制**: 每3秒检查一次新的用户消息
- **声音提醒**: 收到用户消息时播放提示音
- **实时更新**: 自动更新会话状态和消息数量
- **权限验证**: 确保只能接收分配给自己的会话消息

## 🔧 API接口

### 前台API

#### 1. 发送消息 (`send_message.php`)
```php
POST /frontend/customer_service/api/send_message.php
{
    "sessionId": "session_xxx",
    "message": "用户消息内容"
}
```

#### 2. 获取新消息 (`get_new_messages.php`)
```php
GET /frontend/customer_service/api/get_new_messages.php?session_id=xxx&last_check=timestamp
```

#### 3. 提交评价 (`submit_rating.php`)
```php
POST /frontend/customer_service/api/submit_rating.php
{
    "sessionId": "session_xxx",
    "rating": 5,
    "comment": "评价内容"
}
```

### 后台API

#### 1. 客服发送消息 (`send_cs_message.php`)
```php
POST /houtai_backup/customer_service_system/api/send_cs_message.php
{
    "sessionId": "session_xxx",
    "message": "客服回复内容"
}
```

#### 2. 获取新用户消息 (`get_new_user_messages.php`)
```php
GET /houtai_backup/customer_service_system/api/get_new_user_messages.php?session_id=xxx&last_check=timestamp
```

#### 3. 获取会话状态 (`get_session_status.php`)
```php
GET /houtai_backup/customer_service_system/api/get_session_status.php?session_id=xxx
```

## 💾 数据库设计

### 消息存储
```sql
-- 客服消息示例
INSERT INTO customer_service_messages (
    session_id, 
    sender_type, 
    sender_id, 
    sender_name, 
    message_type, 
    content, 
    created_at
) VALUES (
    'session_xxx', 
    'customer_service', 
    1, 
    '张客服', 
    'text', 
    '您好，我是客服张三，很高兴为您服务！', 
    NOW()
);
```

### 实时通知
```sql
-- 客服消息通知示例
INSERT INTO realtime_notifications (
    user_id, 
    type, 
    title, 
    message, 
    data, 
    status, 
    created_at
) VALUES (
    123, 
    'customer_service_message', 
    '客服回复', 
    '您有新的客服回复消息', 
    '{"type":"customer_service_message","session_id":"session_xxx","cs_name":"张客服","message":"客服回复内容"}', 
    'unread', 
    NOW()
);
```

## 🔄 消息流转

### 用户发送消息流程
1. 用户在前台输入消息并点击发送
2. 前台调用 `send_message.php` API
3. 消息存储到 `customer_service_messages` 表
4. 如果会话已分配客服，记录通知信息
5. 机器人生成自动回复（如果启用）
6. 后台客服页面通过轮询获取新消息

### 客服回复消息流程
1. 客服在后台输入回复并点击发送
2. 后台调用 `send_cs_message.php` API
3. 消息存储到 `customer_service_messages` 表
4. 创建实时通知记录到 `realtime_notifications` 表
5. 前台通过实时通知系统接收消息
6. 前台立即显示客服回复消息

## 🎨 用户界面

### 前台聊天界面
- **消息气泡**: 用户消息蓝色右对齐，客服消息白色左对齐
- **头像图标**: 用户(👤)、客服(🎧)、机器人(🤖)、系统(⚙️)
- **发送者名称**: 显示客服真实姓名
- **时间戳**: HH:MM 格式显示发送时间

### 后台会话界面
- **三栏布局**: 左侧会话信息 + 中间聊天区域 + 右侧工具栏
- **输入增强**: 表情面板、快捷回复、文件上传
- **状态指示**: 实时显示会话状态和消息数量
- **权限控制**: 根据客服权限显示不同操作按钮

## 🔊 提醒机制

### 声音提醒
- **前台**: 收到客服回复时播放提示音
- **后台**: 收到用户消息时播放提示音
- **音量控制**: 默认30%音量，避免打扰
- **错误处理**: 播放失败时静默处理

### 视觉提醒
- **消息闪烁**: 新消息出现时的动画效果
- **状态更新**: 实时更新会话状态标签
- **计数器**: 显示未读消息数量

## 🛡️ 安全机制

### 权限验证
- **用户验证**: 前台检查用户登录状态
- **客服验证**: 后台检查客服登录状态和权限
- **会话权限**: 确保只能访问分配给自己的会话
- **跨域保护**: API设置适当的CORS头

### 数据安全
- **输入验证**: 前后端双重验证消息内容
- **SQL防注入**: 使用预处理语句
- **XSS防护**: 输出内容进行HTML转义
- **文件上传**: 严格的文件类型和大小限制

## 📊 性能优化

### 轮询优化
- **智能频率**: 3秒轮询间隔，平衡实时性和性能
- **条件查询**: 只查询指定时间后的新消息
- **结果缓存**: 避免重复查询相同数据
- **错误处理**: 网络错误时的重试机制

### 数据库优化
- **索引优化**: 在 `session_id` 和 `created_at` 字段建立索引
- **查询限制**: 限制单次查询的消息数量
- **连接池**: 复用数据库连接
- **清理机制**: 定期清理过期的通知记录

## 🚀 扩展功能

### 即将支持
- **消息撤回**: 发送后短时间内可撤回消息
- **消息转发**: 将消息转发给其他客服
- **快捷短语**: 客服可自定义常用回复短语
- **消息搜索**: 在会话中搜索历史消息

### 高级特性
- **语音消息**: 支持语音消息的录制和播放
- **视频通话**: 集成视频通话功能
- **屏幕共享**: 技术支持时的屏幕共享
- **文件预览**: 在线预览上传的文件

## 📈 监控统计

### 实时监控
- **消息延迟**: 监控消息从发送到接收的延迟
- **连接状态**: 监控实时通知连接状态
- **错误率**: 统计API调用的成功率
- **活跃会话**: 实时显示活跃的客服会话数

### 数据分析
- **响应时间**: 客服平均响应时间统计
- **会话时长**: 平均会话持续时间
- **满意度**: 用户评价统计分析
- **工作负载**: 客服工作量分析

---

**双向实时通信** - 让客服服务更高效、更人性化！
