<?php
/**
 * 智能客服机器人管理主页
 * 参考大厂智能客服设计
 */

session_start();

// 数据库连接函数
function getDbConnection() {
    try {
        $pdo = new PDO(
            "mysql:host=localhost;dbname=quwanplanet;charset=utf8mb4",
            "quwanplanet",
            "nJmJm23FB4Xn6Fc3",
            [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC
            ]
        );
        return $pdo;
    } catch (PDOException $e) {
        throw new Exception("数据库连接失败: " . $e->getMessage());
    }
}

// 检查管理员登录状态 (暂时跳过用于测试)
// if (!isset($_SESSION['admin_id'])) {
//     header('Location: ../login.php');
//     exit;
// }

$admin_id = $_SESSION['admin_id'] ?? 1;
$admin_username = $_SESSION['admin_username'] ?? '管理员';

// 初始化变量
$stats = [];
$recent_conversations = [];
$bot_config = null;
$reply_rules = [];
$db_error = false;

try {
    $pdo = getDbConnection();
} catch (Exception $e) {
    $db_error = true;
    $error_message = $e->getMessage();
}

// 如果数据库连接成功，获取数据
if (!$db_error) {
    try {
        // 获取统计数据
        // 今日对话数
        $stmt = $pdo->query("SELECT COUNT(*) FROM customer_service_conversations WHERE DATE(created_at) = CURDATE()");
        $stats['today_conversations'] = $stmt->fetchColumn();

        // 总对话数
        $stmt = $pdo->query("SELECT COUNT(*) FROM customer_service_conversations");
        $stats['total_conversations'] = $stmt->fetchColumn();

        // 活跃会话数（最近1小时）
        $stmt = $pdo->query("SELECT COUNT(DISTINCT session_id) FROM customer_service_conversations WHERE created_at >= DATE_SUB(NOW(), INTERVAL 1 HOUR)");
        $stats['active_sessions'] = $stmt->fetchColumn();

        // 人工客服转接数
        $stmt = $pdo->query("SELECT COUNT(*) FROM customer_service_conversations WHERE is_human_service = 1");
        $stats['human_service_count'] = $stmt->fetchColumn();

        // 获取最近的对话记录
        $stmt = $pdo->prepare("
            SELECT c.*, u.username, u.phone
            FROM customer_service_conversations c
            LEFT JOIN users u ON c.user_id = u.id
            ORDER BY c.created_at DESC
            LIMIT 20
        ");
        $stmt->execute();
        $recent_conversations = $stmt->fetchAll();

        // 获取机器人配置
        $stmt = $pdo->query("SELECT * FROM customer_service_bot WHERE is_enabled = 1 ORDER BY id DESC LIMIT 1");
        $bot_config = $stmt->fetch();

        // 获取回复规则
        $stmt = $pdo->query("SELECT * FROM customer_service_replies ORDER BY priority DESC, id ASC");
        $reply_rules = $stmt->fetchAll();

    } catch (Exception $e) {
        $db_error = true;
        $error_message = "数据查询失败: " . $e->getMessage();
    }
} else {
    // 设置默认值
    $stats = [
        'today_conversations' => 0,
        'total_conversations' => 0,
        'active_sessions' => 0,
        'human_service_count' => 0
    ];
}
?>

<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>客服管理 - 趣玩星球后台</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="../assets/css/admin.css">
    <style>
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: var(--spacing-lg);
            margin-bottom: var(--spacing-2xl);
        }

        .stat-card {
            background: white;
            padding: var(--spacing-xl);
            border-radius: var(--radius-xl);
            box-shadow: var(--shadow-lg);
            text-align: center;
            border-left: 4px solid var(--primary-color);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .stat-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
        }

        .stat-card:hover {
            transform: translateY(-4px);
            box-shadow: var(--shadow-xl);
        }

        .stat-number {
            font-size: 2.5rem;
            font-weight: 700;
            color: var(--primary-color);
            margin-bottom: var(--spacing-sm);
            background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .stat-label {
            color: var(--gray-600);
            font-size: 0.875rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.05em;
        }

        .conversation-item {
            background: white;
            border-radius: var(--radius-xl);
            padding: var(--spacing-lg);
            margin-bottom: var(--spacing-md);
            border-left: 4px solid var(--gray-200);
            box-shadow: var(--shadow-md);
            transition: all 0.3s ease;
        }

        .conversation-item:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-lg);
        }

        .conversation-item.human-service {
            border-left-color: var(--warning-color);
            background: linear-gradient(135deg, rgba(245, 158, 11, 0.02), rgba(255, 255, 255, 1));
        }

        .conversation-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: var(--spacing-md);
            padding-bottom: var(--spacing-sm);
            border-bottom: 1px solid var(--gray-100);
        }

        .user-info {
            font-weight: 600;
            color: var(--gray-800);
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);
        }

        .conversation-time {
            font-size: 0.75rem;
            color: var(--gray-500);
            font-weight: 500;
        }

        .message-content {
            background: var(--gray-50);
            padding: var(--spacing-md);
            border-radius: var(--radius-lg);
            margin-bottom: var(--spacing-sm);
            border-left: 3px solid var(--gray-300);
            transition: all 0.3s ease;
        }

        .message-content:hover {
            background: var(--gray-100);
        }

        .user-message {
            border-left-color: var(--primary-color);
            background: linear-gradient(135deg, rgba(111, 123, 245, 0.05), var(--gray-50));
        }

        .bot-reply {
            border-left-color: var(--success-color);
            background: linear-gradient(135deg, rgba(16, 185, 129, 0.05), var(--gray-50));
        }

        .config-section {
            background: white;
            border-radius: var(--radius-xl);
            padding: var(--spacing-2xl);
            margin-bottom: var(--spacing-xl);
            box-shadow: var(--shadow-lg);
            border: 1px solid var(--gray-100);
            transition: all 0.3s ease;
        }

        .config-section:hover {
            box-shadow: var(--shadow-xl);
        }

        .config-section h3 {
            color: var(--gray-800);
            font-size: 1.25rem;
            font-weight: 700;
            margin-bottom: var(--spacing-lg);
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);
            padding-bottom: var(--spacing-md);
            border-bottom: 2px solid var(--gray-100);
        }

        .config-section h3 i {
            color: var(--primary-color);
            font-size: 1.125rem;
        }

        .rule-item {
            background: var(--gray-50);
            border: 1px solid var(--gray-200);
            border-radius: var(--radius-xl);
            padding: var(--spacing-lg);
            margin-bottom: var(--spacing-md);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .rule-item::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 4px;
            height: 100%;
            background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
        }

        .rule-item:hover {
            background: white;
            box-shadow: var(--shadow-md);
            transform: translateY(-2px);
        }

        .rule-keywords {
            display: flex;
            flex-wrap: wrap;
            gap: var(--spacing-xs);
            margin-bottom: var(--spacing-sm);
        }

        .keyword-tag {
            background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
            color: white;
            padding: 4px 12px;
            border-radius: var(--radius-xl);
            font-size: 0.75rem;
            font-weight: 600;
            box-shadow: var(--shadow-sm);
            transition: all 0.3s ease;
        }

        .keyword-tag:hover {
            transform: translateY(-1px);
            box-shadow: var(--shadow-md);
        }

        .priority-badge {
            background: linear-gradient(135deg, var(--warning-color), #FBBF24);
            color: white;
            padding: 4px 12px;
            border-radius: var(--radius-xl);
            font-size: 0.6875rem;
            font-weight: 700;
            text-transform: uppercase;
            letter-spacing: 0.05em;
            box-shadow: var(--shadow-sm);
        }
    </style>
</head>
<body>
    <div class="admin-container">
        <!-- 侧边栏 -->
        <?php include '../includes/sidebar.php'; ?>

        <!-- 主内容区域 -->
        <div class="main-content">
            <div class="content-wrapper">
                <div class="content-header">
                    <h1><i class="fas fa-headset"></i> 客服管理</h1>
                    <div class="header-actions">
                        <button class="btn btn-primary" onclick="openBotConfig()">
                            <i class="fas fa-robot"></i>
                            机器人配置
                        </button>
                        <button class="btn btn-success" onclick="openReplyRules()">
                            <i class="fas fa-comments"></i>
                            回复规则
                        </button>
                    </div>
                </div>

                <?php if ($db_error): ?>
                <!-- 数据库错误提示 -->
                <div class="config-section" style="border-left: 4px solid var(--error-color); background: linear-gradient(135deg, rgba(239, 68, 68, 0.05), white);">
                    <h3 style="color: var(--error-color);"><i class="fas fa-exclamation-triangle"></i> 数据库连接错误</h3>
                    <div style="background: rgba(239, 68, 68, 0.1); padding: var(--spacing-lg); border-radius: var(--radius-lg); margin-bottom: var(--spacing-lg);">
                        <p style="color: var(--error-color); font-weight: 600; margin-bottom: var(--spacing-sm);">
                            <i class="fas fa-times-circle"></i> 无法连接到数据库
                        </p>
                        <p style="color: var(--gray-700); font-size: 0.875rem; line-height: 1.5;">
                            错误信息：<?php echo htmlspecialchars($error_message); ?>
                        </p>
                    </div>
                    <div style="background: var(--gray-50); padding: var(--spacing-lg); border-radius: var(--radius-lg);">
                        <h4 style="color: var(--gray-800); margin-bottom: var(--spacing-md);">
                            <i class="fas fa-tools"></i> 解决方案
                        </h4>
                        <ul style="color: var(--gray-700); line-height: 1.6; padding-left: var(--spacing-lg);">
                            <li>检查数据库服务是否正常运行</li>
                            <li>确认数据库用户名和密码是否正确</li>
                            <li>验证数据库名称是否存在</li>
                            <li>检查数据库用户是否有足够的权限</li>
                            <li>确认相关数据表是否已创建</li>
                        </ul>
                        <div style="margin-top: var(--spacing-lg);">
                            <button class="btn btn-primary" onclick="location.reload()">
                                <i class="fas fa-redo"></i>
                                重新连接
                            </button>
                        </div>
                    </div>
                </div>
                <?php endif; ?>

            <!-- 统计卡片 -->
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-number"><?php echo number_format($stats['today_conversations']); ?></div>
                    <div class="stat-label">今日对话数</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number"><?php echo number_format($stats['total_conversations']); ?></div>
                    <div class="stat-label">总对话数</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number"><?php echo number_format($stats['active_sessions']); ?></div>
                    <div class="stat-label">活跃会话</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number"><?php echo number_format($stats['human_service_count']); ?></div>
                    <div class="stat-label">人工客服</div>
                </div>
            </div>

            <!-- 机器人配置 -->
            <div class="config-section">
                <h3><i class="fas fa-robot"></i> 当前机器人配置</h3>
                <?php if ($bot_config): ?>
                    <div class="info-grid">
                        <div class="info-item">
                            <span class="label">机器人名称</span>
                            <span class="value"><?php echo htmlspecialchars($bot_config['bot_name']); ?></span>
                        </div>
                        <div class="info-item">
                            <span class="label">状态</span>
                            <span class="value">
                                <?php if ($bot_config['is_enabled']): ?>
                                    <span class="status-badge success">启用</span>
                                <?php else: ?>
                                    <span class="status-badge danger">禁用</span>
                                <?php endif; ?>
                            </span>
                        </div>
                        <div class="info-item full-width">
                            <span class="label">欢迎消息</span>
                            <div class="value"><?php echo nl2br(htmlspecialchars($bot_config['welcome_message'])); ?></div>
                        </div>
                    </div>
                <?php else: ?>
                    <p class="text-muted">暂无机器人配置</p>
                <?php endif; ?>
            </div>

            <!-- 回复规则 -->
            <div class="config-section">
                <h3><i class="fas fa-comments"></i> 回复规则 (<?php echo count($reply_rules); ?>条)</h3>
                <?php if (!empty($reply_rules)): ?>
                    <?php foreach ($reply_rules as $rule): ?>
                        <div class="rule-item">
                            <div style="display: flex; justify-content: space-between; align-items: flex-start; margin-bottom: 8px;">
                                <div class="rule-keywords">
                                    <?php
                                    $keywords = json_decode($rule['keywords'], true);
                                    if (is_array($keywords)):
                                        foreach ($keywords as $keyword):
                                    ?>
                                        <span class="keyword-tag"><?php echo htmlspecialchars($keyword); ?></span>
                                    <?php
                                        endforeach;
                                    endif;
                                    ?>
                                </div>
                                <span class="priority-badge">优先级: <?php echo $rule['priority']; ?></span>
                            </div>
                            <div style="color: var(--gray-700); line-height: 1.5;">
                                <?php echo nl2br(htmlspecialchars($rule['reply_content'])); ?>
                            </div>
                        </div>
                    <?php endforeach; ?>
                <?php else: ?>
                    <p class="text-muted">暂无回复规则</p>
                <?php endif; ?>
            </div>

            <!-- 最近对话 -->
            <div class="config-section">
                <h3><i class="fas fa-history"></i> 最近对话</h3>
                <?php if (!empty($recent_conversations)): ?>
                    <?php foreach ($recent_conversations as $conv): ?>
                        <div class="conversation-item <?php echo $conv['is_human_service'] ? 'human-service' : ''; ?>">
                            <div class="conversation-header">
                                <div class="user-info">
                                    <?php if ($conv['username']): ?>
                                        <?php echo htmlspecialchars($conv['username']); ?>
                                        <?php if ($conv['phone']): ?>
                                            (<?php echo htmlspecialchars($conv['phone']); ?>)
                                        <?php endif; ?>
                                    <?php else: ?>
                                        访客 (<?php echo substr($conv['session_id'], -8); ?>)
                                    <?php endif; ?>
                                    <?php if ($conv['is_human_service']): ?>
                                        <span class="status-badge warning">人工客服</span>
                                    <?php endif; ?>
                                </div>
                                <div class="conversation-time">
                                    <?php echo date('Y-m-d H:i:s', strtotime($conv['created_at'])); ?>
                                </div>
                            </div>

                            <div class="message-content user-message">
                                <strong>用户:</strong> <?php echo htmlspecialchars($conv['user_message']); ?>
                            </div>

                            <?php if ($conv['bot_reply']): ?>
                                <div class="message-content bot-reply">
                                    <strong>机器人:</strong> <?php echo nl2br(htmlspecialchars($conv['bot_reply'])); ?>
                                </div>
                            <?php endif; ?>

                            <?php if ($conv['admin_reply']): ?>
                                <div class="message-content" style="border-left-color: #f59e0b;">
                                    <strong>客服:</strong> <?php echo nl2br(htmlspecialchars($conv['admin_reply'])); ?>
                                </div>
                            <?php endif; ?>
                        </div>
                    <?php endforeach; ?>
                <?php else: ?>
                    <p class="text-muted">暂无对话记录</p>
                <?php endif; ?>
            </div>
            </div>
        </div>
    </div>

    <script>
        // Toast 通知函数
        function showToast(message, type = 'info') {
            const toast = document.createElement('div');
            toast.className = `toast ${type}`;
            toast.innerHTML = `
                <div style="display: flex; align-items: center; gap: 12px;">
                    <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'warning' ? 'exclamation-triangle' : type === 'error' ? 'times-circle' : 'info-circle'}"></i>
                    <span>${message}</span>
                    <button onclick="this.parentElement.parentElement.remove()" style="background: none; border: none; color: inherit; cursor: pointer; margin-left: auto;">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            `;

            document.body.appendChild(toast);

            // 自动移除
            setTimeout(() => {
                if (toast.parentElement) {
                    toast.remove();
                }
            }, 5000);
        }

        function openBotConfig() {
            showToast('机器人配置功能开发中，敬请期待！', 'info');
        }

        function openReplyRules() {
            showToast('回复规则管理功能开发中，敬请期待！', 'info');
        }

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 添加统计卡片的动画效果
            const statCards = document.querySelectorAll('.stat-card');
            statCards.forEach((card, index) => {
                card.style.animationDelay = `${index * 0.1}s`;
                card.style.animation = 'fadeInUp 0.6s ease forwards';
            });

            // 添加对话项的悬停效果
            const conversationItems = document.querySelectorAll('.conversation-item');
            conversationItems.forEach(item => {
                item.addEventListener('mouseenter', function() {
                    this.style.borderLeftWidth = '6px';
                });

                item.addEventListener('mouseleave', function() {
                    this.style.borderLeftWidth = '4px';
                });
            });
        });
    </script>
</body>
</html>
