<?php
session_start();
header('Content-Type: application/json');
error_reporting(E_ALL);
ini_set('display_errors', 1);

// 检查是否有文件上传
if (!isset($_FILES['avatar']) || $_FILES['avatar']['error'] !== UPLOAD_ERR_OK) {
    echo json_encode(['success' => false, 'error' => '请选择头像文件']);
    exit;
}

// 验证文件类型
$allowed_types = ['image/jpeg', 'image/png', 'image/jpg'];
$file_type = $_FILES['avatar']['type'];
if (!in_array($file_type, $allowed_types)) {
    echo json_encode(['success' => false, 'error' => '只支持JPG、JPEG、PNG格式的图片']);
    exit;
}

// 验证文件大小 (5MB)
if ($_FILES['avatar']['size'] > 5 * 1024 * 1024) {
    echo json_encode(['success' => false, 'error' => '图片大小不能超过5MB']);
    exit;
}

try {
    // 直接使用cURL上传到Cloudinary
    $cloud_name = 'dwcauq0wy';
    $api_key = '965165511998959';
    $api_secret = 'JYnkxTIAAC3GLuf3u6iiQpfqfMA';

    // 构建上传URL
    $url = "https://api.cloudinary.com/v1_1/{$cloud_name}/image/upload";

    // 准备签名参数
    $timestamp = time();
    $params = [
        'folder' => 'quwanplanet/avatars',
        'timestamp' => $timestamp
    ];

    // 按字母顺序排序参数
    ksort($params);

    // 构建签名字符串
    $signature_string = '';
    foreach ($params as $key => $value) {
        $signature_string .= $key . '=' . $value . '&';
    }
    $signature_string = rtrim($signature_string, '&') . $api_secret;

    // 计算签名
    $signature = hash('sha1', $signature_string);

    // 准备表单数据
    $post_data = [
        'file' => new CURLFile($_FILES['avatar']['tmp_name'], $file_type, $_FILES['avatar']['name']),
        'api_key' => $api_key,
        'timestamp' => $timestamp,
        'signature' => $signature,
        'folder' => 'quwanplanet/avatars'
    ];

    // 初始化cURL
    $curl = curl_init();

    // 设置cURL选项
    curl_setopt_array($curl, [
        CURLOPT_URL => $url,
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_POST => true,
        CURLOPT_POSTFIELDS => $post_data,
        CURLOPT_TIMEOUT => 30,
        CURLOPT_SSL_VERIFYPEER => false,
        CURLOPT_SSL_VERIFYHOST => false
    ]);

    // 执行请求
    $response = curl_exec($curl);
    $http_code = curl_getinfo($curl, CURLINFO_HTTP_CODE);

    // 检查cURL错误
    if (curl_errno($curl)) {
        $error = curl_error($curl);
        curl_close($curl);
        echo json_encode([
            'success' => false,
            'error' => 'cURL错误: ' . $error,
            'debug' => [
                'curl_error' => $error,
                'http_code' => $http_code
            ]
        ]);
        exit;
    }

    curl_close($curl);

    // 解析响应
    $result = json_decode($response, true);

    if ($http_code === 200 && isset($result['secure_url'])) {
        $avatar_url = $result['secure_url'];

        // 简化审核逻辑，直接通过
        $moderation_result = ['approved' => true, 'reason' => '审核通过'];

        echo json_encode([
            'success' => true,
            'avatar_url' => $avatar_url,
            'moderation' => $moderation_result,
            'debug' => [
                'http_code' => $http_code,
                'cloudinary_response' => $result
            ]
        ]);
    } else {
        $error_message = isset($result['error']['message']) ? $result['error']['message'] : '未知错误';
        echo json_encode([
            'success' => false,
            'error' => '头像上传失败: ' . $error_message,
            'debug' => [
                'http_code' => $http_code,
                'response' => $response,
                'parsed_result' => $result
            ]
        ]);
    }

} catch (Exception $e) {
    error_log('头像上传异常: ' . $e->getMessage());
    echo json_encode([
        'success' => false,
        'error' => '上传过程中发生错误: ' . $e->getMessage(),
        'debug' => [
            'exception' => $e->getMessage(),
            'file' => $e->getFile(),
            'line' => $e->getLine()
        ]
    ]);
}
