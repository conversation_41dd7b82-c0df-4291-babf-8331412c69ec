<?php
// 测试简化版API
header('Content-Type: text/html; charset=UTF-8');
ini_set('default_charset', 'UTF-8');

session_start();

// 检查登录状态
if (!isset($_SESSION['cs_logged_in']) || !$_SESSION['cs_logged_in']) {
    echo '<h1>请先登录客服系统</h1>';
    exit;
}

require_once '../db_config.php';

echo '<h1>测试简化版接受会话API</h1>';

try {
    $pdo = getDbConnection();
    
    // 获取等待中的会话
    $stmt = $pdo->query("SELECT session_id, user_name, priority, started_at FROM customer_service_sessions WHERE status = 'waiting' ORDER BY started_at DESC LIMIT 5");
    $waitingSessions = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (!empty($waitingSessions)) {
        echo '<h2>等待中的会话</h2>';
        echo '<table border="1" style="border-collapse: collapse; width: 100%;">';
        echo '<tr><th>会话ID</th><th>用户名</th><th>优先级</th><th>开始时间</th><th>操作</th></tr>';
        
        foreach ($waitingSessions as $session) {
            echo '<tr>';
            echo '<td>' . htmlspecialchars($session['session_id']) . '</td>';
            echo '<td>' . htmlspecialchars($session['user_name']) . '</td>';
            echo '<td>' . htmlspecialchars($session['priority']) . '</td>';
            echo '<td>' . htmlspecialchars($session['started_at']) . '</td>';
            echo '<td>';
            echo '<button onclick="testOriginalAPI(\'' . $session['session_id'] . '\')" style="margin: 2px; padding: 4px 8px; background: #007cba; color: white; border: none; border-radius: 3px; cursor: pointer;">测试原版API</button>';
            echo '<button onclick="testSimpleAPI(\'' . $session['session_id'] . '\')" style="margin: 2px; padding: 4px 8px; background: #28a745; color: white; border: none; border-radius: 3px; cursor: pointer;">测试简化API</button>';
            echo '</td>';
            echo '</tr>';
        }
        echo '</table>';
    } else {
        echo '<p>没有等待中的会话</p>';
        echo '<p><a href="create_test_data.php">创建测试数据</a></p>';
    }
    
} catch (Exception $e) {
    echo '<p style="color: red;">错误: ' . $e->getMessage() . '</p>';
}
?>

<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>测试简化版API</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        table { margin: 10px 0; }
        th, td { padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
        .result { margin: 20px 0; padding: 15px; border-radius: 5px; }
        .success { background: #d4edda; border: 1px solid #c3e6cb; color: #155724; }
        .error { background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 3px; overflow-x: auto; }
    </style>
</head>
<body>
    <div id="result"></div>
    
    <script>
        async function testOriginalAPI(sessionId) {
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = '<div class="result">🔄 测试原版API中...</div>';
            
            try {
                const response = await fetch('api/accept_session.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        sessionId: sessionId
                    })
                });
                
                const data = await response.json();
                
                if (data.success) {
                    resultDiv.innerHTML = `
                        <div class="result success">
                            <h3>✅ 原版API测试成功！</h3>
                            <pre>${JSON.stringify(data, null, 2)}</pre>
                            <p><a href="sessions.php">查看会话列表</a></p>
                        </div>
                    `;
                } else {
                    resultDiv.innerHTML = `
                        <div class="result error">
                            <h3>❌ 原版API测试失败</h3>
                            <pre>${JSON.stringify(data, null, 2)}</pre>
                        </div>
                    `;
                }
                
            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="result error">
                        <h3>❌ 原版API网络错误</h3>
                        <p>${error.message}</p>
                    </div>
                `;
            }
        }
        
        async function testSimpleAPI(sessionId) {
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = '<div class="result">🔄 测试简化API中...</div>';
            
            try {
                const response = await fetch('api/accept_session_simple.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        sessionId: sessionId
                    })
                });
                
                const data = await response.json();
                
                if (data.success) {
                    resultDiv.innerHTML = `
                        <div class="result success">
                            <h3>✅ 简化API测试成功！</h3>
                            <pre>${JSON.stringify(data, null, 2)}</pre>
                            <p><strong>说明：</strong>简化版API跳过了系统消息插入，避免了数据库字段问题。</p>
                            <p><a href="sessions.php">查看会话列表</a></p>
                        </div>
                    `;
                } else {
                    resultDiv.innerHTML = `
                        <div class="result error">
                            <h3>❌ 简化API测试失败</h3>
                            <pre>${JSON.stringify(data, null, 2)}</pre>
                        </div>
                    `;
                }
                
            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="result error">
                        <h3>❌ 简化API网络错误</h3>
                        <p>${error.message}</p>
                    </div>
                `;
            }
        }
    </script>
    
    <hr>
    <h2>说明</h2>
    <ul>
        <li><strong>原版API</strong>：完整功能，包括系统消息插入</li>
        <li><strong>简化API</strong>：跳过系统消息插入，避免数据库字段问题</li>
    </ul>
    
    <p>
        <a href="sessions.php">返回会话列表</a> | 
        <a href="final_fix.php">最终修复</a> | 
        <a href="complete_fix.php">完整修复</a>
    </p>
</body>
</html>
