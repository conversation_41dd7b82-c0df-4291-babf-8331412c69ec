<?php
// 引入session配置
require_once '../sql/session_config.php';
initAdminSession();

// 检查是否已登录
if (!isAdminLoggedIn()) {
    header('Location: login.php');
    exit;
}

// 数据库连接
require_once '../sql/db_config.php';
$pdo = getDbConnection();

// 获取管理员信息
$stmt = $pdo->prepare("SELECT * FROM admin_users WHERE id = ?");
$stmt->execute([$_SESSION['admin_id']]);
$admin = $stmt->fetch(PDO::FETCH_ASSOC);

if (!$admin) {
    session_destroy();
    header('Location: login.php');
    exit;
}

// 获取统计数据
$stats = [];

// 待审核实名认证数量
$stmt = $pdo->query("SELECT COUNT(*) as count FROM realname_verification WHERE verification_status = 'pending'");
$stats['pending_verifications'] = $stmt->fetch(PDO::FETCH_ASSOC)['count'] ?? 0;

// 今日新增用户
$stmt = $pdo->query("SELECT COUNT(*) as count FROM users WHERE DATE(created_at) = CURDATE()");
$stats['today_users'] = $stmt->fetch(PDO::FETCH_ASSOC)['count'] ?? 0;

// 总用户数
$stmt = $pdo->query("SELECT COUNT(*) as count FROM users");
$stats['total_users'] = $stmt->fetch(PDO::FETCH_ASSOC)['count'] ?? 0;

// 已认证用户数
$stmt = $pdo->query("SELECT COUNT(*) as count FROM realname_verification WHERE verification_status = 'approved'");
$stats['verified_users'] = $stmt->fetch(PDO::FETCH_ASSOC)['count'] ?? 0;
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>趣玩星球 - 后台管理系统</title>
    <link rel="stylesheet" href="css/admin.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <div class="admin-container">
        <!-- 侧边栏 -->
        <aside class="sidebar">
            <div class="sidebar-header">
                <h2><i class="fas fa-star"></i> 趣玩星球</h2>
                <p>后台管理系统</p>
            </div>

            <nav class="sidebar-nav">
                <ul>
                    <li class="active">
                        <a href="index.php">
                            <i class="fas fa-tachometer-alt"></i>
                            <span>仪表盘</span>
                        </a>
                    </li>
                    <li>
                        <a href="verification.php">
                            <i class="fas fa-user-check"></i>
                            <span>实名认证审核</span>
                            <?php if ($stats['pending_verifications'] > 0): ?>
                                <span class="badge"><?php echo $stats['pending_verifications']; ?></span>
                            <?php endif; ?>
                        </a>
                    </li>
                    <li>
                        <a href="users.php">
                            <i class="fas fa-users"></i>
                            <span>用户管理</span>
                        </a>
                    </li>
                    <li>
                        <a href="content.php">
                            <i class="fas fa-file-alt"></i>
                            <span>内容管理</span>
                        </a>
                    </li>
                    <li>
                        <a href="settings.php">
                            <i class="fas fa-cog"></i>
                            <span>系统设置</span>
                        </a>
                    </li>
                </ul>
            </nav>

            <div class="sidebar-footer">
                <div class="admin-info">
                    <div class="admin-avatar">
                        <i class="fas fa-user-shield"></i>
                    </div>
                    <div class="admin-details">
                        <p class="admin-name"><?php echo htmlspecialchars($admin['name']); ?></p>
                        <p class="admin-role"><?php echo htmlspecialchars($admin['role']); ?></p>
                    </div>
                </div>
                <a href="logout.php" class="logout-btn">
                    <i class="fas fa-sign-out-alt"></i>
                    <span>退出登录</span>
                </a>
            </div>
        </aside>

        <!-- 主内容区 -->
        <main class="main-content">
            <!-- 顶部栏 -->
            <header class="top-header">
                <div class="header-left">
                    <h1>仪表盘</h1>
                    <p>欢迎回来，<?php echo htmlspecialchars($admin['name']); ?>！</p>
                </div>
                <div class="header-right">
                    <span class="current-time" id="currentTime"></span>
                </div>
            </header>

            <!-- 统计卡片 -->
            <section class="stats-grid">
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-clock text-warning"></i>
                    </div>
                    <div class="stat-content">
                        <h3><?php echo $stats['pending_verifications']; ?></h3>
                        <p>待审核认证</p>
                    </div>
                    <div class="stat-action">
                        <a href="verification.php">立即处理</a>
                    </div>
                </div>

                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-user-plus text-success"></i>
                    </div>
                    <div class="stat-content">
                        <h3><?php echo $stats['today_users']; ?></h3>
                        <p>今日新增用户</p>
                    </div>
                </div>

                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-users text-primary"></i>
                    </div>
                    <div class="stat-content">
                        <h3><?php echo $stats['total_users']; ?></h3>
                        <p>总用户数</p>
                    </div>
                </div>

                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-user-check text-info"></i>
                    </div>
                    <div class="stat-content">
                        <h3><?php echo $stats['verified_users']; ?></h3>
                        <p>已认证用户</p>
                    </div>
                </div>
            </section>

            <!-- 快速操作 -->
            <section class="quick-actions">
                <h2>快速操作</h2>
                <div class="action-grid">
                    <a href="verification.php" class="action-card">
                        <i class="fas fa-user-check"></i>
                        <h3>审核实名认证</h3>
                        <p>处理用户实名认证申请</p>
                    </a>
                    <a href="users.php" class="action-card">
                        <i class="fas fa-users"></i>
                        <h3>用户管理</h3>
                        <p>查看和管理用户信息</p>
                    </a>
                    <a href="content.php" class="action-card">
                        <i class="fas fa-file-alt"></i>
                        <h3>内容审核</h3>
                        <p>审核用户发布的内容</p>
                    </a>
                    <a href="settings.php" class="action-card">
                        <i class="fas fa-cog"></i>
                        <h3>系统设置</h3>
                        <p>配置系统参数</p>
                    </a>
                </div>
            </section>
        </main>
    </div>

    <script src="js/admin.js"></script>
</body>
</html>
