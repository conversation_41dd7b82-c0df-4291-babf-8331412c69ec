<?php
// 客服发送消息给用户API
header('Content-Type: application/json; charset=UTF-8');
ini_set('default_charset', 'UTF-8');

session_start();

// 检查客服登录状态
if (!isset($_SESSION['cs_logged_in']) || !$_SESSION['cs_logged_in']) {
    http_response_code(401);
    echo json_encode(['error' => '客服未登录']);
    exit;
}

// 只接受POST请求
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['error' => '方法不允许']);
    exit;
}

// 获取POST数据
$input = json_decode(file_get_contents('php://input'), true);
$sessionId = $input['sessionId'] ?? '';
$message = trim($input['message'] ?? '');

if (empty($sessionId) || empty($message)) {
    http_response_code(400);
    echo json_encode(['error' => '参数不完整']);
    exit;
}

// 引用数据库配置文件
require_once '../../db_config.php';

try {
    $pdo = getDbConnection();

    // 检查会话是否存在
    $stmt = $pdo->prepare("SELECT * FROM customer_service_sessions WHERE session_id = ?");
    $stmt->execute([$sessionId]);
    $session = $stmt->fetch(PDO::FETCH_ASSOC);

    if (!$session) {
        http_response_code(404);
        echo json_encode(['error' => '会话不存在']);
        exit;
    }

    // 检查客服权限（只有分配的客服或超级管理员可以发送消息）
    if ($session['customer_service_id'] != $_SESSION['cs_user_id'] && $_SESSION['cs_role'] !== 'super_admin') {
        // 如果是等待中的会话，自动分配给当前客服
        if ($session['status'] === 'waiting') {
            $stmt = $pdo->prepare("
                UPDATE customer_service_sessions
                SET customer_service_id = ?, status = 'active', updated_at = NOW()
                WHERE session_id = ?
            ");
            $stmt->execute([$_SESSION['cs_user_id'], $sessionId]);
        } else {
            http_response_code(403);
            echo json_encode(['error' => '无权限操作此会话']);
            exit;
        }
    }

    // 确保消息内容不为空（防止TEXT字段插入失败）
    if (empty($message)) {
        $message = '空消息';
    }

    // 保存客服消息
    $stmt = $pdo->prepare("
        INSERT INTO customer_service_messages
        (session_id, sender_type, sender_id, sender_name, message_type, content, created_at)
        VALUES (?, 'customer_service', ?, ?, 'text', ?, NOW())
    ");
    $stmt->execute([
        $sessionId,
        $_SESSION['cs_user_id'],
        $_SESSION['cs_name'],
        $message
    ]);

    // 获取插入的消息ID
    $messageId = $pdo->lastInsertId();

    // 更新会话消息数量和最后更新时间
    $stmt = $pdo->prepare("
        UPDATE customer_service_sessions
        SET message_count = message_count + 1, updated_at = NOW()
        WHERE session_id = ?
    ");
    $stmt->execute([$sessionId]);

    // 如果会话有用户ID，发送实时通知给前台用户
    if ($session['user_id']) {
        // 创建实时通知数据
        $notificationData = [
            'type' => 'customer_service_message',
            'session_id' => $sessionId,
            'message_id' => $messageId,
            'content' => $message,
            'sender_name' => $_SESSION['cs_name'],
            'sender_type' => 'customer_service',
            'timestamp' => time(),
            'created_at' => date('Y-m-d H:i:s')
        ];

        // 检查 realtime_notifications 表是否有 message 字段
        $stmt = $pdo->query("SHOW COLUMNS FROM realtime_notifications LIKE 'message'");
        $hasMessageColumn = $stmt->rowCount() > 0;

        if ($hasMessageColumn) {
            // 有 message 字段的版本
            $stmt = $pdo->prepare("
                INSERT INTO realtime_notifications
                (user_id, type, title, message, data, status, created_at)
                VALUES (?, 'customer_service_message', '客服回复', ?, ?, 'unread', NOW())
            ");
            $stmt->execute([
                $session['user_id'],
                '您有新的客服回复消息',
                json_encode($notificationData, JSON_UNESCAPED_UNICODE)
            ]);
        } else {
            // 没有 message 字段的版本
            $stmt = $pdo->prepare("
                INSERT INTO realtime_notifications
                (user_id, type, title, data, status, created_at)
                VALUES (?, 'customer_service_message', '客服回复', ?, 'unread', NOW())
            ");
            $stmt->execute([
                $session['user_id'],
                json_encode($notificationData, JSON_UNESCAPED_UNICODE)
            ]);
        }
    }

    echo json_encode([
        'success' => true,
        'message' => '消息发送成功',
        'timestamp' => time()
    ]);

} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => '发送消息失败',
        'message' => $e->getMessage()
    ]);
}
?>
