<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>游戏玩伴页面</title>
    <!-- 注意：这里的CSS链接是相对于主页面（index.php）的路径 -->
    <!-- <link rel="stylesheet" href="features/game_companion/css/game_companion.css"> -->
    <!-- 实际项目中，Font Awesome 应该在主HTML或通过JS全局加载 -->
    <!-- <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css"> -->
</head>
<body>
    <div class="game-companion-container game-companion-new-layout">

        <!-- 快速筛选标签 -->
        <div class="quick-filters-top">
            <div class="filter-tabs">
                <button class="filter-tab active" data-filter="all">全部</button>
                <button class="filter-tab" data-filter="online">在线</button>
                <button class="filter-tab" data-filter="male">小哥哥</button>
                <button class="filter-tab" data-filter="female">小姐姐</button>
                <button class="filter-tab" data-filter="master">大神</button>
            </div>
            <button class="advanced-filter-btn"><i class="fas fa-sliders-h"></i></button>
        </div>

        <!-- 推荐大神横向滚动 -->
        <section class="recommended-masters">
            <div class="section-header">
                <h3>热门推荐</h3>
                <a href="#" class="view-more">更多</a>
            </div>
            <div class="masters-scroll-container">
                <div class="master-card-mini">
                    <div class="master-avatar">
                        <img src="https://via.placeholder.com/60x60?text=大神1" alt="大神头像">
                        <div class="online-dot"></div>
                        <div class="master-badge">王者</div>
                    </div>
                    <div class="master-info">
                        <h4>大神小明</h4>
                        <span class="master-game">英雄联盟</span>
                        <div class="master-rating">⭐ 4.8</div>
                    </div>
                    <div class="master-price">¥60/时</div>
                </div>
                <div class="master-card-mini">
                    <div class="master-avatar">
                        <img src="https://via.placeholder.com/60x60?text=大神2" alt="大神头像">
                        <div class="online-dot"></div>
                        <div class="master-badge female">大神</div>
                    </div>
                    <div class="master-info">
                        <h4>大神莉莉</h4>
                        <span class="master-game">无畏契约</span>
                        <div class="master-rating">⭐ 5.0</div>
                    </div>
                    <div class="master-price">¥80/时</div>
                </div>
                <div class="master-card-mini">
                    <div class="master-avatar">
                        <img src="https://via.placeholder.com/60x60?text=大神3" alt="大神头像">
                        <div class="online-dot offline"></div>
                        <div class="master-badge">高手</div>
                    </div>
                    <div class="master-info">
                        <h4>老司机张三</h4>
                        <span class="master-game">CS:GO</span>
                        <div class="master-rating">⭐ 4.2</div>
                    </div>
                    <div class="master-price">¥50/时</div>
                </div>
                <div class="master-card-mini">
                    <div class="master-avatar">
                        <img src="https://via.placeholder.com/60x60?text=大神4" alt="大神头像">
                        <div class="online-dot"></div>
                        <div class="master-badge female">女神</div>
                    </div>
                    <div class="master-info">
                        <h4>电竞女神</h4>
                        <span class="master-game">王者荣耀</span>
                        <div class="master-rating">⭐ 4.9</div>
                    </div>
                    <div class="master-price">¥65/时</div>
                </div>
            </div>
        </section>

        <!-- 玩伴列表 -->
        <section class="companions-overview">
            <div class="section-header-simple">
                <h3>全部玩伴</h3>
                <span class="online-count">2,345人在线</span>
            </div>

            <div class="companion-list-new">
                <div class="companion-card-new">
                    <div class="companion-left">
                        <div class="companion-avatar">
                            <img src="https://via.placeholder.com/50x50?text=A" alt="玩伴头像">
                            <div class="online-status"></div>
                        </div>
                        <div class="companion-info">
                            <h4>甜心教主 <span class="gender-tag female">♀</span></h4>
                            <div class="companion-tags">
                                <span class="game-tag">英雄联盟</span>
                                <span class="skill-tag">王者</span>
                            </div>
                            <div class="companion-features">
                                <span class="feature">🎤 开麦</span>
                                <span class="feature">⭐ 4.9</span>
                            </div>
                        </div>
                    </div>
                    <div class="companion-right">
                        <div class="companion-price">¥60<span>/时</span></div>
                        <button class="invite-btn-new">邀请</button>
                    </div>
                </div>

                <div class="companion-card-new">
                    <div class="companion-left">
                        <div class="companion-avatar">
                            <img src="https://via.placeholder.com/50x50?text=B" alt="玩伴头像">
                            <div class="online-status offline"></div>
                        </div>
                        <div class="companion-info">
                            <h4>峡谷钢琴家 <span class="gender-tag male">♂</span></h4>
                            <div class="companion-tags">
                                <span class="game-tag">无畏契约</span>
                                <span class="skill-tag">大神</span>
                            </div>
                            <div class="companion-features">
                                <span class="feature">🔇 不开麦</span>
                                <span class="feature">⭐ 4.7</span>
                            </div>
                        </div>
                    </div>
                    <div class="companion-right">
                        <div class="companion-price">¥45<span>/时</span></div>
                        <button class="invite-btn-new" disabled>离线</button>
                    </div>
                </div>

                <div class="companion-card-new">
                    <div class="companion-left">
                        <div class="companion-avatar">
                            <img src="https://via.placeholder.com/50x50?text=C" alt="玩伴头像">
                            <div class="online-status"></div>
                        </div>
                        <div class="companion-info">
                            <h4>Apex猎杀者 <span class="gender-tag male">♂</span></h4>
                            <div class="companion-tags">
                                <span class="game-tag">Apex英雄</span>
                                <span class="skill-tag">高手</span>
                            </div>
                            <div class="companion-features">
                                <span class="feature">🎤 开麦</span>
                                <span class="feature">⭐ 4.8</span>
                            </div>
                        </div>
                    </div>
                    <div class="companion-right">
                        <div class="companion-price">¥55<span>/时</span></div>
                        <button class="invite-btn-new">邀请</button>
                    </div>
                </div>

                <div class="companion-card-new">
                    <div class="companion-left">
                        <div class="companion-avatar">
                            <img src="https://via.placeholder.com/50x50?text=D" alt="玩伴头像">
                            <div class="online-status"></div>
                        </div>
                        <div class="companion-info">
                            <h4>CSGO老兵 <span class="gender-tag male">♂</span></h4>
                            <div class="companion-tags">
                                <span class="game-tag">CS:GO</span>
                                <span class="skill-tag">大神</span>
                            </div>
                            <div class="companion-features">
                                <span class="feature">🎤 开麦</span>
                                <span class="feature">⭐ 4.6</span>
                            </div>
                        </div>
                    </div>
                    <div class="companion-right">
                        <div class="companion-price">¥50<span>/时</span></div>
                        <button class="invite-btn-new">邀请</button>
                    </div>
                </div>

                <div class="companion-card-new">
                    <div class="companion-left">
                        <div class="companion-avatar">
                            <img src="https://via.placeholder.com/50x50?text=E" alt="玩伴头像">
                            <div class="online-status"></div>
                        </div>
                        <div class="companion-info">
                            <h4>电竞女神 <span class="gender-tag female">♀</span></h4>
                            <div class="companion-tags">
                                <span class="game-tag">王者荣耀</span>
                                <span class="skill-tag">王者</span>
                            </div>
                            <div class="companion-features">
                                <span class="feature">🎤 开麦</span>
                                <span class="feature">⭐ 4.9</span>
                            </div>
                        </div>
                    </div>
                    <div class="companion-right">
                        <div class="companion-price">¥65<span>/时</span></div>
                        <button class="invite-btn-new">邀请</button>
                    </div>
                </div>

                <div class="load-more-container">
                    <button class="load-more-btn-new">查看更多玩伴</button>
                </div>
            </div>
        </section>
    </div>

    <!-- 注意：这里的JS链接是相对于主页面（index.php）的路径 -->
    <!-- <script src="features/game_companion/js/game_companion.js"></script> -->

    <script>
        // 确保筛选标签只能水平滑动
        document.addEventListener('DOMContentLoaded', function() {
            const filterTabs = document.querySelector('.filter-tabs');
            if (filterTabs) {
                // 禁止垂直滚动
                filterTabs.addEventListener('touchmove', function(e) {
                    // 只允许水平滑动
                    const touch = e.touches[0];
                    const startX = touch.clientX;
                    const startY = touch.clientY;

                    // 如果是垂直滑动，阻止默认行为
                    if (Math.abs(startY - touch.clientY) > Math.abs(startX - touch.clientX)) {
                        e.preventDefault();
                    }
                }, { passive: false });
            }

            // 筛选标签点击事件
            const filterButtons = document.querySelectorAll('.filter-tab');
            filterButtons.forEach(button => {
                button.addEventListener('click', function() {
                    // 移除所有active类
                    filterButtons.forEach(btn => btn.classList.remove('active'));
                    // 添加active类到当前按钮
                    this.classList.add('active');
                });
            });
        });
    </script>
</body>
</html>