<?php
// 接受会话API
header('Content-Type: application/json; charset=UTF-8');
ini_set('default_charset', 'UTF-8');

session_start();

// 检查客服登录状态
if (!isset($_SESSION['cs_logged_in']) || !$_SESSION['cs_logged_in']) {
    http_response_code(401);
    echo json_encode(['error' => '客服未登录']);
    exit;
}

// 只接受POST请求
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['error' => '方法不允许']);
    exit;
}

// 获取POST数据
$input = json_decode(file_get_contents('php://input'), true);
$sessionId = $input['sessionId'] ?? '';

if (empty($sessionId)) {
    http_response_code(400);
    echo json_encode(['error' => '会话ID不能为空']);
    exit;
}

// 引用数据库配置文件
require_once '../../db_config.php';

try {
    $pdo = getDbConnection();

    // 检查会话是否存在且为等待状态
    $stmt = $pdo->prepare("SELECT * FROM customer_service_sessions WHERE session_id = ?");
    $stmt->execute([$sessionId]);
    $session = $stmt->fetch(PDO::FETCH_ASSOC);

    if (!$session) {
        http_response_code(404);
        echo json_encode(['error' => '会话不存在']);
        exit;
    }

    if ($session['status'] !== 'waiting') {
        http_response_code(400);
        echo json_encode(['error' => '会话状态不是等待中，无法接受']);
        exit;
    }

    if ($session['customer_service_id'] && $session['customer_service_id'] != $_SESSION['cs_user_id']) {
        http_response_code(400);
        echo json_encode(['error' => '会话已被其他客服接受']);
        exit;
    }

    // 开始事务
    $pdo->beginTransaction();

    try {
        // 更新会话状态为活跃，分配给当前客服
        $stmt = $pdo->prepare("
            UPDATE customer_service_sessions
            SET customer_service_id = ?, status = 'active', updated_at = NOW()
            WHERE session_id = ?
        ");
        $stmt->execute([$_SESSION['cs_user_id'], $sessionId]);

        // 跳过系统消息插入，避免数据库字段问题
        // 直接进行实时通知，不记录系统消息

        // 如果会话有用户ID，发送实时通知给前台用户
        if ($session['user_id']) {
            $notificationData = [
                'type' => 'session_accepted',
                'session_id' => $sessionId,
                'cs_name' => $_SESSION['cs_name'],
                'cs_employee_id' => $_SESSION['cs_employee_id'] ?? '',
                'timestamp' => time()
            ];

            // 检查 realtime_notifications 表是否有 message 字段
            $stmt = $pdo->query("SHOW COLUMNS FROM realtime_notifications LIKE 'message'");
            $hasMessageColumn = $stmt->rowCount() > 0;

            if ($hasMessageColumn) {
                // 有 message 字段的版本
                $stmt = $pdo->prepare("
                    INSERT INTO realtime_notifications
                    (user_id, type, title, message, data, status, created_at)
                    VALUES (?, 'session_accepted', '客服已接入', ?, ?, 'unread', NOW())
                ");
                $stmt->execute([
                    $session['user_id'],
                    '客服 ' . $_SESSION['cs_name'] . ' 已为您服务',
                    json_encode($notificationData, JSON_UNESCAPED_UNICODE)
                ]);
            } else {
                // 没有 message 字段的版本
                $stmt = $pdo->prepare("
                    INSERT INTO realtime_notifications
                    (user_id, type, title, data, status, created_at)
                    VALUES (?, 'session_accepted', '客服已接入', ?, 'unread', NOW())
                ");
                $stmt->execute([
                    $session['user_id'],
                    json_encode($notificationData, JSON_UNESCAPED_UNICODE)
                ]);
            }
        }

        // 提交事务
        $pdo->commit();

        echo json_encode([
            'success' => true,
            'message' => '会话接受成功',
            'session' => [
                'session_id' => $sessionId,
                'status' => 'active',
                'cs_name' => $_SESSION['cs_name'],
                'cs_employee_id' => $_SESSION['cs_employee_id'] ?? ''
            ]
        ]);

    } catch (Exception $e) {
        $pdo->rollBack();
        throw $e;
    }

} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => '接受会话失败',
        'message' => $e->getMessage()
    ]);
}
?>
