# 用户等级系统

## 📋 系统概述

本文件夹包含趣玩星球的完整用户等级体系，包括用户等级、贵族等级、会员等级三套独立又相互关联的等级系统。

## 📁 文件夹结构

```
/user_level_system/
├── README.md                 # 功能说明和使用文档
├── config/                   # 配置文件目录
├── classes/                  # 核心类文件目录
├── api/                      # API接口目录
├── frontend/                 # 前台组件目录
└── backend/                  # 后台管理目录
```

## 🎯 等级系统设计方案

### 1. 用户等级系统（经验值体系）

#### 等级设计（1-10级）：
- **Lv.1 萌新**：0经验值
- **Lv.2 新手**：500经验值
- **Lv.3 熟练**：1500经验值（需要1000经验升级）
- **Lv.4 进阶**：3000经验值（需要1500经验升级）
- **Lv.5 达人**：5500经验值（需要2500经验升级）
- **Lv.6 专家**：9000经验值（需要3500经验升级）
- **Lv.7 大神**：14000经验值（需要5000经验升级）
- **Lv.8 王者**：21000经验值（需要7000经验升级）
- **Lv.9 传说**：30000经验值（需要9000经验升级）
- **Lv.10 至尊**：42000经验值（需要12000经验升级）

#### 每日经验值获取：
- **签到**：1积分/天
- **发布内容**：2积分/次，最多2次/天
- **获得点赞**：0.5积分/个，最多10个有效/天
- **评论互动**：0.5积分/次，最多6次/天
- **参与活动**：5积分/次，最多1次/天
- **完成任务**：3积分/个，最多2个/天

**普通用户每日上限：约20积分**

#### 防刷机制：
- **点赞冷却**：同一用户24小时内只能给你1个有效点赞积分
- **评论质量**：少于5个字的评论不计入积分
- **内容审核**：发布内容需要审核通过才能获得积分
- **异常检测**：短时间内大量操作会被系统标记

### 2. 会员等级系统（付费订阅）

#### 会员类型：
- **普通用户**：免费，基础功能
- **白银会员**：19.9元/月，所有积分翻倍，每日上限40积分
- **黄金会员**：39.9元/月，所有积分翻倍+额外20%，每日上限48积分
- **钻石会员**：69.9元/月，所有积分翻倍+额外50%，每日上限60积分
- **黑金会员**：99.9元/月，所有积分翻倍+额外100%，每日上限80积分

#### 会员特权详细说明：

##### 🎨 **视觉特权**
- **红名显示**：用户名显示为红色，彰显会员身份
- **专属头像框**：不同等级专属设计的头像装饰框
- **专属铭牌**：个人资料页面专属会员铭牌展示
- **专属个人中心封面**：个性化背景封面，支持自定义
- **会员表情包**：专属表情包，聊天更有趣
- **会员气泡框**：聊天消息专属气泡样式

##### 💰 **收益特权**
- **白银会员**：创作收益提升10%
- **黄金会员**：创作收益提升20%
- **钻石会员**：创作收益提升35%
- **黑金会员**：创作收益提升50%

##### 🚗 **生活便利**
- **免车费次数**（每月）：
  - 白银会员：2次
  - 黄金会员：5次
  - 钻石会员：10次
  - 黑金会员：20次
- **免邮寄费次数**（每月）：
  - 白银会员：3次
  - 黄金会员：8次
  - 钻石会员：15次
  - 黑金会员：30次

##### 📈 **推广特权**
- **每月作品热推次数**：
  - 白银会员：2次
  - 黄金会员：5次
  - 钻石会员：10次
  - 黑金会员：20次

##### 👥 **社交特权**
- **群人数上限**（基础500）：
  - 白银会员：+200（总计700）
  - 黄金会员：+500（总计1000）
  - 钻石会员：+1000（总计1500）
  - 黑金会员：+2000（总计2500）
- **好友人数上限**（基础1000）：
  - 白银会员：+500（总计1500）
  - 黄金会员：+1000（总计2000）
  - 钻石会员：+2000（总计3000）
  - 黑金会员：+5000（总计6000）
- **创群数上限**（基础10）：
  - 白银会员：+5（总计15）
  - 黄金会员：+10（总计20）
  - 钻石会员：+20（总计30）
  - 黑金会员：+40（总计50）

##### 🏛️ **公会特权**
- **成立公会权限**：
  - 白银会员：可成立1个公会
  - 黄金会员：可成立2个公会
  - 钻石会员：可成立3个公会
  - 黑金会员：可成立5个公会

##### 🆔 **靓号特权**
- **会员专属ID靓号**：
  - 白银会员：6位连号、生日号、吉利号
  - 黄金会员：5位连号、AABB型、ABAB型
  - 钻石会员：4位连号、AAAA型、ABCD型
  - 黑金会员：3位连号、特殊意义号码
- **ID显示规则**：
  - 拥有会员靓号时，优先显示靓号ID
  - 用户可在设置中选择显示原ID或靓号ID
  - 会员过期后自动恢复显示原ID

##### 🎯 **功能特权**
- **无限私信**：不受每日私信次数限制
- **高级筛选**：更多筛选条件和排序方式
- **批量操作**：批量管理好友、消息、作品
- **去除广告**：浏览无广告干扰
- **优先展示**：内容获得更多曝光机会
- **详细统计**：查看详细的数据分析报告
- **数据导出**：导出个人数据和统计信息
- **优先客服**：享受优先客服支持
- **专属群组**：加入会员专属交流群
- **内测资格**：优先体验新功能

##### ⚠️ **特权失效规则**
- **会员过期**：所有特权立即失效
- **ID特殊处理**：靓号ID自动隐藏，恢复显示原ID
- **数据保留**：会员期间的数据和记录保留
- **重新订阅**：重新开通会员后特权立即恢复

#### 💾 **会员特权数据库设计建议**

##### 会员特权使用记录表：
```sql
CREATE TABLE member_privilege_usage (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    member_level ENUM('silver','gold','diamond','black_gold') NOT NULL,
    privilege_type ENUM('free_ride','free_shipping','hot_promotion') NOT NULL,
    used_count INT DEFAULT 0,
    month_year VARCHAR(7) NOT NULL, -- 格式：2024-01
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_user_month (user_id, month_year),
    INDEX idx_privilege_type (privilege_type)
);
```

##### 会员靓号管理表：
```sql
CREATE TABLE member_vip_numbers (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    original_id VARCHAR(20) NOT NULL, -- 原始趣玩ID
    vip_number VARCHAR(20) NOT NULL, -- 会员靓号
    member_level ENUM('silver','gold','diamond','black_gold') NOT NULL,
    is_active TINYINT(1) DEFAULT 1, -- 是否激活显示
    assigned_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    expires_at TIMESTAMP NULL, -- 会员到期时间
    INDEX idx_user_id (user_id),
    INDEX idx_vip_number (vip_number),
    UNIQUE KEY unique_vip_number (vip_number)
);
```

##### 会员特权配置表：
```sql
CREATE TABLE member_privilege_config (
    id INT AUTO_INCREMENT PRIMARY KEY,
    member_level ENUM('silver','gold','diamond','black_gold') NOT NULL,
    privilege_name VARCHAR(50) NOT NULL,
    privilege_value INT NOT NULL, -- 数值型特权的值
    privilege_description TEXT,
    is_active TINYINT(1) DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_member_level (member_level)
);
```

### 3. 贵族等级系统（消费体系）

#### 贵族等级（基于累计消费）：
- **青铜星**：0-99元
- **白银星**：100-499元，享受95折优惠
- **黄金星**：500-1999元，享受9折优惠+专属客服
- **钻石星**：2000-4999元，享受85折优惠+月度礼包
- **王者星**：5000-9999元，享受8折优惠+专属活动
- **荣耀星**：10000元以上，享受75折优惠+一对一服务

#### 贵族特权：
- **消费优惠**：不同等级享受不同折扣
- **专属服务**：优先客服、专属活动、定制服务
- **身份象征**：专属标识、贵族聊天室、优先展示
- **增值服务**：免费会员体验、专属礼品、生日特权

## 💡 系统联动设计

### 三套等级相互促进：
- **用户等级高** → 贵族升级更快、会员折扣
- **贵族等级高** → 经验值加成、会员特价
- **会员等级高** → 经验值翻倍、消费返利

### 特殊加成机制：
- **周末双倍**：周六日所有积分再翻倍
- **连续签到加成**：连续7天+10积分，连续30天+50积分
- **等级加成**：Lv.5以上每日额外+5积分，Lv.8以上每日额外+10积分

## ⏰ 升级时间预估

### 普通用户（每日约20积分）：
- **Lv.1→Lv.2**：25天
- **Lv.1→Lv.5**：约9-10个月
- **Lv.1→Lv.10**：约5-6年

### 会员用户（每日40-80积分）：
- **白银会员**：Lv.1→Lv.5 约4-5个月
- **黄金会员**：Lv.1→Lv.5 约3-4个月
- **钻石会员**：Lv.1→Lv.5 约3个月
- **黑金会员**：Lv.1→Lv.5 约2-3个月
- **满级至尊**：会员需要2-3年

## 🎁 奖励机制

### 升级奖励：
- **用户等级**：升级获得积分、道具、头像框
- **贵族等级**：升级获得优惠券、专属礼品
- **会员续费**：续费获得额外天数、专属道具

### 成就系统：
- **里程碑奖励**：首次发布、首个粉丝、首次消费
- **连续奖励**：连续签到、连续活跃、连续消费
- **特殊成就**：节日活动、限时挑战、社区贡献

## 🚀 使用说明

前后台可以通过以下方式引用本系统：

```php
// 引用用户等级类
require_once 'user_level_system/classes/UserLevel.php';

// 引用等级配置
require_once 'user_level_system/config/level_config.php';
```

## 📝 开发状态

- [ ] 系统设计中
- [ ] 配置文件待创建
- [ ] 核心类待开发
- [ ] API接口待实现
- [ ] 前台组件待开发
- [ ] 后台管理待开发

---

*本系统设计将在讨论确定后完善具体实现方案*
