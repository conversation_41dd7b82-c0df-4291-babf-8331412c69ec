<?php
/**
 * 权限管理 - 权限审批页面
 * 趣玩星球管理后台
 */

session_start();

// 登录检查
if (!isset($_SESSION['admin_id'])) {
    header('Location: ../login.php');
    exit;
}

$admin_name = $_SESSION['admin_name'] ?? '测试管理员';
$admin_id = $_SESSION['admin_id'] ?? '1';
$admin_employee_id = $_SESSION['admin_employee_id'] ?? '000001';

// 模拟权限审批数据（避免数据库依赖）
$pending_requests = [
    [
        'id' => 1,
        'permission_name' => '用户管理权限',
        'user_name' => '李小红',
        'employee_id' => 'CS002',
        'reason' => '需要管理部门内的用户账户，处理用户咨询和账户问题',
        'created_at' => '2024-05-24 09:30:00'
    ],
    [
        'id' => 2,
        'permission_name' => '数据导出权限',
        'user_name' => '王小强',
        'employee_id' => 'CS003',
        'reason' => '需要导出客户服务数据进行分析，提升服务质量',
        'created_at' => '2024-05-24 11:15:00'
    ],
    [
        'id' => 3,
        'permission_name' => '内容审核权限',
        'user_name' => '赵小美',
        'employee_id' => 'CS004',
        'reason' => '协助处理用户举报内容，维护平台内容质量',
        'created_at' => '2024-05-24 14:20:00'
    ]
];

$recent_reviews = [
    [
        'id' => 4,
        'permission_name' => '数据统计查看',
        'user_name' => '孙小亮',
        'employee_id' => 'CS005',
        'status' => 'approved',
        'reviewed_at' => '2024-05-23 16:30:00'
    ],
    [
        'id' => 5,
        'permission_name' => '系统配置权限',
        'user_name' => '周小芳',
        'employee_id' => 'CS006',
        'status' => 'rejected',
        'reviewed_at' => '2024-05-23 10:45:00'
    ]
];
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>权限审批 - 趣玩星球管理后台</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="../assets/css/admin-layout.css">
    <style>
        /* 权限审批页面专用样式 */
        .approval-content {
            padding: 24px;
        }

        .stats-overview {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 32px;
        }

        .stat-card {
            background: white;
            border-radius: 16px;
            padding: 20px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
            text-align: center;
            transition: all 0.3s ease;
        }

        .stat-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15);
        }

        .stat-icon {
            width: 60px;
            height: 60px;
            margin: 0 auto 16px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            color: white;
        }

        .stat-number {
            font-size: 2rem;
            font-weight: 700;
            color: var(--gray-800);
            margin-bottom: 4px;
        }

        .stat-label {
            color: var(--gray-600);
            font-size: 0.875rem;
        }

        .content-sections {
            display: grid;
            grid-template-columns: 2fr 1fr;
            gap: 32px;
        }

        .section-card {
            background: white;
            border-radius: 16px;
            padding: 24px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
        }

        .section-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 24px;
            padding-bottom: 16px;
            border-bottom: 2px solid var(--gray-100);
        }

        .section-title {
            display: flex;
            align-items: center;
            gap: 12px;
            font-size: 1.25rem;
            font-weight: 700;
            color: var(--gray-800);
        }

        .section-badge {
            background: var(--error-color);
            color: white;
            font-size: 0.75rem;
            font-weight: 600;
            padding: 4px 8px;
            border-radius: 12px;
            min-width: 20px;
            text-align: center;
        }

        /* 待审批列表 */
        .request-list {
            display: flex;
            flex-direction: column;
            gap: 16px;
        }

        .request-card {
            background: var(--gray-50);
            border-radius: 12px;
            padding: 20px;
            border: 2px solid transparent;
            transition: all 0.3s ease;
        }

        .request-card:hover {
            border-color: var(--primary-color);
            background: white;
            box-shadow: 0 4px 20px rgba(64, 224, 208, 0.1);
        }

        .request-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 16px;
        }

        .request-info h4 {
            font-weight: 600;
            color: var(--gray-800);
            margin-bottom: 4px;
        }

        .request-meta {
            font-size: 0.875rem;
            color: var(--gray-500);
        }

        .request-user {
            text-align: right;
        }

        .user-name {
            font-weight: 600;
            color: var(--gray-800);
            font-size: 0.875rem;
        }

        .user-id {
            font-size: 0.75rem;
            color: var(--gray-500);
        }

        .request-reason {
            background: white;
            padding: 16px;
            border-radius: 8px;
            margin-bottom: 16px;
            font-size: 0.875rem;
            color: var(--gray-600);
            line-height: 1.5;
            border-left: 4px solid var(--primary-color);
        }

        .request-actions {
            display: flex;
            gap: 12px;
        }

        .action-btn {
            flex: 1;
            padding: 10px 16px;
            border: none;
            border-radius: var(--border-radius);
            font-weight: 600;
            cursor: pointer;
            transition: var(--transition);
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
            font-size: 0.875rem;
        }

        .btn-approve {
            background: linear-gradient(135deg, var(--success-color), #34D399);
            color: white;
        }

        .btn-approve:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
        }

        .btn-reject {
            background: linear-gradient(135deg, var(--error-color), #F87171);
            color: white;
        }

        .btn-reject:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(239, 68, 68, 0.3);
        }

        .btn-detail {
            background: var(--gray-100);
            color: var(--gray-700);
        }

        .btn-detail:hover {
            background: var(--gray-200);
        }

        /* 审批历史 */
        .history-item {
            background: var(--gray-50);
            border-radius: 8px;
            padding: 16px;
            margin-bottom: 12px;
            border-left: 4px solid var(--info-color);
        }

        .history-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;
        }

        .history-title {
            font-weight: 600;
            font-size: 0.875rem;
            color: var(--gray-800);
        }

        .history-status {
            padding: 2px 8px;
            border-radius: 8px;
            font-size: 0.75rem;
            font-weight: 600;
        }

        .history-meta {
            font-size: 0.75rem;
            color: var(--gray-500);
        }

        .empty-state {
            text-align: center;
            padding: 40px 20px;
            color: var(--gray-500);
        }

        .empty-icon {
            font-size: 48px;
            margin-bottom: 16px;
            opacity: 0.5;
        }

        /* 审批模态框 */
        .modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            display: none;
            align-items: center;
            justify-content: center;
            z-index: 1000;
        }

        .modal.show {
            display: flex;
        }

        .modal-content {
            background: white;
            border-radius: 16px;
            padding: 24px;
            max-width: 500px;
            width: 90%;
            max-height: 80vh;
            overflow-y: auto;
        }

        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 16px;
            border-bottom: 2px solid var(--gray-100);
        }

        .modal-title {
            font-size: 1.25rem;
            font-weight: 700;
            color: var(--gray-800);
        }

        .modal-close {
            background: none;
            border: none;
            font-size: 24px;
            color: var(--gray-400);
            cursor: pointer;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-label {
            display: block;
            font-weight: 600;
            color: var(--gray-700);
            margin-bottom: 8px;
        }

        .form-textarea {
            width: 100%;
            padding: 12px;
            border: 2px solid var(--gray-200);
            border-radius: var(--border-radius);
            resize: vertical;
            min-height: 100px;
        }

        .modal-actions {
            display: flex;
            gap: 12px;
            justify-content: flex-end;
        }

        /* 响应式设计 */
        @media (max-width: 1024px) {
            .content-sections {
                grid-template-columns: 1fr;
                gap: 24px;
            }
        }

        @media (max-width: 768px) {
            .approval-content {
                padding: 16px;
            }

            .stats-overview {
                grid-template-columns: repeat(2, 1fr);
            }

            .request-actions {
                flex-direction: column;
            }
        }
    </style>
</head>
<body>
    <div class="admin-layout">
        <!-- 左侧菜单 -->
        <?php include '../includes/sidebar.php'; ?>

        <!-- 主内容区域 -->
        <main class="main-content">
            <!-- 顶部导航栏 -->
            <?php include '../includes/topbar.php'; ?>

            <div class="approval-content">
                <!-- 页面标题 -->
                <div class="page-header">
                    <h1 class="page-title">
                        <i class="fas fa-check-double" style="color: var(--primary-color);"></i>
                        权限审批
                    </h1>
                    <p class="page-subtitle">审核员工权限申请，确保系统安全</p>
                </div>

                <!-- 统计概览 -->
                <div class="stats-overview">
                    <div class="stat-card">
                        <div class="stat-icon" style="background: linear-gradient(135deg, var(--warning-color), #FBBF24);">
                            <i class="fas fa-clock"></i>
                        </div>
                        <div class="stat-number"><?php echo count($pending_requests); ?></div>
                        <div class="stat-label">待审批</div>
                    </div>

                    <div class="stat-card">
                        <div class="stat-icon" style="background: linear-gradient(135deg, var(--success-color), #34D399);">
                            <i class="fas fa-check"></i>
                        </div>
                        <div class="stat-number"><?php echo count(array_filter($recent_reviews, fn($r) => $r['status'] === 'approved')); ?></div>
                        <div class="stat-label">已通过</div>
                    </div>

                    <div class="stat-card">
                        <div class="stat-icon" style="background: linear-gradient(135deg, var(--error-color), #F87171);">
                            <i class="fas fa-times"></i>
                        </div>
                        <div class="stat-number"><?php echo count(array_filter($recent_reviews, fn($r) => $r['status'] === 'rejected')); ?></div>
                        <div class="stat-label">已拒绝</div>
                    </div>

                    <div class="stat-card">
                        <div class="stat-icon" style="background: linear-gradient(135deg, var(--info-color), #60A5FA);">
                            <i class="fas fa-history"></i>
                        </div>
                        <div class="stat-number"><?php echo count($recent_reviews); ?></div>
                        <div class="stat-label">总处理</div>
                    </div>
                </div>

                <div class="content-sections">
                    <!-- 待审批列表 -->
                    <div class="section-card">
                        <div class="section-header">
                            <div class="section-title">
                                <i class="fas fa-list-ul"></i>
                                待审批申请
                                <?php if (count($pending_requests) > 0): ?>
                                    <span class="section-badge"><?php echo count($pending_requests); ?></span>
                                <?php endif; ?>
                            </div>
                        </div>

                        <div class="request-list">
                            <?php if (empty($pending_requests)): ?>
                                <div class="empty-state">
                                    <div class="empty-icon">
                                        <i class="fas fa-check-circle"></i>
                                    </div>
                                    <p>暂无待审批申请</p>
                                    <small>所有申请都已处理完毕</small>
                                </div>
                            <?php else: ?>
                                <?php foreach ($pending_requests as $request): ?>
                                    <div class="request-card">
                                        <div class="request-header">
                                            <div class="request-info">
                                                <h4><?php echo htmlspecialchars($request['permission_name'] ?? '未知权限'); ?></h4>
                                                <div class="request-meta">
                                                    申请时间：<?php echo date('Y-m-d H:i', strtotime($request['created_at'])); ?>
                                                </div>
                                            </div>
                                            <div class="request-user">
                                                <div class="user-name"><?php echo htmlspecialchars($request['user_name'] ?? '未知用户'); ?></div>
                                                <div class="user-id">工号：<?php echo htmlspecialchars($request['employee_id'] ?? 'N/A'); ?></div>
                                            </div>
                                        </div>

                                        <div class="request-reason">
                                            <strong>申请理由：</strong><br>
                                            <?php echo htmlspecialchars($request['reason']); ?>
                                        </div>

                                        <div class="request-actions">
                                            <button class="action-btn btn-approve" onclick="approveRequest(<?php echo $request['id']; ?>, '<?php echo htmlspecialchars($request['user_name']); ?>')">
                                                <i class="fas fa-check"></i>
                                                通过
                                            </button>
                                            <button class="action-btn btn-reject" onclick="rejectRequest(<?php echo $request['id']; ?>, '<?php echo htmlspecialchars($request['user_name']); ?>')">
                                                <i class="fas fa-times"></i>
                                                拒绝
                                            </button>
                                            <button class="action-btn btn-detail" onclick="viewDetail(<?php echo $request['id']; ?>)">
                                                <i class="fas fa-eye"></i>
                                                详情
                                            </button>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            <?php endif; ?>
                        </div>
                    </div>

                    <!-- 审批历史 -->
                    <div class="section-card">
                        <div class="section-header">
                            <div class="section-title">
                                <i class="fas fa-history"></i>
                                审批历史
                            </div>
                        </div>

                        <div class="history-list">
                            <?php if (empty($recent_reviews)): ?>
                                <div class="empty-state">
                                    <div class="empty-icon">
                                        <i class="fas fa-clipboard-list"></i>
                                    </div>
                                    <p>暂无审批记录</p>
                                </div>
                            <?php else: ?>
                                <?php foreach ($recent_reviews as $review): ?>
                                    <div class="history-item">
                                        <div class="history-header">
                                            <div class="history-title">
                                                <?php echo htmlspecialchars($review['permission_name']); ?>
                                            </div>
                                            <div class="history-status status-<?php echo $review['status']; ?>">
                                                <?php echo $review['status'] === 'approved' ? '已通过' : '已拒绝'; ?>
                                            </div>
                                        </div>
                                        <div class="history-meta">
                                            申请人：<?php echo htmlspecialchars($review['user_name']); ?> |
                                            审批时间：<?php echo date('m-d H:i', strtotime($review['reviewed_at'])); ?>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <!-- 审批模态框 -->
    <div class="modal" id="approvalModal">
        <div class="modal-content">
            <div class="modal-header">
                <div class="modal-title" id="modalTitle">审批权限申请</div>
                <button class="modal-close" onclick="closeModal()">&times;</button>
            </div>
            <div class="modal-body">
                <div class="form-group">
                    <label class="form-label">审批意见</label>
                    <textarea class="form-textarea" id="reviewComment" placeholder="请输入审批意见（可选）..."></textarea>
                </div>
            </div>
            <div class="modal-actions">
                <button class="action-btn btn-detail" onclick="closeModal()">取消</button>
                <button class="action-btn" id="confirmBtn" onclick="confirmAction()">确认</button>
            </div>
        </div>
    </div>

    <script src="../assets/js/admin-layout.js"></script>
    <script>
        // 权限审批页面脚本
        let currentAction = null;
        let currentRequestId = null;
        let currentUserName = null;

        document.addEventListener('DOMContentLoaded', function() {
            // 更新页面标题
            updatePageTitle('权限审批');

            // 动画效果
            const cards = document.querySelectorAll('.stat-card, .request-card, .history-item');
            cards.forEach((card, index) => {
                card.style.opacity = '0';
                card.style.transform = 'translateY(20px)';
                setTimeout(() => {
                    card.style.transition = 'all 0.5s ease';
                    card.style.opacity = '1';
                    card.style.transform = 'translateY(0)';
                }, index * 50);
            });
        });

        function approveRequest(requestId, userName) {
            currentAction = 'approve';
            currentRequestId = requestId;
            currentUserName = userName;

            document.getElementById('modalTitle').textContent = `通过 ${userName} 的权限申请`;
            document.getElementById('confirmBtn').textContent = '确认通过';
            document.getElementById('confirmBtn').className = 'action-btn btn-approve';
            document.getElementById('approvalModal').classList.add('show');
        }

        function rejectRequest(requestId, userName) {
            currentAction = 'reject';
            currentRequestId = requestId;
            currentUserName = userName;

            document.getElementById('modalTitle').textContent = `拒绝 ${userName} 的权限申请`;
            document.getElementById('confirmBtn').textContent = '确认拒绝';
            document.getElementById('confirmBtn').className = 'action-btn btn-reject';
            document.getElementById('approvalModal').classList.add('show');
        }

        function viewDetail(requestId) {
            alert('查看详情功能开发中...\n\n将显示申请的详细信息和用户背景');
        }

        function confirmAction() {
            const comment = document.getElementById('reviewComment').value;
            const actionText = currentAction === 'approve' ? '通过' : '拒绝';

            if (confirm(`确定要${actionText} ${currentUserName} 的权限申请吗？`)) {
                // 模拟审批处理
                alert(`权限申请已${actionText}！\n\n申请人将收到审批结果通知。`);
                closeModal();

                // 刷新页面
                setTimeout(() => {
                    location.reload();
                }, 1000);
            }
        }

        function closeModal() {
            document.getElementById('approvalModal').classList.remove('show');
            document.getElementById('reviewComment').value = '';
            currentAction = null;
            currentRequestId = null;
            currentUserName = null;
        }

        // 点击模态框外部关闭
        document.getElementById('approvalModal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeModal();
            }
        });

        console.log('✅ 权限审批页面已加载');
    </script>
</body>
</html>
