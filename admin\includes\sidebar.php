<?php
// 获取当前页面文件名
$current_page = basename($_SERVER['PHP_SELF']);

// 获取待审核数量
$pending_count = 0;
try {
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM realname_verification WHERE verification_status = 'pending'");
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    $pending_count = $result['count'] ?? 0;
} catch (PDOException $e) {
    // 忽略错误，使用默认值
}

// 获取管理员信息
$admin_name = $_SESSION['admin_name'] ?? '管理员';
$admin_role = $_SESSION['admin_role'] ?? '系统管理员';
?>
<aside class="sidebar">
    <div class="sidebar-header">
        <h2><i class="fas fa-star"></i> 趣玩星球</h2>
        <p>后台管理系统</p>
    </div>
    
    <nav class="sidebar-nav">
        <ul>
            <li class="<?php echo $current_page === 'index.php' ? 'active' : ''; ?>">
                <a href="index.php">
                    <i class="fas fa-tachometer-alt"></i>
                    <span>仪表盘</span>
                </a>
            </li>
            <li class="<?php echo $current_page === 'verification.php' ? 'active' : ''; ?>">
                <a href="verification.php">
                    <i class="fas fa-user-check"></i>
                    <span>实名认证审核</span>
                    <?php if ($pending_count > 0): ?>
                        <span class="badge"><?php echo $pending_count; ?></span>
                    <?php endif; ?>
                </a>
            </li>
            <li class="<?php echo $current_page === 'users.php' ? 'active' : ''; ?>">
                <a href="users.php">
                    <i class="fas fa-users"></i>
                    <span>用户管理</span>
                </a>
            </li>
            <li class="<?php echo $current_page === 'content.php' ? 'active' : ''; ?>">
                <a href="content.php">
                    <i class="fas fa-file-alt"></i>
                    <span>内容管理</span>
                </a>
            </li>
            <li class="<?php echo $current_page === 'reports.php' ? 'active' : ''; ?>">
                <a href="reports.php">
                    <i class="fas fa-chart-bar"></i>
                    <span>数据报表</span>
                </a>
            </li>
            <li class="<?php echo $current_page === 'settings.php' ? 'active' : ''; ?>">
                <a href="settings.php">
                    <i class="fas fa-cog"></i>
                    <span>系统设置</span>
                </a>
            </li>
        </ul>
    </nav>
    
    <div class="sidebar-footer">
        <div class="admin-info">
            <div class="admin-avatar">
                <i class="fas fa-user-shield"></i>
            </div>
            <div class="admin-details">
                <p class="admin-name"><?php echo htmlspecialchars($admin_name); ?></p>
                <p class="admin-role"><?php echo htmlspecialchars($admin_role); ?></p>
            </div>
        </div>
        <a href="logout.php" class="logout-btn" onclick="return confirm('确定要退出登录吗？')">
            <i class="fas fa-sign-out-alt"></i>
            <span>退出登录</span>
        </a>
    </div>
</aside>
