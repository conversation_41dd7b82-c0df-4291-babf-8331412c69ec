<?php
session_start();

// 检查是否通过了手机验证
if (!isset($_SESSION['phone_verified']) || $_SESSION['phone_verified'] !== 'true') {
    header('Location: ../register/index.php');
    exit;
}

// 数据库连接配置
$db_config = [
    'host' => 'localhost',
    'username' => 'quwanplanet',
    'password' => 'nJmJm23FB4Xn6Fc3',
    'dbname' => 'quwanplanet',
    'port' => 3306,
    'charset' => 'utf8mb4'
];

// 错误信息
$error = '';
$success = '';

// 这个页面现在只显示表单，实际的注册逻辑在JavaScript中处理
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0, user-scalable=no, shrink-to-fit=no">
    <meta name="format-detection" content="telephone=no">
    <meta name="theme-color" content="#FFFFFF">
    <meta name="apple-mobile-web-app-status-bar-style" content="light-content">
    <title>完善个人信息 - 趣玩星球</title>
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="../css/page-protection.css">
</head>
<body>
    <!-- 星空背景动画 -->
    <!-- Toast提示，确保这个元素存在且ID为toast -->
    <div class="toast" id="toast" style="z-index:9999;"></div>

    <div class="container">
        <!-- 品牌区域 -->
        <div class="brand-section">
            <div class="brand-logo">
                <img src="https://s1.imagehub.cc/images/2025/05/26/2cdd63b39f96072fc60cd733027d2a30.png" alt="趣玩星球" style="width: 100%; height: 100%; object-fit: contain;">
            </div>
            <div class="brand-text">
                <img src="https://s1.imagehub.cc/images/2025/05/26/ef3f8c98828faa0de2111ac0cfc9bd6d.png" alt="趣玩星球 - 探索有趣的生活">
            </div>
        </div>

        <div class="onboarding-title">
            <h1>完善个人信息</h1>
            <p>让我们更好地了解您，提供更个性化的服务</p>
        </div>

        <div class="onboarding-card">
            <?php if (!empty($error)): ?>
                <div class="error-message"><?php echo $error; ?></div>
            <?php endif; ?>

            <?php if (!empty($success)): ?>
                <div class="success-message"><?php echo $success; ?></div>
            <?php endif; ?>

            <form id="onboarding-form">
                <div class="avatar-upload">
                    <div class="avatar-preview">
                        <div id="avatar-preview" style="background-image: url('img/default-avatar.jpg');"></div>
                        <div class="avatar-edit">
                            <input type="file" id="avatar" name="avatar" accept=".png, .jpg, .jpeg">
                            <label for="avatar" title="选择头像">
                                <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <path d="M23 19C23 20.1046 22.1046 21 21 21H3C1.89543 21 1 20.1046 1 19V8C1 6.89543 1.89543 6 3 6H7L9 4H15L17 6H21C22.1046 6 23 6.89543 23 8V19Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                    <circle cx="12" cy="13" r="3" stroke="currentColor" stroke-width="2"/>
                                </svg>
                            </label>
                        </div>
                    </div>
                    <div class="avatar-text">选择一个个性化头像</div>
                </div>

                <div class="form-group">
                    <label for="phone">手机号 <span class="required">*</span></label>
                    <input type="text" id="phone" name="phone" required placeholder="获取中..." readonly>
                    <small class="field-note">注册时验证的手机号，不可修改</small>
                </div>

                <div class="form-group">
                    <label for="nickname">昵称 <span class="required">*</span></label>
                    <input type="text" id="nickname" name="nickname" required placeholder="请输入您的昵称">
                </div>

                <div class="form-group">
                    <label>性别 <span class="required">*</span></label>
                    <div class="radio-group">
                        <label class="radio-label">
                            <input type="radio" name="gender" value="male" required>
                            <span>男</span>
                        </label>
                        <label class="radio-label">
                            <input type="radio" name="gender" value="female" required>
                            <span>女</span>
                        </label>
                        <label class="radio-label">
                            <input type="radio" name="gender" value="other" required>
                            <span>其他</span>
                        </label>
                    </div>
                </div>

                <div class="form-group">
                    <label for="birth_date">出生日期 <span class="required">*</span></label>
                    <div class="date-input-container">
                        <input type="text" id="birth_date" name="birth_date" required placeholder="请点击日历图标选择出生日期" readonly>
                        <button type="button" class="date-select-btn" onclick="openDatePicker()">
                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                                <rect x="3" y="4" width="18" height="18" rx="2" ry="2" stroke="currentColor" stroke-width="2"/>
                                <line x1="16" y1="2" x2="16" y2="6" stroke="currentColor" stroke-width="2"/>
                                <line x1="8" y1="2" x2="8" y2="6" stroke="currentColor" stroke-width="2"/>
                                <line x1="3" y1="10" x2="21" y2="10" stroke="currentColor" stroke-width="2"/>
                            </svg>
                        </button>
                    </div>
                    <small class="field-note">请点击右侧日历图标选择您的出生日期</small>
                </div>

                <div class="form-group">
                    <label for="region">所在地区 <span class="required">*</span></label>
                    <div class="region-input-container">
                        <input type="text" id="region" name="region" required placeholder="点击选择城市" readonly>
                        <button type="button" class="region-select-btn" onclick="openCitySelector()">
                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                                <path d="M21 10C21 17 12 23 12 23S3 17 3 10C3 5.02944 7.02944 1 12 1C16.9706 1 21 5.02944 21 10Z" stroke="currentColor" stroke-width="2"/>
                                <circle cx="12" cy="10" r="3" stroke="currentColor" stroke-width="2"/>
                            </svg>
                        </button>
                    </div>
                </div>

                <div class="form-group">
                    <label for="email">邮箱 <span class="required">*</span></label>
                    <input type="email" id="email" name="email" required placeholder="请输入您的邮箱">
                </div>

                <div class="form-group">
                    <label for="password">密码 <span class="required">*</span></label>
                    <input type="password" id="password" name="password" required placeholder="请设置登录密码">
                </div>

                <div class="form-group">
                    <label for="confirm_password">确认密码 <span class="required">*</span></label>
                    <input type="password" id="confirm_password" name="confirm_password" required placeholder="请再次输入密码">
                </div>

                <div class="form-group">
                    <label for="bio">个人简介</label>
                    <textarea id="bio" name="bio" rows="3" placeholder="简单介绍一下自己吧..."></textarea>
                </div>

                <div class="form-actions">
                    <button type="submit" class="btn-submit">完成注册</button>
                </div>
            </form>
        </div>
    </div>

    <!-- 头像审核动画覆盖层 -->
    <div class="avatar-moderation-overlay" id="avatar-moderation-overlay">
        <div class="moderation-star-system">
            <div class="moderation-star moderation-star-1"></div>
            <div class="moderation-star moderation-star-2"></div>
            <div class="moderation-star moderation-star-3"></div>
        </div>

        <div class="moderation-text">头像自动审核中</div>
        <div class="moderation-subtext">正在检测头像内容是否符合社区规范...</div>
    </div>

    <!-- 自定义日期选择器覆盖层 -->
    <div class="date-picker-overlay" id="date-picker-overlay">
        <div class="date-picker-modal">
            <div class="date-picker-header">
                <h3>选择出生日期</h3>
                <button class="date-picker-close" onclick="closeDatePicker()">
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                        <line x1="18" y1="6" x2="6" y2="18" stroke="currentColor" stroke-width="2"/>
                        <line x1="6" y1="6" x2="18" y2="18" stroke="currentColor" stroke-width="2"/>
                    </svg>
                </button>
            </div>

            <div class="date-picker-content">
                <div class="date-selectors">
                    <div class="selector-group">
                        <label>年份</label>
                        <div class="selector-wrapper">
                            <select id="year-selector" class="date-selector">
                                <!-- 年份选项将通过JavaScript生成 -->
                            </select>
                        </div>
                    </div>

                    <div class="selector-group">
                        <label>月份</label>
                        <div class="selector-wrapper">
                            <select id="month-selector" class="date-selector">
                                <option value="1">1月</option>
                                <option value="2">2月</option>
                                <option value="3">3月</option>
                                <option value="4">4月</option>
                                <option value="5">5月</option>
                                <option value="6">6月</option>
                                <option value="7">7月</option>
                                <option value="8">8月</option>
                                <option value="9">9月</option>
                                <option value="10">10月</option>
                                <option value="11">11月</option>
                                <option value="12">12月</option>
                            </select>
                        </div>
                    </div>

                    <div class="selector-group">
                        <label>日期</label>
                        <div class="selector-wrapper">
                            <select id="day-selector" class="date-selector">
                                <!-- 日期选项将通过JavaScript生成 -->
                            </select>
                        </div>
                    </div>
                </div>

                <div class="date-picker-actions">
                    <button class="btn-cancel" onclick="closeDatePicker()">取消</button>
                    <button class="btn-confirm" onclick="confirmDateSelection()">确认</button>
                </div>
            </div>
        </div>
    </div>

    <script src="../js/page-protection.js"></script>
    <script>
        // 设置全局变量
        window.verifiedPhone = '<?php echo $_SESSION['verified_phone'] ?? ''; ?>';
    </script>
    <script src="js/script.js"></script>
</body>
</html>
