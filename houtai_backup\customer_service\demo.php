<?php
session_start();

// 模拟登录状态（仅用于演示）
if (!isset($_SESSION['admin_logged_in'])) {
    $_SESSION['admin_logged_in'] = true;
    $_SESSION['admin_name'] = '演示管理员';
    $_SESSION['admin_role'] = '系统管理员';
}

// 模拟数据
$stats = [
    'today_conversations' => 156,
    'total_conversations' => 2847,
    'active_sessions' => 23,
    'human_service_count' => 8
];

$bot_config = [
    'bot_name' => '趣玩小助手',
    'is_enabled' => 1,
    'welcome_message' => '您好！我是趣玩星球智能客服小助手🤖

我可以帮助您解决以下问题：
• 账号相关问题
• 功能使用指导
• 常见问题解答

请描述您遇到的问题，我会尽力为您解答！'
];

$reply_rules = [
    [
        'keywords' => '["账号", "封号", "封禁", "被封"]',
        'reply_content' => '关于账号封禁问题：

如果您的账号被误封，可以通过以下方式申诉：
• 点击登录页面的"提交申诉"按钮
• 详细说明情况并提供证明材料
• 我们会在3-5个工作日内处理

申诉时请如实填写信息，虚假信息将影响申诉结果。',
        'priority' => 10
    ],
    [
        'keywords' => '["申诉", "解封", "误封"]',
        'reply_content' => '账号申诉流程：

1️⃣ 访问申诉页面
2️⃣ 填写手机号和申诉原因
3️⃣ 上传相关证明材料
4️⃣ 等待审核结果（3-5个工作日）

申诉提交后无法修改，请仔细核对信息。',
        'priority' => 9
    ]
];

$recent_conversations = [
    [
        'username' => '张三',
        'phone' => '138****1234',
        'user_message' => '我的账号被误封了，怎么申诉？',
        'bot_reply' => '关于账号封禁问题，您可以通过申诉页面提交申诉...',
        'admin_reply' => null,
        'is_human_service' => 0,
        'created_at' => '2024-12-19 14:30:25'
    ],
    [
        'username' => '李四',
        'phone' => '139****5678',
        'user_message' => '人工客服',
        'bot_reply' => '正在为您转接人工客服，请稍候...',
        'admin_reply' => '您好，我是人工客服，请问有什么可以帮助您的？',
        'is_human_service' => 1,
        'created_at' => '2024-12-19 14:25:10'
    ]
];
?>

<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>客服管理演示 - 趣玩星球后台</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="../assets/css/admin.css">
    <style>
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: var(--spacing-lg);
            margin-bottom: var(--spacing-2xl);
        }

        .stat-card {
            background: white;
            padding: var(--spacing-xl);
            border-radius: var(--radius-xl);
            box-shadow: var(--shadow-lg);
            text-align: center;
            border-left: 4px solid var(--primary-color);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .stat-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
        }

        .stat-card:hover {
            transform: translateY(-4px);
            box-shadow: var(--shadow-xl);
        }

        .stat-number {
            font-size: 2.5rem;
            font-weight: 700;
            color: var(--primary-color);
            margin-bottom: var(--spacing-sm);
            background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .stat-label {
            color: var(--gray-600);
            font-size: 0.875rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.05em;
        }

        .conversation-item {
            background: white;
            border-radius: var(--radius-xl);
            padding: var(--spacing-lg);
            margin-bottom: var(--spacing-md);
            border-left: 4px solid var(--gray-200);
            box-shadow: var(--shadow-md);
            transition: all 0.3s ease;
        }

        .conversation-item:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-lg);
        }

        .conversation-item.human-service {
            border-left-color: var(--warning-color);
            background: linear-gradient(135deg, rgba(245, 158, 11, 0.02), rgba(255, 255, 255, 1));
        }

        .conversation-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: var(--spacing-md);
            padding-bottom: var(--spacing-sm);
            border-bottom: 1px solid var(--gray-100);
        }

        .user-info {
            font-weight: 600;
            color: var(--gray-800);
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);
        }

        .conversation-time {
            font-size: 0.75rem;
            color: var(--gray-500);
            font-weight: 500;
        }

        .message-content {
            background: var(--gray-50);
            padding: var(--spacing-md);
            border-radius: var(--radius-lg);
            margin-bottom: var(--spacing-sm);
            border-left: 3px solid var(--gray-300);
            transition: all 0.3s ease;
        }

        .message-content:hover {
            background: var(--gray-100);
        }

        .user-message {
            border-left-color: var(--primary-color);
            background: linear-gradient(135deg, rgba(111, 123, 245, 0.05), var(--gray-50));
        }

        .bot-reply {
            border-left-color: var(--success-color);
            background: linear-gradient(135deg, rgba(16, 185, 129, 0.05), var(--gray-50));
        }

        .config-section {
            background: white;
            border-radius: var(--radius-xl);
            padding: var(--spacing-2xl);
            margin-bottom: var(--spacing-xl);
            box-shadow: var(--shadow-lg);
            border: 1px solid var(--gray-100);
            transition: all 0.3s ease;
        }

        .config-section:hover {
            box-shadow: var(--shadow-xl);
        }

        .config-section h3 {
            color: var(--gray-800);
            font-size: 1.25rem;
            font-weight: 700;
            margin-bottom: var(--spacing-lg);
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);
            padding-bottom: var(--spacing-md);
            border-bottom: 2px solid var(--gray-100);
        }

        .config-section h3 i {
            color: var(--primary-color);
            font-size: 1.125rem;
        }

        .rule-item {
            background: var(--gray-50);
            border: 1px solid var(--gray-200);
            border-radius: var(--radius-xl);
            padding: var(--spacing-lg);
            margin-bottom: var(--spacing-md);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .rule-item::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 4px;
            height: 100%;
            background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
        }

        .rule-item:hover {
            background: white;
            box-shadow: var(--shadow-md);
            transform: translateY(-2px);
        }

        .rule-keywords {
            display: flex;
            flex-wrap: wrap;
            gap: var(--spacing-xs);
            margin-bottom: var(--spacing-sm);
        }

        .keyword-tag {
            background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
            color: white;
            padding: 4px 12px;
            border-radius: var(--radius-xl);
            font-size: 0.75rem;
            font-weight: 600;
            box-shadow: var(--shadow-sm);
            transition: all 0.3s ease;
        }

        .keyword-tag:hover {
            transform: translateY(-1px);
            box-shadow: var(--shadow-md);
        }

        .priority-badge {
            background: linear-gradient(135deg, var(--warning-color), #FBBF24);
            color: white;
            padding: 4px 12px;
            border-radius: var(--radius-xl);
            font-size: 0.6875rem;
            font-weight: 700;
            text-transform: uppercase;
            letter-spacing: 0.05em;
            box-shadow: var(--shadow-sm);
        }

        .demo-notice {
            background: linear-gradient(135deg, rgba(59, 130, 246, 0.1), rgba(147, 197, 253, 0.1));
            border: 2px solid #3B82F6;
            border-radius: var(--radius-xl);
            padding: var(--spacing-lg);
            margin-bottom: var(--spacing-xl);
            text-align: center;
        }

        .demo-notice h3 {
            color: #3B82F6;
            margin-bottom: var(--spacing-sm);
        }

        .demo-notice p {
            color: var(--gray-700);
            margin: 0;
        }
    </style>
</head>
<body>
    <div class="admin-container">
        <!-- 侧边栏 -->
        <?php include '../includes/sidebar.php'; ?>

        <!-- 主内容区域 -->
        <div class="main-content">
            <div class="content-wrapper">
                <div class="content-header">
                    <h1><i class="fas fa-headset"></i> 客服管理演示</h1>
                    <div class="header-actions">
                        <button class="btn btn-primary" onclick="showToast('机器人配置功能开发中！', 'info')">
                            <i class="fas fa-robot"></i>
                            机器人配置
                        </button>
                        <button class="btn btn-success" onclick="showToast('回复规则管理功能开发中！', 'info')">
                            <i class="fas fa-comments"></i>
                            回复规则
                        </button>
                    </div>
                </div>

                <!-- 演示说明 -->
                <div class="demo-notice">
                    <h3><i class="fas fa-info-circle"></i> 演示页面</h3>
                    <p>这是客服管理页面的演示版本，使用模拟数据展示页面布局和功能。实际页面需要连接数据库。</p>
                </div>

                <!-- 统计卡片 -->
                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-number"><?php echo number_format($stats['today_conversations']); ?></div>
                        <div class="stat-label">今日对话数</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number"><?php echo number_format($stats['total_conversations']); ?></div>
                        <div class="stat-label">总对话数</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number"><?php echo number_format($stats['active_sessions']); ?></div>
                        <div class="stat-label">活跃会话</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number"><?php echo number_format($stats['human_service_count']); ?></div>
                        <div class="stat-label">人工客服</div>
                    </div>
                </div>

                <!-- 机器人配置 -->
                <div class="config-section">
                    <h3><i class="fas fa-robot"></i> 当前机器人配置</h3>
                    <div class="info-grid">
                        <div class="info-item">
                            <span class="label">机器人名称</span>
                            <span class="value"><?php echo htmlspecialchars($bot_config['bot_name']); ?></span>
                        </div>
                        <div class="info-item">
                            <span class="label">状态</span>
                            <span class="value">
                                <span class="status-badge success">启用</span>
                            </span>
                        </div>
                        <div class="info-item full-width">
                            <span class="label">欢迎消息</span>
                            <div class="value"><?php echo nl2br(htmlspecialchars($bot_config['welcome_message'])); ?></div>
                        </div>
                    </div>
                </div>

                <!-- 回复规则 -->
                <div class="config-section">
                    <h3><i class="fas fa-comments"></i> 回复规则 (<?php echo count($reply_rules); ?>条)</h3>
                    <?php foreach ($reply_rules as $rule): ?>
                        <div class="rule-item">
                            <div style="display: flex; justify-content: space-between; align-items: flex-start; margin-bottom: 8px;">
                                <div class="rule-keywords">
                                    <?php
                                    $keywords = json_decode($rule['keywords'], true);
                                    if (is_array($keywords)):
                                        foreach ($keywords as $keyword):
                                    ?>
                                        <span class="keyword-tag"><?php echo htmlspecialchars($keyword); ?></span>
                                    <?php
                                        endforeach;
                                    endif;
                                    ?>
                                </div>
                                <span class="priority-badge">优先级: <?php echo $rule['priority']; ?></span>
                            </div>
                            <div style="color: var(--gray-700); line-height: 1.5;">
                                <?php echo nl2br(htmlspecialchars($rule['reply_content'])); ?>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>

                <!-- 最近对话 -->
                <div class="config-section">
                    <h3><i class="fas fa-history"></i> 最近对话</h3>
                    <?php foreach ($recent_conversations as $conv): ?>
                        <div class="conversation-item <?php echo $conv['is_human_service'] ? 'human-service' : ''; ?>">
                            <div class="conversation-header">
                                <div class="user-info">
                                    <?php echo htmlspecialchars($conv['username']); ?>
                                    (<?php echo htmlspecialchars($conv['phone']); ?>)
                                    <?php if ($conv['is_human_service']): ?>
                                        <span class="status-badge warning">人工客服</span>
                                    <?php endif; ?>
                                </div>
                                <div class="conversation-time">
                                    <?php echo $conv['created_at']; ?>
                                </div>
                            </div>

                            <div class="message-content user-message">
                                <strong>用户:</strong> <?php echo htmlspecialchars($conv['user_message']); ?>
                            </div>

                            <?php if ($conv['bot_reply']): ?>
                                <div class="message-content bot-reply">
                                    <strong>机器人:</strong> <?php echo nl2br(htmlspecialchars($conv['bot_reply'])); ?>
                                </div>
                            <?php endif; ?>

                            <?php if ($conv['admin_reply']): ?>
                                <div class="message-content" style="border-left-color: #f59e0b;">
                                    <strong>客服:</strong> <?php echo nl2br(htmlspecialchars($conv['admin_reply'])); ?>
                                </div>
                            <?php endif; ?>
                        </div>
                    <?php endforeach; ?>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Toast 通知函数
        function showToast(message, type = 'info') {
            const toast = document.createElement('div');
            toast.className = `toast ${type}`;
            toast.innerHTML = `
                <div style="display: flex; align-items: center; gap: 12px;">
                    <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'warning' ? 'exclamation-triangle' : type === 'error' ? 'times-circle' : 'info-circle'}"></i>
                    <span>${message}</span>
                    <button onclick="this.parentElement.parentElement.remove()" style="background: none; border: none; color: inherit; cursor: pointer; margin-left: auto;">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            `;
            
            document.body.appendChild(toast);
            
            // 自动移除
            setTimeout(() => {
                if (toast.parentElement) {
                    toast.remove();
                }
            }, 5000);
        }

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 添加统计卡片的动画效果
            const statCards = document.querySelectorAll('.stat-card');
            statCards.forEach((card, index) => {
                card.style.animationDelay = `${index * 0.1}s`;
                card.style.animation = 'fadeInUp 0.6s ease forwards';
            });

            // 添加对话项的悬停效果
            const conversationItems = document.querySelectorAll('.conversation-item');
            conversationItems.forEach(item => {
                item.addEventListener('mouseenter', function() {
                    this.style.borderLeftWidth = '6px';
                });
                
                item.addEventListener('mouseleave', function() {
                    this.style.borderLeftWidth = '4px';
                });
            });

            // 显示欢迎消息
            showToast('客服管理页面样式修复完成！这是演示版本。', 'success');
        });
    </script>
</body>
</html>
