<?php
// 修复版聊天页面 - 使用优化的轮询
header('Content-Type: text/html; charset=UTF-8');
ini_set('default_charset', 'UTF-8');

session_start();

// 模拟用户登录（用于测试）
if (!isset($_SESSION['user_id'])) {
    $_SESSION['user_id'] = 4;
    $_SESSION['user_name'] = '测试用户';
    $_SESSION['nickname'] = '测试用户';
}

$sessionId = $_GET['session_id'] ?? '';
if (empty($sessionId)) {
    echo '<h1>错误：缺少会话ID</h1>';
    exit;
}

require_once '../../houtai_backup/db_config.php';

// 获取会话信息
$session = null;
try {
    $pdo = getDbConnection();
    $stmt = $pdo->prepare("SELECT * FROM customer_service_sessions WHERE session_id = ?");
    $stmt->execute([$sessionId]);
    $session = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$session) {
        echo '<h1>错误：会话不存在</h1>';
        exit;
    }
} catch (Exception $e) {
    echo '<h1>错误：' . htmlspecialchars($e->getMessage()) . '</h1>';
    exit;
}
?>

<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>客服聊天 - 修复版</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f5f5f5;
            height: 100vh;
            display: flex;
            flex-direction: column;
        }
        
        .header {
            background: #6F7BF5;
            color: white;
            padding: 15px 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .header h1 {
            font-size: 18px;
            font-weight: 600;
        }
        
        .status {
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 14px;
        }
        
        .status-indicator {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: #dc3545;
            animation: pulse 2s infinite;
        }
        
        .status-indicator.connected {
            background: #28a745;
        }
        
        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }
        
        .chat-container {
            flex: 1;
            display: flex;
            flex-direction: column;
            max-width: 800px;
            margin: 0 auto;
            background: white;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
        }
        
        .messages {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
            background: #fafafa;
        }
        
        .message {
            margin: 15px 0;
            display: flex;
            align-items: flex-end;
            gap: 10px;
        }
        
        .message.user {
            flex-direction: row-reverse;
        }
        
        .message-avatar {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 16px;
            color: white;
            flex-shrink: 0;
        }
        
        .message.user .message-avatar {
            background: #6F7BF5;
        }
        
        .message.cs .message-avatar {
            background: #28a745;
        }
        
        .message.system .message-avatar {
            background: #6c757d;
        }
        
        .message-content {
            max-width: 70%;
            background: white;
            padding: 12px 16px;
            border-radius: 18px;
            box-shadow: 0 1px 2px rgba(0,0,0,0.1);
            position: relative;
        }
        
        .message.user .message-content {
            background: #6F7BF5;
            color: white;
        }
        
        .message.system .message-content {
            background: #e9ecef;
            color: #495057;
            text-align: center;
            font-style: italic;
        }
        
        .message-sender {
            font-size: 12px;
            color: #666;
            margin-bottom: 4px;
        }
        
        .message.user .message-sender {
            color: rgba(255,255,255,0.8);
        }
        
        .message-text {
            font-size: 14px;
            line-height: 1.4;
            word-wrap: break-word;
        }
        
        .message-time {
            font-size: 11px;
            color: #999;
            margin-top: 4px;
        }
        
        .message.user .message-time {
            color: rgba(255,255,255,0.7);
        }
        
        .input-area {
            padding: 20px;
            border-top: 1px solid #e1e1e1;
            background: white;
        }
        
        .input-group {
            display: flex;
            gap: 10px;
            align-items: flex-end;
        }
        
        .message-input {
            flex: 1;
            padding: 12px 16px;
            border: 1px solid #ddd;
            border-radius: 25px;
            outline: none;
            font-size: 14px;
            resize: none;
            min-height: 44px;
            max-height: 120px;
        }
        
        .send-btn {
            width: 44px;
            height: 44px;
            background: #6F7BF5;
            color: white;
            border: none;
            border-radius: 50%;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 16px;
        }
        
        .send-btn:hover {
            background: #5a67d8;
        }
        
        .send-btn:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        
        .notification {
            position: fixed;
            top: 20px;
            right: 20px;
            background: #28a745;
            color: white;
            padding: 15px 20px;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.3);
            z-index: 1000;
            max-width: 300px;
            animation: slideIn 0.3s ease;
        }
        
        .notification.error {
            background: #dc3545;
        }
        
        @keyframes slideIn {
            from { transform: translateX(100%); opacity: 0; }
            to { transform: translateX(0); opacity: 1; }
        }
        
        .debug-info {
            position: fixed;
            bottom: 20px;
            left: 20px;
            background: rgba(0,0,0,0.8);
            color: white;
            padding: 10px;
            border-radius: 5px;
            font-size: 12px;
            font-family: monospace;
            max-width: 300px;
            z-index: 1000;
        }
    </style>
</head>
<body>
    <div class="header">
        <div>
            <h1>💬 客服聊天 - 修复版</h1>
            <div style="font-size: 12px; opacity: 0.9;">
                会话: <?php echo htmlspecialchars($sessionId); ?>
            </div>
        </div>
        <div class="status">
            <div id="status-indicator" class="status-indicator"></div>
            <span id="status-text">连接中...</span>
        </div>
    </div>
    
    <div class="chat-container">
        <div id="messages" class="messages">
            <div class="message system">
                <div class="message-avatar">🤖</div>
                <div class="message-content">
                    <div class="message-text">欢迎使用客服系统！正在初始化...</div>
                </div>
            </div>
        </div>
        
        <div class="input-area">
            <div class="input-group">
                <textarea id="message-input" class="message-input" placeholder="输入消息..." rows="1"></textarea>
                <button id="send-btn" class="send-btn" onclick="sendMessage()">
                    <span>📤</span>
                </button>
            </div>
        </div>
    </div>
    
    <div class="debug-info" id="debug-info">
        初始化中...
    </div>
    
    <script>
        const sessionId = '<?php echo $sessionId; ?>';
        const userId = <?php echo $_SESSION['user_id']; ?>;
        let lastMessageId = 0;
        let pollingInterval = null;
        let isConnected = false;
        let messageCount = 0;
        
        // 更新调试信息
        function updateDebugInfo(info) {
            document.getElementById('debug-info').innerHTML = `
                会话: ${sessionId}<br>
                用户: ${userId}<br>
                连接: ${isConnected ? '是' : '否'}<br>
                消息数: ${messageCount}<br>
                最后检查: ${new Date().toLocaleTimeString()}<br>
                ${info}
            `;
        }
        
        // 更新连接状态
        function updateConnectionStatus(connected) {
            isConnected = connected;
            const indicator = document.getElementById('status-indicator');
            const text = document.getElementById('status-text');
            
            if (connected) {
                indicator.classList.add('connected');
                text.textContent = '已连接';
            } else {
                indicator.classList.remove('connected');
                text.textContent = '连接中断';
            }
            
            updateDebugInfo(connected ? '轮询正常' : '轮询中断');
        }
        
        // 开始轮询
        function startPolling() {
            console.log('开始轮询消息...');
            updateDebugInfo('开始轮询...');
            
            // 立即检查一次
            checkNewMessages();
            
            // 每2秒检查一次
            pollingInterval = setInterval(checkNewMessages, 2000);
        }
        
        // 检查新消息
        async function checkNewMessages() {
            try {
                const url = `api/get_new_messages.php?session_id=${sessionId}&last_message_id=${lastMessageId}`;
                console.log('轮询URL:', url);
                
                const response = await fetch(url);
                const data = await response.json();
                
                console.log('轮询响应:', data);
                updateDebugInfo(`响应: ${data.success ? '成功' : '失败'}`);
                
                if (data.success) {
                    updateConnectionStatus(true);
                    
                    if (data.messages && data.messages.length > 0) {
                        console.log('收到新消息:', data.messages.length, '条');
                        
                        data.messages.forEach(message => {
                            if (message.sender_type === 'customer_service') {
                                addMessage('cs', message.content, message.sender_name);
                                playNotificationSound();
                                showNotification('收到新消息');
                                
                                // 更新最后消息ID
                                if (message.id > lastMessageId) {
                                    lastMessageId = message.id;
                                }
                            }
                        });
                        
                        messageCount += data.messages.length;
                    }
                } else {
                    console.error('轮询失败:', data.error);
                    updateConnectionStatus(false);
                    updateDebugInfo(`错误: ${data.error}`);
                }
                
            } catch (error) {
                console.error('轮询请求失败:', error);
                updateConnectionStatus(false);
                updateDebugInfo(`网络错误: ${error.message}`);
            }
        }
        
        // 添加消息
        function addMessage(type, content, senderName = null) {
            const messagesContainer = document.getElementById('messages');
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${type}`;
            
            let avatarIcon = '';
            switch(type) {
                case 'user':
                    avatarIcon = '👤';
                    break;
                case 'cs':
                    avatarIcon = '👨‍💼';
                    break;
                case 'system':
                    avatarIcon = '🤖';
                    break;
            }
            
            const time = new Date().toLocaleTimeString('zh-CN', {
                hour: '2-digit',
                minute: '2-digit'
            });
            
            messageDiv.innerHTML = `
                <div class="message-avatar">${avatarIcon}</div>
                <div class="message-content">
                    ${senderName && type !== 'user' ? `<div class="message-sender">${senderName}</div>` : ''}
                    <div class="message-text">${content.replace(/\n/g, '<br>')}</div>
                    <div class="message-time">${time}</div>
                </div>
            `;
            
            messagesContainer.appendChild(messageDiv);
            messagesContainer.scrollTop = messagesContainer.scrollHeight;
        }
        
        // 发送消息
        async function sendMessage() {
            const input = document.getElementById('message-input');
            const message = input.value.trim();
            
            if (!message) return;
            
            // 添加用户消息
            addMessage('user', message);
            input.value = '';
            
            try {
                const response = await fetch('api/send_message.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        sessionId: sessionId,
                        message: message
                    })
                });
                
                const data = await response.json();
                
                if (data.success && data.bot_reply) {
                    setTimeout(() => {
                        addMessage('cs', data.bot_reply, data.bot_name);
                    }, 500);
                }
            } catch (error) {
                console.error('发送消息失败:', error);
                showNotification('发送失败: 网络错误', 'error');
            }
        }
        
        // 显示通知
        function showNotification(message, type = 'success') {
            const notification = document.createElement('div');
            notification.className = `notification ${type}`;
            notification.textContent = message;
            
            document.body.appendChild(notification);
            
            setTimeout(() => {
                notification.remove();
            }, 3000);
        }
        
        // 播放通知音
        function playNotificationSound() {
            const audio = new Audio('data:audio/wav;base64,UklGRnoGAABXQVZFZm10IBAAAAABAAEAQB8AAEAfAAABAAgAZGF0YQoGAACBhYqFbF1fdJivrJBhNjVgodDbq2EcBj+a2/LDciUFLIHO8tiJNwgZaLvt559NEAxQp+PwtmMcBjiR1/LMeSwFJHfH8N2QQAoUXrTp66hVFApGn+DyvmwhBSuBzvLZiTYIG2m98OScTgwOUarm7blmGgU7k9n1unEiBC13yO/eizEIHWq+8+OWT');
            audio.volume = 0.3;
            audio.play().catch(e => console.log('无法播放提示音'));
        }
        
        // 输入框事件
        document.getElementById('message-input').addEventListener('keypress', function(e) {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                sendMessage();
            }
        });
        
        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('页面加载完成，开始初始化...');
            addMessage('system', '正在连接客服系统...');
            
            // 延迟1秒后开始轮询
            setTimeout(() => {
                startPolling();
                addMessage('system', '连接成功！您可以开始对话了。');
            }, 1000);
        });
        
        // 页面卸载时停止轮询
        window.addEventListener('beforeunload', function() {
            if (pollingInterval) {
                clearInterval(pollingInterval);
            }
        });
    </script>
</body>
</html>
