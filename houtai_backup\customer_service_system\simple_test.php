<?php
// 简化的数据库修复和测试页面
header('Content-Type: text/html; charset=UTF-8');
ini_set('default_charset', 'UTF-8');

session_start();

// 检查登录状态
if (!isset($_SESSION['cs_logged_in']) || !$_SESSION['cs_logged_in']) {
    echo '<h1>请先登录客服系统</h1>';
    echo '<a href="../login.php">点击登录</a>';
    exit;
}

require_once '../db_config.php';

echo '<h1>简化修复和测试</h1>';

try {
    $pdo = getDbConnection();
    
    // 执行修复
    if ($_POST['do_fix'] ?? false) {
        echo '<h2>正在执行修复...</h2>';
        
        $success = true;
        
        // 修复1: 添加message字段
        try {
            $pdo->exec("ALTER TABLE realtime_notifications ADD COLUMN message TEXT COMMENT '通知消息内容' AFTER title");
            echo '<p style="color: green;">✓ 成功添加 message 字段</p>';
        } catch (Exception $e) {
            if (strpos($e->getMessage(), 'Duplicate column name') !== false) {
                echo '<p style="color: blue;">message 字段已存在，跳过</p>';
            } else {
                echo '<p style="color: red;">添加 message 字段失败: ' . $e->getMessage() . '</p>';
                $success = false;
            }
        }
        
        // 修复2: 修复content字段
        try {
            $pdo->exec("ALTER TABLE customer_service_messages MODIFY COLUMN content TEXT NULL COMMENT '消息内容'");
            echo '<p style="color: green;">✓ 成功修复 content 字段</p>';
        } catch (Exception $e) {
            echo '<p style="color: red;">修复 content 字段失败: ' . $e->getMessage() . '</p>';
            $success = false;
        }
        
        if ($success) {
            echo '<p style="color: green; font-weight: bold;">✓ 修复完成！</p>';
        }
    }
    
    // 检查表结构
    echo '<h2>当前表结构检查</h2>';
    
    // 检查 realtime_notifications
    $stmt = $pdo->query("SHOW COLUMNS FROM realtime_notifications LIKE 'message'");
    if ($stmt->rowCount() > 0) {
        echo '<p style="color: green;">✓ realtime_notifications.message 字段存在</p>';
    } else {
        echo '<p style="color: red;">✗ realtime_notifications.message 字段不存在</p>';
    }
    
    // 检查 customer_service_messages.content
    $stmt = $pdo->query("SHOW COLUMNS FROM customer_service_messages LIKE 'content'");
    $contentColumn = $stmt->fetch();
    if ($contentColumn) {
        echo '<p style="color: green;">✓ customer_service_messages.content 字段存在</p>';
        echo '<p>字段信息: ' . $contentColumn['Type'] . ', NULL: ' . $contentColumn['Null'] . ', Default: ' . ($contentColumn['Default'] ?? 'NULL') . '</p>';
    } else {
        echo '<p style="color: red;">✗ customer_service_messages.content 字段不存在</p>';
    }
    
    // 测试API
    echo '<h2>API测试</h2>';
    
    $stmt = $pdo->query("SELECT session_id FROM customer_service_sessions WHERE status = 'waiting' LIMIT 1");
    $testSession = $stmt->fetch();
    
    if ($testSession) {
        $testSessionId = $testSession['session_id'];
        echo '<p>测试会话ID: ' . $testSessionId . '</p>';
        echo '<button onclick="testAPI(\'' . $testSessionId . '\')" style="padding: 8px 16px; background: #28a745; color: white; border: none; border-radius: 3px; cursor: pointer;">测试接受API</button>';
        echo '<div id="result"></div>';
    } else {
        echo '<p>没有等待中的会话可供测试</p>';
        echo '<p><a href="create_test_data.php">创建测试数据</a></p>';
    }
    
} catch (Exception $e) {
    echo '<p style="color: red;">数据库错误: ' . $e->getMessage() . '</p>';
}
?>

<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>简化修复和测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        button { margin: 5px; padding: 8px 16px; }
        .fix-btn { background: #dc3545; color: white; border: none; border-radius: 3px; cursor: pointer; }
        pre { background: #f5f5f5; padding: 10px; border-radius: 5px; }
    </style>
</head>
<body>
    <?php if (!($_POST['do_fix'] ?? false)): ?>
    <h2>执行修复</h2>
    <p>这将执行以下操作：</p>
    <ul>
        <li>添加 realtime_notifications.message 字段（如果不存在）</li>
        <li>修复 customer_service_messages.content 字段（设置为允许NULL）</li>
    </ul>
    
    <form method="POST">
        <button type="submit" name="do_fix" value="1" class="fix-btn">执行修复</button>
    </form>
    <?php endif; ?>
    
    <script>
        async function testAPI(sessionId) {
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = '<p>测试中...</p>';
            
            try {
                const response = await fetch('api/accept_session.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        sessionId: sessionId
                    })
                });
                
                const data = await response.json();
                
                if (data.success) {
                    resultDiv.innerHTML = '<h3 style="color: green;">✓ API测试成功！</h3><pre>' + JSON.stringify(data, null, 2) + '</pre>';
                } else {
                    resultDiv.innerHTML = '<h3 style="color: red;">✗ API测试失败</h3><pre>' + JSON.stringify(data, null, 2) + '</pre>';
                }
                
            } catch (error) {
                resultDiv.innerHTML = '<h3 style="color: red;">✗ 网络错误</h3><p>' + error.message + '</p>';
            }
        }
    </script>
    
    <hr>
    <p>
        <a href="sessions.php">返回会话列表</a> | 
        <a href="diagnose.php">完整诊断</a> | 
        <a href="create_test_data.php">创建测试数据</a>
    </p>
</body>
</html>
