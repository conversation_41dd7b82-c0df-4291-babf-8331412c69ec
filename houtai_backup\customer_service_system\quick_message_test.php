<?php
// 快速消息测试
header('Content-Type: text/html; charset=UTF-8');
ini_set('default_charset', 'UTF-8');

session_start();

// 检查登录状态
if (!isset($_SESSION['cs_logged_in']) || !$_SESSION['cs_logged_in']) {
    echo '<h1>请先登录客服系统</h1>';
    exit;
}

require_once '../db_config.php';

echo '<h1>🚀 快速消息测试</h1>';

// 发送测试消息
if ($_POST['send_quick_test'] ?? false) {
    $sessionId = $_POST['session_id'] ?? '';
    $testMessage = '快速测试消息 - ' . date('Y-m-d H:i:s');
    
    if ($sessionId) {
        echo '<h2>📤 发送测试消息</h2>';
        echo '<p><strong>会话ID:</strong> ' . htmlspecialchars($sessionId) . '</p>';
        echo '<p><strong>消息内容:</strong> ' . htmlspecialchars($testMessage) . '</p>';
        
        try {
            $pdo = getDbConnection();
            
            // 获取会话信息
            $stmt = $pdo->prepare("SELECT * FROM customer_service_sessions WHERE session_id = ?");
            $stmt->execute([$sessionId]);
            $session = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if ($session) {
                echo '<p><strong>用户ID:</strong> ' . ($session['user_id'] ?? 'N/A') . '</p>';
                echo '<p><strong>会话状态:</strong> ' . $session['status'] . '</p>';
                
                // 插入消息
                $stmt = $pdo->prepare("
                    INSERT INTO customer_service_messages
                    (session_id, sender_type, sender_id, sender_name, message_type, content, created_at)
                    VALUES (?, 'customer_service', ?, ?, 'text', ?, NOW())
                ");
                $stmt->execute([
                    $sessionId,
                    $_SESSION['cs_user_id'],
                    $_SESSION['cs_name'],
                    $testMessage
                ]);
                
                $messageId = $pdo->lastInsertId();
                echo '<p style="color: green; font-weight: bold;">✅ 消息插入成功！消息ID: ' . $messageId . '</p>';
                
                // 插入通知
                if ($session['user_id']) {
                    $notificationData = [
                        'session_id' => $sessionId,
                        'cs_name' => $_SESSION['cs_name'],
                        'message' => $testMessage,
                        'timestamp' => time()
                    ];
                    
                    $stmt = $pdo->prepare("
                        INSERT INTO realtime_notifications
                        (user_id, type, title, message, data, status, created_at)
                        VALUES (?, 'customer_service_message', '客服回复', ?, ?, 'unread', NOW())
                    ");
                    $stmt->execute([
                        $session['user_id'],
                        '您有新的客服回复消息',
                        json_encode($notificationData, JSON_UNESCAPED_UNICODE)
                    ]);
                    
                    $notificationId = $pdo->lastInsertId();
                    echo '<p style="color: green; font-weight: bold;">✅ 通知插入成功！通知ID: ' . $notificationId . '</p>';
                    
                    // 显示前台测试链接
                    echo '<div style="background: #d4edda; padding: 15px; border-radius: 8px; margin: 10px 0;">';
                    echo '<h4>🎯 前台测试链接</h4>';
                    echo '<p>现在可以在前台测试是否收到消息：</p>';
                    echo '<a href="../../frontend/customer_service/chat.php?session_id=' . urlencode($sessionId) . '" target="_blank" style="background: #6F7BF5; color: white; padding: 8px 16px; text-decoration: none; border-radius: 5px; margin-right: 10px;">打开前台聊天</a>';
                    echo '<a href="../../frontend/customer_service/fix_realtime_communication.php?user_id=' . $session['user_id'] . '" target="_blank" style="background: #28a745; color: white; padding: 8px 16px; text-decoration: none; border-radius: 5px;">测试实时通信</a>';
                    echo '</div>';
                    
                } else {
                    echo '<p style="color: orange;">⚠️ 会话没有用户ID，无法发送通知</p>';
                }
                
            } else {
                echo '<p style="color: red;">❌ 会话不存在</p>';
            }
            
        } catch (Exception $e) {
            echo '<p style="color: red;">❌ 发送失败: ' . $e->getMessage() . '</p>';
        }
    }
}

try {
    $pdo = getDbConnection();
    
    // 获取进行中的会话
    $stmt = $pdo->query("
        SELECT session_id, user_name, user_id, status, message_count, updated_at
        FROM customer_service_sessions 
        WHERE status = 'active' 
        ORDER BY updated_at DESC 
        LIMIT 3
    ");
    $activeSessions = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (!empty($activeSessions)) {
        echo '<h2>📋 选择会话发送测试消息</h2>';
        
        foreach ($activeSessions as $session) {
            echo '<div style="border: 1px solid #ddd; padding: 15px; margin: 10px 0; border-radius: 8px; background: #f9f9f9;">';
            echo '<h4>会话: ' . htmlspecialchars($session['session_id']) . '</h4>';
            echo '<p><strong>用户:</strong> ' . htmlspecialchars($session['user_name']) . '</p>';
            echo '<p><strong>用户ID:</strong> ' . htmlspecialchars($session['user_id'] ?? 'N/A') . '</p>';
            echo '<p><strong>状态:</strong> ' . htmlspecialchars($session['status']) . '</p>';
            echo '<p><strong>消息数量:</strong> ' . htmlspecialchars($session['message_count']) . '</p>';
            echo '<p><strong>更新时间:</strong> ' . htmlspecialchars($session['updated_at']) . '</p>';
            
            // 快速发送按钮
            echo '<form method="POST" style="margin-top: 10px;">';
            echo '<input type="hidden" name="session_id" value="' . htmlspecialchars($session['session_id']) . '">';
            echo '<button type="submit" name="send_quick_test" value="1" style="padding: 8px 16px; background: #28a745; color: white; border: none; border-radius: 5px; cursor: pointer; font-size: 14px;">🚀 发送测试消息</button>';
            echo '</form>';
            
            echo '</div>';
        }
        
    } else {
        echo '<div style="background: #fff3cd; padding: 15px; border-radius: 8px; margin: 10px 0;">';
        echo '<h3>⚠️ 没有进行中的会话</h3>';
        echo '<p>需要先接受一个会话才能发送消息</p>';
        echo '<a href="sessions.php" style="background: #007cba; color: white; padding: 8px 16px; text-decoration: none; border-radius: 5px;">返回会话列表</a>';
        echo '</div>';
    }
    
} catch (Exception $e) {
    echo '<div style="background: #f8d7da; padding: 15px; border-radius: 8px; margin: 10px 0; color: #721c24;">';
    echo '<h3>❌ 错误</h3>';
    echo '<p>' . htmlspecialchars($e->getMessage()) . '</p>';
    echo '</div>';
}
?>

<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>快速消息测试</title>
    <style>
        body { 
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; 
            margin: 20px; 
            background: #f5f5f5; 
        }
        .container { 
            max-width: 800px; 
            margin: 0 auto; 
            background: white; 
            padding: 20px; 
            border-radius: 10px; 
            box-shadow: 0 2px 10px rgba(0,0,0,0.1); 
        }
    </style>
</head>
<body>
    <div class="container">
        <div style="background: #e7f3ff; padding: 15px; border-radius: 8px; margin: 10px 0;">
            <h3>📝 测试说明</h3>
            <p><strong>目的：</strong>快速测试消息发送和前台接收功能</p>
            <p><strong>步骤：</strong></p>
            <ol>
                <li>点击"发送测试消息"按钮</li>
                <li>确认消息和通知插入成功</li>
                <li>点击"打开前台聊天"链接</li>
                <li>在前台页面查看是否收到消息</li>
            </ol>
        </div>
        
        <p style="margin-top: 20px;">
            <a href="sessions.php">返回会话列表</a> | 
            <a href="test_message_flow.php">详细测试</a> | 
            <a href="../../frontend/customer_service/fix_realtime_communication.php">前台通信测试</a>
        </p>
    </div>
</body>
</html>
