/* 趣玩星球 - 用户等级勋章样式 */

/* 基础勋章容器 */
.badge-container {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    position: relative;
    margin: 10px;
}

/* 勋章基础样式 */
.level-badge {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    color: white;
    text-shadow: 0 1px 2px rgba(0,0,0,0.5);
    transition: all 0.3s ease;
    cursor: pointer;
}

.level-badge:hover {
    transform: scale(1.1);
}

/* 简单勋章 - Lv.1 见习玩家 */
.badge-level-1 {
    width: 48px;
    height: 48px;
    border-radius: 50%;
    background: linear-gradient(135deg, #9CA3AF, #6B7280);
    border: 2px solid #E5E7EB;
    box-shadow: 
        0 2px 8px rgba(0,0,0,0.15),
        inset 0 1px 2px rgba(255,255,255,0.2);
    font-size: 12px;
}

.badge-level-1::before {
    content: "1";
    font-size: 16px;
    font-weight: 800;
}

/* 中等复杂勋章 - Lv.5 资深玩咖 */
.badge-level-5 {
    width: 48px;
    height: 48px;
    border-radius: 50%;
    background: linear-gradient(135deg, #8B5CF6, #6366F1, #3B82F6);
    border: 3px solid #A855F7;
    box-shadow: 
        0 4px 15px rgba(139, 92, 246, 0.4),
        inset 0 2px 4px rgba(255,255,255,0.3),
        inset 0 -2px 4px rgba(0,0,0,0.2);
    position: relative;
    overflow: hidden;
}

.badge-level-5::before {
    content: "⭐";
    font-size: 20px;
    z-index: 2;
    position: relative;
}

.badge-level-5::after {
    content: "";
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: linear-gradient(45deg, transparent, rgba(255,255,255,0.1), transparent);
    animation: shine 3s infinite;
    z-index: 1;
}

/* 复杂勋章 - Lv.10 传奇玩咖 */
.badge-level-10 {
    width: 56px;
    height: 56px;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* 主体六边形 */
.badge-level-10::before {
    content: "";
    position: absolute;
    width: 48px;
    height: 48px;
    background: linear-gradient(45deg, #FFD700, #FFA500, #FF6B35, #FF6B35, #FFA500, #FFD700);
    clip-path: polygon(50% 0%, 100% 25%, 100% 75%, 50% 100%, 0% 75%, 0% 25%);
    box-shadow: 
        0 0 20px rgba(255, 215, 0, 0.6),
        inset 0 2px 4px rgba(255,255,255,0.4),
        inset 0 -2px 4px rgba(0,0,0,0.3);
    animation: glow 2s ease-in-out infinite alternate;
}

/* 中心图标 */
.badge-level-10::after {
    content: "👑";
    font-size: 24px;
    z-index: 3;
    position: relative;
    filter: drop-shadow(0 2px 4px rgba(0,0,0,0.3));
}

/* 外围光环 */
.badge-level-10 .ring {
    position: absolute;
    width: 60px;
    height: 60px;
    border: 2px solid transparent;
    border-top: 2px solid rgba(255, 215, 0, 0.8);
    border-right: 2px solid rgba(255, 215, 0, 0.6);
    border-radius: 50%;
    animation: rotate 4s linear infinite;
}

.badge-level-10 .ring:nth-child(2) {
    width: 70px;
    height: 70px;
    border-top: 2px solid rgba(255, 165, 0, 0.6);
    border-left: 2px solid rgba(255, 165, 0, 0.4);
    animation: rotate 6s linear infinite reverse;
}

/* 粒子效果 */
.badge-level-10 .particle {
    position: absolute;
    width: 4px;
    height: 4px;
    background: #FFD700;
    border-radius: 50%;
    animation: float 3s ease-in-out infinite;
}

.badge-level-10 .particle:nth-child(3) {
    top: 10px;
    left: 20px;
    animation-delay: 0s;
}

.badge-level-10 .particle:nth-child(4) {
    top: 20px;
    right: 10px;
    animation-delay: 1s;
}

.badge-level-10 .particle:nth-child(5) {
    bottom: 15px;
    left: 15px;
    animation-delay: 2s;
}

/* 动画效果 */
@keyframes shine {
    0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
    100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
}

@keyframes glow {
    0% { 
        box-shadow: 
            0 0 20px rgba(255, 215, 0, 0.6),
            inset 0 2px 4px rgba(255,255,255,0.4),
            inset 0 -2px 4px rgba(0,0,0,0.3);
    }
    100% { 
        box-shadow: 
            0 0 30px rgba(255, 215, 0, 0.9),
            0 0 40px rgba(255, 165, 0, 0.6),
            inset 0 2px 4px rgba(255,255,255,0.4),
            inset 0 -2px 4px rgba(0,0,0,0.3);
    }
}

@keyframes rotate {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

@keyframes float {
    0%, 100% { 
        transform: translateY(0px) scale(1);
        opacity: 0.7;
    }
    50% { 
        transform: translateY(-10px) scale(1.2);
        opacity: 1;
    }
}

/* 勋章尺寸变体 */
.badge-small {
    transform: scale(0.7);
}

.badge-medium {
    transform: scale(1);
}

.badge-large {
    transform: scale(1.3);
}

/* 勋章标签 */
.badge-label {
    margin-top: 8px;
    font-size: 12px;
    color: #6B7280;
    text-align: center;
    font-weight: 600;
}
