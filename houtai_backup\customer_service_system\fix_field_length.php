<?php
// 修复数据库字段长度问题
header('Content-Type: text/html; charset=UTF-8');
ini_set('default_charset', 'UTF-8');

session_start();

// 检查登录状态
if (!isset($_SESSION['cs_logged_in']) || !$_SESSION['cs_logged_in']) {
    echo '<h1>请先登录客服系统</h1>';
    exit;
}

require_once '../db_config.php';

echo '<h1>🔧 修复数据库字段长度</h1>';

if ($_POST['fix_fields'] ?? false) {
    echo '<h2>正在修复字段长度...</h2>';
    
    try {
        $pdo = getDbConnection();
        
        $fixes = [
            // realtime_notifications 表
            "ALTER TABLE realtime_notifications MODIFY COLUMN type VARCHAR(100) NOT NULL COMMENT '通知类型'",
            "ALTER TABLE realtime_notifications MODIFY COLUMN title VARCHAR(255) NOT NULL COMMENT '通知标题'",
            "ALTER TABLE realtime_notifications MODIFY COLUMN data TEXT NULL COMMENT '通知数据'",
            
            // customer_service_messages 表
            "ALTER TABLE customer_service_messages MODIFY COLUMN sender_type VARCHAR(50) NULL DEFAULT 'user' COMMENT '发送者类型'",
            "ALTER TABLE customer_service_messages MODIFY COLUMN sender_name VARCHAR(100) NULL DEFAULT '' COMMENT '发送者姓名'",
            "ALTER TABLE customer_service_messages MODIFY COLUMN message_type VARCHAR(50) NULL DEFAULT 'text' COMMENT '消息类型'",
            "ALTER TABLE customer_service_messages MODIFY COLUMN content TEXT NULL COMMENT '消息内容'",
            "ALTER TABLE customer_service_messages MODIFY COLUMN attachment_url TEXT NULL COMMENT '附件URL'",
            "ALTER TABLE customer_service_messages MODIFY COLUMN attachment_type VARCHAR(50) NULL COMMENT '附件类型'",
            
            // customer_service_sessions 表
            "ALTER TABLE customer_service_sessions MODIFY COLUMN session_id VARCHAR(255) NOT NULL COMMENT '会话ID'",
            "ALTER TABLE customer_service_sessions MODIFY COLUMN user_name VARCHAR(100) NULL DEFAULT '用户' COMMENT '用户名称'",
            "ALTER TABLE customer_service_sessions MODIFY COLUMN priority VARCHAR(20) NULL DEFAULT 'normal' COMMENT '优先级'",
            "ALTER TABLE customer_service_sessions MODIFY COLUMN category VARCHAR(50) NULL COMMENT '分类'",
            "ALTER TABLE customer_service_sessions MODIFY COLUMN source VARCHAR(50) NULL DEFAULT 'web' COMMENT '来源'",
            "ALTER TABLE customer_service_sessions MODIFY COLUMN status VARCHAR(20) NOT NULL DEFAULT 'waiting' COMMENT '状态'"
        ];
        
        $successCount = 0;
        $errorCount = 0;
        
        foreach ($fixes as $sql) {
            try {
                $pdo->exec($sql);
                $successCount++;
                echo '<p style="color: green;">✓ ' . substr($sql, 0, 80) . '...</p>';
            } catch (Exception $e) {
                $errorCount++;
                echo '<p style="color: red;">✗ ' . substr($sql, 0, 80) . '... 错误: ' . $e->getMessage() . '</p>';
            }
        }
        
        echo '<h3>修复完成</h3>';
        echo '<p>成功: ' . $successCount . ' 条</p>';
        echo '<p>失败: ' . $errorCount . ' 条</p>';
        
        // 尝试添加 message 字段（如果不存在）
        try {
            $pdo->exec("ALTER TABLE realtime_notifications ADD COLUMN message TEXT NULL COMMENT '通知消息内容' AFTER title");
            echo '<p style="color: green;">✓ 成功添加 message 字段</p>';
        } catch (Exception $e) {
            if (strpos($e->getMessage(), 'Duplicate column name') !== false) {
                echo '<p style="color: blue;">message 字段已存在</p>';
            } else {
                echo '<p style="color: orange;">添加 message 字段失败: ' . $e->getMessage() . '</p>';
            }
        }
        
    } catch (Exception $e) {
        echo '<p style="color: red;">数据库连接失败: ' . $e->getMessage() . '</p>';
    }
}

// 显示当前表结构
try {
    $pdo = getDbConnection();
    
    echo '<h2>当前表结构</h2>';
    
    $tables = ['realtime_notifications', 'customer_service_messages', 'customer_service_sessions'];
    
    foreach ($tables as $table) {
        echo '<h3>' . $table . '</h3>';
        try {
            $stmt = $pdo->query("DESCRIBE $table");
            $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            echo '<table border="1" style="border-collapse: collapse; margin: 10px 0;">';
            echo '<tr><th>字段名</th><th>类型</th><th>是否为空</th><th>默认值</th><th>注释</th></tr>';
            
            foreach ($columns as $column) {
                $typeColor = 'black';
                if (strpos($column['Type'], 'varchar') !== false) {
                    $length = preg_match('/varchar\((\d+)\)/', $column['Type'], $matches);
                    if ($length && $matches[1] < 50) {
                        $typeColor = 'red'; // 标记可能太短的字段
                    }
                }
                
                echo '<tr>';
                echo '<td><strong>' . htmlspecialchars($column['Field']) . '</strong></td>';
                echo '<td style="color: ' . $typeColor . ';">' . htmlspecialchars($column['Type']) . '</td>';
                echo '<td>' . htmlspecialchars($column['Null']) . '</td>';
                echo '<td>' . htmlspecialchars($column['Default'] ?? 'NULL') . '</td>';
                echo '<td>' . htmlspecialchars($column['Comment'] ?? '') . '</td>';
                echo '</tr>';
            }
            echo '</table>';
            
        } catch (Exception $e) {
            echo '<p style="color: red;">获取 ' . $table . ' 表结构失败: ' . $e->getMessage() . '</p>';
        }
    }
    
} catch (Exception $e) {
    echo '<p style="color: red;">数据库连接失败: ' . $e->getMessage() . '</p>';
}
?>

<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>修复字段长度</title>
    <style>
        body { 
            font-family: Arial, sans-serif; 
            margin: 20px; 
            background: #f5f5f5; 
        }
        .container { 
            max-width: 1000px; 
            margin: 0 auto; 
            background: white; 
            padding: 20px; 
            border-radius: 10px; 
            box-shadow: 0 2px 10px rgba(0,0,0,0.1); 
        }
        table { 
            width: 100%; 
            margin: 10px 0; 
        }
        th, td { 
            padding: 8px; 
            text-align: left; 
        }
        th { 
            background-color: #f2f2f2; 
        }
        .fix-btn { 
            background: #dc3545; 
            color: white; 
            border: none; 
            padding: 12px 24px; 
            border-radius: 5px; 
            cursor: pointer; 
            font-size: 16px; 
        }
        .warning { 
            background: #fff3cd; 
            padding: 15px; 
            border-radius: 5px; 
            margin: 10px 0; 
            border-left: 4px solid #ffc107; 
        }
    </style>
</head>
<body>
    <div class="container">
        <?php if (!($_POST['fix_fields'] ?? false)): ?>
        <div class="warning">
            <h3>⚠️ 字段长度问题</h3>
            <p>错误信息显示数据库字段长度不足，需要扩展以下字段：</p>
            <ul>
                <li><strong>type</strong> 字段：从 VARCHAR(20) 扩展到 VARCHAR(100)</li>
                <li><strong>title</strong> 字段：扩展到 VARCHAR(255)</li>
                <li><strong>其他字段</strong>：确保足够的长度存储数据</li>
            </ul>
            <p><strong>这个操作是安全的，不会丢失数据。</strong></p>
        </div>
        
        <form method="POST">
            <button type="submit" name="fix_fields" value="1" class="fix-btn">修复字段长度</button>
        </form>
        <?php endif; ?>
        
        <p style="margin-top: 20px;">
            <a href="sessions.php">返回会话列表</a> | 
            <a href="test_message_flow.php">测试消息流程</a>
        </p>
    </div>
</body>
</html>
