<?php
// 发布活动接口（从草稿状态发布）
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// 处理OPTIONS请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

// 引入session配置
require_once '../../../sql/session_config.php';
initLoginSession();

// 检查用户是否已登录
if (!isUserLoggedIn()) {
    echo json_encode([
        'success' => false,
        'message' => '请先登录后再操作'
    ]);
    exit;
}

// 检查请求方法
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode([
        'success' => false,
        'message' => '请求方法错误'
    ]);
    exit;
}

// 获取POST数据
$input = json_decode(file_get_contents('php://input'), true);
if (!$input || !isset($input['activity_id'])) {
    echo json_encode([
        'success' => false,
        'message' => '参数错误'
    ]);
    exit;
}

$activity_id = intval($input['activity_id']);
$user_id = $_SESSION['user_id'];

// 数据库连接配置
$db_config = [
    'host' => 'localhost',
    'username' => 'quwanplanet',
    'password' => 'nJmJm23FB4Xn6Fc3',
    'dbname' => 'quwanplanet',
    'port' => 3306,
    'charset' => 'utf8mb4'
];

try {
    $dsn = "mysql:host={$db_config['host']};dbname={$db_config['dbname']};charset={$db_config['charset']};port={$db_config['port']}";
    $pdo = new PDO($dsn, $db_config['username'], $db_config['password']);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

    // 检查表是否存在
    $stmt = $pdo->query("SHOW TABLES LIKE 'camping_activities'");
    if ($stmt->rowCount() == 0) {
        echo json_encode([
            'success' => false,
            'message' => '活动系统暂未开放'
        ]);
        exit;
    }

    // 开始事务
    $pdo->beginTransaction();

    // 检查活动是否存在且属于当前用户
    $check_sql = "SELECT id, status, organizer_id FROM camping_activities WHERE id = ? AND organizer_id = ?";
    $check_stmt = $pdo->prepare($check_sql);
    $check_stmt->execute([$activity_id, $user_id]);
    $activity = $check_stmt->fetch(PDO::FETCH_ASSOC);

    if (!$activity) {
        $pdo->rollBack();
        echo json_encode([
            'success' => false,
            'message' => '活动不存在或无权限操作'
        ]);
        exit;
    }

    if ($activity['status'] !== 'draft') {
        $pdo->rollBack();
        echo json_encode([
            'success' => false,
            'message' => '只能发布草稿状态的活动'
        ]);
        exit;
    }

    // 更新活动状态为招募中
    $update_sql = "UPDATE camping_activities SET status = 'recruiting', updated_at = NOW() WHERE id = ?";
    $update_stmt = $pdo->prepare($update_sql);
    $update_stmt->execute([$activity_id]);

    // 检查组局者是否已经参加活动
    $participant_check_sql = "SELECT id FROM camping_participants WHERE activity_id = ? AND user_id = ?";
    $participant_check_stmt = $pdo->prepare($participant_check_sql);
    $participant_check_stmt->execute([$activity_id, $user_id]);

    if (!$participant_check_stmt->fetch()) {
        // 自动让组局者参加活动
        $participant_sql = "INSERT INTO camping_participants (
            activity_id, user_id, join_time, status, payment_status, actual_price
        ) VALUES (?, ?, NOW(), 'joined', 'paid', 0)";
        
        $participant_stmt = $pdo->prepare($participant_sql);
        $participant_stmt->execute([$activity_id, $user_id]);

        // 更新当前参与人数
        $update_participants_sql = "UPDATE camping_activities SET current_participants = current_participants + 1 WHERE id = ?";
        $update_participants_stmt = $pdo->prepare($update_participants_sql);
        $update_participants_stmt->execute([$activity_id]);
    }

    // 提交事务
    $pdo->commit();

    echo json_encode([
        'success' => true,
        'message' => '活动发布成功，开始招募参与者！'
    ]);

} catch (PDOException $e) {
    // 回滚事务
    if ($pdo->inTransaction()) {
        $pdo->rollBack();
    }
    
    error_log("发布活动错误: " . $e->getMessage());
    echo json_encode([
        'success' => false,
        'message' => '系统错误，请稍后重试'
    ]);
}
?>
