<?php
/**
 * 权限申请处理脚本
 * 趣玩星球管理后台
 */

session_start();

// 设置响应头
header('Content-Type: application/json; charset=utf-8');

// 登录检查
if (!isset($_SESSION['admin_id'])) {
    echo json_encode(['success' => false, 'message' => '请先登录']);
    exit;
}

// 检查请求方法
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode(['success' => false, 'message' => '请求方法错误']);
    exit;
}

$admin_id = $_SESSION['admin_id'];
$admin_name = $_SESSION['admin_name'] ?? '管理员';

// 获取POST数据
$permission_id = $_POST['permission_id'] ?? '';
$reason = trim($_POST['reason'] ?? '');

// 验证输入
if (empty($permission_id) || empty($reason)) {
    echo json_encode(['success' => false, 'message' => '请填写完整的申请信息']);
    exit;
}

if (strlen($reason) < 10) {
    echo json_encode(['success' => false, 'message' => '申请理由至少需要10个字符']);
    exit;
}

try {
    require_once '../db_config.php';
    $pdo = getDbConnection();
    
    // 检查权限是否存在
    $stmt = $pdo->prepare("SELECT id, name FROM permissions WHERE id = ?");
    $stmt->execute([$permission_id]);
    $permission = $stmt->fetch();
    
    if (!$permission) {
        echo json_encode(['success' => false, 'message' => '申请的权限不存在']);
        exit;
    }
    
    // 检查是否已经拥有该权限
    $stmt = $pdo->prepare("
        SELECT 1 FROM user_permissions WHERE user_id = ? AND permission_id = ?
        UNION
        SELECT 1 FROM user_roles ur 
        JOIN role_permissions rp ON ur.role_id = rp.role_id 
        WHERE ur.user_id = ? AND rp.permission_id = ?
        LIMIT 1
    ");
    $stmt->execute([$admin_id, $permission_id, $admin_id, $permission_id]);
    
    if ($stmt->fetch()) {
        echo json_encode(['success' => false, 'message' => '您已经拥有该权限']);
        exit;
    }
    
    // 检查是否已经申请过该权限（待审核状态）
    $stmt = $pdo->prepare("
        SELECT id FROM permission_requests 
        WHERE user_id = ? AND permission_id = ? AND status = 'pending'
    ");
    $stmt->execute([$admin_id, $permission_id]);
    
    if ($stmt->fetch()) {
        echo json_encode(['success' => false, 'message' => '您已经申请过该权限，请等待审核']);
        exit;
    }
    
    // 插入权限申请记录
    $stmt = $pdo->prepare("
        INSERT INTO permission_requests (user_id, permission_id, reason, status, created_at)
        VALUES (?, ?, ?, 'pending', NOW())
    ");
    
    $result = $stmt->execute([$admin_id, $permission_id, $reason]);
    
    if ($result) {
        $request_id = $pdo->lastInsertId();
        
        // 记录操作日志（如果有日志表的话）
        try {
            $stmt = $pdo->prepare("
                INSERT INTO admin_logs (admin_id, action, details, ip_address, created_at)
                VALUES (?, 'permission_apply', ?, ?, NOW())
            ");
            $details = "申请权限: {$permission['name']} (ID: {$permission_id})";
            $ip_address = $_SERVER['REMOTE_ADDR'] ?? '';
            $stmt->execute([$admin_id, $details, $ip_address]);
        } catch (Exception $e) {
            // 日志记录失败不影响主要功能
        }
        
        echo json_encode([
            'success' => true, 
            'message' => '权限申请提交成功！您的申请将在1-2个工作日内得到处理。',
            'data' => [
                'request_id' => $request_id,
                'permission_name' => $permission['name'],
                'status' => 'pending'
            ]
        ]);
    } else {
        echo json_encode(['success' => false, 'message' => '申请提交失败，请重试']);
    }
    
} catch (PDOException $e) {
    error_log("Permission apply error: " . $e->getMessage());
    echo json_encode(['success' => false, 'message' => '数据库错误，请联系管理员']);
} catch (Exception $e) {
    error_log("Permission apply error: " . $e->getMessage());
    echo json_encode(['success' => false, 'message' => '系统错误，请重试']);
}
?>
