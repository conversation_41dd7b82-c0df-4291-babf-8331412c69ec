<?php
session_start();

// 数据库连接配置
$db_config = [
    'host' => 'localhost',
    'username' => 'quwanplanet',
    'password' => 'nJmJm23FB4Xn6Fc3',
    'dbname' => 'quwanplanet',
    'port' => 3306,
    'charset' => 'utf8mb4'
];

// 错误信息
$login_error = '';
$register_error = '';

// 如果用户已登录，重定向到首页
if (isset($_SESSION['user_id'])) {
    header('Location: ../../index.php');
    exit;
}

// 检查"记住我"Cookie
if (!isset($_SESSION['user_id']) && isset($_COOKIE['remember_token'])) {
    try {
        $dsn = "mysql:host={$db_config['host']};dbname={$db_config['dbname']};charset={$db_config['charset']};port={$db_config['port']}";
        $pdo = new PDO($dsn, $db_config['username'], $db_config['password']);
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

        $token = $_COOKIE['remember_token'];
        $ip = $_SERVER['REMOTE_ADDR'];
        $user_agent = $_SERVER['HTTP_USER_AGENT'];

        // 查询有效的会话
        $stmt = $pdo->prepare("SELECT us.user_id, u.username, u.quwan_id
                              FROM user_sessions us
                              JOIN users u ON us.user_id = u.id
                              WHERE us.token = :token
                              AND us.expires_at > NOW()");
        $stmt->execute(['token' => $token]);
        $session = $stmt->fetch(PDO::FETCH_ASSOC);

        if ($session) {
            // 自动登录用户
            $_SESSION['user_id'] = $session['user_id'];
            $_SESSION['username'] = $session['username'];
            $_SESSION['quwan_id'] = $session['quwan_id'];

            // 更新最后登录时间
            $stmt = $pdo->prepare("UPDATE users SET last_login = NOW() WHERE id = :id");
            $stmt->execute(['id' => $session['user_id']]);

            // 记录登录日志
            $stmt = $pdo->prepare("INSERT INTO login_logs (user_id, login_time, ip_address, user_agent, status) VALUES (:user_id, NOW(), :ip_address, :user_agent, 'success')");
            $stmt->execute([
                'user_id' => $session['user_id'],
                'ip_address' => $ip,
                'user_agent' => $user_agent
            ]);

            // 重定向到新首页
            header('Location: ../home/<USER>');
            exit;
        } else {
            // 无效的令牌，删除Cookie
            setcookie('remember_token', '', time() - 3600, '/', '', false, true);
        }
    } catch (PDOException $e) {
        error_log("自动登录错误: " . $e->getMessage());
        // 删除可能导致问题的Cookie
        setcookie('remember_token', '', time() - 3600, '/', '', false, true);
    }
}



// 处理登录请求
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action']) && $_POST['action'] === 'login') {
    try {
        $dsn = "mysql:host={$db_config['host']};dbname={$db_config['dbname']};charset={$db_config['charset']};port={$db_config['port']}";
        $pdo = new PDO($dsn, $db_config['username'], $db_config['password']);
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

        $username = $_POST['username'] ?? '';
        $password = $_POST['password'] ?? '';
        $remember = isset($_POST['remember']) ? true : false;

        // 查询用户（包含状态字段）
        $stmt = $pdo->prepare("SELECT id, username, password, quwan_id, status FROM users WHERE username = :username OR email = :username OR phone = :username OR quwan_id = :username");
        $stmt->execute(['username' => $username]);
        $user = $stmt->fetch(PDO::FETCH_ASSOC);

        if ($user) {
            // 检查用户状态
            if ($user['status'] === 'banned') {
                $login_error = '您的账号已被封禁，无法登录';
                $_SESSION['login_error'] = $login_error;
                header('Location: index.php?login_failed=1');
                exit;
            }

            // 检查是否有有效的封号记录（如果表存在）
            try {
                $stmt = $pdo->prepare("
                    SELECT * FROM user_bans
                    WHERE user_id = ? AND status = 'active'
                    AND (expire_at IS NULL OR expire_at > NOW())
                ");
                $stmt->execute([$user['id']]);
                $active_ban = $stmt->fetch(PDO::FETCH_ASSOC);

                if ($active_ban) {
                    $ban_message = '您的账号已被封禁';
                    if ($active_ban['expire_at']) {
                        $ban_message .= '，解封时间：' . date('Y-m-d H:i:s', strtotime($active_ban['expire_at']));
                    } else {
                        $ban_message .= '（永久封禁）';
                    }
                    $ban_message .= '，原因：' . $active_ban['reason'];

                    $_SESSION['login_error'] = $ban_message;
                    header('Location: index.php?login_failed=1');
                    exit;
                }
            } catch (PDOException $e) {
                // user_bans表不存在，跳过封号检查
                error_log("user_bans表不存在: " . $e->getMessage());
            }

            // 验证密码
            if (password_verify($password, $user['password'])) {
                // 登录成功
                $_SESSION['user_id'] = $user['id'];
                $_SESSION['username'] = $user['username'];
                $_SESSION['quwan_id'] = $user['quwan_id'];
                $_SESSION['login_success'] = true; // 设置登录成功标志

                // 记录登录日志
                $ip = $_SERVER['REMOTE_ADDR'];
                $user_agent = $_SERVER['HTTP_USER_AGENT'];
                $stmt = $pdo->prepare("INSERT INTO login_logs (user_id, login_time, ip_address, user_agent, status) VALUES (:user_id, NOW(), :ip_address, :user_agent, 'success')");
                $stmt->execute([
                    'user_id' => $user['id'],
                    'ip_address' => $ip,
                    'user_agent' => $user_agent
                ]);

                // 更新最后登录时间
                $stmt = $pdo->prepare("UPDATE users SET last_login = NOW() WHERE id = :id");
                $stmt->execute(['id' => $user['id']]);

                // 处理"记住我"功能
                if ($remember) {
                    // 生成唯一令牌
                    $token = bin2hex(random_bytes(32));
                    $expires = date('Y-m-d H:i:s', strtotime('+30 days'));

                    // 存储令牌到数据库
                    $stmt = $pdo->prepare("INSERT INTO user_sessions (user_id, token, ip_address, user_agent, created_at, expires_at) VALUES (:user_id, :token, :ip_address, :user_agent, NOW(), :expires_at)");
                    $stmt->execute([
                        'user_id' => $user['id'],
                        'token' => $token,
                        'ip_address' => $ip,
                        'user_agent' => $user_agent,
                        'expires_at' => $expires
                    ]);

                    // 设置Cookie
                    setcookie('remember_token', $token, strtotime('+30 days'), '/', '', false, true);
                }

                // 设置登录成功标志
                $_SESSION['login_success'] = true;
                // 重定向到登录页面显示成功消息，然后跳转
                header('Location: index.php?login_success=1');
                exit;
            } else {
                // 记录失败登录
                if ($user) {
                    $ip = $_SERVER['REMOTE_ADDR'];
                    $user_agent = $_SERVER['HTTP_USER_AGENT'];
                    $stmt = $pdo->prepare("INSERT INTO login_logs (user_id, login_time, ip_address, user_agent, status) VALUES (:user_id, NOW(), :ip_address, :user_agent, 'failed')");
                    $stmt->execute([
                        'user_id' => $user['id'],
                        'ip_address' => $ip,
                        'user_agent' => $user_agent
                    ]);
                }

                $login_error = '用户名或密码错误';
                // 设置会话变量，用于显示错误提示
                $_SESSION['login_error'] = $login_error;
                // 重定向回登录页面，带上错误标志
                header('Location: index.php?login_failed=1');
                exit;
            }
        } else {
            $login_error = '用户名或密码错误';
            $_SESSION['login_error'] = $login_error;
            header('Location: index.php?login_failed=1');
            exit;
        }
    } catch (PDOException $e) {
        $login_error = '登录失败，请稍后再试';
        error_log("登录错误: " . $e->getMessage());
        // 设置会话变量，用于显示错误提示
        $_SESSION['login_error'] = $login_error;
        // 重定向回登录页面，带上错误标志
        header('Location: index.php?login_failed=1');
        exit;
    }
}

// 处理注册请求
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action']) && $_POST['action'] === 'register') {
    try {
        // 验证协议勾选
        $agreement = isset($_POST['register_agreement']) ? true : false;
        if (!$agreement) {
            $register_error = '请阅读并同意用户协议和隐私政策';
            // 设置会话变量，用于显示错误提示
            $_SESSION['register_error'] = $register_error;
            // 重定向回注册页面，带上错误标志
            header('Location: index.php?register_failed=1&tab=register');
            exit;
        }
        // 验证验证码
        else {
            $captcha = isset($_POST['captcha']) ? strtoupper(trim($_POST['captcha'])) : '';
            $session_captcha = isset($_SESSION['captcha']) ? $_SESSION['captcha'] : '';

            if (empty($captcha) || empty($session_captcha) || $captcha !== $session_captcha) {
                $register_error = '验证码错误，请重新输入';
                // 设置会话变量，用于显示错误提示
                $_SESSION['register_error'] = $register_error;
                // 重定向回注册页面，带上错误标志
                header('Location: index.php?register_failed=1&tab=register');
                exit;
            } else {
                // 验证码正确，清除会话中的验证码（一次性使用）
                unset($_SESSION['captcha']);

                // 连接数据库
                $dsn = "mysql:host={$db_config['host']};dbname={$db_config['dbname']};charset={$db_config['charset']};port={$db_config['port']}";
                $pdo = new PDO($dsn, $db_config['username'], $db_config['password']);
                $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

                // 获取表单数据
                $username = $_POST['reg_username'] ?? '';
                $phone = $_POST['reg_phone'] ?? '';
                $email = $_POST['reg_email'] ?? '';
                $password = $_POST['reg_password'] ?? '';

                // 检查用户名、手机号或邮箱是否已存在
                $stmt = $pdo->prepare("SELECT COUNT(*) FROM users WHERE username = :username OR phone = :phone OR email = :email");
                $stmt->execute([
                    'username' => $username,
                    'phone' => $phone,
                    'email' => $email
                ]);

                if ($stmt->fetchColumn() > 0) {
                    $register_error = '用户名、手机号或邮箱已被注册';
                    // 设置会话变量，用于显示错误提示
                    $_SESSION['register_error'] = $register_error;
                    // 重定向回注册页面，带上错误标志
                    header('Location: index.php?register_failed=1&tab=register');
                    exit;
                } else {
                    // 生成唯一的7位趣玩ID（不以0开头）
                    $quwan_id = '';
                    do {
                        $quwan_id = mt_rand(1000000, 9999999);
                        $stmt = $pdo->prepare("SELECT COUNT(*) FROM users WHERE quwan_id = :quwan_id");
                        $stmt->execute(['quwan_id' => $quwan_id]);
                    } while ($stmt->fetchColumn() > 0);

                    // 插入新用户
                    $stmt = $pdo->prepare("INSERT INTO users (username, phone, email, password, quwan_id, created_at) VALUES (:username, :phone, :email, :password, :quwan_id, NOW())");
                    $stmt->execute([
                        'username' => $username,
                        'phone' => $phone,
                        'email' => $email,
                        'password' => password_hash($password, PASSWORD_DEFAULT),
                        'quwan_id' => $quwan_id
                    ]);

                    // 获取新用户ID
                    $user_id = $pdo->lastInsertId();

                    // 登录用户
                    $_SESSION['user_id'] = $user_id;
                    $_SESSION['username'] = $username;
                    $_SESSION['quwan_id'] = $quwan_id;
                    $_SESSION['register_success'] = true; // 设置注册成功标志

                    // 记录登录日志
                    $ip = $_SERVER['REMOTE_ADDR'];
                    $user_agent = $_SERVER['HTTP_USER_AGENT'];
                    $stmt = $pdo->prepare("INSERT INTO login_logs (user_id, login_time, ip_address, user_agent, status) VALUES (:user_id, NOW(), :ip_address, :user_agent, 'success')");
                    $stmt->execute([
                        'user_id' => $user_id,
                        'ip_address' => $ip,
                        'user_agent' => $user_agent
                    ]);

                    // 重定向到引导页
                    header('Location: ../onboarding/index.php');
                    exit;
                }
            }
        }
    } catch (PDOException $e) {
        $register_error = '注册失败，请稍后再试';
        error_log("注册错误: " . $e->getMessage());
        // 设置会话变量，用于显示错误提示
        $_SESSION['register_error'] = $register_error;
        // 重定向回注册页面，带上错误标志
        header('Location: index.php?register_failed=1&tab=register');
        exit;
    }
}
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0, user-scalable=no, shrink-to-fit=no">
    <meta name="theme-color" content="#0a0a2e"> <!-- 设置状态栏颜色为深蓝色，与背景匹配 -->
    <meta name="apple-mobile-web-app-capable" content="yes"> <!-- 启用iOS全屏模式 -->
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent"> <!-- iOS状态栏样式 -->
    <meta name="msapplication-navbutton-color" content="#0a0a2e"> <!-- Windows Phone状态栏颜色 -->
    <title>登录 - 趣玩星球</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="../css/page-protection.css">
    <style>
        /* CSS变量定义 - 主题色系统 */
        :root {
            --primary-color: #40E0D0;
            --primary-light: #AFFBF2;
            --primary-dark: #20B2AA;
            --secondary-color: #06D6A0;
            --accent-color: #FF6B9D;
            --gradient-primary: linear-gradient(135deg, #40E0D0, #06D6A0);
            --gradient-secondary: linear-gradient(135deg, #AFFBF2, #40E0D0);
            --gradient-accent: linear-gradient(135deg, #FF6B9D, #FFB6C1);
            --text-primary: #333333;
            --text-secondary: #666666;
            --text-light: #999999;
            --bg-white: #FFFFFF;
            --bg-light: #F8F9FA;
            --shadow-sm: 0 2px 8px rgba(64, 224, 208, 0.08);
            --shadow-md: 0 4px 16px rgba(64, 224, 208, 0.12);
            --shadow-lg: 0 8px 32px rgba(64, 224, 208, 0.16);
            --radius-sm: 8px;
            --radius-md: 12px;
            --radius-lg: 16px;
            --radius-xl: 24px;
            --transition-fast: 0.2s ease;
            --transition-normal: 0.3s ease;
            --transition-slow: 0.4s ease;
        }

        /* 防止内容被选择 */
        * {
            -webkit-touch-callout: none;
            -webkit-user-select: none;
            -khtml-user-select: none;
            -moz-user-select: none;
            -ms-user-select: none;
            user-select: none;
        }

        html {
            height: 100%;
        }

        body {
            background: linear-gradient(135deg, #0a0a2e 0%, #16213e 25%, #1a1a3a 50%, #0f3460 75%, #0a0a2e 100%);
            position: relative;
            margin: 0;
            padding: 0;
            min-height: 100vh;
            overflow-x: hidden;
            font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;
        }

        /* 星空背景动画 */
        .space-background {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: 1;
            overflow: hidden;
        }

        /* 主星球和轨道系统 - 跟随滚动 */
        .planet-system {
            position: absolute;
            top: 80px;
            left: 50%;
            transform: translateX(-50%);
            width: 300px;
            height: 300px;
        }

        /* 轨道 */
        .orbit {
            position: absolute;
            border: 2px solid rgba(64, 224, 208, 0.2);
            border-radius: 50%;
            animation: orbitRotate 30s linear infinite;
        }

        .orbit-1 {
            width: 200px;
            height: 200px;
            top: 50px;
            left: 50px;
        }

        .orbit-2 {
            width: 260px;
            height: 260px;
            top: 20px;
            left: 20px;
            animation-duration: 45s;
            animation-direction: reverse;
        }

        .orbit-3 {
            width: 300px;
            height: 300px;
            top: 0;
            left: 0;
            animation-duration: 60s;
        }

        /* 中心星球 */
        .central-planet {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 80px;
            height: 80px;
            background: radial-gradient(circle at 30% 30%, var(--primary-light), var(--primary-color), var(--primary-dark));
            border-radius: 50%;
            box-shadow:
                inset -15px -15px 30px rgba(32, 178, 170, 0.4),
                0 0 40px rgba(64, 224, 208, 0.3),
                0 0 80px rgba(64, 224, 208, 0.1);
            animation: planetPulse 4s ease-in-out infinite;
        }

        .central-planet::before {
            content: '';
            position: absolute;
            top: 15%;
            left: 20%;
            width: 25%;
            height: 25%;
            background: rgba(175, 251, 242, 0.6);
            border-radius: 50%;
            animation: planetRotate 20s linear infinite;
        }

        .central-planet::after {
            content: '';
            position: absolute;
            bottom: 25%;
            right: 15%;
            width: 15%;
            height: 15%;
            background: rgba(6, 214, 160, 0.5);
            border-radius: 50%;
            animation: planetRotate 15s linear infinite reverse;
        }

        /* 轨道上的圆角星星 */
        .orbit-star {
            position: absolute;
            width: 8px;
            height: 8px;
            background: #FFD700;
            border-radius: 2px;
            box-shadow: 0 0 10px #FFD700, 0 0 20px #FFD700;
            animation: starTwinkle 2s ease-in-out infinite;
        }

        .orbit-1 .orbit-star {
            top: -4px;
            left: 50%;
            transform: translateX(-50%);
            animation-delay: 0s;
        }

        .orbit-2 .orbit-star {
            top: 50%;
            right: -4px;
            transform: translateY(-50%);
            animation-delay: 1s;
        }

        .orbit-3 .orbit-star {
            bottom: -4px;
            left: 30%;
            transform: translateX(-50%);
            animation-delay: 2s;
        }

        /* 登录容器 - 全屏无边距 */
        .container {
            position: relative;
            z-index: 10;
            margin: 0;
            padding: 420px 0 60px 0;
            min-height: 100vh;
            width: 100vw;
            display: flex;
            align-items: flex-start;
            justify-content: center;
        }

        /* 登录卡片样式 - 全屏无边距，自适应高度 */
        .login-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 0;
            padding: 40px 20px;
            box-shadow: none;
            border: none;
            width: 100%;
            max-width: none;
            min-height: auto;
        }

        /* Logo样式 */
        .logo {
            text-align: center;
            margin-bottom: 30px;
        }

        .logo h1 {
            background: var(--gradient-primary);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            font-size: 2rem;
            font-weight: 700;
            margin: 0;
            position: relative;
        }

        .logo h1::before {
            content: '🪐';
            font-size: 1.5rem;
            margin-right: 8px;
            -webkit-text-fill-color: initial;
        }

        .logo p {
            color: var(--text-secondary);
            font-size: 0.9rem;
            margin: 8px 0 0 0;
        }

        /* 标签页样式 */
        .tabs {
            display: flex;
            background: rgba(64, 224, 208, 0.1);
            border-radius: var(--radius-md);
            padding: 4px;
            margin-bottom: 30px;
        }

        /* 标签页按钮 */
        .tab {
            flex: 1;
            padding: 12px;
            text-align: center;
            border-radius: var(--radius-sm);
            font-weight: 600;
            color: var(--text-secondary);
            cursor: pointer;
            transition: var(--transition-normal);
            background: transparent;
            border: none;
            font-size: 0.9rem;
        }

        .tab.active {
            background: var(--bg-white);
            color: var(--primary-color);
            box-shadow: var(--shadow-sm);
        }

        /* 智能登录按钮样式 */
        .smart-login-btn {
            width: 100%;
            padding: 16px;
            background: linear-gradient(135deg, #FF6B9D, #FFB6C1);
            color: white;
            border: none;
            border-radius: var(--radius-md);
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: var(--transition-normal);
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
            box-shadow: var(--shadow-md);
        }

        .smart-login-btn:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-lg);
        }

        .divider {
            display: flex;
            align-items: center;
            margin: 20px 0;
            color: var(--text-light);
            font-size: 0.9rem;
        }

        .divider::before,
        .divider::after {
            content: '';
            flex: 1;
            height: 1px;
            background: rgba(64, 224, 208, 0.2);
        }

        .divider span {
            padding: 0 16px;
        }

        /* 表单容器 - 修复高度问题 */
        .form-container {
            position: relative;
            min-height: 500px;
        }

        /* 表单样式 - 修复定位问题 */
        .form {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            opacity: 0;
            visibility: hidden;
            transition: all var(--transition-normal);
            transform: translateY(20px);
        }

        .form.active {
            opacity: 1;
            visibility: visible;
            transform: translateY(0);
            position: relative;
        }

        /* 浮动标签输入框组 */
        .input-group {
            margin-bottom: 24px;
            position: relative;
        }

        .input-group input {
            width: 100%;
            padding: 20px 16px 8px 16px;
            border: 2px solid rgba(64, 224, 208, 0.2);
            border-radius: var(--radius-md);
            font-size: 1rem;
            background: var(--bg-white);
            color: var(--text-primary);
            transition: var(--transition-normal);
            box-sizing: border-box;
        }

        /* 手机号输入框特殊样式 - 包含按钮 */
        .input-group:has(#reg_phone) input {
            padding-right: 100px; /* 为按钮留出空间 */
        }

        /* 手机号验证码按钮 */
        .phone-captcha-btn {
            position: absolute;
            right: 8px;
            top: 50%;
            transform: translateY(-50%);
            padding: 8px 12px;
            background: var(--gradient-primary);
            color: white;
            border: 1px solid var(--primary-color);
            border-radius: var(--radius-sm);
            font-size: 0.8rem;
            font-weight: 600;
            cursor: pointer;
            transition: var(--transition-normal);
            white-space: nowrap;
            min-width: 80px;
            height: 32px;
            z-index: 2;
        }

        .phone-captcha-btn:hover:not(:disabled) {
            background: var(--gradient-secondary);
            color: var(--primary-color);
            border-color: var(--primary-color);
        }

        .phone-captcha-btn:disabled {
            background: #f5f5f5;
            color: #999;
            cursor: not-allowed;
            border-color: #ddd;
        }

        .input-group label {
            position: absolute;
            left: 16px;
            top: 50%;
            transform: translateY(-50%);
            color: var(--text-secondary);
            font-size: 1rem;
            font-weight: 400;
            pointer-events: none;
            transition: var(--transition-normal);
            background: var(--bg-white);
            padding: 0 4px;
        }

        .input-group input:focus,
        .input-group input:not(:placeholder-shown) {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 4px rgba(64, 224, 208, 0.1);
        }

        .input-group input:focus + label,
        .input-group input:not(:placeholder-shown) + label {
            top: 0;
            transform: translateY(-50%);
            font-size: 0.8rem;
            color: var(--primary-color);
            font-weight: 600;
        }

        /* 按钮样式 */
        .btn {
            width: 100%;
            padding: 16px;
            border: none;
            border-radius: var(--radius-md);
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: var(--transition-normal);
            position: relative;
            overflow: hidden;
        }

        .btn-primary {
            background: var(--gradient-primary);
            color: white;
            box-shadow: var(--shadow-md);
        }

        .btn-primary:hover {
            transform: translateY(-3px);
            box-shadow: var(--shadow-lg);
        }

        .btn-primary:active {
            transform: translateY(-1px);
        }

        /* 表单选项 */
        .form-options {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 24px;
            font-size: 0.9rem;
        }

        .checkbox-label {
            display: flex;
            align-items: center;
            cursor: pointer;
            color: var(--text-secondary);
        }

        .checkbox-label input[type="checkbox"] {
            display: none;
        }

        .checkmark {
            width: 18px;
            height: 18px;
            border: 2px solid rgba(64, 224, 208, 0.3);
            border-radius: 4px;
            margin-right: 8px;
            position: relative;
            transition: var(--transition-normal);
        }

        .checkbox-label input[type="checkbox"]:checked + .checkmark {
            background: var(--gradient-primary);
            border-color: var(--primary-color);
        }

        .checkbox-label input[type="checkbox"]:checked + .checkmark::after {
            content: '✓';
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            color: white;
            font-size: 12px;
            font-weight: bold;
        }

        .forgot-link {
            color: var(--primary-color);
            text-decoration: none;
            font-size: 0.9rem;
            transition: var(--transition-normal);
        }

        .forgot-link:hover {
            color: var(--primary-dark);
        }

        /* 手机验证码组 */
        .phone-captcha-group {
            margin-bottom: 24px;
            position: relative;
            animation: slideDown 0.3s ease-out;
        }

        @keyframes slideDown {
            from {
                opacity: 0;
                transform: translateY(-10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* 验证码输入 - 重新设计布局 */
        .captcha-group {
            margin-bottom: 24px;
            position: relative;
        }

        .captcha-container {
            width: 100%;
            padding: 8px 12px;
            border: 2px solid rgba(64, 224, 208, 0.2);
            border-radius: var(--radius-md);
            background: var(--bg-white);
            transition: var(--transition-normal);
            box-sizing: border-box;
            display: flex;
            align-items: center;
            gap: 8px;
            min-height: 56px;
        }

        .captcha-container:focus-within {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 4px rgba(64, 224, 208, 0.1);
        }

        .captcha-input-wrapper {
            flex: 1;
            position: relative;
            min-width: 0;
        }

        .captcha-input {
            width: 100%;
            padding: 20px 16px 8px 16px;
            border: none;
            outline: none;
            background: transparent;
            font-size: 1rem;
            color: var(--text-primary);
            box-sizing: border-box;
        }

        .captcha-input-wrapper label {
            position: absolute;
            left: 16px;
            top: 50%;
            transform: translateY(-50%);
            color: var(--text-secondary);
            font-size: 1rem;
            font-weight: 400;
            pointer-events: none;
            transition: var(--transition-normal);
            background: var(--bg-white);
            padding: 0 4px;
            z-index: 2;
        }

        .captcha-input:focus + label,
        .captcha-input:not(:placeholder-shown) + label {
            top: 0;
            transform: translateY(-50%);
            font-size: 0.8rem;
            color: var(--primary-color);
            font-weight: 600;
        }

        .captcha-btn {
            padding: 8px 12px;
            background: var(--gradient-primary);
            color: white;
            border: 1px solid var(--primary-color);
            border-radius: var(--radius-sm);
            font-size: 0.8rem;
            font-weight: 600;
            cursor: pointer;
            transition: var(--transition-normal);
            white-space: nowrap;
            min-width: 80px;
            height: 36px;
            flex-shrink: 0;
        }

        .captcha-btn:hover:not(:disabled) {
            background: var(--gradient-secondary);
            color: var(--primary-color);
            border-color: var(--primary-color);
        }

        .captcha-btn:disabled {
            background: #f5f5f5;
            color: #999;
            cursor: not-allowed;
            border-color: #ddd;
        }

        #captcha-image {
            height: 36px;
            width: 100px;
            border: 1px solid rgba(64, 224, 208, 0.3);
            border-radius: var(--radius-sm);
            cursor: pointer;
            transition: var(--transition-normal);
            object-fit: contain;
            flex-shrink: 0;
            background: white;
        }

        #captcha-image:hover {
            border-color: var(--primary-color);
            transform: scale(1.05);
        }

        /* 协议部分 */
        .agreement-section {
            margin-bottom: 24px;
            padding: 16px;
            background: rgba(64, 224, 208, 0.05);
            border-radius: var(--radius-md);
            border: 1px solid rgba(64, 224, 208, 0.1);
        }

        .agreement-checkbox {
            display: flex;
            align-items: flex-start;
            cursor: pointer;
            color: var(--text-primary);
            font-size: 0.85rem;
            line-height: 1.5;
        }

        .agreement-checkbox input[type="checkbox"] {
            display: none;
        }

        .agreement-checkbox .checkmark {
            width: 18px;
            height: 18px;
            border: 2px solid rgba(64, 224, 208, 0.3);
            border-radius: 4px;
            margin-right: 12px;
            margin-top: 3px;
            position: relative;
            transition: var(--transition-normal);
            flex-shrink: 0;
        }

        .agreement-checkbox input[type="checkbox"]:checked + .checkmark {
            background: var(--gradient-primary);
            border-color: var(--primary-color);
        }

        .agreement-checkbox input[type="checkbox"]:checked + .checkmark::after {
            content: '✓';
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            color: white;
            font-size: 12px;
            font-weight: bold;
        }

        .agreement-checkbox .agreement-text {
            flex: 1;
            color: var(--text-primary);
            font-size: 0.85rem;
            line-height: 1.4;
            word-wrap: break-word;
            overflow-wrap: break-word;
        }

        .agreement-checkbox .agreement-text a {
            color: var(--primary-color);
            text-decoration: none;
            font-weight: 600;
        }

        .agreement-checkbox .agreement-text a:hover {
            color: var(--primary-dark);
            text-decoration: underline;
        }

        /* 登录页面协议文本 */
        .agreement-text {
            text-align: center;
            font-size: 0.8rem;
            color: var(--text-light);
            margin-top: 16px;
            line-height: 1.4;
        }

        .agreement-text a {
            color: var(--primary-color);
            text-decoration: none;
        }

        .agreement-text a:hover {
            text-decoration: underline;
        }

        /* 注册链接样式 */
        .register-link {
            text-align: center;
            margin-top: 24px;
            padding-top: 20px;
            border-top: 1px solid rgba(64, 224, 208, 0.1);
        }

        .register-link p {
            color: var(--text-secondary);
            font-size: 0.9rem;
            margin: 0;
        }

        .register-link a {
            color: var(--primary-color);
            text-decoration: none;
            font-weight: 600;
            transition: var(--transition-normal);
        }

        .register-link a:hover {
            color: var(--primary-dark);
            text-decoration: underline;
        }

        /* Toast提示样式 */
        .toast {
            position: fixed;
            top: 20px;
            left: 50%;
            transform: translateX(-50%);
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 12px 24px;
            border-radius: var(--radius-md);
            font-size: 0.9rem;
            font-weight: 500;
            z-index: 10000;
            opacity: 0;
            visibility: hidden;
            transition: all var(--transition-normal);
            backdrop-filter: blur(10px);
            box-shadow: var(--shadow-lg);
        }

        .toast.show {
            opacity: 1;
            visibility: visible;
            transform: translateX(-50%) translateY(0);
        }

        .toast.success {
            background: rgba(34, 197, 94, 0.9);
            border: 1px solid rgba(34, 197, 94, 0.3);
        }

        .toast.error {
            background: rgba(239, 68, 68, 0.9);
            border: 1px solid rgba(239, 68, 68, 0.3);
        }

        .toast.warning {
            background: rgba(245, 158, 11, 0.9);
            border: 1px solid rgba(245, 158, 11, 0.3);
        }

        .toast.info {
            background: rgba(59, 130, 246, 0.9);
            border: 1px solid rgba(59, 130, 246, 0.3);
        }

        /* 允许输入框内的文本选择 */
        input[type="text"],
        input[type="password"],
        input[type="email"],
        input[type="tel"] {
            -webkit-user-select: text;
            -khtml-user-select: text;
            -moz-user-select: text;
            -ms-user-select: text;
            user-select: text;
        }

        /* 动画关键帧 */
        @keyframes orbitRotate {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        @keyframes planetRotate {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        @keyframes planetPulse {
            0%, 100% { transform: translate(-50%, -50%) scale(1); }
            50% { transform: translate(-50%, -50%) scale(1.05); }
        }

        @keyframes starTwinkle {
            0%, 100% { opacity: 0.3; transform: scale(0.8); }
            50% { opacity: 1; transform: scale(1.2); }
        }

        /* 响应式调整 - 全屏设计 */
        @media (max-width: 768px) {
            .planet-system {
                width: 250px;
                height: 250px;
                top: 60px;
            }

            .orbit-1 {
                width: 160px;
                height: 160px;
                top: 45px;
                left: 45px;
            }

            .orbit-2 {
                width: 200px;
                height: 200px;
                top: 25px;
                left: 25px;
            }

            .orbit-3 {
                width: 250px;
                height: 250px;
                top: 0;
                left: 0;
            }

            .central-planet {
                width: 60px;
                height: 60px;
            }

            .container {
                padding: 340px 0 60px 0;
                margin: 0;
                width: 100vw;
            }

            .login-card {
                padding: 30px 15px;
                margin: 0;
                border-radius: 0;
                min-height: auto;
            }
        }

        @media (max-width: 480px) {
            .planet-system {
                width: 200px;
                height: 200px;
                top: 40px;
            }

            .orbit-1 {
                width: 120px;
                height: 120px;
                top: 40px;
                left: 40px;
            }

            .orbit-2 {
                width: 160px;
                height: 160px;
                top: 20px;
                left: 20px;
            }

            .orbit-3 {
                width: 200px;
                height: 200px;
                top: 0;
                left: 0;
            }

            .central-planet {
                width: 50px;
                height: 50px;
            }

            .container {
                padding: 280px 0 60px 0;
                margin: 0;
                width: 100vw;
            }

            .login-card {
                padding: 25px 10px;
                margin: 0;
                border-radius: 0;
                min-height: auto;
            }

            .captcha-btn {
                font-size: 0.7rem;
                min-width: 70px;
                padding: 6px 8px;
                height: 32px;
            }

            #captcha-image {
                width: 80px;
                height: 32px;
            }

            .agreement-section {
                padding: 12px;
                margin-bottom: 20px;
            }

            .agreement-checkbox {
                font-size: 0.8rem;
            }

            .agreement-checkbox .checkmark {
                width: 16px;
                height: 16px;
                margin-right: 10px;
                margin-top: 2px;
            }

            /* 移动端手机号按钮调整 */
            .phone-captcha-btn {
                min-width: 70px;
                font-size: 0.7rem;
                padding: 6px 8px;
                height: 28px;
            }

            /* 移动端手机号输入框右边距调整 */
            .input-group:has(#reg_phone) input {
                padding-right: 85px;
            }
        }
    </style>
</head>
<body>
    <!-- 星空背景动画 -->
    <div class="space-background">
        <div class="planet-system">
            <!-- 轨道 -->
            <div class="orbit orbit-1">
                <div class="orbit-star"></div>
            </div>
            <div class="orbit orbit-2">
                <div class="orbit-star"></div>
            </div>
            <div class="orbit orbit-3">
                <div class="orbit-star"></div>
            </div>

            <!-- 中心星球 -->
            <div class="central-planet"></div>
        </div>
    </div>

    <!-- Toast提示 -->
    <div class="toast" id="toast" style="z-index:9999;"></div>

    <div class="container">
        <div class="login-card">
            <!-- Logo -->
            <div class="logo">
                <h1>趣玩星球</h1>
                <p>探索有趣的生活</p>
            </div>

            <!-- 智能登录按钮 -->
            <button type="button" class="smart-login-btn" onclick="window.location.href='smart_login.php'">
                <i class="fas fa-magic"></i>
                智能登录（推荐）
            </button>

            <div class="divider">
                <span>或使用传统登录</span>
            </div>

            <!-- 登录表单 -->
            <form id="login-form" method="post">
                    <input type="hidden" name="action" value="login">

                    <div class="input-group">
                        <input type="text" name="username" id="username" placeholder=" " required>
                        <label for="username">手机号/趣玩ID</label>
                    </div>

                    <div class="input-group">
                        <input type="password" name="password" id="password" placeholder=" " required>
                        <label for="password">密码</label>
                    </div>

                    <div class="form-options">
                        <label class="checkbox-label">
                            <input type="checkbox" id="remember" name="remember">
                            <span class="checkmark"></span>
                            记住我
                        </label>
                        <a href="#" class="forgot-link">忘记密码?</a>
                    </div>

                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-sign-in-alt"></i>
                        登录
                    </button>

                    <div class="agreement-text">
                        登录即代表您同意
                        <a href="../policies/user_agreement.php?from=login">用户协议</a>
                        和
                        <a href="../policies/privacy_policy.php?from=login">隐私政策</a>
                    </div>
                </form>

                <!-- 注册链接 -->
                <div class="register-link">
                    <p>还没有账号？<a href="../register/index.php">立即注册</a></p>
                </div>
        </div>
    </div>

    <!-- Toast提示 -->
    <div id="toast" class="toast"></div>

    <script src="js/script.js"></script>
    <script>
        // 验证码状态管理
        let captchaTimer = null;
        let captchaActive = false;

        // 手机号格式验证
        function validatePhoneNumber(phone) {
            // 中国大陆手机号正则：1开头，第二位是3-9，总共11位数字
            const phoneRegex = /^1[3-9]\d{9}$/;
            return phoneRegex.test(phone);
        }

        // 获取手机验证码函数
        function getPhoneCaptcha() {
            const phoneInput = document.getElementById('reg_phone');
            const btn = document.getElementById('get-phone-captcha-btn');
            const captchaGroup = document.getElementById('phone-captcha-group');
            const img = document.getElementById('captcha-image');

            const phone = phoneInput.value.trim();

            // 验证手机号格式
            if (!phone) {
                showToast('请输入手机号', 'warning');
                phoneInput.focus();
                return;
            }

            if (!validatePhoneNumber(phone)) {
                showToast('请输入正确的手机号格式', 'warning');
                phoneInput.focus();
                return;
            }

            // 禁用按钮
            btn.disabled = true;
            btn.textContent = '获取中...';
            captchaActive = true;

            // 显示验证码组
            captchaGroup.style.display = 'block';

            // 显示验证码图片
            img.src = 'captcha.php?' + new Date().getTime();
            img.style.display = 'inline-block';

            // 开始60秒倒计时
            let countdown = 60;
            captchaTimer = setInterval(() => {
                countdown--;
                btn.textContent = `${countdown}秒后重新获取`;

                if (countdown <= 0) {
                    clearInterval(captchaTimer);
                    captchaTimer = null;
                    captchaActive = false;
                    btn.disabled = false;
                    btn.textContent = '获取验证码';
                }
            }, 1000);

            // 聚焦到验证码输入框
            setTimeout(() => {
                document.getElementById('captcha').focus();
            }, 300);
        }

        // 刷新验证码函数 - 只有在60秒倒计时期间才允许刷新
        function refreshCaptcha() {
            const captchaImage = document.getElementById('captcha-image');

            // 只有在验证码激活状态且图片可见时才允许刷新
            if (captchaActive && captchaImage && captchaImage.style.display !== 'none') {
                captchaImage.src = 'captcha.php?' + new Date().getTime();
            } else if (!captchaActive) {
                // 如果不在激活状态，提示用户需要先获取验证码
                showToast('请先点击"获取验证码"按钮', 'warning');
            }
        }

        // Toast提示功能
        function showToast(message, type = 'info') {
            const toast = document.getElementById('toast');
            toast.textContent = message;
            toast.className = `toast ${type} show`;

            setTimeout(() => {
                toast.className = 'toast';
            }, 3000);
        }

        // 表单提交处理
        function handleFormSubmit(event) {
            const form = event.target;
            const formId = form.id;

            // 对于登录表单，进行字段验证
            if (formId === 'login-form') {
                const usernameInput = document.getElementById('username');
                const passwordInput = document.getElementById('password');
                const username = usernameInput.value.trim();
                const password = passwordInput.value.trim();

                // 检查用户名是否填写
                if (!username) {
                    event.preventDefault();
                    showToast('请输入手机号或趣玩ID', 'warning');
                    usernameInput.focus();
                    return false;
                }

                // 检查密码是否填写
                if (!password) {
                    event.preventDefault();
                    showToast('请输入密码', 'warning');
                    passwordInput.focus();
                    return false;
                }
            }

            // 对于注册表单，进行验证码检查
            if (formId === 'register-form') {
                const phoneInput = document.getElementById('reg_phone');
                const captchaInput = document.getElementById('captcha');
                const phone = phoneInput.value.trim();

                // 检查手机号格式
                if (!phone || !validatePhoneNumber(phone)) {
                    event.preventDefault();
                    showToast('请输入正确的手机号格式', 'warning');
                    phoneInput.focus();
                    return false;
                }

                // 检查是否已获取验证码
                if (!captchaActive) {
                    event.preventDefault();
                    showToast('请先获取手机验证码', 'warning');
                    return false;
                }

                // 检查验证码是否已输入
                if (!captchaInput.value.trim()) {
                    event.preventDefault();
                    showToast('请输入验证码', 'warning');
                    captchaInput.focus();
                    return false;
                }
            }

            // 显示加载状态
            const submitBtn = form.querySelector('button[type="submit"]');
            const originalText = submitBtn.innerHTML;
            submitBtn.disabled = true;

            if (formId === 'login-form') {
                submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 登录中...';
            } else {
                submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 注册中...';
            }

            // 让表单正常提交，不阻止默认行为
            // 后端会处理重定向
        }

        // 防止图片被保存或复制
        document.addEventListener('DOMContentLoaded', function() {
            // 绑定表单提交事件
            const loginForm = document.getElementById('login-form');
            const registerForm = document.getElementById('register-form');

            if (loginForm) {
                loginForm.addEventListener('submit', handleFormSubmit);
            }
            if (registerForm) {
                registerForm.addEventListener('submit', handleFormSubmit);
            }

            // 手机号输入监听 - 手机号改变时重置验证码状态
            const phoneInput = document.getElementById('reg_phone');
            if (phoneInput) {
                let lastValidPhone = '';

                phoneInput.addEventListener('input', function() {
                    const currentPhone = this.value.trim();

                    // 如果手机号发生变化且之前已获取过验证码，则重置状态
                    if (lastValidPhone && currentPhone !== lastValidPhone && captchaActive) {
                        // 重置验证码状态
                        if (captchaTimer) {
                            clearInterval(captchaTimer);
                            captchaTimer = null;
                        }
                        captchaActive = false;

                        // 重置按钮状态
                        const btn = document.getElementById('get-phone-captcha-btn');
                        btn.disabled = false;
                        btn.textContent = '获取验证码';

                        // 隐藏验证码组
                        const captchaGroup = document.getElementById('phone-captcha-group');
                        captchaGroup.style.display = 'none';

                        // 清空验证码输入
                        const captchaInput = document.getElementById('captcha');
                        captchaInput.value = '';

                        showToast('手机号已更改，请重新获取验证码', 'info');
                    }

                    // 更新最后有效的手机号
                    if (validatePhoneNumber(currentPhone)) {
                        lastValidPhone = currentPhone;
                    }
                });
            }

            // 检查URL参数，显示相应提示
            const urlParams = new URLSearchParams(window.location.search);

            if (urlParams.get('login_failed') === '1') {
                showToast('登录失败，请检查用户名和密码', 'error');
            }

            if (urlParams.get('register_failed') === '1') {
                showToast('注册失败，请检查输入信息', 'error');
            }

            // 检查登录成功参数
            if (urlParams.get('login_success') === '1') {
                showToast('登录成功！欢迎回来', 'success');
                // 清除URL参数
                setTimeout(() => {
                    window.history.replaceState({}, document.title, window.location.pathname);
                }, 2000);
            }

            // 检查会话中的成功标志
            <?php if (isset($_SESSION['login_success'])): ?>
                showToast('登录成功！欢迎回来', 'success');
                <?php unset($_SESSION['login_success']); ?>
            <?php endif; ?>

            <?php if (isset($_SESSION['register_success'])): ?>
                showToast('注册成功！正在跳转...', 'success');
                setTimeout(() => {
                    window.location.href = '../onboarding/index.php';
                }, 1500);
                <?php unset($_SESSION['register_success']); ?>
            <?php endif; ?>
            // 禁止右键菜单
            document.addEventListener('contextmenu', function(e) {
                e.preventDefault();
                return false;
            });

            // 禁止拖拽图片
            document.addEventListener('dragstart', function(e) {
                e.preventDefault();
                return false;
            });

            // 禁止长按选择
            document.addEventListener('selectstart', function(e) {
                // 允许输入框内的文本选择
                if (e.target.tagName === 'INPUT' || e.target.tagName === 'TEXTAREA') {
                    return true;
                }
                e.preventDefault();
                return false;
            });

            // 禁止触摸长按弹出菜单
            document.addEventListener('touchstart', function(e) {
                // 允许按钮和链接的触摸事件
                if (e.target.tagName === 'BUTTON' || e.target.tagName === 'A' ||
                    e.target.tagName === 'INPUT' || e.target.tagName === 'TEXTAREA') {
                    return true;
                }
                // 防止长按弹出菜单
                if (e.touches.length > 1 || e.targetTouches.length > 1) {
                    e.preventDefault();
                    return false;
                }
            }, { passive: false });
        });
    </script>
    <script src="../js/page-protection.js"></script>
</body>
</html>
