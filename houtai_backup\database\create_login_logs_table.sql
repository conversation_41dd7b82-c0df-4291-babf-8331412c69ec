-- 创建登录日志表
-- 用于记录用户登录活动，支持用户画像分析

CREATE TABLE IF NOT EXISTS `login_logs` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL COMMENT '用户ID，0表示登录失败',
  `login_time` datetime NOT NULL COMMENT '登录时间',
  `ip_address` varchar(45) NOT NULL COMMENT 'IP地址',
  `user_agent` text COMMENT '用户代理字符串',
  `status` enum('success','failed') NOT NULL DEFAULT 'success' COMMENT '登录状态',
  `device_fingerprint` varchar(32) DEFAULT NULL COMMENT '设备指纹',
  `login_type` enum('quick_login','secure_login','normal_login') DEFAULT 'normal_login' COMMENT '登录类型',
  `location` varchar(100) DEFAULT NULL COMMENT '地理位置',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_login_time` (`login_time`),
  KEY `idx_ip_address` (`ip_address`),
  KEY `idx_device_fingerprint` (`device_fingerprint`),
  KEY `idx_status` (`status`),
  KEY `idx_location` (`location`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户登录日志表';

-- 插入一些示例数据（可选）
-- 注意：请根据实际的用户ID替换下面的数据

INSERT IGNORE INTO `login_logs` (`user_id`, `login_time`, `ip_address`, `user_agent`, `status`, `login_type`, `location`) VALUES
(1, NOW() - INTERVAL 1 HOUR, '*************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36', 'success', 'normal_login', '北京市'),
(1, NOW() - INTERVAL 2 HOUR, '*************', 'Mozilla/5.0 (iPhone; CPU iPhone OS 17_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.0 Mobile/15E148 Safari/604.1', 'success', 'quick_login', '北京市'),
(1, NOW() - INTERVAL 1 DAY, '**************', 'Mozilla/5.0 (Linux; Android 13; SM-G991B) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Mobile Safari/537.36', 'success', 'normal_login', '上海市'),
(2, NOW() - INTERVAL 3 HOUR, '*************', 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36', 'success', 'normal_login', '广东省深圳市'),
(2, NOW() - INTERVAL 6 HOUR, '*************', 'Mozilla/5.0 (iPad; CPU OS 17_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.0 Mobile/15E148 Safari/604.1', 'success', 'quick_login', '广东省深圳市'),
(3, NOW() - INTERVAL 4 HOUR, '***************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) Gecko/20100101 Firefox/119.0', 'success', 'secure_login', '江苏省南京市'),
(3, NOW() - INTERVAL 8 HOUR, '***************', 'Mozilla/5.0 (Linux; Android 12; Pixel 6) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Mobile Safari/537.36', 'success', 'normal_login', '江苏省南京市'),
(4, NOW() - INTERVAL 5 HOUR, '*******', 'Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1', 'success', 'quick_login', '浙江省杭州市'),
(4, NOW() - INTERVAL 12 HOUR, '*******', 'Mozilla/5.0 (Windows NT 11.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36 Edg/120.0.0.0', 'success', 'normal_login', '浙江省杭州市'),
(5, NOW() - INTERVAL 7 HOUR, '*********', 'Mozilla/5.0 (Linux; Android 11; SM-A515F) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Mobile Safari/537.36', 'success', 'normal_login', '四川省成都市');

-- 为不同时间段添加更多登录记录，用于24小时活跃度分析
INSERT IGNORE INTO `login_logs` (`user_id`, `login_time`, `ip_address`, `user_agent`, `status`, `login_type`, `location`) VALUES
-- 早上时段 (6-12点)
(1, DATE_SUB(NOW(), INTERVAL 6 HOUR), '*************', 'Mozilla/5.0 (iPhone; CPU iPhone OS 17_0 like Mac OS X) AppleWebKit/605.1.15', 'success', 'quick_login', '北京市'),
(2, DATE_SUB(NOW(), INTERVAL 8 HOUR), '*************', 'Mozilla/5.0 (Linux; Android 13; SM-G991B) AppleWebKit/537.36', 'success', 'normal_login', '上海市'),
(3, DATE_SUB(NOW(), INTERVAL 10 HOUR), '***************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36', 'success', 'normal_login', '广东省'),

-- 下午时段 (12-18点)
(1, DATE_SUB(NOW(), INTERVAL 14 HOUR), '*************', 'Mozilla/5.0 (iPhone; CPU iPhone OS 17_0 like Mac OS X) AppleWebKit/605.1.15', 'success', 'quick_login', '北京市'),
(4, DATE_SUB(NOW(), INTERVAL 15 HOUR), '*******', 'Mozilla/5.0 (iPad; CPU OS 17_0 like Mac OS X) AppleWebKit/605.1.15', 'success', 'normal_login', '江苏省'),
(5, DATE_SUB(NOW(), INTERVAL 16 HOUR), '*********', 'Mozilla/5.0 (Linux; Android 11; SM-A515F) AppleWebKit/537.36', 'success', 'normal_login', '浙江省'),

-- 晚上时段 (18-24点) - 高峰期
(1, DATE_SUB(NOW(), INTERVAL 20 HOUR), '*************', 'Mozilla/5.0 (iPhone; CPU iPhone OS 17_0 like Mac OS X) AppleWebKit/605.1.15', 'success', 'quick_login', '北京市'),
(2, DATE_SUB(NOW(), INTERVAL 20 HOUR), '*************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36', 'success', 'normal_login', '上海市'),
(3, DATE_SUB(NOW(), INTERVAL 21 HOUR), '***************', 'Mozilla/5.0 (Linux; Android 13; SM-G991B) AppleWebKit/537.36', 'success', 'normal_login', '广东省'),
(4, DATE_SUB(NOW(), INTERVAL 21 HOUR), '*******', 'Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15', 'success', 'quick_login', '江苏省'),
(5, DATE_SUB(NOW(), INTERVAL 22 HOUR), '*********', 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36', 'success', 'normal_login', '浙江省'),

-- 深夜时段 (0-6点)
(1, DATE_SUB(NOW(), INTERVAL 26 HOUR), '*************', 'Mozilla/5.0 (iPhone; CPU iPhone OS 17_0 like Mac OS X) AppleWebKit/605.1.15', 'success', 'quick_login', '北京市'),
(3, DATE_SUB(NOW(), INTERVAL 28 HOUR), '***************', 'Mozilla/5.0 (Linux; Android 13; SM-G991B) AppleWebKit/537.36', 'success', 'normal_login', '广东省');

-- 添加一些登录失败记录
INSERT IGNORE INTO `login_logs` (`user_id`, `login_time`, `ip_address`, `user_agent`, `status`, `login_type`, `location`) VALUES
(0, NOW() - INTERVAL 30 MINUTE, '*************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 [Phone: ***********, Reason: invalid_credentials]', 'failed', 'normal_login', '未知地区'),
(0, NOW() - INTERVAL 1 HOUR, '*************', 'Mozilla/5.0 (iPhone; CPU iPhone OS 17_0 like Mac OS X) AppleWebKit/605.1.15 [Phone: ***********, Reason: invalid_verification_code]', 'failed', 'quick_login', '未知地区'),
(0, NOW() - INTERVAL 2 HOUR, '*************', 'Mozilla/5.0 (Linux; Android 13; SM-G991B) AppleWebKit/537.36 [Phone: ***********, Reason: account_locked]', 'failed', 'normal_login', '未知地区');

-- 创建索引以优化查询性能
CREATE INDEX IF NOT EXISTS `idx_user_login_time` ON `login_logs` (`user_id`, `login_time`);
CREATE INDEX IF NOT EXISTS `idx_status_time` ON `login_logs` (`status`, `login_time`);
CREATE INDEX IF NOT EXISTS `idx_location_time` ON `login_logs` (`location`, `login_time`);

-- 添加表注释
ALTER TABLE `login_logs` COMMENT = '用户登录日志表 - 用于用户画像分析和安全监控';

-- 显示创建结果
SELECT 'login_logs表创建完成！' as message;
SELECT COUNT(*) as total_records FROM `login_logs`;
SELECT COUNT(DISTINCT user_id) as unique_users FROM `login_logs` WHERE user_id > 0;
SELECT COUNT(*) as failed_attempts FROM `login_logs` WHERE status = 'failed';
