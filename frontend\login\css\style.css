/* 全局样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    font-family: 'PingFang SC', 'Helvetica Neue', Arial, sans-serif;
}

body {
    background: #f8f9fa;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    align-items: center;
    color: #333333;
    line-height: 1.6;
    margin: 0;
    padding: 0;
}

/* 状态栏样式 */
.status-bar {
    width: 100%;
    height: 24px;
    background-color: #4AA9E9; /* 使用指定的蓝色 */
    position: fixed;
    top: 0;
    left: 0;
    z-index: 1001;
    color: white;
    font-size: 12px;
    transition: background-color 0.3s ease; /* 添加过渡效果 */
}

/* 当滚动到登录框时的状态栏样式 */
.status-bar.scrolled {
    background-color: white;
}

.status-bar-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 10px;
    height: 100%;
}

.status-time {
    font-weight: 500;
}

.status-icons {
    display: flex;
    gap: 8px;
}

.status-icon {
    display: flex;
    align-items: center;
}

/* 顶部区域样式 */
.top-header {
    width: 100%;
    height: 480px; /* 增加高度以匹配新图片 */
    position: relative;
    overflow: hidden;
    margin-bottom: 0; /* 移除底部边距 */
    margin-top: 0; /* 移除顶部边距，直接连接状态栏 */
}

.header-bg {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image: url('https://s1.imagehub.cc/images/2025/05/15/b05f1273191be02e1fb6eac9d0fa8eaf.png');
    background-size: cover;
    background-position: center top;
    z-index: 1;
}

/* 移除波浪效果，使用背景图片 */

.header-content {
    position: relative;
    z-index: 2;
    width: 100%;
    height: 100%;
    /* 保留空的header-content，但确保它占据整个顶部区域 */
}

/* 移除不再需要的样式，因为背景图片已包含标题和装饰元素 */

@keyframes float {
    0%, 100% {
        transform: translateY(0);
    }
    50% {
        transform: translateY(-20px);
    }
}

/* 搜索框样式 */
.search-container {
    margin-top: 25px;
    width: 100%;
    max-width: 500px;
}

.search-box {
    background-color: white;
    border-radius: 30px;
    display: flex;
    align-items: center;
    padding: 8px 15px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    position: relative;
}

.search-icon {
    color: #aaa;
    margin-right: 10px;
}

.search-input-container {
    flex: 1;
    position: relative;
}

.search-input {
    width: 100%;
    border: none;
    outline: none;
    font-size: 16px;
    padding: 5px 0;
    background: transparent;
}

.search-placeholder {
    position: absolute;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
    display: flex;
    align-items: center;
    color: #aaa;
    pointer-events: none;
    background-color: white;
    padding: 0 5px;
    border-radius: 10px;
}

.user-icon {
    display: flex;
    align-items: center;
    color: #40E0D0;
    margin-right: 5px;
}

.divider {
    color: #ddd;
}

@keyframes float {
    0%, 100% {
        transform: translateY(0);
    }
    50% {
        transform: translateY(-20px);
    }
}

.container {
    width: 100%;
    padding: 0;
    position: relative;
    margin-top: -120px; /* 减少重叠程度，避免遮挡"趁年轻，就趣玩"的文字 */
    z-index: 10; /* 确保在渐变背景上方 */
}

/* 登录卡片样式 */
.login-card {
    background-color: white;
    border-radius: 20px 20px 0 0; /* 左右上角圆缺角 */
    box-shadow: 0 -10px 30px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    padding: 30px;
    width: 100%;
    position: relative;
}

.logo {
    text-align: center;
    margin-bottom: 15px;
}

.logo h1 {
    font-size: 1.6rem;
    color: #1E90FF;
    font-weight: bold;
}

/* 标签页样式 */
.tabs {
    display: flex;
    margin-bottom: 20px;
    border-bottom: 1px solid #eee;
}

.tab {
    flex: 1;
    text-align: center;
    padding: 8px 0;
    cursor: pointer;
    font-weight: 500;
    color: #888;
    transition: all 0.3s ease;
}

.tab.active {
    color: #1E90FF;
    border-bottom: 2px solid #1E90FF;
}

/* 表单样式 */
.form-container {
    position: relative;
}

.form {
    display: none;
}

.form.active {
    display: block;
}

.input-group {
    position: relative;
    margin-bottom: 15px;
}

.input-icon {
    position: absolute;
    left: 15px;
    top: 50%;
    transform: translateY(-50%);
    color: #aaa;
    z-index: 2; /* 确保图标在最上层 */
}

input[type="text"],
input[type="password"],
input[type="email"],
input[type="tel"] {
    width: 100%;
    padding: 15px 15px 15px 45px;
    border: 1px solid #e0e0e0;
    border-radius: 30px;
    font-size: 16px;
    transition: all 0.3s ease;
    outline: none;
    background-color: transparent;
}

/* 隐藏placeholder */
.floating-label input::placeholder {
    color: transparent;
}

input[type="text"]:focus,
input[type="password"]:focus,
input[type="email"]:focus,
input[type="tel"]:focus {
    border-color: #1E90FF;
    box-shadow: 0 0 0 2px rgba(30, 144, 255, 0.1);
    background-color: rgba(255, 255, 255, 0.9);
}

/* 浮动标签样式 */
.floating-label {
    position: relative;
}

.floating-label label {
    position: absolute;
    left: 45px;
    top: 50%;
    transform: translateY(-50%);
    font-size: 16px;
    color: #888;
    pointer-events: none;
    transition: all 0.3s ease;
}

.floating-label input:focus ~ label,
.floating-label input.has-value ~ label {
    top: -6px; /* 调整到输入框上方 */
    left: 20px; /* 稍微缩进，避免与图标重叠 */
    font-size: 12px;
    color: #1E90FF;
    padding: 0 5px;
    transform: translateY(0);
    z-index: 1; /* 确保标签在输入框上方 */
    font-weight: 500;
    position: relative;
}

.floating-label input:focus ~ label:before,
.floating-label input.has-value ~ label:before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: white;
    z-index: -1;
    border-radius: 10px;
}

.floating-label.focused label {
    color: #1E90FF;
}

.floating-label input {
    padding-top: 20px;
    padding-bottom: 10px;
    height: 55px;
    position: relative;
    z-index: 0; /* 确保输入框在图标下方，标签上方 */
}

/* 添加动画效果 */
.floating-label label {
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

.toggle-password {
    position: absolute;
    right: 15px;
    top: 50%;
    transform: translateY(-50%);
    cursor: pointer;
    color: #aaa;
}

.form-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    font-size: 14px;
}

.remember-me {
    display: flex;
    align-items: center;
}

.remember-me input {
    margin-right: 5px;
}

.forgot-password {
    color: #1E90FF;
    text-decoration: none;
    position: relative;
    transition: all 0.3s;
}

.forgot-password:after {
    content: '';
    position: absolute;
    width: 100%;
    height: 1px;
    bottom: -2px;
    left: 0;
    background: #1E90FF;
    transform: scaleX(0);
    transform-origin: bottom right;
    transition: transform 0.3s;
}

.forgot-password:hover:after {
    transform: scaleX(1);
    transform-origin: bottom left;
}

.btn-submit {
    width: 100%;
    padding: 15px;
    background: #1E90FF;
    color: white;
    border: none;
    border-radius: 30px;
    font-size: 16px;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 10px rgba(30, 144, 255, 0.3);
}

.btn-submit:hover {
    background: #4BA3FF;
    transform: translateY(-3px);
    box-shadow: 0 6px 15px rgba(30, 144, 255, 0.4);
}

/* Toast提示样式 */
.toast {
    position: fixed;
    top: 80px; /* 调整位置，避免被状态栏遮挡 */
    left: 50%;
    transform: translateX(-50%);
    background-color: rgba(255, 255, 255, 0.95);
    color: #333333;
    padding: 14px 24px;
    border-radius: 12px;
    box-shadow: 0 6px 16px rgba(0, 0, 0, 0.12);
    z-index: 9999; /* 确保在最上层 */
    display: none;
    border-left: 4px solid #1E90FF;
    font-size: 15px;
    font-weight: 500;
    max-width: 90%;
    text-align: center;
    pointer-events: none; /* 防止toast阻挡点击 */
}

@keyframes fadeIn {
    from { opacity: 0; transform: translate(-50%, -20px); }
    to { opacity: 1; transform: translate(-50%, 0); }
}

@keyframes fadeOut {
    from { opacity: 1; transform: translate(-50%, 0); }
    to { opacity: 0; transform: translate(-50%, -20px); }
}

/* 添加弹跳效果 */
@keyframes bounce {
    0%, 20%, 50%, 80%, 100% {transform: translate(-50%, 0);}
    40% {transform: translate(-50%, -5px);}
    60% {transform: translate(-50%, -2px);}
}

/* 添加按钮波纹效果 */
@keyframes ripple {
    0% {
        transform: translate(-50%, -50%) scale(0);
        opacity: 1;
    }
    100% {
        transform: translate(-50%, -50%) scale(3);
        opacity: 0;
    }
}

/* 验证码样式 */
.captcha-group {
    position: relative;
}

.captcha-container {
    position: absolute;
    right: 10px;
    top: 50%;
    transform: translateY(-50%);
    display: flex;
    align-items: center;
    height: 36px;
    z-index: 3;
}

.captcha-text {
    font-size: 16px;
    font-weight: bold;
    color: #333;
    letter-spacing: 2px;
    margin-right: 10px;
    min-width: 60px;
    text-align: center;
}

.refresh-captcha-btn {
    padding: 5px 10px;
    background: #1E90FF;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 12px;
    transition: all 0.3s ease;
    white-space: nowrap;
    box-shadow: 0 2px 5px rgba(30, 144, 255, 0.2);
    min-width: 90px;
    text-align: center;
}

.refresh-captcha-btn:hover {
    background: #4BA3FF;
    transform: translateY(-1px);
    box-shadow: 0 3px 8px rgba(30, 144, 255, 0.3);
}

.refresh-captcha-btn:disabled {
    background: #cccccc;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
    color: #666;
    font-size: 11px;
    width: 90px;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

#captcha {
    padding-right: 120px;
}

/* 协议勾选框样式 */
.agreement-checkbox {
    margin: 10px 0 15px;
    font-size: 13px;
    color: #666;
    display: flex;
    align-items: center;
}

.agreement-checkbox input[type="checkbox"] {
    margin-right: 8px;
    width: 16px;
    height: 16px;
    accent-color: #1E90FF;
    cursor: pointer;
    transition: all 0.3s;
}

.agreement-checkbox input[type="checkbox"]:checked {
    accent-color: #1E90FF;
    box-shadow: 0 0 0 2px rgba(30, 144, 255, 0.1);
}

.agreement-checkbox label {
    cursor: pointer;
    user-select: none;
    line-height: 1.4;
}

.agreement-checkbox a {
    color: #1E90FF;
    text-decoration: none;
    position: relative;
    transition: all 0.3s;
}

.agreement-checkbox a:after {
    content: '';
    position: absolute;
    width: 100%;
    height: 1px;
    bottom: -1px;
    left: 0;
    background: #1E90FF;
    transform: scaleX(0);
    transform-origin: bottom right;
    transition: transform 0.3s;
}

.agreement-checkbox a:hover:after {
    transform: scaleX(1);
    transform-origin: bottom left;
}

/* 错误状态 */
.agreement-checkbox.error {
    animation: shake 0.5s cubic-bezier(.36,.07,.19,.97) both;
}

.agreement-checkbox.error label {
    color: #ff3860;
}

@keyframes shake {
    10%, 90% { transform: translate3d(-1px, 0, 0); }
    20%, 80% { transform: translate3d(2px, 0, 0); }
    30%, 50%, 70% { transform: translate3d(-3px, 0, 0); }
    40%, 60% { transform: translate3d(3px, 0, 0); }
}

/* 响应式调整 */
@media (max-width: 768px) {
    .top-header {
        height: 450px; /* 增加高度以匹配新图片 */
    }

    .container {
        margin-top: -100px; /* 减少移动端的重叠效果，避免遮挡文字 */
    }

    .login-card {
        padding: 20px; /* 减小移动端的内边距 */
    }
}

@media (max-width: 480px) {
    .container {
        padding: 0;
        margin-top: -90px; /* 减少小屏幕的重叠效果，避免遮挡文字 */
    }

    .login-card {
        padding: 15px;
        border-radius: 15px 15px 0 0; /* 稍微减小圆角 */
    }

    .top-header {
        height: 420px; /* 增加高度以匹配新图片 */
        margin-bottom: 0;
    }

    /* 移除不再需要的样式 */

    .search-box {
        padding: 6px 12px;
    }

    .search-input {
        font-size: 14px;
    }

    .search-placeholder {
        font-size: 14px;
    }

    /* 调整输入框在小屏幕上的大小 */
    input[type="text"],
    input[type="password"],
    input[type="email"],
    input[type="tel"] {
        padding: 12px 12px 12px 40px;
        font-size: 14px;
    }

    .floating-label input {
        height: 50px;
    }
}
