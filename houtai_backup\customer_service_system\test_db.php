<?php
// 测试数据库连接和数据
header('Content-Type: text/html; charset=UTF-8');
ini_set('default_charset', 'UTF-8');

session_start();

// 检查登录状态
if (!isset($_SESSION['cs_logged_in']) || !$_SESSION['cs_logged_in']) {
    echo '<h1>请先登录客服系统</h1>';
    echo '<a href="../login.php">点击登录</a>';
    exit;
}

require_once '../db_config.php';

echo '<h1>数据库连接测试</h1>';

try {
    $pdo = getDbConnection();
    echo '<p style="color: green;">✓ 数据库连接成功</p>';
    
    // 检查客服会话表
    echo '<h2>客服会话表检查：</h2>';
    $stmt = $pdo->query("SHOW TABLES LIKE 'customer_service_sessions'");
    if ($stmt->rowCount() > 0) {
        echo '<p style="color: green;">✓ customer_service_sessions 表存在</p>';
        
        // 查询会话数据
        $stmt = $pdo->query("SELECT COUNT(*) as count FROM customer_service_sessions");
        $result = $stmt->fetch();
        echo '<p>会话总数：' . $result['count'] . '</p>';
        
        // 查询等待中的会话
        $stmt = $pdo->query("SELECT COUNT(*) as count FROM customer_service_sessions WHERE status = 'waiting'");
        $result = $stmt->fetch();
        echo '<p>等待中的会话：' . $result['count'] . '</p>';
        
        // 显示前5个会话
        echo '<h3>前5个会话：</h3>';
        $stmt = $pdo->query("SELECT session_id, user_name, status, priority, started_at FROM customer_service_sessions ORDER BY started_at DESC LIMIT 5");
        $sessions = $stmt->fetchAll();
        
        if ($sessions) {
            echo '<table border="1" style="border-collapse: collapse;">';
            echo '<tr><th>会话ID</th><th>用户名</th><th>状态</th><th>优先级</th><th>开始时间</th></tr>';
            foreach ($sessions as $session) {
                echo '<tr>';
                echo '<td>' . htmlspecialchars($session['session_id']) . '</td>';
                echo '<td>' . htmlspecialchars($session['user_name']) . '</td>';
                echo '<td>' . htmlspecialchars($session['status']) . '</td>';
                echo '<td>' . htmlspecialchars($session['priority']) . '</td>';
                echo '<td>' . htmlspecialchars($session['started_at']) . '</td>';
                echo '</tr>';
            }
            echo '</table>';
        } else {
            echo '<p>没有找到会话数据</p>';
        }
    } else {
        echo '<p style="color: red;">✗ customer_service_sessions 表不存在</p>';
    }
    
    // 检查客服消息表
    echo '<h2>客服消息表检查：</h2>';
    $stmt = $pdo->query("SHOW TABLES LIKE 'customer_service_messages'");
    if ($stmt->rowCount() > 0) {
        echo '<p style="color: green;">✓ customer_service_messages 表存在</p>';
        
        $stmt = $pdo->query("SELECT COUNT(*) as count FROM customer_service_messages");
        $result = $stmt->fetch();
        echo '<p>消息总数：' . $result['count'] . '</p>';
    } else {
        echo '<p style="color: red;">✗ customer_service_messages 表不存在</p>';
    }
    
    // 检查客服用户表
    echo '<h2>客服用户表检查：</h2>';
    $stmt = $pdo->query("SHOW TABLES LIKE 'customer_service_users'");
    if ($stmt->rowCount() > 0) {
        echo '<p style="color: green;">✓ customer_service_users 表存在</p>';
        
        $stmt = $pdo->query("SELECT COUNT(*) as count FROM customer_service_users WHERE status = 'active'");
        $result = $stmt->fetch();
        echo '<p>活跃客服数：' . $result['count'] . '</p>';
        
        // 显示当前登录的客服信息
        echo '<h3>当前登录客服信息：</h3>';
        echo '<p>客服ID：' . ($_SESSION['cs_user_id'] ?? 'N/A') . '</p>';
        echo '<p>客服姓名：' . ($_SESSION['cs_name'] ?? 'N/A') . '</p>';
        echo '<p>工号：' . ($_SESSION['cs_employee_id'] ?? 'N/A') . '</p>';
        echo '<p>角色：' . ($_SESSION['cs_role'] ?? 'N/A') . '</p>';
    } else {
        echo '<p style="color: red;">✗ customer_service_users 表不存在</p>';
    }
    
    // 检查实时通知表
    echo '<h2>实时通知表检查：</h2>';
    $stmt = $pdo->query("SHOW TABLES LIKE 'realtime_notifications'");
    if ($stmt->rowCount() > 0) {
        echo '<p style="color: green;">✓ realtime_notifications 表存在</p>';
    } else {
        echo '<p style="color: red;">✗ realtime_notifications 表不存在</p>';
    }
    
} catch (Exception $e) {
    echo '<p style="color: red;">✗ 数据库错误：' . $e->getMessage() . '</p>';
}
?>

<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>数据库测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        table { margin: 10px 0; }
        th, td { padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
    </style>
</head>
<body>
    <p><a href="sessions.php">返回会话列表</a></p>
    <p><a href="test_accept_api.php">测试接受API</a></p>
</body>
</html>
