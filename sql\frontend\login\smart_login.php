<?php
session_start();

// 如果用户已登录，重定向到首页
if (isset($_SESSION['user_id'])) {
    header('Location: ../home/<USER>');
    exit;
}
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0, user-scalable=no, shrink-to-fit=no">
    <meta name="theme-color" content="#0a0a2e">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">
    <meta name="msapplication-navbutton-color" content="#0a0a2e">
    <title>智能登录 - 趣玩星球</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="../css/page-protection.css">
    <style>
        /* CSS变量定义 */
        :root {
            --primary-color: #40E0D0;
            --primary-light: #AFFBF2;
            --primary-dark: #20B2AA;
            --secondary-color: #06D6A0;
            --accent-color: #FF6B9D;
            --gradient-primary: linear-gradient(135deg, #40E0D0, #06D6A0);
            --text-primary: #333333;
            --text-secondary: #666666;
            --text-light: #999999;
            --bg-white: #FFFFFF;
            --bg-light: #F8F9FA;
            --shadow-sm: 0 2px 8px rgba(64, 224, 208, 0.08);
            --shadow-md: 0 4px 16px rgba(64, 224, 208, 0.12);
            --shadow-lg: 0 8px 32px rgba(64, 224, 208, 0.16);
            --radius-md: 12px;
            --radius-lg: 16px;
            --radius-xl: 24px;
            --transition-normal: 0.3s ease;
        }

        /* 防止内容被选择 */
        * {
            -webkit-touch-callout: none;
            -webkit-user-select: none;
            -khtml-user-select: none;
            -moz-user-select: none;
            -ms-user-select: none;
            user-select: none;
        }

        body {
            background: linear-gradient(135deg, #0a0a2e 0%, #16213e 25%, #1a1a3a 50%, #0f3460 75%, #0a0a2e 100%);
            margin: 0;
            padding: 0;
            min-height: 100vh;
            overflow-x: hidden;
            font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;
            position: relative;
        }

        /* 星空背景 */
        .space-background {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: 1;
        }

        /* 星球系统 */
        .planet-system {
            position: fixed;
            top: 60px;
            left: 50%;
            transform: translateX(-50%);
            width: 200px;
            height: 200px;
            z-index: 2;
        }

        .central-planet {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 60px;
            height: 60px;
            background: radial-gradient(circle at 30% 30%, var(--primary-light), var(--primary-color), var(--primary-dark));
            border-radius: 50%;
            box-shadow: 0 0 30px rgba(64, 224, 208, 0.3);
            animation: planetPulse 4s ease-in-out infinite;
        }

        /* 主容器 */
        .login-container {
            position: relative;
            z-index: 10;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            padding: 20px 5px;
        }

        /* 登录卡片 */
        .login-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: var(--radius-xl);
            padding: 32px 24px;
            width: 100%;
            max-width: 400px;
            box-shadow: var(--shadow-lg);
            border: 1px solid rgba(64, 224, 208, 0.1);
            transition: var(--transition-normal);
        }

        /* 标题 */
        .login-title {
            text-align: center;
            margin-bottom: 32px;
        }

        .login-title h1 {
            color: var(--text-primary);
            font-size: 28px;
            font-weight: 600;
            margin: 0 0 8px 0;
        }

        .login-title p {
            color: var(--text-secondary);
            font-size: 14px;
            margin: 0;
        }

        /* 表单组 */
        .form-group {
            margin-bottom: 24px;
        }

        .form-group label {
            display: block;
            color: var(--text-primary);
            font-size: 14px;
            font-weight: 500;
            margin-bottom: 8px;
        }

        .form-input {
            width: 100%;
            padding: 16px;
            border: 2px solid #E5E7EB;
            border-radius: var(--radius-md);
            font-size: 16px;
            transition: var(--transition-normal);
            background: var(--bg-white);
            box-sizing: border-box;
        }

        .form-input:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(64, 224, 208, 0.1);
        }

        /* 用户信息显示 */
        .user-info {
            display: none;
            text-align: center;
            margin-bottom: 24px;
            padding: 20px;
            background: linear-gradient(135deg, rgba(64, 224, 208, 0.1), rgba(6, 214, 160, 0.1));
            border-radius: var(--radius-lg);
            border: 1px solid rgba(64, 224, 208, 0.2);
        }

        .user-avatar {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            margin: 0 auto 16px;
            border: 3px solid var(--primary-color);
            object-fit: cover;
        }

        .user-name {
            font-size: 18px;
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 4px;
        }

        .user-id {
            font-size: 14px;
            color: var(--text-secondary);
            margin-bottom: 8px;
        }

        .security-status {
            display: inline-flex;
            align-items: center;
            gap: 6px;
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 500;
        }

        .security-status.trusted {
            background: rgba(34, 197, 94, 0.1);
            color: #059669;
        }

        .security-status.risk {
            background: rgba(239, 68, 68, 0.1);
            color: #DC2626;
        }

        /* 按钮 */
        .btn {
            width: 100%;
            padding: 16px;
            border: none;
            border-radius: var(--radius-md);
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: var(--transition-normal);
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
        }

        .btn-primary {
            background: var(--gradient-primary);
            color: white;
            box-shadow: var(--shadow-md);
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-lg);
        }

        .btn-primary:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        /* 加载状态 */
        .loading {
            display: none;
            text-align: center;
            padding: 20px;
        }

        .spinner {
            width: 40px;
            height: 40px;
            border: 3px solid rgba(64, 224, 208, 0.3);
            border-top: 3px solid var(--primary-color);
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto 16px;
        }

        /* 验证码输入区域 */
        .verification-section {
            display: none;
        }

        .verification-input-group {
            display: flex;
            gap: 8px;
            margin-bottom: 16px;
        }

        .verification-input {
            flex: 1;
            text-align: center;
            font-size: 18px;
            font-weight: 600;
            padding: 12px 8px;
            border: 2px solid #E5E7EB;
            border-radius: var(--radius-md);
            transition: var(--transition-normal);
        }

        .verification-input:focus {
            outline: none;
            border-color: var(--primary-color);
        }

        /* 密码输入区域 */
        .password-section {
            display: none;
        }

        /* 错误提示 */
        .error-message {
            display: none;
            background: rgba(239, 68, 68, 0.1);
            color: #DC2626;
            padding: 12px 16px;
            border-radius: var(--radius-md);
            font-size: 14px;
            margin-bottom: 16px;
            border: 1px solid rgba(239, 68, 68, 0.2);
        }

        /* 动画 */
        @keyframes planetPulse {
            0%, 100% { transform: translate(-50%, -50%) scale(1); }
            50% { transform: translate(-50%, -50%) scale(1.1); }
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* 响应式设计 */
        @media (max-width: 480px) {
            .login-card {
                margin: 0 5px;
                padding: 24px 20px;
            }

            .planet-system {
                width: 150px;
                height: 150px;
                top: 40px;
            }

            .central-planet {
                width: 45px;
                height: 45px;
            }
        }

        /* 短信弹窗样式 */
        .sms-modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.8);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 10000;
        }

        .sms-content {
            background: #f8f9fa;
            border-radius: 12px;
            padding: 24px;
            margin: 20px;
            max-width: 400px;
            width: 90%;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
        }

        .sms-header {
            font-weight: bold;
            color: #333;
            margin-bottom: 16px;
            font-size: 16px;
        }

        .sms-body {
            color: #666;
            line-height: 1.6;
            margin-bottom: 24px;
            font-size: 14px;
        }

        .verification-code {
            background: var(--primary-color);
            color: white;
            padding: 12px 16px;
            border-radius: 8px;
            font-size: 24px;
            font-weight: bold;
            text-align: center;
            margin: 16px 0;
            letter-spacing: 4px;
        }

        .btn-secondary {
            background: #6c757d;
            color: white;
            margin-left: 8px;
        }

        .btn-secondary:hover {
            background: #5a6268;
        }

        /* 验证码操作按钮组 */
        .verification-actions {
            display: flex;
            gap: 12px;
            margin-top: 16px;
            margin-bottom: 20px;
        }

        .btn-send-code {
            flex: 2;
            padding: 12px 16px;
            background: var(--primary-color);
            color: white;
            border: none;
            border-radius: var(--radius-md);
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: var(--transition-normal);
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 6px;
        }

        .btn-send-code:hover {
            background: var(--primary-dark);
            transform: translateY(-1px);
        }

        .btn-send-code:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .btn-view-sms {
            flex: 1;
            padding: 12px 16px;
            background: rgba(108, 117, 125, 0.1);
            color: var(--text-secondary);
            border: 1px solid rgba(108, 117, 125, 0.3);
            border-radius: var(--radius-md);
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: var(--transition-normal);
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 6px;
        }

        .btn-view-sms:hover {
            background: rgba(108, 117, 125, 0.2);
            border-color: rgba(108, 117, 125, 0.5);
        }

        /* 优化验证码输入框样式 */
        .verification-input-group {
            display: flex;
            gap: 8px;
            margin-bottom: 0;
            justify-content: center;
        }

        .verification-input {
            width: 36px;
            height: 40px;
            text-align: center;
            font-size: 18px;
            font-weight: 600;
            border: 2px solid #E5E7EB;
            border-radius: 8px;
            transition: var(--transition-normal);
            background: var(--bg-white);
            /* 强制数字键盘 */
            -webkit-appearance: none;
            -moz-appearance: textfield;
        }

        .verification-input:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 2px rgba(64, 224, 208, 0.1);
        }

        .verification-input.filled {
            border-color: var(--primary-color);
            background: rgba(64, 224, 208, 0.05);
            color: var(--primary-color);
        }

        /* 隐藏数字输入框的上下箭头 */
        .verification-input::-webkit-outer-spin-button,
        .verification-input::-webkit-inner-spin-button {
            -webkit-appearance: none;
            margin: 0;
        }
    </style>
</head>
<body>
    <!-- 星空背景 -->
    <div class="space-background">
        <div class="planet-system">
            <div class="central-planet"></div>
        </div>
    </div>

    <!-- 主容器 -->
    <div class="login-container">
        <div class="login-card">
            <!-- 标题 -->
            <div class="login-title">
                <h1>智能登录</h1>
                <p>安全便捷的登录体验</p>
            </div>

            <!-- 错误提示 -->
            <div class="error-message" id="errorMessage"></div>

            <!-- 第一步：输入手机号/趣玩ID -->
            <div class="step-1" id="step1">
                <div class="form-group">
                    <label for="identifier">手机号 / 趣玩ID</label>
                    <input type="text" id="identifier" class="form-input" placeholder="请输入手机号或趣玩ID" maxlength="11">
                </div>
                <button type="button" class="btn btn-primary" id="checkBtn">
                    <i class="fas fa-search"></i>
                    查找账户
                </button>
            </div>

            <!-- 加载状态 -->
            <div class="loading" id="loading">
                <div class="spinner"></div>
                <p>正在进行安全检测...</p>
            </div>

            <!-- 第二步：用户信息显示 -->
            <div class="step-2" id="step2" style="display: none;">
                <div class="user-info" id="userInfo">
                    <img src="" alt="用户头像" class="user-avatar" id="userAvatar">
                    <div class="user-name" id="userName"></div>
                    <div class="user-id" id="userId"></div>
                    <div class="security-status" id="securityStatus">
                        <i class="fas fa-shield-alt"></i>
                        <span id="securityText"></span>
                    </div>
                </div>

                <!-- 快速登录按钮 -->
                <button type="button" class="btn btn-primary" id="quickLoginBtn" style="display: none;">
                    <i class="fas fa-sign-in-alt"></i>
                    快速登录
                </button>

                <!-- 验证码验证 -->
                <div class="verification-section" id="verificationSection">
                    <!-- 验证码输入区域 -->
                    <div class="form-group">
                        <label>输入验证码</label>
                        <p style="font-size: 14px; color: var(--text-secondary); margin-bottom: 16px;">
                            验证码已发送至 <span id="phone-display">***</span>
                        </p>

                        <!-- 6位验证码输入框 -->
                        <div class="verification-input-group">
                            <input type="tel" class="verification-input" maxlength="1" data-index="0" inputmode="numeric" pattern="[0-9]" autocomplete="off">
                            <input type="tel" class="verification-input" maxlength="1" data-index="1" inputmode="numeric" pattern="[0-9]" autocomplete="off">
                            <input type="tel" class="verification-input" maxlength="1" data-index="2" inputmode="numeric" pattern="[0-9]" autocomplete="off">
                            <input type="tel" class="verification-input" maxlength="1" data-index="3" inputmode="numeric" pattern="[0-9]" autocomplete="off">
                            <input type="tel" class="verification-input" maxlength="1" data-index="4" inputmode="numeric" pattern="[0-9]" autocomplete="off">
                            <input type="tel" class="verification-input" maxlength="1" data-index="5" inputmode="numeric" pattern="[0-9]" autocomplete="off">
                        </div>

                        <!-- 发送验证码和查看短信按钮组 -->
                        <div class="verification-actions">
                            <button type="button" class="btn-send-code" id="sendCodeBtn">
                                <i class="fas fa-paper-plane"></i>
                                发送验证码
                            </button>
                            <button type="button" class="btn-view-sms" onclick="showSmsModal()">
                                <i class="fas fa-eye"></i>
                                查看短信
                            </button>
                        </div>
                    </div>

                    <!-- 密码输入 -->
                    <div class="password-section" id="passwordSection" style="display: none;">
                        <div class="form-group">
                            <label for="password">登录密码</label>
                            <input type="password" id="password" class="form-input" placeholder="请输入登录密码">
                        </div>
                    </div>

                    <!-- 登录按钮 -->
                    <button type="button" class="btn btn-primary" id="secureLoginBtn" style="margin-top: 24px;">
                        <i class="fas fa-lock"></i>
                        安全登录
                    </button>
                </div>

                <button type="button" class="btn" id="backBtn" style="background: #6B7280; color: white; margin-top: 16px;">
                    <i class="fas fa-arrow-left"></i>
                    返回
                </button>
            </div>
        </div>
    </div>

    <!-- 短信弹窗 -->
    <div class="sms-modal" id="sms-modal" style="display: none;">
        <div class="sms-content">
            <div class="sms-header">【趣玩星球】</div>
            <div class="sms-body">
                Hi~ 欢迎回到趣玩星球！您的验证码：
                <div class="verification-code" id="verification-code-display">------</div>
                验证码有效期为5分钟，请勿将验证码泄露给他人，以免造成不必要的损失。如非本人操作，请忽略此短信。
            </div>
            <button type="button" class="btn btn-primary" onclick="copyAndFillCode()">
                <i class="fas fa-copy"></i>
                复制并填入验证码
            </button>
            <button type="button" class="btn btn-secondary" onclick="closeSmsModal()">
                关闭
            </button>
        </div>
    </div>

    <script>
        // 智能登录JavaScript逻辑
        class SmartLogin {
            constructor() {
                this.currentUser = null;
                this.securityResult = null;
                this.verificationCode = '';
                this.init();
            }

            init() {
                this.bindEvents();
            }

            bindEvents() {
                // 查找账户按钮
                document.getElementById('checkBtn').addEventListener('click', () => {
                    this.checkAccount();
                });

                // 输入框回车事件
                document.getElementById('identifier').addEventListener('keypress', (e) => {
                    if (e.key === 'Enter') {
                        this.checkAccount();
                    }
                });

                // 快速登录按钮
                document.getElementById('quickLoginBtn').addEventListener('click', () => {
                    this.quickLogin();
                });

                // 发送验证码按钮
                document.getElementById('sendCodeBtn').addEventListener('click', () => {
                    this.sendVerificationCode();
                });

                // 安全登录按钮
                document.getElementById('secureLoginBtn').addEventListener('click', () => {
                    this.secureLogin();
                });

                // 返回按钮
                document.getElementById('backBtn').addEventListener('click', () => {
                    this.goBack();
                });

                // 验证码输入框事件
                this.bindVerificationInputs();
            }

            bindVerificationInputs() {
                const inputs = document.querySelectorAll('.verification-input');
                inputs.forEach((input, index) => {
                    // 输入事件处理
                    input.addEventListener('input', (e) => {
                        let value = e.target.value;

                        // 只允许数字，移除非数字字符
                        value = value.replace(/[^0-9]/g, '');

                        // 如果输入了多个字符，只取第一个
                        if (value.length > 1) {
                            value = value.charAt(0);
                        }

                        e.target.value = value;

                        // 添加填充样式
                        if (value) {
                            e.target.classList.add('filled');
                            // 自动跳转到下一个输入框
                            if (index < inputs.length - 1) {
                                setTimeout(() => {
                                    inputs[index + 1].focus();
                                }, 50); // 小延迟确保输入完成
                            }
                        } else {
                            e.target.classList.remove('filled');
                        }

                        this.updateVerificationCode();
                    });

                    // 键盘事件处理
                    input.addEventListener('keydown', (e) => {
                        // 退格键处理
                        if (e.key === 'Backspace') {
                            if (!e.target.value && index > 0) {
                                // 当前框为空，跳转到上一个框并清空
                                setTimeout(() => {
                                    inputs[index - 1].focus();
                                    inputs[index - 1].value = '';
                                    inputs[index - 1].classList.remove('filled');
                                    this.updateVerificationCode();
                                }, 50);
                            }
                        }

                        // 左右箭头键导航
                        if (e.key === 'ArrowLeft' && index > 0) {
                            inputs[index - 1].focus();
                        }
                        if (e.key === 'ArrowRight' && index < inputs.length - 1) {
                            inputs[index + 1].focus();
                        }
                    });

                    // 粘贴事件处理
                    input.addEventListener('paste', (e) => {
                        e.preventDefault();
                        const paste = e.clipboardData.getData('text').replace(/[^0-9]/g, '');

                        if (paste.length >= 6) {
                            // 粘贴6位或更多数字
                            inputs.forEach((inp, i) => {
                                if (i < 6 && paste[i]) {
                                    inp.value = paste[i];
                                    inp.classList.add('filled');
                                } else {
                                    inp.value = '';
                                    inp.classList.remove('filled');
                                }
                            });
                            // 聚焦到最后一个输入框
                            inputs[5].focus();
                        } else if (paste.length > 0) {
                            // 粘贴少于6位数字，从当前位置开始填充
                            for (let i = 0; i < paste.length && (index + i) < inputs.length; i++) {
                                inputs[index + i].value = paste[i];
                                inputs[index + i].classList.add('filled');
                            }
                            // 聚焦到下一个空位置
                            const nextIndex = Math.min(index + paste.length, inputs.length - 1);
                            inputs[nextIndex].focus();
                        }

                        this.updateVerificationCode();
                    });

                    // 聚焦事件处理
                    input.addEventListener('focus', (e) => {
                        // 聚焦时选中内容，方便替换
                        setTimeout(() => {
                            e.target.select();
                        }, 50);
                    });
                });
            }

            updateVerificationCode() {
                const inputs = document.querySelectorAll('.verification-input');
                this.verificationCode = Array.from(inputs).map(input => input.value).join('');

                if (this.verificationCode.length === 6) {
                    document.getElementById('passwordSection').style.display = 'block';
                }
            }

            async checkAccount() {
                const identifier = document.getElementById('identifier').value.trim();

                if (!identifier) {
                    this.showError('请输入手机号或趣玩ID');
                    return;
                }

                // 验证格式
                if (!/^\d{11}$/.test(identifier) && !/^\d{7,9}$/.test(identifier)) {
                    this.showError('请输入正确的手机号（11位）或趣玩ID（7-9位）');
                    return;
                }

                this.showLoading();

                try {
                    const response = await fetch('security_check_simple.php', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({ identifier })
                    });

                    const result = await response.json();

                    if (result.success) {
                        this.currentUser = result.user;
                        this.securityResult = result.security;
                        this.showUserInfo();
                    } else {
                        this.showError(result.message);
                        this.hideLoading();
                    }
                } catch (error) {
                    console.error('安全检测失败:', error);
                    this.showError('网络错误，请稍后重试');
                    this.hideLoading();
                }
            }

            showUserInfo() {
                this.hideLoading();

                // 隐藏第一步
                document.getElementById('step1').style.display = 'none';

                // 显示用户信息
                document.getElementById('userAvatar').src = this.currentUser.avatar;
                document.getElementById('userName').textContent = this.currentUser.username;
                document.getElementById('userId').textContent = `趣玩ID: ${this.currentUser.quwan_id}`;

                // 显示安全状态
                const securityStatus = document.getElementById('securityStatus');
                const securityText = document.getElementById('securityText');

                if (this.securityResult.trusted) {
                    securityStatus.className = 'security-status trusted';
                    securityText.textContent = '可信环境';
                    document.getElementById('quickLoginBtn').style.display = 'block';
                } else {
                    securityStatus.className = 'security-status risk';
                    securityText.textContent = '需要验证';
                    document.getElementById('verificationSection').style.display = 'block';

                    // 显示手机号信息
                    const phoneDisplay = document.getElementById('phone-display');
                    if (phoneDisplay && this.currentUser.phone) {
                        const maskedPhone = this.currentUser.phone.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2');
                        phoneDisplay.textContent = maskedPhone;
                    }
                }

                // 显示用户信息区域
                document.getElementById('userInfo').style.display = 'block';
                document.getElementById('step2').style.display = 'block';
            }

            async quickLogin() {
                const btn = document.getElementById('quickLoginBtn');
                btn.disabled = true;
                btn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 登录中...';

                try {
                    const response = await fetch('quick_login_simple.php', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({ user_id: this.currentUser.id })
                    });

                    const result = await response.json();

                    if (result.success) {
                        window.location.href = result.redirect;
                    } else {
                        this.showError(result.message);
                        btn.disabled = false;
                        btn.innerHTML = '<i class="fas fa-sign-in-alt"></i> 快速登录';
                    }
                } catch (error) {
                    console.error('快速登录失败:', error);
                    this.showError('登录失败，请稍后重试');
                    btn.disabled = false;
                    btn.innerHTML = '<i class="fas fa-sign-in-alt"></i> 快速登录';
                }
            }

            async sendVerificationCode() {
                const btn = document.getElementById('sendCodeBtn');
                btn.disabled = true;
                btn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 发送中...';

                try {
                    const response = await fetch('send_verification.php', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/x-www-form-urlencoded',
                        },
                        body: `phone=${encodeURIComponent(this.currentUser.phone || document.getElementById('identifier').value)}`
                    });

                    const result = await response.json();

                    if (result.success) {
                        this.startCountdown(btn);

                        // 更新短信弹窗中的验证码
                        if (result.debug_code) {
                            currentVerificationCode = result.debug_code;
                            document.getElementById('verification-code-display').textContent = result.debug_code;

                            // 3秒后自动显示短信弹窗
                            setTimeout(() => {
                                showSmsModal();
                            }, 3000);
                        }
                    } else {
                        this.showError(result.message);
                        btn.disabled = false;
                        btn.innerHTML = '<i class="fas fa-paper-plane"></i> 发送验证码';
                    }
                } catch (error) {
                    console.error('发送验证码失败:', error);
                    this.showError('发送失败，请稍后重试');
                    btn.disabled = false;
                    btn.innerHTML = '<i class="fas fa-paper-plane"></i> 发送验证码';
                }
            }

            startCountdown(btn) {
                let countdown = 60;
                const timer = setInterval(() => {
                    btn.innerHTML = `<i class="fas fa-clock"></i> ${countdown}秒后重发`;
                    countdown--;

                    if (countdown < 0) {
                        clearInterval(timer);
                        btn.disabled = false;
                        btn.innerHTML = '<i class="fas fa-paper-plane"></i> 重新发送';
                    }
                }, 1000);
            }

            async secureLogin() {
                const password = document.getElementById('password').value.trim();

                if (this.verificationCode.length !== 6) {
                    this.showError('请输入6位验证码');
                    return;
                }

                if (!password) {
                    this.showError('请输入登录密码');
                    return;
                }

                const btn = document.getElementById('secureLoginBtn');
                btn.disabled = true;
                btn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 登录中...';

                try {
                    const response = await fetch('secure_login_simple.php', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({
                            user_id: this.currentUser.id,
                            verification_code: this.verificationCode,
                            password: password
                        })
                    });

                    const result = await response.json();

                    if (result.success) {
                        window.location.href = result.redirect;
                    } else {
                        this.showError(result.message);
                        btn.disabled = false;
                        btn.innerHTML = '<i class="fas fa-lock"></i> 安全登录';
                    }
                } catch (error) {
                    console.error('安全登录失败:', error);
                    this.showError('登录失败，请稍后重试');
                    btn.disabled = false;
                    btn.innerHTML = '<i class="fas fa-lock"></i> 安全登录';
                }
            }

            goBack() {
                document.getElementById('step2').style.display = 'none';
                document.getElementById('step1').style.display = 'block';
                document.getElementById('identifier').value = '';
                document.getElementById('identifier').focus();
                this.hideError();

                // 重置验证码输入
                document.querySelectorAll('.verification-input').forEach(input => {
                    input.value = '';
                    input.classList.remove('filled');
                });
                document.getElementById('password').value = '';
                document.getElementById('passwordSection').style.display = 'none';
                this.verificationCode = '';
            }

            showLoading() {
                document.getElementById('step1').style.display = 'none';
                document.getElementById('loading').style.display = 'block';
            }

            hideLoading() {
                document.getElementById('loading').style.display = 'none';
            }

            showError(message) {
                const errorElement = document.getElementById('errorMessage');
                errorElement.textContent = message;
                errorElement.style.display = 'block';

                // 3秒后自动隐藏
                setTimeout(() => {
                    this.hideError();
                }, 3000);
            }

            hideError() {
                document.getElementById('errorMessage').style.display = 'none';
            }
        }

        // 短信弹窗相关函数
        let currentVerificationCode = '';

        function showSmsModal() {
            document.getElementById('sms-modal').style.display = 'flex';
        }

        function closeSmsModal() {
            document.getElementById('sms-modal').style.display = 'none';
        }

        function copyAndFillCode() {
            const code = currentVerificationCode;
            const inputs = document.querySelectorAll('.verification-input');

            // 填入验证码
            code.split('').forEach((digit, index) => {
                if (inputs[index]) {
                    inputs[index].value = digit;
                    inputs[index].classList.add('filled');
                }
            });

            // 更新验证码
            const smartLogin = window.smartLoginInstance;
            if (smartLogin) {
                smartLogin.updateVerificationCode();
            }

            // 关闭弹窗
            closeSmsModal();
        }

        // 初始化智能登录
        document.addEventListener('DOMContentLoaded', () => {
            window.smartLoginInstance = new SmartLogin();
        });
    </script>
</body>
</html>
