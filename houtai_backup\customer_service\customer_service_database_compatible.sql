-- =====================================================
-- 智能客服机器人数据库结构（兼容版本）
-- 适用于MySQL 5.7+
-- =====================================================

-- 1. 检查并创建机器人配置表
-- =====================================================
CREATE TABLE IF NOT EXISTS `customer_service_bot` (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `bot_name` VARCHAR(100) NOT NULL DEFAULT '趣玩小助手' COMMENT '机器人名称',
    `bot_avatar` VARCHAR(500) DEFAULT 'https://s1.imagehub.cc/images/2025/05/26/2cdd63b39f96072fc60cd733027d2a30.png' COMMENT '机器人头像',
    `welcome_message` TEXT NOT NULL COMMENT '欢迎消息',
    `default_reply` TEXT NOT NULL COMMENT '默认回复',
    `is_enabled` TINYINT(1) DEFAULT 1 COMMENT '是否启用',
    `bot_description` TEXT COMMENT '机器人描述',
    `working_hours` JSON COMMENT '工作时间配置',
    `auto_transfer_keywords` JSON COMMENT '自动转人工关键词',
    `max_session_duration` INT DEFAULT 1800 COMMENT '最大会话时长(秒)',
    `greeting_delay` INT DEFAULT 2 COMMENT '问候语延迟(秒)',
    `typing_delay` INT DEFAULT 1 COMMENT '打字延迟(秒)',
    `enable_smart_suggestions` TINYINT(1) DEFAULT 1 COMMENT '启用智能建议',
    `enable_satisfaction_survey` TINYINT(1) DEFAULT 1 COMMENT '启用满意度调查',
    `fallback_to_human` TINYINT(1) DEFAULT 1 COMMENT '无法回答时转人工',
    `collect_user_info` TINYINT(1) DEFAULT 0 COMMENT '是否收集用户信息',
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='客服机器人配置表';

-- 2. 检查并创建回复规则表
-- =====================================================
CREATE TABLE IF NOT EXISTS `customer_service_replies` (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `keywords` JSON NOT NULL COMMENT '关键词列表',
    `reply_content` TEXT NOT NULL COMMENT '回复内容',
    `priority` INT DEFAULT 1 COMMENT '优先级，数字越大优先级越高',
    `is_enabled` TINYINT(1) DEFAULT 1 COMMENT '是否启用',
    `rule_name` VARCHAR(100) COMMENT '规则名称',
    `rule_description` TEXT COMMENT '规则描述',
    `reply_type` ENUM('text', 'rich_text', 'quick_reply', 'card', 'form') DEFAULT 'text' COMMENT '回复类型',
    `reply_data` JSON COMMENT '回复数据(富文本、快捷回复等)',
    `trigger_conditions` JSON COMMENT '触发条件',
    `follow_up_actions` JSON COMMENT '后续动作',
    `category` VARCHAR(50) DEFAULT 'general' COMMENT '分类',
    `tags` JSON COMMENT '标签',
    `usage_count` INT DEFAULT 0 COMMENT '使用次数',
    `success_rate` DECIMAL(5,2) DEFAULT 0.00 COMMENT '成功率',
    `admin_id` INT COMMENT '创建者ID',
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX `idx_priority` (`priority`),
    INDEX `idx_enabled` (`is_enabled`),
    INDEX `idx_category` (`category`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='客服回复规则表';

-- 3. 检查并创建对话记录表
-- =====================================================
CREATE TABLE IF NOT EXISTS `customer_service_conversations` (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `session_id` VARCHAR(100) NOT NULL COMMENT '会话ID',
    `user_id` INT COMMENT '用户ID（如果已登录）',
    `user_message` TEXT NOT NULL COMMENT '用户消息',
    `bot_reply` TEXT COMMENT '机器人回复',
    `is_human_service` TINYINT(1) DEFAULT 0 COMMENT '是否转人工客服',
    `admin_id` INT COMMENT '客服人员ID',
    `admin_reply` TEXT COMMENT '人工客服回复',
    `message_type` ENUM('user', 'bot', 'admin') DEFAULT 'user' COMMENT '消息类型',
    `user_phone` VARCHAR(20) COMMENT '用户手机号',
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX `idx_session_id` (`session_id`),
    INDEX `idx_user_id` (`user_id`),
    INDEX `idx_created_at` (`created_at`),
    INDEX `idx_admin_id` (`admin_id`),
    INDEX `idx_message_type` (`message_type`),
    INDEX `idx_user_phone` (`user_phone`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='客服对话记录表';

-- 4. 创建快捷回复模板表
-- =====================================================
CREATE TABLE IF NOT EXISTS `customer_service_quick_replies` (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `title` VARCHAR(100) NOT NULL COMMENT '快捷回复标题',
    `content` TEXT NOT NULL COMMENT '回复内容',
    `category` VARCHAR(50) DEFAULT 'general' COMMENT '分类',
    `sort_order` INT DEFAULT 0 COMMENT '排序',
    `is_enabled` TINYINT(1) DEFAULT 1 COMMENT '是否启用',
    `usage_count` INT DEFAULT 0 COMMENT '使用次数',
    `admin_id` INT COMMENT '创建者ID',
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX `idx_category` (`category`),
    INDEX `idx_enabled` (`is_enabled`),
    INDEX `idx_sort` (`sort_order`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='快捷回复模板表';

-- 5. 创建用户信息收集表
-- =====================================================
CREATE TABLE IF NOT EXISTS `customer_service_user_info` (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `session_id` VARCHAR(100) NOT NULL COMMENT '会话ID',
    `user_id` INT COMMENT '用户ID',
    `collected_data` JSON COMMENT '收集的数据',
    `collection_status` ENUM('pending', 'completed', 'failed') DEFAULT 'pending' COMMENT '收集状态',
    `form_template_id` INT COMMENT '表单模板ID',
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX `idx_session_id` (`session_id`),
    INDEX `idx_user_id` (`user_id`),
    INDEX `idx_status` (`collection_status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户信息收集表';

-- 6. 创建表单模板表
-- =====================================================
CREATE TABLE IF NOT EXISTS `customer_service_form_templates` (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `template_name` VARCHAR(100) NOT NULL COMMENT '模板名称',
    `template_description` TEXT COMMENT '模板描述',
    `form_fields` JSON NOT NULL COMMENT '表单字段配置',
    `trigger_keywords` JSON COMMENT '触发关键词',
    `success_message` TEXT COMMENT '成功提示',
    `is_enabled` TINYINT(1) DEFAULT 1 COMMENT '是否启用',
    `admin_id` INT COMMENT '创建者ID',
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX `idx_enabled` (`is_enabled`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='表单模板表';

-- 7. 创建满意度调查表
-- =====================================================
CREATE TABLE IF NOT EXISTS `customer_service_satisfaction` (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `session_id` VARCHAR(100) NOT NULL COMMENT '会话ID',
    `user_id` INT COMMENT '用户ID',
    `rating` TINYINT(1) COMMENT '评分(1-5)',
    `feedback` TEXT COMMENT '反馈内容',
    `tags` JSON COMMENT '标签',
    `admin_id` INT COMMENT '客服ID',
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX `idx_session_id` (`session_id`),
    INDEX `idx_rating` (`rating`),
    INDEX `idx_admin_id` (`admin_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='满意度调查表';

-- 8. 创建智能建议表
-- =====================================================
CREATE TABLE IF NOT EXISTS `customer_service_suggestions` (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `suggestion_text` VARCHAR(200) NOT NULL COMMENT '建议文本',
    `trigger_keywords` JSON COMMENT '触发关键词',
    `category` VARCHAR(50) DEFAULT 'general' COMMENT '分类',
    `click_count` INT DEFAULT 0 COMMENT '点击次数',
    `is_enabled` TINYINT(1) DEFAULT 1 COMMENT '是否启用',
    `admin_id` INT COMMENT '创建者ID',
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX `idx_category` (`category`),
    INDEX `idx_enabled` (`is_enabled`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='智能建议表';

-- 9. 创建会话统计表
-- =====================================================
CREATE TABLE IF NOT EXISTS `customer_service_session_stats` (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `session_id` VARCHAR(100) NOT NULL COMMENT '会话ID',
    `user_id` INT COMMENT '用户ID',
    `start_time` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '开始时间',
    `end_time` TIMESTAMP NULL COMMENT '结束时间',
    `duration` INT DEFAULT 0 COMMENT '持续时间(秒)',
    `message_count` INT DEFAULT 0 COMMENT '消息数量',
    `bot_message_count` INT DEFAULT 0 COMMENT '机器人消息数',
    `user_message_count` INT DEFAULT 0 COMMENT '用户消息数',
    `transferred_to_human` TINYINT(1) DEFAULT 0 COMMENT '是否转人工',
    `satisfaction_rating` TINYINT(1) COMMENT '满意度评分',
    `resolved` TINYINT(1) DEFAULT 0 COMMENT '是否解决',
    `admin_id` INT COMMENT '客服ID',
    `ip_address` VARCHAR(45) COMMENT 'IP地址',
    `user_agent` TEXT COMMENT '用户代理',
    INDEX `idx_session_id` (`session_id`),
    INDEX `idx_user_id` (`user_id`),
    INDEX `idx_start_time` (`start_time`),
    INDEX `idx_admin_id` (`admin_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='会话统计表';

-- 10. 插入默认配置数据
-- =====================================================

-- 插入默认机器人配置（如果不存在）
INSERT IGNORE INTO `customer_service_bot` (`id`, `bot_name`, `bot_description`, `welcome_message`, `default_reply`, `working_hours`, `auto_transfer_keywords`) VALUES 
(1, '趣玩小助手', '趣玩星球智能客服助手，7x24小时为您服务', 
'您好！我是趣玩星球智能客服小助手🤖

我可以帮助您解决以下问题：
• 账号相关问题
• 功能使用指导
• 常见问题解答

请描述您遇到的问题，我会尽力为您解答！',
'抱歉，我暂时无法理解您的问题😅

您可以：
• 换个方式描述问题
• 联系人工客服
• 查看帮助文档

如需人工客服，请回复"人工客服"',
'{"enabled": true, "timezone": "Asia/Shanghai", "schedule": [{"day": "monday", "start": "09:00", "end": "18:00"}, {"day": "tuesday", "start": "09:00", "end": "18:00"}, {"day": "wednesday", "start": "09:00", "end": "18:00"}, {"day": "thursday", "start": "09:00", "end": "18:00"}, {"day": "friday", "start": "09:00", "end": "18:00"}]}',
'["人工客服", "转人工", "人工", "客服", "投诉", "退款"]');

-- 插入快捷回复模板
INSERT IGNORE INTO `customer_service_quick_replies` (`title`, `content`, `category`, `sort_order`) VALUES
('问候语', '您好！欢迎来到趣玩星球，我是您的专属客服，有什么可以帮助您的吗？', 'greeting', 1),
('感谢语', '感谢您的咨询，如果还有其他问题，随时联系我们！', 'closing', 1),
('稍等提示', '请稍等片刻，我正在为您查询相关信息...', 'processing', 1),
('转人工', '正在为您转接人工客服，请稍等...', 'transfer', 1),
('工作时间', '我们的客服工作时间是：周一至周五 9:00-18:00', 'info', 1);

-- 插入智能建议
INSERT IGNORE INTO `customer_service_suggestions` (`suggestion_text`, `trigger_keywords`, `category`) VALUES
('账号登录问题', '["登录", "密码", "账号"]', 'account'),
('功能使用指导', '["怎么用", "如何", "教程"]', 'guide'),
('申诉相关问题', '["申诉", "封号", "误封"]', 'appeal'),
('联系人工客服', '["人工", "客服", "投诉"]', 'service');

-- 插入表单模板
INSERT IGNORE INTO `customer_service_form_templates` (`template_name`, `template_description`, `form_fields`, `trigger_keywords`, `success_message`) VALUES
('用户反馈表单', '收集用户反馈信息', 
'{"fields": [{"name": "contact", "label": "联系方式", "type": "text", "required": true}, {"name": "issue_type", "label": "问题类型", "type": "select", "options": ["功能问题", "账号问题", "其他"], "required": true}, {"name": "description", "label": "问题描述", "type": "textarea", "required": true}]}',
'["反馈", "建议", "问题"]',
'感谢您的反馈，我们会认真处理您的意见！');

-- 插入一些示例回复规则
INSERT IGNORE INTO `customer_service_replies` (`rule_name`, `keywords`, `reply_content`, `priority`, `category`) VALUES
('登录问题', '["登录", "密码", "忘记密码", "登不上"]', '关于登录问题：

1. 请确认手机号是否正确
2. 如忘记密码，可点击"忘记密码"重置
3. 如仍无法登录，请联系客服

需要更多帮助吗？', 10, 'account'),

('账号封禁', '["封号", "被封", "误封", "申诉"]', '关于账号封禁问题：

如果您认为账号被误封，可以：
1. 提供相关证明材料
2. 详细说明情况
3. 我们会在24小时内处理

请问需要提交申诉吗？', 15, 'account'),

('功能使用', '["怎么用", "如何使用", "教程", "操作"]', '功能使用指导：

我们为您提供详细的使用教程：
1. 新手指南
2. 功能介绍
3. 常见问题

您想了解哪个功能的使用方法？', 5, 'guide'),

('人工客服', '["人工", "客服", "转人工", "人工客服"]', '正在为您转接人工客服...

请稍等片刻，我们的客服人员会尽快为您服务。

工作时间：周一至周五 9:00-18:00', 20, 'service');

-- 验证表创建
SELECT 'customer_service_database_setup_completed' as status, NOW() as completed_at;
