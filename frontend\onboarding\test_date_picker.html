<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="theme-color" content="#0a0a2e">
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">
    <title>日期选择器测试</title>
    <link rel="stylesheet" href="css/style.css">
    <style>
        body {
            padding: 20px;
            background: linear-gradient(135deg, #0a0a2e 0%, #16213e 25%, #1a1a3a 50%, #0f3460 75%, #0a0a2e 100%);
            min-height: 100vh;
        }
        .test-container {
            max-width: 400px;
            margin: 50px auto;
            background: white;
            padding: 30px;
            border-radius: 16px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
        }
        .test-title {
            text-align: center;
            margin-bottom: 30px;
            color: #333;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 8px;
            border-left: 4px solid #40E0D0;
        }
    </style>
</head>
<body>
    <!-- Toast提示 -->
    <div class="toast" id="toast"></div>

    <div class="test-container">
        <h1 class="test-title">日期选择器测试</h1>
        
        <div class="form-group">
            <label for="birth_date">出生日期 <span class="required">*</span></label>
            <div class="date-input-container">
                <input type="text" id="birth_date" name="birth_date" required placeholder="请选择出生日期" readonly>
                <button type="button" class="date-select-btn" onclick="openDatePicker()">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                        <rect x="3" y="4" width="18" height="18" rx="2" ry="2" stroke="currentColor" stroke-width="2"/>
                        <line x1="16" y1="2" x2="16" y2="6" stroke="currentColor" stroke-width="2"/>
                        <line x1="8" y1="2" x2="8" y2="6" stroke="currentColor" stroke-width="2"/>
                        <line x1="3" y1="10" x2="21" y2="10" stroke="currentColor" stroke-width="2"/>
                    </svg>
                </button>
            </div>
        </div>

        <div class="result" id="result" style="display: none;">
            <h3>选择结果：</h3>
            <p id="result-text"></p>
            <p id="result-age"></p>
        </div>
    </div>

    <!-- 自定义日期选择器覆盖层 -->
    <div class="date-picker-overlay" id="date-picker-overlay">
        <div class="date-picker-modal">
            <div class="date-picker-header">
                <h3>选择出生日期</h3>
                <button class="date-picker-close" onclick="closeDatePicker()">
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                        <line x1="18" y1="6" x2="6" y2="18" stroke="currentColor" stroke-width="2"/>
                        <line x1="6" y1="6" x2="18" y2="18" stroke="currentColor" stroke-width="2"/>
                    </svg>
                </button>
            </div>
            
            <div class="date-picker-content">
                <div class="date-selectors">
                    <div class="selector-group">
                        <label>年份</label>
                        <div class="selector-wrapper">
                            <select id="year-selector" class="date-selector">
                                <!-- 年份选项将通过JavaScript生成 -->
                            </select>
                        </div>
                    </div>
                    
                    <div class="selector-group">
                        <label>月份</label>
                        <div class="selector-wrapper">
                            <select id="month-selector" class="date-selector">
                                <option value="1">1月</option>
                                <option value="2">2月</option>
                                <option value="3">3月</option>
                                <option value="4">4月</option>
                                <option value="5">5月</option>
                                <option value="6">6月</option>
                                <option value="7">7月</option>
                                <option value="8">8月</option>
                                <option value="9">9月</option>
                                <option value="10">10月</option>
                                <option value="11">11月</option>
                                <option value="12">12月</option>
                            </select>
                        </div>
                    </div>
                    
                    <div class="selector-group">
                        <label>日期</label>
                        <div class="selector-wrapper">
                            <select id="day-selector" class="date-selector">
                                <!-- 日期选项将通过JavaScript生成 -->
                            </select>
                        </div>
                    </div>
                </div>
                
                <div class="date-picker-actions">
                    <button class="btn-cancel" onclick="closeDatePicker()">取消</button>
                    <button class="btn-confirm" onclick="confirmDateSelection()">确认</button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 复制日期选择器相关的JavaScript代码
        function showToast(message, duration = 3000) {
            const toast = document.getElementById('toast');
            if (!toast) {
                alert(message);
                return;
            }

            if (toast.timer) {
                clearTimeout(toast.timer);
            }

            toast.style.display = 'none';
            void toast.offsetWidth;

            toast.textContent = message;
            toast.style.display = 'block';

            toast.timer = setTimeout(() => {
                toast.style.display = 'none';
            }, duration);
        }

        let selectedYear = null;
        let selectedMonth = null;
        let selectedDay = null;

        function openDatePicker() {
            const overlay = document.getElementById('date-picker-overlay');
            if (overlay) {
                initializeYearOptions();
                initializeDateSelectors();
                updateDayOptions();
                
                const defaultDate = new Date();
                defaultDate.setFullYear(defaultDate.getFullYear() - 18);
                
                document.getElementById('year-selector').value = defaultDate.getFullYear();
                document.getElementById('month-selector').value = defaultDate.getMonth() + 1;
                selectedYear = defaultDate.getFullYear();
                selectedMonth = defaultDate.getMonth() + 1;
                
                updateDayOptions();
                document.getElementById('day-selector').value = defaultDate.getDate();
                selectedDay = defaultDate.getDate();
                
                overlay.style.display = 'flex';
            }
        }

        function closeDatePicker() {
            const overlay = document.getElementById('date-picker-overlay');
            if (overlay) {
                overlay.style.display = 'none';
            }
        }

        function initializeYearOptions() {
            const yearSelector = document.getElementById('year-selector');
            const currentYear = new Date().getFullYear();
            const startYear = currentYear - 80;
            const endYear = currentYear - 18;
            
            yearSelector.innerHTML = '';
            
            for (let year = endYear; year >= startYear; year--) {
                const option = document.createElement('option');
                option.value = year;
                option.textContent = year + '年';
                yearSelector.appendChild(option);
            }
        }

        function initializeDateSelectors() {
            const yearSelector = document.getElementById('year-selector');
            const monthSelector = document.getElementById('month-selector');
            const daySelector = document.getElementById('day-selector');
            
            yearSelector.addEventListener('change', function() {
                selectedYear = parseInt(this.value);
                updateDayOptions();
            });
            
            monthSelector.addEventListener('change', function() {
                selectedMonth = parseInt(this.value);
                updateDayOptions();
            });
            
            daySelector.addEventListener('change', function() {
                selectedDay = parseInt(this.value);
            });
        }

        function updateDayOptions() {
            const daySelector = document.getElementById('day-selector');
            const year = selectedYear || parseInt(document.getElementById('year-selector').value);
            const month = selectedMonth || parseInt(document.getElementById('month-selector').value);
            
            const daysInMonth = new Date(year, month, 0).getDate();
            
            daySelector.innerHTML = '';
            
            for (let day = 1; day <= daysInMonth; day++) {
                const option = document.createElement('option');
                option.value = day;
                option.textContent = day + '日';
                daySelector.appendChild(option);
            }
        }

        function confirmDateSelection() {
            const year = parseInt(document.getElementById('year-selector').value);
            const month = parseInt(document.getElementById('month-selector').value);
            const day = parseInt(document.getElementById('day-selector').value);
            
            if (!year || !month || !day) {
                showToast('请选择完整的日期');
                return;
            }
            
            const selectedDate = new Date(year, month - 1, day);
            const today = new Date();
            let age = today.getFullYear() - selectedDate.getFullYear();
            const monthDiff = today.getMonth() - selectedDate.getMonth();
            
            if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < selectedDate.getDate())) {
                age--;
            }
            
            if (age < 18) {
                showToast('趣玩星球仅对年满十八周岁的用户提供服务', 4000);
                return;
            }
            
            const formattedDate = `${year}-${month.toString().padStart(2, '0')}-${day.toString().padStart(2, '0')}`;
            const displayDate = `${year}年${month}月${day}日`;
            
            document.getElementById('birth_date').value = displayDate;
            document.getElementById('birth_date').setAttribute('data-value', formattedDate);
            
            // 显示结果
            document.getElementById('result').style.display = 'block';
            document.getElementById('result-text').textContent = `选择的日期：${displayDate}`;
            document.getElementById('result-age').textContent = `年龄：${age}岁`;
            
            closeDatePicker();
            showToast('已选择出生日期：' + displayDate);
        }

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            const birthDateInput = document.getElementById('birth_date');
            if (birthDateInput) {
                birthDateInput.addEventListener('click', function() {
                    openDatePicker();
                });
            }
        });
    </script>
</body>
</html>
