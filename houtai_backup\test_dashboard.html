<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>趣玩星球管理后台 - 主控制台 (测试版)</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        /* 现代化实用型管理后台样式 */
        :root {
            --primary-color: #40E0D0;
            --primary-light: #AFFBF2;
            --primary-dark: #20B2AA;
            --secondary-color: #667eea;
            --accent-color: #FF6B6B;
            --success-color: #10B981;
            --warning-color: #F59E0B;
            --error-color: #EF4444;
            --info-color: #3B82F6;
            
            --gray-50: #F9FAFB;
            --gray-100: #F3F4F6;
            --gray-200: #E5E7EB;
            --gray-300: #D1D5DB;
            --gray-400: #9CA3AF;
            --gray-500: #6B7280;
            --gray-600: #4B5563;
            --gray-700: #374151;
            --gray-800: #1F2937;
            --gray-900: #111827;
            
            --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
            --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
            --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
            
            --border-radius: 12px;
            --border-radius-lg: 16px;
            --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: var(--gray-800);
            line-height: 1.6;
        }

        /* 主容器 */
        .dashboard-container {
            min-height: 100vh;
            display: flex;
            flex-direction: column;
        }

        /* 顶部导航栏 */
        .top-header {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-bottom: 1px solid var(--gray-200);
            padding: 0 24px;
            height: 70px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            position: sticky;
            top: 0;
            z-index: 100;
            box-shadow: var(--shadow-sm);
        }

        .header-left {
            display: flex;
            align-items: center;
            gap: 24px;
        }

        .brand-logo {
            display: flex;
            align-items: center;
            gap: 12px;
            font-size: 1.25rem;
            font-weight: 700;
            color: var(--gray-800);
            text-decoration: none;
        }

        .brand-icon {
            width: 40px;
            height: 40px;
            background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
            border-radius: var(--border-radius);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 18px;
        }

        /* 快速导航 */
        .quick-nav {
            display: flex;
            gap: 8px;
        }

        .nav-item {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 8px 16px;
            border-radius: var(--border-radius);
            text-decoration: none;
            color: var(--gray-600);
            font-weight: 500;
            transition: var(--transition);
            position: relative;
        }

        .nav-item:hover {
            background: var(--gray-100);
            color: var(--primary-color);
            text-decoration: none;
        }

        .nav-item.active {
            background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
            color: white;
        }

        .nav-badge {
            background: var(--error-color);
            color: white;
            font-size: 10px;
            padding: 2px 6px;
            border-radius: 10px;
            min-width: 18px;
            text-align: center;
            position: absolute;
            top: -2px;
            right: -2px;
        }

        /* 用户信息 */
        .header-right {
            display: flex;
            align-items: center;
            gap: 16px;
        }

        .user-profile {
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 8px 16px;
            border-radius: var(--border-radius);
            background: var(--gray-50);
            cursor: pointer;
            transition: var(--transition);
            position: relative;
        }

        .user-profile:hover {
            background: var(--gray-100);
        }

        .user-avatar {
            width: 36px;
            height: 36px;
            background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
            border-radius: var(--border-radius);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: 600;
            font-size: 14px;
        }

        .user-info {
            display: flex;
            flex-direction: column;
            align-items: flex-start;
        }

        .user-name {
            font-weight: 600;
            color: var(--gray-800);
            font-size: 14px;
        }

        .user-role {
            font-size: 12px;
            color: var(--gray-500);
        }

        /* 用户下拉菜单 */
        .user-dropdown {
            position: absolute;
            top: 100%;
            right: 0;
            background: white;
            border-radius: var(--border-radius);
            box-shadow: var(--shadow-lg);
            padding: 8px;
            min-width: 200px;
            opacity: 0;
            visibility: hidden;
            transform: translateY(-10px);
            transition: var(--transition);
            z-index: 1000;
        }

        .user-dropdown.show {
            opacity: 1;
            visibility: visible;
            transform: translateY(0);
        }

        .dropdown-item {
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 12px;
            border-radius: var(--border-radius);
            text-decoration: none;
            color: var(--gray-700);
            transition: var(--transition);
        }

        .dropdown-item:hover {
            background: var(--gray-50);
            text-decoration: none;
            color: var(--gray-800);
        }

        .dropdown-item.danger {
            color: var(--error-color);
        }

        .dropdown-item.danger:hover {
            background: rgba(239, 68, 68, 0.1);
            color: var(--error-color);
        }

        /* 主内容区域 */
        .main-content {
            flex: 1;
            padding: 24px;
            max-width: 1400px;
            margin: 0 auto;
            width: 100%;
        }

        /* 页面标题区域 */
        .page-header {
            margin-bottom: 32px;
        }

        .page-title {
            font-size: 2rem;
            font-weight: 700;
            color: white;
            margin-bottom: 8px;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .page-subtitle {
            color: rgba(255, 255, 255, 0.8);
            font-size: 1.1rem;
        }

        /* 统计卡片网格 */
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 20px;
            margin-bottom: 32px;
        }

        .stat-card {
            background: white;
            border-radius: var(--border-radius-lg);
            padding: 24px;
            box-shadow: var(--shadow-md);
            transition: var(--transition);
            cursor: pointer;
            position: relative;
            overflow: hidden;
        }

        .stat-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, var(--primary-color), var(--primary-light));
        }

        .stat-card:hover {
            transform: translateY(-4px);
            box-shadow: var(--shadow-xl);
        }

        .stat-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 16px;
        }

        .stat-icon {
            width: 56px;
            height: 56px;
            border-radius: var(--border-radius);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            color: white;
            background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
        }

        .stat-trend {
            display: flex;
            align-items: center;
            gap: 4px;
            font-size: 12px;
            font-weight: 600;
            padding: 4px 8px;
            border-radius: 20px;
        }

        .stat-trend.up {
            background: rgba(16, 185, 129, 0.1);
            color: var(--success-color);
        }

        .stat-trend.down {
            background: rgba(239, 68, 68, 0.1);
            color: var(--error-color);
        }

        .stat-number {
            font-size: 2.5rem;
            font-weight: 700;
            color: var(--gray-800);
            margin-bottom: 4px;
            line-height: 1;
        }

        .stat-label {
            font-size: 0.875rem;
            color: var(--gray-600);
            font-weight: 500;
        }

        .stat-description {
            font-size: 0.75rem;
            color: var(--gray-500);
            margin-top: 8px;
        }
    </style>
</head>
<body>
    <div class="dashboard-container">
        <!-- 顶部导航栏 -->
        <header class="top-header">
            <div class="header-left">
                <a href="#" class="brand-logo">
                    <div class="brand-icon">
                        <i class="fas fa-star"></i>
                    </div>
                    <span>趣玩星球管理后台</span>
                </a>
                
                <!-- 快速导航 -->
                <nav class="quick-nav">
                    <a href="#" class="nav-item active">
                        <i class="fas fa-chart-line"></i>
                        <span>仪表盘</span>
                    </a>
                    <a href="#" class="nav-item">
                        <i class="fas fa-users"></i>
                        <span>用户管理</span>
                    </a>
                    <a href="#" class="nav-item">
                        <i class="fas fa-id-card"></i>
                        <span>实名认证</span>
                        <span class="nav-badge">3</span>
                    </a>
                </nav>
            </div>
            
            <div class="header-right">
                <div class="user-profile" onclick="toggleUserMenu()">
                    <div class="user-avatar">管</div>
                    <div class="user-info">
                        <div class="user-name">管理员</div>
                        <div class="user-role">系统管理员</div>
                    </div>
                    <i class="fas fa-chevron-down"></i>
                    
                    <!-- 用户下拉菜单 -->
                    <div class="user-dropdown" id="userDropdown">
                        <a href="#" class="dropdown-item">
                            <i class="fas fa-user"></i>
                            <span>个人资料</span>
                        </a>
                        <a href="#" class="dropdown-item">
                            <i class="fas fa-cog"></i>
                            <span>系统设置</span>
                        </a>
                        <div style="border-top: 1px solid var(--gray-200); margin: 8px 0;"></div>
                        <a href="#" class="dropdown-item danger">
                            <i class="fas fa-sign-out-alt"></i>
                            <span>退出登录</span>
                        </a>
                    </div>
                </div>
            </div>
        </header>

        <!-- 主内容区域 -->
        <main class="main-content">
            <!-- 页面标题 -->
            <div class="page-header">
                <h1 class="page-title">管理控制台</h1>
                <p class="page-subtitle">欢迎回来，管理员！实时监控平台运营状况</p>
            </div>

            <!-- 统计卡片 -->
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-header">
                        <div class="stat-icon" style="background: linear-gradient(135deg, var(--primary-color), var(--primary-light));">
                            <i class="fas fa-users"></i>
                        </div>
                        <div class="stat-trend up">
                            <i class="fas fa-arrow-up"></i>
                            <span>+12%</span>
                        </div>
                    </div>
                    <div class="stat-number">1,234</div>
                    <div class="stat-label">总用户数</div>
                    <div class="stat-description">平台注册用户总数</div>
                </div>

                <div class="stat-card">
                    <div class="stat-header">
                        <div class="stat-icon" style="background: linear-gradient(135deg, var(--warning-color), #FBBF24);">
                            <i class="fas fa-clock"></i>
                        </div>
                        <div class="stat-trend down">
                            <i class="fas fa-exclamation-triangle"></i>
                            <span>待处理</span>
                        </div>
                    </div>
                    <div class="stat-number">3</div>
                    <div class="stat-label">待审核认证</div>
                    <div class="stat-description">需要审核的实名认证申请</div>
                </div>

                <div class="stat-card">
                    <div class="stat-header">
                        <div class="stat-icon" style="background: linear-gradient(135deg, var(--info-color), #60A5FA);">
                            <i class="fas fa-user-plus"></i>
                        </div>
                        <div class="stat-trend up">
                            <i class="fas fa-arrow-up"></i>
                            <span>今日</span>
                        </div>
                    </div>
                    <div class="stat-number">12</div>
                    <div class="stat-label">今日新增</div>
                    <div class="stat-description">今天新注册的用户数</div>
                </div>

                <div class="stat-card">
                    <div class="stat-header">
                        <div class="stat-icon" style="background: linear-gradient(135deg, var(--success-color), #34D399);">
                            <i class="fas fa-check-circle"></i>
                        </div>
                        <div class="stat-trend up">
                            <i class="fas fa-check"></i>
                            <span>已认证</span>
                        </div>
                    </div>
                    <div class="stat-number">856</div>
                    <div class="stat-label">已认证用户</div>
                    <div class="stat-description">通过实名认证的用户数</div>
                </div>
            </div>
        </main>
    </div>

    <script>
        // 用户菜单切换
        function toggleUserMenu() {
            const dropdown = document.getElementById('userDropdown');
            dropdown.classList.toggle('show');
        }
        
        // 点击外部关闭用户菜单
        document.addEventListener('click', function(e) {
            const userProfile = e.target.closest('.user-profile');
            if (!userProfile) {
                const dropdown = document.getElementById('userDropdown');
                if (dropdown) {
                    dropdown.classList.remove('show');
                }
            }
        });
        
        // 统计卡片动画效果
        document.querySelectorAll('.stat-card').forEach(card => {
            card.addEventListener('mouseenter', function() {
                this.style.transform = 'translateY(-4px)';
                this.style.boxShadow = 'var(--shadow-xl)';
            });

            card.addEventListener('mouseleave', function() {
                this.style.transform = 'translateY(0)';
                this.style.boxShadow = 'var(--shadow-md)';
            });
        });
        
        console.log('🌟 趣玩星球管理后台测试版已加载完成');
    </script>
</body>
</html>
