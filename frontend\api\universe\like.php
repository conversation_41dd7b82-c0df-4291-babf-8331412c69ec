<?php
// 设置响应头
header('Content-Type: application/json');

// 启动会话
session_start();

// 检查用户是否已登录
if (!isset($_SESSION['user_id'])) {
    echo json_encode([
        'success' => false,
        'message' => '请先登录'
    ]);
    exit;
}

// 检查必填参数
if (!isset($_POST['post_id']) || empty($_POST['post_id'])) {
    echo json_encode([
        'success' => false,
        'message' => '缺少内容ID'
    ]);
    exit;
}

// 获取参数
$post_id = intval($_POST['post_id']);
$user_id = $_SESSION['user_id'];
$action = isset($_POST['action']) ? $_POST['action'] : 'like';

// 引入数据库配置
require_once '../../../includes/db_config.php';

try {
    // 连接数据库
    $dsn = "mysql:host={$db_config['host']};dbname={$db_config['dbname']};charset={$db_config['charset']};port={$db_config['port']}";
    $pdo = new PDO($dsn, $db_config['username'], $db_config['password']);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

    // 开始事务
    $pdo->beginTransaction();

    // 检查内容是否存在
    $stmt = $pdo->prepare("SELECT id FROM universe_posts WHERE id = :id AND status = 1");
    $stmt->execute(['id' => $post_id]);
    if (!$stmt->fetch()) {
        echo json_encode([
            'success' => false,
            'message' => '内容不存在或已被删除'
        ]);
        exit;
    }

    if ($action === 'like') {
        // 检查是否已点赞
        $stmt = $pdo->prepare("SELECT id FROM universe_likes WHERE post_id = :post_id AND user_id = :user_id");
        $stmt->execute([
            'post_id' => $post_id,
            'user_id' => $user_id
        ]);

        if ($stmt->fetch()) {
            // 已点赞，返回成功
            echo json_encode([
                'success' => true,
                'message' => '已点赞'
            ]);
            exit;
        }

        // 添加点赞记录
        $stmt = $pdo->prepare("INSERT INTO universe_likes (post_id, user_id) VALUES (:post_id, :user_id)");
        $stmt->execute([
            'post_id' => $post_id,
            'user_id' => $user_id
        ]);

        // 更新内容点赞数
        $stmt = $pdo->prepare("UPDATE universe_posts SET likes = likes + 1 WHERE id = :id");
        $stmt->execute(['id' => $post_id]);

        // 提交事务
        $pdo->commit();

        // 返回成功信息
        echo json_encode([
            'success' => true,
            'message' => '点赞成功'
        ]);
    } else if ($action === 'unlike') {
        // 检查是否已点赞
        $stmt = $pdo->prepare("SELECT id FROM universe_likes WHERE post_id = :post_id AND user_id = :user_id");
        $stmt->execute([
            'post_id' => $post_id,
            'user_id' => $user_id
        ]);

        if (!$stmt->fetch()) {
            // 未点赞，返回成功
            echo json_encode([
                'success' => true,
                'message' => '未点赞'
            ]);
            exit;
        }

        // 删除点赞记录
        $stmt = $pdo->prepare("DELETE FROM universe_likes WHERE post_id = :post_id AND user_id = :user_id");
        $stmt->execute([
            'post_id' => $post_id,
            'user_id' => $user_id
        ]);

        // 更新内容点赞数
        $stmt = $pdo->prepare("UPDATE universe_posts SET likes = GREATEST(likes - 1, 0) WHERE id = :id");
        $stmt->execute(['id' => $post_id]);

        // 提交事务
        $pdo->commit();

        // 返回成功信息
        echo json_encode([
            'success' => true,
            'message' => '取消点赞成功'
        ]);
    } else {
        echo json_encode([
            'success' => false,
            'message' => '无效的操作'
        ]);
    }
} catch (PDOException $e) {
    // 回滚事务
    if ($pdo->inTransaction()) {
        $pdo->rollBack();
    }

    // 记录错误
    error_log("点赞操作错误: " . $e->getMessage());

    // 返回错误信息
    echo json_encode([
        'success' => false,
        'message' => '操作失败，请稍后再试'
    ]);
}
