<?php
/**
 * 前台代理：检查通知记录
 * 代理请求到后台API，解决跨域问题
 */

// 禁用错误输出
error_reporting(0);
ini_set('display_errors', 0);

header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// 处理OPTIONS预检请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

try {
    // 直接在前台执行数据库查询，避免跨域问题
    $pdo = new PDO(
        "mysql:host=localhost;dbname=quwanplanet;charset=utf8mb4",
        "quwanplanet",
        "nJmJm23FB4Xn6Fc3",
        [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC
        ]
    );
    
    $response = [
        'success' => true,
        'data' => [],
        'debug' => []
    ];
    
    // 检查表是否存在
    $tables = ['verification_codes', 'realtime_notifications', 'admin_operation_logs'];
    foreach ($tables as $table) {
        $stmt = $pdo->query("SHOW TABLES LIKE '{$table}'");
        $exists = $stmt->rowCount() > 0;
        $response['debug']['tables'][$table] = $exists ? '存在' : '不存在';
    }
    
    // 查询最近的验证码记录
    try {
        $stmt = $pdo->query("
            SELECT id, user_id, phone, code, type, status, sent_by_admin, admin_note, expires_at, created_at 
            FROM verification_codes 
            ORDER BY created_at DESC 
            LIMIT 5
        ");
        $response['data']['verification_codes'] = $stmt->fetchAll();
    } catch (Exception $e) {
        $response['debug']['verification_codes_error'] = $e->getMessage();
    }
    
    // 查询最近的实时通知记录
    try {
        $stmt = $pdo->query("
            SELECT id, user_id, type, title, content, data, status, priority, expires_at, delivered_at, created_at 
            FROM realtime_notifications 
            ORDER BY created_at DESC 
            LIMIT 5
        ");
        $response['data']['realtime_notifications'] = $stmt->fetchAll();
    } catch (Exception $e) {
        $response['debug']['realtime_notifications_error'] = $e->getMessage();
    }
    
    // 查询最近的操作日志
    try {
        $stmt = $pdo->query("
            SELECT id, admin_id, operation_type, target_type, target_id, description, details, created_at 
            FROM admin_operation_logs 
            WHERE operation_type = '发送验证码'
            ORDER BY created_at DESC 
            LIMIT 5
        ");
        $response['data']['admin_operation_logs'] = $stmt->fetchAll();
    } catch (Exception $e) {
        $response['debug']['admin_operation_logs_error'] = $e->getMessage();
    }
    
    // 查询用户在线状态
    $user_id = intval($_GET['user_id'] ?? 4);
    try {
        $stmt = $pdo->prepare("
            SELECT id, username, online_status, last_activity, last_notification_check 
            FROM users 
            WHERE id = ?
        ");
        $stmt->execute([$user_id]);
        $response['data']['user_status'] = $stmt->fetch();
    } catch (Exception $e) {
        $response['debug']['user_status_error'] = $e->getMessage();
    }
    
    // 统计信息
    try {
        $stmt = $pdo->query("SELECT COUNT(*) as count FROM verification_codes");
        $response['debug']['stats']['verification_codes_count'] = $stmt->fetch()['count'];
        
        $stmt = $pdo->query("SELECT COUNT(*) as count FROM realtime_notifications");
        $response['debug']['stats']['realtime_notifications_count'] = $stmt->fetch()['count'];
        
        $stmt = $pdo->query("SELECT COUNT(*) as count FROM realtime_notifications WHERE status = 'pending'");
        $response['debug']['stats']['pending_notifications_count'] = $stmt->fetch()['count'];
    } catch (Exception $e) {
        $response['debug']['stats_error'] = $e->getMessage();
    }
    
} catch (Exception $e) {
    $response = [
        'success' => false,
        'error' => $e->getMessage()
    ];
}

echo json_encode($response, JSON_UNESCAPED_UNICODE);
?>
