<?php
// 检查外键约束
header('Content-Type: application/json; charset=UTF-8');

try {
    require_once '../../houtai_backup/db_config.php';
    $pdo = getDbConnection();
    
    echo json_encode([
        'step' => 1,
        'message' => '✅ 开始检查外键约束',
        'success' => true
    ]) . "\n";
    
    // 检查外键约束
    $stmt = $pdo->prepare("
        SELECT 
            CONSTRAINT_NAME,
            TABLE_NAME,
            COLUMN_NAME,
            REFERENCED_TABLE_NAME,
            REFERENCED_COLUMN_NAME
        FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE 
        WHERE REFERENCED_TABLE_SCHEMA = 'quwanplanet' 
        AND TABLE_NAME = 'customer_service_messages'
    ");
    $stmt->execute();
    $foreignKeys = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo json_encode([
        'step' => 2,
        'message' => '✅ 外键约束查询完成',
        'success' => true,
        'foreign_keys' => $foreignKeys
    ]) . "\n";
    
    // 检查现有会话ID
    $stmt = $pdo->prepare("SELECT session_id, user_name, status FROM customer_service_sessions ORDER BY started_at DESC LIMIT 5");
    $stmt->execute();
    $sessions = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo json_encode([
        'step' => 3,
        'message' => '✅ 现有会话查询完成',
        'success' => true,
        'existing_sessions' => $sessions
    ]) . "\n";
    
    // 检查孤立的消息（没有对应会话的消息）
    $stmt = $pdo->prepare("
        SELECT m.session_id, COUNT(*) as message_count
        FROM customer_service_messages m
        LEFT JOIN customer_service_sessions s ON m.session_id = s.session_id
        WHERE s.session_id IS NULL
        GROUP BY m.session_id
    ");
    $stmt->execute();
    $orphanMessages = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo json_encode([
        'step' => 4,
        'message' => '✅ 孤立消息检查完成',
        'success' => true,
        'orphan_messages' => $orphanMessages,
        'orphan_count' => count($orphanMessages)
    ]) . "\n";
    
    // 检查消息表中的session_id分布
    $stmt = $pdo->prepare("
        SELECT session_id, COUNT(*) as count 
        FROM customer_service_messages 
        GROUP BY session_id 
        ORDER BY count DESC 
        LIMIT 10
    ");
    $stmt->execute();
    $sessionDistribution = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo json_encode([
        'step' => 5,
        'message' => '✅ 消息分布检查完成',
        'success' => true,
        'session_distribution' => $sessionDistribution
    ]) . "\n";
    
    // 建议解决方案
    $solutions = [];
    
    if (count($orphanMessages) > 0) {
        $solutions[] = "发现 " . count($orphanMessages) . " 个孤立消息，需要清理或创建对应会话";
    }
    
    if (count($sessions) == 0) {
        $solutions[] = "没有现有会话，需要先创建会话才能发送消息";
    }
    
    $solutions[] = "外键约束要求消息必须关联到现有会话";
    $solutions[] = "发送消息前必须确保会话存在";
    
    echo json_encode([
        'step' => 'FINAL',
        'message' => '🎯 外键约束分析完成',
        'success' => true,
        'summary' => [
            'foreign_keys_count' => count($foreignKeys),
            'existing_sessions_count' => count($sessions),
            'orphan_messages_count' => count($orphanMessages),
            'solutions' => $solutions
        ]
    ]) . "\n";
    
} catch (Exception $e) {
    echo json_encode([
        'step' => 'ERROR',
        'message' => '❌ 外键检查失败: ' . $e->getMessage(),
        'success' => false,
        'error' => $e->getMessage()
    ]) . "\n";
}
?>
