<?php
/**
 * 申诉审核管理页面
 * 趣玩星球管理后台
 */

// 引入认证检查
require_once '../auth_check.php';

// 引入数据库配置
require_once '../db_config.php';

// 获取分页参数
$page = max(1, intval($_GET['page'] ?? 1));
$limit = 20;
$offset = ($page - 1) * $limit;

// 获取筛选参数
$status_filter = $_GET['status'] ?? '';
$type_filter = $_GET['type'] ?? '';
$search = trim($_GET['search'] ?? '');

try {
    $pdo = getDbConnection();

    // 构建查询条件
    $where_conditions = [];
    $params = [];

    if (!empty($status_filter)) {
        $where_conditions[] = "status = ?";
        $params[] = $status_filter;
    }

    if (!empty($type_filter)) {
        $where_conditions[] = "appeal_type = ?";
        $params[] = $type_filter;
    }

    if (!empty($search)) {
        $where_conditions[] = "(phone LIKE ? OR email LIKE ? OR reason LIKE ?)";
        $search_param = "%$search%";
        $params[] = $search_param;
        $params[] = $search_param;
        $params[] = $search_param;
    }

    $where_clause = empty($where_conditions) ? '' : 'WHERE ' . implode(' AND ', $where_conditions);

    // 获取总数
    $count_sql = "SELECT COUNT(*) FROM user_appeals $where_clause";
    $count_stmt = $pdo->prepare($count_sql);
    $count_stmt->execute($params);
    $total = $count_stmt->fetchColumn();

    // 获取申诉列表
    $sql = "
        SELECT * FROM user_appeals
        $where_clause
        ORDER BY created_at DESC
        LIMIT $limit OFFSET $offset
    ";

    $stmt = $pdo->prepare($sql);
    $stmt->execute($params);
    $appeals = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // 计算分页信息
    $total_pages = ceil($total / $limit);

    // 获取统计数据
    $stats_sql = "
        SELECT
            status,
            COUNT(*) as count
        FROM user_appeals
        GROUP BY status
    ";
    $stats_stmt = $pdo->query($stats_sql);
    $stats = [];
    while ($row = $stats_stmt->fetch(PDO::FETCH_ASSOC)) {
        $stats[$row['status']] = $row['count'];
    }

} catch (Exception $e) {
    $error_message = "数据加载失败：" . $e->getMessage();
    $appeals = [];
    $total = 0;
    $total_pages = 0;
    $stats = [];
}

// 状态和类型映射
$status_map = [
    'pending' => ['text' => '待处理', 'class' => 'warning'],
    'processing' => ['text' => '处理中', 'class' => 'info'],
    'approved' => ['text' => '已通过', 'class' => 'success'],
    'rejected' => ['text' => '已拒绝', 'class' => 'danger']
];

$type_map = [
    'wrongful_ban' => '误封申诉',
    'account_stolen' => '账号被盗',
    'system_error' => '系统错误',
    'other' => '其他原因'
];

?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>申诉审核管理 - 趣玩星球管理后台</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="../assets/css/admin-layout.css">
    <style>
        /* 申诉审核页面专用样式 */
        .appeal-container {
            padding: var(--spacing-xl);
            max-width: 1400px;
            margin: 0 auto;
        }

        .page-header {
            margin-bottom: var(--spacing-xl);
        }

        .page-title h1 {
            font-size: var(--font-size-3xl);
            font-weight: 700;
            color: var(--gray-800);
            margin-bottom: var(--spacing-sm);
            display: flex;
            align-items: center;
            gap: var(--spacing-md);
        }

        .page-title p {
            color: var(--gray-600);
            font-size: var(--font-size-lg);
        }

        /* 统计卡片样式 */
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: var(--spacing-lg);
            margin-bottom: var(--spacing-xl);
        }

        .stat-card {
            background: var(--white);
            border-radius: var(--border-radius-lg);
            padding: var(--spacing-xl);
            box-shadow: var(--shadow-lg);
            display: flex;
            align-items: center;
            gap: var(--spacing-lg);
            transition: var(--transition);
        }

        .stat-card:hover {
            transform: translateY(-4px);
            box-shadow: var(--shadow-xl);
        }

        .stat-icon {
            width: 60px;
            height: 60px;
            border-radius: var(--border-radius-lg);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            color: white;
        }

        .stat-icon.warning { background: var(--primary-gradient); }
        .stat-icon.info { background: linear-gradient(135deg, var(--info-color), var(--info-light)); }
        .stat-icon.success { background: linear-gradient(135deg, var(--success-color), var(--success-light)); }
        .stat-icon.danger { background: linear-gradient(135deg, var(--error-color), var(--error-light)); }

        .stat-content {
            flex: 1;
        }

        .stat-number {
            font-size: var(--font-size-3xl);
            font-weight: 700;
            color: var(--gray-800);
            margin-bottom: var(--spacing-xs);
        }

        .stat-label {
            color: var(--gray-600);
            font-size: var(--font-size-sm);
            font-weight: 500;
        }

        /* 筛选区域样式 */
        .filter-section {
            background: white;
            border-radius: var(--border-radius-lg);
            padding: var(--spacing-xl);
            margin-bottom: var(--spacing-xl);
            box-shadow: var(--shadow-md);
        }

        .filter-form {
            display: flex;
            gap: var(--spacing-lg);
            align-items: end;
            flex-wrap: wrap;
        }

        .filter-group {
            display: flex;
            flex-direction: column;
            gap: var(--spacing-xs);
        }

        .filter-group label {
            font-size: var(--font-size-sm);
            font-weight: 600;
            color: var(--gray-700);
        }

        .filter-actions {
            display: flex;
            gap: var(--spacing-sm);
        }

        /* 表单控件样式 */
        .form-control {
            padding: var(--spacing-sm) var(--spacing-md);
            border: 1px solid var(--gray-300);
            border-radius: var(--border-radius);
            font-size: var(--font-size-sm);
            transition: var(--transition);
            min-width: 150px;
        }

        .form-control:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(111, 123, 245, 0.1);
        }

        /* 表格容器样式 */
        .table-container {
            background: white;
            border-radius: var(--border-radius-lg);
            overflow: hidden;
            box-shadow: var(--shadow-md);
            margin-bottom: var(--spacing-xl);
        }

        /* 数据表格样式 */
        .data-table {
            width: 100%;
            border-collapse: collapse;
        }

        .data-table th,
        .data-table td {
            padding: var(--spacing-md);
            text-align: left;
            border-bottom: 1px solid var(--gray-200);
            vertical-align: middle;
        }

        .data-table th {
            background: var(--gray-50);
            font-weight: 600;
            color: var(--gray-700);
            font-size: var(--font-size-sm);
        }

        .data-table tr:hover {
            background: var(--gray-50);
        }

        /* 状态和类型徽章 */
        .type-badge {
            background: linear-gradient(135deg, #6F7BF5, #5A67D8);
            color: white;
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 13px;
            font-weight: 600;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
            box-shadow: 0 2px 4px rgba(111, 123, 245, 0.3);
            display: inline-block;
            min-width: 60px;
            text-align: center;
        }

        .status-badge {
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 13px;
            font-weight: 600;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
            display: inline-block;
            min-width: 60px;
            text-align: center;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .status-warning {
            background: linear-gradient(135deg, #F59E0B, #D97706);
            color: white;
        }
        .status-info {
            background: linear-gradient(135deg, #3B82F6, #2563EB);
            color: white;
        }
        .status-success {
            background: linear-gradient(135deg, #10B981, #059669);
            color: white;
        }
        .status-danger {
            background: linear-gradient(135deg, #EF4444, #DC2626);
            color: white;
        }

        /* 操作按钮 */
        .action-buttons {
            display: flex;
            gap: var(--spacing-xs);
        }

        .btn {
            padding: var(--spacing-xs) var(--spacing-sm);
            border: none;
            border-radius: var(--border-radius);
            font-size: var(--font-size-sm);
            font-weight: 500;
            cursor: pointer;
            transition: var(--transition);
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: var(--spacing-xs);
        }

        .btn-primary { background: var(--primary-color); color: white; }
        .btn-success { background: var(--success-color); color: white; }
        .btn-danger { background: var(--error-color); color: white; }
        .btn-secondary { background: var(--gray-500); color: white; }

        .btn:hover {
            transform: translateY(-1px);
            box-shadow: var(--shadow-md);
        }

        .btn-sm {
            padding: 4px 8px;
            font-size: var(--font-size-xs);
        }

        /* 分页样式 */
        .pagination-container {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: var(--spacing-lg);
            background: white;
            border-radius: var(--border-radius-lg);
            box-shadow: var(--shadow-md);
        }

        .pagination {
            display: flex;
            gap: var(--spacing-xs);
        }

        .page-btn {
            padding: var(--spacing-sm) var(--spacing-md);
            border: 1px solid var(--gray-300);
            border-radius: var(--border-radius);
            color: var(--gray-700);
            text-decoration: none;
            transition: var(--transition);
        }

        .page-btn:hover,
        .page-btn.active {
            background: var(--primary-color);
            color: white;
            border-color: var(--primary-color);
        }

        /* 空状态样式 */
        .empty-state {
            padding: var(--spacing-3xl);
            text-align: center;
            color: var(--gray-500);
        }

        .empty-state i {
            font-size: 48px;
            margin-bottom: var(--spacing-lg);
            opacity: 0.5;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .filter-form {
                flex-direction: column;
                align-items: stretch;
            }

            .filter-actions {
                justify-content: center;
            }

            .action-buttons {
                flex-direction: column;
            }

            .data-table {
                font-size: var(--font-size-sm);
            }

            .data-table th,
            .data-table td {
                padding: var(--spacing-sm);
            }
        }
    </style>
</head>
<body>
    <div class="admin-layout">
        <!-- 左侧菜单 -->
        <?php include '../includes/sidebar.php'; ?>

        <!-- 主内容区域 -->
        <main class="main-content">
            <!-- 顶部导航栏 -->
            <?php include '../includes/topbar.php'; ?>

            <div class="appeal-container">
                <!-- 页面头部 -->
                <div class="page-header">
                    <div class="page-title">
                        <h1><i class="fas fa-file-alt"></i> 申诉审核管理</h1>
                        <p>管理用户申诉，处理账号封禁申诉请求</p>
                    </div>
                </div>

                <!-- 统计卡片 -->
                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-icon warning">
                            <i class="fas fa-clock"></i>
                        </div>
                        <div class="stat-content">
                            <div class="stat-number"><?php echo $stats['pending'] ?? 0; ?></div>
                            <div class="stat-label">待处理</div>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon info">
                            <i class="fas fa-cog"></i>
                        </div>
                        <div class="stat-content">
                            <div class="stat-number"><?php echo $stats['processing'] ?? 0; ?></div>
                            <div class="stat-label">处理中</div>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon success">
                            <i class="fas fa-check"></i>
                        </div>
                        <div class="stat-content">
                            <div class="stat-number"><?php echo $stats['approved'] ?? 0; ?></div>
                            <div class="stat-label">已通过</div>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon danger">
                            <i class="fas fa-times"></i>
                        </div>
                        <div class="stat-content">
                            <div class="stat-number"><?php echo $stats['rejected'] ?? 0; ?></div>
                            <div class="stat-label">已拒绝</div>
                        </div>
                    </div>
                </div>

    <!-- 筛选和搜索 -->
    <div class="filter-section">
        <form method="GET" class="filter-form">
            <div class="filter-group">
                <label>状态筛选：</label>
                <select name="status" class="form-control">
                    <option value="">全部状态</option>
                    <?php foreach ($status_map as $key => $value): ?>
                        <option value="<?php echo $key; ?>" <?php echo $status_filter === $key ? 'selected' : ''; ?>>
                            <?php echo $value['text']; ?>
                        </option>
                    <?php endforeach; ?>
                </select>
            </div>

            <div class="filter-group">
                <label>类型筛选：</label>
                <select name="type" class="form-control">
                    <option value="">全部类型</option>
                    <?php foreach ($type_map as $key => $value): ?>
                        <option value="<?php echo $key; ?>" <?php echo $type_filter === $key ? 'selected' : ''; ?>>
                            <?php echo $value; ?>
                        </option>
                    <?php endforeach; ?>
                </select>
            </div>

            <div class="filter-group">
                <label>搜索：</label>
                <input type="text" name="search" class="form-control" placeholder="手机号/邮箱/申诉原因" value="<?php echo htmlspecialchars($search); ?>">
            </div>

            <div class="filter-actions">
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-search"></i> 搜索
                </button>
                <a href="index.php" class="btn btn-secondary">
                    <i class="fas fa-refresh"></i> 重置
                </a>
            </div>
        </form>
    </div>

    <!-- 申诉列表 -->
    <div class="table-container">
        <table class="data-table">
            <thead>
                <tr>
                    <th>申诉ID</th>
                    <th>手机号</th>
                    <th>申诉类型</th>
                    <th>申诉原因</th>
                    <th>联系邮箱</th>
                    <th>状态</th>
                    <th>提交时间</th>
                    <th>操作</th>
                </tr>
            </thead>
            <tbody>
                <?php if (empty($appeals)): ?>
                    <tr>
                        <td colspan="8" class="text-center">
                            <div class="empty-state">
                                <i class="fas fa-inbox"></i>
                                <p>暂无申诉记录</p>
                            </div>
                        </td>
                    </tr>
                <?php else: ?>
                    <?php foreach ($appeals as $appeal): ?>
                        <tr>
                            <td>#<?php echo $appeal['id']; ?></td>
                            <td><?php echo htmlspecialchars($appeal['phone']); ?></td>
                            <td>
                                <span class="type-badge">
                                    <?php echo $type_map[$appeal['appeal_type']] ?? $appeal['appeal_type']; ?>
                                </span>
                            </td>
                            <td class="reason-cell">
                                <div class="reason-preview">
                                    <?php echo mb_substr(htmlspecialchars($appeal['reason']), 0, 50); ?>
                                    <?php if (mb_strlen($appeal['reason']) > 50): ?>...<?php endif; ?>
                                </div>
                            </td>
                            <td><?php echo htmlspecialchars($appeal['email']); ?></td>
                            <td>
                                <span class="status-badge status-<?php echo $status_map[$appeal['status']]['class']; ?>">
                                    <?php echo $status_map[$appeal['status']]['text']; ?>
                                </span>
                            </td>
                            <td><?php echo date('Y-m-d H:i', strtotime($appeal['created_at'])); ?></td>
                            <td class="action-buttons">
                                <a href="detail.php?id=<?php echo $appeal['id']; ?>" class="btn btn-sm btn-primary" title="查看详情">
                                    <i class="fas fa-eye"></i>
                                </a>
                                <?php if ($appeal['status'] === 'pending'): ?>
                                    <button class="btn btn-sm btn-success" onclick="processAppeal(<?php echo $appeal['id']; ?>, 'approve')" title="通过申诉">
                                        <i class="fas fa-check"></i>
                                    </button>
                                    <button class="btn btn-sm btn-danger" onclick="processAppeal(<?php echo $appeal['id']; ?>, 'reject')" title="拒绝申诉">
                                        <i class="fas fa-times"></i>
                                    </button>
                                <?php endif; ?>
                            </td>
                        </tr>
                    <?php endforeach; ?>
                <?php endif; ?>
            </tbody>
        </table>
    </div>

    <!-- 分页 -->
    <?php if ($total_pages > 1): ?>
        <div class="pagination-container">
            <div class="pagination-info">
                显示第 <?php echo $offset + 1; ?> - <?php echo min($offset + $limit, $total); ?> 条，共 <?php echo $total; ?> 条记录
            </div>
            <div class="pagination">
                <?php if ($page > 1): ?>
                    <a href="?page=<?php echo $page - 1; ?>&status=<?php echo $status_filter; ?>&type=<?php echo $type_filter; ?>&search=<?php echo urlencode($search); ?>" class="page-btn">
                        <i class="fas fa-chevron-left"></i>
                    </a>
                <?php endif; ?>

                <?php for ($i = max(1, $page - 2); $i <= min($total_pages, $page + 2); $i++): ?>
                    <a href="?page=<?php echo $i; ?>&status=<?php echo $status_filter; ?>&type=<?php echo $type_filter; ?>&search=<?php echo urlencode($search); ?>"
                       class="page-btn <?php echo $i === $page ? 'active' : ''; ?>">
                        <?php echo $i; ?>
                    </a>
                <?php endfor; ?>

                <?php if ($page < $total_pages): ?>
                    <a href="?page=<?php echo $page + 1; ?>&status=<?php echo $status_filter; ?>&type=<?php echo $type_filter; ?>&search=<?php echo urlencode($search); ?>" class="page-btn">
                        <i class="fas fa-chevron-right"></i>
                    </a>
                <?php endif; ?>
            </div>
        </div>
    <?php endif; ?>
</div>

<script>
function processAppeal(appealId, action) {
    const actionText = action === 'approve' ? '通过' : '拒绝';
    const confirmText = `确定要${actionText}这个申诉吗？`;

    if (confirm(confirmText)) {
        // 跳转到处理页面
        window.location.href = `process.php?id=${appealId}&action=${action}`;
    }
}
</script>

            </div>
        </main>
    </div>
</body>
</html>
