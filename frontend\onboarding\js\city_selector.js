// 全局变量
let searchTimeout;

// 城市数据
const allCities = [
    // 直辖市
    '北京市', '上海市', '天津市', '重庆市',

    // 省会城市和主要城市
    '广州市', '深圳市', '成都市', '杭州市', '武汉市', '西安市', '苏州市', '南京市',
    '郑州市', '长沙市', '东莞市', '沈阳市', '青岛市', '合肥市', '佛山市', '济南市',
    '无锡市', '福州市', '厦门市', '哈尔滨市', '大连市', '昆明市', '长春市', '泉州市',
    '石家庄市', '贵阳市', '南宁市', '金华市', '常州市', '珠海市', '惠州市', '嘉兴市',
    '南通市', '中山市', '保定市', '兰州市', '台州市', '徐州市', '太原市', '绍兴市',
    '烟台市', '廊坊市', '唐山市', '秦皇岛市', '邯郸市', '邢台市', '张家口市', '承德市',
    '沧州市', '衡水市', '大同市', '阳泉市', '长治市', '晋城市', '朔州市', '晋中市',
    '运城市', '忻州市', '临汾市', '吕梁市', '呼和浩特市', '包头市', '乌海市', '赤峰市',
    '通辽市', '鄂尔多斯市', '呼伦贝尔市', '巴彦淖尔市', '乌兰察布市', '鞍山市', '抚顺市',
    '本溪市', '丹东市', '锦州市', '营口市', '阜新市', '辽阳市', '盘锦市', '铁岭市',
    '朝阳市', '葫芦岛市', '吉林市', '四平市', '辽源市', '通化市', '白山市', '松原市',
    '白城市', '齐齐哈尔市', '鸡西市', '鹤岗市', '双鸭山市', '大庆市', '伊春市', '佳木斯市',
    '七台河市', '牡丹江市', '黑河市', '绥化市', '宁波市', '温州市', '湖州市', '衢州市',
    '舟山市', '丽水市', '芜湖市', '蚌埠市', '淮南市', '马鞍山市', '淮北市', '铜陵市',
    '安庆市', '黄山市', '滁州市', '阜阳市', '宿州市', '六安市', '亳州市', '池州市',
    '宣城市', '莆田市', '三明市', '漳州市', '南平市', '龙岩市', '宁德市', '南昌市',
    '景德镇市', '萍乡市', '九江市', '新余市', '鹰潭市', '赣州市', '吉安市', '宜春市',
    '抚州市', '上饶市', '淄博市', '枣庄市', '东营市', '潍坊市', '济宁市', '泰安市',
    '威海市', '日照市', '临沂市', '德州市', '聊城市', '滨州市', '菏泽市', '开封市',
    '洛阳市', '平顶山市', '安阳市', '鹤壁市', '新乡市', '焦作市', '濮阳市', '许昌市',
    '漯河市', '三门峡市', '南阳市', '商丘市', '信阳市', '周口市', '驻马店市', '黄石市',
    '十堰市', '宜昌市', '襄阳市', '鄂州市', '荆门市', '孝感市', '荆州市', '黄冈市',
    '咸宁市', '随州市', '株洲市', '湘潭市', '衡阳市', '邵阳市', '岳阳市', '常德市',
    '张家界市', '益阳市', '郴州市', '永州市', '怀化市', '娄底市', '韶关市', '汕头市',
    '江门市', '湛江市', '茂名市', '肇庆市', '梅州市', '汕尾市', '河源市', '阳江市',
    '清远市', '潮州市', '揭阳市', '云浮市', '柳州市', '桂林市', '梧州市', '北海市',
    '防城港市', '钦州市', '贵港市', '玉林市', '百色市', '贺州市', '河池市', '来宾市',
    '崇左市', '海口市', '三亚市', '三沙市', '儋州市', '自贡市', '攀枝花市', '泸州市',
    '德阳市', '绵阳市', '广元市', '遂宁市', '内江市', '乐山市', '南充市', '眉山市',
    '宜宾市', '广安市', '达州市', '雅安市', '巴中市', '资阳市', '六盘水市', '遵义市',
    '安顺市', '毕节市', '铜仁市', '曲靖市', '玉溪市', '保山市', '昭通市', '丽江市',
    '普洱市', '临沧市', '拉萨市', '日喀则市', '昌都市', '林芝市', '山南市', '那曲市',
    '阿里地区', '铜川市', '宝鸡市', '咸阳市', '渭南市', '延安市', '汉中市', '榆林市',
    '安康市', '商洛市', '嘉峪关市', '金昌市', '白银市', '天水市', '武威市', '张掖市',
    '平凉市', '酒泉市', '庆阳市', '定西市', '陇南市', '西宁市', '海东市', '银川市',
    '石嘴山市', '吴忠市', '固原市', '中卫市', '乌鲁木齐市', '克拉玛依市', '吐鲁番市',
    '哈密市', '昌吉市', '博尔塔拉市', '巴音郭楞市', '阿克苏市', '克孜勒苏市', '喀什市',
    '和田市', '伊犁市', '塔城市', '阿勒泰市', '香港特别行政区', '澳门特别行政区',
    '台北市', '高雄市', '台中市', '台南市'
];

// Toast提示函数
function showToast(message, duration = 3000) {
    const toast = document.getElementById('toast');
    if (!toast) {
        alert(message);
        return;
    }

    if (toast.timer) {
        clearTimeout(toast.timer);
    }

    toast.style.display = 'none';
    void toast.offsetWidth;

    toast.textContent = message;
    toast.style.display = 'block';

    toast.timer = setTimeout(() => {
        toast.style.display = 'none';
    }, duration);
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    initializeSearch();
});



// 初始化搜索功能
function initializeSearch() {
    const searchInput = document.getElementById('search-input');
    const clearBtn = document.getElementById('clear-btn');
    const searchResults = document.getElementById('search-results');
    const popularCities = document.getElementById('popular-cities');
    const allCitiesSection = document.getElementById('all-cities');

    if (searchInput) {
        searchInput.addEventListener('input', function() {
            const query = this.value.trim();

            if (query.length > 0) {
                if (clearBtn) clearBtn.style.display = 'block';

                // 防抖搜索
                clearTimeout(searchTimeout);
                searchTimeout = setTimeout(() => {
                    searchCities(query);
                }, 300);
            } else {
                if (clearBtn) clearBtn.style.display = 'none';
                if (searchResults) searchResults.style.display = 'none';
                if (popularCities) popularCities.style.display = 'block';
                if (allCitiesSection) allCitiesSection.style.display = 'block';
            }
        });

        searchInput.addEventListener('focus', function() {
            if (this.value.trim().length > 0) {
                if (searchResults) searchResults.style.display = 'block';
                if (popularCities) popularCities.style.display = 'none';
                if (allCitiesSection) allCitiesSection.style.display = 'none';
            }
        });
    }
}

// 搜索城市
function searchCities(query) {
    // 使用本地城市数据进行搜索
    const filteredCities = allCities.filter(city => {
        return city.toLowerCase().includes(query.toLowerCase()) ||
               city.replace('市', '').toLowerCase().includes(query.toLowerCase()) ||
               city.replace('省', '').toLowerCase().includes(query.toLowerCase()) ||
               city.replace('自治区', '').toLowerCase().includes(query.toLowerCase()) ||
               city.replace('特别行政区', '').toLowerCase().includes(query.toLowerCase());
    });

    displaySearchResults(filteredCities);
}

// 显示搜索结果
function displaySearchResults(results) {
    const searchResults = document.getElementById('search-results');
    const resultsList = document.getElementById('results-list');
    const popularCities = document.getElementById('popular-cities');
    const allCitiesSection = document.getElementById('all-cities');

    if (!resultsList) return;

    resultsList.innerHTML = '';

    if (results.length === 0) {
        resultsList.innerHTML = '<div class="result-item"><div class="result-name">未找到相关城市</div></div>';
    } else {
        results.forEach(city => {
            const resultItem = document.createElement('button');
            resultItem.className = 'result-item';
            resultItem.onclick = () => selectCity(city);

            resultItem.innerHTML = `
                <div class="result-name">${city}</div>
            `;

            resultsList.appendChild(resultItem);
        });
    }

    if (searchResults) searchResults.style.display = 'block';
    if (popularCities) popularCities.style.display = 'none';
    if (allCitiesSection) allCitiesSection.style.display = 'none';
}



// 选择城市
function selectCity(cityName, address = '') {
    // 保存选择的城市到sessionStorage
    sessionStorage.setItem('selected_city', cityName);
    sessionStorage.setItem('selected_address', address);

    showToast('已选择：' + cityName);

    // 延迟返回上一页
    setTimeout(() => {
        goBack();
    }, 1000);
}

// 返回上一页
function goBack() {
    // 检查是否有选择的城市
    const selectedCity = sessionStorage.getItem('selected_city');
    if (selectedCity) {
        // 通知父页面更新地区信息
        if (window.opener && window.opener.updateRegion) {
            window.opener.updateRegion(selectedCity);
        }

        // 如果是在同一窗口中打开的，使用history.back()
        if (document.referrer) {
            history.back();
        } else {
            // 否则跳转到引导页面
            window.location.href = 'index.php';
        }
    } else {
        history.back();
    }
}

// 清空搜索
function clearSearch() {
    const searchInput = document.getElementById('search-input');
    const clearBtn = document.getElementById('clear-btn');
    const searchResults = document.getElementById('search-results');
    const popularCities = document.getElementById('popular-cities');
    const allCitiesSection = document.getElementById('all-cities');

    if (searchInput) searchInput.value = '';
    if (clearBtn) clearBtn.style.display = 'none';
    if (searchResults) searchResults.style.display = 'none';
    if (popularCities) popularCities.style.display = 'block';
    if (allCitiesSection) allCitiesSection.style.display = 'block';

    if (searchInput) searchInput.focus();
}

// 防止图片被保存或复制
document.addEventListener('DOMContentLoaded', function() {
    // 禁止右键菜单
    document.addEventListener('contextmenu', function(e) {
        e.preventDefault();
        return false;
    });
});
