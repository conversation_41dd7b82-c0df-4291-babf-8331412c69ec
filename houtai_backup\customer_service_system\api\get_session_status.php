<?php
// 获取会话状态API
header('Content-Type: application/json; charset=UTF-8');
ini_set('default_charset', 'UTF-8');

session_start();

// 检查客服登录状态
if (!isset($_SESSION['cs_logged_in']) || !$_SESSION['cs_logged_in']) {
    http_response_code(401);
    echo json_encode(['error' => '客服未登录']);
    exit;
}

// 获取参数
$session_id = $_GET['session_id'] ?? '';

if (empty($session_id)) {
    http_response_code(400);
    echo json_encode(['error' => '会话ID不能为空']);
    exit;
}

// 引用数据库配置文件
require_once '../../db_config.php';

try {
    $pdo = getDbConnection();
    
    // 获取会话信息
    $stmt = $pdo->prepare("
        SELECT s.*, 
               cs.name as cs_name, 
               cs.employee_id as cs_employee_id,
               TIMESTAMPDIFF(SECOND, s.started_at, COALESCE(s.ended_at, NOW())) as duration
        FROM customer_service_sessions s
        LEFT JOIN customer_service_users cs ON s.customer_service_id = cs.id
        WHERE s.session_id = ?
    ");
    $stmt->execute([$session_id]);
    $session = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$session) {
        http_response_code(404);
        echo json_encode(['error' => '会话不存在']);
        exit;
    }
    
    // 检查客服权限（只有分配的客服或超级管理员可以查看）
    if ($session['customer_service_id'] != $_SESSION['cs_user_id'] && $_SESSION['cs_role'] !== 'super_admin') {
        http_response_code(403);
        echo json_encode(['error' => '无权限访问此会话']);
        exit;
    }
    
    echo json_encode([
        'success' => true,
        'session' => $session,
        'timestamp' => time()
    ]);
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => '获取会话状态失败',
        'message' => $e->getMessage()
    ]);
}
?>
