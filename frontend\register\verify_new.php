<?php
session_start();

// 检查是否有验证的手机号
if (!isset($_SESSION['verified_phone'])) {
    // 如果没有验证的手机号，重定向到注册页面
    header('Location: index.php');
    exit();
}

$phone = $_SESSION['verified_phone'];

// 生成6位随机验证码
$verification_code = sprintf('%06d', mt_rand(0, 999999));
$_SESSION['verification_code'] = $verification_code;
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <title>输入验证码 - 趣玩星球</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        :root {
            --primary-color: #6F7BF5;
            --primary-dark: #5A67E8;
            --bg-white: #FFFFFF;
            --bg-gray: #F8F9FA;
            --text-primary: #1F2937;
            --text-secondary: #6B7280;
            --border-color: #E5E7EB;
            --border-focus: #40E0D0;
            --success-color: #48BB78;
            --error-color: #F56565;
            --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
            --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
            --radius-sm: 6px;
            --radius-md: 12px;
            --radius-lg: 16px;
            --radius-xl: 20px;
            --transition-normal: all 0.3s ease;
            --font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            background: var(--bg-white);
            color: var(--text-primary);
            font-family: var(--font-family);
            margin: 0;
            padding: 0;
            min-height: 100vh;
        }

        /* 登录容器样式 - 与登录页面完全一致 */
        .login-container {
            min-height: 100vh;
            background: var(--bg-white);
            position: relative;
            display: flex;
            flex-direction: column;
        }

        /* 顶部安全区域 */
        .safe-area-top {
            height: env(safe-area-inset-top);
            background: var(--bg-white);
        }

        /* 底部安全区域 */
        .safe-area-bottom {
            height: env(safe-area-inset-bottom);
            background: var(--bg-white);
        }

        /* 返回按钮 */
        .back-button {
            position: absolute;
            top: calc(env(safe-area-inset-top) + 16px);
            left: 20px;
            width: 40px;
            height: 40px;
            border: none;
            background: rgba(255, 255, 255, 0.9);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            z-index: 100;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            transition: var(--transition-normal);
        }

        .back-button:hover {
            background: var(--bg-white);
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }

        .back-button i {
            color: var(--text-primary);
            font-size: 16px;
        }

        /* 主要内容区域 */
        .main-content {
            flex: 1;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            padding: 20px;
            max-width: 400px;
            margin: 0 auto;
            width: 100%;
        }

        /* 品牌区域样式 */
        .brand-section {
            text-align: center;
            margin-bottom: 40px;
        }

        .brand-logo {
            width: 80px;
            height: 80px;
            margin: 0 auto 16px;
            border-radius: 20px;
            overflow: hidden;
            box-shadow: 0 8px 24px rgba(111, 123, 245, 0.15);
        }

        .brand-text img {
            max-width: 200px;
            height: auto;
        }

        /* 表单区域 */
        .form-section {
            width: 100%;
        }

        /* 错误和成功消息 */
        .error-message, .success-message {
            padding: 12px 16px;
            border-radius: var(--radius-md);
            margin-bottom: 16px;
            font-size: 14px;
            font-weight: 500;
            display: none;
        }

        .error-message {
            background: rgba(245, 101, 101, 0.1);
            color: var(--error-color);
            border: 1px solid rgba(245, 101, 101, 0.2);
        }

        .success-message {
            background: rgba(72, 187, 120, 0.1);
            color: var(--success-color);
            border: 1px solid rgba(72, 187, 120, 0.2);
        }

        /* 表单组 */
        .form-group {
            margin-bottom: 24px;
        }

        .form-label {
            display: block;
            font-size: 14px;
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 8px;
        }

        /* 验证码输入框样式 - 与登录页面完全一致 */
        .verification-inputs {
            display: flex;
            gap: 8px;
            justify-content: center;
            margin-bottom: 16px;
        }

        .verification-input {
            width: 48px;
            height: 56px;
            border: 2px solid var(--border-color);
            border-radius: var(--radius-md);
            text-align: center;
            font-size: 20px;
            font-weight: 600;
            color: var(--text-primary);
            background: var(--bg-white);
            transition: var(--transition-normal);
            outline: none;
            /* 立体质感效果 */
            box-shadow: 
                0 2px 4px rgba(0, 0, 0, 0.1),
                inset 0 1px 0 rgba(255, 255, 255, 0.8),
                inset 0 -1px 0 rgba(0, 0, 0, 0.05);
            position: relative;
        }

        .verification-input:focus {
            border-color: var(--primary-color);
            box-shadow: 
                0 0 0 3px rgba(111, 123, 245, 0.1),
                0 4px 8px rgba(0, 0, 0, 0.15),
                inset 0 1px 0 rgba(255, 255, 255, 0.9),
                inset 0 -1px 0 rgba(111, 123, 245, 0.1);
        }

        .verification-input.filled {
            border-color: var(--primary-color);
            background: rgba(111, 123, 245, 0.02);
            box-shadow: 
                0 3px 6px rgba(111, 123, 245, 0.15),
                inset 0 1px 0 rgba(255, 255, 255, 0.9),
                inset 0 -1px 0 rgba(111, 123, 245, 0.1);
        }

        /* 按钮样式 */
        .btn {
            width: 100%;
            padding: 16px;
            border: none;
            border-radius: var(--radius-md);
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: var(--transition-normal);
            position: relative;
            overflow: hidden;
            margin-bottom: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
        }

        .btn-primary {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
            color: white;
            box-shadow: var(--shadow-md);
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-lg);
        }

        .btn-secondary {
            background: var(--bg-gray);
            color: var(--text-secondary);
            border: 1px solid var(--border-color);
        }

        .btn-secondary:hover {
            background: var(--border-color);
            color: var(--text-primary);
        }

        /* 小按钮样式 */
        .btn-small {
            height: 36px;
            padding: 0 16px;
            font-size: 14px;
            font-weight: 500;
        }

        /* 重新发送按钮激活状态 */
        .btn-resend-active {
            background: var(--primary-color) !important;
            color: white !important;
            border: 1px solid var(--primary-color) !important;
        }

        .btn-resend-active:hover {
            background: var(--primary-dark) !important;
            color: white !important;
        }

        /* 按钮组样式 */
        .button-group {
            display: flex;
            gap: 8px;
            margin-bottom: 16px;
            justify-content: center;
        }

        /* 密码输入框 */
        .form-input {
            width: 100%;
            padding: 16px;
            border: 2px solid var(--border-color);
            border-radius: var(--radius-md);
            font-size: 16px;
            transition: var(--transition-normal);
            background: var(--bg-white);
        }

        .form-input:focus {
            outline: none;
            border-color: var(--border-focus);
            box-shadow: 0 0 0 3px rgba(64, 224, 208, 0.1);
        }
    </style>
</head>
<body>
    <div class="login-container">
        <!-- 顶部安全区域 -->
        <div class="safe-area-top"></div>

        <!-- 返回按钮 -->
        <button class="back-button" onclick="goBack()">
            <i class="fas fa-arrow-left"></i>
        </button>

        <!-- 主要内容区域 -->
        <div class="main-content">
            <!-- 品牌区域 -->
            <div class="brand-section">
                <div class="brand-logo">
                    <img src="https://s1.imagehub.cc/images/2025/05/26/2cdd63b39f96072fc60cd733027d2a30.png" alt="趣玩星球" style="width: 100%; height: 100%; object-fit: contain;">
                </div>
                <div class="brand-text">
                    <img src="https://s1.imagehub.cc/images/2025/05/26/ef3f8c98828faa0de2111ac0cfc9bd6d.png" alt="趣玩星球 - 探索有趣的生活">
                </div>
            </div>

            <!-- 表单区域 -->
            <div class="form-section">
                <!-- 错误和成功消息 -->
                <div class="error-message" id="errorMessage"></div>
                <div class="success-message" id="successMessage"></div>

                <!-- 验证码输入步骤 -->
                <div class="auth-step" id="verificationStep">
                    <div class="form-group">
                        <label class="form-label">输入验证码</label>
                        <p style="font-size: 14px; color: var(--text-secondary); margin-bottom: 16px;">
                            验证码已发送至 <span id="phone-display"><?php echo preg_replace('/(\d{3})\d{4}(\d{4})/', '$1****$2', $phone); ?></span>
                        </p>

                        <!-- 6位验证码输入框 -->
                        <div class="verification-inputs">
                            <input type="tel" class="verification-input" maxlength="1" data-index="0" inputmode="numeric" pattern="[0-9]" autocomplete="off">
                            <input type="tel" class="verification-input" maxlength="1" data-index="1" inputmode="numeric" pattern="[0-9]" autocomplete="off">
                            <input type="tel" class="verification-input" maxlength="1" data-index="2" inputmode="numeric" pattern="[0-9]" autocomplete="off">
                            <input type="tel" class="verification-input" maxlength="1" data-index="3" inputmode="numeric" pattern="[0-9]" autocomplete="off">
                            <input type="tel" class="verification-input" maxlength="1" data-index="4" inputmode="numeric" pattern="[0-9]" autocomplete="off">
                            <input type="tel" class="verification-input" maxlength="1" data-index="5" inputmode="numeric" pattern="[0-9]" autocomplete="off">
                        </div>

                        <!-- 发送验证码和查看验证码按钮组 -->
                        <div class="button-group">
                            <button type="button" class="btn btn-secondary btn-small" id="resendCodeBtn" onclick="resendCode()">
                                <i class="fas fa-paper-plane"></i>
                                重新发送
                            </button>
                            <button type="button" class="btn btn-secondary btn-small" onclick="showSmsModal()">
                                <i class="fas fa-sms"></i>
                                查看短信
                            </button>
                        </div>
                    </div>

                    <button type="button" class="btn btn-primary" onclick="verifyCode()">
                        <i class="fas fa-check"></i>
                        验证
                    </button>
                </div>
            </div>
        </div>

        <!-- 底部安全区域 -->
        <div class="safe-area-bottom"></div>
    </div>
</body>
</html>
