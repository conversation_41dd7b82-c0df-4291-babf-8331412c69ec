<?php
// 实时聊天页面 - 使用SSE
header('Content-Type: text/html; charset=UTF-8');
ini_set('default_charset', 'UTF-8');

session_start();

// 模拟用户登录（用于测试）
if (!isset($_SESSION['user_id'])) {
    $_SESSION['user_id'] = 4;
    $_SESSION['user_name'] = '测试用户';
}

$sessionId = $_GET['session_id'] ?? '';
if (empty($sessionId)) {
    echo '<h1>错误：缺少会话ID</h1>';
    exit;
}

require_once '../../houtai_backup/db_config.php';

// 获取会话信息
$session = null;
try {
    $pdo = getDbConnection();
    $stmt = $pdo->prepare("SELECT * FROM customer_service_sessions WHERE session_id = ? AND user_id = ?");
    $stmt->execute([$sessionId, $_SESSION['user_id']]);
    $session = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$session) {
        echo '<h1>错误：会话不存在或无权限访问</h1>';
        exit;
    }
} catch (Exception $e) {
    echo '<h1>错误：' . htmlspecialchars($e->getMessage()) . '</h1>';
    exit;
}
?>

<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>实时客服聊天</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f5f5f5;
            height: 100vh;
            display: flex;
            flex-direction: column;
        }
        
        .header {
            background: #6F7BF5;
            color: white;
            padding: 15px 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .header h1 {
            font-size: 18px;
            font-weight: 600;
        }
        
        .status {
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 14px;
        }
        
        .status-indicator {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: #dc3545;
        }
        
        .status-indicator.connected {
            background: #28a745;
        }
        
        .chat-container {
            flex: 1;
            display: flex;
            flex-direction: column;
            max-width: 800px;
            margin: 0 auto;
            background: white;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
        }
        
        .messages {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
            background: #fafafa;
        }
        
        .message {
            margin: 15px 0;
            display: flex;
            align-items: flex-end;
            gap: 10px;
        }
        
        .message.user {
            flex-direction: row-reverse;
        }
        
        .message-avatar {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 16px;
            color: white;
            flex-shrink: 0;
        }
        
        .message.user .message-avatar {
            background: #6F7BF5;
        }
        
        .message.cs .message-avatar {
            background: #28a745;
        }
        
        .message.system .message-avatar {
            background: #6c757d;
        }
        
        .message-content {
            max-width: 70%;
            background: white;
            padding: 12px 16px;
            border-radius: 18px;
            box-shadow: 0 1px 2px rgba(0,0,0,0.1);
            position: relative;
        }
        
        .message.user .message-content {
            background: #6F7BF5;
            color: white;
        }
        
        .message.system .message-content {
            background: #e9ecef;
            color: #495057;
            text-align: center;
            font-style: italic;
        }
        
        .message-sender {
            font-size: 12px;
            color: #666;
            margin-bottom: 4px;
        }
        
        .message.user .message-sender {
            color: rgba(255,255,255,0.8);
        }
        
        .message-text {
            font-size: 14px;
            line-height: 1.4;
            word-wrap: break-word;
        }
        
        .message-time {
            font-size: 11px;
            color: #999;
            margin-top: 4px;
        }
        
        .message.user .message-time {
            color: rgba(255,255,255,0.7);
        }
        
        .input-area {
            padding: 20px;
            border-top: 1px solid #e1e1e1;
            background: white;
        }
        
        .input-group {
            display: flex;
            gap: 10px;
            align-items: flex-end;
        }
        
        .message-input {
            flex: 1;
            padding: 12px 16px;
            border: 1px solid #ddd;
            border-radius: 25px;
            outline: none;
            font-size: 14px;
            resize: none;
            min-height: 44px;
            max-height: 120px;
        }
        
        .send-btn {
            width: 44px;
            height: 44px;
            background: #6F7BF5;
            color: white;
            border: none;
            border-radius: 50%;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 16px;
        }
        
        .send-btn:hover {
            background: #5a67d8;
        }
        
        .send-btn:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        
        .typing-indicator {
            display: none;
            padding: 10px 20px;
            color: #666;
            font-style: italic;
            font-size: 14px;
        }
        
        .notification {
            position: fixed;
            top: 20px;
            right: 20px;
            background: #28a745;
            color: white;
            padding: 15px 20px;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.3);
            z-index: 1000;
            max-width: 300px;
            animation: slideIn 0.3s ease;
        }
        
        .notification.error {
            background: #dc3545;
        }
        
        @keyframes slideIn {
            from { transform: translateX(100%); opacity: 0; }
            to { transform: translateX(0); opacity: 1; }
        }
        
        @media (max-width: 600px) {
            .chat-container {
                height: 100vh;
                margin: 0;
                border-radius: 0;
            }
            
            .header {
                padding: 10px 15px;
            }
            
            .messages {
                padding: 15px;
            }
            
            .input-area {
                padding: 15px;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <div>
            <h1>💬 客服聊天</h1>
            <div style="font-size: 12px; opacity: 0.9;">
                会话: <?php echo htmlspecialchars($sessionId); ?>
            </div>
        </div>
        <div class="status">
            <div id="status-indicator" class="status-indicator"></div>
            <span id="status-text">连接中...</span>
        </div>
    </div>
    
    <div class="chat-container">
        <div id="messages" class="messages">
            <div class="message system">
                <div class="message-avatar">🤖</div>
                <div class="message-content">
                    <div class="message-text">欢迎使用实时客服系统！正在建立连接...</div>
                </div>
            </div>
        </div>
        
        <div class="typing-indicator" id="typing-indicator">
            客服正在输入...
        </div>
        
        <div class="input-area">
            <div class="input-group">
                <textarea id="message-input" class="message-input" placeholder="输入消息..." rows="1"></textarea>
                <button id="send-btn" class="send-btn" onclick="sendMessage()">
                    <span>📤</span>
                </button>
            </div>
        </div>
    </div>
    
    <script>
        const sessionId = '<?php echo $sessionId; ?>';
        let eventSource = null;
        let isConnected = false;
        
        // 初始化SSE连接
        function initSSE() {
            console.log('初始化SSE连接，会话ID:', sessionId);
            
            eventSource = new EventSource(`api/sse_messages.php?session_id=${sessionId}`);
            
            eventSource.onopen = function(event) {
                console.log('SSE连接已打开');
                updateConnectionStatus(true);
            };
            
            eventSource.onmessage = function(event) {
                try {
                    const data = JSON.parse(event.data);
                    console.log('收到SSE消息:', data);
                    handleSSEMessage(data);
                } catch (error) {
                    console.error('解析SSE消息失败:', error);
                }
            };
            
            eventSource.onerror = function(event) {
                console.error('SSE连接错误:', event);
                updateConnectionStatus(false);
                
                // 5秒后重连
                setTimeout(() => {
                    if (eventSource.readyState === EventSource.CLOSED) {
                        console.log('尝试重新连接SSE...');
                        initSSE();
                    }
                }, 5000);
            };
        }
        
        // 处理SSE消息
        function handleSSEMessage(data) {
            switch(data.type) {
                case 'connected':
                    showNotification('实时连接已建立', 'success');
                    break;
                    
                case 'new_message':
                    addMessage('cs', data.content, data.sender_name);
                    playNotificationSound();
                    showNotification('收到新消息', 'success');
                    break;
                    
                case 'session_status_changed':
                    addMessage('system', `会话状态已变更：${data.old_status} → ${data.new_status}`);
                    break;
                    
                case 'heartbeat':
                    console.log('收到心跳:', data.timestamp);
                    break;
                    
                case 'error':
                    console.error('SSE错误:', data.message);
                    showNotification('连接错误: ' + data.message, 'error');
                    break;
            }
        }
        
        // 更新连接状态
        function updateConnectionStatus(connected) {
            isConnected = connected;
            const indicator = document.getElementById('status-indicator');
            const text = document.getElementById('status-text');
            
            if (connected) {
                indicator.classList.add('connected');
                text.textContent = '已连接';
            } else {
                indicator.classList.remove('connected');
                text.textContent = '连接中断';
            }
        }
        
        // 添加消息
        function addMessage(type, content, senderName = null) {
            const messagesContainer = document.getElementById('messages');
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${type}`;
            
            let avatarIcon = '';
            switch(type) {
                case 'user':
                    avatarIcon = '👤';
                    break;
                case 'cs':
                    avatarIcon = '👨‍💼';
                    break;
                case 'system':
                    avatarIcon = '🤖';
                    break;
            }
            
            const time = new Date().toLocaleTimeString('zh-CN', {
                hour: '2-digit',
                minute: '2-digit'
            });
            
            messageDiv.innerHTML = `
                <div class="message-avatar">${avatarIcon}</div>
                <div class="message-content">
                    ${senderName && type !== 'user' ? `<div class="message-sender">${senderName}</div>` : ''}
                    <div class="message-text">${content.replace(/\n/g, '<br>')}</div>
                    <div class="message-time">${time}</div>
                </div>
            `;
            
            messagesContainer.appendChild(messageDiv);
            messagesContainer.scrollTop = messagesContainer.scrollHeight;
        }
        
        // 发送消息
        async function sendMessage() {
            const input = document.getElementById('message-input');
            const message = input.value.trim();
            
            if (!message) return;
            
            // 添加用户消息
            addMessage('user', message);
            input.value = '';
            
            try {
                const response = await fetch('api/send_message.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        sessionId: sessionId,
                        message: message
                    })
                });
                
                const data = await response.json();
                
                if (!data.success) {
                    showNotification('发送失败: ' + data.error, 'error');
                }
            } catch (error) {
                console.error('发送消息失败:', error);
                showNotification('发送失败: 网络错误', 'error');
            }
        }
        
        // 显示通知
        function showNotification(message, type = 'success') {
            const notification = document.createElement('div');
            notification.className = `notification ${type}`;
            notification.textContent = message;
            
            document.body.appendChild(notification);
            
            setTimeout(() => {
                notification.remove();
            }, 3000);
        }
        
        // 播放通知音
        function playNotificationSound() {
            const audio = new Audio('data:audio/wav;base64,UklGRnoGAABXQVZFZm10IBAAAAABAAEAQB8AAEAfAAABAAgAZGF0YQoGAACBhYqFbF1fdJivrJBhNjVgodDbq2EcBj+a2/LDciUFLIHO8tiJNwgZaLvt559NEAxQp+PwtmMcBjiR1/LMeSwFJHfH8N2QQAoUXrTp66hVFApGn+DyvmwhBSuBzvLZiTYIG2m98OScTgwOUarm7blmGgU7k9n1unEiBC13yO/eizEIHWq+8+OWT');
            audio.volume = 0.3;
            audio.play().catch(e => console.log('无法播放提示音'));
        }
        
        // 输入框事件
        document.getElementById('message-input').addEventListener('keypress', function(e) {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                sendMessage();
            }
        });
        
        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            initSSE();
            
            // 页面卸载时关闭连接
            window.addEventListener('beforeunload', function() {
                if (eventSource) {
                    eventSource.close();
                }
            });
        });
    </script>
</body>
</html>
