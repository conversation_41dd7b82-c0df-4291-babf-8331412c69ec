<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>在线客服 - 趣玩星球</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            height: 100vh;
            overflow: hidden;
            overscroll-behavior: none;
            touch-action: pan-y;
            -webkit-overflow-scrolling: touch;
        }

        .chat-container {
            height: 100vh;
            display: flex;
            flex-direction: column;
            background: white;
            max-width: 800px;
            margin: 0 auto;
            box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
        }

        .chat-header {
            background: linear-gradient(135deg, #6F7BF5, #8B93F7);
            color: white;
            padding: 16px 20px;
            display: flex;
            align-items: center;
            gap: 12px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        .back-btn {
            background: none;
            border: none;
            color: white;
            font-size: 20px;
            cursor: pointer;
            padding: 8px;
            border-radius: 50%;
            transition: background 0.3s;
        }

        .back-btn:hover {
            background: rgba(255, 255, 255, 0.2);
        }

        .chat-title {
            flex: 1;
        }

        .chat-title h1 {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 2px;
        }

        .chat-subtitle {
            font-size: 12px;
            opacity: 0.8;
        }

        .chat-messages {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
            background: #f8f9fa;
        }

        .message {
            margin-bottom: 16px;
            display: flex;
            align-items: flex-start;
            gap: 12px;
        }

        .message.user {
            flex-direction: row-reverse;
        }

        .message-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: linear-gradient(135deg, #6F7BF5, #8B93F7);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 16px;
            flex-shrink: 0;
        }

        .message.user .message-avatar {
            background: linear-gradient(135deg, #10B981, #34D399);
        }

        .message-content {
            background: white;
            padding: 12px 16px 8px 16px;
            border-radius: 18px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            position: relative;
            display: inline-block;
            max-width: fit-content;
            min-width: 80px;
            word-wrap: break-word;
            white-space: pre-wrap;
        }

        .message.user .message-content {
            background: linear-gradient(135deg, #6F7BF5, #8B93F7);
            color: white;
            max-width: 80%;
            display: inline-block;
        }

        .message.bot .message-content {
            max-width: 70%;
        }

        .message-text {
            display: block;
            margin-bottom: 4px;
            line-height: 1.4;
        }

        .message-sender {
            font-size: 12px;
            color: #666;
            font-weight: 600;
            margin-bottom: 4px;
        }

        .message-time {
            font-size: 10px;
            opacity: 0.7;
            text-align: right;
            margin-top: 4px;
            line-height: 1;
        }

        .message.user .message-time {
            color: rgba(255, 255, 255, 0.8);
        }

        .message.bot .message-time {
            color: #999;
        }

        .chat-input {
            padding: 16px 20px;
            background: white;
            border-top: 1px solid #e5e7eb;
            display: flex;
            flex-direction: column;
            gap: 12px;
        }

        .input-toolbar {
            display: flex;
            gap: 8px;
            align-items: center;
            padding: 8px 0;
        }

        .toolbar-btn {
            width: 36px;
            height: 36px;
            background: #f8f9fa;
            border: none;
            border-radius: 50%;
            color: #666;
            font-size: 16px;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .toolbar-btn:hover {
            background: #6F7BF5;
            color: white;
            transform: scale(1.1);
        }

        .toolbar-btn.active {
            background: #6F7BF5;
            color: white;
        }

        .input-row {
            display: flex;
            gap: 12px;
            align-items: flex-end;
        }

        .input-wrapper {
            flex: 1;
            position: relative;
        }

        .message-input {
            width: 100%;
            min-height: 40px;
            max-height: 120px;
            padding: 10px 50px 10px 16px;
            border: 2px solid #e5e7eb;
            border-radius: 20px;
            font-size: 14px;
            resize: none;
            outline: none;
            transition: border-color 0.3s;
            font-size: 16px; /* 防止iOS自动缩放 */
            -webkit-appearance: none;
            -webkit-user-select: text;
        }

        .message-input:focus {
            border-color: #6F7BF5;
            transform: none; /* 防止焦点时的变换 */
        }

        .input-actions {
            position: absolute;
            right: 8px;
            top: 50%;
            transform: translateY(-50%);
            display: flex;
            gap: 4px;
        }

        .input-btn {
            width: 32px;
            height: 32px;
            background: none;
            border: none;
            border-radius: 50%;
            color: #666;
            font-size: 14px;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .input-btn:hover {
            background: #f0f0f0;
            color: #6F7BF5;
        }

        .send-btn {
            width: 40px;
            height: 40px;
            background: linear-gradient(135deg, #6F7BF5, #8B93F7);
            border: none;
            border-radius: 50%;
            color: white;
            font-size: 16px;
            cursor: pointer;
            transition: transform 0.2s;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .send-btn:hover {
            transform: scale(1.05);
        }

        .send-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none;
        }

        /* 表情面板 */
        .emoji-panel {
            position: absolute;
            bottom: 100%;
            left: 0;
            right: 0;
            background: white;
            border: 1px solid #e5e7eb;
            border-radius: 12px;
            padding: 16px;
            box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
            display: none;
            z-index: 1000;
        }

        .emoji-grid {
            display: grid;
            grid-template-columns: repeat(8, 1fr);
            gap: 8px;
            max-height: 200px;
            overflow-y: auto;
        }

        .emoji-item {
            width: 32px;
            height: 32px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 20px;
            cursor: pointer;
            border-radius: 6px;
            transition: background 0.2s;
        }

        .emoji-item:hover {
            background: #f0f0f0;
        }

        /* 文件上传 */
        .file-input {
            display: none;
        }

        /* 结束服务按钮 */
        .end-service-btn {
            background: linear-gradient(135deg, #FF6B6B, #FF8E8E);
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 12px;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-left: auto;
        }

        .end-service-btn:hover {
            transform: scale(1.05);
            box-shadow: 0 4px 12px rgba(255, 107, 107, 0.3);
        }

        /* 评价弹窗 */
        .rating-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.5);
            display: none;
            align-items: center;
            justify-content: center;
            z-index: 2000;
        }

        .rating-content {
            background: white;
            border-radius: 20px;
            padding: 30px;
            max-width: 400px;
            width: 90%;
            text-align: center;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
        }

        .rating-title {
            font-size: 20px;
            font-weight: 600;
            color: #333;
            margin-bottom: 20px;
        }

        .rating-stars {
            display: flex;
            justify-content: center;
            gap: 8px;
            margin-bottom: 20px;
        }

        .star {
            font-size: 32px;
            color: #ddd;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .star:hover,
        .star.active {
            color: #FFD166;
            transform: scale(1.1);
        }

        .rating-comment {
            width: 100%;
            min-height: 80px;
            padding: 12px;
            border: 2px solid #e5e7eb;
            border-radius: 12px;
            font-size: 14px;
            resize: none;
            outline: none;
            margin-bottom: 20px;
            transition: border-color 0.3s;
        }

        .rating-comment:focus {
            border-color: #6F7BF5;
        }

        .rating-actions {
            display: flex;
            gap: 12px;
            justify-content: center;
        }

        .rating-btn {
            padding: 10px 20px;
            border: none;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .rating-btn.primary {
            background: linear-gradient(135deg, #6F7BF5, #8B93F7);
            color: white;
        }

        .rating-btn.secondary {
            background: #f8f9fa;
            color: #666;
        }

        .rating-btn:hover {
            transform: translateY(-2px);
        }

        .typing-indicator {
            display: none;
            padding: 12px 16px;
            color: #666;
            font-style: italic;
            font-size: 14px;
        }

        .welcome-message {
            text-align: center;
            padding: 20px;
            color: #666;
            background: white;
            border-radius: 12px;
            margin-bottom: 20px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
        }

        .welcome-message h3 {
            color: #6F7BF5;
            margin-bottom: 8px;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .chat-container {
                height: 100vh;
                border-radius: 0;
            }

            .message.user .message-content {
                max-width: 85%;
            }

            .message.bot .message-content {
                max-width: 80%;
            }

            /* 防止移动端页面缩放 */
            .message-input {
                font-size: 16px !important;
            }
        }

        /* 加载动画 */
        .loading-dots {
            display: inline-block;
        }

        .loading-dots::after {
            content: '';
            animation: dots 1.5s infinite;
        }

        @keyframes dots {
            0%, 20% { content: ''; }
            40% { content: '.'; }
            60% { content: '..'; }
            80%, 100% { content: '...'; }
        }
    </style>
</head>
<body>
    <div class="chat-container">
        <!-- 聊天头部 -->
        <div class="chat-header">
            <button class="back-btn" onclick="goBack()">
                <i class="fas fa-arrow-left"></i>
            </button>
            <div class="chat-title">
                <h1>在线客服</h1>
                <div class="chat-subtitle">我们将竭诚为您服务</div>
            </div>
            <button class="end-service-btn" onclick="endService()">
                <i class="fas fa-times"></i>
                结束服务
            </button>
        </div>

        <!-- 聊天消息区域 -->
        <div class="chat-messages" id="chatMessages">
            <div class="welcome-message">
                <h3><i class="fas fa-robot"></i> 趣玩小助手</h3>
                <p>您好！我是趣玩星球智能客服，有什么可以帮助您的吗？</p>
            </div>
        </div>

        <!-- 输入提示 -->
        <div class="typing-indicator" id="typingIndicator">
            客服正在输入<span class="loading-dots"></span>
        </div>

        <!-- 聊天输入区域 -->
        <div class="chat-input">
            <!-- 工具栏 -->
            <div class="input-toolbar">
                <button class="toolbar-btn" id="emojiBtn" onclick="toggleEmoji()" title="表情">
                    <i class="fas fa-smile"></i>
                </button>
                <button class="toolbar-btn" onclick="selectImage()" title="图片">
                    <i class="fas fa-image"></i>
                </button>
                <button class="toolbar-btn" onclick="selectFile()" title="文件">
                    <i class="fas fa-paperclip"></i>
                </button>
                <button class="toolbar-btn" onclick="transferToHuman()" title="转人工">
                    <i class="fas fa-user-tie"></i>
                </button>
            </div>

            <!-- 输入行 -->
            <div class="input-row">
                <div class="input-wrapper">
                    <textarea
                        class="message-input"
                        id="messageInput"
                        placeholder="请输入您的问题..."
                        rows="1"
                    ></textarea>
                    <div class="input-actions">
                        <button class="input-btn" onclick="clearInput()" title="清空">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>

                    <!-- 表情面板 -->
                    <div class="emoji-panel" id="emojiPanel">
                        <div class="emoji-grid" id="emojiGrid">
                            <!-- 表情将通过JavaScript动态生成 -->
                        </div>
                    </div>
                </div>
                <button class="send-btn" id="sendBtn" onclick="sendMessage()">
                    <i class="fas fa-paper-plane"></i>
                </button>
            </div>

            <!-- 隐藏的文件输入 -->
            <input type="file" id="imageInput" class="file-input" accept="image/*" onchange="handleImageUpload(event)">
            <input type="file" id="fileInput" class="file-input" accept="*" onchange="handleFileUpload(event)">
        </div>

        <!-- 评价弹窗 -->
        <div class="rating-modal" id="ratingModal">
            <div class="rating-content">
                <h3 class="rating-title">服务评价</h3>
                <p>请为本次客服服务打分</p>

                <div class="rating-stars" id="ratingStars">
                    <span class="star" data-rating="1">★</span>
                    <span class="star" data-rating="2">★</span>
                    <span class="star" data-rating="3">★</span>
                    <span class="star" data-rating="4">★</span>
                    <span class="star" data-rating="5">★</span>
                </div>

                <textarea
                    class="rating-comment"
                    id="ratingComment"
                    placeholder="请输入您的评价和建议（可选）"
                ></textarea>

                <div class="rating-actions">
                    <button class="rating-btn secondary" onclick="closeRating()">跳过</button>
                    <button class="rating-btn primary" onclick="submitRating()">提交评价</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 引入现有的实时通知系统 -->
    <script src="../assets/js/websocket_notifications.js"></script>

    <script>
        let sessionId = generateSessionId();
        let botConfig = null;
        let currentRating = 0;
        let isServiceEnded = false;

        // 常用表情列表
        const emojis = [
            '😀', '😃', '😄', '😁', '😆', '😅', '😂', '🤣',
            '😊', '😇', '🙂', '🙃', '😉', '😌', '😍', '🥰',
            '😘', '😗', '😙', '😚', '😋', '😛', '😝', '😜',
            '🤪', '🤨', '🧐', '🤓', '😎', '🤩', '🥳', '😏',
            '😒', '😞', '😔', '😟', '😕', '🙁', '☹️', '😣',
            '😖', '😫', '😩', '🥺', '😢', '😭', '😤', '😠',
            '😡', '🤬', '🤯', '😳', '🥵', '🥶', '😱', '😨',
            '😰', '😥', '😓', '🤗', '🤔', '🤭', '🤫', '🤥',
            '😶', '😐', '😑', '😬', '🙄', '😯', '😦', '😧',
            '😮', '😲', '🥱', '😴', '🤤', '😪', '😵', '🤐',
            '🥴', '🤢', '🤮', '🤧', '😷', '🤒', '🤕', '🤑',
            '🤠', '😈', '👿', '👹', '👺', '🤡', '💩', '👻',
            '💀', '☠️', '👽', '👾', '🤖', '🎃', '😺', '😸',
            '😹', '😻', '😼', '😽', '🙀', '😿', '😾', '👋',
            '🤚', '🖐️', '✋', '🖖', '👌', '🤏', '✌️', '🤞',
            '🤟', '🤘', '🤙', '👈', '👉', '👆', '🖕', '👇',
            '☝️', '👍', '👎', '👊', '✊', '🤛', '🤜', '👏',
            '🙌', '👐', '🤲', '🤝', '🙏', '✍️', '💅', '🤳'
        ];

        // 页面加载时初始化
        document.addEventListener('DOMContentLoaded', function() {
            loadBotConfig();
            setupInputEvents();
            initializeEmojis();
            initializeRating();
            initializeRealTimeNotifications();
        });

        // 生成会话ID
        function generateSessionId() {
            return 'session_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
        }

        // 加载机器人配置
        async function loadBotConfig() {
            try {
                const response = await fetch('api/get_bot_config.php');
                const data = await response.json();

                if (data.success) {
                    botConfig = data.data;
                    // 显示欢迎消息
                    if (botConfig.welcome_message) {
                        addMessage('bot', botConfig.welcome_message);
                    }
                }
            } catch (error) {
                console.error('加载机器人配置失败:', error);
                // 显示默认欢迎消息
                addMessage('bot', '您好！欢迎来到趣玩星球，我是您的专属客服小助手，有什么可以帮助您的吗？');
            }
        }

        // 初始化表情面板
        function initializeEmojis() {
            const emojiGrid = document.getElementById('emojiGrid');
            emojis.forEach(emoji => {
                const emojiItem = document.createElement('div');
                emojiItem.className = 'emoji-item';
                emojiItem.textContent = emoji;
                emojiItem.onclick = () => insertEmoji(emoji);
                emojiGrid.appendChild(emojiItem);
            });
        }

        // 初始化评价系统
        function initializeRating() {
            const stars = document.querySelectorAll('.star');
            stars.forEach((star, index) => {
                star.addEventListener('click', () => setRating(index + 1));
                star.addEventListener('mouseenter', () => highlightStars(index + 1));
            });

            document.getElementById('ratingStars').addEventListener('mouseleave', () => {
                highlightStars(currentRating);
            });
        }

        // 设置输入事件
        function setupInputEvents() {
            const input = document.getElementById('messageInput');
            const sendBtn = document.getElementById('sendBtn');

            // 自动调整输入框高度
            input.addEventListener('input', function() {
                this.style.height = 'auto';
                this.style.height = Math.min(this.scrollHeight, 120) + 'px';

                // 控制发送按钮状态
                sendBtn.disabled = !this.value.trim();
            });

            // 回车发送消息
            input.addEventListener('keydown', function(e) {
                if (e.key === 'Enter' && !e.shiftKey) {
                    e.preventDefault();
                    sendMessage();
                }
            });

            // 点击外部关闭表情面板
            document.addEventListener('click', function(e) {
                if (!e.target.closest('.input-wrapper')) {
                    hideEmoji();
                }
            });
        }

        // 切换表情面板
        function toggleEmoji() {
            const panel = document.getElementById('emojiPanel');
            const btn = document.getElementById('emojiBtn');

            if (panel.style.display === 'block') {
                hideEmoji();
            } else {
                showEmoji();
            }
        }

        // 显示表情面板
        function showEmoji() {
            const panel = document.getElementById('emojiPanel');
            const btn = document.getElementById('emojiBtn');

            panel.style.display = 'block';
            btn.classList.add('active');
        }

        // 隐藏表情面板
        function hideEmoji() {
            const panel = document.getElementById('emojiPanel');
            const btn = document.getElementById('emojiBtn');

            panel.style.display = 'none';
            btn.classList.remove('active');
        }

        // 插入表情
        function insertEmoji(emoji) {
            const input = document.getElementById('messageInput');
            const start = input.selectionStart;
            const end = input.selectionEnd;
            const text = input.value;

            input.value = text.substring(0, start) + emoji + text.substring(end);
            input.selectionStart = input.selectionEnd = start + emoji.length;
            input.focus();

            // 触发input事件以调整高度和按钮状态
            input.dispatchEvent(new Event('input'));

            hideEmoji();
        }

        // 选择图片
        function selectImage() {
            document.getElementById('imageInput').click();
        }

        // 选择文件
        function selectFile() {
            document.getElementById('fileInput').click();
        }

        // 处理图片上传
        function handleImageUpload(event) {
            const file = event.target.files[0];
            if (file) {
                // 检查文件大小（限制为5MB）
                if (file.size > 5 * 1024 * 1024) {
                    alert('图片大小不能超过5MB');
                    return;
                }

                // 检查文件类型
                if (!file.type.startsWith('image/')) {
                    alert('请选择图片文件');
                    return;
                }

                // 显示上传中消息
                addMessage('user', '正在上传图片...');

                // 这里应该实现实际的文件上传逻辑
                // 暂时模拟上传成功
                setTimeout(() => {
                    addMessage('user', `[图片] ${file.name}`);
                    addMessage('bot', '我看到您发送了一张图片，请问有什么可以帮助您的吗？');
                }, 1000);
            }
        }

        // 处理文件上传
        function handleFileUpload(event) {
            const file = event.target.files[0];
            if (file) {
                // 检查文件大小（限制为10MB）
                if (file.size > 10 * 1024 * 1024) {
                    alert('文件大小不能超过10MB');
                    return;
                }

                // 显示上传中消息
                addMessage('user', '正在上传文件...');

                // 这里应该实现实际的文件上传逻辑
                // 暂时模拟上传成功
                setTimeout(() => {
                    addMessage('user', `[文件] ${file.name}`);
                    addMessage('bot', '我收到了您的文件，请问有什么可以帮助您的吗？');
                }, 1000);
            }
        }

        // 转人工客服
        function transferToHuman() {
            if (confirm('确定要转接人工客服吗？')) {
                addMessage('system', '正在为您转接人工客服，请稍候...');

                // 这里应该实现转接逻辑
                setTimeout(() => {
                    addMessage('system', '已为您转接人工客服，客服将很快为您服务');
                    addMessage('customer_service', '您好！我是人工客服，很高兴为您服务，请问有什么可以帮助您的？');
                }, 2000);
            }
        }

        // 清空输入
        function clearInput() {
            const input = document.getElementById('messageInput');
            input.value = '';
            input.style.height = 'auto';
            document.getElementById('sendBtn').disabled = true;
            input.focus();
        }

        // 结束服务
        function endService() {
            if (isServiceEnded) {
                goBack();
                return;
            }

            if (confirm('确定要结束客服服务吗？')) {
                isServiceEnded = true;
                addMessage('system', '感谢您使用趣玩星球客服服务！');

                // 显示评价弹窗
                setTimeout(() => {
                    showRating();
                }, 1000);
            }
        }

        // 显示评价弹窗
        function showRating() {
            document.getElementById('ratingModal').style.display = 'flex';
        }

        // 关闭评价弹窗
        function closeRating() {
            document.getElementById('ratingModal').style.display = 'none';
            goBack();
        }

        // 设置评分
        function setRating(rating) {
            currentRating = rating;
            highlightStars(rating);
        }

        // 高亮星星
        function highlightStars(rating) {
            const stars = document.querySelectorAll('.star');
            stars.forEach((star, index) => {
                if (index < rating) {
                    star.classList.add('active');
                } else {
                    star.classList.remove('active');
                }
            });
        }

        // 初始化实时通知
        function initializeRealTimeNotifications() {
            // 检查是否有用户ID（已登录）
            <?php if (isset($_SESSION['user_id'])): ?>
            const userId = <?php echo $_SESSION['user_id']; ?>;

            // 使用现有的实时通知系统
            if (typeof WebSocketNotifications !== 'undefined') {
                const notifications = new WebSocketNotifications(userId);
                notifications.connect();

                // 监听客服消息和会话状态变化
                notifications.onMessage = function(data) {
                    if (data.session_id === sessionId) {
                        switch(data.type) {
                            case 'customer_service_message':
                                // 收到客服回复消息
                                addMessage('customer_service', data.message, data.cs_name);
                                playNotificationSound();
                                break;

                            case 'session_accepted':
                                // 客服接受会话
                                addMessage('system', `客服 ${data.cs_name} 已为您服务`);
                                playNotificationSound();
                                break;

                            case 'session_closed':
                                // 会话被客服结束
                                isServiceEnded = true;
                                addMessage('system', '客服已结束会话，感谢您的使用！');
                                setTimeout(() => {
                                    showRating();
                                }, 1000);
                                break;
                        }
                    }
                };
            } else {
                // 降级到轮询方式
                startPollingForMessages();
            }
            <?php endif; ?>
        }

        // 开始轮询消息（降级方案）
        let lastCheckTime = Math.floor(Date.now() / 1000);
        let pollingInterval = null;

        function startPollingForMessages() {
            console.log('🔄 开始轮询消息，会话ID:', sessionId);
            console.log('📅 初始检查时间:', lastCheckTime);

            // 清除之前的轮询
            if (pollingInterval) {
                clearInterval(pollingInterval);
            }

            pollingInterval = setInterval(async function() {
                try {
                    const url = `api/get_new_messages.php?session_id=${encodeURIComponent(sessionId)}&last_check=${lastCheckTime}`;
                    console.log('📡 轮询请求:', url);

                    const response = await fetch(url);
                    const data = await response.json();

                    console.log('📨 轮询响应:', data);

                    if (data.success && data.messages && data.messages.length > 0) {
                        console.log('🎉 收到新消息:', data.messages.length, '条');
                        data.messages.forEach(message => {
                            console.log('💬 处理消息:', message);
                            if (message.sender_type === 'customer_service') {
                                addMessage('customer_service', message.content, message.sender_name);
                                playNotificationSound();
                            }
                        });
                        // 更新最后检查时间为当前时间戳
                        lastCheckTime = Math.floor(Date.now() / 1000);
                        console.log('⏰ 更新检查时间:', lastCheckTime);
                    } else {
                        console.log('📭 没有新消息');
                    }
                } catch (error) {
                    console.error('❌ 轮询消息失败:', error);
                }
            }, 2000); // 每2秒检查一次，更频繁
        }

        // 播放通知音
        function playNotificationSound() {
            // 创建音频提示
            const audio = new Audio('data:audio/wav;base64,UklGRnoGAABXQVZFZm10IBAAAAABAAEAQB8AAEAfAAABAAgAZGF0YQoGAACBhYqFbF1fdJivrJBhNjVgodDbq2EcBj+a2/LDciUFLIHO8tiJNwgZaLvt559NEAxQp+PwtmMcBjiR1/LMeSwFJHfH8N2QQAoUXrTp66hVFApGn+DyvmwhBSuBzvLZiTYIG2m98OScTgwOUarm7blmGgU7k9n1unEiBC13yO/eizEIHWq+8+OWT');
            audio.volume = 0.3;
            audio.play().catch(e => console.log('无法播放提示音'));
        }

        // 提交评价
        async function submitRating() {
            if (currentRating === 0) {
                alert('请选择评分');
                return;
            }

            const comment = document.getElementById('ratingComment').value;

            try {
                const response = await fetch('api/submit_rating.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        sessionId: sessionId,
                        rating: currentRating,
                        comment: comment
                    })
                });

                const data = await response.json();

                if (data.success) {
                    alert('感谢您的评价！');
                    closeRating();
                } else {
                    alert('评价提交失败：' + (data.error || '未知错误'));
                }
            } catch (error) {
                console.error('提交评价失败:', error);
                alert('评价提交失败，请稍后重试');
            }
        }

        // 发送消息
        async function sendMessage() {
            const input = document.getElementById('messageInput');
            const message = input.value.trim();

            if (!message || isServiceEnded) return;

            // 添加用户消息
            addMessage('user', message);
            input.value = '';
            input.style.height = 'auto';
            document.getElementById('sendBtn').disabled = true;

            // 显示输入提示
            showTypingIndicator();

            try {
                console.log('📤 发送消息:', message, '会话ID:', sessionId);

                // 发送到后端处理
                const response = await fetch('api/send_message.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        sessionId: sessionId,
                        message: message
                    })
                });

                const data = await response.json();
                console.log('📨 发送响应:', data);

                // 隐藏输入提示
                hideTypingIndicator();

                if (data.success) {
                    // 如果有机器人回复
                    if (data.bot_reply) {
                        setTimeout(() => {
                            addMessage('bot', data.bot_reply, data.bot_name);
                        }, 500);
                    }

                    // 如果创建了新会话，更新会话ID
                    if (data.session_id && data.session_id !== sessionId) {
                        sessionId = data.session_id;
                        console.log('🆕 更新会话ID:', sessionId);

                        // 重新开始轮询
                        startPollingForMessages();
                    }
                } else {
                    console.error('❌ 发送失败:', data.error);
                    addMessage('bot', '抱歉，系统暂时无法处理您的问题，请稍后重试。');
                }
            } catch (error) {
                console.error('❌ 发送消息失败:', error);
                hideTypingIndicator();
                addMessage('bot', '网络连接异常，请检查网络后重试。');
            }
        }

        // 添加消息到聊天区域
        function addMessage(type, content, senderName = null) {
            const messagesContainer = document.getElementById('chatMessages');
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${type}`;

            const avatar = document.createElement('div');
            avatar.className = 'message-avatar';

            // 根据消息类型设置头像
            let avatarIcon = '';
            switch(type) {
                case 'user':
                    avatarIcon = '<i class="fas fa-user"></i>';
                    break;
                case 'bot':
                    avatarIcon = '<i class="fas fa-robot"></i>';
                    break;
                case 'customer_service':
                    avatarIcon = '<i class="fas fa-headset"></i>';
                    break;
                case 'system':
                    avatarIcon = '<i class="fas fa-cog"></i>';
                    break;
                default:
                    avatarIcon = '<i class="fas fa-comment"></i>';
            }
            avatar.innerHTML = avatarIcon;

            const messageContent = document.createElement('div');
            messageContent.className = 'message-content';

            // 创建发送者名称（如果有）
            if (senderName && type !== 'user') {
                const senderDiv = document.createElement('div');
                senderDiv.className = 'message-sender';
                senderDiv.textContent = senderName;
                messageContent.appendChild(senderDiv);
            }

            // 创建消息文本
            const messageText = document.createElement('div');
            messageText.className = 'message-text';
            messageText.innerHTML = content.replace(/\n/g, '<br>');

            // 创建时间戳
            const messageTime = document.createElement('div');
            messageTime.className = 'message-time';
            messageTime.textContent = new Date().toLocaleTimeString('zh-CN', {
                hour: '2-digit',
                minute: '2-digit'
            });

            // 将文本和时间戳都放在气泡内
            messageContent.appendChild(messageText);
            messageContent.appendChild(messageTime);

            messageDiv.appendChild(avatar);
            messageDiv.appendChild(messageContent);

            messagesContainer.appendChild(messageDiv);

            // 滚动到底部
            messagesContainer.scrollTop = messagesContainer.scrollHeight;
        }

        // 显示输入提示
        function showTypingIndicator() {
            document.getElementById('typingIndicator').style.display = 'block';
        }

        // 隐藏输入提示
        function hideTypingIndicator() {
            document.getElementById('typingIndicator').style.display = 'none';
        }

        // 返回上一页
        function goBack() {
            if (window.history.length > 1) {
                window.history.back();
            } else {
                window.location.href = '../login/index.php';
            }
        }
    </script>
</body>
</html>
