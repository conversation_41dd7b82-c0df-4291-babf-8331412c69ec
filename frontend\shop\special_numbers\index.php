<?php
session_start();

// 检查用户是否已登录
if (!isset($_SESSION['user_id'])) {
    header('Location: ../../login/index.php');
    exit;
}
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <meta name="theme-color" content="#7B68EE">
    <title>靓号 - 趣玩商城</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'PingFang SC', 'Helvetica Neue', Arial, sans-serif;
        }
        
        body {
            background-color: #f8f9fa;
            color: #333333;
            line-height: 1.6;
            min-height: 100vh;
        }
        
        .header-bar {
            display: flex;
            align-items: center;
            padding: 15px;
            background-color: #7B68EE;
            color: white;
            position: sticky;
            top: 0;
            z-index: 100;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }
        
        .back-button {
            margin-right: 15px;
            font-size: 18px;
            color: white;
            text-decoration: none;
        }
        
        .header-title {
            font-size: 18px;
            font-weight: 600;
            flex-grow: 1;
        }
        
        .search-bar {
            display: flex;
            align-items: center;
            background-color: white;
            border-radius: 20px;
            margin: 15px;
            padding: 10px 15px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
        }
        
        .search-bar i {
            color: #999;
            margin-right: 10px;
        }
        
        .search-bar input {
            border: none;
            outline: none;
            flex-grow: 1;
            font-size: 15px;
        }
        
        .filter-tabs {
            display: flex;
            padding: 0 15px;
            margin-bottom: 15px;
            overflow-x: auto;
            scrollbar-width: none; /* Firefox */
            -ms-overflow-style: none; /* IE and Edge */
        }
        
        .filter-tabs::-webkit-scrollbar {
            display: none; /* Chrome, Safari, Opera */
        }
        
        .filter-tab {
            padding: 8px 15px;
            margin-right: 10px;
            background-color: white;
            border-radius: 20px;
            font-size: 14px;
            color: #666;
            white-space: nowrap;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
        }
        
        .filter-tab.active {
            background-color: #7B68EE;
            color: white;
        }
        
        .number-list {
            padding: 0 15px;
            margin-bottom: 20px;
        }
        
        .number-card {
            background-color: white;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
            margin-bottom: 15px;
            transition: transform 0.3s ease;
            padding: 15px;
        }
        
        .number-card:active {
            transform: scale(0.98);
        }
        
        .number-display {
            text-align: center;
            padding: 15px 0;
            margin-bottom: 10px;
            border-bottom: 1px dashed #eee;
        }
        
        .number-value {
            font-size: 24px;
            font-weight: bold;
            color: #333;
            letter-spacing: 2px;
        }
        
        .number-info {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .number-details {
            flex-grow: 1;
        }
        
        .number-type {
            font-size: 14px;
            color: #7B68EE;
            margin-bottom: 5px;
            font-weight: 500;
        }
        
        .number-price {
            color: #FF6B00;
            font-size: 18px;
            font-weight: bold;
        }
        
        .number-price small {
            font-size: 13px;
            font-weight: normal;
        }
        
        .buy-button {
            background: linear-gradient(90deg, #7B68EE, #55E4F5);
            color: white;
            border: none;
            border-radius: 20px;
            padding: 8px 20px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            box-shadow: 0 2px 8px rgba(123, 104, 238, 0.3);
        }
        
        .number-tag {
            display: inline-block;
            background-color: #FF6B00;
            color: white;
            font-size: 12px;
            padding: 2px 8px;
            border-radius: 10px;
            margin-left: 10px;
        }
        
        .empty-notice {
            text-align: center;
            padding: 30px 15px;
            color: #999;
            font-size: 15px;
        }
    </style>
    <!-- 引入字体图标库 -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <!-- 顶部导航栏 -->
    <div class="header-bar">
        <a href="../index.php" class="back-button">
            <i class="fas fa-arrow-left"></i>
        </a>
        <div class="header-title">靓号</div>
    </div>
    
    <!-- 搜索栏 -->
    <div class="search-bar">
        <i class="fas fa-search"></i>
        <input type="text" placeholder="搜索靓号">
    </div>
    
    <!-- 筛选标签 -->
    <div class="filter-tabs">
        <div class="filter-tab active">全部</div>
        <div class="filter-tab">AAAA</div>
        <div class="filter-tab">ABAB</div>
        <div class="filter-tab">ABCD</div>
        <div class="filter-tab">生日号</div>
        <div class="filter-tab">幸运号</div>
    </div>
    
    <!-- 靓号列表 -->
    <div class="number-list">
        <div class="number-card">
            <div class="number-display">
                <div class="number-value">88888</div>
            </div>
            <div class="number-info">
                <div class="number-details">
                    <div class="number-type">AAAAA类<span class="number-tag">稀有</span></div>
                    <div class="number-price"><small>¥</small>1888</div>
                </div>
                <button class="buy-button">购买</button>
            </div>
        </div>
        
        <div class="number-card">
            <div class="number-display">
                <div class="number-value">12345</div>
            </div>
            <div class="number-info">
                <div class="number-details">
                    <div class="number-type">ABCDE类<span class="number-tag">顺子</span></div>
                    <div class="number-price"><small>¥</small>688</div>
                </div>
                <button class="buy-button">购买</button>
            </div>
        </div>
        
        <div class="number-card">
            <div class="number-display">
                <div class="number-value">520520</div>
            </div>
            <div class="number-info">
                <div class="number-details">
                    <div class="number-type">ABABAB类<span class="number-tag">情侣号</span></div>
                    <div class="number-price"><small>¥</small>888</div>
                </div>
                <button class="buy-button">购买</button>
            </div>
        </div>
        
        <div class="number-card">
            <div class="number-display">
                <div class="number-value">666999</div>
            </div>
            <div class="number-info">
                <div class="number-details">
                    <div class="number-type">AAABBB类<span class="number-tag">吉祥号</span></div>
                    <div class="number-price"><small>¥</small>999</div>
                </div>
                <button class="buy-button">购买</button>
            </div>
        </div>
    </div>
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 筛选标签点击效果
            const filterTabs = document.querySelectorAll('.filter-tab');
            filterTabs.forEach(tab => {
                tab.addEventListener('click', function() {
                    // 移除所有标签的active类
                    filterTabs.forEach(t => t.classList.remove('active'));
                    
                    // 添加active类到当前标签
                    this.classList.add('active');
                    
                    // 这里可以添加筛选逻辑
                    const filter = this.textContent.trim();
                    // 示例：如果选择了特定类型，只显示该类型的靓号
                    if (filter !== '全部') {
                        alert(`已选择筛选条件：${filter}\n筛选功能即将上线，敬请期待`);
                    }
                });
            });
            
            // 购买按钮点击效果
            const buyButtons = document.querySelectorAll('.buy-button');
            buyButtons.forEach(button => {
                button.addEventListener('click', function(e) {
                    e.stopPropagation(); // 阻止事件冒泡
                    const number = this.closest('.number-card').querySelector('.number-value').textContent;
                    alert(`购买功能即将上线，敬请期待\n靓号：${number}`);
                });
            });
            
            // 靓号卡片点击效果
            const numberCards = document.querySelectorAll('.number-card');
            numberCards.forEach(card => {
                card.addEventListener('click', function() {
                    const number = this.querySelector('.number-value').textContent;
                    alert(`靓号详情页即将上线，敬请期待\n靓号：${number}`);
                });
            });
        });
    </script>
</body>
</html>
