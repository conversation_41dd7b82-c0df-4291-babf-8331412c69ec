<?php
/**
 * 验证码验证
 */
session_start();

// 设置响应类型为JSON
header('Content-Type: application/json');

// 获取提交的验证码
$captcha = isset($_POST['captcha']) ? trim($_POST['captcha']) : '';

// 获取会话中的验证码
$session_captcha = isset($_SESSION['captcha']) ? $_SESSION['captcha'] : '';

// 验证验证码
if (empty($captcha)) {
    echo json_encode([
        'success' => false,
        'message' => '请输入验证码'
    ]);
    exit;
}

if (empty($session_captcha)) {
    echo json_encode([
        'success' => false,
        'message' => '验证码已过期，请重新获取'
    ]);
    exit;
}

// 转换为大写后比较
if (strtoupper($captcha) !== $session_captcha) {
    echo json_encode([
        'success' => false,
        'message' => '验证码错误，请重新输入'
    ]);
    exit;
}

// 验证成功，清除会话中的验证码（一次性使用）
unset($_SESSION['captcha']);

echo json_encode([
    'success' => true,
    'message' => '验证码正确'
]);
exit;
