<?php
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// 处理预检请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

// 只接受POST请求
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode(['success' => false, 'message' => '只接受POST请求'], JSON_UNESCAPED_UNICODE);
    exit;
}

// 获取请求数据
$input = json_decode(file_get_contents('php://input'), true);

if (!$input || !isset($input['message']) || !isset($input['session_id'])) {
    echo json_encode(['success' => false, 'message' => '请求参数不完整'], JSON_UNESCAPED_UNICODE);
    exit;
}

$message = trim($input['message']);
$session_id = $input['session_id'];

if (empty($message)) {
    echo json_encode(['success' => false, 'message' => '消息不能为空'], JSON_UNESCAPED_UNICODE);
    exit;
}

// 数据库配置
$host = 'localhost';
$dbname = 'quwanplanet';
$username = 'quwanplanet';
$password = 'nJmJm23FB4Xn6Fc3';

try {
    // 连接数据库
    $pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password, [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
        PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES utf8mb4"
    ]);

    // 获取用户ID（如果已登录）
    $user_id = null;
    if (isset($_SESSION['user_id'])) {
        $user_id = $_SESSION['user_id'];
    }

    // 查找匹配的回复规则
    $reply = findBotReply($pdo, $message);

    // 保存对话记录
    $stmt = $pdo->prepare("
        INSERT INTO customer_service_conversations
        (session_id, user_id, user_message, bot_reply, created_at)
        VALUES (?, ?, ?, ?, NOW())
    ");
    $stmt->execute([$session_id, $user_id, $message, $reply]);

    echo json_encode([
        'success' => true,
        'reply' => $reply,
        'session_id' => $session_id
    ], JSON_UNESCAPED_UNICODE);

} catch (PDOException $e) {
    echo json_encode([
        'success' => false,
        'message' => '数据库错误',
        'error' => $e->getMessage()
    ], JSON_UNESCAPED_UNICODE);
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => '处理失败',
        'error' => $e->getMessage()
    ], JSON_UNESCAPED_UNICODE);
}

/**
 * 查找机器人回复
 */
function findBotReply($pdo, $message) {
    try {
        // 获取所有启用的回复规则，按优先级排序
        $stmt = $pdo->prepare("
            SELECT keywords, reply_content, priority
            FROM customer_service_replies
            WHERE is_enabled = 1
            ORDER BY priority DESC, id ASC
        ");
        $stmt->execute();
        $rules = $stmt->fetchAll();

        $message_lower = mb_strtolower($message, 'UTF-8');

        // 遍历规则查找匹配
        foreach ($rules as $rule) {
            $keywords = json_decode($rule['keywords'], true);

            if (!is_array($keywords)) {
                continue;
            }

            // 检查是否包含任何关键词
            foreach ($keywords as $keyword) {
                if (mb_strpos($message_lower, mb_strtolower($keyword, 'UTF-8')) !== false) {
                    return $rule['reply_content'];
                }
            }
        }

        // 没有匹配的规则，返回默认回复
        return getDefaultReply($pdo);

    } catch (Exception $e) {
        return getDefaultReply($pdo);
    }
}

/**
 * 获取默认回复
 */
function getDefaultReply($pdo) {
    try {
        $stmt = $pdo->prepare("SELECT default_reply FROM customer_service_bot WHERE is_enabled = 1 ORDER BY id DESC LIMIT 1");
        $stmt->execute();
        $config = $stmt->fetch();

        if ($config && !empty($config['default_reply'])) {
            return $config['default_reply'];
        }

        // 硬编码的默认回复
        return '抱歉，我暂时无法理解您的问题😅

您可以：
• 换个方式描述问题
• 联系人工客服
• 查看帮助文档

如需人工客服，请回复"人工客服"';

    } catch (Exception $e) {
        return '抱歉，系统暂时无法处理您的问题，请稍后重试或联系人工客服。';
    }
}
?>
