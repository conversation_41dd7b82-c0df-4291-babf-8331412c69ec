<?php
/**
 * 权限管理 - 权限申请页面
 * 趣玩星球管理后台
 */

session_start();

// 登录检查
if (!isset($_SESSION['admin_id'])) {
    header('Location: ../login.php');
    exit;
}

$admin_name = $_SESSION['admin_name'] ?? '管理员';
$admin_id = $_SESSION['admin_id'] ?? '';
$admin_employee_id = $_SESSION['admin_employee_id'] ?? '';

// 获取权限申请数据
require_once '../db_config.php';

try {
    $pdo = getDbConnection();

    // 获取我的权限申请
    $stmt = $pdo->prepare("
        SELECT pr.*, p.name as permission_name, p.description as permission_desc,
               reviewer.name as reviewer_name
        FROM permission_requests pr
        LEFT JOIN permissions p ON pr.permission_id = p.id
        LEFT JOIN admin_users reviewer ON pr.reviewer_id = reviewer.id
        WHERE pr.user_id = ?
        ORDER BY pr.created_at DESC
        LIMIT 10
    ");
    $stmt->execute([$admin_id]);
    $my_requests = $stmt->fetchAll();

    // 获取可申请的权限列表（排除已拥有的权限）
    $stmt = $pdo->prepare("
        SELECT p.* FROM permissions p
        WHERE p.id NOT IN (
            SELECT COALESCE(up.permission_id, 0) FROM user_permissions up WHERE up.user_id = ?
            UNION
            SELECT COALESCE(rp.permission_id, 0) FROM user_roles ur
            JOIN role_permissions rp ON ur.role_id = rp.role_id
            WHERE ur.user_id = ?
        )
        ORDER BY p.module, p.name
    ");
    $stmt->execute([$admin_id, $admin_id]);
    $available_permissions = $stmt->fetchAll();

} catch (Exception $e) {
    // 如果数据库查询失败，使用模拟数据
    $my_requests = [
        [
            'id' => 1,
            'permission_name' => '用户管理权限',
            'status' => 'pending',
            'reason' => '需要管理部门内的用户账户',
            'created_at' => '2024-05-20 10:30:00',
            'reviewer_name' => null
        ]
    ];

    $available_permissions = [
        ['id' => 1, 'name' => '用户管理权限', 'description' => '可以管理用户账户信息'],
        ['id' => 2, 'name' => '内容审核权限', 'description' => '可以审核用户发布的内容'],
        ['id' => 3, 'name' => '数据导出权限', 'description' => '可以导出系统数据报表']
    ];
}
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>权限申请 - 趣玩星球管理后台</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="../assets/css/admin-layout.css">
    <style>
        /* 权限申请页面专用样式 */
        .permission-content {
            padding: 24px;
        }

        .page-header {
            margin-bottom: 32px;
        }

        .page-title {
            font-size: 2rem;
            font-weight: 700;
            color: var(--gray-800);
            margin-bottom: 8px;
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .page-subtitle {
            color: var(--gray-600);
            font-size: 1.1rem;
        }

        .content-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 32px;
        }

        .section-card {
            background: white;
            border-radius: 16px;
            padding: 24px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
            border: 2px solid transparent;
            transition: all 0.3s ease;
        }

        .section-card:hover {
            border-color: var(--primary-color);
            transform: translateY(-2px);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
        }

        .section-header {
            display: flex;
            align-items: center;
            gap: 12px;
            margin-bottom: 20px;
            padding-bottom: 16px;
            border-bottom: 2px solid var(--gray-100);
        }

        .section-icon {
            width: 48px;
            height: 48px;
            background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 20px;
        }

        .section-title {
            font-size: 1.25rem;
            font-weight: 700;
            color: var(--gray-800);
        }

        /* 申请表单样式 */
        .apply-form {
            display: flex;
            flex-direction: column;
            gap: 20px;
        }

        .form-group {
            display: flex;
            flex-direction: column;
            gap: 8px;
        }

        .form-label {
            font-weight: 600;
            color: var(--gray-700);
            font-size: 0.875rem;
        }

        .form-select,
        .form-textarea {
            padding: 12px 16px;
            border: 2px solid var(--gray-200);
            border-radius: var(--border-radius);
            font-size: 0.875rem;
            transition: var(--transition);
            background: white;
        }

        .form-select:focus,
        .form-textarea:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(64, 224, 208, 0.1);
        }

        .form-textarea {
            resize: vertical;
            min-height: 100px;
        }

        .apply-btn {
            background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: var(--border-radius);
            font-weight: 600;
            cursor: pointer;
            transition: var(--transition);
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
        }

        .apply-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(64, 224, 208, 0.3);
        }

        .apply-btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        /* 申请记录样式 */
        .request-list {
            display: flex;
            flex-direction: column;
            gap: 16px;
        }

        .request-item {
            background: var(--gray-50);
            border-radius: 12px;
            padding: 16px;
            border-left: 4px solid var(--primary-color);
            transition: var(--transition);
        }

        .request-item:hover {
            background: var(--gray-100);
            transform: translateX(4px);
        }

        .request-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 8px;
        }

        .request-title {
            font-weight: 600;
            color: var(--gray-800);
            font-size: 0.875rem;
        }

        .request-status {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 0.75rem;
            font-weight: 600;
        }

        .status-pending {
            background: rgba(245, 158, 11, 0.1);
            color: var(--warning-color);
        }

        .status-approved {
            background: rgba(16, 185, 129, 0.1);
            color: var(--success-color);
        }

        .status-rejected {
            background: rgba(239, 68, 68, 0.1);
            color: var(--error-color);
        }

        .request-meta {
            font-size: 0.75rem;
            color: var(--gray-500);
            margin-bottom: 8px;
        }

        .request-reason {
            font-size: 0.875rem;
            color: var(--gray-600);
            line-height: 1.4;
        }

        .empty-state {
            text-align: center;
            padding: 40px 20px;
            color: var(--gray-500);
        }

        .empty-icon {
            font-size: 48px;
            margin-bottom: 16px;
            opacity: 0.5;
        }

        /* 响应式设计 */
        @media (max-width: 1024px) {
            .content-grid {
                grid-template-columns: 1fr;
                gap: 24px;
            }
        }

        @media (max-width: 768px) {
            .permission-content {
                padding: 16px;
            }

            .page-title {
                font-size: 1.5rem;
            }
        }
    </style>
</head>
<body>
    <div class="admin-layout">
        <!-- 左侧菜单 -->
        <?php include '../includes/sidebar.php'; ?>

        <!-- 主内容区域 -->
        <main class="main-content">
            <!-- 顶部导航栏 -->
            <?php include '../includes/topbar.php'; ?>

            <div class="permission-content">
                <!-- 页面标题 -->
                <div class="page-header">
                    <h1 class="page-title">
                        <i class="fas fa-hand-paper" style="color: var(--primary-color);"></i>
                        权限申请
                    </h1>
                    <p class="page-subtitle">申请新的系统权限，提升工作效率</p>
                </div>

                <div class="content-grid">
                    <!-- 申请新权限 -->
                    <div class="section-card">
                        <div class="section-header">
                            <div class="section-icon">
                                <i class="fas fa-plus"></i>
                            </div>
                            <div class="section-title">申请新权限</div>
                        </div>

                        <form class="apply-form" id="permissionForm">
                            <div class="form-group">
                                <label class="form-label">选择权限</label>
                                <select class="form-select" name="permission_id" required>
                                    <option value="">请选择需要申请的权限</option>
                                    <?php foreach ($available_permissions as $permission): ?>
                                        <option value="<?php echo $permission['id']; ?>">
                                            <?php echo htmlspecialchars($permission['name']); ?>
                                            <?php if ($permission['description']): ?>
                                                - <?php echo htmlspecialchars($permission['description']); ?>
                                            <?php endif; ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>

                            <div class="form-group">
                                <label class="form-label">申请理由</label>
                                <textarea class="form-textarea" name="reason" placeholder="请详细说明申请该权限的理由和用途..." required></textarea>
                            </div>

                            <button type="submit" class="apply-btn">
                                <i class="fas fa-paper-plane"></i>
                                提交申请
                            </button>
                        </form>
                    </div>

                    <!-- 我的申请记录 -->
                    <div class="section-card">
                        <div class="section-header">
                            <div class="section-icon" style="background: linear-gradient(135deg, var(--info-color), #60A5FA);">
                                <i class="fas fa-history"></i>
                            </div>
                            <div class="section-title">我的申请记录</div>
                        </div>

                        <div class="request-list">
                            <?php if (empty($my_requests)): ?>
                                <div class="empty-state">
                                    <div class="empty-icon">
                                        <i class="fas fa-inbox"></i>
                                    </div>
                                    <p>暂无申请记录</p>
                                </div>
                            <?php else: ?>
                                <?php foreach ($my_requests as $request): ?>
                                    <div class="request-item">
                                        <div class="request-header">
                                            <div class="request-title">
                                                <?php echo htmlspecialchars($request['permission_name'] ?? '未知权限'); ?>
                                            </div>
                                            <div class="request-status status-<?php echo $request['status']; ?>">
                                                <?php
                                                $status_text = [
                                                    'pending' => '待审核',
                                                    'approved' => '已通过',
                                                    'rejected' => '已拒绝'
                                                ];
                                                echo $status_text[$request['status']] ?? $request['status'];
                                                ?>
                                            </div>
                                        </div>
                                        <div class="request-meta">
                                            申请时间：<?php echo date('Y-m-d H:i', strtotime($request['created_at'])); ?>
                                            <?php if ($request['reviewer_name']): ?>
                                                | 审核人：<?php echo htmlspecialchars($request['reviewer_name']); ?>
                                            <?php endif; ?>
                                        </div>
                                        <div class="request-reason">
                                            <?php echo htmlspecialchars($request['reason']); ?>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <script src="../assets/js/admin-layout.js"></script>
    <script>
        // 权限申请页面脚本
        document.addEventListener('DOMContentLoaded', function() {
            // 更新页面标题
            updatePageTitle('权限申请');

            // 表单提交处理
            const form = document.getElementById('permissionForm');
            form.addEventListener('submit', function(e) {
                e.preventDefault();

                const formData = new FormData(form);
                const submitBtn = form.querySelector('.apply-btn');

                // 验证表单
                const permissionId = formData.get('permission_id');
                const reason = formData.get('reason');

                if (!permissionId || !reason.trim()) {
                    alert('请填写完整的申请信息！');
                    return;
                }

                if (reason.trim().length < 10) {
                    alert('申请理由至少需要10个字符！');
                    return;
                }

                // 禁用提交按钮
                submitBtn.disabled = true;
                submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 提交中...';

                // 提交申请
                fetch('apply_permission.php', {
                    method: 'POST',
                    body: formData
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        alert(data.message);
                        form.reset();
                        // 刷新页面显示新申请
                        setTimeout(() => {
                            location.reload();
                        }, 1000);
                    } else {
                        alert('申请失败：' + data.message);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('网络错误，请重试');
                })
                .finally(() => {
                    // 恢复按钮
                    submitBtn.disabled = false;
                    submitBtn.innerHTML = '<i class="fas fa-paper-plane"></i> 提交申请';
                });
            });

            // 动画效果
            const cards = document.querySelectorAll('.section-card, .request-item');
            cards.forEach((card, index) => {
                card.style.opacity = '0';
                card.style.transform = 'translateY(20px)';
                setTimeout(() => {
                    card.style.transition = 'all 0.5s ease';
                    card.style.opacity = '1';
                    card.style.transform = 'translateY(0)';
                }, index * 100);
            });
        });

        console.log('🔐 权限申请页面已加载');
    </script>
</body>
</html>
