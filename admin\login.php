<?php
// 引入session配置
require_once '../sql/session_config.php';
initAdminSession();

// 如果已登录，跳转到仪表盘
if (isAdminLoggedIn()) {
    header('Location: index.php');
    exit;
}

// 生成验证码会话
if (!isset($_SESSION['captcha'])) {
    $_SESSION['captcha'] = '';
}

$error_message = '';

// 检查登录限制
$client_ip = $_SERVER['REMOTE_ADDR'];
$lockout_time = 5 * 60; // 5分钟
$max_attempts = 5;

// 数据库连接
require_once '../sql/db_config.php';
$pdo = getDbConnection();

// 检查是否被锁定
$stmt = $pdo->prepare("
    SELECT COUNT(*) as attempts, MAX(attempt_time) as last_attempt
    FROM login_attempts
    WHERE ip_address = ? AND attempt_time > DATE_SUB(NOW(), INTERVAL ? SECOND)
");
$stmt->execute([$client_ip, $lockout_time]);
$lockout_check = $stmt->fetch(PDO::FETCH_ASSOC);

$is_locked = $lockout_check['attempts'] >= $max_attempts;
$remaining_time = 0;

if ($is_locked) {
    $last_attempt = strtotime($lockout_check['last_attempt']);
    $remaining_time = $lockout_time - (time() - $last_attempt);
    if ($remaining_time > 0) {
        $error_message = "登录失败次数过多，请等待 " . ceil($remaining_time / 60) . " 分钟后再试";
    } else {
        $is_locked = false;
    }
}

if ($_SERVER['REQUEST_METHOD'] === 'POST' && !$is_locked) {
    $login_field = trim($_POST['login_field'] ?? '');
    $password = $_POST['password'] ?? '';
    $captcha = $_POST['captcha'] ?? '';

    // 验证验证码
    if (empty($captcha) || strtolower($captcha) !== strtolower($_SESSION['captcha'] ?? '')) {
        $error_message = '验证码错误';
        // 记录失败尝试
        recordLoginAttempt($pdo, $client_ip, $login_field, 'captcha_error');
    } elseif (empty($login_field) || empty($password)) {
        $error_message = '请输入工号/邮箱和密码';
    } else {
        try {
            // 支持工号或邮箱登录，适配现有表结构
            $stmt = $pdo->prepare("
                SELECT * FROM admin_users
                WHERE (employee_id = ? OR email = ?) AND status = 1
            ");
            $stmt->execute([$login_field, $login_field]);
            $admin = $stmt->fetch(PDO::FETCH_ASSOC);

            if ($admin && password_verify($password, $admin['password'])) {
                // 登录成功
                setAdminLoginSession($admin);

                // 更新最后登录时间
                $stmt = $pdo->prepare("UPDATE admin_users SET last_login = NOW() WHERE id = ?");
                $stmt->execute([$admin['id']]);

                // 清除失败记录
                $stmt = $pdo->prepare("DELETE FROM login_attempts WHERE ip_address = ?");
                $stmt->execute([$client_ip]);

                // 记录成功登录
                recordLoginAttempt($pdo, $client_ip, $login_field, 'success');

                header('Location: index.php');
                exit;
            } else {
                $error_message = '工号/邮箱或密码错误';
                // 记录失败尝试
                recordLoginAttempt($pdo, $client_ip, $login_field, 'invalid_credentials');
            }
        } catch (PDOException $e) {
            error_log("Admin login error: " . $e->getMessage());
            $error_message = '系统错误，请稍后重试';
            recordLoginAttempt($pdo, $client_ip, $login_field, 'system_error');
        }
    }
}

// 记录登录尝试
function recordLoginAttempt($pdo, $ip, $login_field, $result) {
    try {
        $stmt = $pdo->prepare("
            INSERT INTO login_attempts (ip_address, login_field, result, attempt_time, user_agent)
            VALUES (?, ?, ?, NOW(), ?)
        ");
        $stmt->execute([$ip, $login_field, $result, $_SERVER['HTTP_USER_AGENT'] ?? '']);
    } catch (PDOException $e) {
        error_log("Failed to record login attempt: " . $e->getMessage());
    }
}
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>管理员登录 - 趣玩星球</title>
    <link rel="stylesheet" href="css/login.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <div class="login-container">
        <div class="login-box">
            <div class="login-header">
                <div class="logo">
                    <i class="fas fa-star"></i>
                </div>
                <h1>趣玩星球</h1>
                <p>后台管理系统</p>
            </div>

            <form class="login-form" method="POST">
                <?php if ($error_message): ?>
                    <div class="error-message">
                        <i class="fas fa-exclamation-circle"></i>
                        <?php echo htmlspecialchars($error_message); ?>
                    </div>
                <?php endif; ?>

                <div class="form-group">
                    <label for="login_field">工号/邮箱</label>
                    <div class="input-group">
                        <i class="fas fa-user"></i>
                        <input type="text" id="login_field" name="login_field" placeholder="请输入工号或邮箱地址" required
                               value="<?php echo htmlspecialchars($_POST['login_field'] ?? ''); ?>"
                               <?php echo $is_locked ? 'disabled' : ''; ?>>
                    </div>
                </div>

                <div class="form-group">
                    <label for="password">密码</label>
                    <div class="input-group">
                        <i class="fas fa-lock"></i>
                        <input type="password" id="password" name="password" placeholder="请输入密码" required
                               <?php echo $is_locked ? 'disabled' : ''; ?>>
                        <button type="button" class="toggle-password" onclick="togglePassword()"
                                <?php echo $is_locked ? 'disabled' : ''; ?>>
                            <i class="fas fa-eye" id="toggleIcon"></i>
                        </button>
                    </div>
                </div>

                <div class="form-group">
                    <label for="captcha">验证码</label>
                    <div class="captcha-group">
                        <div class="input-group">
                            <i class="fas fa-shield-alt"></i>
                            <input type="text" id="captcha" name="captcha" placeholder="请输入验证码" required maxlength="4"
                                   <?php echo $is_locked ? 'disabled' : ''; ?>>
                        </div>
                        <div class="captcha-image">
                            <img src="captcha.php" alt="验证码" id="captchaImg" onclick="refreshCaptcha()">
                            <button type="button" class="refresh-captcha" onclick="refreshCaptcha()" title="刷新验证码"
                                    <?php echo $is_locked ? 'disabled' : ''; ?>>
                                <i class="fas fa-sync-alt"></i>
                            </button>
                        </div>
                    </div>
                </div>

                <button type="submit" class="login-btn" <?php echo $is_locked ? 'disabled' : ''; ?>>
                    <i class="fas fa-sign-in-alt"></i>
                    <?php echo $is_locked ? "等待 " . ceil($remaining_time / 60) . " 分钟" : "登录"; ?>
                </button>
            </form>

            <div class="login-footer">
                <p>© 2024 趣玩星球. 保留所有权利.</p>
            </div>
        </div>

        <div class="background-decoration">
            <div class="circle circle-1"></div>
            <div class="circle circle-2"></div>
            <div class="circle circle-3"></div>
        </div>
    </div>

    <script>
        function togglePassword() {
            const passwordInput = document.getElementById('password');
            const toggleIcon = document.getElementById('toggleIcon');

            if (passwordInput.type === 'password') {
                passwordInput.type = 'text';
                toggleIcon.className = 'fas fa-eye-slash';
            } else {
                passwordInput.type = 'password';
                toggleIcon.className = 'fas fa-eye';
            }
        }

        // 刷新验证码
        function refreshCaptcha() {
            const captchaImg = document.getElementById('captchaImg');
            captchaImg.src = 'captcha.php?' + new Date().getTime();
        }

        // 自动聚焦到登录字段输入框
        document.addEventListener('DOMContentLoaded', function() {
            const loginField = document.getElementById('login_field');
            if (loginField && !loginField.disabled) {
                loginField.focus();
            }

            // 如果被锁定，显示倒计时
            <?php if ($is_locked && $remaining_time > 0): ?>
            let remainingTime = <?php echo $remaining_time; ?>;
            const loginBtn = document.querySelector('.login-btn');

            const countdown = setInterval(() => {
                remainingTime--;
                const minutes = Math.ceil(remainingTime / 60);
                loginBtn.innerHTML = '<i class="fas fa-clock"></i> 等待 ' + minutes + ' 分钟';

                if (remainingTime <= 0) {
                    clearInterval(countdown);
                    location.reload();
                }
            }, 1000);
            <?php endif; ?>
        });
    </script>
</body>
</html>
