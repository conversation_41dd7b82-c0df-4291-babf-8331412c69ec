/**
 * 发起露营活动页面JavaScript
 * 处理活动发布逻辑
 */

class CreateActivityPage {
    constructor() {
        this.loadingOverlay = document.getElementById('loading-overlay');
        this.toast = document.getElementById('toast');
        this.form = document.getElementById('activityForm');
        
        this.init();
    }

    init() {
        this.bindEvents();
        this.setDefaultDateTime();
        this.disableBrowserAlerts();
    }

    bindEvents() {
        // 表单提交事件
        this.form.addEventListener('submit', (e) => {
            e.preventDefault();
            this.publishActivity();
        });

        // 返回按钮事件
        const backBtn = document.querySelector('.back-btn');
        if (backBtn) {
            backBtn.addEventListener('click', () => {
                this.goBack();
            });
        }

        // 实时验证
        const inputs = this.form.querySelectorAll('input, select, textarea');
        inputs.forEach(input => {
            input.addEventListener('blur', () => {
                this.validateField(input);
            });
        });

        // 时间验证
        const startDate = document.getElementById('start_date');
        const endDate = document.getElementById('end_date');
        
        startDate.addEventListener('change', () => {
            this.validateDateRange();
        });
        
        endDate.addEventListener('change', () => {
            this.validateDateRange();
        });
    }

    // 禁用浏览器原生弹窗
    disableBrowserAlerts() {
        window.alert = (message) => {
            this.showToast(message);
        };

        window.confirm = (message) => {
            this.showToast(message);
            return true;
        };
    }

    // 设置默认日期时间
    setDefaultDateTime() {
        const now = new Date();
        const tomorrow = new Date(now.getTime() + 24 * 60 * 60 * 1000);
        const dayAfterTomorrow = new Date(now.getTime() + 48 * 60 * 60 * 1000);
        
        // 设置开始时间为明天上午9点
        tomorrow.setHours(9, 0, 0, 0);
        document.getElementById('start_date').value = this.formatDateTimeLocal(tomorrow);
        
        // 设置结束时间为后天下午6点
        dayAfterTomorrow.setHours(18, 0, 0, 0);
        document.getElementById('end_date').value = this.formatDateTimeLocal(dayAfterTomorrow);
    }

    // 格式化日期时间
    formatDateTimeLocal(date) {
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const day = String(date.getDate()).padStart(2, '0');
        const hours = String(date.getHours()).padStart(2, '0');
        const minutes = String(date.getMinutes()).padStart(2, '0');
        
        return `${year}-${month}-${day}T${hours}:${minutes}`;
    }

    // 验证单个字段
    validateField(field) {
        const value = field.value.trim();
        let isValid = true;
        let message = '';

        switch (field.name) {
            case 'title':
                if (!value) {
                    isValid = false;
                    message = '请输入活动标题';
                } else if (value.length < 5) {
                    isValid = false;
                    message = '活动标题至少需要5个字符';
                } else if (value.length > 50) {
                    isValid = false;
                    message = '活动标题不能超过50个字符';
                }
                break;
                
            case 'description':
                if (!value) {
                    isValid = false;
                    message = '请输入活动描述';
                } else if (value.length < 20) {
                    isValid = false;
                    message = '活动描述至少需要20个字符';
                }
                break;
                
            case 'location':
                if (!value) {
                    isValid = false;
                    message = '请输入活动地点';
                }
                break;
                
            case 'max_participants':
                const maxParticipants = parseInt(value);
                if (!maxParticipants || maxParticipants < 2) {
                    isValid = false;
                    message = '最大参与人数不能少于2人';
                } else if (maxParticipants > 50) {
                    isValid = false;
                    message = '最大参与人数不能超过50人';
                }
                break;
                
            case 'price':
                const price = parseFloat(value);
                if (isNaN(price) || price < 0) {
                    isValid = false;
                    message = '请输入有效的活动费用';
                }
                break;
        }

        this.setFieldValidation(field, isValid, message);
        return isValid;
    }

    // 设置字段验证状态
    setFieldValidation(field, isValid, message) {
        const formGroup = field.closest('.form-group');
        
        // 移除之前的验证状态
        formGroup.classList.remove('field-valid', 'field-invalid');
        
        // 移除之前的错误消息
        const existingError = formGroup.querySelector('.field-error');
        if (existingError) {
            existingError.remove();
        }

        if (!isValid && message) {
            formGroup.classList.add('field-invalid');
            
            // 添加错误消息
            const errorEl = document.createElement('div');
            errorEl.className = 'field-error';
            errorEl.textContent = message;
            formGroup.appendChild(errorEl);
        } else if (isValid && field.value.trim()) {
            formGroup.classList.add('field-valid');
        }
    }

    // 验证日期范围
    validateDateRange() {
        const startDate = new Date(document.getElementById('start_date').value);
        const endDate = new Date(document.getElementById('end_date').value);
        const now = new Date();

        let isValid = true;
        let message = '';

        if (startDate <= now) {
            isValid = false;
            message = '活动开始时间必须晚于当前时间';
        } else if (endDate <= startDate) {
            isValid = false;
            message = '活动结束时间必须晚于开始时间';
        } else if ((endDate - startDate) < 2 * 60 * 60 * 1000) {
            isValid = false;
            message = '活动持续时间至少需要2小时';
        }

        if (!isValid) {
            this.showToast(message);
        }

        return isValid;
    }

    // 验证整个表单
    validateForm() {
        const requiredFields = this.form.querySelectorAll('[required]');
        let isValid = true;

        // 验证必填字段
        requiredFields.forEach(field => {
            if (!this.validateField(field)) {
                isValid = false;
            }
        });

        // 验证日期范围
        if (!this.validateDateRange()) {
            isValid = false;
        }

        // 验证至少选择一个特色
        const features = this.form.querySelectorAll('input[name="features[]"]:checked');
        if (features.length === 0) {
            this.showToast('请至少选择一个活动特色');
            isValid = false;
        }

        return isValid;
    }

    // 获取表单数据
    getFormData() {
        const formData = new FormData(this.form);
        const data = {};

        // 获取基本字段
        for (let [key, value] of formData.entries()) {
            if (key === 'features[]') {
                if (!data.features) {
                    data.features = [];
                }
                data.features.push(value);
            } else {
                data[key] = value;
            }
        }

        // 处理特色标签
        if (data.features) {
            data.features = data.features.join(',');
        } else {
            data.features = '';
        }

        return data;
    }

    // 保存草稿
    saveDraft() {
        if (!this.validateBasicFields()) {
            return;
        }

        this.showLoading('正在保存草稿...');
        
        const data = this.getFormData();
        data.status = 'draft';

        this.submitActivity(data, '草稿保存成功！');
    }

    // 发布活动
    publishActivity() {
        if (!this.validateForm()) {
            return;
        }

        this.showLoading('正在发布活动...');
        
        const data = this.getFormData();
        data.status = 'recruiting';

        this.submitActivity(data, '活动发布成功！');
    }

    // 提交活动数据
    submitActivity(data, successMessage) {
        fetch('api/create_activity.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(data)
        })
        .then(response => response.json())
        .then(result => {
            this.hideLoading();
            
            if (result.success) {
                this.showToast(successMessage);
                
                // 2秒后跳转
                setTimeout(() => {
                    if (data.status === 'recruiting') {
                        // 发布成功后跳转到活动详情页或我的组局页面
                        window.location.href = 'index.php';
                    } else {
                        // 草稿保存后留在当前页面
                        this.showToast('可以继续编辑或直接发布');
                    }
                }, 2000);
            } else {
                this.showToast(result.message || '操作失败，请重试');
            }
        })
        .catch(error => {
            this.hideLoading();
            console.error('Error:', error);
            this.showToast('网络错误，请检查网络连接后重试');
        });
    }

    // 验证基本字段（用于草稿保存）
    validateBasicFields() {
        const title = document.getElementById('title').value.trim();
        const description = document.getElementById('description').value.trim();
        
        if (!title) {
            this.showToast('请至少输入活动标题');
            return false;
        }
        
        if (!description) {
            this.showToast('请至少输入活动描述');
            return false;
        }
        
        return true;
    }

    // 返回上一页
    goBack() {
        const hasChanges = this.hasFormChanges();
        
        if (hasChanges) {
            if (confirm('您有未保存的更改，确定要离开吗？')) {
                history.back();
            }
        } else {
            history.back();
        }
    }

    // 检查表单是否有更改
    hasFormChanges() {
        const inputs = this.form.querySelectorAll('input, select, textarea');
        
        for (let input of inputs) {
            if (input.type === 'checkbox') {
                if (input.checked !== input.defaultChecked) {
                    return true;
                }
            } else {
                if (input.value !== input.defaultValue) {
                    return true;
                }
            }
        }
        
        return false;
    }

    // 显示加载动画
    showLoading(text = '正在处理...') {
        if (this.loadingOverlay) {
            const loadingText = this.loadingOverlay.querySelector('.loading-text');
            if (loadingText) {
                loadingText.textContent = text;
            }
            this.loadingOverlay.classList.add('active');
        }
    }

    // 隐藏加载动画
    hideLoading() {
        if (this.loadingOverlay) {
            this.loadingOverlay.classList.remove('active');
        }
    }

    // 显示Toast提示
    showToast(message, duration = 3000) {
        if (!this.toast) return;

        this.toast.textContent = message;
        this.toast.style.display = 'block';
        this.toast.style.opacity = '0';
        this.toast.style.transform = 'translateX(-50%) translateY(20px)';

        setTimeout(() => {
            this.toast.style.transition = 'all 0.3s ease';
            this.toast.style.opacity = '1';
            this.toast.style.transform = 'translateX(-50%) translateY(0)';
        }, 10);

        setTimeout(() => {
            this.toast.style.opacity = '0';
            this.toast.style.transform = 'translateX(-50%) translateY(20px)';
            
            setTimeout(() => {
                this.toast.style.display = 'none';
            }, 300);
        }, duration);
    }
}

// 全局函数
function saveDraft() {
    if (window.createActivityPage) {
        window.createActivityPage.saveDraft();
    }
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    window.createActivityPage = new CreateActivityPage();
});

// 添加字段验证样式
const style = document.createElement('style');
style.textContent = `
    .field-valid input,
    .field-valid select,
    .field-valid textarea {
        border-color: #06D6A0;
        box-shadow: 0 0 0 3px rgba(6, 214, 160, 0.1);
    }
    
    .field-invalid input,
    .field-invalid select,
    .field-invalid textarea {
        border-color: #FF6B6B;
        box-shadow: 0 0 0 3px rgba(255, 107, 107, 0.1);
    }
    
    .field-error {
        margin-top: 6px;
        color: #FF6B6B;
        font-size: 0.8rem;
        display: flex;
        align-items: center;
        gap: 4px;
    }
    
    .field-error::before {
        content: "⚠";
        font-size: 0.9rem;
    }
`;
document.head.appendChild(style);
