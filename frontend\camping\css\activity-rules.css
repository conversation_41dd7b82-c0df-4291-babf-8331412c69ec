/* 活动规则页面样式 */

/* CSS变量定义 */
:root {
    --primary-color: #6F7BF5;
    --primary-light: #A8B2F8;
    --primary-dark: #5A67E8;
    --secondary-color: #8B95F7;
    --accent-color: #FF6B9D;
    --success-color: #06D6A0;
    --warning-color: #FFD166;
    --error-color: #FF6B6B;
    --critical-color: #DC3545;
    --gradient-primary: linear-gradient(135deg, #6F7BF5, #8B95F7);
    --gradient-secondary: linear-gradient(135deg, #A8B2F8, #6F7BF5);
    --gradient-critical: linear-gradient(135deg, #FF6B6B, #DC3545);
    --text-primary: #333333;
    --text-secondary: #666666;
    --text-light: #999999;
    --bg-white: #FFFFFF;
    --bg-light: #F8F9FA;
    --bg-critical: #FFF5F5;
    --shadow-sm: 0 2px 8px rgba(0, 0, 0, 0.08);
    --shadow-md: 0 4px 16px rgba(0, 0, 0, 0.12);
    --shadow-lg: 0 8px 32px rgba(0, 0, 0, 0.16);
    --radius-sm: 8px;
    --radius-md: 12px;
    --radius-lg: 16px;
    --radius-xl: 24px;
    --transition-fast: all 0.2s ease;
    --transition-normal: all 0.3s ease;
}

/* 基础样式重置 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background: var(--bg-light);
    color: var(--text-primary);
    line-height: 1.6;
    overflow-x: hidden;
}

/* 页面头部 */
.rules-header {
    background: var(--bg-white);
    border-bottom: 1px solid #E5E7EB;
    position: sticky;
    top: 0;
    z-index: 100;
    box-shadow: var(--shadow-sm);
}

.header-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 16px 20px;
    max-width: 100%;
}

.back-btn {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: var(--bg-light);
    border: none;
    color: var(--text-primary);
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: var(--transition-normal);
}

.back-btn:hover {
    background: var(--primary-color);
    color: white;
    transform: scale(1.05);
}

.page-title {
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--text-primary);
    text-align: center;
    flex: 1;
}

.header-placeholder {
    width: 40px;
}

/* 主要内容区域 */
.rules-main {
    padding: 20px;
    padding-bottom: 100px;
    max-width: 800px;
    margin: 0 auto;
}

/* 重要提示区域 */
.important-notice {
    background: var(--bg-critical);
    border: 2px solid var(--error-color);
    border-radius: var(--radius-lg);
    padding: 20px;
    margin-bottom: 30px;
    box-shadow: var(--shadow-md);
}

.notice-header {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 16px;
}

.notice-header i {
    color: var(--error-color);
    font-size: 1.5rem;
}

.notice-header h2 {
    color: var(--error-color);
    font-size: 1.25rem;
    font-weight: 700;
}

.notice-content {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.notice-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px 16px;
    border-radius: var(--radius-md);
    font-weight: 600;
}

.notice-item.critical {
    background: var(--gradient-critical);
    color: white;
}

.notice-item.age-limit {
    background: var(--gradient-primary);
    color: white;
}

.notice-item i {
    font-size: 1.125rem;
}

/* 规则内容区域 */
.rules-content {
    display: flex;
    flex-direction: column;
    gap: 30px;
}

.rule-section {
    background: var(--bg-white);
    border-radius: var(--radius-lg);
    padding: 24px;
    box-shadow: var(--shadow-md);
    transition: var(--transition-normal);
}

.rule-section:hover {
    box-shadow: var(--shadow-lg);
}

.rule-section h3 {
    display: flex;
    align-items: center;
    gap: 12px;
    font-size: 1.125rem;
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: 20px;
    padding-bottom: 12px;
    border-bottom: 2px solid var(--bg-light);
}

.rule-section h3 i {
    font-size: 1.25rem;
}

.rule-items {
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.rule-item {
    display: flex;
    gap: 16px;
    padding: 16px;
    border-radius: var(--radius-md);
    background: var(--bg-light);
    transition: var(--transition-normal);
}

.rule-item:hover {
    background: rgba(111, 123, 245, 0.05);
    transform: translateX(4px);
}

.rule-item.critical-rule {
    background: var(--bg-critical);
    border-left: 4px solid var(--error-color);
}

.rule-number {
    background: var(--primary-color);
    color: white;
    width: 28px;
    height: 28px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.875rem;
    font-weight: 600;
    flex-shrink: 0;
}

.rule-item.critical-rule .rule-number {
    background: var(--error-color);
}

.rule-text {
    flex: 1;
    font-size: 0.9rem;
    line-height: 1.6;
}

.rule-text strong {
    color: var(--primary-color);
    font-weight: 600;
}

.rule-item.critical-rule .rule-text strong {
    color: var(--error-color);
}

/* 联系方式区域 */
.contact-section {
    background: var(--gradient-primary);
    border-radius: var(--radius-lg);
    padding: 24px;
    color: white;
    box-shadow: var(--shadow-md);
    margin-top: 30px;
}

.contact-header {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 20px;
}

.contact-header i {
    font-size: 1.5rem;
}

.contact-header h3 {
    font-size: 1.125rem;
    font-weight: 700;
}

.contact-items {
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.contact-item {
    display: flex;
    align-items: center;
    gap: 16px;
    padding: 16px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: var(--radius-md);
    backdrop-filter: blur(10px);
}

.contact-item i {
    font-size: 1.25rem;
    width: 24px;
    text-align: center;
}

.contact-info {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.contact-label {
    font-size: 0.875rem;
    opacity: 0.9;
}

.contact-value {
    font-size: 1rem;
    font-weight: 600;
}

/* 确认区域 */
.confirm-section {
    background: var(--bg-white);
    border-radius: var(--radius-lg);
    padding: 24px;
    text-align: center;
    box-shadow: var(--shadow-md);
    margin-top: 30px;
}

.confirm-text {
    margin-bottom: 20px;
}

.confirm-text p {
    font-size: 0.9rem;
    color: var(--text-secondary);
    line-height: 1.6;
}

.confirm-btn {
    background: var(--gradient-primary);
    color: white;
    border: none;
    padding: 16px 32px;
    border-radius: var(--radius-lg);
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition-normal);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 12px;
    margin: 0 auto;
    box-shadow: var(--shadow-md);
}

.confirm-btn:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.confirm-btn:active {
    transform: translateY(0);
}

/* Toast提示 */
.toast {
    position: fixed;
    bottom: 100px;
    left: 50%;
    transform: translateX(-50%);
    background: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 12px 20px;
    border-radius: var(--radius-lg);
    font-size: 0.875rem;
    z-index: 10000;
    display: none;
    backdrop-filter: blur(10px);
}

/* 响应式设计 */
@media (min-width: 768px) {
    .rules-main {
        padding: 30px 40px 100px;
    }
    
    .contact-items {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 20px;
    }
}

@media (max-width: 480px) {
    .rules-main {
        padding: 16px;
        padding-bottom: 100px;
    }
    
    .rule-section {
        padding: 20px;
    }
    
    .rule-item {
        padding: 12px;
    }
    
    .contact-section {
        padding: 20px;
    }
    
    .confirm-section {
        padding: 20px;
    }
    
    .confirm-btn {
        padding: 14px 24px;
        font-size: 0.9rem;
    }
}
