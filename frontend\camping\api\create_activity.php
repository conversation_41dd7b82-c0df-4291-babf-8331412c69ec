<?php
// 创建露营活动接口
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// 处理OPTIONS请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

// 引入session配置
require_once '../../../sql/session_config.php';
initLoginSession();

// 检查用户是否已登录
if (!isUserLoggedIn()) {
    echo json_encode([
        'success' => false,
        'message' => '请先登录后再发布活动'
    ]);
    exit;
}

// 检查请求方法
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode([
        'success' => false,
        'message' => '请求方法错误'
    ]);
    exit;
}

// 获取POST数据
$input = json_decode(file_get_contents('php://input'), true);
if (!$input) {
    echo json_encode([
        'success' => false,
        'message' => '数据格式错误'
    ]);
    exit;
}

// 验证必填字段
$required_fields = ['title', 'description', 'category', 'location', 'start_date', 'end_date', 'max_participants', 'price'];
foreach ($required_fields as $field) {
    if (!isset($input[$field]) || trim($input[$field]) === '') {
        echo json_encode([
            'success' => false,
            'message' => "缺少必填字段: {$field}"
        ]);
        exit;
    }
}

$user_id = $_SESSION['user_id'];

// 数据库连接配置
$db_config = [
    'host' => 'localhost',
    'username' => 'quwanplanet',
    'password' => 'nJmJm23FB4Xn6Fc3',
    'dbname' => 'quwanplanet',
    'port' => 3306,
    'charset' => 'utf8mb4'
];

try {
    $dsn = "mysql:host={$db_config['host']};dbname={$db_config['dbname']};charset={$db_config['charset']};port={$db_config['port']}";
    $pdo = new PDO($dsn, $db_config['username'], $db_config['password']);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

    // 检查表是否存在
    $stmt = $pdo->query("SHOW TABLES LIKE 'camping_activities'");
    if ($stmt->rowCount() == 0) {
        echo json_encode([
            'success' => false,
            'message' => '活动系统暂未开放，请联系管理员'
        ]);
        exit;
    }

    // 验证数据
    $validation_result = validateActivityData($input);
    if (!$validation_result['valid']) {
        echo json_encode([
            'success' => false,
            'message' => $validation_result['message']
        ]);
        exit;
    }

    // 开始事务
    $pdo->beginTransaction();

    // 插入活动数据
    $sql = "INSERT INTO camping_activities (
        title, description, organizer_id, location, start_date, end_date, 
        max_participants, price, category, features, image_url, status, created_at
    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW())";

    $stmt = $pdo->prepare($sql);
    $result = $stmt->execute([
        trim($input['title']),
        trim($input['description']),
        $user_id,
        trim($input['location']),
        $input['start_date'],
        $input['end_date'],
        intval($input['max_participants']),
        floatval($input['price']),
        $input['category'],
        isset($input['features']) ? trim($input['features']) : '',
        isset($input['image_url']) ? trim($input['image_url']) : null,
        isset($input['status']) ? $input['status'] : 'recruiting'
    ]);

    if (!$result) {
        $pdo->rollBack();
        echo json_encode([
            'success' => false,
            'message' => '活动创建失败，请重试'
        ]);
        exit;
    }

    $activity_id = $pdo->lastInsertId();

    // 如果是发布状态，自动让组局者参加活动
    if (isset($input['status']) && $input['status'] === 'recruiting') {
        $participant_sql = "INSERT INTO camping_participants (
            activity_id, user_id, join_time, status, payment_status, actual_price
        ) VALUES (?, ?, NOW(), 'joined', 'paid', 0)";
        
        $participant_stmt = $pdo->prepare($participant_sql);
        $participant_stmt->execute([$activity_id, $user_id]);

        // 更新当前参与人数
        $update_sql = "UPDATE camping_activities SET current_participants = 1 WHERE id = ?";
        $update_stmt = $pdo->prepare($update_sql);
        $update_stmt->execute([$activity_id]);
    }

    // 提交事务
    $pdo->commit();

    $message = isset($input['status']) && $input['status'] === 'draft' ? '草稿保存成功' : '活动发布成功';

    echo json_encode([
        'success' => true,
        'message' => $message,
        'activity_id' => $activity_id
    ]);

} catch (PDOException $e) {
    // 回滚事务
    if ($pdo->inTransaction()) {
        $pdo->rollBack();
    }
    
    error_log("创建活动错误: " . $e->getMessage());
    echo json_encode([
        'success' => false,
        'message' => '系统错误，请稍后重试'
    ]);
}

// 验证活动数据
function validateActivityData($data) {
    // 验证标题
    if (strlen(trim($data['title'])) < 5) {
        return ['valid' => false, 'message' => '活动标题至少需要5个字符'];
    }
    if (strlen(trim($data['title'])) > 100) {
        return ['valid' => false, 'message' => '活动标题不能超过100个字符'];
    }

    // 验证描述
    if (strlen(trim($data['description'])) < 20) {
        return ['valid' => false, 'message' => '活动描述至少需要20个字符'];
    }
    if (strlen(trim($data['description'])) > 1000) {
        return ['valid' => false, 'message' => '活动描述不能超过1000个字符'];
    }

    // 验证分类
    $valid_categories = ['mountain', 'lake', 'forest', 'beach'];
    if (!in_array($data['category'], $valid_categories)) {
        return ['valid' => false, 'message' => '活动类型无效'];
    }

    // 验证地点
    if (strlen(trim($data['location'])) < 3) {
        return ['valid' => false, 'message' => '活动地点至少需要3个字符'];
    }

    // 验证时间
    $start_time = strtotime($data['start_date']);
    $end_time = strtotime($data['end_date']);
    $now = time();

    if ($start_time <= $now) {
        return ['valid' => false, 'message' => '活动开始时间必须晚于当前时间'];
    }

    if ($end_time <= $start_time) {
        return ['valid' => false, 'message' => '活动结束时间必须晚于开始时间'];
    }

    if (($end_time - $start_time) < 2 * 3600) {
        return ['valid' => false, 'message' => '活动持续时间至少需要2小时'];
    }

    // 验证参与人数
    $max_participants = intval($data['max_participants']);
    if ($max_participants < 2 || $max_participants > 50) {
        return ['valid' => false, 'message' => '参与人数必须在2-50人之间'];
    }

    // 验证价格
    $price = floatval($data['price']);
    if ($price < 0 || $price > 9999.99) {
        return ['valid' => false, 'message' => '活动费用必须在0-9999.99元之间'];
    }

    // 验证图片URL（如果提供）
    if (isset($data['image_url']) && !empty(trim($data['image_url']))) {
        if (!filter_var($data['image_url'], FILTER_VALIDATE_URL)) {
            return ['valid' => false, 'message' => '图片链接格式无效'];
        }
    }

    return ['valid' => true, 'message' => '验证通过'];
}
?>
