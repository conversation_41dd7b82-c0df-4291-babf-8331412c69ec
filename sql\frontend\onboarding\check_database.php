<?php
// 数据库检查脚本
header('Content-Type: application/json; charset=utf-8');

// 数据库配置
$host = 'localhost';
$dbname = 'quwanplanet';
$db_username = 'quwanplanet';
$db_password = 'nJmJm23FB4Xn6Fc3';

try {
    $pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $db_username, $db_password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

    $result = [
        'success' => true,
        'database_info' => [],
        'table_structure' => [],
        'field_check' => [],
        'sample_data' => [],
        'recommendations' => []
    ];

    // 1. 检查数据库基本信息
    $stmt = $pdo->query("SELECT DATABASE() as current_db, VERSION() as mysql_version");
    $result['database_info'] = $stmt->fetch(PDO::FETCH_ASSOC);

    // 2. 检查users表结构
    $stmt = $pdo->query("DESCRIBE users");
    $result['table_structure'] = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // 3. 检查ID字段存在情况
    $stmt = $pdo->query("
        SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE, COLUMN_DEFAULT, EXTRA
        FROM INFORMATION_SCHEMA.COLUMNS
        WHERE TABLE_SCHEMA = 'quwanplanet'
        AND TABLE_NAME = 'users'
        AND COLUMN_NAME IN ('quwan_id', 'quwanplanet_id')
        ORDER BY COLUMN_NAME
    ");
    $result['field_check'] = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // 4. 获取样本数据
    $stmt = $pdo->query("SELECT id, phone, quwan_id, nickname FROM users LIMIT 5");
    $result['sample_data'] = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // 5. 分析并给出建议
    $has_quwan_id = false;
    $has_quwanplanet_id = false;

    foreach ($result['field_check'] as $field) {
        if ($field['COLUMN_NAME'] === 'quwan_id') {
            $has_quwan_id = true;
        }
        if ($field['COLUMN_NAME'] === 'quwanplanet_id') {
            $has_quwanplanet_id = true;
        }
    }

    if ($has_quwan_id && $has_quwanplanet_id) {
        $result['recommendations'][] = '⚠️ 数据库中同时存在 quwan_id 和 quwanplanet_id 字段，需要合并数据';
        $result['recommendations'][] = '建议执行：UPDATE users SET quwan_id = quwanplanet_id WHERE quwan_id IS NULL AND quwanplanet_id IS NOT NULL;';
        $result['recommendations'][] = '然后删除：ALTER TABLE users DROP COLUMN quwanplanet_id;';
    } elseif ($has_quwanplanet_id && !$has_quwan_id) {
        $result['recommendations'][] = '🔧 数据库中只有 quwanplanet_id 字段，需要重命名为 quwan_id';
        $result['recommendations'][] = '建议执行：ALTER TABLE users CHANGE COLUMN quwanplanet_id quwan_id VARCHAR(9) NULL DEFAULT NULL;';
    } elseif ($has_quwan_id && !$has_quwanplanet_id) {
        $result['recommendations'][] = '✅ 数据库中只有 quwan_id 字段，这是正确的';
        $result['recommendations'][] = '建议执行：ALTER TABLE users MODIFY COLUMN quwan_id VARCHAR(9) NULL DEFAULT NULL;';
    } else {
        $result['recommendations'][] = '❌ 数据库中没有找到ID字段，需要创建';
        $result['recommendations'][] = '建议执行：ALTER TABLE users ADD COLUMN quwan_id VARCHAR(9) NULL DEFAULT NULL;';
    }

    // 6. 检查索引
    $stmt = $pdo->query("SHOW INDEX FROM users WHERE Column_name IN ('quwan_id', 'quwanplanet_id')");
    $indexes = $stmt->fetchAll(PDO::FETCH_ASSOC);
    $result['indexes'] = $indexes;

    if (empty($indexes)) {
        $result['recommendations'][] = '🔍 需要为ID字段添加唯一索引';
        $result['recommendations'][] = '建议执行：ALTER TABLE users ADD UNIQUE INDEX idx_quwan_id (quwan_id);';
    }

    echo json_encode($result, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);

} catch(PDOException $e) {
    echo json_encode([
        'success' => false,
        'error' => '数据库连接失败: ' . $e->getMessage(),
        'error_code' => $e->getCode()
    ], JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
}
?>
