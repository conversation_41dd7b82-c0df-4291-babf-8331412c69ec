<?php
/**
 * 用户信息编辑API
 * 支持管理员修改用户的所有基本信息
 */

session_start();
header('Content-Type: application/json; charset=utf-8');

// 检查管理员权限
if (!isset($_SESSION['admin_id'])) {
    echo json_encode(['success' => false, 'message' => '未授权访问']);
    exit;
}

// 数据库连接配置
$db_config = [
    'host' => 'localhost',
    'username' => 'quwanplanet',
    'password' => 'nJmJm23FB4Xn6Fc3',
    'dbname' => 'quwanplanet',
    'port' => 3306,
    'charset' => 'utf8mb4'
];

try {
    $pdo = new PDO(
        "mysql:host={$db_config['host']};port={$db_config['port']};dbname={$db_config['dbname']};charset={$db_config['charset']}",
        $db_config['username'],
        $db_config['password'],
        [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
            PDO::ATTR_EMULATE_PREPARES => false
        ]
    );
} catch (PDOException $e) {
    echo json_encode(['success' => false, 'message' => '数据库连接失败']);
    exit;
}

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode(['success' => false, 'message' => '请求方法错误']);
    exit;
}

// 获取POST数据
$user_id = intval($_POST['user_id'] ?? 0);
$action = $_POST['action'] ?? '';

if (!$user_id || $action !== 'edit_user') {
    echo json_encode(['success' => false, 'message' => '参数错误']);
    exit;
}

// 获取用户当前信息
$stmt = $pdo->prepare("SELECT * FROM users WHERE id = ?");
$stmt->execute([$user_id]);
$user = $stmt->fetch();

if (!$user) {
    echo json_encode(['success' => false, 'message' => '用户不存在']);
    exit;
}

// 准备更新的字段
$update_fields = [];
$update_values = [];
$changes = []; // 记录变更内容

// 处理用户昵称
if (isset($_POST['username']) && $_POST['username'] !== $user['username']) {
    $new_username = trim($_POST['username']);
    if (empty($new_username)) {
        echo json_encode(['success' => false, 'message' => '用户昵称不能为空']);
        exit;
    }

    // 检查昵称是否已被使用
    $stmt = $pdo->prepare("SELECT id FROM users WHERE username = ? AND id != ?");
    $stmt->execute([$new_username, $user_id]);
    if ($stmt->fetch()) {
        echo json_encode(['success' => false, 'message' => '该昵称已被使用']);
        exit;
    }

    $update_fields[] = 'username = ?';
    $update_values[] = $new_username;
    $changes[] = "昵称: {$user['username']} → {$new_username}";
}

// 处理趣玩ID（管理员可以任意修改，不受限制）
if (isset($_POST['quwan_id']) && $_POST['quwan_id'] !== $user['quwan_id']) {
    $new_quwan_id = trim($_POST['quwan_id']);
    if (empty($new_quwan_id)) {
        echo json_encode(['success' => false, 'message' => '趣玩ID不能为空']);
        exit;
    }

    // 验证趣玩ID格式（7-9位数字）
    if (!preg_match('/^\d{7,9}$/', $new_quwan_id)) {
        echo json_encode(['success' => false, 'message' => '趣玩ID必须是7-9位数字']);
        exit;
    }

    // 检查趣玩ID是否已被使用
    $stmt = $pdo->prepare("SELECT id FROM users WHERE quwan_id = ? AND id != ?");
    $stmt->execute([$new_quwan_id, $user_id]);
    if ($stmt->fetch()) {
        echo json_encode(['success' => false, 'message' => '该趣玩ID已被使用']);
        exit;
    }

    $update_fields[] = 'quwan_id = ?';
    $update_values[] = $new_quwan_id;
    $changes[] = "趣玩ID: {$user['quwan_id']} → {$new_quwan_id}";
}

// 处理邮箱
if (isset($_POST['email']) && $_POST['email'] !== $user['email']) {
    $new_email = trim($_POST['email']);
    if (!empty($new_email)) {
        // 验证邮箱格式
        if (!filter_var($new_email, FILTER_VALIDATE_EMAIL)) {
            echo json_encode(['success' => false, 'message' => '邮箱格式不正确']);
            exit;
        }

        // 检查邮箱是否已被使用
        $stmt = $pdo->prepare("SELECT id FROM users WHERE email = ? AND id != ?");
        $stmt->execute([$new_email, $user_id]);
        if ($stmt->fetch()) {
            echo json_encode(['success' => false, 'message' => '该邮箱已被使用']);
            exit;
        }
    }

    $update_fields[] = 'email = ?';
    $update_values[] = $new_email;
    $changes[] = "邮箱: {$user['email']} → {$new_email}";
}

// 处理手机号
if (isset($_POST['phone']) && $_POST['phone'] !== $user['phone']) {
    $new_phone = trim($_POST['phone']);
    if (!empty($new_phone)) {
        // 验证手机号格式
        if (!preg_match('/^1[3-9]\d{9}$/', $new_phone)) {
            echo json_encode(['success' => false, 'message' => '手机号格式不正确']);
            exit;
        }

        // 检查手机号是否已被使用
        $stmt = $pdo->prepare("SELECT id FROM users WHERE phone = ? AND id != ?");
        $stmt->execute([$new_phone, $user_id]);
        if ($stmt->fetch()) {
            echo json_encode(['success' => false, 'message' => '该手机号已被使用']);
            exit;
        }
    }

    $update_fields[] = 'phone = ?';
    $update_values[] = $new_phone;
    $changes[] = "手机号: {$user['phone']} → {$new_phone}";
}

// 处理地区
if (isset($_POST['region']) && $_POST['region'] !== $user['region']) {
    $new_region = trim($_POST['region']);
    $update_fields[] = 'region = ?';
    $update_values[] = $new_region;
    $changes[] = "地区: {$user['region']} → {$new_region}";
}

// 处理个人简介
if (isset($_POST['bio']) && $_POST['bio'] !== $user['bio']) {
    $new_bio = trim($_POST['bio']);
    if (mb_strlen($new_bio) > 200) {
        echo json_encode(['success' => false, 'message' => '个人简介不能超过200字']);
        exit;
    }

    $update_fields[] = 'bio = ?';
    $update_values[] = $new_bio;
    $changes[] = "个人简介: " . (empty($user['bio']) ? '(空)' : mb_substr($user['bio'], 0, 20) . '...') . " → " . (empty($new_bio) ? '(空)' : mb_substr($new_bio, 0, 20) . '...');
}

// 如果没有任何变更
if (empty($update_fields)) {
    echo json_encode(['success' => false, 'message' => '没有检测到任何变更']);
    exit;
}

// 执行更新
try {
    $pdo->beginTransaction();

    // 更新用户信息
    $update_values[] = $user_id; // 添加WHERE条件的参数
    $sql = "UPDATE users SET " . implode(', ', $update_fields) . ", updated_at = NOW() WHERE id = ?";
    $stmt = $pdo->prepare($sql);
    $stmt->execute($update_values);

    // 记录管理员操作日志（可选，如果表不存在则跳过）
    try {
        $admin_id = $_SESSION['admin_id'];
        $admin_name = $_SESSION['admin_name'] ?? '未知管理员';
        $change_log = implode('; ', $changes);

        $stmt = $pdo->prepare("
            INSERT INTO admin_logs (admin_id, admin_name, action, target_type, target_id, details, ip_address, user_agent, created_at)
            VALUES (?, ?, 'edit_user', 'user', ?, ?, ?, ?, NOW())
        ");
        $stmt->execute([
            $admin_id,
            $admin_name,
            $user_id,
            "修改用户信息: " . $change_log,
            $_SERVER['REMOTE_ADDR'],
            $_SERVER['HTTP_USER_AGENT']
        ]);
    } catch (PDOException $e) {
        // 如果admin_logs表不存在，忽略日志记录错误
        error_log("管理员日志记录失败: " . $e->getMessage());
    }

    $pdo->commit();

    // 获取更新后的用户信息
    $stmt = $pdo->prepare("SELECT * FROM users WHERE id = ?");
    $stmt->execute([$user_id]);
    $updated_user = $stmt->fetch();

    echo json_encode([
        'success' => true,
        'message' => '用户信息修改成功',
        'changes' => $changes,
        'user' => [
            'id' => $updated_user['id'],
            'username' => $updated_user['username'],
            'quwan_id' => $updated_user['quwan_id'],
            'email' => $updated_user['email'],
            'phone' => $updated_user['phone'],
            'region' => $updated_user['region'],
            'bio' => $updated_user['bio'],
            'updated_at' => $updated_user['updated_at']
        ]
    ]);

} catch (PDOException $e) {
    $pdo->rollBack();
    error_log("用户信息修改失败: " . $e->getMessage());
    echo json_encode([
        'success' => false,
        'message' => '修改失败：' . $e->getMessage(),
        'debug' => [
            'sql' => $sql ?? '',
            'values' => $update_values ?? [],
            'fields' => $update_fields ?? []
        ]
    ]);
}
?>
