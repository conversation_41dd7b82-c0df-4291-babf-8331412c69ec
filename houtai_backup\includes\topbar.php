<?php
/**
 * 顶部导航栏组件
 * 趣玩星球管理后台
 */

$admin_name = $_SESSION['admin_name'] ?? '管理员';
$admin_role = $_SESSION['admin_role'] ?? '系统管理员';
$admin_employee_id = $_SESSION['admin_employee_id'] ?? '';

// 获取当前时间
$current_time = date('Y年m月d日 H:i');
$current_weekday = ['日', '一', '二', '三', '四', '五', '六'][date('w')];
?>

<header class="topbar">
    <div class="topbar-left">
        <button class="mobile-menu-toggle" onclick="toggleMobileMenu()">
            <i class="fas fa-bars"></i>
        </button>
        <div class="page-breadcrumb">
            <span class="breadcrumb-item">趣玩星球管理后台</span>
            <i class="fas fa-chevron-right"></i>
            <span class="breadcrumb-current" id="currentPageTitle">首页</span>
        </div>
    </div>

    <div class="topbar-right">
        <div class="user-profile-dropdown">
            <div class="user-profile-trigger" onclick="toggleUserDropdown()">
                <div class="user-avatar">
                    <?php echo mb_substr($admin_name, 0, 1, 'UTF-8'); ?>
                </div>
                <div class="user-info">
                    <div class="user-name"><?php echo htmlspecialchars($admin_name); ?></div>
                    <div class="user-role">工号：<?php echo htmlspecialchars($admin_employee_id); ?></div>
                </div>
                <i class="fas fa-chevron-down dropdown-arrow"></i>
            </div>

            <div class="user-dropdown" id="userDropdown">
                <div class="dropdown-header">
                    <div class="dropdown-user-info">
                        <div class="dropdown-avatar">
                            <?php echo mb_substr($admin_name, 0, 1, 'UTF-8'); ?>
                        </div>
                        <div class="dropdown-details">
                            <div class="dropdown-name"><?php echo htmlspecialchars($admin_name); ?></div>
                            <div class="dropdown-id">工号：<?php echo htmlspecialchars($admin_employee_id); ?></div>
                        </div>
                    </div>
                </div>

                <div class="dropdown-menu">
                    <a href="#" class="dropdown-item" onclick="openProfile()">
                        <i class="fas fa-user"></i>
                        <span>个人资料</span>
                    </a>
                    <a href="#" class="dropdown-item" onclick="openSettings()">
                        <i class="fas fa-cog"></i>
                        <span>系统设置</span>
                    </a>
                    <a href="/houtai_backup/home.php" class="dropdown-item">
                        <i class="fas fa-home"></i>
                        <span>返回首页</span>
                    </a>
                    <div class="dropdown-divider"></div>
                    <a href="/houtai_backup/logout.php" class="dropdown-item danger" onclick="return confirm('确定要退出登录吗？')">
                        <i class="fas fa-sign-out-alt"></i>
                        <span>退出登录</span>
                    </a>
                </div>
            </div>
        </div>
    </div>
</header>

<style>
/* 顶部导航栏样式 */
.topbar {
    height: 60px;
    background: white;
    border-bottom: 1px solid var(--gray-200);
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 24px;
    position: sticky;
    top: 0;
    z-index: 100;
    box-shadow: var(--shadow-sm);
}

.topbar-left {
    display: flex;
    align-items: center;
    gap: 16px;
}

.mobile-menu-toggle {
    display: none;
    background: none;
    border: none;
    font-size: 18px;
    color: var(--gray-600);
    cursor: pointer;
    padding: 8px;
    border-radius: var(--border-radius);
    transition: var(--transition);
}

.mobile-menu-toggle:hover {
    background: var(--gray-100);
    color: var(--primary-color);
}

.page-breadcrumb {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 0.875rem;
    color: var(--gray-600);
}

.breadcrumb-current {
    color: var(--primary-color);
    font-weight: 600;
}

.topbar-right {
    display: flex;
    align-items: center;
    gap: 20px;
}



.user-profile-dropdown {
    position: relative;
}

.user-profile-trigger {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 8px 12px;
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: var(--transition);
    border: 1px solid transparent;
}

.user-profile-trigger:hover {
    background: var(--gray-50);
    border-color: var(--gray-200);
}

.user-avatar {
    width: 36px;
    height: 36px;
    background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
    border-radius: var(--border-radius);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: 600;
    font-size: 14px;
}

.user-info {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
}

.user-name {
    font-weight: 600;
    color: var(--gray-800);
    font-size: 14px;
    line-height: 1.2;
}

.user-role {
    font-size: 12px;
    color: var(--gray-500);
    line-height: 1.2;
}

.dropdown-arrow {
    font-size: 12px;
    color: var(--gray-400);
    transition: var(--transition);
}

.user-profile-trigger.active .dropdown-arrow {
    transform: rotate(180deg);
}

/* 用户下拉菜单 */
.user-dropdown {
    position: absolute;
    top: 100%;
    right: 0;
    background: white;
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-xl);
    min-width: 280px;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: var(--transition);
    z-index: 1000;
    border: 1px solid var(--gray-200);
    margin-top: 8px;
}

.user-dropdown.show {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.dropdown-header {
    padding: 20px;
    border-bottom: 1px solid var(--gray-200);
    background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
    border-radius: var(--border-radius-lg) var(--border-radius-lg) 0 0;
}

.dropdown-user-info {
    display: flex;
    align-items: center;
    gap: 12px;
}

.dropdown-avatar {
    width: 48px;
    height: 48px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: var(--border-radius);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: 700;
    font-size: 18px;
    border: 2px solid rgba(255, 255, 255, 0.3);
}

.dropdown-details {
    flex: 1;
}

.dropdown-name {
    font-weight: 700;
    color: white;
    font-size: 16px;
    margin-bottom: 4px;
}

.dropdown-id {
    font-size: 13px;
    color: rgba(255, 255, 255, 0.8);
}

.dropdown-menu {
    padding: 8px;
}

.dropdown-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px 16px;
    border-radius: var(--border-radius);
    text-decoration: none;
    color: var(--gray-700);
    transition: var(--transition);
    font-size: 14px;
}

.dropdown-item:hover {
    background: var(--gray-50);
    text-decoration: none;
    color: var(--gray-800);
}

.dropdown-item.danger {
    color: var(--error-color);
}

.dropdown-item.danger:hover {
    background: rgba(239, 68, 68, 0.1);
    color: var(--error-color);
}

.dropdown-divider {
    height: 1px;
    background: var(--gray-200);
    margin: 8px 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .mobile-menu-toggle {
        display: block;
    }

    .page-breadcrumb {
        display: none;
    }

    .user-info {
        display: none;
    }

    .user-dropdown {
        min-width: 240px;
    }
}
</style>

<script>
// 顶部导航栏交互脚本
function toggleUserDropdown() {
    const dropdown = document.getElementById('userDropdown');
    const trigger = document.querySelector('.user-profile-trigger');

    dropdown.classList.toggle('show');
    trigger.classList.toggle('active');
}

// 点击外部关闭下拉菜单
document.addEventListener('click', function(e) {
    const dropdown = document.getElementById('userDropdown');
    const trigger = document.querySelector('.user-profile-trigger');

    if (!e.target.closest('.user-profile-dropdown')) {
        dropdown.classList.remove('show');
        trigger.classList.remove('active');
    }
});

function openProfile() {
    alert('个人资料功能开发中...');
    document.getElementById('userDropdown').classList.remove('show');
    document.querySelector('.user-profile-trigger').classList.remove('active');
}

function openSettings() {
    alert('系统设置功能开发中...');
    document.getElementById('userDropdown').classList.remove('show');
    document.querySelector('.user-profile-trigger').classList.remove('active');
}

// 更新页面标题
function updatePageTitle(title) {
    const titleElement = document.getElementById('currentPageTitle');
    if (titleElement) {
        titleElement.textContent = title;
    }
}


</script>
