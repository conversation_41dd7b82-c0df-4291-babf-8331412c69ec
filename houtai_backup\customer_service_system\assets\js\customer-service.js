/**
 * 趣玩星球客服管理系统 JavaScript
 * 现代化交互和功能实现
 */

// 全局变量
let isFullscreen = false;
let notificationCount = 0;
let activeWebSocket = null;

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    initializeSystem();
    setupEventListeners();
    startRealTimeUpdates();
});

/**
 * 系统初始化
 */
function initializeSystem() {
    console.log('客服管理系统初始化中...');
    
    // 检查浏览器兼容性
    checkBrowserCompatibility();
    
    // 初始化主题
    initializeTheme();
    
    // 初始化通知系统
    initializeNotifications();
    
    // 初始化快捷键
    initializeKeyboardShortcuts();
    
    console.log('客服管理系统初始化完成');
}

/**
 * 设置事件监听器
 */
function setupEventListeners() {
    // 侧边栏切换
    const sidebarToggle = document.querySelector('.cs-sidebar-toggle');
    if (sidebarToggle) {
        sidebarToggle.addEventListener('click', toggleSidebar);
    }
    
    // 全屏切换
    const fullscreenBtn = document.querySelector('[onclick="toggleFullscreen()"]');
    if (fullscreenBtn) {
        fullscreenBtn.addEventListener('click', toggleFullscreen);
    }
    
    // 页面刷新
    const refreshBtn = document.querySelector('[onclick="refreshPage()"]');
    if (refreshBtn) {
        refreshBtn.addEventListener('click', refreshPage);
    }
    
    // 通知菜单
    const notificationBtn = document.querySelector('.cs-notification-btn');
    if (notificationBtn) {
        notificationBtn.addEventListener('click', toggleNotifications);
    }
    
    // 用户菜单
    const userMenuBtn = document.querySelector('.cs-user-menu-btn');
    if (userMenuBtn) {
        userMenuBtn.addEventListener('click', toggleUserMenu);
    }
    
    // 点击外部关闭下拉菜单
    document.addEventListener('click', handleOutsideClick);
    
    // 窗口大小变化
    window.addEventListener('resize', handleWindowResize);
}

/**
 * 侧边栏切换
 */
function toggleSidebar() {
    const container = document.querySelector('.cs-container');
    if (container) {
        container.classList.toggle('sidebar-collapsed');
        
        // 保存状态到本地存储
        const isCollapsed = container.classList.contains('sidebar-collapsed');
        localStorage.setItem('cs-sidebar-collapsed', isCollapsed);
    }
}

/**
 * 全屏模式切换
 */
function toggleFullscreen() {
    if (!document.fullscreenElement) {
        document.documentElement.requestFullscreen().then(() => {
            isFullscreen = true;
            updateFullscreenIcon(true);
        }).catch(err => {
            console.error('无法进入全屏模式:', err);
            showToast('无法进入全屏模式', 'error');
        });
    } else {
        document.exitFullscreen().then(() => {
            isFullscreen = false;
            updateFullscreenIcon(false);
        }).catch(err => {
            console.error('无法退出全屏模式:', err);
        });
    }
}

/**
 * 更新全屏图标
 */
function updateFullscreenIcon(isFullscreen) {
    const icon = document.querySelector('[onclick="toggleFullscreen()"] i');
    if (icon) {
        icon.className = isFullscreen ? 'fas fa-compress' : 'fas fa-expand';
    }
}

/**
 * 页面刷新
 */
function refreshPage() {
    // 显示加载动画
    showLoadingOverlay();
    
    // 延迟刷新以显示动画
    setTimeout(() => {
        location.reload();
    }, 500);
}

/**
 * 通知菜单切换
 */
function toggleNotifications() {
    const dropdown = document.getElementById('notificationDropdown');
    const userDropdown = document.getElementById('userDropdown');
    
    if (dropdown) {
        dropdown.classList.toggle('show');
        
        // 关闭用户菜单
        if (userDropdown) {
            userDropdown.classList.remove('show');
        }
        
        // 标记通知为已读
        if (dropdown.classList.contains('show')) {
            markNotificationsAsRead();
        }
    }
}

/**
 * 用户菜单切换
 */
function toggleUserMenu() {
    const dropdown = document.getElementById('userDropdown');
    const notificationDropdown = document.getElementById('notificationDropdown');
    
    if (dropdown) {
        dropdown.classList.toggle('show');
        
        // 关闭通知菜单
        if (notificationDropdown) {
            notificationDropdown.classList.remove('show');
        }
    }
}

/**
 * 处理外部点击
 */
function handleOutsideClick(e) {
    // 关闭通知下拉菜单
    if (!e.target.closest('.cs-notifications')) {
        const notificationDropdown = document.getElementById('notificationDropdown');
        if (notificationDropdown) {
            notificationDropdown.classList.remove('show');
        }
    }
    
    // 关闭用户下拉菜单
    if (!e.target.closest('.cs-user-menu')) {
        const userDropdown = document.getElementById('userDropdown');
        if (userDropdown) {
            userDropdown.classList.remove('show');
        }
    }
}

/**
 * 窗口大小变化处理
 */
function handleWindowResize() {
    const container = document.querySelector('.cs-container');
    if (window.innerWidth <= 768) {
        // 移动端自动收起侧边栏
        if (container && !container.classList.contains('sidebar-collapsed')) {
            container.classList.add('sidebar-collapsed');
        }
    }
}

/**
 * 检查浏览器兼容性
 */
function checkBrowserCompatibility() {
    // 检查必要的API支持
    const requiredFeatures = [
        'fetch',
        'Promise',
        'localStorage',
        'sessionStorage'
    ];
    
    const unsupportedFeatures = requiredFeatures.filter(feature => 
        !(feature in window)
    );
    
    if (unsupportedFeatures.length > 0) {
        console.warn('浏览器不支持以下功能:', unsupportedFeatures);
        showToast('您的浏览器版本过低，建议升级到最新版本', 'warning');
    }
}

/**
 * 初始化主题
 */
function initializeTheme() {
    // 从本地存储恢复主题设置
    const savedTheme = localStorage.getItem('cs-theme');
    if (savedTheme) {
        document.body.setAttribute('data-theme', savedTheme);
    }
    
    // 从本地存储恢复侧边栏状态
    const sidebarCollapsed = localStorage.getItem('cs-sidebar-collapsed');
    if (sidebarCollapsed === 'true') {
        const container = document.querySelector('.cs-container');
        if (container) {
            container.classList.add('sidebar-collapsed');
        }
    }
}

/**
 * 初始化通知系统
 */
function initializeNotifications() {
    // 请求通知权限
    if ('Notification' in window && Notification.permission === 'default') {
        Notification.requestPermission();
    }
    
    // 加载未读通知数量
    loadNotificationCount();
}

/**
 * 初始化快捷键
 */
function initializeKeyboardShortcuts() {
    document.addEventListener('keydown', function(e) {
        // Ctrl/Cmd + B: 切换侧边栏
        if ((e.ctrlKey || e.metaKey) && e.key === 'b') {
            e.preventDefault();
            toggleSidebar();
        }
        
        // F11: 全屏模式
        if (e.key === 'F11') {
            e.preventDefault();
            toggleFullscreen();
        }
        
        // Ctrl/Cmd + R: 刷新页面
        if ((e.ctrlKey || e.metaKey) && e.key === 'r') {
            e.preventDefault();
            refreshPage();
        }
    });
}

/**
 * 开始实时更新
 */
function startRealTimeUpdates() {
    // 每30秒更新一次状态指示器
    setInterval(updateStatusIndicators, 30000);
    
    // 每60秒检查一次新通知
    setInterval(checkNewNotifications, 60000);
    
    // 初始化WebSocket连接（如果支持）
    initializeWebSocket();
}

/**
 * 更新状态指示器
 */
function updateStatusIndicators() {
    fetch('api/get_status.php')
        .then(response => response.json())
        .then(data => {
            // 更新在线客服数量
            const onlineElement = document.querySelector('.cs-status-item:nth-child(1) .cs-status-value');
            if (onlineElement && data.online_cs_count !== undefined) {
                onlineElement.textContent = data.online_cs_count;
            }
            
            // 更新待处理会话数量
            const waitingElement = document.querySelector('.cs-status-item:nth-child(2) .cs-status-value');
            if (waitingElement && data.waiting_sessions !== undefined) {
                waitingElement.textContent = data.waiting_sessions;
            }
            
            // 更新活跃会话数量
            const activeElement = document.querySelector('.cs-status-item:nth-child(3) .cs-status-value');
            if (activeElement && data.active_sessions !== undefined) {
                activeElement.textContent = data.active_sessions;
            }
        })
        .catch(error => {
            console.error('更新状态指示器失败:', error);
        });
}

/**
 * 检查新通知
 */
function checkNewNotifications() {
    fetch('api/get_notifications.php')
        .then(response => response.json())
        .then(data => {
            if (data.count > notificationCount) {
                // 有新通知
                const newCount = data.count - notificationCount;
                showDesktopNotification(`您有 ${newCount} 条新通知`);
                updateNotificationBadge(data.count);
                notificationCount = data.count;
            }
        })
        .catch(error => {
            console.error('检查通知失败:', error);
        });
}

/**
 * 加载通知数量
 */
function loadNotificationCount() {
    fetch('api/get_notification_count.php')
        .then(response => response.json())
        .then(data => {
            notificationCount = data.count || 0;
            updateNotificationBadge(notificationCount);
        })
        .catch(error => {
            console.error('加载通知数量失败:', error);
        });
}

/**
 * 更新通知徽章
 */
function updateNotificationBadge(count) {
    const badge = document.querySelector('.cs-notification-badge');
    if (badge) {
        if (count > 0) {
            badge.textContent = count > 99 ? '99+' : count;
            badge.style.display = 'block';
        } else {
            badge.style.display = 'none';
        }
    }
}

/**
 * 标记通知为已读
 */
function markNotificationsAsRead() {
    fetch('api/mark_notifications_read.php', {
        method: 'POST'
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            updateNotificationBadge(0);
            notificationCount = 0;
        }
    })
    .catch(error => {
        console.error('标记通知已读失败:', error);
    });
}

/**
 * 显示桌面通知
 */
function showDesktopNotification(message, title = '趣玩星球客服系统') {
    if ('Notification' in window && Notification.permission === 'granted') {
        new Notification(title, {
            body: message,
            icon: '/favicon.ico',
            tag: 'cs-notification'
        });
    }
}

/**
 * 显示Toast消息
 */
function showToast(message, type = 'info', duration = 3000) {
    // 创建toast元素
    const toast = document.createElement('div');
    toast.className = `cs-toast cs-toast-${type}`;
    toast.innerHTML = `
        <div class="cs-toast-content">
            <i class="fas fa-${getToastIcon(type)}"></i>
            <span>${message}</span>
        </div>
        <button class="cs-toast-close" onclick="this.parentElement.remove()">
            <i class="fas fa-times"></i>
        </button>
    `;
    
    // 添加到页面
    document.body.appendChild(toast);
    
    // 显示动画
    setTimeout(() => toast.classList.add('show'), 100);
    
    // 自动移除
    setTimeout(() => {
        toast.classList.remove('show');
        setTimeout(() => toast.remove(), 300);
    }, duration);
}

/**
 * 获取Toast图标
 */
function getToastIcon(type) {
    const icons = {
        'success': 'check-circle',
        'error': 'exclamation-circle',
        'warning': 'exclamation-triangle',
        'info': 'info-circle'
    };
    return icons[type] || 'info-circle';
}

/**
 * 显示加载覆盖层
 */
function showLoadingOverlay() {
    const overlay = document.createElement('div');
    overlay.className = 'cs-loading-overlay';
    overlay.innerHTML = `
        <div class="cs-loading-spinner">
            <i class="fas fa-spinner fa-spin"></i>
            <p>加载中...</p>
        </div>
    `;
    document.body.appendChild(overlay);
}

/**
 * 隐藏加载覆盖层
 */
function hideLoadingOverlay() {
    const overlay = document.querySelector('.cs-loading-overlay');
    if (overlay) {
        overlay.remove();
    }
}

/**
 * 初始化WebSocket连接
 */
function initializeWebSocket() {
    // 这里可以实现WebSocket连接用于实时通信
    // 暂时留空，后续可以扩展
}

/**
 * 工具函数：格式化时间
 */
function formatTime(timestamp) {
    const date = new Date(timestamp);
    const now = new Date();
    const diff = now - date;
    
    if (diff < 60000) { // 1分钟内
        return '刚刚';
    } else if (diff < 3600000) { // 1小时内
        return Math.floor(diff / 60000) + '分钟前';
    } else if (diff < 86400000) { // 1天内
        return Math.floor(diff / 3600000) + '小时前';
    } else {
        return date.toLocaleDateString();
    }
}

/**
 * 工具函数：防抖
 */
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

/**
 * 工具函数：节流
 */
function throttle(func, limit) {
    let inThrottle;
    return function() {
        const args = arguments;
        const context = this;
        if (!inThrottle) {
            func.apply(context, args);
            inThrottle = true;
            setTimeout(() => inThrottle = false, limit);
        }
    };
}

// 导出函数供全局使用
window.CSSystem = {
    toggleSidebar,
    toggleFullscreen,
    refreshPage,
    toggleNotifications,
    toggleUserMenu,
    showToast,
    showLoadingOverlay,
    hideLoadingOverlay,
    formatTime,
    debounce,
    throttle
};
