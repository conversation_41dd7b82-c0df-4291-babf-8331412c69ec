<?php
// 获取客服系统状态API
header('Content-Type: application/json; charset=UTF-8');
ini_set('default_charset', 'UTF-8');

session_start();

// 检查登录状态
if (!isset($_SESSION['cs_logged_in']) || !$_SESSION['cs_logged_in']) {
    http_response_code(401);
    echo json_encode(['error' => '未登录']);
    exit;
}

// 引用数据库配置文件
require_once '../../db_config.php';

try {
    $pdo = getDbConnection();
    
    // 获取在线客服数量（30分钟内有登录记录）
    $stmt = $pdo->query("
        SELECT COUNT(*) as count 
        FROM customer_service_users 
        WHERE status = 'active' AND last_login >= DATE_SUB(NOW(), INTERVAL 30 MINUTE)
    ");
    $online_cs_count = $stmt->fetch()['count'] ?? 0;
    
    // 获取待处理会话数量
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM customer_service_sessions WHERE status = 'waiting'");
    $waiting_sessions = $stmt->fetch()['count'] ?? 0;
    
    // 获取活跃会话数量
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM customer_service_sessions WHERE status = 'active'");
    $active_sessions = $stmt->fetch()['count'] ?? 0;
    
    // 获取今日会话数量
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM customer_service_sessions WHERE DATE(created_at) = CURDATE()");
    $today_sessions = $stmt->fetch()['count'] ?? 0;
    
    // 获取当前用户的活跃会话数量
    $stmt = $pdo->prepare("SELECT COUNT(*) as count FROM customer_service_sessions WHERE customer_service_id = ? AND status = 'active'");
    $stmt->execute([$_SESSION['cs_user_id']]);
    $my_active_sessions = $stmt->fetch()['count'] ?? 0;
    
    // 返回状态数据
    echo json_encode([
        'success' => true,
        'data' => [
            'online_cs_count' => $online_cs_count,
            'waiting_sessions' => $waiting_sessions,
            'active_sessions' => $active_sessions,
            'today_sessions' => $today_sessions,
            'my_active_sessions' => $my_active_sessions,
            'timestamp' => time()
        ]
    ]);
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => '获取状态失败',
        'message' => $e->getMessage()
    ]);
}
?>
