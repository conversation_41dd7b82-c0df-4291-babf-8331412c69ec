<?php
// 获取用户消息API - 供前台轮询使用
header('Content-Type: application/json; charset=UTF-8');
ini_set('default_charset', 'UTF-8');

// 允许跨域请求（如果前台和后台在不同域名）
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// 处理OPTIONS预检请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

$userId = $_GET['user_id'] ?? '';
$lastMessageId = intval($_GET['last_id'] ?? 0);
$sessionId = $_GET['session_id'] ?? '';

if (!$userId) {
    echo json_encode(['error' => '用户ID不能为空']);
    exit;
}

require_once '../../db_config.php';

try {
    $pdo = getDbConnection();
    
    // 获取用户的新消息
    if ($sessionId) {
        // 如果指定了会话ID，只获取该会话的消息
        $stmt = $pdo->prepare("
            SELECT m.*, s.session_id, s.status as session_status
            FROM customer_service_messages m
            JOIN customer_service_sessions s ON m.session_id = s.session_id
            WHERE s.user_id = ? AND s.session_id = ? AND m.id > ?
            ORDER BY m.created_at ASC
        ");
        $stmt->execute([$userId, $sessionId, $lastMessageId]);
    } else {
        // 获取用户所有会话的新消息
        $stmt = $pdo->prepare("
            SELECT m.*, s.session_id, s.status as session_status
            FROM customer_service_messages m
            JOIN customer_service_sessions s ON m.session_id = s.session_id
            WHERE s.user_id = ? AND m.id > ?
            ORDER BY m.created_at ASC
        ");
        $stmt->execute([$userId, $lastMessageId]);
    }
    
    $messages = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // 获取用户的未读通知
    $stmt = $pdo->prepare("
        SELECT * FROM realtime_notifications 
        WHERE user_id = ? AND status = 'unread'
        AND type IN ('customer_service_message', 'session_accepted')
        ORDER BY created_at DESC
        LIMIT 10
    ");
    $stmt->execute([$userId]);
    $notifications = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // 获取用户的活跃会话信息
    $stmt = $pdo->prepare("
        SELECT session_id, status, customer_service_id, message_count, updated_at
        FROM customer_service_sessions 
        WHERE user_id = ? AND status IN ('waiting', 'active')
        ORDER BY updated_at DESC
    ");
    $stmt->execute([$userId]);
    $sessions = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // 计算最新的消息ID
    $latestMessageId = $lastMessageId;
    if (!empty($messages)) {
        $latestMessageId = max(array_column($messages, 'id'));
    }
    
    echo json_encode([
        'success' => true,
        'messages' => $messages,
        'notifications' => $notifications,
        'sessions' => $sessions,
        'latest_message_id' => $latestMessageId,
        'timestamp' => time(),
        'debug' => [
            'user_id' => $userId,
            'last_message_id' => $lastMessageId,
            'session_id' => $sessionId,
            'message_count' => count($messages),
            'notification_count' => count($notifications)
        ]
    ]);
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'error' => '获取消息失败',
        'message' => $e->getMessage()
    ]);
}
?>
