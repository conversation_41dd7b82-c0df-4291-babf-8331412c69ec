# 优惠券管理系统安装指南

## 🚨 重要说明
如果您访问统计分析页面时遇到数据库表不存在的错误，请按照以下步骤创建必要的数据库表。

## 📋 安装步骤

### 第一步：检查数据库连接
1. 访问测试页面：`/houtai_backup/coupon_management/test_db.php`
2. 检查数据库连接是否正常
3. 查看哪些表缺失

### 第二步：创建数据库表
1. 登录宝塔面板或phpMyAdmin
2. 选择数据库：`quwanplanet`
3. 点击"SQL"选项卡
4. 复制 `create_coupon_tables.sql` 文件的全部内容
5. 粘贴到SQL执行器中
6. 点击"执行"按钮

### 第三步：验证安装
1. 再次访问测试页面：`/houtai_backup/coupon_management/test_db.php`
2. 确认以下表已创建：
   - ✅ `camping_coupons` - 优惠券表
   - ✅ `user_camping_coupons` - 用户优惠券记录表
   - ✅ `camping_activities` - 露营活动表（可选）

### 第四步：访问功能页面
1. 优惠券管理：`/houtai_backup/coupon_management/camping_coupons.php`
2. 统计分析：`/houtai_backup/coupon_management/statistics.php`
3. 用户详情页面的优惠券板块

## 📊 创建的表结构

### 1. camping_coupons（优惠券表）
- `id` - 优惠券ID
- `title` - 优惠券标题
- `description` - 优惠券描述
- `type` - 优惠券类型（join_discount/organize_discount/newbie_discount）
- `discount_amount` - 优惠金额
- `min_amount` - 最低消费金额
- `total_quantity` - 总发放数量
- `claimed_quantity` - 已领取数量
- `used_quantity` - 已使用数量
- `valid_from` - 有效期开始时间
- `valid_until` - 有效期结束时间
- `status` - 优惠券状态

### 2. user_camping_coupons（用户优惠券记录表）
- `id` - 记录ID
- `user_id` - 用户ID
- `coupon_id` - 优惠券ID
- `claimed_at` - 领取时间
- `used_at` - 使用时间
- `used_activity_id` - 使用的活动ID
- `status` - 优惠券状态（claimed/used/expired）

## 🎯 初始数据

安装脚本会自动插入以下测试数据：

### 优惠券数据
1. **新人专享券** - 20元优惠，满80元可用
2. **放肆趣玩券** - 30元优惠，满100元可用
3. **组局优惠券** - 50元优惠，满200元可用
4. **春季特惠券** - 25元优惠，满120元可用
5. **周末狂欢券** - 35元优惠，满150元可用

### 用户优惠券记录
- 为用户ID 1-5 创建测试优惠券记录
- 包含已领取、已使用、已过期等不同状态
- 用于测试统计功能

## 🔧 自动化功能

### 触发器
安装脚本会创建以下触发器来自动维护统计数据：
- `update_coupon_claimed_count_insert` - 领取时更新统计
- `update_coupon_used_count_update` - 使用时更新统计
- `update_coupon_count_delete` - 删除时更新统计

### 统计数据自动更新
- 优惠券的 `claimed_quantity` 和 `used_quantity` 会自动更新
- 无需手动维护统计数据

## 🚀 功能特性

### 管理后台功能
- ✅ 优惠券CRUD操作
- ✅ 优惠券状态管理
- ✅ 统计分析页面
- ✅ 用户详情页面优惠券展示
- ✅ 实时数据图表
- ✅ 响应式设计

### 统计分析功能
- ✅ 总体统计（领取率、使用率、流失率）
- ✅ 按类型统计分析
- ✅ 使用趋势图表
- ✅ 热门优惠券排行
- ✅ 用户领取统计
- ✅ 交互式图表展示

## 🔍 故障排除

### 常见问题

#### 1. 数据库连接失败
- 检查 `db_config.php` 中的数据库配置
- 确认数据库服务正常运行
- 检查用户权限

#### 2. 表不存在错误
- 执行 `create_coupon_tables.sql` 创建表
- 检查SQL执行是否有错误
- 确认数据库名称正确

#### 3. 权限不足
- 确认数据库用户有CREATE、INSERT、UPDATE权限
- 检查触发器创建权限

#### 4. 数据显示异常
- 检查初始数据是否插入成功
- 验证用户表是否存在
- 确认外键关联正确

### 测试步骤
1. 访问 `/houtai_backup/coupon_management/test_db.php` 检查数据库状态
2. 访问 `/houtai_backup/coupon_management/camping_coupons.php` 测试优惠券管理
3. 访问 `/houtai_backup/coupon_management/statistics.php` 测试统计分析
4. 访问用户详情页面测试优惠券显示

## 📞 技术支持

如果遇到问题，请检查：
1. 数据库连接配置
2. 表结构是否完整
3. 初始数据是否正确
4. 浏览器控制台错误信息

## 🎉 安装完成

安装完成后，您将拥有：
- 完整的优惠券管理系统
- 详细的统计分析功能
- 美观的用户界面
- 实时的数据更新
- 响应式设计支持

享受您的优惠券管理系统吧！🚀
