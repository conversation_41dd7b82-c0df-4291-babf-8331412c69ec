import { defineStore } from 'pinia'
import { ref, computed } from 'vue'

export const useUserStore = defineStore('user', () => {
  // 状态
  const userInfo = ref(null)
  const token = ref('')
  const isLoggedIn = ref(false)
  const loginTime = ref(null)

  // 计算属性
  const isTokenValid = computed(() => {
    if (!token.value || !loginTime.value) return false
    
    // 检查token是否在3天有效期内
    const now = new Date().getTime()
    const loginTimestamp = new Date(loginTime.value).getTime()
    const threeDaysInMs = 3 * 24 * 60 * 60 * 1000 // 3天
    
    return (now - loginTimestamp) < threeDaysInMs
  })

  const userAvatar = computed(() => {
    return userInfo.value?.avatar || '/static/images/default-avatar.png'
  })

  const userNickname = computed(() => {
    return userInfo.value?.nickname || '未设置昵称'
  })

  // 方法
  const setUserInfo = (info) => {
    userInfo.value = info
    isLoggedIn.value = true
    
    // 保存到本地存储
    uni.setStorageSync('userInfo', info)
  }

  const setToken = (newToken) => {
    token.value = newToken
    loginTime.value = new Date().toISOString()
    
    // 保存到本地存储
    uni.setStorageSync('token', newToken)
    uni.setStorageSync('loginTime', loginTime.value)
  }

  const login = async (phone, verificationCode) => {
    try {
      // 调用云函数进行登录
      const result = await uniCloud.callFunction({
        name: 'user-login',
        data: {
          phone,
          verificationCode
        }
      })

      if (result.result.success) {
        const { userInfo: info, token: newToken } = result.result.data
        
        setUserInfo(info)
        setToken(newToken)
        
        return { success: true, data: info }
      } else {
        return { success: false, message: result.result.message }
      }
    } catch (error) {
      console.error('登录失败:', error)
      return { success: false, message: '登录失败，请重试' }
    }
  }

  const logout = () => {
    userInfo.value = null
    token.value = ''
    isLoggedIn.value = false
    loginTime.value = null
    
    // 清除本地存储
    uni.removeStorageSync('userInfo')
    uni.removeStorageSync('token')
    uni.removeStorageSync('loginTime')
    
    // 跳转到登录页
    uni.reLaunch({
      url: '/pages/login/login'
    })
  }

  const updateUserInfo = async (updateData) => {
    try {
      const result = await uniCloud.callFunction({
        name: 'user-update',
        data: {
          userId: userInfo.value.id,
          updateData
        }
      })

      if (result.result.success) {
        // 更新本地用户信息
        const updatedInfo = { ...userInfo.value, ...updateData }
        setUserInfo(updatedInfo)
        
        return { success: true }
      } else {
        return { success: false, message: result.result.message }
      }
    } catch (error) {
      console.error('更新用户信息失败:', error)
      return { success: false, message: '更新失败，请重试' }
    }
  }

  const checkLoginStatus = async () => {
    try {
      // 从本地存储恢复登录状态
      const savedUserInfo = uni.getStorageSync('userInfo')
      const savedToken = uni.getStorageSync('token')
      const savedLoginTime = uni.getStorageSync('loginTime')

      if (savedUserInfo && savedToken && savedLoginTime) {
        userInfo.value = savedUserInfo
        token.value = savedToken
        loginTime.value = savedLoginTime

        // 检查token是否有效
        if (isTokenValid.value) {
          isLoggedIn.value = true
          
          // 可选：验证token是否仍然有效
          const result = await uniCloud.callFunction({
            name: 'user-verify-token',
            data: { token: savedToken }
          })

          if (!result.result.success) {
            // token无效，清除登录状态
            logout()
          }
        } else {
          // token过期，清除登录状态
          logout()
        }
      }
    } catch (error) {
      console.error('检查登录状态失败:', error)
    }
  }

  const sendVerificationCode = async (phone) => {
    try {
      const result = await uniCloud.callFunction({
        name: 'send-verification-code',
        data: { phone }
      })

      return result.result
    } catch (error) {
      console.error('发送验证码失败:', error)
      return { success: false, message: '发送失败，请重试' }
    }
  }

  const validateNickname = (nickname) => {
    // 昵称验证规则：1-5个字符，只允许中文、英文、数字
    const regex = /^[\u4e00-\u9fa5a-zA-Z0-9]{1,5}$/
    
    if (!nickname) {
      return { valid: false, message: '昵称不能为空' }
    }
    
    if (!regex.test(nickname)) {
      return { valid: false, message: '昵称只能包含中文、英文、数字，长度1-5个字符' }
    }
    
    // 检查敏感词（这里可以扩展敏感词库）
    const sensitiveWords = ['管理员', '客服', '系统', '官方']
    const hasSensitiveWord = sensitiveWords.some(word => nickname.includes(word))
    
    if (hasSensitiveWord) {
      return { valid: false, message: '昵称包含敏感词，请重新输入' }
    }
    
    return { valid: true }
  }

  const validatePhone = (phone) => {
    // 中国大陆手机号验证
    const regex = /^1[3-9]\d{9}$/
    
    if (!phone) {
      return { valid: false, message: '手机号不能为空' }
    }
    
    if (!regex.test(phone)) {
      return { valid: false, message: '请输入正确的手机号' }
    }
    
    return { valid: true }
  }

  return {
    // 状态
    userInfo,
    token,
    isLoggedIn,
    loginTime,
    
    // 计算属性
    isTokenValid,
    userAvatar,
    userNickname,
    
    // 方法
    setUserInfo,
    setToken,
    login,
    logout,
    updateUserInfo,
    checkLoginStatus,
    sendVerificationCode,
    validateNickname,
    validatePhone
  }
})
