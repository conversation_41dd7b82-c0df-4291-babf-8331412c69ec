<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试头像上传</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 600px;
            margin: 50px auto;
            padding: 20px;
        }
        .upload-area {
            border: 2px dashed #40E0D0;
            border-radius: 10px;
            padding: 40px;
            text-align: center;
            margin: 20px 0;
        }
        .upload-btn {
            background: #40E0D0;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
        }
        .result {
            margin-top: 20px;
            padding: 10px;
            border-radius: 5px;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
    </style>
</head>
<body>
    <h1>头像上传测试</h1>
    
    <div class="upload-area">
        <p>选择头像文件进行测试</p>
        <input type="file" id="avatar" accept="image/*">
        <br><br>
        <button class="upload-btn" onclick="testUpload()">测试上传</button>
    </div>
    
    <div id="result"></div>

    <script>
        async function testUpload() {
            const fileInput = document.getElementById('avatar');
            const resultDiv = document.getElementById('result');
            
            if (!fileInput.files[0]) {
                resultDiv.innerHTML = '<div class="error">请选择文件</div>';
                return;
            }
            
            const formData = new FormData();
            formData.append('avatar', fileInput.files[0]);
            
            try {
                resultDiv.innerHTML = '<div>上传中...</div>';
                
                const response = await fetch('upload_avatar.php', {
                    method: 'POST',
                    body: formData
                });
                
                const data = await response.json();
                
                if (data.success) {
                    resultDiv.innerHTML = `
                        <div class="success">
                            <h3>上传成功！</h3>
                            <p>头像URL: ${data.avatar_url}</p>
                            <p>审核结果: ${data.moderation.approved ? '通过' : '未通过'}</p>
                            <p>审核原因: ${data.moderation.reason}</p>
                            <img src="${data.avatar_url}" style="max-width: 200px; border-radius: 10px;">
                        </div>
                    `;
                } else {
                    resultDiv.innerHTML = `<div class="error">上传失败: ${data.error}</div>`;
                }
            } catch (error) {
                resultDiv.innerHTML = `<div class="error">请求失败: ${error.message}</div>`;
            }
        }
    </script>
</body>
</html>
