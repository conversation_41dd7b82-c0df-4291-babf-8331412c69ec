/* 管理员登录页面样式 */
:root {
    --primary-color: #40E0D0;
    --secondary-color: #36C5B0;
    --danger-color: #dc3545;
    --dark-color: #343a40;
    --text-muted: #6c757d;
    --border-color: #dee2e6;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    overflow: hidden;
}

/* 背景装饰 */
.background-decoration {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 1;
}

.circle {
    position: absolute;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.1);
    animation: float 6s ease-in-out infinite;
}

.circle-1 {
    width: 200px;
    height: 200px;
    top: 10%;
    left: 10%;
    animation-delay: 0s;
}

.circle-2 {
    width: 150px;
    height: 150px;
    top: 60%;
    right: 10%;
    animation-delay: 2s;
}

.circle-3 {
    width: 100px;
    height: 100px;
    bottom: 20%;
    left: 20%;
    animation-delay: 4s;
}

@keyframes float {
    0%, 100% {
        transform: translateY(0px);
    }
    50% {
        transform: translateY(-20px);
    }
}

/* 登录容器 */
.login-container {
    position: relative;
    z-index: 2;
    width: 100%;
    max-width: 400px;
    padding: 20px;
}

.login-box {
    background: white;
    border-radius: 20px;
    padding: 40px;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
    backdrop-filter: blur(10px);
    animation: slideUp 0.6s ease-out;
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 登录头部 */
.login-header {
    text-align: center;
    margin-bottom: 30px;
}

.logo {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 20px;
    box-shadow: 0 10px 30px rgba(64, 224, 208, 0.3);
}

.logo i {
    font-size: 36px;
    color: white;
}

.login-header h1 {
    font-size: 28px;
    color: var(--dark-color);
    margin-bottom: 8px;
    font-weight: 700;
}

.login-header p {
    color: var(--text-muted);
    font-size: 16px;
}

/* 表单样式 */
.login-form {
    margin-bottom: 20px;
}

.error-message {
    background: #f8d7da;
    color: #721c24;
    padding: 12px 16px;
    border-radius: 8px;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    font-size: 14px;
    border: 1px solid #f5c6cb;
}

.error-message i {
    margin-right: 8px;
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    color: var(--dark-color);
    font-weight: 500;
    font-size: 14px;
}

.input-group {
    position: relative;
    display: flex;
    align-items: center;
}

.input-group i {
    position: absolute;
    left: 16px;
    color: var(--text-muted);
    font-size: 16px;
    z-index: 2;
}

.input-group input {
    width: 100%;
    padding: 14px 16px 14px 48px;
    border: 2px solid var(--border-color);
    border-radius: 12px;
    font-size: 16px;
    transition: all 0.3s ease;
    background: #f8f9fa;
}

.input-group input:focus {
    outline: none;
    border-color: var(--primary-color);
    background: white;
    box-shadow: 0 0 0 3px rgba(64, 224, 208, 0.1);
}

.toggle-password {
    position: absolute;
    right: 16px;
    background: none;
    border: none;
    color: var(--text-muted);
    cursor: pointer;
    font-size: 16px;
    z-index: 2;
    padding: 4px;
    border-radius: 4px;
    transition: color 0.3s ease;
}

.toggle-password:hover {
    color: var(--primary-color);
}

.login-btn {
    width: 100%;
    padding: 16px;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    color: white;
    border: none;
    border-radius: 12px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    box-shadow: 0 4px 15px rgba(64, 224, 208, 0.3);
}

.login-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 25px rgba(64, 224, 208, 0.4);
}

.login-btn:active {
    transform: translateY(0);
}

/* 验证码组 */
.captcha-group {
    display: flex;
    gap: 10px;
    align-items: flex-end;
}

.captcha-group .input-group {
    flex: 1;
}

.captcha-image {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 5px;
}

.captcha-image img {
    width: 120px;
    height: 40px;
    border: 2px solid var(--border-color);
    border-radius: 8px;
    cursor: pointer;
    transition: border-color 0.3s ease;
}

.captcha-image img:hover {
    border-color: var(--primary-color);
}

.refresh-captcha {
    background: var(--primary-color);
    color: white;
    border: none;
    border-radius: 6px;
    padding: 6px 8px;
    cursor: pointer;
    font-size: 12px;
    transition: all 0.3s ease;
}

.refresh-captcha:hover {
    background: var(--secondary-color);
    transform: rotate(180deg);
}

.refresh-captcha:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none;
}

/* 锁定状态样式 */
.login-btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    background: var(--text-muted);
    transform: none;
    box-shadow: none;
}

.input-group input:disabled {
    background: #f0f0f0;
    color: var(--text-muted);
    cursor: not-allowed;
}

.toggle-password:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

/* 错误状态增强 */
.error-message {
    animation: shake 0.5s ease-in-out;
}

@keyframes shake {
    0%, 100% { transform: translateX(0); }
    25% { transform: translateX(-5px); }
    75% { transform: translateX(5px); }
}

/* 登录底部 */
.login-footer {
    text-align: center;
    margin-top: 20px;
}

.login-footer p {
    color: var(--text-muted);
    font-size: 12px;
}

/* 响应式设计 */
@media (max-width: 480px) {
    .login-container {
        padding: 15px;
    }

    .login-box {
        padding: 30px 25px;
    }

    .logo {
        width: 60px;
        height: 60px;
    }

    .logo i {
        font-size: 28px;
    }

    .login-header h1 {
        font-size: 24px;
    }

    .login-header p {
        font-size: 14px;
    }

    .input-group input {
        padding: 12px 16px 12px 44px;
        font-size: 14px;
    }

    .login-btn {
        padding: 14px;
        font-size: 14px;
    }
}

/* 加载状态 */
.login-btn:disabled {
    opacity: 0.7;
    cursor: not-allowed;
    transform: none;
}

.login-btn.loading {
    position: relative;
    color: transparent;
}

.login-btn.loading::after {
    content: '';
    position: absolute;
    width: 20px;
    height: 20px;
    border: 2px solid transparent;
    border-top: 2px solid white;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% {
        transform: rotate(0deg);
    }
    100% {
        transform: rotate(360deg);
    }
}
