<?php
// 领取露营优惠券接口
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// 处理OPTIONS请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

// 引入session配置
require_once '../../../sql/session_config.php';
initLoginSession();

// 检查用户是否已登录
if (!isUserLoggedIn()) {
    echo json_encode([
        'success' => false,
        'message' => '请先登录后再领取优惠券'
    ]);
    exit;
}

// 获取POST数据
$input = json_decode(file_get_contents('php://input'), true);
if (!$input || !isset($input['coupon_id'])) {
    echo json_encode([
        'success' => false,
        'message' => '参数错误'
    ]);
    exit;
}

$coupon_id = intval($input['coupon_id']);
$user_id = $_SESSION['user_id'];

// 数据库连接配置
$db_config = [
    'host' => 'localhost',
    'username' => 'quwanplanet',
    'password' => 'nJmJm23FB4Xn6Fc3',
    'dbname' => 'quwanplanet',
    'port' => 3306,
    'charset' => 'utf8mb4'
];

try {
    $dsn = "mysql:host={$db_config['host']};dbname={$db_config['dbname']};charset={$db_config['charset']};port={$db_config['port']}";
    $pdo = new PDO($dsn, $db_config['username'], $db_config['password']);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

    // 检查表是否存在
    $stmt = $pdo->query("SHOW TABLES LIKE 'camping_coupons'");
    if ($stmt->rowCount() == 0) {
        echo json_encode([
            'success' => false,
            'message' => '优惠券系统暂未开放'
        ]);
        exit;
    }

    // 开始事务
    $pdo->beginTransaction();

    // 检查优惠券是否存在且有效
    $coupon_sql = "SELECT id, title, total_quantity, claimed_quantity, valid_until 
                   FROM camping_coupons 
                   WHERE id = ? AND status = 'active' 
                   AND NOW() BETWEEN valid_from AND valid_until";
    $coupon_stmt = $pdo->prepare($coupon_sql);
    $coupon_stmt->execute([$coupon_id]);
    $coupon = $coupon_stmt->fetch(PDO::FETCH_ASSOC);

    if (!$coupon) {
        $pdo->rollBack();
        echo json_encode([
            'success' => false,
            'message' => '优惠券不存在或已过期'
        ]);
        exit;
    }

    // 检查是否还有库存
    if ($coupon['claimed_quantity'] >= $coupon['total_quantity']) {
        $pdo->rollBack();
        echo json_encode([
            'success' => false,
            'message' => '优惠券已被领完'
        ]);
        exit;
    }

    // 检查用户是否已经领取过
    $check_sql = "SELECT id FROM user_camping_coupons WHERE user_id = ? AND coupon_id = ?";
    $check_stmt = $pdo->prepare($check_sql);
    $check_stmt->execute([$user_id, $coupon_id]);
    
    if ($check_stmt->fetch()) {
        $pdo->rollBack();
        echo json_encode([
            'success' => false,
            'message' => '您已经领取过这张优惠券了'
        ]);
        exit;
    }

    // 插入用户优惠券记录
    $insert_sql = "INSERT INTO user_camping_coupons (user_id, coupon_id, claimed_at, status) 
                   VALUES (?, ?, NOW(), 'claimed')";
    $insert_stmt = $pdo->prepare($insert_sql);
    $insert_stmt->execute([$user_id, $coupon_id]);

    // 更新优惠券已领取数量
    $update_sql = "UPDATE camping_coupons SET claimed_quantity = claimed_quantity + 1 WHERE id = ?";
    $update_stmt = $pdo->prepare($update_sql);
    $update_stmt->execute([$coupon_id]);

    // 提交事务
    $pdo->commit();

    echo json_encode([
        'success' => true,
        'message' => '优惠券领取成功！已存入您的券包'
    ]);

} catch (PDOException $e) {
    // 回滚事务
    if ($pdo->inTransaction()) {
        $pdo->rollBack();
    }
    
    error_log("领取优惠券错误: " . $e->getMessage());
    echo json_encode([
        'success' => false,
        'message' => '系统错误，请稍后重试'
    ]);
}
?>
