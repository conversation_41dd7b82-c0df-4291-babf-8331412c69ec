/* 发布页面样式 */
.content-container {
    padding: 60px 15px 20px;
}

/* 发布按钮 */
.publish-button {
    background-color: #1E90FF;
    color: white;
    border: none;
    border-radius: 5px;
    padding: 6px 12px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
}

.publish-button:hover {
    background-color: #187bcd;
}

.publish-button:disabled,
.publish-button.disabled {
    background-color: #ccc;
    cursor: not-allowed;
}

/* 错误消息 */
.error-message {
    color: #ff3b30;
    background-color: #ffeeee;
    padding: 10px;
    border-radius: 8px;
    margin-top: 10px;
    font-size: 14px;
    border-left: 3px solid #ff3b30;
}

/* 表单样式 */
.publish-form {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.form-group {
    margin-bottom: 15px;
}

/* 标题输入框 */
#title {
    width: 100%;
    padding: 12px 15px;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    font-size: 16px;
    color: #333;
    outline: none;
    transition: border-color 0.3s;
}

#title:focus {
    border-color: #1E90FF;
}

/* 分类选择器 */
.category-selector {
    display: flex;
    gap: 10px;
}

.category-selector select {
    flex: 1;
    padding: 12px 15px;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    font-size: 15px;
    color: #333;
    background-color: white;
    outline: none;
    transition: border-color 0.3s;
    appearance: none;
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='12' height='12' viewBox='0 0 24 24' fill='none' stroke='%23999' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");
    background-repeat: no-repeat;
    background-position: right 15px center;
    background-size: 12px;
}

.category-selector select:focus {
    border-color: #1E90FF;
}

.category-selector select:disabled {
    background-color: #f5f5f5;
    color: #999;
}

/* 富文本编辑器 */
#editor-container {
    margin-bottom: 15px;
    border-radius: 8px;
    overflow: hidden;
}

.ql-toolbar.ql-snow {
    border: 1px solid #e0e0e0;
    border-top-left-radius: 8px;
    border-top-right-radius: 8px;
    background-color: #f9f9f9;
}

.ql-container.ql-snow {
    border: 1px solid #e0e0e0;
    border-top: none;
    border-bottom-left-radius: 8px;
    border-bottom-right-radius: 8px;
    min-height: 200px;
}

/* 媒体上传区域 */
.media-upload-section {
    margin-bottom: 15px;
}

.upload-buttons {
    display: flex;
    gap: 10px;
    margin-bottom: 15px;
    overflow-x: auto;
    padding-bottom: 5px;
}

.upload-button {
    flex-shrink: 0;
    background-color: #f0f0f0;
    border: none;
    border-radius: 20px;
    padding: 8px 15px;
    font-size: 14px;
    color: #666;
    display: flex;
    align-items: center;
    gap: 5px;
    cursor: pointer;
    transition: background-color 0.3s;
}

.upload-button i {
    font-size: 16px;
}

.upload-button:hover {
    background-color: #e0e0e0;
}

/* 表情选择器容器 */
.emoji-picker-container {
    display: none;
    margin-bottom: 15px;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    overflow: hidden;
}

/* 媒体预览区域 */
.media-preview {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    margin-top: 15px;
}

.media-item {
    position: relative;
    width: calc(33.333% - 7px);
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.media-item img {
    width: 100%;
    height: 100px;
    object-fit: cover;
}

.media-item video {
    width: 100%;
    height: 100px;
    object-fit: cover;
    background-color: #000;
}

.media-item audio {
    width: 100%;
    height: 40px;
    margin-top: 30px;
}

.media-item .audio-container {
    width: 100%;
    height: 100px;
    background-color: #f5f5f5;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
}

.media-item .audio-icon {
    font-size: 24px;
    color: #1E90FF;
    margin-bottom: 5px;
}

.media-item .audio-name {
    font-size: 12px;
    color: #666;
    text-align: center;
    padding: 0 5px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    width: 100%;
}

.media-item .remove-media {
    position: absolute;
    top: 5px;
    right: 5px;
    width: 24px;
    height: 24px;
    background-color: rgba(0, 0, 0, 0.5);
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    font-size: 12px;
}

/* 封面上传 */
.cover-upload {
    margin-top: 20px;
}

.cover-upload label {
    display: block;
    margin-bottom: 10px;
    font-size: 15px;
    color: #666;
}

.cover-preview {
    width: 100%;
    height: 180px;
    border: 1px dashed #ccc;
    border-radius: 8px;
    overflow: hidden;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    position: relative;
}

.upload-placeholder {
    display: flex;
    flex-direction: column;
    align-items: center;
    color: #999;
}

.upload-placeholder i {
    font-size: 24px;
    margin-bottom: 5px;
}

#cover-image-preview {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

/* 加载中动画 */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(255, 255, 255, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
}

.loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid rgba(30, 144, 255, 0.3);
    border-radius: 50%;
    border-top-color: #1E90FF;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}
