<?php
session_start();

// 检查登录状态
if (!isset($_SESSION['admin_id'])) {
    header('Location: login.php');
    exit;
}

require_once 'db_config.php';
require_once 'admin_layout.php';

// 获取筛选参数
$status_filter = $_GET['status'] ?? 'all';
$search = trim($_GET['search'] ?? '');
$page = max(1, intval($_GET['page'] ?? 1));
$per_page = 20;
$offset = ($page - 1) * $per_page;

$users = [];
$total = 0;
$total_pages = 0;
$show_results = false;

// 只有在有搜索条件时才查询用户数据
if (!empty($search)) {
    try {
        $pdo = getDbConnection();
        $show_results = true;

        // 构建查询条件
        $where_conditions = [];
        $params = [];

        // 搜索条件
        $where_conditions[] = "(u.username LIKE ? OR u.phone LIKE ? OR u.email LIKE ? OR u.quwanplanet_id LIKE ? OR u.id = ?)";
        $search_param = "%$search%";
        $params = [$search_param, $search_param, $search_param, $search_param, $search];

        // 状态筛选
        if ($status_filter !== 'all') {
            if ($status_filter === 'verified') {
                $where_conditions[] = "u.is_verified = 1";
            } elseif ($status_filter === 'unverified') {
                $where_conditions[] = "(u.is_verified = 0 OR u.is_verified IS NULL)";
            }
        }

        $where_clause = 'WHERE ' . implode(' AND ', $where_conditions);

        // 获取总数
        $count_sql = "SELECT COUNT(*) as total FROM users u $where_clause";
        $stmt = $pdo->prepare($count_sql);
        $stmt->execute($params);
        $total = $stmt->fetch(PDO::FETCH_ASSOC)['total'];

        // 获取用户列表
        $sql = "
            SELECT
                u.*,
                rv.verification_status,
                rv.real_name,
                rv.verified_at
            FROM users u
            LEFT JOIN realname_verification rv ON u.id = rv.user_id
            $where_clause
            ORDER BY u.created_at DESC
            LIMIT $per_page OFFSET $offset
        ";

        $stmt = $pdo->prepare($sql);
        $stmt->execute($params);
        $users = $stmt->fetchAll(PDO::FETCH_ASSOC);

        // 计算分页
        $total_pages = ceil($total / $per_page);

    } catch (PDOException $e) {
        $error_message = '数据库错误：' . $e->getMessage();
    }
}

// 状态标签函数
function getVerificationBadge($user) {
    if ($user['verification_status'] === 'approved') {
        return '<span class="badge success"><i class="fas fa-check-circle"></i> 已认证</span>';
    } elseif ($user['verification_status'] === 'pending') {
        return '<span class="badge warning"><i class="fas fa-clock"></i> 审核中</span>';
    } elseif ($user['verification_status'] === 'rejected') {
        return '<span class="badge danger"><i class="fas fa-times-circle"></i> 已拒绝</span>';
    } else {
        return '<span class="badge secondary"><i class="fas fa-user"></i> 未认证</span>';
    }
}

// 构建页面内容
$content = '
<div class="user-management-container">
    <!-- 搜索区域 -->
    <div class="search-section">
        <div class="search-header">
            <div class="search-title">
                <h2><i class="fas fa-search"></i> 用户查询系统</h2>
                <p>为保护用户隐私，请输入具体的搜索条件查询用户信息</p>
            </div>
        </div>

        <div class="search-form-container">
            <form method="GET" class="advanced-search-form">
                <div class="search-row">
                    <div class="search-field">
                        <label for="search">搜索条件</label>
                        <div class="input-with-icon">
                            <i class="fas fa-search"></i>
                            <input type="text"
                                   id="search"
                                   name="search"
                                   value="' . htmlspecialchars($search) . '"
                                   placeholder="请输入用户名、手机号、邮箱、趣玩ID或用户ID"
                                   required>
                        </div>
                        <small class="field-hint">支持模糊搜索，至少输入2个字符</small>
                    </div>

                    <div class="search-field">
                        <label for="status">认证状态</label>
                        <select name="status" id="status">
                            <option value="all" ' . ($status_filter === 'all' ? 'selected' : '') . '>全部状态</option>
                            <option value="verified" ' . ($status_filter === 'verified' ? 'selected' : '') . '>已认证</option>
                            <option value="unverified" ' . ($status_filter === 'unverified' ? 'selected' : '') . '>未认证</option>
                        </select>
                    </div>

                    <div class="search-actions">
                        <button type="submit" class="btn-search">
                            <i class="fas fa-search"></i>
                            查询用户
                        </button>
                        <button type="button" class="btn-reset" onclick="resetSearch()">
                            <i class="fas fa-undo"></i>
                            重置
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>';

// 显示搜索结果或提示信息
if ($show_results) {
    if (!empty($users)) {
        $content .= '
    <!-- 搜索结果 -->
    <div class="results-section">
        <div class="results-header">
            <h3><i class="fas fa-list"></i> 搜索结果</h3>
            <div class="results-info">
                找到 <strong>' . $total . '</strong> 个匹配的用户
            </div>
        </div>

        <div class="table-container">
            <table class="user-table">
                <thead>
                    <tr>
                        <th>用户信息</th>
                        <th>趣玩ID</th>
                        <th>联系方式</th>
                        <th>认证状态</th>
                        <th>注册时间</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody>';

        foreach ($users as $user) {
            $content .= '
                    <tr>
                        <td>
                            <div class="user-info">
                                <img src="' . htmlspecialchars($user['avatar'] ?? 'https://via.placeholder.com/40') . '"
                                     alt="头像" class="user-avatar">
                                <div class="user-details">
                                    <div class="user-name">' . htmlspecialchars($user['username']) . '</div>
                                    <div class="user-id">ID: ' . $user['id'] . '</div>
                                </div>
                            </div>
                        </td>
                        <td>
                            <div class="quwanplanet-id">
                                <strong>' . htmlspecialchars($user['quwan_id']) . '</strong>
                            </div>
                        </td>
                        <td>
                            <div class="contact-info">
                                <div>' . htmlspecialchars($user['phone'] ?? '-') . '</div>
                                <small>' . htmlspecialchars($user['email'] ?? '-') . '</small>
                            </div>
                        </td>
                        <td>' . getVerificationBadge($user) . '</td>
                        <td>' . date('Y-m-d H:i', strtotime($user['created_at'])) . '</td>
                        <td>
                            <a href="user_detail.php?id=' . $user['id'] . '" class="btn-sm primary">
                                <i class="fas fa-eye"></i> 查看详情
                            </a>
                        </td>
                    </tr>';
        }

        $content .= '
                </tbody>
            </table>
        </div>';

        // 分页
        if ($total_pages > 1) {
            $content .= '
        <div class="pagination">
            <div class="pagination-info">
                显示第 ' . (($page - 1) * $per_page + 1) . '-' . min($page * $per_page, $total) . ' 条，共 ' . $total . ' 条记录
            </div>
            <div class="pagination-links">';

            for ($i = 1; $i <= $total_pages; $i++) {
                $active = $i === $page ? 'active' : '';
                $content .= '
                <a href="?page=' . $i . '&status=' . $status_filter . '&search=' . urlencode($search) . '"
                   class="page-link ' . $active . '">' . $i . '</a>';
            }

            $content .= '
            </div>
        </div>';
        }

        $content .= '
    </div>';
    } else {
        $content .= '
    <!-- 无结果提示 -->
    <div class="no-results-section">
        <div class="no-results-content">
            <i class="fas fa-search-minus"></i>
            <h3>未找到匹配的用户</h3>
            <p>请检查搜索条件是否正确，或尝试使用其他关键词</p>
            <div class="search-tips">
                <h4>搜索建议：</h4>
                <ul>
                    <li>确保用户名、手机号或邮箱输入正确</li>
                    <li>尝试使用部分关键词进行模糊搜索</li>
                    <li>检查趣玩ID是否为7位数字</li>
                    <li>可以直接输入用户ID进行精确查找</li>
                </ul>
            </div>
        </div>
    </div>';
    }
} else {
    $content .= '
    <!-- 默认提示 -->
    <div class="welcome-section">
        <div class="welcome-content">
            <div class="welcome-icon">
                <i class="fas fa-shield-alt"></i>
            </div>
            <h3>用户隐私保护</h3>
            <p>为了保护用户隐私安全，系统采用按需查询模式</p>
            <div class="privacy-features">
                <div class="feature-item">
                    <i class="fas fa-lock"></i>
                    <span>数据加密存储</span>
                </div>
                <div class="feature-item">
                    <i class="fas fa-eye-slash"></i>
                    <span>按需查询显示</span>
                </div>
                <div class="feature-item">
                    <i class="fas fa-user-shield"></i>
                    <span>权限分级管理</span>
                </div>
                <div class="feature-item">
                    <i class="fas fa-history"></i>
                    <span>操作日志记录</span>
                </div>
            </div>
            <div class="search-guide">
                <h4>如何查询用户：</h4>
                <ol>
                    <li>在上方搜索框中输入用户的具体信息</li>
                    <li>支持搜索：用户名、手机号、邮箱、趣玩ID、用户ID</li>
                    <li>可选择认证状态进行筛选</li>
                    <li>点击"查询用户"按钮获取结果</li>
                </ol>
            </div>
        </div>
    </div>';
}

$content .= '
</div>';

// 输出页面
echo renderAdminLayout('用户管理', $content, 'user_management');
?>

<script>
function resetSearch() {
    document.getElementById('search').value = '';
    document.getElementById('status').value = 'all';
    window.location.href = 'user_management.php';
}

// 搜索表单验证
document.querySelector('.advanced-search-form').addEventListener('submit', function(e) {
    const searchInput = document.getElementById('search');
    if (searchInput.value.trim().length < 2) {
        e.preventDefault();
        alert('请输入至少2个字符进行搜索');
        searchInput.focus();
        return false;
    }
});
</script>
