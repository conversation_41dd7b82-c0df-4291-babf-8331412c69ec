/**
 * 前台实时通知组件
 * 用于接收后台推送的验证码等通知
 */

class RealtimeNotifications {
    constructor(userId) {
        this.userId = userId;
        this.eventSource = null;
        this.isConnected = false;
        this.reconnectAttempts = 0;
        this.maxReconnectAttempts = 5;
        this.reconnectDelay = 3000;
        this.heartbeatTimeout = null;

        this.init();
    }

    init() {
        this.createNotificationContainer();
        this.connect();

        // 页面卸载时断开连接
        window.addEventListener('beforeunload', () => {
            this.disconnect();
        });

        // 页面可见性变化时重连
        document.addEventListener('visibilitychange', () => {
            if (!document.hidden && !this.isConnected) {
                this.connect();
            }
        });
    }

    createNotificationContainer() {
        // 创建通知容器
        if (!document.getElementById('notification-container')) {
            const container = document.createElement('div');
            container.id = 'notification-container';
            container.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                z-index: 10000;
                pointer-events: none;
            `;
            document.body.appendChild(container);
        }
    }

    connect() {
        if (this.eventSource) {
            this.eventSource.close();
        }

        try {
            // 根据当前页面路径确定API路径
            const currentPath = window.location.pathname;
            let apiPath;

            if (currentPath.includes('/customer_service/')) {
                // 在客服页面中
                apiPath = `../api/realtime_notifications.php?user_id=${this.userId}`;
            } else if (currentPath.includes('/frontend/')) {
                // 在其他前台页面中
                apiPath = `api/realtime_notifications.php?user_id=${this.userId}`;
            } else {
                // 默认路径
                apiPath = `frontend/api/realtime_notifications.php?user_id=${this.userId}`;
            }

            this.eventSource = new EventSource(apiPath);

            this.eventSource.onopen = () => {
                console.log('实时通知连接已建立');
                this.isConnected = true;
                this.reconnectAttempts = 0;
                this.resetHeartbeatTimeout();
            };

            this.eventSource.onmessage = (event) => {
                try {
                    const data = JSON.parse(event.data);
                    this.handleNotification(data);
                } catch (error) {
                    console.error('解析通知数据失败:', error);
                }
            };

            this.eventSource.onerror = () => {
                console.error('实时通知连接错误');
                this.isConnected = false;
                this.eventSource.close();
                this.scheduleReconnect();
            };

        } catch (error) {
            console.error('创建SSE连接失败:', error);
            this.scheduleReconnect();
        }
    }

    handleNotification(data) {
        console.log('收到通知:', data);

        switch (data.type) {
            case 'connection':
                console.log('连接确认:', data.message);
                break;

            case 'heartbeat':
                this.resetHeartbeatTimeout();
                break;

            case 'verification_code':
                this.showVerificationCodeModal(data);
                break;

            case 'system_message':
                this.showSystemMessage(data);
                break;

            case 'admin_notice':
                this.showAdminNotice(data);
                break;

            case 'customer_service_message':
                this.handleCustomerServiceMessage(data);
                break;

            case 'error':
                console.error('服务器错误:', data.message);
                this.showErrorNotification(data.message);
                break;

            case 'timeout':
                console.log('连接超时:', data.message);
                this.scheduleReconnect();
                break;

            default:
                console.log('未知通知类型:', data);
        }
    }

    showVerificationCodeModal(data) {
        // 创建验证码弹窗
        const modal = this.createVerificationModal(data);
        document.body.appendChild(modal);

        // 显示弹窗
        setTimeout(() => {
            modal.style.display = 'flex';
            modal.classList.add('show');
        }, 100);

        // 播放提示音（如果支持）
        this.playNotificationSound();

        // 自动关闭定时器
        const autoCloseTime = 60000; // 60秒后自动关闭
        setTimeout(() => {
            if (document.body.contains(modal)) {
                this.closeModal(modal);
            }
        }, autoCloseTime);
    }

    createVerificationModal(data) {
        const modal = document.createElement('div');
        modal.className = 'verification-modal';
        modal.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.6);
            z-index: 10001;
            display: none;
            align-items: center;
            justify-content: center;
            padding: 20px;
            backdrop-filter: blur(5px);
            opacity: 0;
            transition: opacity 0.3s ease;
        `;

        const code = data.data?.code || '******';
        const expiresAt = data.data?.expires_at || '';
        const adminNote = data.data?.admin_note || '';
        const sentBy = data.data?.sent_by || '管理员';

        // 计算剩余时间
        const expiryTime = new Date(expiresAt);
        const now = new Date();
        const remainingMinutes = Math.max(0, Math.ceil((expiryTime - now) / 60000));

        modal.innerHTML = `
            <div class="verification-content" style="
                background: white;
                border-radius: 16px;
                padding: 32px 24px;
                max-width: 400px;
                width: 100%;
                box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
                animation: modalSlideIn 0.3s ease;
                text-align: center;
            ">
                <div class="verification-header" style="
                    margin-bottom: 24px;
                ">
                    <div style="
                        width: 60px;
                        height: 60px;
                        background: linear-gradient(135deg, #6F7BF5 0%, #9B59B6 100%);
                        border-radius: 50%;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        margin: 0 auto 16px;
                        color: white;
                        font-size: 24px;
                    ">
                        🔑
                    </div>
                    <h3 style="
                        font-size: 20px;
                        font-weight: 700;
                        color: #2D3748;
                        margin: 0 0 8px 0;
                    ">${data.title}</h3>
                    <p style="
                        color: #718096;
                        font-size: 14px;
                        margin: 0;
                    ">来自 ${sentBy}</p>
                </div>

                <div class="verification-code-display" style="
                    background: linear-gradient(135deg, #6F7BF5 0%, #9B59B6 100%);
                    color: white;
                    padding: 20px;
                    border-radius: 12px;
                    font-size: 32px;
                    font-weight: 700;
                    letter-spacing: 8px;
                    font-family: 'Courier New', monospace;
                    margin: 24px 0;
                    box-shadow: 0 8px 16px rgba(111, 123, 245, 0.3);
                ">${code}</div>

                <div class="verification-info" style="
                    background: #F7FAFC;
                    border-radius: 8px;
                    padding: 16px;
                    margin: 20px 0;
                    text-align: left;
                ">
                    <div style="
                        display: flex;
                        align-items: center;
                        gap: 8px;
                        margin-bottom: 8px;
                        color: #4A5568;
                        font-size: 14px;
                    ">
                        <span>⏰</span>
                        <span>有效期：${remainingMinutes} 分钟</span>
                    </div>
                    ${adminNote ? `
                    <div style="
                        display: flex;
                        align-items: flex-start;
                        gap: 8px;
                        color: #4A5568;
                        font-size: 14px;
                        line-height: 1.4;
                    ">
                        <span>📝</span>
                        <span>备注：${adminNote}</span>
                    </div>
                    ` : ''}
                </div>

                <div class="verification-actions" style="
                    display: flex;
                    gap: 12px;
                    margin-top: 24px;
                ">
                    <button class="copy-btn" style="
                        flex: 1;
                        padding: 12px 16px;
                        background: #EDF2F7;
                        color: #4A5568;
                        border: none;
                        border-radius: 8px;
                        font-size: 14px;
                        font-weight: 600;
                        cursor: pointer;
                        transition: all 0.2s ease;
                    " onclick="this.closest('.verification-modal').querySelector('.verification-code-display').select(); document.execCommand('copy'); this.textContent='已复制'; setTimeout(() => this.textContent='复制验证码', 2000);">
                        复制验证码
                    </button>
                    <button class="close-btn" style="
                        flex: 1;
                        padding: 12px 16px;
                        background: linear-gradient(135deg, #6F7BF5 0%, #9B59B6 100%);
                        color: white;
                        border: none;
                        border-radius: 8px;
                        font-size: 14px;
                        font-weight: 600;
                        cursor: pointer;
                        transition: all 0.2s ease;
                    " onclick="this.closest('.verification-modal').remove();">
                        我知道了
                    </button>
                </div>
            </div>
        `;

        // 添加样式
        const style = document.createElement('style');
        style.textContent = `
            @keyframes modalSlideIn {
                from {
                    opacity: 0;
                    transform: translateY(20px) scale(0.95);
                }
                to {
                    opacity: 1;
                    transform: translateY(0) scale(1);
                }
            }

            .verification-modal.show {
                opacity: 1;
            }

            .copy-btn:hover {
                background: #E2E8F0 !important;
                transform: translateY(-1px);
            }

            .close-btn:hover {
                transform: translateY(-1px);
                box-shadow: 0 4px 12px rgba(111, 123, 245, 0.4);
            }
        `;
        document.head.appendChild(style);

        return modal;
    }

    closeModal(modal) {
        modal.style.opacity = '0';
        setTimeout(() => {
            if (document.body.contains(modal)) {
                modal.remove();
            }
        }, 300);
    }

    showSystemMessage(data) {
        this.showToastNotification(data.title, data.content, 'info');
    }

    showAdminNotice(data) {
        this.showToastNotification(data.title, data.content, 'warning');
    }

    showErrorNotification(message) {
        this.showToastNotification('系统提示', message, 'error');
    }

    handleCustomerServiceMessage(data) {
        console.log('收到客服消息:', data);

        // 如果有自定义的消息处理函数，调用它
        if (window.onCustomerServiceMessage && typeof window.onCustomerServiceMessage === 'function') {
            window.onCustomerServiceMessage(data);
            return;
        }

        // 默认显示通知
        const messageData = data.data ? JSON.parse(data.data) : data;
        const content = messageData.content || data.content || '您有新的客服消息';
        const senderName = messageData.sender_name || '客服';

        this.showToastNotification(
            `${senderName}回复`,
            content,
            'info'
        );

        // 播放提示音
        this.playNotificationSound();
    }

    showToastNotification(title, message, type = 'info') {
        const container = document.getElementById('notification-container');
        const toast = document.createElement('div');

        const colors = {
            info: { bg: '#3182CE', icon: 'ℹ️' },
            warning: { bg: '#D69E2E', icon: '⚠️' },
            error: { bg: '#E53E3E', icon: '❌' },
            success: { bg: '#38A169', icon: '✅' }
        };

        const color = colors[type] || colors.info;

        toast.style.cssText = `
            background: ${color.bg};
            color: white;
            padding: 16px 20px;
            border-radius: 8px;
            margin-bottom: 12px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            pointer-events: auto;
            cursor: pointer;
            transition: all 0.3s ease;
            transform: translateX(100%);
            opacity: 0;
            max-width: 350px;
        `;

        toast.innerHTML = `
            <div style="display: flex; align-items: flex-start; gap: 12px;">
                <span style="font-size: 18px;">${color.icon}</span>
                <div style="flex: 1;">
                    <div style="font-weight: 600; margin-bottom: 4px;">${title}</div>
                    <div style="font-size: 14px; opacity: 0.9; line-height: 1.4;">${message}</div>
                </div>
            </div>
        `;

        container.appendChild(toast);

        // 显示动画
        setTimeout(() => {
            toast.style.transform = 'translateX(0)';
            toast.style.opacity = '1';
        }, 100);

        // 点击关闭
        toast.addEventListener('click', () => {
            this.removeToast(toast);
        });

        // 自动关闭
        setTimeout(() => {
            this.removeToast(toast);
        }, 5000);
    }

    removeToast(toast) {
        toast.style.transform = 'translateX(100%)';
        toast.style.opacity = '0';
        setTimeout(() => {
            if (toast.parentNode) {
                toast.parentNode.removeChild(toast);
            }
        }, 300);
    }

    playNotificationSound() {
        try {
            // 创建简单的提示音
            const audioContext = new (window.AudioContext || window.webkitAudioContext)();
            const oscillator = audioContext.createOscillator();
            const gainNode = audioContext.createGain();

            oscillator.connect(gainNode);
            gainNode.connect(audioContext.destination);

            oscillator.frequency.setValueAtTime(800, audioContext.currentTime);
            oscillator.frequency.setValueAtTime(1000, audioContext.currentTime + 0.1);

            gainNode.gain.setValueAtTime(0.1, audioContext.currentTime);
            gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.2);

            oscillator.start(audioContext.currentTime);
            oscillator.stop(audioContext.currentTime + 0.2);
        } catch (error) {
            console.log('无法播放提示音:', error);
        }
    }

    resetHeartbeatTimeout() {
        if (this.heartbeatTimeout) {
            clearTimeout(this.heartbeatTimeout);
        }

        // 如果60秒内没有收到心跳，认为连接断开
        this.heartbeatTimeout = setTimeout(() => {
            console.log('心跳超时，重新连接...');
            this.isConnected = false;
            this.scheduleReconnect();
        }, 60000);
    }

    scheduleReconnect() {
        if (this.reconnectAttempts >= this.maxReconnectAttempts) {
            console.error('达到最大重连次数，停止重连');
            this.showErrorNotification('实时通知连接失败，请刷新页面');
            return;
        }

        this.reconnectAttempts++;
        const delay = this.reconnectDelay * this.reconnectAttempts;

        console.log(`${delay/1000}秒后尝试第${this.reconnectAttempts}次重连...`);

        setTimeout(() => {
            if (!this.isConnected) {
                this.connect();
            }
        }, delay);
    }

    disconnect() {
        if (this.eventSource) {
            this.eventSource.close();
            this.eventSource = null;
        }

        if (this.heartbeatTimeout) {
            clearTimeout(this.heartbeatTimeout);
        }

        this.isConnected = false;
        console.log('实时通知连接已断开');
    }
}

// 自动初始化（如果页面中有用户ID）
document.addEventListener('DOMContentLoaded', function() {
    // 尝试从多个地方获取用户ID
    const userId = window.currentUserId ||
                   document.querySelector('meta[name="user-id"]')?.content ||
                   localStorage.getItem('user_id') ||
                   sessionStorage.getItem('user_id');

    if (userId && userId !== '0') {
        console.log('初始化实时通知系统，用户ID:', userId);
        window.realtimeNotifications = new RealtimeNotifications(userId);
    } else {
        console.log('未找到用户ID，跳过实时通知初始化');
    }
});

// 导出类供其他脚本使用
window.RealtimeNotifications = RealtimeNotifications;
