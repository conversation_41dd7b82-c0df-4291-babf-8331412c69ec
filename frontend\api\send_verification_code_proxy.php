<?php
/**
 * 前台代理：发送验证码
 * 代理请求到后台API，解决跨域问题
 */

// 禁用错误输出
error_reporting(0);
ini_set('display_errors', 0);

// 清理输出缓冲区
if (ob_get_level()) {
    ob_clean();
}

header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// 处理OPTIONS预检请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

$response = ['success' => false, 'message' => '', 'debug' => []];

try {
    // 获取数据库连接
    $pdo = new PDO(
        "mysql:host=localhost;dbname=quwanplanet;charset=utf8mb4",
        "quwanplanet",
        "nJmJm23FB4Xn6Fc3",
        [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC
        ]
    );

    $response['debug']['step'] = '1-数据库连接成功';

    // 获取POST数据
    $input_raw = file_get_contents('php://input');
    $input = json_decode($input_raw, true);

    if (!$input) {
        $response['message'] = '无效的请求数据';
        echo json_encode($response, JSON_UNESCAPED_UNICODE);
        exit;
    }

    $response['debug']['step'] = '2-数据解析成功';

    // 获取参数
    $user_id = intval($input['user_id'] ?? 0);
    $phone = trim($input['phone'] ?? '');
    $type = trim($input['type'] ?? '');
    $note = trim($input['note'] ?? '');
    $expiry_minutes = intval($input['expiry'] ?? 5);

    // 验证必需参数
    if ($user_id <= 0 || empty($phone) || empty($type) || empty($note)) {
        $response['message'] = '请填写完整的验证码信息';
        echo json_encode($response, JSON_UNESCAPED_UNICODE);
        exit;
    }

    $response['debug']['step'] = '3-参数验证通过';

    // 验证手机号格式
    if (!preg_match('/^1[3-9]\d{9}$/', $phone)) {
        $response['message'] = '手机号格式不正确';
        echo json_encode($response, JSON_UNESCAPED_UNICODE);
        exit;
    }

    // 检查用户是否存在
    $stmt = $pdo->prepare("SELECT id, username FROM users WHERE id = ?");
    $stmt->execute([$user_id]);
    $user = $stmt->fetch(PDO::FETCH_ASSOC);

    if (!$user) {
        $response['message'] = '用户不存在';
        echo json_encode($response, JSON_UNESCAPED_UNICODE);
        exit;
    }

    $response['debug']['step'] = '4-用户验证通过';

    // 生成验证码
    $verification_code = sprintf('%06d', mt_rand(100000, 999999));
    $expires_at = date('Y-m-d H:i:s', time() + ($expiry_minutes * 60));

    $response['debug']['step'] = '5-验证码生成完成';

    // 开始事务
    $pdo->beginTransaction();

    try {
        // 1. 将之前的验证码设为过期
        $stmt = $pdo->prepare("
            UPDATE verification_codes
            SET status = 'expired'
            WHERE user_id = ? AND status = 'pending'
        ");
        $stmt->execute([$user_id]);

        // 2. 插入新的验证码记录
        $stmt = $pdo->prepare("
            INSERT INTO verification_codes
            (user_id, phone, code, type, status, sent_by_admin, admin_note, expires_at)
            VALUES (?, ?, ?, ?, 'pending', 1, ?, ?)
        ");
        $stmt->execute([
            $user_id,
            $phone,
            $verification_code,
            $type,
            $note,
            $expires_at
        ]);

        $verification_id = $pdo->lastInsertId();
        $response['debug']['step'] = '6-验证码记录插入成功';

        // 3. 创建实时通知记录
        $notification_title = '管理员验证码';
        $notification_content = "您收到一条来自管理员的验证码：{$verification_code}";

        // 根据类型设置不同的通知内容
        switch ($type) {
            case 'security_verify':
                $notification_title = '安全验证码';
                $notification_content = "安全验证码：{$verification_code}，请在{$expiry_minutes}分钟内使用";
                break;
            case 'system_notice':
                $notification_title = '系统通知验证码';
                $notification_content = "系统验证码：{$verification_code}，有效期{$expiry_minutes}分钟";
                break;
            default:
                $notification_title = '管理员验证码';
                $notification_content = "管理员发送的验证码：{$verification_code}，请在{$expiry_minutes}分钟内使用";
        }

        $notification_data = [
            'verification_id' => $verification_id,
            'code' => $verification_code,
            'type' => $type,
            'expires_at' => $expires_at,
            'admin_note' => $note,
            'sent_by' => '管理员'
        ];

        // 检查 priority 字段是否存在
        $stmt = $pdo->query("SHOW COLUMNS FROM realtime_notifications LIKE 'priority'");
        $has_priority = $stmt->rowCount() > 0;

        if ($has_priority) {
            $stmt = $pdo->prepare("
                INSERT INTO realtime_notifications
                (user_id, type, title, content, data, status, priority, expires_at)
                VALUES (?, 'verification_code', ?, ?, ?, 'pending', 5, ?)
            ");
            $stmt->execute([
                $user_id,
                $notification_title,
                $notification_content,
                json_encode($notification_data),
                $expires_at
            ]);
        } else {
            $stmt = $pdo->prepare("
                INSERT INTO realtime_notifications
                (user_id, type, title, content, data, status, expires_at)
                VALUES (?, 'verification_code', ?, ?, ?, 'pending', ?)
            ");
            $stmt->execute([
                $user_id,
                $notification_title,
                $notification_content,
                json_encode($notification_data),
                $expires_at
            ]);
        }

        $notification_id = $pdo->lastInsertId();
        $response['debug']['step'] = '7-实时通知记录创建成功';

        // 提交事务
        $pdo->commit();

        $response['success'] = true;
        $response['message'] = '验证码发送成功';
        $response['data'] = [
            'verification_id' => $verification_id,
            'notification_id' => $notification_id,
            'code' => $verification_code,
            'expires_at' => $expires_at,
            'user' => [
                'id' => $user_id,
                'username' => $user['username'],
                'phone' => $phone
            ]
        ];

    } catch (Exception $e) {
        $pdo->rollBack();
        throw $e;
    }

} catch (PDOException $e) {
    $response['message'] = '数据库操作失败';
    $response['debug']['error'] = $e->getMessage();
} catch (Exception $e) {
    $response['message'] = '发送验证码失败：' . $e->getMessage();
    $response['debug']['error'] = $e->getMessage();
}

echo json_encode($response, JSON_UNESCAPED_UNICODE);
?>
