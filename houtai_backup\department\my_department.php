<?php
/**
 * 部门管理 - 我的部门页面
 * 趣玩星球管理后台
 */

session_start();

// 登录检查
if (!isset($_SESSION['admin_id'])) {
    header('Location: ../login.php');
    exit;
}

$admin_name = $_SESSION['admin_name'] ?? '管理员';
$admin_id = $_SESSION['admin_id'] ?? '';
$admin_employee_id = $_SESSION['admin_employee_id'] ?? '';

// 模拟当前用户的部门信息
$my_department = [
    'id' => 1,
    'name' => '客户服务中心',
    'code' => 'CS',
    'description' => '负责用户咨询、投诉处理和客户关系维护，提供7x24小时优质客户服务',
    'manager_name' => '张小明',
    'manager_id' => 'CS001',
    'employee_count' => 15,
    'created_at' => '2024-01-01',
    'status' => 'active'
];

// 模拟部门员工数据
$department_employees = [
    [
        'id' => 1,
        'name' => '李小红',
        'employee_id' => 'CS002',
        'position' => '客服主管',
        'email' => '<EMAIL>',
        'phone' => '138****1234',
        'hire_date' => '2024-01-15',
        'status' => 'active',
        'avatar' => '李'
    ],
    [
        'id' => 2,
        'name' => '王小强',
        'employee_id' => 'CS003',
        'position' => '高级客服专员',
        'email' => '<EMAIL>',
        'phone' => '139****5678',
        'hire_date' => '2024-02-01',
        'status' => 'active',
        'avatar' => '王'
    ],
    [
        'id' => 3,
        'name' => '赵小美',
        'employee_id' => 'CS004',
        'position' => '客服专员',
        'email' => '<EMAIL>',
        'phone' => '137****9012',
        'hire_date' => '2024-02-15',
        'status' => 'active',
        'avatar' => '赵'
    ],
    [
        'id' => 4,
        'name' => '孙小亮',
        'employee_id' => 'CS005',
        'position' => '客服专员',
        'email' => '<EMAIL>',
        'phone' => '136****3456',
        'hire_date' => '2024-03-01',
        'status' => 'active',
        'avatar' => '孙'
    ],
    [
        'id' => 5,
        'name' => '周小芳',
        'employee_id' => 'CS006',
        'position' => '实习生',
        'email' => '<EMAIL>',
        'phone' => '135****7890',
        'hire_date' => '2024-04-01',
        'status' => 'probation',
        'avatar' => '周'
    ]
];

// 模拟部门统计数据
$dept_stats = [
    'total_employees' => count($department_employees),
    'active_employees' => count(array_filter($department_employees, fn($e) => $e['status'] === 'active')),
    'probation_employees' => count(array_filter($department_employees, fn($e) => $e['status'] === 'probation')),
    'avg_tenure' => 8.5, // 平均工龄（月）
    'this_month_new' => 2,
    'satisfaction_rate' => 95.8
];
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>我的部门 - 趣玩星球管理后台</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="../assets/css/admin-layout.css">
    <style>
        /* 我的部门页面专用样式 */
        .my-dept-content {
            padding: 24px;
        }

        .dept-overview {
            background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
            border-radius: 16px;
            padding: 32px;
            margin-bottom: 32px;
            color: white;
            position: relative;
            overflow: hidden;
        }

        .dept-overview::before {
            content: '';
            position: absolute;
            top: -50%;
            right: -50%;
            width: 200%;
            height: 200%;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="50" cy="50" r="1" fill="rgba(255,255,255,0.1)"/></svg>') repeat;
            animation: float 20s infinite linear;
        }

        .dept-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 24px;
            position: relative;
            z-index: 1;
        }

        .dept-info h1 {
            font-size: 2rem;
            font-weight: 700;
            margin-bottom: 8px;
        }

        .dept-code {
            background: rgba(255, 255, 255, 0.2);
            padding: 4px 12px;
            border-radius: 12px;
            font-size: 0.875rem;
            font-weight: 600;
            display: inline-block;
            margin-bottom: 16px;
        }

        .dept-description {
            font-size: 1.1rem;
            line-height: 1.6;
            opacity: 0.9;
        }

        .dept-manager-card {
            background: rgba(255, 255, 255, 0.15);
            border-radius: 12px;
            padding: 20px;
            backdrop-filter: blur(10px);
        }

        .manager-info {
            display: flex;
            align-items: center;
            gap: 16px;
        }

        .manager-avatar {
            width: 60px;
            height: 60px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            font-weight: 700;
        }

        .manager-details h3 {
            font-size: 1.25rem;
            font-weight: 700;
            margin-bottom: 4px;
        }

        .manager-title {
            opacity: 0.8;
        }

        /* 统计卡片 */
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 32px;
        }

        .stat-card {
            background: white;
            border-radius: 16px;
            padding: 24px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
            text-align: center;
            transition: all 0.3s ease;
            border: 2px solid transparent;
        }

        .stat-card:hover {
            border-color: var(--primary-color);
            transform: translateY(-4px);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15);
        }

        .stat-icon {
            width: 60px;
            height: 60px;
            margin: 0 auto 16px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            color: white;
        }

        .stat-number {
            font-size: 2rem;
            font-weight: 700;
            color: var(--gray-800);
            margin-bottom: 4px;
        }

        .stat-label {
            color: var(--gray-600);
            font-size: 0.875rem;
        }

        .stat-trend {
            font-size: 0.75rem;
            margin-top: 4px;
        }

        .trend-up {
            color: var(--success-color);
        }

        .trend-down {
            color: var(--error-color);
        }

        /* 员工管理区域 */
        .employees-section {
            background: white;
            border-radius: 16px;
            padding: 24px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
        }

        .section-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 24px;
            padding-bottom: 16px;
            border-bottom: 2px solid var(--gray-100);
        }

        .section-title {
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--gray-800);
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .add-employee-btn {
            background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
            color: white;
            border: none;
            padding: 12px 20px;
            border-radius: var(--border-radius);
            font-weight: 600;
            cursor: pointer;
            transition: var(--transition);
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .add-employee-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(64, 224, 208, 0.3);
        }

        /* 员工列表 */
        .employees-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 20px;
        }

        .employee-card {
            background: var(--gray-50);
            border-radius: 12px;
            padding: 20px;
            border: 2px solid transparent;
            transition: all 0.3s ease;
        }

        .employee-card:hover {
            background: white;
            border-color: var(--primary-color);
            box-shadow: 0 4px 20px rgba(64, 224, 208, 0.1);
            transform: translateY(-2px);
        }

        .employee-header {
            display: flex;
            align-items: center;
            gap: 16px;
            margin-bottom: 16px;
        }

        .employee-avatar {
            width: 50px;
            height: 50px;
            background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 18px;
            font-weight: 600;
        }

        .employee-info h4 {
            font-size: 1.1rem;
            font-weight: 600;
            color: var(--gray-800);
            margin-bottom: 4px;
        }

        .employee-id {
            font-size: 0.75rem;
            color: var(--gray-500);
            background: var(--gray-200);
            padding: 2px 8px;
            border-radius: 8px;
            font-family: monospace;
        }

        .employee-details {
            margin-bottom: 16px;
        }

        .detail-item {
            display: flex;
            align-items: center;
            gap: 8px;
            margin-bottom: 8px;
            font-size: 0.875rem;
            color: var(--gray-600);
        }

        .detail-icon {
            width: 16px;
            color: var(--gray-400);
        }

        .employee-status {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }

        .status-badge {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 0.75rem;
            font-weight: 600;
        }

        .status-active {
            background: rgba(16, 185, 129, 0.1);
            color: var(--success-color);
        }

        .status-probation {
            background: rgba(245, 158, 11, 0.1);
            color: var(--warning-color);
        }

        .tenure-info {
            font-size: 0.75rem;
            color: var(--gray-500);
        }

        .employee-actions {
            display: flex;
            gap: 8px;
        }

        .action-btn {
            flex: 1;
            padding: 8px 12px;
            border: none;
            border-radius: var(--border-radius);
            font-size: 0.875rem;
            font-weight: 600;
            cursor: pointer;
            transition: var(--transition);
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 6px;
        }

        .btn-view {
            background: var(--info-color);
            color: white;
        }

        .btn-view:hover {
            background: #2563EB;
            transform: translateY(-1px);
        }

        .btn-edit {
            background: var(--secondary-color);
            color: white;
        }

        .btn-edit:hover {
            background: #5B21B6;
            transform: translateY(-1px);
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .my-dept-content {
                padding: 16px;
            }

            .dept-header {
                flex-direction: column;
                gap: 20px;
            }

            .employees-grid {
                grid-template-columns: 1fr;
            }

            .section-header {
                flex-direction: column;
                gap: 16px;
                align-items: stretch;
            }

            .employee-actions {
                flex-direction: column;
            }
        }
    </style>
</head>
<body>
    <div class="admin-layout">
        <!-- 左侧菜单 -->
        <?php include '../includes/sidebar.php'; ?>

        <!-- 主内容区域 -->
        <main class="main-content">
            <!-- 顶部导航栏 -->
            <?php include '../includes/topbar.php'; ?>

            <div class="my-dept-content">
                <!-- 部门概览 -->
                <div class="dept-overview">
                    <div class="dept-header">
                        <div class="dept-info">
                            <h1><?php echo htmlspecialchars($my_department['name']); ?></h1>
                            <div class="dept-code"><?php echo htmlspecialchars($my_department['code']); ?></div>
                            <div class="dept-description">
                                <?php echo htmlspecialchars($my_department['description']); ?>
                            </div>
                        </div>
                        <div class="dept-manager-card">
                            <div class="manager-info">
                                <div class="manager-avatar">
                                    <?php echo mb_substr($my_department['manager_name'], 0, 1, 'UTF-8'); ?>
                                </div>
                                <div class="manager-details">
                                    <h3><?php echo htmlspecialchars($my_department['manager_name']); ?></h3>
                                    <div class="manager-title">部门经理</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 部门统计 -->
                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-icon" style="background: linear-gradient(135deg, var(--primary-color), var(--primary-light));">
                            <i class="fas fa-users"></i>
                        </div>
                        <div class="stat-number"><?php echo $dept_stats['total_employees']; ?></div>
                        <div class="stat-label">总员工数</div>
                        <div class="stat-trend trend-up">
                            <i class="fas fa-arrow-up"></i> 本月新增 <?php echo $dept_stats['this_month_new']; ?>人
                        </div>
                    </div>

                    <div class="stat-card">
                        <div class="stat-icon" style="background: linear-gradient(135deg, var(--success-color), #34D399);">
                            <i class="fas fa-user-check"></i>
                        </div>
                        <div class="stat-number"><?php echo $dept_stats['active_employees']; ?></div>
                        <div class="stat-label">正式员工</div>
                        <div class="stat-trend">
                            转正率 <?php echo round($dept_stats['active_employees']/$dept_stats['total_employees']*100, 1); ?>%
                        </div>
                    </div>

                    <div class="stat-card">
                        <div class="stat-icon" style="background: linear-gradient(135deg, var(--warning-color), #FBBF24);">
                            <i class="fas fa-user-clock"></i>
                        </div>
                        <div class="stat-number"><?php echo $dept_stats['probation_employees']; ?></div>
                        <div class="stat-label">试用期员工</div>
                        <div class="stat-trend">
                            平均试用期 3个月
                        </div>
                    </div>

                    <div class="stat-card">
                        <div class="stat-icon" style="background: linear-gradient(135deg, var(--info-color), #60A5FA);">
                            <i class="fas fa-chart-line"></i>
                        </div>
                        <div class="stat-number"><?php echo $dept_stats['satisfaction_rate']; ?>%</div>
                        <div class="stat-label">满意度</div>
                        <div class="stat-trend trend-up">
                            <i class="fas fa-arrow-up"></i> 较上月提升 2.3%
                        </div>
                    </div>
                </div>

                <!-- 员工管理 -->
                <div class="employees-section">
                    <div class="section-header">
                        <div class="section-title">
                            <i class="fas fa-user-friends"></i>
                            部门员工管理
                        </div>
                        <button class="add-employee-btn" onclick="addEmployee()">
                            <i class="fas fa-user-plus"></i>
                            添加员工
                        </button>
                    </div>

                    <div class="employees-grid">
                        <?php foreach ($department_employees as $employee): ?>
                            <div class="employee-card">
                                <div class="employee-header">
                                    <div class="employee-avatar">
                                        <?php echo htmlspecialchars($employee['avatar']); ?>
                                    </div>
                                    <div class="employee-info">
                                        <h4><?php echo htmlspecialchars($employee['name']); ?></h4>
                                        <div class="employee-id"><?php echo htmlspecialchars($employee['employee_id']); ?></div>
                                    </div>
                                </div>

                                <div class="employee-details">
                                    <div class="detail-item">
                                        <i class="fas fa-briefcase detail-icon"></i>
                                        <span><?php echo htmlspecialchars($employee['position']); ?></span>
                                    </div>
                                    <div class="detail-item">
                                        <i class="fas fa-envelope detail-icon"></i>
                                        <span><?php echo htmlspecialchars($employee['email']); ?></span>
                                    </div>
                                    <div class="detail-item">
                                        <i class="fas fa-phone detail-icon"></i>
                                        <span><?php echo htmlspecialchars($employee['phone']); ?></span>
                                    </div>
                                    <div class="detail-item">
                                        <i class="fas fa-calendar detail-icon"></i>
                                        <span>入职：<?php echo date('Y-m-d', strtotime($employee['hire_date'])); ?></span>
                                    </div>
                                </div>

                                <div class="employee-status">
                                    <div class="status-badge status-<?php echo $employee['status']; ?>">
                                        <?php echo $employee['status'] === 'active' ? '正式员工' : '试用期'; ?>
                                    </div>
                                    <div class="tenure-info">
                                        工龄 <?php
                                        $months = (time() - strtotime($employee['hire_date'])) / (30 * 24 * 3600);
                                        echo round($months, 1);
                                        ?>个月
                                    </div>
                                </div>

                                <div class="employee-actions">
                                    <button class="action-btn btn-view" onclick="viewEmployee(<?php echo $employee['id']; ?>)">
                                        <i class="fas fa-eye"></i>
                                        查看
                                    </button>
                                    <button class="action-btn btn-edit" onclick="editEmployee(<?php echo $employee['id']; ?>)">
                                        <i class="fas fa-edit"></i>
                                        编辑
                                    </button>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <script src="../assets/js/admin-layout.js"></script>
    <script>
        // 我的部门页面脚本
        document.addEventListener('DOMContentLoaded', function() {
            // 更新页面标题
            updatePageTitle('我的部门');

            // 动画效果
            const cards = document.querySelectorAll('.stat-card, .employee-card');
            cards.forEach((card, index) => {
                card.style.opacity = '0';
                card.style.transform = 'translateY(20px)';
                setTimeout(() => {
                    card.style.transition = 'all 0.5s ease';
                    card.style.opacity = '1';
                    card.style.transform = 'translateY(0)';
                }, index * 100);
            });
        });

        function addEmployee() {
            alert('添加员工功能开发中...\n\n将打开员工信息录入表单，可以设置员工基本信息、职位、权限等。');
        }

        function viewEmployee(employeeId) {
            alert(`查看员工详情功能开发中...\n\n将显示员工 ${employeeId} 的详细信息、工作记录和绩效数据。`);
        }

        function editEmployee(employeeId) {
            alert(`编辑员工功能开发中...\n\n将打开员工 ${employeeId} 的编辑表单，可以修改基本信息和职位。`);
        }

        console.log('👥 我的部门页面已加载');
    </script>
</body>
</html>
