<?php
/**
 * 管理员认证检查
 * 确保只有已登录的管理员可以访问后台页面
 */

session_start();

// 检查是否已登录
if (!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
    // 如果是AJAX请求，返回JSON
    if (!empty($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest') {
        header('Content-Type: application/json');
        echo json_encode(['success' => false, 'message' => '请先登录']);
        exit;
    }
    
    // 普通请求，重定向到登录页面
    header('Location: /houtai_backup/login.php');
    exit;
}

// 检查会话是否过期（可选）
if (isset($_SESSION['admin_last_activity'])) {
    $inactive_time = time() - $_SESSION['admin_last_activity'];
    $session_timeout = 3600; // 1小时超时
    
    if ($inactive_time > $session_timeout) {
        // 会话过期，清除会话并重定向
        session_destroy();
        header('Location: /houtai_backup/login.php?timeout=1');
        exit;
    }
}

// 更新最后活动时间
$_SESSION['admin_last_activity'] = time();

// 获取管理员信息（如果需要）
$admin_id = $_SESSION['admin_id'] ?? 0;
$admin_name = $_SESSION['admin_name'] ?? '管理员';
$admin_role = $_SESSION['admin_role'] ?? '系统管理员';
$admin_permissions = $_SESSION['admin_permissions'] ?? [];
?>
