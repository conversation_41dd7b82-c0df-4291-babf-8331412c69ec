-- =====================================================
-- 只添加缺失字段的修复脚本
-- 避免重复添加已存在的字段
-- =====================================================

-- 如果 admin_logs 表缺少 department 字段，请执行：
-- ALTER TABLE admin_logs ADD COLUMN department VARCHAR(100) DEFAULT NULL;

-- 创建 user_logs 表（如果不存在）
CREATE TABLE IF NOT EXISTS user_logs (
    id INT(11) NOT NULL AUTO_INCREMENT,
    user_id INT(11) NOT NULL,
    operator_name VARCHAR(100) NOT NULL,
    employee_id VARCHAR(50) DEFAULT NULL,
    department VARCHAR(100) DEFAULT NULL,
    type VARCHAR(50) NOT NULL,
    content TEXT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (id),
    KEY idx_user_id (user_id),
    KEY idx_type (type),
    KEY idx_created_at (created_at)
);

-- 如果 admin_users 表缺少 employee_id 字段，请执行：
-- ALTER TABLE admin_users ADD COLUMN employee_id VARCHAR(50) DEFAULT NULL;

-- 如果 admin_users 表缺少 department 字段，请执行：
-- ALTER TABLE admin_users ADD COLUMN department VARCHAR(100) DEFAULT NULL;

-- 更新管理员信息（可选）
UPDATE admin_users SET 
    employee_id = CONCAT('EMP', LPAD(id, 3, '0')),
    department = '管理部门'
WHERE (employee_id IS NULL OR employee_id = '') AND id IS NOT NULL;
