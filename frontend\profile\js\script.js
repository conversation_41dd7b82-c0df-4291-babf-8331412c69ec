// 全局定义Toast提示函数
function showToast(message, duration = 3000) {
    console.log('显示Toast提示:', message);

    const toast = document.getElementById('toast');
    if (!toast) {
        console.error('未找到toast元素');
        alert(message); // 如果找不到toast元素，使用alert作为备选
        return;
    }

    // 如果已经有toast在显示，先清除之前的定时器
    if (toast.timer) {
        clearTimeout(toast.timer);
    }

    // 先隐藏toast，触发重绘，重置动画
    toast.style.display = 'none';
    void toast.offsetWidth; // 触发重绘

    // 设置消息并显示
    toast.textContent = message;
    toast.classList.add('show');

    // 设置定时器关闭toast
    toast.timer = setTimeout(() => {
        toast.classList.remove('show');
    }, duration);
}

// 确保window.showToast也可用
window.showToast = showToast;

document.addEventListener('DOMContentLoaded', function() {
    // 现代化复制ID功能
    const copyIdBtn = document.querySelector('.modern-copy-btn, .copy-id-btn');
    if (copyIdBtn) {
        copyIdBtn.addEventListener('click', function() {
            const idToCopy = this.getAttribute('data-id');

            // 创建临时输入框
            const tempInput = document.createElement('input');
            tempInput.value = idToCopy;
            document.body.appendChild(tempInput);

            // 选择并复制文本
            tempInput.select();
            document.execCommand('copy');

            // 移除临时输入框
            document.body.removeChild(tempInput);

            // 显示复制成功提示，包含实际ID
            showToast('您已复制趣玩ID：' + idToCopy);

            // 添加现代化点击动画
            this.style.transform = 'scale(1.2)';
            setTimeout(() => {
                this.style.transform = 'scale(1)';
            }, 200);
        });
    }

    // 现代化会员卡片交互效果
    const memberBtn = document.querySelector('.modern-member-btn, .member-btn');
    if (memberBtn) {
        memberBtn.addEventListener('click', function() {
            // 点击效果
            this.style.transform = 'scale(0.95)';
            setTimeout(() => {
                this.style.transform = 'scale(1)';
            }, 100);

            // 显示会员开通提示
            showToast('会员功能即将上线，敬请期待');
        });
    }

    // 现代化会员卡片悬停效果
    const memberCard = document.querySelector('.modern-member-card, .member-card');
    if (memberCard) {
        memberCard.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-2px)';
            this.style.boxShadow = '0 12px 40px rgba(111, 123, 245, 0.15)';
        });

        memberCard.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0)';
            this.style.boxShadow = '';
        });
    }

    // 现代化会员权益项点击效果
    const benefitItems = document.querySelectorAll('.modern-benefit-item, .benefit-item');
    benefitItems.forEach(item => {
        item.addEventListener('click', function() {
            // 获取权益名称
            const benefitName = this.querySelector('.modern-benefit-text, .benefit-text').textContent;

            // 显示权益提示
            showToast(`${benefitName}即将上线，敬请期待`);

            // 添加点击动画
            const icon = this.querySelector('.modern-benefit-icon, .benefit-icon');
            icon.style.transform = 'scale(1.2) rotate(10deg)';
            setTimeout(() => {
                icon.style.transform = 'scale(1) rotate(0deg)';
            }, 300);
        });
    });

    // 现代化菜单项点击效果
    const menuItems = document.querySelectorAll('.modern-menu-item, .menu-item');
    menuItems.forEach(item => {
        item.addEventListener('click', function() {
            // 现代化点击效果
            this.style.transform = 'translateX(8px)';
            this.style.backgroundColor = 'var(--bg-primary)';
            setTimeout(() => {
                this.style.transform = '';
                this.style.backgroundColor = '';
            }, 200);
        });
    });

    // 现代化功能网格项点击效果
    const featureItems = document.querySelectorAll('.modern-feature-item, .feature-item');
    featureItems.forEach(item => {
        item.addEventListener('click', function() {
            // 点击动画
            this.style.transform = 'translateY(-2px) scale(0.98)';
            setTimeout(() => {
                this.style.transform = '';
            }, 150);
        });
    });

    // 底部导航栏点击效果
    const navItems = document.querySelectorAll('.nav-item');
    navItems.forEach(item => {
        item.addEventListener('click', function() {
            // 这里不需要移除其他项的active类，因为会跳转到新页面
            // 但为了视觉反馈，可以添加点击效果
            this.style.transform = 'scale(0.95)';
            setTimeout(() => {
                this.style.transform = 'scale(1)';
            }, 100);
        });
    });

    // 检查URL参数，显示相关提示
    const urlParams = new URLSearchParams(window.location.search);
    if (urlParams.has('settings_updated')) {
        showToast('设置已更新');
    }

    // 设置弹窗功能
    initializeSettingsPanel();
});

// 设置弹窗控制函数
function openSettingsPanel() {
    const overlay = document.getElementById('settingsOverlay');
    if (overlay) {
        overlay.classList.add('show');
        document.body.style.overflow = 'hidden'; // 防止背景滚动
    }
}

function closeSettingsPanel() {
    const overlay = document.getElementById('settingsOverlay');
    if (overlay) {
        overlay.classList.remove('show');
        document.body.style.overflow = ''; // 恢复滚动
    }
}

// 初始化设置弹窗
function initializeSettingsPanel() {
    // 深色模式切换
    const darkModeToggle = document.getElementById('dark-mode-toggle');
    if (darkModeToggle) {
        darkModeToggle.addEventListener('change', function() {
            if (this.checked) {
                showToast('深色模式即将上线，敬请期待');
                setTimeout(() => {
                    this.checked = false;
                }, 500);
            }
        });
    }

    // 注销账号确认
    const deleteAccount = document.getElementById('delete-account');
    if (deleteAccount) {
        deleteAccount.addEventListener('click', function(e) {
            e.preventDefault();
            if (confirm('确定要注销账号吗？此操作不可逆，您的所有数据将被删除。')) {
                showToast('账号注销功能即将上线，敬请期待');
            }
        });
    }

    // 退出登录处理
    const logoutForm = document.getElementById('logout-form');
    if (logoutForm) {
        logoutForm.addEventListener('submit', function(e) {
            if (!confirm('确定要退出登录吗？')) {
                e.preventDefault();
            }
        });
    }

    // 设置项点击效果和链接处理
    const settingsItems = document.querySelectorAll('.settings-item');
    settingsItems.forEach(item => {
        item.addEventListener('click', function(e) {
            // 点击效果
            this.style.backgroundColor = 'var(--bg-primary)';
            setTimeout(() => {
                this.style.backgroundColor = '';
            }, 150);

            // 如果是链接项（有href属性），在跳转前关闭弹窗
            if (this.tagName === 'A' && this.href) {
                // 延迟关闭弹窗，让点击效果完成
                setTimeout(() => {
                    closeSettingsPanel();
                }, 100);
            }
        });
    });

    // ESC键关闭弹窗
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape') {
            closeSettingsPanel();
        }
    });
}

// 全局暴露函数
window.openSettingsPanel = openSettingsPanel;
window.closeSettingsPanel = closeSettingsPanel;
