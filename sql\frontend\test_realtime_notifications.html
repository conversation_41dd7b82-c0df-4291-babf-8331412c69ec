<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="user-id" content="1">
    <title>实时通知测试页面</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 16px;
            padding: 32px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        }
        
        .header {
            text-align: center;
            margin-bottom: 32px;
        }
        
        .header h1 {
            color: #667eea;
            margin: 0 0 8px 0;
            font-size: 2rem;
            font-weight: 700;
        }
        
        .header p {
            color: #666;
            margin: 0;
            font-size: 1.1rem;
        }
        
        .test-section {
            background: #f8f9fa;
            border-radius: 12px;
            padding: 24px;
            margin-bottom: 24px;
            border-left: 4px solid #667eea;
        }
        
        .test-section h3 {
            margin: 0 0 16px 0;
            color: #333;
            font-size: 1.2rem;
        }
        
        .test-btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-right: 12px;
            margin-bottom: 8px;
        }
        
        .test-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(102, 126, 234, 0.3);
        }
        
        .status {
            margin-top: 16px;
            padding: 12px;
            border-radius: 8px;
            font-size: 14px;
        }
        
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .status.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        
        .log-section {
            background: #2d3748;
            color: #e2e8f0;
            border-radius: 12px;
            padding: 20px;
            margin-top: 24px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            max-height: 300px;
            overflow-y: auto;
        }
        
        .log-section h4 {
            margin: 0 0 12px 0;
            color: #a0aec0;
        }
        
        .log-entry {
            margin-bottom: 8px;
            padding: 4px 0;
            border-bottom: 1px solid #4a5568;
        }
        
        .log-time {
            color: #68d391;
            margin-right: 8px;
        }
        
        .instructions {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 8px;
            padding: 16px;
            margin-bottom: 24px;
        }
        
        .instructions h4 {
            margin: 0 0 12px 0;
            color: #856404;
        }
        
        .instructions ol {
            margin: 0;
            padding-left: 20px;
            color: #856404;
        }
        
        .instructions li {
            margin-bottom: 8px;
            line-height: 1.5;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔔 实时通知测试</h1>
            <p>测试前后台联动发送验证码功能</p>
        </div>
        
        <div class="instructions">
            <h4>📋 测试步骤</h4>
            <ol>
                <li>确保您已登录后台管理系统</li>
                <li>在后台用户详情页点击"发送验证码"按钮</li>
                <li>填写验证码信息并发送</li>
                <li>观察此页面是否实时收到验证码弹窗</li>
                <li>检查浏览器控制台的连接状态</li>
            </ol>
        </div>
        
        <div class="test-section">
            <h3>🔗 连接状态</h3>
            <button class="test-btn" onclick="testConnection()">测试连接</button>
            <button class="test-btn" onclick="reconnect()">重新连接</button>
            <button class="test-btn" onclick="disconnect()">断开连接</button>
            <div id="connectionStatus" class="status info">等待连接...</div>
        </div>
        
        <div class="test-section">
            <h3>🧪 模拟测试</h3>
            <button class="test-btn" onclick="simulateVerificationCode()">模拟验证码</button>
            <button class="test-btn" onclick="simulateSystemMessage()">模拟系统消息</button>
            <button class="test-btn" onclick="simulateError()">模拟错误</button>
            <div id="testStatus" class="status info">点击按钮进行测试</div>
        </div>
        
        <div class="log-section">
            <h4>📝 连接日志</h4>
            <div id="logContainer"></div>
        </div>
    </div>

    <!-- 实时通知组件 -->
    <script src="components/realtime_notifications.js"></script>
    <script>
        // 设置测试用户ID
        window.currentUserId = 1;
        
        let notifications = null;
        let logContainer = document.getElementById('logContainer');
        
        // 添加日志
        function addLog(message, type = 'info') {
            const time = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.className = 'log-entry';
            logEntry.innerHTML = `<span class="log-time">[${time}]</span> ${message}`;
            logContainer.appendChild(logEntry);
            logContainer.scrollTop = logContainer.scrollHeight;
            
            console.log(`[${time}] ${message}`);
        }
        
        // 更新连接状态
        function updateConnectionStatus(message, type) {
            const status = document.getElementById('connectionStatus');
            status.textContent = message;
            status.className = `status ${type}`;
        }
        
        // 测试连接
        function testConnection() {
            if (notifications && notifications.isConnected) {
                updateConnectionStatus('连接正常 ✅', 'success');
                addLog('连接测试：连接正常');
            } else {
                updateConnectionStatus('连接异常 ❌', 'error');
                addLog('连接测试：连接异常');
            }
        }
        
        // 重新连接
        function reconnect() {
            if (notifications) {
                notifications.disconnect();
            }
            
            setTimeout(() => {
                notifications = new RealtimeNotifications(1);
                addLog('手动重新连接');
                updateConnectionStatus('重新连接中...', 'info');
            }, 1000);
        }
        
        // 断开连接
        function disconnect() {
            if (notifications) {
                notifications.disconnect();
                updateConnectionStatus('已断开连接', 'error');
                addLog('手动断开连接');
            }
        }
        
        // 模拟验证码通知
        function simulateVerificationCode() {
            const mockData = {
                type: 'verification_code',
                id: Date.now(),
                title: '管理员验证码',
                content: '您收到一条来自管理员的验证码：123456',
                data: {
                    code: '123456',
                    expires_at: new Date(Date.now() + 5 * 60 * 1000).toISOString(),
                    admin_note: '这是一个测试验证码',
                    sent_by: '测试管理员'
                },
                priority: 5,
                timestamp: Date.now()
            };
            
            if (notifications) {
                notifications.showVerificationCodeModal(mockData);
                addLog('模拟验证码通知已发送');
                document.getElementById('testStatus').textContent = '验证码模拟完成 ✅';
                document.getElementById('testStatus').className = 'status success';
            } else {
                addLog('错误：通知组件未初始化');
                document.getElementById('testStatus').textContent = '错误：通知组件未初始化 ❌';
                document.getElementById('testStatus').className = 'status error';
            }
        }
        
        // 模拟系统消息
        function simulateSystemMessage() {
            const mockData = {
                type: 'system_message',
                title: '系统通知',
                content: '这是一条测试系统消息'
            };
            
            if (notifications) {
                notifications.showSystemMessage(mockData);
                addLog('模拟系统消息已发送');
                document.getElementById('testStatus').textContent = '系统消息模拟完成 ✅';
                document.getElementById('testStatus').className = 'status success';
            }
        }
        
        // 模拟错误
        function simulateError() {
            if (notifications) {
                notifications.showErrorNotification('这是一个测试错误消息');
                addLog('模拟错误消息已发送');
                document.getElementById('testStatus').textContent = '错误消息模拟完成 ✅';
                document.getElementById('testStatus').className = 'status success';
            }
        }
        
        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            addLog('页面加载完成，初始化实时通知组件...');
            
            // 等待组件加载
            setTimeout(() => {
                if (window.RealtimeNotifications) {
                    notifications = new RealtimeNotifications(1);
                    addLog('实时通知组件初始化成功');
                    updateConnectionStatus('正在连接...', 'info');
                    
                    // 监听连接状态变化
                    setTimeout(() => {
                        if (notifications.isConnected) {
                            updateConnectionStatus('连接成功 ✅', 'success');
                            addLog('SSE连接建立成功');
                        } else {
                            updateConnectionStatus('连接失败 ❌', 'error');
                            addLog('SSE连接建立失败');
                        }
                    }, 3000);
                } else {
                    addLog('错误：实时通知组件加载失败');
                    updateConnectionStatus('组件加载失败 ❌', 'error');
                }
            }, 1000);
        });
        
        // 页面卸载时断开连接
        window.addEventListener('beforeunload', function() {
            if (notifications) {
                notifications.disconnect();
            }
        });
    </script>
</body>
</html>
