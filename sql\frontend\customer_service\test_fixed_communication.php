<?php
// 测试修复后的客服通信系统
header('Content-Type: text/html; charset=UTF-8');
ini_set('default_charset', 'UTF-8');

session_start();

// 检查用户登录状态
if (!isset($_SESSION['user_id'])) {
    // 模拟登录用于测试
    $_SESSION['user_id'] = 4;
    $_SESSION['user_name'] = '测试用户';
    $_SESSION['nickname'] = '测试用户';
    $_SESSION['phone'] = '13800138000';
}

require_once '../../houtai_backup/db_config.php';

echo '<h1>🔧 测试修复后的客服通信系统</h1>';

try {
    $pdo = getDbConnection();
    
    // 检查或创建测试会话
    $sessionId = 'session_' . time() . '_test';
    
    $stmt = $pdo->prepare("
        INSERT INTO customer_service_sessions
        (session_id, user_id, user_phone, user_name, status, priority, source, started_at)
        VALUES (?, ?, ?, ?, 'waiting', 'normal', 'web', NOW())
    ");
    $stmt->execute([
        $sessionId,
        $_SESSION['user_id'],
        $_SESSION['phone'],
        $_SESSION['nickname']
    ]);
    
    echo '<div style="background: #d4edda; padding: 15px; border-radius: 8px; margin: 20px 0;">';
    echo '<h3>✅ 测试会话创建成功</h3>';
    echo '<p><strong>会话ID:</strong> ' . htmlspecialchars($sessionId) . '</p>';
    echo '<p><strong>用户ID:</strong> ' . $_SESSION['user_id'] . '</p>';
    echo '<p><strong>用户名:</strong> ' . htmlspecialchars($_SESSION['nickname']) . '</p>';
    echo '</div>';
    
    echo '<div style="background: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0;">';
    echo '<h3>🚀 测试步骤</h3>';
    echo '<ol>';
    echo '<li><strong>打开前台聊天页面</strong>：<br>';
    echo '<a href="chat_with_realtime.php?session_id=' . urlencode($sessionId) . '" target="_blank" style="background: #6F7BF5; color: white; padding: 8px 16px; text-decoration: none; border-radius: 5px;">🗨️ 打开聊天页面</a>';
    echo '</li><br>';
    
    echo '<li><strong>打开后台客服页面</strong>：<br>';
    echo '<a href="../../houtai_backup/customer_service_system/sessions.php" target="_blank" style="background: #28a745; color: white; padding: 8px 16px; text-decoration: none; border-radius: 5px;">👨‍💼 打开客服后台</a>';
    echo '</li><br>';
    
    echo '<li><strong>测试消息发送</strong>：<br>';
    echo '在前台发送消息，然后在后台回复，观察实时通信效果';
    echo '</li>';
    echo '</ol>';
    echo '</div>';
    
    echo '<div style="background: #fff3cd; padding: 15px; border-radius: 8px; margin: 20px 0;">';
    echo '<h3>⚠️ 关键修复点</h3>';
    echo '<ul>';
    echo '<li>✅ 移除了硬编码的用户ID，使用真实登录状态</li>';
    echo '<li>✅ 修复了实时通知组件的客服消息处理</li>';
    echo '<li>✅ 确保后台发送消息时创建正确的实时通知</li>';
    echo '<li>✅ 前台聊天页面正确接收和显示客服消息</li>';
    echo '<li>✅ 统一了数据库配置路径</li>';
    echo '</ul>';
    echo '</div>';
    
    echo '<div style="background: #d1ecf1; padding: 15px; border-radius: 8px; margin: 20px 0;">';
    echo '<h3>🔍 调试信息</h3>';
    echo '<p><strong>前台域名:</strong> planet.vancrest.xyz</p>';
    echo '<p><strong>后台域名:</strong> vansmrz.vancrest.xyz</p>';
    echo '<p><strong>数据库配置:</strong> houtai_backup/db_config.php</p>';
    echo '<p><strong>实时通知API:</strong> frontend/api/realtime_notifications.php</p>';
    echo '</div>';
    
} catch (Exception $e) {
    echo '<div style="background: #f8d7da; padding: 15px; border-radius: 8px; margin: 20px 0;">';
    echo '<h3>❌ 错误</h3>';
    echo '<p>' . htmlspecialchars($e->getMessage()) . '</p>';
    echo '</div>';
}
?>

<style>
body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    max-width: 800px;
    margin: 0 auto;
    padding: 20px;
    background: #f5f5f5;
}

h1 {
    color: #2d3748;
    text-align: center;
    margin-bottom: 30px;
}

a {
    display: inline-block;
    margin: 5px 0;
}

ol li {
    margin: 10px 0;
}

ul li {
    margin: 5px 0;
}
</style>

<p style="margin-top: 30px; text-align: center;">
    <a href="index.php">返回客服首页</a> | 
    <a href="../../houtai_backup/customer_service_system/sessions.php">客服后台</a>
</p>
