<?php
/**
 * 验证码登录API
 */

session_start();
header('Content-Type: application/json; charset=utf-8');

// 错误处理
error_reporting(0);
ini_set('display_errors', 0);

// 捕获所有错误并返回JSON
set_error_handler(function($severity, $message, $file, $line) {
    echo json_encode(['success' => false, 'message' => '系统错误，请稍后重试']);
    exit;
});

set_exception_handler(function($exception) {
    echo json_encode(['success' => false, 'message' => '系统错误，请稍后重试']);
    exit;
});

// 数据库连接配置
$db_config = [
    'host' => 'localhost',
    'username' => 'quwanplanet',
    'password' => 'nJmJm23FB4Xn6Fc3',
    'dbname' => 'quwanplanet',
    'port' => 3306,
    'charset' => 'utf8mb4'
];

try {
    $pdo = new PDO(
        "mysql:host={$db_config['host']};port={$db_config['port']};dbname={$db_config['dbname']};charset={$db_config['charset']}",
        $db_config['username'],
        $db_config['password'],
        [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
            PDO::ATTR_EMULATE_PREPARES => false
        ]
    );
} catch (PDOException $e) {
    echo json_encode(['success' => false, 'message' => '数据库连接失败']);
    exit;
}

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode(['success' => false, 'message' => '请求方法错误']);
    exit;
}

$input = json_decode(file_get_contents('php://input'), true);
$phone = trim($input['phone'] ?? '');
$verification_code = trim($input['verification_code'] ?? '');

if (empty($phone)) {
    echo json_encode(['success' => false, 'message' => '手机号不能为空']);
    exit;
}

if (empty($verification_code)) {
    echo json_encode(['success' => false, 'message' => '请输入验证码']);
    exit;
}

try {
    // 验证验证码（从session中获取）
    $session_code = $_SESSION['verification_code'] ?? '';
    $session_phone = $_SESSION['verification_phone'] ?? '';
    $session_time = $_SESSION['verification_time'] ?? 0;
    $session_user = $_SESSION['verification_user'] ?? null;

    // 检查验证码是否过期（5分钟有效期）
    if (time() - $session_time > 300) {
        echo json_encode(['success' => false, 'message' => '验证码已过期，请重新获取']);
        exit;
    }

    // 验证手机号是否匹配
    if ($phone !== $session_phone) {
        echo json_encode(['success' => false, 'message' => '手机号不匹配']);
        exit;
    }

    // 验证验证码
    if ($verification_code !== $session_code) {
        echo json_encode(['success' => false, 'message' => '验证码错误']);
        exit;
    }

    // 从数据库获取完整用户信息
    $stmt = $pdo->prepare("SELECT id, username, nickname, quwan_id, phone, status FROM users WHERE phone = ?");
    $stmt->execute([$phone]);
    $user = $stmt->fetch();

    if (!$user) {
        echo json_encode(['success' => false, 'message' => '用户不存在']);
        exit;
    }

    if ($user['status'] === 'banned') {
        echo json_encode(['success' => false, 'message' => '账户已被封禁']);
        exit;
    }

    // 清除验证码
    unset($_SESSION['verification_code']);
    unset($_SESSION['verification_phone']);
    unset($_SESSION['verification_time']);
    unset($_SESSION['verification_user']);

    // 设置登录会话
    $_SESSION['user_id'] = $user['id'];
    $_SESSION['username'] = $user['username'];
    $_SESSION['nickname'] = $user['nickname'];
    $_SESSION['quwan_id'] = $user['quwan_id'];
    $_SESSION['logged_in'] = true;

    // 更新最后登录时间
    $stmt = $pdo->prepare("UPDATE users SET last_login = NOW() WHERE id = ?");
    $stmt->execute([$user['id']]);

    // 记录登录日志
    require_once 'login_logger.php';
    recordUserLoginLog($pdo, $user['id'], 'verification_code', 'success');

    echo json_encode([
        'success' => true,
        'message' => '登录成功',
        'user' => [
            'id' => $user['id'],
            'username' => $user['username'],
            'nickname' => $user['nickname'],
            'quwan_id' => $user['quwan_id']
        ],
        'redirect' => '../home/<USER>'
    ]);

} catch (PDOException $e) {
    error_log("验证码登录失败: " . $e->getMessage());
    echo json_encode(['success' => false, 'message' => '登录失败，请稍后重试']);
}
?>
