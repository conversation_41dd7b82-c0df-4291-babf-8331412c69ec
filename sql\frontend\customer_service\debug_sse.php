<?php
// SSE调试页面
header('Content-Type: text/html; charset=UTF-8');
ini_set('default_charset', 'UTF-8');

session_start();

// 模拟用户登录
if (!isset($_SESSION['user_id'])) {
    $_SESSION['user_id'] = 4;
    $_SESSION['user_name'] = '测试用户';
}

$sessionId = $_GET['session_id'] ?? 'sse_test_' . time();
?>

<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>SSE调试页面</title>
    <style>
        body { 
            font-family: Arial, sans-serif; 
            margin: 20px; 
            background: #f5f5f5; 
        }
        .container { 
            max-width: 800px; 
            margin: 0 auto; 
            background: white; 
            padding: 20px; 
            border-radius: 10px; 
        }
        .status { 
            padding: 10px; 
            margin: 10px 0; 
            border-radius: 5px; 
            font-weight: bold; 
        }
        .connected { background: #d4edda; color: #155724; }
        .disconnected { background: #f8d7da; color: #721c24; }
        .connecting { background: #fff3cd; color: #856404; }
        .log { 
            background: #f8f9fa; 
            padding: 15px; 
            border-radius: 5px; 
            height: 400px; 
            overflow-y: auto; 
            font-family: monospace; 
            font-size: 12px; 
            border: 1px solid #ddd; 
        }
        .log-entry { 
            margin: 5px 0; 
            padding: 5px; 
            border-left: 3px solid #007cba; 
            background: white; 
        }
        .error { border-left-color: #dc3545; }
        .success { border-left-color: #28a745; }
        .info { border-left-color: #17a2b8; }
        button { 
            padding: 10px 20px; 
            margin: 5px; 
            border: none; 
            border-radius: 5px; 
            cursor: pointer; 
        }
        .btn-primary { background: #007cba; color: white; }
        .btn-success { background: #28a745; color: white; }
        .btn-danger { background: #dc3545; color: white; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 SSE连接调试</h1>
        
        <div class="status connecting" id="status">
            连接状态：连接中...
        </div>
        
        <div>
            <strong>会话ID：</strong><?php echo htmlspecialchars($sessionId); ?><br>
            <strong>用户ID：</strong><?php echo $_SESSION['user_id']; ?><br>
            <strong>用户名：</strong><?php echo $_SESSION['user_name']; ?>
        </div>
        
        <div style="margin: 20px 0;">
            <button class="btn-primary" onclick="connectSSE()">连接SSE</button>
            <button class="btn-danger" onclick="disconnectSSE()">断开连接</button>
            <button class="btn-success" onclick="testSimpleSSE()">测试简化SSE</button>
            <button onclick="clearLog()">清空日志</button>
        </div>
        
        <h3>连接日志</h3>
        <div class="log" id="log"></div>
        
        <div style="margin-top: 20px;">
            <h3>测试链接</h3>
            <p>
                <a href="api/sse_simple.php?session_id=<?php echo urlencode($sessionId); ?>&user_id=<?php echo $_SESSION['user_id']; ?>" target="_blank">直接访问简化SSE</a> |
                <a href="api/sse_messages.php?session_id=<?php echo urlencode($sessionId); ?>" target="_blank">直接访问完整SSE</a>
            </p>
        </div>
    </div>
    
    <script>
        const sessionId = '<?php echo $sessionId; ?>';
        const userId = <?php echo $_SESSION['user_id']; ?>;
        let eventSource = null;
        
        function log(message, type = 'info') {
            const logDiv = document.getElementById('log');
            const entry = document.createElement('div');
            entry.className = `log-entry ${type}`;
            entry.innerHTML = `[${new Date().toLocaleTimeString()}] ${message}`;
            logDiv.appendChild(entry);
            logDiv.scrollTop = logDiv.scrollHeight;
        }
        
        function updateStatus(status, className) {
            const statusDiv = document.getElementById('status');
            statusDiv.textContent = `连接状态：${status}`;
            statusDiv.className = `status ${className}`;
        }
        
        function connectSSE() {
            if (eventSource) {
                eventSource.close();
            }
            
            log('开始连接SSE...', 'info');
            updateStatus('连接中...', 'connecting');
            
            const url = `api/sse_messages.php?session_id=${sessionId}`;
            log(`连接URL: ${url}`, 'info');
            
            eventSource = new EventSource(url);
            
            eventSource.onopen = function(event) {
                log('SSE连接已打开', 'success');
                updateStatus('已连接', 'connected');
            };
            
            eventSource.onmessage = function(event) {
                try {
                    const data = JSON.parse(event.data);
                    log(`收到消息: ${JSON.stringify(data)}`, 'success');
                    
                    if (data.type === 'connected') {
                        updateStatus('已连接', 'connected');
                    }
                } catch (error) {
                    log(`解析消息失败: ${error.message}`, 'error');
                    log(`原始数据: ${event.data}`, 'error');
                }
            };
            
            eventSource.onerror = function(event) {
                log('SSE连接错误', 'error');
                log(`ReadyState: ${eventSource.readyState}`, 'error');
                updateStatus('连接错误', 'disconnected');
            };
        }
        
        function testSimpleSSE() {
            if (eventSource) {
                eventSource.close();
            }
            
            log('开始测试简化SSE...', 'info');
            updateStatus('连接中...', 'connecting');
            
            const url = `api/sse_simple.php?session_id=${sessionId}&user_id=${userId}`;
            log(`连接URL: ${url}`, 'info');
            
            eventSource = new EventSource(url);
            
            eventSource.onopen = function(event) {
                log('简化SSE连接已打开', 'success');
                updateStatus('已连接', 'connected');
            };
            
            eventSource.onmessage = function(event) {
                try {
                    const data = JSON.parse(event.data);
                    log(`收到消息: ${JSON.stringify(data)}`, 'success');
                } catch (error) {
                    log(`解析消息失败: ${error.message}`, 'error');
                    log(`原始数据: ${event.data}`, 'error');
                }
            };
            
            eventSource.onerror = function(event) {
                log('简化SSE连接错误', 'error');
                log(`ReadyState: ${eventSource.readyState}`, 'error');
                updateStatus('连接错误', 'disconnected');
            };
        }
        
        function disconnectSSE() {
            if (eventSource) {
                eventSource.close();
                eventSource = null;
                log('SSE连接已断开', 'info');
                updateStatus('已断开', 'disconnected');
            }
        }
        
        function clearLog() {
            document.getElementById('log').innerHTML = '';
        }
        
        // 页面加载时自动连接
        window.addEventListener('load', function() {
            log('页面加载完成', 'info');
            log(`会话ID: ${sessionId}`, 'info');
            log(`用户ID: ${userId}`, 'info');
        });
        
        // 页面卸载时断开连接
        window.addEventListener('beforeunload', function() {
            if (eventSource) {
                eventSource.close();
            }
        });
    </script>
</body>
</html>
