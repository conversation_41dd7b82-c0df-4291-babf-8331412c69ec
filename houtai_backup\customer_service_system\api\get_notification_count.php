<?php
// 获取通知数量API
header('Content-Type: application/json; charset=UTF-8');
ini_set('default_charset', 'UTF-8');

session_start();

// 检查登录状态
if (!isset($_SESSION['cs_logged_in']) || !$_SESSION['cs_logged_in']) {
    http_response_code(401);
    echo json_encode(['error' => '未登录']);
    exit;
}

// 引用数据库配置文件
require_once '../../db_config.php';

try {
    $pdo = getDbConnection();
    
    $notification_count = 0;
    
    // 统计待处理的会话（作为通知）
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM customer_service_sessions WHERE status = 'waiting'");
    $waiting_sessions = $stmt->fetch()['count'] ?? 0;
    $notification_count += $waiting_sessions;
    
    // 统计待质检的会话（仅对有权限的用户）
    if ($_SESSION['cs_role'] === 'super_admin') {
        $stmt = $pdo->query("SELECT COUNT(*) as count FROM customer_service_quality_checks WHERE status = 'pending'");
        $pending_quality_checks = $stmt->fetch()['count'] ?? 0;
        $notification_count += $pending_quality_checks;
    }
    
    // 统计分配给当前用户的活跃会话
    $stmt = $pdo->prepare("SELECT COUNT(*) as count FROM customer_service_sessions WHERE customer_service_id = ? AND status = 'active'");
    $stmt->execute([$_SESSION['cs_user_id']]);
    $my_active_sessions = $stmt->fetch()['count'] ?? 0;
    
    // 返回通知数量
    echo json_encode([
        'success' => true,
        'count' => $notification_count,
        'details' => [
            'waiting_sessions' => $waiting_sessions,
            'pending_quality_checks' => $_SESSION['cs_role'] === 'super_admin' ? ($pending_quality_checks ?? 0) : 0,
            'my_active_sessions' => $my_active_sessions
        ],
        'timestamp' => time()
    ]);
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => '获取通知数量失败',
        'message' => $e->getMessage()
    ]);
}
?>
