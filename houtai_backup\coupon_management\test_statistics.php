<?php
session_start();
require_once '../db_config.php';

// 检查管理员登录状态
if (!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
    header('Location: ../login.php');
    exit;
}

echo "<h1>优惠券统计功能测试</h1>";
echo "<style>
    table { border-collapse: collapse; margin: 20px 0; }
    th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
    th { background-color: #f2f2f2; }
    .success { color: green; font-weight: bold; }
    .error { color: red; font-weight: bold; }
    .warning { color: orange; font-weight: bold; }
</style>";

try {
    $pdo = getDbConnection();
    echo "<p class='success'>✅ 数据库连接成功！</p>";

    // 检查 camping_coupons 表是否存在
    echo "<h2>1. 检查优惠券表</h2>";
    $stmt = $pdo->query("SHOW TABLES LIKE 'camping_coupons'");
    if ($stmt->rowCount() > 0) {
        echo "<p class='success'>✅ camping_coupons 表存在</p>";

        // 检查表结构
        $stmt = $pdo->query("DESCRIBE camping_coupons");
        $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);

        echo "<table>";
        echo "<tr><th>字段名</th><th>类型</th><th>允许NULL</th><th>键</th><th>默认值</th><th>额外</th></tr>";

        $has_used_quantity = false;
        foreach ($columns as $column) {
            echo "<tr>";
            echo "<td>" . htmlspecialchars($column['Field']) . "</td>";
            echo "<td>" . htmlspecialchars($column['Type']) . "</td>";
            echo "<td>" . htmlspecialchars($column['Null']) . "</td>";
            echo "<td>" . htmlspecialchars($column['Key']) . "</td>";
            echo "<td>" . htmlspecialchars($column['Default'] ?? 'NULL') . "</td>";
            echo "<td>" . htmlspecialchars($column['Extra']) . "</td>";
            echo "</tr>";

            if ($column['Field'] === 'used_quantity') {
                $has_used_quantity = true;
            }
        }
        echo "</table>";

        if ($has_used_quantity) {
            echo "<p class='success'>✅ used_quantity 字段存在</p>";
        } else {
            echo "<p class='error'>❌ used_quantity 字段缺失</p>";
            echo "<p><strong>解决方案：</strong>执行 fix_used_quantity_field.sql 脚本</p>";
        }

        // 获取表数据统计
        $stmt = $pdo->query("SELECT COUNT(*) as count FROM camping_coupons");
        $coupon_count = $stmt->fetch()['count'];
        echo "<p>优惠券总数：{$coupon_count}</p>";

    } else {
        echo "<p class='error'>❌ camping_coupons 表不存在</p>";
        echo "<p><strong>解决方案：</strong>请先创建优惠券表</p>";
    }

    // 检查 user_camping_coupons 表
    echo "<h2>2. 检查用户优惠券表</h2>";
    $stmt = $pdo->query("SHOW TABLES LIKE 'user_camping_coupons'");
    if ($stmt->rowCount() > 0) {
        echo "<p class='success'>✅ user_camping_coupons 表存在</p>";

        $stmt = $pdo->query("SELECT COUNT(*) as count FROM user_camping_coupons");
        $user_coupon_count = $stmt->fetch()['count'];
        echo "<p>用户优惠券总数：{$user_coupon_count}</p>";

        $stmt = $pdo->query("SELECT COUNT(*) as count FROM user_camping_coupons WHERE status = 'used'");
        $used_count = $stmt->fetch()['count'];
        echo "<p>已使用优惠券数：{$used_count}</p>";

    } else {
        echo "<p class='error'>❌ user_camping_coupons 表不存在</p>";
    }

    // 测试统计查询
    echo "<h2>3. 测试统计查询</h2>";

    try {
        // 测试总体统计查询
        if ($has_used_quantity) {
            $stmt = $pdo->prepare("
                SELECT
                    COUNT(*) as total_coupons,
                    SUM(CASE WHEN status = 'active' THEN 1 ELSE 0 END) as active_coupons,
                    SUM(total_quantity) as total_issued,
                    SUM(claimed_quantity) as total_claimed,
                    SUM(COALESCE(used_quantity, 0)) as total_used
                FROM camping_coupons
            ");
        } else {
            $stmt = $pdo->prepare("
                SELECT
                    COUNT(*) as total_coupons,
                    SUM(CASE WHEN status = 'active' THEN 1 ELSE 0 END) as active_coupons,
                    SUM(total_quantity) as total_issued,
                    SUM(claimed_quantity) as total_claimed,
                    (SELECT COUNT(*) FROM user_camping_coupons WHERE status = 'used') as total_used
                FROM camping_coupons
            ");
        }

        $stmt->execute();
        $stats = $stmt->fetch(PDO::FETCH_ASSOC);

        echo "<p class='success'>✅ 总体统计查询成功</p>";
        echo "<table>";
        echo "<tr><th>指标</th><th>数值</th></tr>";
        echo "<tr><td>总优惠券数</td><td>" . number_format($stats['total_coupons']) . "</td></tr>";
        echo "<tr><td>活跃优惠券</td><td>" . number_format($stats['active_coupons']) . "</td></tr>";
        echo "<tr><td>总发放量</td><td>" . number_format($stats['total_issued']) . "</td></tr>";
        echo "<tr><td>已领取量</td><td>" . number_format($stats['total_claimed']) . "</td></tr>";
        echo "<tr><td>已使用量</td><td>" . number_format($stats['total_used']) . "</td></tr>";
        echo "</table>";

    } catch (PDOException $e) {
        echo "<p class='error'>❌ 统计查询失败：" . htmlspecialchars($e->getMessage()) . "</p>";
    }

    // 测试按类型统计
    try {
        if ($has_used_quantity) {
            $stmt = $pdo->prepare("
                SELECT
                    type,
                    COUNT(*) as coupon_count,
                    SUM(total_quantity) as total_issued,
                    SUM(claimed_quantity) as total_claimed,
                    SUM(COALESCE(used_quantity, 0)) as total_used
                FROM camping_coupons
                GROUP BY type
                ORDER BY total_issued DESC
            ");
        } else {
            $stmt = $pdo->prepare("
                SELECT
                    cc.type,
                    COUNT(*) as coupon_count,
                    SUM(cc.total_quantity) as total_issued,
                    SUM(cc.claimed_quantity) as total_claimed,
                    COALESCE(used_stats.total_used, 0) as total_used
                FROM camping_coupons cc
                LEFT JOIN (
                    SELECT
                        cc2.type,
                        COUNT(*) as total_used
                    FROM user_camping_coupons ucc2
                    JOIN camping_coupons cc2 ON ucc2.coupon_id = cc2.id
                    WHERE ucc2.status = 'used'
                    GROUP BY cc2.type
                ) used_stats ON cc.type = used_stats.type
                GROUP BY cc.type
                ORDER BY total_issued DESC
            ");
        }

        $stmt->execute();
        $type_stats = $stmt->fetchAll(PDO::FETCH_ASSOC);

        echo "<p class='success'>✅ 按类型统计查询成功</p>";

        if (!empty($type_stats)) {
            echo "<table>";
            echo "<tr><th>类型</th><th>数量</th><th>总发放</th><th>已领取</th><th>已使用</th></tr>";
            foreach ($type_stats as $stat) {
                echo "<tr>";
                echo "<td>" . htmlspecialchars($stat['type']) . "</td>";
                echo "<td>" . number_format($stat['coupon_count']) . "</td>";
                echo "<td>" . number_format($stat['total_issued']) . "</td>";
                echo "<td>" . number_format($stat['total_claimed']) . "</td>";
                echo "<td>" . number_format($stat['total_used']) . "</td>";
                echo "</tr>";
            }
            echo "</table>";
        } else {
            echo "<p class='warning'>⚠️ 暂无类型统计数据</p>";
        }

    } catch (PDOException $e) {
        echo "<p class='error'>❌ 按类型统计查询失败：" . htmlspecialchars($e->getMessage()) . "</p>";
    }

    // 测试用户统计查询
    echo "<h2>4. 测试用户统计查询</h2>";

    // 检查用户表字段
    $stmt = $pdo->query("SHOW COLUMNS FROM users");
    $user_columns = $stmt->fetchAll(PDO::FETCH_COLUMN);
    $has_quwanplanet_id = in_array('quwanplanet_id', $user_columns);
    $has_quwan_id = in_array('quwan_id', $user_columns);

    echo "<p>用户表字段检查：</p>";
    echo "<ul>";
    echo "<li>" . ($has_quwanplanet_id ? "<span class='success'>✅ quwanplanet_id 字段存在</span>" : "<span class='error'>❌ quwanplanet_id 字段缺失</span>") . "</li>";
    echo "<li>" . ($has_quwan_id ? "<span class='success'>✅ quwan_id 字段存在</span>" : "<span class='error'>❌ quwan_id 字段缺失</span>") . "</li>";
    echo "</ul>";

    // 根据字段存在情况选择查询
    $user_id_field = 'u.id';
    if ($has_quwanplanet_id) {
        $user_id_field = 'u.quwanplanet_id';
    } elseif ($has_quwan_id) {
        $user_id_field = 'u.quwan_id';
    }

    try {
        $stmt = $pdo->prepare("
            SELECT
                u.id,
                u.username,
                {$user_id_field} as quwanplanet_id,
                COUNT(ucc.id) as total_claimed,
                COUNT(CASE WHEN ucc.status = 'used' THEN 1 END) as total_used
            FROM users u
            LEFT JOIN user_camping_coupons ucc ON u.id = ucc.user_id
            LEFT JOIN camping_coupons cc ON ucc.coupon_id = cc.id
            WHERE ucc.id IS NOT NULL
            GROUP BY u.id
            ORDER BY total_claimed DESC
            LIMIT 5
        ");
        $stmt->execute();
        $user_stats = $stmt->fetchAll(PDO::FETCH_ASSOC);

        echo "<p class='success'>✅ 用户统计查询成功</p>";

        if (!empty($user_stats)) {
            echo "<table>";
            echo "<tr><th>用户名</th><th>用户ID</th><th>总领取</th><th>已使用</th></tr>";
            foreach ($user_stats as $stat) {
                echo "<tr>";
                echo "<td>" . htmlspecialchars($stat['username']) . "</td>";
                echo "<td>" . htmlspecialchars($stat['quwanplanet_id'] ?? '无') . "</td>";
                echo "<td>" . number_format($stat['total_claimed']) . "</td>";
                echo "<td>" . number_format($stat['total_used']) . "</td>";
                echo "</tr>";
            }
            echo "</table>";
        } else {
            echo "<p class='warning'>⚠️ 暂无用户统计数据</p>";
        }

    } catch (PDOException $e) {
        echo "<p class='error'>❌ 用户统计查询失败：" . htmlspecialchars($e->getMessage()) . "</p>";
    }

    // 检查触发器
    echo "<h2>4. 检查触发器</h2>";
    $stmt = $pdo->query("SHOW TRIGGERS LIKE 'update_coupon_used_quantity%'");
    $triggers = $stmt->fetchAll(PDO::FETCH_ASSOC);

    if (!empty($triggers)) {
        echo "<p class='success'>✅ 找到 " . count($triggers) . " 个相关触发器</p>";
        echo "<table>";
        echo "<tr><th>触发器名</th><th>事件</th><th>表</th><th>时机</th></tr>";
        foreach ($triggers as $trigger) {
            echo "<tr>";
            echo "<td>" . htmlspecialchars($trigger['Trigger']) . "</td>";
            echo "<td>" . htmlspecialchars($trigger['Event']) . "</td>";
            echo "<td>" . htmlspecialchars($trigger['Table']) . "</td>";
            echo "<td>" . htmlspecialchars($trigger['Timing']) . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p class='warning'>⚠️ 未找到自动更新触发器</p>";
    }

    // 修复建议
    echo "<h2>5. 修复建议</h2>";

    if (!$has_used_quantity) {
        echo "<div style='background: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 5px;'>";
        echo "<h3>🔧 需要执行修复脚本</h3>";
        echo "<p>请按照以下步骤修复：</p>";
        echo "<ol>";
        echo "<li>登录宝塔面板或phpMyAdmin</li>";
        echo "<li>选择数据库: <code>quwanplanet</code></li>";
        echo "<li>执行 <code>fix_used_quantity_field.sql</code> 脚本</li>";
        echo "<li>刷新此页面验证修复结果</li>";
        echo "<li>访问统计分析页面测试功能</li>";
        echo "</ol>";
        echo "</div>";
    } else {
        echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; padding: 15px; border-radius: 5px;'>";
        echo "<h3>✅ 数据库结构正常</h3>";
        echo "<p>所有必需的字段都已存在，统计分析功能应该可以正常使用。</p>";
        echo "<p><a href='statistics.php'>访问统计分析页面</a></p>";
        echo "</div>";
    }

} catch (PDOException $e) {
    echo "<p class='error'>❌ 数据库错误：" . htmlspecialchars($e->getMessage()) . "</p>";
}

echo "<br><a href='index.php'>返回优惠券管理</a>";
?>
