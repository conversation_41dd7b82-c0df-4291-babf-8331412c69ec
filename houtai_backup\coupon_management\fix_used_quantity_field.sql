-- =====================================================
-- 修复优惠券表缺失的 used_quantity 字段
-- =====================================================

-- 检查并添加 used_quantity 字段到 camping_coupons 表
ALTER TABLE camping_coupons 
ADD COLUMN IF NOT EXISTS used_quantity INT(11) DEFAULT 0 COMMENT '已使用数量';

-- 创建触发器自动更新 used_quantity 字段
DELIMITER $$

-- 删除已存在的触发器（如果有）
DROP TRIGGER IF EXISTS update_coupon_used_quantity_insert$$
DROP TRIGGER IF EXISTS update_coupon_used_quantity_update$$
DROP TRIGGER IF EXISTS update_coupon_used_quantity_delete$$

-- 创建插入触发器
CREATE TRIGGER update_coupon_used_quantity_insert
AFTER INSERT ON user_camping_coupons
FOR EACH ROW
BEGIN
    IF NEW.status = 'used' THEN
        UPDATE camping_coupons 
        SET used_quantity = (
            SELECT COUNT(*) 
            FROM user_camping_coupons 
            WHERE coupon_id = NEW.coupon_id AND status = 'used'
        )
        WHERE id = NEW.coupon_id;
    END IF;
END$$

-- 创建更新触发器
CREATE TRIGGER update_coupon_used_quantity_update
AFTER UPDATE ON user_camping_coupons
FOR EACH ROW
BEGIN
    -- 如果状态发生变化
    IF OLD.status != NEW.status THEN
        UPDATE camping_coupons 
        SET used_quantity = (
            SELECT COUNT(*) 
            FROM user_camping_coupons 
            WHERE coupon_id = NEW.coupon_id AND status = 'used'
        )
        WHERE id = NEW.coupon_id;
    END IF;
END$$

-- 创建删除触发器
CREATE TRIGGER update_coupon_used_quantity_delete
AFTER DELETE ON user_camping_coupons
FOR EACH ROW
BEGIN
    IF OLD.status = 'used' THEN
        UPDATE camping_coupons 
        SET used_quantity = (
            SELECT COUNT(*) 
            FROM user_camping_coupons 
            WHERE coupon_id = OLD.coupon_id AND status = 'used'
        )
        WHERE id = OLD.coupon_id;
    END IF;
END$$

DELIMITER ;

-- 初始化现有数据的 used_quantity 字段
UPDATE camping_coupons cc
SET used_quantity = (
    SELECT COUNT(*)
    FROM user_camping_coupons ucc
    WHERE ucc.coupon_id = cc.id AND ucc.status = 'used'
);

-- 验证修复结果
SELECT 
    'used_quantity field added and initialized successfully!' as message,
    COUNT(*) as total_coupons,
    SUM(used_quantity) as total_used
FROM camping_coupons;
