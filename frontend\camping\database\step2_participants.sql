-- 第二步：创建活动参与记录表
-- 请在宝塔数据库中执行以下SQL语句

-- 2. 活动参与记录表
CREATE TABLE IF NOT EXISTS `camping_participants` (
    `id` INT(11) NOT NULL AUTO_INCREMENT COMMENT '记录ID',
    `activity_id` INT(11) NOT NULL COMMENT '活动ID',
    `user_id` INT(11) NOT NULL COMMENT '参与者用户ID',
    `join_time` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '参与时间',
    `status` ENUM('joined', 'cancelled', 'completed') NOT NULL DEFAULT 'joined' COMMENT '参与状态',
    `payment_status` ENUM('unpaid', 'paid', 'refunded') NOT NULL DEFAULT 'unpaid' COMMENT '支付状态',
    `coupon_used` INT(11) DEFAULT NULL COMMENT '使用的优惠券ID',
    `actual_price` DECIMAL(10,2) NOT NULL DEFAULT 0.00 COMMENT '实际支付金额',
    `notes` TEXT COMMENT '备注信息',
    PRIMARY KEY (`id`),
    UNIQUE KEY `unique_participation` (`activity_id`, `user_id`),
    KEY `idx_user` (`user_id`),
    KEY `idx_activity` (`activity_id`),
    KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='活动参与记录表';
