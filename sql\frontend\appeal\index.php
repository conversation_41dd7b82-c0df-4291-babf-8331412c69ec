<?php
// 引入数据库配置
require_once '../../sql/db_config.php';

// 检查申诉状态
$appeal_status = null;
$phone = $_GET['phone'] ?? '';

if ($phone) {
    try {
        $pdo = getDbConnection();

        // 简化逻辑：直接查询该手机号的最新申诉记录
        $stmt = $pdo->prepare("
            SELECT status, created_at, admin_reply, updated_at, appeal_type, reason
            FROM user_appeals
            WHERE phone = ?
            ORDER BY created_at DESC
            LIMIT 1
        ");
        $stmt->execute([$phone]);
        $appeal_status = $stmt->fetch(PDO::FETCH_ASSOC);

        // 如果有申诉记录，检查状态
        if ($appeal_status) {
            // 如果申诉已被拒绝，允许重新申诉
            if ($appeal_status['status'] === 'rejected') {
                // 检查是否在拒绝后又提交了新的申诉
                $stmt = $pdo->prepare("
                    SELECT COUNT(*) as count
                    FROM user_appeals
                    WHERE phone = ? AND created_at > ?
                ");
                $stmt->execute([$phone, $appeal_status['updated_at']]);
                $newer_appeals = $stmt->fetchColumn();

                // 如果没有更新的申诉，可以重新申诉
                if ($newer_appeals == 0) {
                    $appeal_status = null; // 允许显示申诉表单
                }
            }
            // 如果申诉已通过，也允许查看状态
            // 如果申诉待处理或处理中，显示状态页面
        }

        // 获取用户封号信息（用于显示）
        $user_ban_info = null;
        try {
            $stmt = $pdo->prepare("
                SELECT u.id as user_id, u.phone, u.status,
                       ub.reason as ban_reason, ub.admin_name as ban_admin,
                       ub.created_at as ban_time
                FROM users u
                LEFT JOIN user_bans ub ON u.id = ub.user_id AND ub.status = 'active'
                WHERE u.phone = ?
                ORDER BY ub.created_at DESC
                LIMIT 1
            ");
            $stmt->execute([$phone]);
            $user_ban_info = $stmt->fetch(PDO::FETCH_ASSOC);
        } catch (Exception $e) {
            // 如果查询失败，继续执行
            error_log("查询用户封号信息失败: " . $e->getMessage());
        }

    } catch (Exception $e) {
        // 如果表不存在或查询失败，继续显示申诉表单
        error_log("查询申诉状态失败: " . $e->getMessage());
    }
}
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>账号申诉 - 趣玩星球</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .appeal-container {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            border-radius: 16px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }

        .appeal-header {
            background: linear-gradient(135deg, #6F7BF5, #9C88FF);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .appeal-header h1 {
            font-size: 24px;
            margin-bottom: 8px;
        }

        .appeal-header p {
            opacity: 0.9;
            font-size: 14px;
        }

        .appeal-content {
            padding: 30px;
        }

        .form-group {
            margin-bottom: 24px;
        }

        .form-label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #374151;
        }

        .form-control {
            width: 100%;
            padding: 12px 16px;
            border: 2px solid #E5E7EB;
            border-radius: 8px;
            font-size: 16px;
            transition: all 0.2s;
        }

        .form-control:focus {
            outline: none;
            border-color: #6F7BF5;
            box-shadow: 0 0 0 3px rgba(111, 123, 245, 0.1);
        }

        textarea.form-control {
            resize: vertical;
            min-height: 120px;
        }

        .file-upload {
            position: relative;
            display: inline-block;
            width: 100%;
        }

        .file-upload input[type="file"] {
            position: absolute;
            opacity: 0;
            width: 100%;
            height: 100%;
            cursor: pointer;
        }

        .file-upload-label {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
            padding: 12px 16px;
            border: 2px dashed #D1D5DB;
            border-radius: 8px;
            color: #6B7280;
            cursor: pointer;
            transition: all 0.2s;
        }

        .file-upload-label:hover {
            border-color: #6F7BF5;
            color: #6F7BF5;
        }

        .file-list {
            margin-top: 12px;
        }

        .file-item {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 8px 12px;
            background: #F3F4F6;
            border-radius: 6px;
            margin-bottom: 8px;
        }

        .file-name {
            font-size: 14px;
            color: #374151;
        }

        .file-remove {
            background: none;
            border: none;
            color: #EF4444;
            cursor: pointer;
            padding: 4px;
        }

        .btn {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s;
            text-decoration: none;
        }

        .btn-primary {
            background: linear-gradient(135deg, #6F7BF5, #9C88FF);
            color: white;
        }

        .btn-primary:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(111, 123, 245, 0.3);
        }

        .btn-secondary {
            background: #6B7280;
            color: white;
        }

        .btn-secondary:hover {
            background: #4B5563;
        }

        .btn-group {
            display: flex;
            gap: 12px;
            justify-content: center;
            margin-top: 30px;
        }

        .info-card {
            background: #EFF6FF;
            border: 1px solid #DBEAFE;
            border-radius: 8px;
            padding: 16px;
            margin-bottom: 24px;
        }

        .info-card h3 {
            color: #1E40AF;
            margin-bottom: 8px;
            font-size: 16px;
        }

        .info-card p {
            color: #1E3A8A;
            font-size: 14px;
            line-height: 1.5;
        }

        .toast {
            position: fixed;
            top: 20px;
            right: 20px;
            background: white;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            padding: 16px 20px;
            z-index: 1000;
            transform: translateX(100%);
            transition: transform 0.3s ease;
        }

        .toast.show {
            transform: translateX(0);
        }

        .toast.success {
            border-left: 4px solid #10B981;
        }

        .toast.error {
            border-left: 4px solid #EF4444;
        }

        /* 申诉状态样式 */
        .status-container {
            text-align: center;
            padding: 40px 30px;
        }

        .status-icon {
            width: 80px;
            height: 80px;
            margin: 0 auto 20px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 36px;
            color: white;
        }

        .status-icon.pending {
            background: linear-gradient(135deg, #F59E0B, #FBBF24);
        }

        .status-icon.approved {
            background: linear-gradient(135deg, #10B981, #34D399);
        }

        .status-icon.rejected {
            background: linear-gradient(135deg, #EF4444, #F87171);
        }

        .status-title {
            font-size: 24px;
            font-weight: 700;
            margin-bottom: 12px;
            color: #1F2937;
        }

        .status-description {
            font-size: 16px;
            color: #6B7280;
            margin-bottom: 24px;
            line-height: 1.6;
        }

        .status-details {
            background: #F9FAFB;
            border-radius: 12px;
            padding: 20px;
            margin: 24px 0;
            text-align: left;
        }

        .status-details h4 {
            color: #374151;
            margin-bottom: 12px;
            font-size: 16px;
        }

        .status-details p {
            color: #6B7280;
            font-size: 14px;
            line-height: 1.5;
            margin-bottom: 8px;
        }

        .progress-steps {
            display: flex;
            justify-content: center;
            align-items: center;
            margin: 30px 0;
            gap: 20px;
        }

        .progress-step {
            display: flex;
            flex-direction: column;
            align-items: center;
            position: relative;
        }

        .progress-step-icon {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 16px;
            margin-bottom: 8px;
            transition: all 0.3s ease;
        }

        .progress-step.completed .progress-step-icon {
            background: #10B981;
            color: white;
        }

        .progress-step.active .progress-step-icon {
            background: #6F7BF5;
            color: white;
        }

        .progress-step.pending .progress-step-icon {
            background: #E5E7EB;
            color: #9CA3AF;
        }

        .progress-step-text {
            font-size: 12px;
            color: #6B7280;
            text-align: center;
        }

        .progress-line {
            width: 60px;
            height: 2px;
            background: #E5E7EB;
            position: relative;
            top: -20px;
        }

        .progress-line.completed {
            background: #10B981;
        }

        /* 申诉详情样式 */
        .detail-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 16px;
            margin-top: 16px;
        }

        .detail-item {
            display: flex;
            flex-direction: column;
            gap: 4px;
        }

        .detail-item.full-width {
            grid-column: 1 / -1;
        }

        .detail-label {
            font-size: 14px;
            font-weight: 600;
            color: #6B7280;
        }

        .detail-value {
            font-size: 16px;
            color: #1F2937;
        }

        .reason-text, .reply-text {
            background: #F9FAFB;
            border: 1px solid #E5E7EB;
            border-radius: 8px;
            padding: 12px;
            line-height: 1.6;
            margin-top: 8px;
        }

        .reply-text {
            background: #EFF6FF;
            border-color: #DBEAFE;
        }

        @media (max-width: 768px) {
            .detail-grid {
                grid-template-columns: 1fr;
            }
            .appeal-container {
                margin: 10px;
                border-radius: 12px;
            }

            .appeal-header {
                padding: 20px;
            }

            .appeal-content {
                padding: 20px;
            }

            .btn-group {
                flex-direction: column;
            }
        }
    </style>
</head>
<body>
    <div class="appeal-container">
        <div class="appeal-header">
            <h1><i class="fas fa-file-alt"></i> 账号申诉</h1>
            <p>请详细描述您的情况，我们将在3-5个工作日内处理</p>
        </div>

        <div class="appeal-content">
            <?php if ($appeal_status): ?>
                <!-- 显示申诉状态 -->
                <div class="status-container">
                    <?php
                    $status = $appeal_status['status'];
                    $statusConfig = [
                        'pending' => [
                            'icon' => 'fas fa-clock',
                            'title' => '申诉审核中',
                            'description' => '您的申诉已提交成功，我们正在审核中，请耐心等待。'
                        ],
                        'approved' => [
                            'icon' => 'fas fa-check',
                            'title' => '申诉通过',
                            'description' => '恭喜！您的申诉已通过审核，账号已解封。'
                        ],
                        'rejected' => [
                            'icon' => 'fas fa-times',
                            'title' => '申诉被驳回',
                            'description' => '很抱歉，您的申诉未通过审核。'
                        ]
                    ];

                    $config = $statusConfig[$status] ?? $statusConfig['pending'];
                    ?>

                    <div class="status-icon <?php echo $status; ?>">
                        <i class="<?php echo $config['icon']; ?>"></i>
                    </div>

                    <h2 class="status-title"><?php echo $config['title']; ?></h2>
                    <p class="status-description"><?php echo $config['description']; ?></p>

                    <!-- 进度条 -->
                    <div class="progress-steps">
                        <div class="progress-step completed">
                            <div class="progress-step-icon">
                                <i class="fas fa-paper-plane"></i>
                            </div>
                            <div class="progress-step-text">提交申诉</div>
                        </div>

                        <div class="progress-line completed"></div>

                        <div class="progress-step <?php echo $status === 'pending' ? 'active' : 'completed'; ?>">
                            <div class="progress-step-icon">
                                <i class="fas fa-search"></i>
                            </div>
                            <div class="progress-step-text">审核中</div>
                        </div>

                        <div class="progress-line <?php echo $status !== 'pending' ? 'completed' : ''; ?>"></div>

                        <div class="progress-step <?php echo $status !== 'pending' ? 'completed' : 'pending'; ?>">
                            <div class="progress-step-icon">
                                <i class="fas fa-flag-checkered"></i>
                            </div>
                            <div class="progress-step-text">审核完成</div>
                        </div>
                    </div>

                    <!-- 申诉详情 -->
                    <div class="status-details">
                        <h4><i class="fas fa-info-circle"></i> 申诉详情</h4>
                        <div class="detail-grid">
                            <div class="detail-item">
                                <span class="detail-label">申诉类型：</span>
                                <span class="detail-value">
                                    <?php
                                    $type_map = [
                                        'wrongful_ban' => '误封申诉',
                                        'account_stolen' => '账号被盗',
                                        'system_error' => '系统错误',
                                        'other' => '其他原因'
                                    ];
                                    echo $type_map[$appeal_status['appeal_type']] ?? $appeal_status['appeal_type'];
                                    ?>
                                </span>
                            </div>
                            <div class="detail-item">
                                <span class="detail-label">提交时间：</span>
                                <span class="detail-value"><?php echo date('Y-m-d H:i:s', strtotime($appeal_status['created_at'])); ?></span>
                            </div>
                            <?php if ($appeal_status['updated_at'] && $appeal_status['updated_at'] !== $appeal_status['created_at']): ?>
                                <div class="detail-item">
                                    <span class="detail-label">处理时间：</span>
                                    <span class="detail-value"><?php echo date('Y-m-d H:i:s', strtotime($appeal_status['updated_at'])); ?></span>
                                </div>
                            <?php endif; ?>
                            <div class="detail-item full-width">
                                <span class="detail-label">申诉原因：</span>
                                <div class="detail-value reason-text">
                                    <?php echo nl2br(htmlspecialchars($appeal_status['reason'])); ?>
                                </div>
                            </div>
                            <?php if ($appeal_status['admin_reply']): ?>
                                <div class="detail-item full-width">
                                    <span class="detail-label">处理结果：</span>
                                    <div class="detail-value reply-text">
                                        <?php echo nl2br(htmlspecialchars($appeal_status['admin_reply'])); ?>
                                    </div>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>

                    <div class="btn-group">
                        <button type="button" class="btn btn-secondary" onclick="goBack()">
                            <i class="fas fa-arrow-left"></i>
                            返回
                        </button>
                        <?php if ($status === 'rejected'): ?>
                            <button type="button" class="btn btn-primary" onclick="showNewAppealForm()">
                                <i class="fas fa-redo"></i>
                                重新申诉
                            </button>
                        <?php elseif ($status === 'approved'): ?>
                            <a href="../login/index.php" class="btn btn-primary">
                                <i class="fas fa-sign-in-alt"></i>
                                立即登录
                            </a>
                        <?php else: ?>
                            <button type="button" class="btn btn-primary" onclick="refreshStatus()">
                                <i class="fas fa-sync-alt"></i>
                                刷新状态
                            </button>
                        <?php endif; ?>
                    </div>
                </div>
            <?php else: ?>
                <!-- 显示申诉表单 -->
                <div class="info-card">
                    <h3><i class="fas fa-info-circle"></i> 申诉须知</h3>
                    <p>1. 请如实填写申诉信息，虚假信息将影响申诉结果<br>
                    2. 可上传相关证明材料（图片格式，最多3张）<br>
                    3. 申诉提交后无法修改，请仔细核对<br>
                    4. 审核时间为3-5个工作日，请耐心等待</p>
                </div>

            <form id="appealForm">
                <div class="form-group">
                    <label class="form-label">手机号</label>
                    <input type="tel" class="form-control" id="phone" placeholder="请输入被封禁的手机号" required>
                </div>

                <div class="form-group">
                    <label class="form-label">申诉类型</label>
                    <select class="form-control" id="appealType" required>
                        <option value="">请选择申诉类型</option>
                        <option value="wrongful_ban">误封申诉</option>
                        <option value="account_stolen">账号被盗</option>
                        <option value="system_error">系统错误</option>
                        <option value="other">其他原因</option>
                    </select>
                </div>

                <div class="form-group">
                    <label class="form-label">申诉原因</label>
                    <textarea class="form-control" id="reason" placeholder="请详细描述您的申诉原因..." required></textarea>
                </div>

                <div class="form-group">
                    <label class="form-label">证明材料（可选）</label>
                    <div class="file-upload">
                        <input type="file" id="evidence" accept="image/*" multiple>
                        <label for="evidence" class="file-upload-label">
                            <i class="fas fa-cloud-upload-alt"></i>
                            点击上传图片（最多3张）
                        </label>
                    </div>
                    <div class="file-list" id="fileList"></div>
                </div>

                <div class="form-group">
                    <label class="form-label">联系邮箱</label>
                    <input type="email" class="form-control" id="email" placeholder="用于接收申诉结果通知" required>
                </div>

                <div class="btn-group">
                    <button type="button" class="btn btn-secondary" onclick="goBack()">
                        <i class="fas fa-arrow-left"></i>
                        返回
                    </button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-paper-plane"></i>
                        提交申诉
                    </button>
                </div>
            </form>
            <?php endif; ?>
        </div>
    </div>

    <script>
        let selectedFiles = [];

        document.addEventListener('DOMContentLoaded', function() {
            // 文件上传处理
            document.getElementById('evidence').addEventListener('change', handleFileSelect);

            // 表单提交处理
            document.getElementById('appealForm').addEventListener('submit', handleSubmit);
        });

        function handleFileSelect(event) {
            const files = Array.from(event.target.files);

            // 限制最多3张图片
            if (selectedFiles.length + files.length > 3) {
                showToast('最多只能上传3张图片', 'error');
                return;
            }

            files.forEach(file => {
                if (file.type.startsWith('image/')) {
                    selectedFiles.push(file);
                    addFileToList(file);
                } else {
                    showToast('只能上传图片文件', 'error');
                }
            });

            // 清空input
            event.target.value = '';
        }

        function addFileToList(file) {
            const fileList = document.getElementById('fileList');
            const fileItem = document.createElement('div');
            fileItem.className = 'file-item';
            fileItem.innerHTML = `
                <span class="file-name">${file.name}</span>
                <button type="button" class="file-remove" onclick="removeFile('${file.name}')">
                    <i class="fas fa-times"></i>
                </button>
            `;
            fileList.appendChild(fileItem);
        }

        function removeFile(fileName) {
            selectedFiles = selectedFiles.filter(file => file.name !== fileName);

            // 重新渲染文件列表
            const fileList = document.getElementById('fileList');
            fileList.innerHTML = '';
            selectedFiles.forEach(file => addFileToList(file));
        }

        function handleSubmit(event) {
            event.preventDefault();

            const formData = new FormData();
            formData.append('phone', document.getElementById('phone').value);
            formData.append('appeal_type', document.getElementById('appealType').value);
            formData.append('reason', document.getElementById('reason').value);
            formData.append('email', document.getElementById('email').value);

            // 添加文件
            selectedFiles.forEach((file, index) => {
                formData.append(`evidence_${index}`, file);
            });

            // 提交申诉
            fetch('submit_appeal.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showToast('申诉提交成功！我们将在3-5个工作日内处理', 'success');
                    setTimeout(() => {
                        // 跳转到申诉状态页面
                        const phone = document.getElementById('phone').value;
                        window.location.href = `index.php?phone=${encodeURIComponent(phone)}`;
                    }, 2000);
                } else {
                    showToast(data.message || '提交失败，请重试', 'error');
                }
            })
            .catch(error => {
                console.error('提交失败:', error);
                showToast('网络错误，请重试', 'error');
            });
        }

        function goBack() {
            window.history.back();
        }

        // 刷新申诉状态
        function refreshStatus() {
            location.reload();
        }

        // 显示新申诉表单（重新申诉）
        function showNewAppealForm() {
            if (confirm('确定要重新提交申诉吗？这将覆盖之前的申诉记录。')) {
                // 移除URL中的phone参数，显示申诉表单
                const url = new URL(window.location);
                url.searchParams.delete('phone');
                window.location.href = url.toString();
            }
        }

        // 自动填充手机号（如果URL中有phone参数）
        document.addEventListener('DOMContentLoaded', function() {
            const urlParams = new URLSearchParams(window.location.search);
            const phone = urlParams.get('phone');
            if (phone && document.getElementById('phone')) {
                document.getElementById('phone').value = phone;
            }
        });

        function showToast(message, type = 'success') {
            const toast = document.createElement('div');
            toast.className = `toast ${type}`;
            toast.textContent = message;
            document.body.appendChild(toast);

            setTimeout(() => {
                toast.classList.add('show');
            }, 100);

            setTimeout(() => {
                toast.classList.remove('show');
                setTimeout(() => {
                    document.body.removeChild(toast);
                }, 300);
            }, 3000);
        }
    </script>
</body>
</html>
