<?php
session_start();
header('Content-Type: application/json');

// 获取POST数据
$input = json_decode(file_get_contents('php://input'), true);
$phone = $input['phone'] ?? '';

if (empty($phone)) {
    echo json_encode(['success' => false, 'error' => '手机号不能为空']);
    exit;
}

// 设置验证成功的session
$_SESSION['phone_verified'] = 'true';
$_SESSION['verified_phone'] = $phone;

echo json_encode(['success' => true]);
?>
