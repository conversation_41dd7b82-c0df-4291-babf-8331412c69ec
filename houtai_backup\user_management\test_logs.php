<?php
session_start();
require_once '../db_config.php';

// 检查管理员登录状态
if (!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
    header('Location: ../login.php');
    exit;
}

echo "<h1>管理日志表结构测试</h1>";

try {
    $pdo = getDbConnection();
    echo "<p style='color: green;'>✅ 数据库连接成功！</p>";
    
    // 检查 admin_logs 表结构
    echo "<h2>1. admin_logs 表结构检查</h2>";
    $stmt = $pdo->query("DESCRIBE admin_logs");
    $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    $required_fields = ['employee_id', 'department'];
    $missing_fields = [];
    
    echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
    echo "<tr><th>字段名</th><th>类型</th><th>允许NULL</th><th>默认值</th><th>注释</th></tr>";
    
    $existing_fields = [];
    foreach ($columns as $column) {
        $existing_fields[] = $column['Field'];
        echo "<tr>";
        echo "<td>" . htmlspecialchars($column['Field']) . "</td>";
        echo "<td>" . htmlspecialchars($column['Type']) . "</td>";
        echo "<td>" . htmlspecialchars($column['Null']) . "</td>";
        echo "<td>" . htmlspecialchars($column['Default'] ?? 'NULL') . "</td>";
        echo "<td>" . htmlspecialchars($column['Extra']) . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // 检查必需字段
    foreach ($required_fields as $field) {
        if (!in_array($field, $existing_fields)) {
            $missing_fields[] = $field;
        }
    }
    
    if (empty($missing_fields)) {
        echo "<p style='color: green;'>✅ admin_logs 表结构完整，包含所有必需字段</p>";
    } else {
        echo "<p style='color: red;'>❌ admin_logs 表缺少字段: " . implode(', ', $missing_fields) . "</p>";
        echo "<p><strong>解决方案：</strong>请执行 fix_admin_logs_table.sql 脚本</p>";
    }
    
    // 检查 user_logs 表是否存在
    echo "<h2>2. user_logs 表检查</h2>";
    $stmt = $pdo->query("SHOW TABLES LIKE 'user_logs'");
    if ($stmt->rowCount() > 0) {
        echo "<p style='color: green;'>✅ user_logs 表存在</p>";
        
        $stmt = $pdo->query("DESCRIBE user_logs");
        $user_logs_columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
        echo "<tr><th>字段名</th><th>类型</th><th>允许NULL</th><th>默认值</th></tr>";
        foreach ($user_logs_columns as $column) {
            echo "<tr>";
            echo "<td>" . htmlspecialchars($column['Field']) . "</td>";
            echo "<td>" . htmlspecialchars($column['Type']) . "</td>";
            echo "<td>" . htmlspecialchars($column['Null']) . "</td>";
            echo "<td>" . htmlspecialchars($column['Default'] ?? 'NULL') . "</td>";
            echo "</tr>";
        }
        echo "</table>";
        
        // 获取记录数量
        $stmt = $pdo->query("SELECT COUNT(*) as count FROM user_logs");
        $count = $stmt->fetch()['count'];
        echo "<p>表中共有 {$count} 条记录</p>";
        
    } else {
        echo "<p style='color: red;'>❌ user_logs 表不存在</p>";
        echo "<p><strong>解决方案：</strong>请执行 fix_admin_logs_table.sql 脚本</p>";
    }
    
    // 检查 admin_users 表的扩展字段
    echo "<h2>3. admin_users 表扩展字段检查</h2>";
    $stmt = $pdo->query("DESCRIBE admin_users");
    $admin_users_columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    $admin_required_fields = ['employee_id', 'department'];
    $admin_missing_fields = [];
    $admin_existing_fields = [];
    
    foreach ($admin_users_columns as $column) {
        $admin_existing_fields[] = $column['Field'];
    }
    
    foreach ($admin_required_fields as $field) {
        if (!in_array($field, $admin_existing_fields)) {
            $admin_missing_fields[] = $field;
        }
    }
    
    if (empty($admin_missing_fields)) {
        echo "<p style='color: green;'>✅ admin_users 表包含所有必需的扩展字段</p>";
        
        // 显示管理员信息
        $stmt = $pdo->query("SELECT id, name, employee_id, department FROM admin_users LIMIT 5");
        $admins = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        if (!empty($admins)) {
            echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
            echo "<tr><th>ID</th><th>姓名</th><th>工号</th><th>部门</th></tr>";
            foreach ($admins as $admin) {
                echo "<tr>";
                echo "<td>" . htmlspecialchars($admin['id']) . "</td>";
                echo "<td>" . htmlspecialchars($admin['name']) . "</td>";
                echo "<td>" . htmlspecialchars($admin['employee_id'] ?? '未设置') . "</td>";
                echo "<td>" . htmlspecialchars($admin['department'] ?? '未设置') . "</td>";
                echo "</tr>";
            }
            echo "</table>";
        }
        
    } else {
        echo "<p style='color: red;'>❌ admin_users 表缺少字段: " . implode(', ', $admin_missing_fields) . "</p>";
    }
    
    // 测试日志插入功能
    echo "<h2>4. 日志插入功能测试</h2>";
    
    if (empty($missing_fields) && in_array('user_logs', array_column($pdo->query("SHOW TABLES")->fetchAll(), 'Tables_in_' . $pdo->query("SELECT DATABASE()")->fetchColumn()))) {
        
        // 获取一个测试用户ID
        $stmt = $pdo->query("SELECT id FROM users LIMIT 1");
        $test_user = $stmt->fetch();
        
        if ($test_user) {
            $test_user_id = $test_user['id'];
            
            try {
                // 测试插入管理日志
                $stmt = $pdo->prepare("
                    INSERT INTO admin_logs (
                        admin_id, admin_name, employee_id, department,
                        target_user_id, action, reason, created_at
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, NOW())
                ");
                $stmt->execute([
                    $_SESSION['admin_id'] ?? 1,
                    $_SESSION['admin_name'] ?? '测试管理员',
                    $_SESSION['employee_id'] ?? 'TEST001',
                    $_SESSION['department'] ?? '测试部门',
                    $test_user_id,
                    '测试操作',
                    '这是一条测试日志记录'
                ]);
                
                echo "<p style='color: green;'>✅ 管理日志插入测试成功</p>";
                
                // 测试插入用户日志
                $stmt = $pdo->prepare("
                    INSERT INTO user_logs (
                        user_id, operator_name, employee_id, department,
                        type, content, created_at
                    ) VALUES (?, ?, ?, ?, ?, ?, NOW())
                ");
                $stmt->execute([
                    $test_user_id,
                    $_SESSION['admin_name'] ?? '测试管理员',
                    $_SESSION['employee_id'] ?? 'TEST001',
                    $_SESSION['department'] ?? '测试部门',
                    '测试日志',
                    '这是一条测试用户日志记录'
                ]);
                
                echo "<p style='color: green;'>✅ 用户日志插入测试成功</p>";
                
                // 清理测试数据
                $pdo->prepare("DELETE FROM admin_logs WHERE reason = '这是一条测试日志记录'")->execute();
                $pdo->prepare("DELETE FROM user_logs WHERE content = '这是一条测试用户日志记录'")->execute();
                
                echo "<p style='color: blue;'>ℹ️ 测试数据已清理</p>";
                
            } catch (PDOException $e) {
                echo "<p style='color: red;'>❌ 日志插入测试失败: " . htmlspecialchars($e->getMessage()) . "</p>";
            }
        } else {
            echo "<p style='color: orange;'>⚠️ 没有找到测试用户，跳过插入测试</p>";
        }
    } else {
        echo "<p style='color: orange;'>⚠️ 表结构不完整，跳过插入测试</p>";
    }
    
    // 显示修复建议
    echo "<h2>5. 修复建议</h2>";
    
    if (!empty($missing_fields) || !empty($admin_missing_fields) || $stmt->rowCount() == 0) {
        echo "<div style='background: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 5px;'>";
        echo "<h3>🔧 需要执行修复脚本</h3>";
        echo "<p>请按照以下步骤修复数据库表结构：</p>";
        echo "<ol>";
        echo "<li>登录宝塔面板或phpMyAdmin</li>";
        echo "<li>选择数据库: <code>quwanplanet</code></li>";
        echo "<li>点击 'SQL' 选项卡</li>";
        echo "<li>复制 <code>fix_admin_logs_table.sql</code> 文件的全部内容</li>";
        echo "<li>粘贴到SQL执行器中并点击执行</li>";
        echo "<li>刷新此页面验证修复结果</li>";
        echo "</ol>";
        echo "</div>";
    } else {
        echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; padding: 15px; border-radius: 5px;'>";
        echo "<h3>✅ 数据库表结构正常</h3>";
        echo "<p>所有必需的表和字段都已存在，管理日志功能应该可以正常使用。</p>";
        echo "</div>";
    }
    
} catch (PDOException $e) {
    echo "<p style='color: red;'>❌ 数据库错误：" . htmlspecialchars($e->getMessage()) . "</p>";
}

echo "<br><a href='detail.php?id=1'>返回用户详情页面测试</a>";
echo " | <a href='index.php'>返回用户管理</a>";
?>
