/* 游戏玩伴页面新样式 - 使用主题色 #40E0D0 */

/* 全局变量 */
:root {
    --theme-color: #40E0D0;
    --theme-light: rgba(64, 224, 208, 0.1);
    --theme-dark: #36C5B0;
    --text-primary: #333;
    --text-secondary: #666;
    --text-light: #999;
    --bg-white: #ffffff;
    --bg-light: #f8f9fa;
    --border-light: #f0f0f0;
    --shadow-sm: 0 2px 8px rgba(0, 0, 0, 0.08);
    --shadow-md: 0 4px 16px rgba(0, 0, 0, 0.12);
    --radius-sm: 8px;
    --radius-md: 12px;
    --radius-lg: 16px;
    --radius-xl: 20px;
}

/* 容器样式 */
.game-companion-container {
    background: var(--bg-white);
    min-height: 100vh;
    padding: 0;
    margin: 0 -15px -15px -15px; /* 负边距实现全屏 */
    padding-bottom: 80px; /* 为底部导航栏留空间 */
}

/* 顶部快速筛选 */
.quick-filters-top {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 15px; /* 减少左右padding，配合负边距实现全屏 */
    background: var(--bg-white);
    border-bottom: 1px solid var(--border-light);
    position: sticky;
    top: 0;
    z-index: 20;
    margin: 0 0 0 0; /* 确保没有额外边距 */
}

.filter-tabs {
    display: flex;
    gap: 8px;
    flex: 1;
    overflow-x: auto; /* 允许水平滚动 */
    overflow-y: hidden; /* 禁止垂直滚动 */
    scrollbar-width: none; /* Firefox 隐藏滚动条 */
    -ms-overflow-style: none; /* IE/Edge 隐藏滚动条 */
    flex-wrap: nowrap; /* 禁止换行 */
    touch-action: pan-x; /* 只允许水平滑动 */
}

.filter-tabs::-webkit-scrollbar {
    display: none;
}

.filter-tab {
    padding: 8px 16px;
    border: none;
    border-radius: var(--radius-xl);
    background: var(--bg-light);
    color: var(--text-secondary);
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    white-space: nowrap; /* 禁止文字换行 */
    min-width: fit-content; /* 最小宽度适应内容 */
    flex-shrink: 0; /* 禁止收缩 */
}

.filter-tab.active {
    background: var(--theme-color);
    color: white;
    transform: scale(1.05);
}

.filter-tab:hover:not(.active) {
    background: #e0e0e0;
    transform: translateY(-1px);
}

.advanced-filter-btn {
    padding: 8px 12px;
    border: 1px solid var(--theme-color);
    border-radius: var(--radius-sm);
    background: white;
    color: var(--theme-color);
    font-size: 16px;
    cursor: pointer;
    transition: all 0.3s ease;
    margin-left: 10px;
}

.advanced-filter-btn:hover {
    background: var(--theme-color);
    color: white;
    transform: scale(1.05);
}

/* 推荐大神区域 */
.recommended-masters {
    padding: 20px 15px; /* 减少左右padding，配合全屏设计 */
    background: white;
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
}

.section-header h3 {
    font-size: 18px;
    color: var(--text-primary);
    margin: 0;
    font-weight: 600;
}

.view-more {
    color: var(--theme-color);
    text-decoration: none;
    font-size: 14px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.view-more:hover {
    text-decoration: underline;
    transform: translateX(2px);
}

/* 大神卡片横向滚动 */
.masters-scroll-container {
    display: flex;
    gap: 12px;
    overflow-x: auto;
    padding-bottom: 10px;
    scrollbar-width: none;
    -ms-overflow-style: none;
}

.masters-scroll-container::-webkit-scrollbar {
    display: none;
}

.master-card-mini {
    min-width: 120px;
    background: white;
    border-radius: var(--radius-md);
    padding: 12px;
    box-shadow: var(--shadow-sm);
    text-align: center;
    border: 1px solid var(--border-light);
    transition: all 0.3s ease;
    cursor: pointer;
}

.master-card-mini:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-md);
    border-color: var(--theme-color);
}

.master-avatar {
    position: relative;
    margin-bottom: 8px;
    display: flex;
    justify-content: center;
}

.master-avatar img {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    object-fit: cover;
    border: 2px solid var(--border-light);
}

.online-dot {
    position: absolute;
    bottom: 2px;
    right: 2px;
    width: 12px;
    height: 12px;
    background: #06D6A0;
    border: 2px solid white;
    border-radius: 50%;
    box-shadow: 0 0 0 1px rgba(0,0,0,0.1);
}

.online-dot.offline {
    background: #ccc;
}

.master-badge {
    position: absolute;
    top: -5px;
    left: -5px;
    background: var(--theme-color);
    color: white;
    font-size: 10px;
    padding: 2px 6px;
    border-radius: var(--radius-sm);
    font-weight: 600;
    box-shadow: var(--shadow-sm);
}

.master-badge.female {
    background: #FF6B9D;
}

.master-info h4 {
    font-size: 13px;
    color: var(--text-primary);
    margin: 0 0 4px 0;
    font-weight: 600;
    line-height: 1.2;
}

.master-game {
    font-size: 11px;
    color: var(--text-secondary);
    display: block;
    margin-bottom: 4px;
}

.master-rating {
    font-size: 11px;
    color: var(--theme-color);
    font-weight: 600;
}

.master-price {
    font-size: 12px;
    color: var(--text-primary);
    font-weight: 700;
    margin-top: 6px;
}

.master-price span {
    font-size: 10px;
    color: var(--text-secondary);
    font-weight: normal;
}

/* 玩伴列表区域 */
.companions-overview {
    padding: 0 15px 20px; /* 减少左右padding，配合全屏设计 */
    background: white;
}

.section-header-simple {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 1px solid var(--border-light);
}

.section-header-simple h3 {
    font-size: 18px;
    color: var(--text-primary);
    margin: 0;
    font-weight: 600;
}

.online-count {
    font-size: 14px;
    color: var(--text-secondary);
    background: var(--theme-light);
    padding: 4px 12px;
    border-radius: var(--radius-xl);
    font-weight: 500;
}

/* 新的玩伴卡片样式 */
.companion-list-new {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.companion-card-new {
    display: flex;
    align-items: center;
    background: white;
    border: 1px solid var(--border-light);
    border-radius: var(--radius-md);
    padding: 15px;
    transition: all 0.3s ease;
    cursor: pointer;
}

.companion-card-new:hover {
    border-color: var(--theme-color);
    box-shadow: var(--shadow-md);
    transform: translateY(-2px);
}

.companion-left {
    display: flex;
    align-items: center;
    flex: 1;
    gap: 12px;
}

.companion-avatar {
    position: relative;
    flex-shrink: 0;
}

.companion-avatar img {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    object-fit: cover;
    border: 2px solid var(--border-light);
}

.online-status {
    position: absolute;
    bottom: 0;
    right: 0;
    width: 12px;
    height: 12px;
    background: #06D6A0;
    border: 2px solid white;
    border-radius: 50%;
    box-shadow: 0 0 0 1px rgba(0,0,0,0.1);
}

.online-status.offline {
    background: #ccc;
}

.companion-info {
    flex: 1;
    min-width: 0;
}

.companion-info h4 {
    font-size: 16px;
    color: var(--text-primary);
    margin: 0 0 6px 0;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 6px;
}

.gender-tag {
    font-size: 12px;
    font-weight: 500;
}

.gender-tag.female {
    color: #FF6B9D;
}

.gender-tag.male {
    color: #4A90E2;
}

.companion-tags {
    display: flex;
    gap: 6px;
    margin-bottom: 6px;
    flex-wrap: wrap;
}

.game-tag, .skill-tag {
    font-size: 11px;
    padding: 2px 8px;
    border-radius: var(--radius-sm);
    font-weight: 500;
}

.game-tag {
    background: var(--theme-light);
    color: var(--theme-color);
}

.skill-tag {
    background: #FFF3E0;
    color: #F57C00;
}

.companion-features {
    display: flex;
    gap: 8px;
    font-size: 12px;
    color: var(--text-secondary);
}

.feature {
    display: flex;
    align-items: center;
    gap: 2px;
}

.companion-right {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    gap: 8px;
    flex-shrink: 0;
}

.companion-price {
    font-size: 18px;
    font-weight: 700;
    color: var(--text-primary);
}

.companion-price span {
    font-size: 12px;
    color: var(--text-secondary);
    font-weight: normal;
}

.invite-btn-new {
    background: var(--theme-color);
    color: white;
    border: none;
    border-radius: var(--radius-sm);
    padding: 8px 16px;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    min-width: 60px;
}

.invite-btn-new:hover:not(:disabled) {
    background: var(--theme-dark);
    transform: scale(1.05);
}

.invite-btn-new:disabled {
    background: #ccc;
    cursor: not-allowed;
    transform: none;
}

/* 加载更多按钮 */
.load-more-container {
    text-align: center;
    margin-top: 30px;
    padding-top: 20px;
    border-top: 1px solid var(--border-light);
}

.load-more-btn-new {
    background: var(--bg-light);
    color: var(--text-secondary);
    border: 1px solid var(--border-light);
    border-radius: var(--radius-md);
    padding: 12px 24px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
}

.load-more-btn-new:hover {
    background: var(--theme-light);
    color: var(--theme-color);
    border-color: var(--theme-color);
    transform: translateY(-2px);
}

/* 响应式设计 */
@media (max-width: 768px) {
    .quick-filters-top {
        padding: 12px 15px;
    }

    .recommended-masters,
    .companions-overview {
        padding: 15px;
    }

    .companion-card-new {
        padding: 12px;
    }

    .companion-left {
        gap: 10px;
    }

    .companion-info h4 {
        font-size: 15px;
    }

    .companion-price {
        font-size: 16px;
    }
}
