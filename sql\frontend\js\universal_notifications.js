/**
 * 通用实时通知系统
 * 可直接集成到任何前台页面
 */

(function() {
    'use strict';
    
    // 防止重复初始化
    if (window.universalNotificationsLoaded) {
        return;
    }
    window.universalNotificationsLoaded = true;
    
    // 配置
    const config = {
        pollInterval: 2000,        // 轮询间隔（毫秒）
        maxRetries: 5,            // 最大重试次数
        retryDelay: 5000,         // 重试延迟（毫秒）
        autoStart: true,          // 自动启动
        showStatus: false,        // 显示连接状态（生产环境建议关闭）
        debug: false              // 调试模式
    };
    
    // 全局变量
    let pollTimer = null;
    let retryCount = 0;
    let isRunning = false;
    let userId = null;
    
    // 日志函数
    function log(message, type = 'info') {
        if (config.debug) {
            console.log(`[实时通知] ${message}`);
        }
    }
    
    // 获取用户ID
    function getUserId() {
        // 多种方式获取用户ID
        const sources = [
            () => window.currentUserId,
            () => document.querySelector('meta[name="user-id"]')?.content,
            () => localStorage.getItem('user_id'),
            () => sessionStorage.getItem('user_id'),
            () => {
                const match = document.cookie.match(/user_id=([^;]+)/);
                return match ? match[1] : null;
            }
        ];
        
        for (const source of sources) {
            try {
                const id = source();
                if (id && id !== '0' && id !== 'null') {
                    return parseInt(id);
                }
            } catch (e) {
                // 忽略错误
            }
        }
        
        return null;
    }
    
    // 显示连接状态
    function showStatus(message, type = 'info') {
        if (!config.showStatus) return;
        
        // 移除现有状态
        const existing = document.getElementById('notification-status');
        if (existing) {
            existing.remove();
        }
        
        const statusDiv = document.createElement('div');
        statusDiv.id = 'notification-status';
        statusDiv.style.cssText = `
            position: fixed;
            bottom: 20px;
            left: 20px;
            z-index: 9999;
            padding: 8px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 500;
            color: white;
            pointer-events: none;
            transition: all 0.3s ease;
        `;
        
        const colors = {
            info: '#3182CE',
            success: '#10B981',
            warning: '#F59E0B',
            error: '#EF4444'
        };
        
        statusDiv.style.background = colors[type] || colors.info;
        statusDiv.textContent = message;
        
        document.body.appendChild(statusDiv);
        
        // 自动隐藏
        setTimeout(() => {
            if (statusDiv.parentNode) {
                statusDiv.style.opacity = '0';
                setTimeout(() => {
                    if (statusDiv.parentNode) {
                        statusDiv.remove();
                    }
                }, 300);
            }
        }, 3000);
    }
    
    // 创建通知容器
    function createNotificationContainer() {
        if (document.getElementById('notification-container')) {
            return;
        }
        
        const container = document.createElement('div');
        container.id = 'notification-container';
        container.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 10000;
            pointer-events: none;
            max-width: 350px;
        `;
        
        document.body.appendChild(container);
    }
    
    // 轮询通知
    async function pollNotifications() {
        if (!userId || !isRunning) return;
        
        try {
            const response = await fetch(`/frontend/api/simple_websocket_server.php?action=poll&user_id=${userId}`);
            const data = await response.json();
            
            if (data.success) {
                log(`轮询成功，收到 ${data.data.count} 条通知`);
                
                if (data.data.notifications && data.data.notifications.length > 0) {
                    data.data.notifications.forEach(notification => {
                        handleNotification(notification);
                    });
                }
                
                // 重置重试计数
                retryCount = 0;
                
            } else {
                log(`轮询失败: ${data.error}`, 'error');
                handleError();
            }
            
        } catch (error) {
            log(`轮询请求失败: ${error.message}`, 'error');
            handleError();
        }
    }
    
    // 处理通知
    function handleNotification(notification) {
        log(`处理通知: ${notification.title}`);
        
        try {
            switch (notification.type) {
                case 'verification_code':
                    showVerificationModal(notification);
                    break;
                    
                case 'system_message':
                    showToast(notification.title, notification.content, 'info');
                    break;
                    
                case 'admin_notice':
                    showToast(notification.title, notification.content, 'warning');
                    break;
                    
                default:
                    showToast(notification.title || '系统通知', notification.content, 'info');
            }
            
            // 标记为已读
            markAsRead(notification.id);
            
        } catch (error) {
            log(`处理通知失败: ${error.message}`, 'error');
            showToast('通知错误', notification.content || '收到新通知', 'error');
        }
    }
    
    // 显示验证码弹窗
    function showVerificationModal(notification) {
        const data = typeof notification.data === 'string' ? 
            JSON.parse(notification.data) : notification.data;
        
        const modal = document.createElement('div');
        modal.className = 'verification-modal';
        modal.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.6);
            z-index: 10001;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
            backdrop-filter: blur(5px);
            animation: modalFadeIn 0.3s ease;
        `;
        
        const code = data?.code || '******';
        const adminNote = data?.admin_note || '';
        const sentBy = data?.sent_by || '管理员';
        const expiresAt = data?.expires_at || '';
        
        // 计算剩余时间
        let remainingMinutes = 5;
        if (expiresAt) {
            const expiryTime = new Date(expiresAt);
            const now = new Date();
            remainingMinutes = Math.max(0, Math.ceil((expiryTime - now) / 60000));
        }
        
        modal.innerHTML = `
            <div style="
                background: white;
                border-radius: 16px;
                padding: 32px 24px;
                max-width: 400px;
                width: 100%;
                text-align: center;
                box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
                animation: modalSlideIn 0.3s ease;
            ">
                <div style="
                    width: 60px;
                    height: 60px;
                    background: linear-gradient(135deg, #6F7BF5 0%, #9B59B6 100%);
                    border-radius: 50%;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    margin: 0 auto 16px;
                    color: white;
                    font-size: 24px;
                ">🔑</div>
                
                <h3 style="margin: 0 0 8px 0; color: #2D3748; font-size: 20px; font-weight: 700;">${notification.title}</h3>
                <p style="color: #718096; font-size: 14px; margin: 0 0 24px 0;">来自 ${sentBy}</p>
                
                <div style="
                    background: linear-gradient(135deg, #6F7BF5 0%, #9B59B6 100%);
                    color: white;
                    padding: 20px;
                    border-radius: 12px;
                    font-size: 32px;
                    font-weight: 700;
                    letter-spacing: 8px;
                    font-family: 'Courier New', monospace;
                    margin: 24px 0;
                    cursor: pointer;
                    user-select: all;
                    box-shadow: 0 8px 16px rgba(111, 123, 245, 0.3);
                " onclick="
                    if (navigator.clipboard) {
                        navigator.clipboard.writeText('${code}').then(() => {
                            alert('验证码已复制到剪贴板');
                        }).catch(() => {
                            prompt('请手动复制验证码:', '${code}');
                        });
                    } else {
                        prompt('请手动复制验证码:', '${code}');
                    }
                ">${code}</div>
                
                <div style="
                    background: #F7FAFC;
                    border-radius: 8px;
                    padding: 16px;
                    margin: 20px 0;
                    text-align: left;
                ">
                    <div style="
                        display: flex;
                        align-items: center;
                        gap: 8px;
                        margin-bottom: 8px;
                        color: #4A5568;
                        font-size: 14px;
                    ">
                        <span>⏰</span>
                        <span>有效期：${remainingMinutes} 分钟</span>
                    </div>
                    ${adminNote ? `
                    <div style="
                        display: flex;
                        align-items: flex-start;
                        gap: 8px;
                        color: #4A5568;
                        font-size: 14px;
                        line-height: 1.4;
                    ">
                        <span>📝</span>
                        <span>备注：${adminNote}</span>
                    </div>
                    ` : ''}
                </div>
                
                <button onclick="this.closest('.verification-modal').remove();" style="
                    width: 100%;
                    padding: 12px 16px;
                    background: linear-gradient(135deg, #6F7BF5 0%, #9B59B6 100%);
                    color: white;
                    border: none;
                    border-radius: 8px;
                    font-size: 14px;
                    font-weight: 600;
                    cursor: pointer;
                    transition: all 0.2s ease;
                ">我知道了</button>
            </div>
        `;
        
        // 添加动画样式
        if (!document.getElementById('notification-styles')) {
            const style = document.createElement('style');
            style.id = 'notification-styles';
            style.textContent = `
                @keyframes modalFadeIn {
                    from { opacity: 0; }
                    to { opacity: 1; }
                }
                @keyframes modalSlideIn {
                    from { opacity: 0; transform: translateY(20px) scale(0.95); }
                    to { opacity: 1; transform: translateY(0) scale(1); }
                }
            `;
            document.head.appendChild(style);
        }
        
        document.body.appendChild(modal);
        
        // 自动关闭
        setTimeout(() => {
            if (document.body.contains(modal)) {
                modal.remove();
            }
        }, 60000);
        
        // 播放提示音
        playNotificationSound();
    }
    
    // 显示Toast通知
    function showToast(title, message, type = 'info') {
        const container = document.getElementById('notification-container');
        if (!container) return;
        
        const toast = document.createElement('div');
        
        const colors = {
            info: { bg: '#3182CE', icon: 'ℹ️' },
            warning: { bg: '#D69E2E', icon: '⚠️' },
            error: { bg: '#E53E3E', icon: '❌' },
            success: { bg: '#38A169', icon: '✅' }
        };
        
        const color = colors[type] || colors.info;
        
        toast.style.cssText = `
            background: ${color.bg};
            color: white;
            padding: 16px 20px;
            border-radius: 8px;
            margin-bottom: 12px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            pointer-events: auto;
            cursor: pointer;
            transition: all 0.3s ease;
            transform: translateX(100%);
            opacity: 0;
        `;
        
        toast.innerHTML = `
            <div style="display: flex; align-items: flex-start; gap: 12px;">
                <span style="font-size: 18px;">${color.icon}</span>
                <div style="flex: 1;">
                    <div style="font-weight: 600; margin-bottom: 4px;">${title}</div>
                    <div style="font-size: 14px; opacity: 0.9; line-height: 1.4;">${message}</div>
                </div>
            </div>
        `;
        
        container.appendChild(toast);
        
        // 显示动画
        setTimeout(() => {
            toast.style.transform = 'translateX(0)';
            toast.style.opacity = '1';
        }, 100);
        
        // 点击关闭
        toast.addEventListener('click', () => {
            removeToast(toast);
        });
        
        // 自动关闭
        setTimeout(() => {
            removeToast(toast);
        }, 5000);
    }
    
    // 移除Toast
    function removeToast(toast) {
        if (toast.parentNode) {
            toast.style.transform = 'translateX(100%)';
            toast.style.opacity = '0';
            setTimeout(() => {
                if (toast.parentNode) {
                    toast.remove();
                }
            }, 300);
        }
    }
    
    // 播放提示音
    function playNotificationSound() {
        try {
            const audioContext = new (window.AudioContext || window.webkitAudioContext)();
            const oscillator = audioContext.createOscillator();
            const gainNode = audioContext.createGain();
            
            oscillator.connect(gainNode);
            gainNode.connect(audioContext.destination);
            
            oscillator.frequency.setValueAtTime(800, audioContext.currentTime);
            oscillator.frequency.setValueAtTime(1000, audioContext.currentTime + 0.1);
            
            gainNode.gain.setValueAtTime(0.1, audioContext.currentTime);
            gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.2);
            
            oscillator.start(audioContext.currentTime);
            oscillator.stop(audioContext.currentTime + 0.2);
        } catch (error) {
            log('无法播放提示音', 'warning');
        }
    }
    
    // 标记通知为已读
    async function markAsRead(notificationId) {
        try {
            await fetch(`/frontend/api/simple_websocket_server.php?action=mark_read&user_id=${userId}&notification_id=${notificationId}`);
        } catch (error) {
            log(`标记已读失败: ${error.message}`, 'error');
        }
    }
    
    // 处理错误
    function handleError() {
        retryCount++;
        
        if (retryCount >= config.maxRetries) {
            log('达到最大重试次数，停止轮询', 'error');
            stop();
            showStatus('通知服务连接失败', 'error');
            return;
        }
        
        log(`连接错误，${retryCount}/${config.maxRetries} 次重试`, 'warning');
        
        // 延迟重试
        setTimeout(() => {
            if (isRunning) {
                pollNotifications();
            }
        }, config.retryDelay);
    }
    
    // 启动通知服务
    function start() {
        userId = getUserId();
        
        if (!userId) {
            log('未找到用户ID，跳过通知服务启动');
            return false;
        }
        
        if (isRunning) {
            log('通知服务已在运行中');
            return true;
        }
        
        log(`启动通知服务，用户ID: ${userId}`);
        isRunning = true;
        retryCount = 0;
        
        // 创建通知容器
        createNotificationContainer();
        
        // 开始轮询
        pollTimer = setInterval(pollNotifications, config.pollInterval);
        
        // 立即执行一次
        pollNotifications();
        
        showStatus('通知服务已启动', 'success');
        return true;
    }
    
    // 停止通知服务
    function stop() {
        if (pollTimer) {
            clearInterval(pollTimer);
            pollTimer = null;
        }
        
        isRunning = false;
        log('通知服务已停止');
        showStatus('通知服务已停止', 'warning');
    }
    
    // 全局API
    window.UniversalNotifications = {
        start: start,
        stop: stop,
        isRunning: () => isRunning,
        setConfig: (newConfig) => Object.assign(config, newConfig),
        showToast: showToast
    };
    
    // 自动启动
    if (config.autoStart) {
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', start);
        } else {
            setTimeout(start, 100);
        }
    }
    
    // 页面卸载时停止
    window.addEventListener('beforeunload', stop);
    
    log('通用实时通知系统加载完成');
})();
