<?php
/**
 * 数据库设置页面
 * 用于执行数据库修复和初始化
 */

// 简单的安全检查
$allowed_ips = ['127.0.0.1', '::1', 'localhost'];
$client_ip = $_SERVER['REMOTE_ADDR'] ?? '';

if (!in_array($client_ip, $allowed_ips) && !isset($_GET['allow'])) {
    die('Access denied. Only localhost access allowed.');
}

// 设置错误报告
error_reporting(E_ALL);
ini_set('display_errors', 1);

?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数据库设置 - 趣玩星球</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Robot<PERSON>, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 20px;
            min-height: 100vh;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 16px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #6F7BF5, #9C88FF);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            margin: 0;
            font-size: 24px;
        }
        
        .content {
            padding: 30px;
        }
        
        .action-card {
            border: 1px solid #E5E7EB;
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 20px;
            transition: all 0.2s;
        }
        
        .action-card:hover {
            border-color: #6F7BF5;
            box-shadow: 0 4px 12px rgba(111, 123, 245, 0.1);
        }
        
        .action-card h3 {
            margin: 0 0 10px 0;
            color: #1F2937;
        }
        
        .action-card p {
            margin: 0 0 15px 0;
            color: #6B7280;
            line-height: 1.5;
        }
        
        .btn {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            padding: 12px 20px;
            border: none;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            text-decoration: none;
            transition: all 0.2s;
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #6F7BF5, #9C88FF);
            color: white;
        }
        
        .btn-primary:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(111, 123, 245, 0.3);
        }
        
        .btn-success {
            background: linear-gradient(135deg, #10B981, #059669);
            color: white;
        }
        
        .btn-success:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
        }
        
        .btn-info {
            background: linear-gradient(135deg, #3B82F6, #2563EB);
            color: white;
        }
        
        .btn-info:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
        }
        
        .status {
            padding: 8px 12px;
            border-radius: 6px;
            font-size: 12px;
            font-weight: 500;
            margin-left: 10px;
        }
        
        .status-success {
            background: #D1FAE5;
            color: #065F46;
        }
        
        .status-warning {
            background: #FEF3C7;
            color: #92400E;
        }
        
        .status-error {
            background: #FEE2E2;
            color: #991B1B;
        }
        
        .warning {
            background: #FEF3C7;
            border: 1px solid #F59E0B;
            border-radius: 8px;
            padding: 16px;
            margin-bottom: 20px;
            color: #92400E;
        }
        
        .warning strong {
            display: block;
            margin-bottom: 8px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔧 数据库设置</h1>
            <p>趣玩星球申诉系统数据库初始化</p>
        </div>
        
        <div class="content">
            <div class="warning">
                <strong>⚠️ 重要提示</strong>
                请确保在执行数据库操作前已备份重要数据。这些操作将修改数据库结构。
            </div>
            
            <div class="action-card">
                <h3>🗄️ 执行数据库修复</h3>
                <p>自动执行申诉系统相关的数据库表创建和字段添加。包括用户申诉表、申诉日志表、客服系统表等。</p>
                <a href="../sql/run_database_fixes.php" class="btn btn-primary" target="_blank">
                    <span>🚀</span> 执行数据库修复
                </a>
                <span class="status status-warning">推荐首先执行</span>
            </div>
            
            <div class="action-card">
                <h3>📋 申诉管理后台</h3>
                <p>访问申诉审核管理页面，查看和处理用户提交的申诉请求。</p>
                <a href="appeal/index.php" class="btn btn-success" target="_blank">
                    <span>📊</span> 申诉管理后台
                </a>
            </div>
            
            <div class="action-card">
                <h3>📝 前台申诉页面</h3>
                <p>测试前台用户申诉功能，用户可以提交封号申诉和上传证明材料。</p>
                <a href="../frontend/appeal/index.php" class="btn btn-info" target="_blank">
                    <span>📄</span> 前台申诉页面
                </a>
            </div>
            
            <div class="action-card">
                <h3>🔐 管理后台登录</h3>
                <p>登录管理后台，访问完整的申诉审核功能。</p>
                <a href="login.php" class="btn btn-primary">
                    <span>🔑</span> 管理后台登录
                </a>
            </div>
            
            <div class="action-card">
                <h3>🧪 测试封号弹窗</h3>
                <p>测试前台登录时的封号详情弹窗显示功能。</p>
                <a href="../frontend/login/index.php" class="btn btn-info" target="_blank">
                    <span>🔍</span> 前台登录测试
                </a>
            </div>
            
            <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #E5E7EB; text-align: center; color: #6B7280; font-size: 14px;">
                <p>💡 <strong>使用说明：</strong></p>
                <p>1. 首先执行"数据库修复"创建必要的表结构</p>
                <p>2. 登录管理后台测试申诉审核功能</p>
                <p>3. 访问前台申诉页面测试用户申诉流程</p>
                <p>4. 测试前台登录的封号弹窗功能</p>
            </div>
        </div>
    </div>
</body>
</html>
