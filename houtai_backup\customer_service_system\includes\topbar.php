<?php
/**
 * 客服系统顶部导航栏组件
 */

// 获取客服信息
$cs_name = $_SESSION['cs_name'] ?? '客服';
$cs_employee_id = $_SESSION['cs_employee_id'] ?? '';
$cs_role = $_SESSION['cs_role'] ?? 'customer_service';

// 获取实时统计数据
try {
    if (!isset($pdo)) {
        require_once __DIR__ . '/../../db_config.php';
        $pdo = getDbConnection();
    }

    // 在线客服数量
    $stmt = $pdo->query("
        SELECT COUNT(*) as count 
        FROM customer_service_users 
        WHERE status = 'active' AND last_login >= DATE_SUB(NOW(), INTERVAL 30 MINUTE)
    ");
    $online_cs_count = $stmt->fetch()['count'] ?? 0;

    // 待处理会话数量
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM customer_service_sessions WHERE status = 'waiting'");
    $waiting_sessions = $stmt->fetch()['count'] ?? 0;

    // 活跃会话数量
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM customer_service_sessions WHERE status = 'active'");
    $active_sessions = $stmt->fetch()['count'] ?? 0;

} catch (Exception $e) {
    $online_cs_count = 0;
    $waiting_sessions = 0;
    $active_sessions = 0;
}
?>

<header class="cs-topbar">
    <div class="cs-topbar-left">
        <button class="cs-sidebar-toggle" onclick="toggleSidebar()">
            <i class="fas fa-bars"></i>
        </button>
        
        <div class="cs-breadcrumb">
            <span class="cs-breadcrumb-item">
                <i class="fas fa-home"></i>
                <a href="index.php">工作台</a>
            </span>
            <span class="cs-breadcrumb-separator">/</span>
            <span class="cs-breadcrumb-current">
                <?php
                $page_titles = [
                    'index.php' => '工作台',
                    'sessions.php' => '客服会话',
                    'session_management.php' => '会话管理',
                    'bot_management.php' => '智能客服管理',
                    'settings.php' => '客服系统设置',
                    'quality_check.php' => '客服质检',
                    'staff_management.php' => '客服管理',
                    'hetu_system.php' => '河图系统',
                    'profile.php' => '个人中心',
                    'help.php' => '帮助中心'
                ];
                echo $page_titles[basename($_SERVER['PHP_SELF'])] ?? '未知页面';
                ?>
            </span>
        </div>
    </div>

    <div class="cs-topbar-center">
        <div class="cs-status-indicators">
            <div class="cs-status-item">
                <i class="fas fa-users text-success"></i>
                <span class="cs-status-label">在线客服</span>
                <span class="cs-status-value"><?php echo $online_cs_count; ?></span>
            </div>
            <div class="cs-status-item">
                <i class="fas fa-clock text-warning"></i>
                <span class="cs-status-label">待处理</span>
                <span class="cs-status-value"><?php echo $waiting_sessions; ?></span>
            </div>
            <div class="cs-status-item">
                <i class="fas fa-comment-dots text-info"></i>
                <span class="cs-status-label">活跃会话</span>
                <span class="cs-status-value"><?php echo $active_sessions; ?></span>
            </div>
        </div>
    </div>

    <div class="cs-topbar-right">
        <!-- 快捷操作 -->
        <div class="cs-quick-actions">
            <button class="cs-quick-btn" onclick="refreshPage()" title="刷新页面">
                <i class="fas fa-sync-alt"></i>
            </button>
            
            <button class="cs-quick-btn" onclick="toggleFullscreen()" title="全屏模式">
                <i class="fas fa-expand"></i>
            </button>
            
            <div class="cs-notifications">
                <button class="cs-quick-btn cs-notification-btn" onclick="toggleNotifications()">
                    <i class="fas fa-bell"></i>
                    <span class="cs-notification-badge">3</span>
                </button>
                
                <!-- 通知下拉菜单 -->
                <div class="cs-notification-dropdown" id="notificationDropdown">
                    <div class="cs-notification-header">
                        <h4>通知消息</h4>
                        <button class="cs-mark-all-read">全部已读</button>
                    </div>
                    <div class="cs-notification-list">
                        <div class="cs-notification-item unread">
                            <div class="cs-notification-icon">
                                <i class="fas fa-comment text-primary"></i>
                            </div>
                            <div class="cs-notification-content">
                                <p>有新的客户咨询等待处理</p>
                                <small>2分钟前</small>
                            </div>
                        </div>
                        <div class="cs-notification-item unread">
                            <div class="cs-notification-icon">
                                <i class="fas fa-star text-warning"></i>
                            </div>
                            <div class="cs-notification-content">
                                <p>您有一个质检任务待完成</p>
                                <small>10分钟前</small>
                            </div>
                        </div>
                        <div class="cs-notification-item">
                            <div class="cs-notification-icon">
                                <i class="fas fa-info-circle text-info"></i>
                            </div>
                            <div class="cs-notification-content">
                                <p>系统将于今晚22:00进行维护</p>
                                <small>1小时前</small>
                            </div>
                        </div>
                    </div>
                    <div class="cs-notification-footer">
                        <a href="#" class="cs-view-all">查看全部通知</a>
                    </div>
                </div>
            </div>
        </div>

        <!-- 用户菜单 -->
        <div class="cs-user-menu">
            <button class="cs-user-menu-btn" onclick="toggleUserMenu()">
                <div class="cs-user-avatar-small">
                    <i class="fas fa-user-circle"></i>
                </div>
                <span class="cs-user-name-small"><?php echo htmlspecialchars($cs_name); ?></span>
                <i class="fas fa-chevron-down"></i>
            </button>
            
            <!-- 用户下拉菜单 -->
            <div class="cs-user-dropdown" id="userDropdown">
                <div class="cs-user-dropdown-header">
                    <div class="cs-user-avatar-large">
                        <i class="fas fa-user-circle"></i>
                    </div>
                    <div class="cs-user-info-dropdown">
                        <h4><?php echo htmlspecialchars($cs_name); ?></h4>
                        <p>工号: <?php echo htmlspecialchars($cs_employee_id); ?></p>
                        <span class="cs-role-badge <?php echo $cs_role; ?>">
                            <?php echo $cs_role === 'super_admin' ? '超级管理员' : '客服专员'; ?>
                        </span>
                    </div>
                </div>
                <div class="cs-user-dropdown-menu">
                    <a href="profile.php" class="cs-dropdown-item">
                        <i class="fas fa-user"></i>
                        <span>个人中心</span>
                    </a>
                    <a href="settings.php" class="cs-dropdown-item">
                        <i class="fas fa-cog"></i>
                        <span>设置</span>
                    </a>
                    <a href="help.php" class="cs-dropdown-item">
                        <i class="fas fa-question-circle"></i>
                        <span>帮助</span>
                    </a>
                    <div class="cs-dropdown-divider"></div>
                    <a href="logout.php" class="cs-dropdown-item text-danger">
                        <i class="fas fa-sign-out-alt"></i>
                        <span>退出登录</span>
                    </a>
                </div>
            </div>
        </div>
    </div>
</header>

<script>
// 顶部导航栏交互脚本
function toggleSidebar() {
    document.querySelector('.cs-container').classList.toggle('sidebar-collapsed');
}

function refreshPage() {
    location.reload();
}

function toggleFullscreen() {
    if (!document.fullscreenElement) {
        document.documentElement.requestFullscreen();
    } else {
        document.exitFullscreen();
    }
}

function toggleNotifications() {
    const dropdown = document.getElementById('notificationDropdown');
    dropdown.classList.toggle('show');
    
    // 关闭用户菜单
    document.getElementById('userDropdown').classList.remove('show');
}

function toggleUserMenu() {
    const dropdown = document.getElementById('userDropdown');
    dropdown.classList.toggle('show');
    
    // 关闭通知菜单
    document.getElementById('notificationDropdown').classList.remove('show');
}

// 点击外部关闭下拉菜单
document.addEventListener('click', function(e) {
    if (!e.target.closest('.cs-notifications')) {
        document.getElementById('notificationDropdown').classList.remove('show');
    }
    if (!e.target.closest('.cs-user-menu')) {
        document.getElementById('userDropdown').classList.remove('show');
    }
});

// 实时更新状态指示器
setInterval(function() {
    // 这里可以通过AJAX更新实时数据
    // updateStatusIndicators();
}, 30000); // 每30秒更新一次
</script>
