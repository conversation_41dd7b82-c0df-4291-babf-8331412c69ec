// 后台管理系统主要JavaScript

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    initializeDateTime();
    initializeCharts();
    initializeNotifications();
    
    console.log('后台管理系统已加载');
});

// 初始化日期时间显示
function initializeDateTime() {
    const timeElement = document.getElementById('currentTime');
    if (timeElement) {
        updateDateTime();
        setInterval(updateDateTime, 1000);
    }
}

// 更新日期时间
function updateDateTime() {
    const now = new Date();
    const options = {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit',
        hour12: false
    };
    
    const timeString = now.toLocaleString('zh-CN', options);
    document.getElementById('currentTime').textContent = timeString;
}

// 初始化图表（如果需要）
function initializeCharts() {
    // 这里可以添加图表初始化代码
    // 例如使用 Chart.js 或其他图表库
}

// 初始化通知系统
function initializeNotifications() {
    // 检查是否有新的待审核项目
    checkPendingItems();
    
    // 每5分钟检查一次
    setInterval(checkPendingItems, 5 * 60 * 1000);
}

// 检查待处理项目
async function checkPendingItems() {
    try {
        const response = await fetch('api/check_pending.php');
        const data = await response.json();
        
        if (data.success) {
            updateNotificationBadges(data.counts);
        }
    } catch (error) {
        console.error('检查待处理项目失败:', error);
    }
}

// 更新通知徽章
function updateNotificationBadges(counts) {
    // 更新实名认证徽章
    const verificationBadge = document.querySelector('a[href="verification.php"] .badge');
    if (verificationBadge) {
        if (counts.pending_verifications > 0) {
            verificationBadge.textContent = counts.pending_verifications;
            verificationBadge.style.display = 'inline-block';
        } else {
            verificationBadge.style.display = 'none';
        }
    }
    
    // 可以添加其他徽章更新逻辑
}

// 侧边栏切换（移动端）
function toggleSidebar() {
    const sidebar = document.querySelector('.sidebar');
    const mainContent = document.querySelector('.main-content');
    
    sidebar.classList.toggle('show');
    mainContent.classList.toggle('sidebar-open');
}

// 确认对话框
function confirmAction(message, callback) {
    if (confirm(message)) {
        callback();
    }
}

// 显示加载状态
function showLoading(element, text = '处理中...') {
    const originalContent = element.innerHTML;
    element.dataset.originalContent = originalContent;
    element.disabled = true;
    element.innerHTML = `<i class="fas fa-spinner fa-spin"></i> ${text}`;
}

// 隐藏加载状态
function hideLoading(element) {
    const originalContent = element.dataset.originalContent;
    if (originalContent) {
        element.innerHTML = originalContent;
        element.disabled = false;
        delete element.dataset.originalContent;
    }
}

// 显示提示消息
function showToast(message, type = 'info', duration = 3000) {
    // 创建toast元素
    const toast = document.createElement('div');
    toast.className = `toast toast-${type}`;
    toast.innerHTML = `
        <div class="toast-content">
            <i class="fas fa-${getToastIcon(type)}"></i>
            <span>${message}</span>
        </div>
        <button class="toast-close" onclick="this.parentElement.remove()">
            <i class="fas fa-times"></i>
        </button>
    `;
    
    // 添加到页面
    document.body.appendChild(toast);
    
    // 显示动画
    setTimeout(() => {
        toast.classList.add('show');
    }, 100);
    
    // 自动隐藏
    setTimeout(() => {
        toast.classList.remove('show');
        setTimeout(() => {
            toast.remove();
        }, 300);
    }, duration);
}

// 获取toast图标
function getToastIcon(type) {
    const icons = {
        success: 'check-circle',
        error: 'exclamation-circle',
        warning: 'exclamation-triangle',
        info: 'info-circle'
    };
    return icons[type] || 'info-circle';
}

// AJAX请求封装
async function apiRequest(url, options = {}) {
    const defaultOptions = {
        method: 'GET',
        headers: {
            'Content-Type': 'application/json',
        },
    };
    
    const config = { ...defaultOptions, ...options };
    
    try {
        const response = await fetch(url, config);
        const data = await response.json();
        
        if (!response.ok) {
            throw new Error(data.message || '请求失败');
        }
        
        return data;
    } catch (error) {
        console.error('API请求错误:', error);
        throw error;
    }
}

// 表格排序功能
function sortTable(table, column, direction = 'asc') {
    const tbody = table.querySelector('tbody');
    const rows = Array.from(tbody.querySelectorAll('tr'));
    
    rows.sort((a, b) => {
        const aValue = a.cells[column].textContent.trim();
        const bValue = b.cells[column].textContent.trim();
        
        if (direction === 'asc') {
            return aValue.localeCompare(bValue, 'zh-CN', { numeric: true });
        } else {
            return bValue.localeCompare(aValue, 'zh-CN', { numeric: true });
        }
    });
    
    // 重新插入排序后的行
    rows.forEach(row => tbody.appendChild(row));
}

// 数据导出功能
function exportData(type, format = 'excel') {
    const url = `api/export.php?type=${type}&format=${format}`;
    window.open(url, '_blank');
}

// 批量操作
function batchOperation(operation, selectedIds) {
    if (selectedIds.length === 0) {
        showToast('请选择要操作的项目', 'warning');
        return;
    }
    
    const message = `确认对选中的 ${selectedIds.length} 个项目执行 ${operation} 操作吗？`;
    
    if (confirm(message)) {
        // 执行批量操作
        apiRequest('api/batch_operation.php', {
            method: 'POST',
            body: JSON.stringify({
                operation: operation,
                ids: selectedIds
            })
        }).then(response => {
            if (response.success) {
                showToast('批量操作成功', 'success');
                location.reload();
            } else {
                showToast(response.message || '批量操作失败', 'error');
            }
        }).catch(error => {
            showToast('操作失败: ' + error.message, 'error');
        });
    }
}

// 搜索功能
function initializeSearch() {
    const searchInput = document.getElementById('searchInput');
    if (searchInput) {
        let searchTimeout;
        
        searchInput.addEventListener('input', function() {
            clearTimeout(searchTimeout);
            searchTimeout = setTimeout(() => {
                performSearch(this.value);
            }, 300);
        });
    }
}

// 执行搜索
function performSearch(query) {
    const searchableElements = document.querySelectorAll('[data-searchable]');
    
    searchableElements.forEach(element => {
        const text = element.textContent.toLowerCase();
        const match = text.includes(query.toLowerCase());
        
        const row = element.closest('tr, .card, .item');
        if (row) {
            row.style.display = match ? '' : 'none';
        }
    });
}

// 键盘快捷键
document.addEventListener('keydown', function(e) {
    // Ctrl+S 保存
    if (e.ctrlKey && e.key === 's') {
        e.preventDefault();
        const saveBtn = document.querySelector('.btn-save, .btn-submit');
        if (saveBtn) {
            saveBtn.click();
        }
    }
    
    // Ctrl+N 新建
    if (e.ctrlKey && e.key === 'n') {
        e.preventDefault();
        const newBtn = document.querySelector('.btn-new, .btn-add');
        if (newBtn) {
            newBtn.click();
        }
    }
    
    // ESC 关闭模态框
    if (e.key === 'Escape') {
        const openModal = document.querySelector('.modal.show');
        if (openModal) {
            const closeBtn = openModal.querySelector('.modal-close, .btn-cancel');
            if (closeBtn) {
                closeBtn.click();
            }
        }
    }
});

// 页面离开确认
window.addEventListener('beforeunload', function(e) {
    const hasUnsavedChanges = document.querySelector('.form-dirty, .has-changes');
    
    if (hasUnsavedChanges) {
        e.preventDefault();
        e.returnValue = '您有未保存的更改，确定要离开吗？';
        return e.returnValue;
    }
});

// 工具函数
const Utils = {
    // 格式化日期
    formatDate: function(date, format = 'YYYY-MM-DD HH:mm:ss') {
        const d = new Date(date);
        const year = d.getFullYear();
        const month = String(d.getMonth() + 1).padStart(2, '0');
        const day = String(d.getDate()).padStart(2, '0');
        const hours = String(d.getHours()).padStart(2, '0');
        const minutes = String(d.getMinutes()).padStart(2, '0');
        const seconds = String(d.getSeconds()).padStart(2, '0');
        
        return format
            .replace('YYYY', year)
            .replace('MM', month)
            .replace('DD', day)
            .replace('HH', hours)
            .replace('mm', minutes)
            .replace('ss', seconds);
    },
    
    // 格式化文件大小
    formatFileSize: function(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    },
    
    // 防抖函数
    debounce: function(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }
};
