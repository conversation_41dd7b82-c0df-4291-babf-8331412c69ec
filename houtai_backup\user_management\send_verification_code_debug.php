<?php
/**
 * 后台发送验证码API - 调试版本
 * 用于排查JSON错误问题
 */

// 禁用所有错误输出
error_reporting(0);
ini_set('display_errors', 0);

// 清理输出缓冲区
while (ob_get_level()) {
    ob_end_clean();
}

// 设置JSON响应头
header('Content-Type: application/json; charset=utf-8');
header('Cache-Control: no-cache');

session_start();

$response = ['success' => false, 'message' => '', 'debug' => []];

try {
    // 调试信息
    $response['debug']['step'] = '1-开始处理';

    // 检查管理员权限
    if (!isset($_SESSION['admin_id'])) {
        $response['message'] = '未授权访问';
        $response['debug']['error'] = '管理员未登录';
        echo json_encode($response, JSON_UNESCAPED_UNICODE);
        exit;
    }

    $response['debug']['step'] = '2-权限验证通过';
    $response['debug']['admin_id'] = $_SESSION['admin_id'];

    // 获取POST数据
    $input_raw = file_get_contents('php://input');
    $response['debug']['input_raw'] = $input_raw;

    $input = json_decode($input_raw, true);
    $response['debug']['input_parsed'] = $input;

    if (!$input) {
        $response['message'] = '无效的请求数据';
        $response['debug']['error'] = 'JSON解析失败';
        echo json_encode($response, JSON_UNESCAPED_UNICODE);
        exit;
    }

    $response['debug']['step'] = '3-数据解析成功';

    // 获取参数
    $user_id = intval($input['user_id'] ?? 0);
    $phone = trim($input['phone'] ?? '');
    $type = trim($input['type'] ?? '');
    $note = trim($input['note'] ?? '');
    $expiry_minutes = intval($input['expiry'] ?? 5);

    $response['debug']['params'] = [
        'user_id' => $user_id,
        'phone' => $phone,
        'type' => $type,
        'note' => $note,
        'expiry' => $expiry_minutes
    ];

    // 验证必需参数
    if ($user_id <= 0 || empty($phone) || empty($type) || empty($note)) {
        $response['message'] = '请填写完整的验证码信息';
        $response['debug']['error'] = '参数验证失败';
        echo json_encode($response, JSON_UNESCAPED_UNICODE);
        exit;
    }

    $response['debug']['step'] = '4-参数验证通过';

    // 验证手机号格式
    if (!preg_match('/^1[3-9]\d{9}$/', $phone)) {
        $response['message'] = '手机号格式不正确';
        $response['debug']['error'] = '手机号格式错误';
        echo json_encode($response, JSON_UNESCAPED_UNICODE);
        exit;
    }

    $response['debug']['step'] = '5-手机号验证通过';

    // 尝试数据库连接
    try {
        $pdo = new PDO(
            "mysql:host=localhost;dbname=quwanplanet;charset=utf8mb4",
            "quwanplanet",
            "nJmJm23FB4Xn6Fc3",
            [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC
            ]
        );
        $response['debug']['step'] = '6-数据库连接成功';
    } catch (PDOException $e) {
        $response['message'] = '数据库连接失败';
        $response['debug']['error'] = '数据库连接错误: ' . $e->getMessage();
        echo json_encode($response, JSON_UNESCAPED_UNICODE);
        exit;
    }

    // 检查用户是否存在
    try {
        $stmt = $pdo->prepare("SELECT id, username FROM users WHERE id = ?");
        $stmt->execute([$user_id]);
        $user = $stmt->fetch(PDO::FETCH_ASSOC);

        if (!$user) {
            $response['message'] = '用户不存在';
            $response['debug']['error'] = '用户ID不存在: ' . $user_id;
            echo json_encode($response, JSON_UNESCAPED_UNICODE);
            exit;
        }

        $response['debug']['step'] = '7-用户验证通过';
        $response['debug']['user'] = $user;
    } catch (PDOException $e) {
        $response['message'] = '查询用户失败';
        $response['debug']['error'] = '用户查询错误: ' . $e->getMessage();
        echo json_encode($response, JSON_UNESCAPED_UNICODE);
        exit;
    }

    // 生成验证码
    $verification_code = sprintf('%06d', mt_rand(100000, 999999));
    $expires_at = date('Y-m-d H:i:s', time() + ($expiry_minutes * 60));

    $response['debug']['step'] = '8-验证码生成完成';
    $response['debug']['code'] = $verification_code;
    $response['debug']['expires_at'] = $expires_at;

    // 检查表是否存在
    try {
        $stmt = $pdo->query("SHOW TABLES LIKE 'verification_codes'");
        $table_exists = $stmt->rowCount() > 0;

        if (!$table_exists) {
            $response['message'] = 'verification_codes表不存在，请先执行数据库脚本';
            $response['debug']['error'] = '数据库表不存在';
            echo json_encode($response, JSON_UNESCAPED_UNICODE);
            exit;
        }

        $response['debug']['step'] = '9-数据库表检查通过';
    } catch (PDOException $e) {
        $response['message'] = '数据库表检查失败';
        $response['debug']['error'] = '表检查错误: ' . $e->getMessage();
        echo json_encode($response, JSON_UNESCAPED_UNICODE);
        exit;
    }

    // 尝试插入验证码记录（简化版本）
    try {
        $stmt = $pdo->prepare("
            INSERT INTO verification_codes
            (user_id, phone, code, type, sent_by_admin, admin_note, expires_at)
            VALUES (?, ?, ?, ?, ?, ?, ?)
        ");

        $result = $stmt->execute([
            $user_id,
            $phone,
            $verification_code,
            $type,
            $_SESSION['admin_id'],
            $note,
            $expires_at
        ]);

        if ($result) {
            $verification_id = $pdo->lastInsertId();
            $response['debug']['step'] = '10-验证码记录插入成功';
            $response['debug']['verification_id'] = $verification_id;

            // 创建实时通知记录
            try {
                $notification_title = '管理员验证码';
                $notification_content = "您收到一条来自管理员的验证码：{$verification_code}";

                // 根据类型设置不同的通知内容
                switch ($type) {
                    case 'security_verify':
                        $notification_title = '安全验证码';
                        $notification_content = "安全验证码：{$verification_code}，请在{$expiry_minutes}分钟内使用";
                        break;
                    case 'system_notice':
                        $notification_title = '系统通知验证码';
                        $notification_content = "系统验证码：{$verification_code}，有效期{$expiry_minutes}分钟";
                        break;
                    default:
                        $notification_title = '管理员验证码';
                        $notification_content = "管理员发送的验证码：{$verification_code}，请在{$expiry_minutes}分钟内使用";
                }

                $notification_data = [
                    'verification_id' => $verification_id,
                    'code' => $verification_code,
                    'type' => $type,
                    'expires_at' => $expires_at,
                    'admin_note' => $note,
                    'sent_by' => $_SESSION['admin_username'] ?? '管理员'
                ];

                // 检查 priority 字段是否存在
                $stmt = $pdo->query("SHOW COLUMNS FROM realtime_notifications LIKE 'priority'");
                $has_priority = $stmt->rowCount() > 0;

                if ($has_priority) {
                    $stmt = $pdo->prepare("
                        INSERT INTO realtime_notifications
                        (user_id, type, title, content, data, status, priority, expires_at)
                        VALUES (?, 'verification_code', ?, ?, ?, 'pending', 5, ?)
                    ");

                    $notification_result = $stmt->execute([
                        $user_id,
                        $notification_title,
                        $notification_content,
                        json_encode($notification_data),
                        $expires_at
                    ]);
                } else {
                    $stmt = $pdo->prepare("
                        INSERT INTO realtime_notifications
                        (user_id, type, title, content, data, status, expires_at)
                        VALUES (?, 'verification_code', ?, ?, ?, 'pending', ?)
                    ");

                    $notification_result = $stmt->execute([
                        $user_id,
                        $notification_title,
                        $notification_content,
                        json_encode($notification_data),
                        $expires_at
                    ]);
                }

                if ($notification_result) {
                    $notification_id = $pdo->lastInsertId();
                    $response['debug']['step'] = '11-实时通知记录创建成功';
                    $response['debug']['notification_id'] = $notification_id;
                } else {
                    $response['debug']['warning'] = '实时通知记录创建失败';
                }

            } catch (PDOException $e) {
                $response['debug']['warning'] = '实时通知创建错误: ' . $e->getMessage();
            }

            // 记录管理员操作日志
            try {
                $response['debug']['step'] = '12-开始记录操作日志';

                // 获取管理员信息
                $stmt = $pdo->prepare("
                    SELECT username, employee_id, department
                    FROM admin_users
                    WHERE id = ?
                ");
                $stmt->execute([$admin_id]);
                $admin_info = $stmt->fetch();

                $admin_name = $admin_info['username'] ?? '未知管理员';
                $employee_id = $admin_info['employee_id'] ?? null;
                $department = $admin_info['department'] ?? null;

                // 获取客户端信息
                $ip_address = $_SERVER['REMOTE_ADDR'] ?? null;
                $user_agent = $_SERVER['HTTP_USER_AGENT'] ?? null;

                // 构建日志内容
                $log_content = "向用户 {$user['username']}（趣玩ID：{$user['quwan_id']}）发送验证码到手机号 {$phone}，类型：{$type}";
                if (!empty($note)) {
                    $log_content .= "，备注：{$note}";
                }
                $log_content .= "，验证码：{$verification_code}，有效期：{$expiry_minutes}分钟";

                // 插入管理员日志
                $stmt = $pdo->prepare("
                    INSERT INTO admin_logs
                    (admin_id, admin_name, employee_id, department, target_user_id, action, reason, created_at)
                    VALUES (?, ?, ?, ?, ?, ?, ?, NOW())
                ");
                $admin_log_result = $stmt->execute([
                    $admin_id,
                    $admin_name,
                    $employee_id,
                    $department,
                    $user_id,
                    '发送验证码',
                    $log_content
                ]);

                $admin_log_id = $pdo->lastInsertId();
                $response['debug']['admin_log_id'] = $admin_log_id;

                // 插入用户日志
                $stmt = $pdo->prepare("
                    INSERT INTO user_logs
                    (user_id, operator_name, employee_id, department, type, content, ip_address, user_agent, created_at)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, NOW())
                ");
                $user_log_result = $stmt->execute([
                    $user_id,
                    $admin_name,
                    $employee_id,
                    $department,
                    '发送验证码',
                    $log_content,
                    $ip_address,
                    $user_agent
                ]);

                $user_log_id = $pdo->lastInsertId();
                $response['debug']['user_log_id'] = $user_log_id;
                $response['debug']['step'] = '13-操作日志记录成功';

            } catch (PDOException $e) {
                $response['debug']['warning'] = '日志记录失败: ' . $e->getMessage();
                // 不影响主要功能，继续执行
            }

            // 成功响应
            $response['success'] = true;
            $response['message'] = '验证码发送成功';
            $response['data'] = [
                'verification_id' => $verification_id,
                'code' => $verification_code,
                'expires_at' => $expires_at,
                'user' => [
                    'id' => $user_id,
                    'username' => $user['username'],
                    'phone' => $phone
                ],
                'log_info' => [
                    'admin_log_id' => $admin_log_id ?? null,
                    'user_log_id' => $user_log_id ?? null,
                    'operator' => $admin_name ?? '未知',
                    'logged_at' => date('Y-m-d H:i:s')
                ]
            ];
        } else {
            $response['message'] = '验证码记录插入失败';
            $response['debug']['error'] = '插入操作返回false';
        }

    } catch (PDOException $e) {
        $response['message'] = '数据库插入失败';
        $response['debug']['error'] = '插入错误: ' . $e->getMessage();
        $response['debug']['sql_error'] = $e->getCode();
    }

} catch (Exception $e) {
    $response['message'] = '系统错误';
    $response['debug']['error'] = '异常: ' . $e->getMessage();
    $response['debug']['trace'] = $e->getTraceAsString();
}

// 输出JSON响应
echo json_encode($response, JSON_UNESCAPED_UNICODE);
?>
