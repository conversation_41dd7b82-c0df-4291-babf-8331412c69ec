<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>趣玩ID生成测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            max-width: 800px;
            margin: 0 auto;
        }
        .btn {
            background: #40E0D0;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            background: #f9f9f9;
            border-radius: 5px;
            white-space: pre-wrap;
        }
        .valid {
            color: #27ae60;
        }
        .invalid {
            color: #e74c3c;
        }
        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 20px;
        }
        .stat-card {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            border-left: 3px solid #40E0D0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h2>趣玩ID生成测试</h2>
        
        <button class="btn" onclick="testSingleId()">生成单个ID</button>
        <button class="btn" onclick="testBatchIds()">批量生成ID (100个)</button>
        <button class="btn" onclick="testValidation()">测试验证规则</button>
        <button class="btn" onclick="clearResults()">清除结果</button>
        
        <div id="result" class="result" style="display: none;"></div>
    </div>

    <script>
        async function testSingleId() {
            try {
                const response = await fetch('test_id_api.php?action=single');
                const data = await response.json();
                
                const result = document.getElementById('result');
                result.style.display = 'block';
                result.innerHTML = `单个ID生成测试：
生成的ID: ${data.id}
是否有效: ${data.valid ? '✅ 有效' : '❌ 无效'}
生成时间: ${data.generation_time}ms
验证详情: ${JSON.stringify(data.validation_details, null, 2)}`;
            } catch (error) {
                showError('测试失败: ' + error.message);
            }
        }

        async function testBatchIds() {
            try {
                const response = await fetch('test_id_api.php?action=batch');
                const data = await response.json();
                
                const result = document.getElementById('result');
                result.style.display = 'block';
                
                let html = `批量ID生成测试 (${data.total_generated}个)：

统计信息：
- 7位数字: ${data.stats.seven_digit}个
- 8位数字: ${data.stats.eight_digit}个  
- 9位数字: ${data.stats.nine_digit}个
- 有效ID: ${data.stats.valid}个
- 无效ID: ${data.stats.invalid}个
- 平均生成时间: ${data.average_time}ms

生成的ID样例 (前20个)：
`;
                
                data.sample_ids.forEach((id, index) => {
                    html += `${index + 1}. ${id.id} (${id.length}位) ${id.valid ? '✅' : '❌'}\n`;
                });
                
                if (data.invalid_ids.length > 0) {
                    html += `\n无效ID及原因：\n`;
                    data.invalid_ids.forEach(item => {
                        html += `${item.id}: ${item.reason}\n`;
                    });
                }
                
                result.innerHTML = html;
            } catch (error) {
                showError('批量测试失败: ' + error.message);
            }
        }

        async function testValidation() {
            try {
                const response = await fetch('test_id_api.php?action=validation');
                const data = await response.json();
                
                const result = document.getElementById('result');
                result.style.display = 'block';
                
                let html = `验证规则测试：\n\n`;
                
                data.test_cases.forEach(testCase => {
                    html += `ID: ${testCase.id}\n`;
                    html += `预期: ${testCase.expected ? '有效' : '无效'}\n`;
                    html += `实际: ${testCase.actual ? '有效' : '无效'}\n`;
                    html += `结果: ${testCase.passed ? '✅ 通过' : '❌ 失败'}\n`;
                    if (testCase.reason) {
                        html += `原因: ${testCase.reason}\n`;
                    }
                    html += `\n`;
                });
                
                html += `总计: ${data.total_tests}个测试用例\n`;
                html += `通过: ${data.passed_tests}个\n`;
                html += `失败: ${data.failed_tests}个\n`;
                html += `通过率: ${data.pass_rate}%`;
                
                result.innerHTML = html;
            } catch (error) {
                showError('验证测试失败: ' + error.message);
            }
        }

        function clearResults() {
            document.getElementById('result').style.display = 'none';
        }

        function showError(message) {
            const result = document.getElementById('result');
            result.style.display = 'block';
            result.innerHTML = `错误: ${message}`;
            result.style.color = '#e74c3c';
        }
    </script>
</body>
</html>
