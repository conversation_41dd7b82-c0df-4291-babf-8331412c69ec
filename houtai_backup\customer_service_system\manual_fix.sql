-- 手动修复客服系统数据库表结构
-- 请在数据库管理工具中逐条执行以下SQL语句

-- 步骤1: 检查当前表结构
DESCRIBE realtime_notifications;
DESCRIBE customer_service_messages;

-- 步骤2: 添加 message 字段到 realtime_notifications 表
-- 如果提示字段已存在，可以忽略错误
ALTER TABLE realtime_notifications 
ADD COLUMN message TEXT COMMENT '通知消息内容' 
AFTER title;

-- 步骤3: 修复 customer_service_messages 表的 content 字段
-- 只设置为允许NULL，不设置默认值（TEXT类型不支持默认值）
ALTER TABLE customer_service_messages 
MODIFY COLUMN content TEXT NULL COMMENT '消息内容';

-- 步骤4: 验证修复结果
DESCRIBE realtime_notifications;
DESCRIBE customer_service_messages;

-- 步骤5: 检查等待中的会话（用于测试）
SELECT session_id, user_name, status, priority, started_at 
FROM customer_service_sessions 
WHERE status = 'waiting' 
ORDER BY started_at DESC 
LIMIT 3;
