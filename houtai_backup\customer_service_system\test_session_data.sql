-- 测试会话数据
-- 用于测试客服系统的接受和结束会话功能

-- 插入测试用户（如果不存在）
INSERT IGNORE INTO users (id, phone, username, nickname, created_at) VALUES 
(999, '13800138000', 'test_user', '测试用户', NOW());

-- 插入测试会话数据
INSERT INTO customer_service_sessions (
    session_id, 
    user_id, 
    user_phone, 
    user_name, 
    status, 
    priority, 
    source, 
    message_count, 
    started_at, 
    created_at
) VALUES 
-- 等待中的会话（可以被接受）
('session_test_waiting_001', 999, '13800138000', '测试用户', 'waiting', 'normal', 'web', 1, NOW(), NOW()),
('session_test_waiting_002', 999, '13800138000', '测试用户', 'waiting', 'high', 'web', 2, DATE_SUB(NOW(), INTERVAL 5 MINUTE), DATE_SUB(NOW(), INTERVAL 5 MINUTE)),
('session_test_waiting_003', 999, '13800138000', '测试用户', 'waiting', 'urgent', 'web', 1, DATE_SUB(NOW(), INTERVAL 2 MINUTE), DATE_SUB(NOW(), INTERVAL 2 MINUTE)),

-- 进行中的会话（可以被结束）
('session_test_active_001', 999, '13800138000', '测试用户', 'active', 'normal', 'web', 5, DATE_SUB(NOW(), INTERVAL 10 MINUTE), DATE_SUB(NOW(), INTERVAL 10 MINUTE)),
('session_test_active_002', 999, '13800138000', '测试用户', 'active', 'high', 'web', 8, DATE_SUB(NOW(), INTERVAL 15 MINUTE), DATE_SUB(NOW(), INTERVAL 15 MINUTE)),

-- 已结束的会话（只能查看）
('session_test_closed_001', 999, '13800138000', '测试用户', 'closed', 'normal', 'web', 12, DATE_SUB(NOW(), INTERVAL 1 HOUR), DATE_SUB(NOW(), INTERVAL 1 HOUR));

-- 插入测试消息
INSERT INTO customer_service_messages (
    session_id, 
    sender_type, 
    sender_id, 
    sender_name, 
    message_type, 
    content, 
    created_at
) VALUES 
-- 等待中会话的消息
('session_test_waiting_001', 'user', 999, '测试用户', 'text', '你好，我需要帮助', NOW()),
('session_test_waiting_002', 'user', 999, '测试用户', 'text', '请问如何使用这个功能？', DATE_SUB(NOW(), INTERVAL 5 MINUTE)),
('session_test_waiting_002', 'bot', NULL, '趣玩小助手', 'text', '您好！我是智能客服，正在为您转接人工客服...', DATE_SUB(NOW(), INTERVAL 4 MINUTE)),
('session_test_waiting_003', 'user', 999, '测试用户', 'text', '紧急问题，请尽快处理！', DATE_SUB(NOW(), INTERVAL 2 MINUTE)),

-- 进行中会话的消息
('session_test_active_001', 'user', 999, '测试用户', 'text', '你好', DATE_SUB(NOW(), INTERVAL 10 MINUTE)),
('session_test_active_001', 'customer_service', 1, '客服小王', 'text', '您好！很高兴为您服务', DATE_SUB(NOW(), INTERVAL 9 MINUTE)),
('session_test_active_001', 'user', 999, '测试用户', 'text', '我想了解一下产品功能', DATE_SUB(NOW(), INTERVAL 8 MINUTE)),
('session_test_active_001', 'customer_service', 1, '客服小王', 'text', '好的，我来为您详细介绍', DATE_SUB(NOW(), INTERVAL 7 MINUTE)),
('session_test_active_001', 'user', 999, '测试用户', 'text', '谢谢', DATE_SUB(NOW(), INTERVAL 6 MINUTE)),

('session_test_active_002', 'user', 999, '测试用户', 'text', '我遇到了一个问题', DATE_SUB(NOW(), INTERVAL 15 MINUTE)),
('session_test_active_002', 'customer_service', 1, '客服小王', 'text', '请详细描述一下问题', DATE_SUB(NOW(), INTERVAL 14 MINUTE)),
('session_test_active_002', 'user', 999, '测试用户', 'text', '登录时提示密码错误', DATE_SUB(NOW(), INTERVAL 13 MINUTE)),
('session_test_active_002', 'customer_service', 1, '客服小王', 'text', '我来帮您检查一下账户状态', DATE_SUB(NOW(), INTERVAL 12 MINUTE)),
('session_test_active_002', 'user', 999, '测试用户', 'text', '好的，谢谢', DATE_SUB(NOW(), INTERVAL 11 MINUTE)),
('session_test_active_002', 'customer_service', 1, '客服小王', 'text', '您的账户正常，建议重置密码', DATE_SUB(NOW(), INTERVAL 10 MINUTE)),
('session_test_active_002', 'user', 999, '测试用户', 'text', '如何重置？', DATE_SUB(NOW(), INTERVAL 9 MINUTE)),
('session_test_active_002', 'customer_service', 1, '客服小王', 'text', '点击登录页面的"忘记密码"链接', DATE_SUB(NOW(), INTERVAL 8 MINUTE)),

-- 已结束会话的消息
('session_test_closed_001', 'user', 999, '测试用户', 'text', '你好，我需要咨询', DATE_SUB(NOW(), INTERVAL 1 HOUR)),
('session_test_closed_001', 'customer_service', 1, '客服小王', 'text', '您好！请问有什么可以帮助您的？', DATE_SUB(NOW(), INTERVAL 59 MINUTE)),
('session_test_closed_001', 'user', 999, '测试用户', 'text', '我想了解会员权益', DATE_SUB(NOW(), INTERVAL 58 MINUTE)),
('session_test_closed_001', 'customer_service', 1, '客服小王', 'text', '我们的会员分为三个等级...', DATE_SUB(NOW(), INTERVAL 57 MINUTE)),
('session_test_closed_001', 'user', 999, '测试用户', 'text', '明白了，谢谢', DATE_SUB(NOW(), INTERVAL 56 MINUTE)),
('session_test_closed_001', 'customer_service', 1, '客服小王', 'text', '不客气！还有其他问题吗？', DATE_SUB(NOW(), INTERVAL 55 MINUTE)),
('session_test_closed_001', 'user', 999, '测试用户', 'text', '没有了，谢谢', DATE_SUB(NOW(), INTERVAL 54 MINUTE)),
('session_test_closed_001', 'customer_service', 1, '客服小王', 'text', '好的，祝您使用愉快！', DATE_SUB(NOW(), INTERVAL 53 MINUTE)),
('session_test_closed_001', 'system', NULL, '系统', 'system', '客服 客服小王 已结束会话', DATE_SUB(NOW(), INTERVAL 52 MINUTE));

-- 更新会话的消息数量
UPDATE customer_service_sessions SET message_count = (
    SELECT COUNT(*) FROM customer_service_messages 
    WHERE customer_service_messages.session_id = customer_service_sessions.session_id
) WHERE session_id LIKE 'session_test_%';

-- 为进行中的会话分配客服
UPDATE customer_service_sessions 
SET customer_service_id = 1 
WHERE session_id IN ('session_test_active_001', 'session_test_active_002');

-- 为已结束的会话设置结束时间
UPDATE customer_service_sessions 
SET ended_at = DATE_SUB(NOW(), INTERVAL 52 MINUTE)
WHERE session_id = 'session_test_closed_001';

-- 显示插入的测试数据
SELECT 
    session_id,
    user_name,
    status,
    priority,
    message_count,
    started_at,
    CASE 
        WHEN customer_service_id IS NULL THEN '未分配'
        ELSE CONCAT('客服ID: ', customer_service_id)
    END as assigned_cs
FROM customer_service_sessions 
WHERE session_id LIKE 'session_test_%'
ORDER BY started_at DESC;
