<?php
/**
 * 调试get_user_ips.php文件
 */

// 模拟管理员登录
session_start();
$_SESSION['admin_id'] = 1; // 模拟管理员ID

// 设置用户ID进行测试
$test_user_id = 1; // 测试用户ID 1

echo "<h2>调试 get_user_ips.php</h2>";

// 测试文件包含
echo "<h3>1. 测试文件包含</h3>";
try {
    require_once '../db_config.php';
    echo "✅ db_config.php 包含成功<br>";
} catch (Exception $e) {
    echo "❌ db_config.php 包含失败: " . $e->getMessage() . "<br>";
}

try {
    require_once '../../frontend/login/login_logger.php';
    echo "✅ login_logger.php 包含成功<br>";
} catch (Exception $e) {
    echo "❌ login_logger.php 包含失败: " . $e->getMessage() . "<br>";
}

// 测试数据库连接
echo "<h3>2. 测试数据库连接</h3>";
try {
    $pdo = getDbConnection();
    echo "✅ 数据库连接成功<br>";
} catch (Exception $e) {
    echo "❌ 数据库连接失败: " . $e->getMessage() . "<br>";
    exit;
}

// 测试login_logs表是否存在
echo "<h3>3. 测试login_logs表</h3>";
try {
    $stmt = $pdo->query("SHOW TABLES LIKE 'login_logs'");
    if ($stmt->rowCount() > 0) {
        echo "✅ login_logs表存在<br>";
        
        // 检查表结构
        $stmt = $pdo->query("DESCRIBE login_logs");
        $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
        echo "表结构:<br>";
        foreach ($columns as $column) {
            echo "- " . $column['Field'] . " (" . $column['Type'] . ")<br>";
        }
    } else {
        echo "❌ login_logs表不存在<br>";
    }
} catch (Exception $e) {
    echo "❌ 检查login_logs表失败: " . $e->getMessage() . "<br>";
}

// 测试查询用户登录记录
echo "<h3>4. 测试查询用户 {$test_user_id} 的登录记录</h3>";
try {
    $stmt = $pdo->prepare("
        SELECT
            ip_address,
            MAX(login_time) as login_time,
            user_agent,
            location,
            COUNT(*) as login_count
        FROM login_logs
        WHERE user_id = ? AND status = 'success'
        GROUP BY ip_address, user_agent
        ORDER BY MAX(login_time) DESC
        LIMIT 50
    ");
    $stmt->execute([$test_user_id]);
    $logs = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "找到 " . count($logs) . " 条记录<br>";
    
    if (count($logs) > 0) {
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr><th>IP地址</th><th>登录时间</th><th>User Agent</th><th>位置</th><th>登录次数</th></tr>";
        foreach ($logs as $log) {
            echo "<tr>";
            echo "<td>" . htmlspecialchars($log['ip_address']) . "</td>";
            echo "<td>" . htmlspecialchars($log['login_time']) . "</td>";
            echo "<td style='max-width: 300px; word-break: break-all;'>" . htmlspecialchars(substr($log['user_agent'], 0, 100)) . "...</td>";
            echo "<td>" . htmlspecialchars($log['location'] ?: '未知') . "</td>";
            echo "<td>" . htmlspecialchars($log['login_count']) . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "❌ 没有找到用户 {$test_user_id} 的登录记录<br>";
        
        // 检查是否有任何登录记录
        $stmt = $pdo->query("SELECT COUNT(*) as total FROM login_logs");
        $total = $stmt->fetch();
        echo "数据库中总共有 " . $total['total'] . " 条登录记录<br>";
        
        if ($total['total'] > 0) {
            // 显示最近的几条记录
            $stmt = $pdo->query("SELECT user_id, ip_address, login_time, status FROM login_logs ORDER BY login_time DESC LIMIT 5");
            $recent = $stmt->fetchAll(PDO::FETCH_ASSOC);
            echo "最近的5条记录:<br>";
            foreach ($recent as $record) {
                echo "- 用户ID: {$record['user_id']}, IP: {$record['ip_address']}, 时间: {$record['login_time']}, 状态: {$record['status']}<br>";
            }
        }
    }
} catch (Exception $e) {
    echo "❌ 查询失败: " . $e->getMessage() . "<br>";
}

// 测试函数
echo "<h3>5. 测试解析函数</h3>";
try {
    $test_ua = "Mozilla/5.0 (iPhone; CPU iPhone OS 16_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148 MicroMessenger/8.0.29";
    
    // 测试parseUserAgent函数
    if (function_exists('parseUserAgent')) {
        $result = parseUserAgent($test_ua);
        echo "✅ parseUserAgent函数工作正常<br>";
        echo "- 设备: " . $result['device'] . "<br>";
        echo "- 系统: " . $result['os'] . "<br>";
        echo "- 浏览器: " . $result['browser'] . "<br>";
        echo "- 微信: " . ($result['is_wechat'] ? '是' : '否') . "<br>";
    } else {
        echo "❌ parseUserAgent函数不存在<br>";
    }
    
    // 测试getLocationFromIP函数
    if (function_exists('getLocationFromIP')) {
        $test_ip = "*******";
        $location = getLocationFromIP($test_ip);
        echo "✅ getLocationFromIP函数工作正常<br>";
        echo "- 测试IP {$test_ip} 的位置: {$location}<br>";
    } else {
        echo "❌ getLocationFromIP函数不存在<br>";
    }
    
} catch (Exception $e) {
    echo "❌ 函数测试失败: " . $e->getMessage() . "<br>";
}

// 测试完整的API调用
echo "<h3>6. 测试完整API调用</h3>";
$_GET['user_id'] = $test_user_id;

echo "<a href='get_user_ips.php?user_id={$test_user_id}' target='_blank'>点击测试 get_user_ips.php API</a><br>";

echo "<h3>7. 添加测试数据</h3>";
echo "<a href='add_test_login_record.php?user_id={$test_user_id}' target='_blank'>点击添加测试登录记录</a><br>";

?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; }
h2, h3 { color: #333; }
table { margin: 10px 0; }
th, td { padding: 8px; text-align: left; }
th { background-color: #f2f2f2; }
</style>
