<?php
// 最终测试页面
header('Content-Type: text/html; charset=UTF-8');
ini_set('default_charset', 'UTF-8');

session_start();

// 模拟用户登录
if (!isset($_SESSION['user_id'])) {
    $_SESSION['user_id'] = 4;
    $_SESSION['user_name'] = '测试用户';
    $_SESSION['nickname'] = '测试用户';
}

require_once '../../houtai_backup/db_config.php';

echo '<h1>🎯 最终测试 - 确保消息能正常收发</h1>';

try {
    $pdo = getDbConnection();

    // 创建一个新的测试会话（避免重复键错误）
    $testSessionId = 'final_test_' . time() . '_' . rand(1000, 9999);

    // 先检查是否已存在
    $stmt = $pdo->prepare("SELECT session_id FROM customer_service_sessions WHERE session_id = ?");
    $stmt->execute([$testSessionId]);

    if (!$stmt->fetch()) {
        $stmt = $pdo->prepare("
            INSERT INTO customer_service_sessions
            (session_id, user_id, user_name, status, priority, source, started_at)
            VALUES (?, ?, ?, 'active', 'normal', 'web', NOW())
        ");
        $stmt->execute([
            $testSessionId,
            $_SESSION['user_id'],
            $_SESSION['user_name']
        ]);
    }

    echo '<div style="background: #d4edda; padding: 15px; border-radius: 8px; margin: 10px 0;">';
    echo '<h3>✅ 新测试会话创建成功</h3>';
    echo '<p><strong>会话ID:</strong> ' . htmlspecialchars($testSessionId) . '</p>';
    echo '<p><strong>状态:</strong> active（已激活）</p>';
    echo '</div>';

    // 获取所有会话
    $stmt = $pdo->prepare("
        SELECT session_id, status, customer_service_id, started_at, message_count
        FROM customer_service_sessions
        WHERE user_id = ?
        ORDER BY started_at DESC
        LIMIT 5
    ");
    $stmt->execute([$_SESSION['user_id']]);
    $sessions = $stmt->fetchAll(PDO::FETCH_ASSOC);

    if (!empty($sessions)) {
        echo '<h2>📋 您的客服会话</h2>';

        foreach ($sessions as $session) {
            $statusColor = $session['status'] === 'active' ? 'green' : ($session['status'] === 'waiting' ? 'orange' : 'gray');

            echo '<div style="border: 1px solid #ddd; padding: 15px; margin: 10px 0; border-radius: 8px; background: #f9f9f9;">';
            echo '<h4>会话: ' . htmlspecialchars($session['session_id']) . '</h4>';
            echo '<p><strong>状态:</strong> <span style="color: ' . $statusColor . ';">' . htmlspecialchars($session['status']) . '</span></p>';
            echo '<p><strong>客服ID:</strong> ' . htmlspecialchars($session['customer_service_id'] ?? '未分配') . '</p>';
            echo '<p><strong>消息数量:</strong> ' . htmlspecialchars($session['message_count']) . '</p>';
            echo '<p><strong>开始时间:</strong> ' . htmlspecialchars($session['started_at']) . '</p>';

            echo '<div style="margin-top: 15px;">';
            echo '<a href="chat_fixed.php?session_id=' . urlencode($session['session_id']) . '" style="background: #6F7BF5; color: white; padding: 8px 16px; text-decoration: none; border-radius: 5px; margin-right: 10px;">💬 修复版聊天</a>';
            echo '<a href="chat_realtime.php?session_id=' . urlencode($session['session_id']) . '" style="background: #28a745; color: white; padding: 8px 16px; text-decoration: none; border-radius: 5px; margin-right: 10px;">🚀 SSE聊天</a>';
            echo '<a href="debug_sse.php?session_id=' . urlencode($session['session_id']) . '" style="background: #ffc107; color: black; padding: 8px 16px; text-decoration: none; border-radius: 5px; margin-right: 10px;">🔧 SSE调试</a>';
            echo '<button onclick="testAPI(\'' . $session['session_id'] . '\')" style="background: #17a2b8; color: white; padding: 8px 16px; border: none; border-radius: 5px; cursor: pointer;">🧪 测试API</button>';
            echo '</div>';

            echo '</div>';
        }
    }

} catch (Exception $e) {
    echo '<div style="background: #f8d7da; padding: 15px; border-radius: 8px; margin: 10px 0; color: #721c24;">';
    echo '<h3>❌ 错误</h3>';
    echo '<p>' . htmlspecialchars($e->getMessage()) . '</p>';
    echo '</div>';
}
?>

<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>最终测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-plan {
            background: #e7f3ff;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
            border-left: 4px solid #6F7BF5;
        }
        .step {
            background: #fff3cd;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
            border-left: 4px solid #ffc107;
        }
        .result {
            margin: 10px 0;
            padding: 15px;
            border-radius: 8px;
        }
        .success { background: #d4edda; color: #155724; border-left: 4px solid #28a745; }
        .error { background: #f8d7da; color: #721c24; border-left: 4px solid #dc3545; }
        .info { background: #d1ecf1; color: #0c5460; border-left: 4px solid #17a2b8; }
        pre {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 5px;
            overflow-x: auto;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="test-plan">
            <h3>🎯 最终测试计划</h3>
            <p>我们已经创建了多个版本的聊天系统，现在来测试哪个能正常工作：</p>
            <ol>
                <li><strong>修复版聊天</strong>：优化的轮询系统，2秒检查一次</li>
                <li><strong>SSE聊天</strong>：Server-Sent Events 实时推送</li>
                <li><strong>SSE调试</strong>：调试SSE连接问题</li>
                <li><strong>API测试</strong>：直接测试消息获取接口</li>
            </ol>
        </div>

        <div class="step">
            <h3>📝 测试步骤</h3>
            <ol>
                <li><strong>选择聊天方式</strong>：点击上面的聊天按钮</li>
                <li><strong>打开客服后台</strong>：<a href="../../houtai_backup/customer_service_system/quick_message_test.php" target="_blank">客服发送消息</a></li>
                <li><strong>发送测试消息</strong>：在客服后台发送消息给用户</li>
                <li><strong>验证接收</strong>：前台应该能收到消息</li>
                <li><strong>测试双向</strong>：前台发送消息，客服后台应能看到</li>
            </ol>
        </div>

        <div id="test-result"></div>

        <div class="info">
            <h3>💡 预期结果</h3>
            <ul>
                <li><strong>修复版聊天</strong>：应该能稳定工作，2秒内收到消息</li>
                <li><strong>SSE聊天</strong>：如果服务器支持，应该能实时收到消息</li>
                <li><strong>API测试</strong>：应该能正确返回消息数据</li>
            </ul>
        </div>

        <p style="margin-top: 30px;">
            <a href="index.php">返回客服首页</a> |
            <a href="../../houtai_backup/customer_service_system/sessions.php">客服后台</a> |
            <a href="../../houtai_backup/customer_service_system/quick_message_test.php">快速发送消息</a>
        </p>
    </div>

    <script>
        async function testAPI(sessionId) {
            const resultDiv = document.getElementById('test-result');
            resultDiv.innerHTML = '<div class="result info">🔄 测试消息获取API...</div>';

            try {
                // 测试API
                const response = await fetch(`api/get_new_messages.php?session_id=${sessionId}&last_message_id=0`);
                const data = await response.json();

                console.log('API测试响应:', data);

                if (data.success) {
                    resultDiv.innerHTML = `
                        <div class="result success">
                            <h3>✅ API测试成功</h3>
                            <p><strong>会话ID：</strong>${data.session_id}</p>
                            <p><strong>消息数量：</strong>${data.count}</p>
                            <p><strong>查询方式：</strong>${data.debug.query_type}</p>
                            <p><strong>用户ID：</strong>${data.debug.user_id}</p>
                            <pre>${JSON.stringify(data, null, 2)}</pre>
                            <div style="margin-top: 15px;">
                                <a href="chat_fixed.php?session_id=${sessionId}" style="background: #6F7BF5; color: white; padding: 8px 16px; text-decoration: none; border-radius: 5px; margin-right: 10px;">💬 打开修复版聊天</a>
                                <a href="../../houtai_backup/customer_service_system/quick_message_test.php" target="_blank" style="background: #dc3545; color: white; padding: 8px 16px; text-decoration: none; border-radius: 5px;">📤 发送测试消息</a>
                            </div>
                        </div>
                    `;
                } else {
                    resultDiv.innerHTML = `
                        <div class="result error">
                            <h3>❌ API测试失败</h3>
                            <p><strong>错误：</strong>${data.error}</p>
                            <p><strong>消息：</strong>${data.message || 'N/A'}</p>
                            <pre>${JSON.stringify(data, null, 2)}</pre>
                        </div>
                    `;
                }

            } catch (error) {
                console.error('API测试失败:', error);
                resultDiv.innerHTML = `
                    <div class="result error">
                        <h3>❌ 网络错误</h3>
                        <p>${error.message}</p>
                    </div>
                `;
            }
        }
    </script>
</body>
</html>
