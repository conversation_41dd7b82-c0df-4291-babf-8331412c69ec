<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .container { max-width: 800px; margin: 0 auto; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .button { background: #007cba; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer; margin: 5px; }
        .button:hover { background: #005a87; }
        .result { margin-top: 15px; padding: 10px; background: #f5f5f5; border-radius: 5px; white-space: pre-wrap; }
        .error { background: #ffe6e6; border: 1px solid #ff9999; }
        .success { background: #e6ffe6; border: 1px solid #99ff99; }
    </style>
</head>
<body>
    <div class="container">
        <h1>后台IP记录和设备记录API测试</h1>
        
        <div class="test-section">
            <h3>1. 管理员登录状态检查</h3>
            <button class="button" onclick="checkAdminLogin()">检查管理员登录状态</button>
            <div id="adminResult" class="result" style="display: none;"></div>
        </div>
        
        <div class="test-section">
            <h3>2. 直接测试API</h3>
            <label>用户ID: <input type="number" id="userId" value="1" min="1"></label>
            <button class="button" onclick="testAPI()">测试 get_user_ips.php</button>
            <div id="apiResult" class="result" style="display: none;"></div>
        </div>
        
        <div class="test-section">
            <h3>3. 添加测试数据</h3>
            <button class="button" onclick="addTestData()">为用户添加测试登录记录</button>
            <div id="testDataResult" class="result" style="display: none;"></div>
        </div>
        
        <div class="test-section">
            <h3>4. 模拟管理员登录</h3>
            <button class="button" onclick="simulateAdminLogin()">模拟管理员登录</button>
            <div id="loginResult" class="result" style="display: none;"></div>
        </div>
        
        <div class="test-section">
            <h3>5. 检查数据库</h3>
            <button class="button" onclick="checkDatabase()">检查数据库状态</button>
            <div id="dbResult" class="result" style="display: none;"></div>
        </div>
    </div>

    <script>
        function checkAdminLogin() {
            const resultDiv = document.getElementById('adminResult');
            resultDiv.style.display = 'block';
            resultDiv.textContent = '检查中...';
            
            fetch('check_admin_status.php')
                .then(response => response.text())
                .then(data => {
                    resultDiv.textContent = data;
                    resultDiv.className = 'result success';
                })
                .catch(error => {
                    resultDiv.textContent = '检查失败: ' + error.message;
                    resultDiv.className = 'result error';
                });
        }
        
        function testAPI() {
            const userId = document.getElementById('userId').value;
            const resultDiv = document.getElementById('apiResult');
            resultDiv.style.display = 'block';
            resultDiv.textContent = '测试中...';
            
            fetch(`get_user_ips.php?user_id=${userId}`)
                .then(response => response.json())
                .then(data => {
                    resultDiv.textContent = JSON.stringify(data, null, 2);
                    resultDiv.className = data.success ? 'result success' : 'result error';
                })
                .catch(error => {
                    resultDiv.textContent = '请求失败: ' + error.message;
                    resultDiv.className = 'result error';
                });
        }
        
        function addTestData() {
            const userId = document.getElementById('userId').value;
            const resultDiv = document.getElementById('testDataResult');
            resultDiv.style.display = 'block';
            resultDiv.textContent = '添加中...';
            
            fetch(`add_test_login_record.php?user_id=${userId}`)
                .then(response => response.text())
                .then(data => {
                    resultDiv.textContent = data;
                    resultDiv.className = 'result success';
                })
                .catch(error => {
                    resultDiv.textContent = '添加失败: ' + error.message;
                    resultDiv.className = 'result error';
                });
        }
        
        function simulateAdminLogin() {
            const resultDiv = document.getElementById('loginResult');
            resultDiv.style.display = 'block';
            resultDiv.textContent = '模拟登录中...';
            
            fetch('simulate_admin_login.php')
                .then(response => response.text())
                .then(data => {
                    resultDiv.textContent = data;
                    resultDiv.className = 'result success';
                })
                .catch(error => {
                    resultDiv.textContent = '模拟登录失败: ' + error.message;
                    resultDiv.className = 'result error';
                });
        }
        
        function checkDatabase() {
            const resultDiv = document.getElementById('dbResult');
            resultDiv.style.display = 'block';
            resultDiv.textContent = '检查中...';
            
            fetch('debug_get_user_ips.php')
                .then(response => response.text())
                .then(data => {
                    resultDiv.innerHTML = data;
                    resultDiv.className = 'result success';
                })
                .catch(error => {
                    resultDiv.textContent = '检查失败: ' + error.message;
                    resultDiv.className = 'result error';
                });
        }
    </script>
</body>
</html>
