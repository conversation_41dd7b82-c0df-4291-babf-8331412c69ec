<?php
session_start();

// 检查用户是否已登录
if (!isset($_SESSION['user_id'])) {
    header('Location: ../../login/index.php');
    exit;
}
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <meta name="theme-color" content="#7B68EE">
    <title>铭牌 - 趣玩商城</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'PingFang SC', 'Helvetica Neue', Arial, sans-serif;
        }
        
        body {
            background-color: #f8f9fa;
            color: #333333;
            line-height: 1.6;
            min-height: 100vh;
        }
        
        .header-bar {
            display: flex;
            align-items: center;
            padding: 15px;
            background-color: #7B68EE;
            color: white;
            position: sticky;
            top: 0;
            z-index: 100;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }
        
        .back-button {
            margin-right: 15px;
            font-size: 18px;
            color: white;
            text-decoration: none;
        }
        
        .header-title {
            font-size: 18px;
            font-weight: 600;
            flex-grow: 1;
        }
        
        .search-bar {
            display: flex;
            align-items: center;
            background-color: white;
            border-radius: 20px;
            margin: 15px;
            padding: 10px 15px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
        }
        
        .search-bar i {
            color: #999;
            margin-right: 10px;
        }
        
        .search-bar input {
            border: none;
            outline: none;
            flex-grow: 1;
            font-size: 15px;
        }
        
        .filter-tabs {
            display: flex;
            padding: 0 15px;
            margin-bottom: 15px;
            overflow-x: auto;
            scrollbar-width: none; /* Firefox */
            -ms-overflow-style: none; /* IE and Edge */
        }
        
        .filter-tabs::-webkit-scrollbar {
            display: none; /* Chrome, Safari, Opera */
        }
        
        .filter-tab {
            padding: 8px 15px;
            margin-right: 10px;
            background-color: white;
            border-radius: 20px;
            font-size: 14px;
            color: #666;
            white-space: nowrap;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
        }
        
        .filter-tab.active {
            background-color: #7B68EE;
            color: white;
        }
        
        .nameplate-list {
            padding: 0 15px;
            margin-bottom: 20px;
        }
        
        .nameplate-card {
            background-color: white;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
            margin-bottom: 15px;
            transition: transform 0.3s ease;
        }
        
        .nameplate-card:active {
            transform: scale(0.98);
        }
        
        .nameplate-preview {
            position: relative;
            width: 100%;
            height: 120px;
            background-color: #f5f5f5;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .nameplate-preview img {
            max-width: 90%;
            max-height: 90%;
            object-fit: contain;
        }
        
        .nameplate-info {
            padding: 15px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .nameplate-details {
            flex-grow: 1;
        }
        
        .nameplate-title {
            font-size: 16px;
            font-weight: 600;
            margin-bottom: 5px;
            color: #333;
        }
        
        .nameplate-desc {
            font-size: 13px;
            color: #999;
            margin-bottom: 8px;
        }
        
        .nameplate-price {
            color: #FF6B00;
            font-size: 18px;
            font-weight: bold;
        }
        
        .nameplate-price small {
            font-size: 13px;
            font-weight: normal;
        }
        
        .buy-button {
            background: linear-gradient(90deg, #7B68EE, #55E4F5);
            color: white;
            border: none;
            border-radius: 20px;
            padding: 8px 20px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            box-shadow: 0 2px 8px rgba(123, 104, 238, 0.3);
        }
        
        .nameplate-tag {
            position: absolute;
            top: 10px;
            right: 10px;
            background-color: #FF6B00;
            color: white;
            font-size: 12px;
            padding: 3px 8px;
            border-radius: 10px;
            z-index: 1;
        }
        
        .empty-notice {
            text-align: center;
            padding: 30px 15px;
            color: #999;
            font-size: 15px;
        }
    </style>
    <!-- 引入字体图标库 -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <!-- 顶部导航栏 -->
    <div class="header-bar">
        <a href="../index.php" class="back-button">
            <i class="fas fa-arrow-left"></i>
        </a>
        <div class="header-title">铭牌</div>
    </div>
    
    <!-- 搜索栏 -->
    <div class="search-bar">
        <i class="fas fa-search"></i>
        <input type="text" placeholder="搜索铭牌">
    </div>
    
    <!-- 筛选标签 -->
    <div class="filter-tabs">
        <div class="filter-tab active">全部</div>
        <div class="filter-tab">限时</div>
        <div class="filter-tab">会员专属</div>
        <div class="filter-tab">活动奖励</div>
        <div class="filter-tab">新品</div>
    </div>
    
    <!-- 铭牌列表 -->
    <div class="nameplate-list">
        <div class="nameplate-card">
            <div class="nameplate-preview">
                <img src="https://s1.imagehub.cc/images/2025/05/16/nameplate_1.png" alt="铭牌预览">
                <div class="nameplate-tag">限时</div>
            </div>
            <div class="nameplate-info">
                <div class="nameplate-details">
                    <div class="nameplate-title">至尊玩家铭牌</div>
                    <div class="nameplate-desc">专属标识，彰显您的尊贵身份</div>
                    <div class="nameplate-price"><small>¥</small>128</div>
                </div>
                <button class="buy-button">购买</button>
            </div>
        </div>
        
        <div class="nameplate-card">
            <div class="nameplate-preview">
                <img src="https://s1.imagehub.cc/images/2025/05/16/nameplate_2.png" alt="铭牌预览">
                <div class="nameplate-tag">新品</div>
            </div>
            <div class="nameplate-info">
                <div class="nameplate-details">
                    <div class="nameplate-title">城市探险家铭牌</div>
                    <div class="nameplate-desc">展示您的城市探索成就</div>
                    <div class="nameplate-price"><small>¥</small>88</div>
                </div>
                <button class="buy-button">购买</button>
            </div>
        </div>
        
        <div class="nameplate-card">
            <div class="nameplate-preview">
                <img src="https://s1.imagehub.cc/images/2025/05/16/nameplate_3.png" alt="铭牌预览">
                <div class="nameplate-tag">会员专属</div>
            </div>
            <div class="nameplate-info">
                <div class="nameplate-details">
                    <div class="nameplate-title">游戏大师铭牌</div>
                    <div class="nameplate-desc">专业玩家的身份象征</div>
                    <div class="nameplate-price"><small>¥</small>108</div>
                </div>
                <button class="buy-button">购买</button>
            </div>
        </div>
    </div>
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 筛选标签点击效果
            const filterTabs = document.querySelectorAll('.filter-tab');
            filterTabs.forEach(tab => {
                tab.addEventListener('click', function() {
                    // 移除所有标签的active类
                    filterTabs.forEach(t => t.classList.remove('active'));
                    
                    // 添加active类到当前标签
                    this.classList.add('active');
                    
                    // 这里可以添加筛选逻辑
                    const filter = this.textContent.trim();
                    // 示例：如果选择了"会员专属"，只显示会员专属的铭牌
                    if (filter !== '全部') {
                        alert(`已选择筛选条件：${filter}\n筛选功能即将上线，敬请期待`);
                    }
                });
            });
            
            // 购买按钮点击效果
            const buyButtons = document.querySelectorAll('.buy-button');
            buyButtons.forEach(button => {
                button.addEventListener('click', function(e) {
                    e.stopPropagation(); // 阻止事件冒泡
                    const nameplate = this.closest('.nameplate-card').querySelector('.nameplate-title').textContent;
                    alert(`购买功能即将上线，敬请期待\n${nameplate}`);
                });
            });
            
            // 铭牌卡片点击效果
            const nameplateCards = document.querySelectorAll('.nameplate-card');
            nameplateCards.forEach(card => {
                card.addEventListener('click', function() {
                    const nameplate = this.querySelector('.nameplate-title').textContent;
                    alert(`铭牌详情页即将上线，敬请期待\n${nameplate}`);
                });
            });
        });
    </script>
</body>
</html>
