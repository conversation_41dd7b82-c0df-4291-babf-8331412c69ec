<?php
session_start();

// 检查用户是否已登录
if (!isset($_SESSION['user_id'])) {
    header('Location: ../login/index.php');
    exit;
}

// 数据库连接配置
$db_config = [
    'host' => 'localhost',
    'username' => 'quwanplanet',
    'password' => 'nJmJm23FB4Xn6Fc3',
    'dbname' => 'quwanplanet',
    'port' => 3306,
    'charset' => 'utf8mb4'
];

// 初始化用户信息变量
$user_username = '用户';
$user_quwan_id = 'N/A';
$user_bio = '这个人很懒，还没有填写个人简介';
$user_display_avatar_url = 'https://s1.imagehub.cc/images/2025/04/26/f5c2f26c4564aabcbb56c5fd30f1d652.jpg'; // 默认头像
$user_nameplate_url = null; // 铭牌URL
$user_nameplate_active = false; // 铭牌是否启用

try {
    $dsn = "mysql:host={$db_config['host']};dbname={$db_config['dbname']};charset={$db_config['charset']};port={$db_config['port']}";
    $pdo = new PDO($dsn, $db_config['username'], $db_config['password']);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

    // 查询需要的字段，包括铭牌信息
    $stmt = $pdo->prepare("SELECT username, quwan_id, bio, avatar, nameplate_url, nameplate_active FROM users WHERE id = :user_id");
    $stmt->execute(['user_id' => $_SESSION['user_id']]);
    $db_user = $stmt->fetch(PDO::FETCH_ASSOC);

    if (!$db_user) {
        session_destroy();
        header('Location: ../login/index.php');
        exit;
    } else {
        $user_username = $db_user['username'];
        $user_quwan_id = $db_user['quwan_id'];
        if (!empty($db_user['bio'])) {
            $user_bio = $db_user['bio'];
        }
        if (!empty($db_user['avatar'])) {
            $user_display_avatar_url = $db_user['avatar']; // 使用数据库中的头像
        }
        if (!empty($db_user['nameplate_url']) && $db_user['nameplate_active'] == 1) {
            $user_nameplate_url = $db_user['nameplate_url']; // 使用数据库中的铭牌
            $user_nameplate_active = true;
        }
    }
} catch (PDOException $e) {
    error_log("个人页面错误: " . $e->getMessage());
    // $db_error = true; // 即使出错，也使用默认值显示页面
}
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <meta name="theme-color" content="#FFFFFF">
    <title>我的 - 趣玩星球</title>
    <link rel="stylesheet" href="css/style.css">
    <?php
    include '../includes/bottom_nav.php';
    echo renderBottomNavCSS();
    ?>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <!-- Toast提示 -->
    <div class="toast" id="toast"></div>

    <!-- 用户信息卡片（带背景图） -->
    <div class="user-card">
        <div class="card-bg"></div>

        <!-- 顶部操作按钮 -->
        <div class="card-actions">
            <a href="../customer_service/index.php" class="action-btn">
                <i class="fas fa-headset"></i>
            </a>
            <a href="settings.php" class="action-btn">
                <i class="fas fa-cog"></i>
            </a>
        </div>

        <!-- 用户头像（居中显示） -->
        <div class="user-avatar-center">
            <div class="avatar-circle">
                <img src="<?php echo htmlspecialchars($user_display_avatar_url); ?>" alt="用户头像">
            </div>
            <?php if ($user_nameplate_active && $user_nameplate_url): ?>
            <div class="user-nameplate">
                <img src="<?php echo htmlspecialchars($user_nameplate_url); ?>" alt="用户铭牌">
            </div>
            <?php endif; ?>
        </div>

        <!-- 用户基本信息（居中显示） -->
        <div class="user-info-center">
            <h2 class="user-name"><?php echo htmlspecialchars($user_username); ?></h2>
            <div class="user-id-container">
                <span class="user-id">趣玩ID: <?php echo htmlspecialchars($user_quwan_id); ?></span>
                <button class="copy-id-btn" data-id="<?php echo htmlspecialchars($user_quwan_id); ?>">
                    <i class="fas fa-copy"></i>
                </button>
            </div>
        </div>

        <!-- 用户统计信息（白色圆角容器） -->
        <div class="stats-container">
            <div class="user-stats">
                <div class="stat-item">
                    <div class="stat-value">0</div>
                    <div class="stat-label">关注</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value">0</div>
                    <div class="stat-label">粉丝</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value">0</div>
                    <div class="stat-label">获赞</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value">0</div>
                    <div class="stat-label">收藏</div>
                </div>
            </div>

            <!-- 用户简介（移到统计信息下方） -->
            <div class="bio-container">
                <div class="user-bio">
                    <?php echo htmlspecialchars($user_bio); ?>
                </div>
            </div>
        </div>
    </div>

    <!-- 会员卡片 -->
    <div class="member-card">
        <div class="member-card-bg"></div>
        <div class="member-info">
            <div class="member-icon">
                <img src="https://s1.imagehub.cc/images/2025/05/15/d1d60276526be44f91b48e51d9aaf423.png" alt="会员图标">
            </div>
            <div class="member-details">
                <div class="member-title">趣玩会员</div>
                <div class="member-level">Lv.1 初级会员</div>
            </div>
            <div class="member-action">
                <button class="member-btn">立即开通</button>
            </div>
        </div>
        <div class="member-benefits">
            <div class="benefit-item">
                <div class="benefit-icon"><i class="fas fa-crown"></i></div>
                <div class="benefit-text">专属特权</div>
            </div>
            <div class="benefit-item">
                <div class="benefit-icon"><i class="fas fa-gift"></i></div>
                <div class="benefit-text">生日礼包</div>
            </div>
            <div class="benefit-item">
                <div class="benefit-icon"><i class="fas fa-tags"></i></div>
                <div class="benefit-text">折扣优惠</div>
            </div>
        </div>
    </div>

    <!-- 金刚区（四个功能） -->
    <div class="feature-grid">
        <a href="../wallet/index.php" class="feature-item">
            <div class="feature-icon">
                <img src="https://s1.imagehub.cc/images/2025/05/02/02e35edf15e5006e668f007dafac68a0.png" alt="钱包图标" class="custom-icon">
            </div>
            <div class="feature-label">钱包</div>
        </a>
        <a href="../orders/index.php" class="feature-item">
            <div class="feature-icon">
                <img src="https://s1.imagehub.cc/images/2025/05/02/6f79ed5045aee8248194b512c0b0ee84.png" alt="订单图标" class="custom-icon">
            </div>
            <div class="feature-label">订单</div>
        </a>
        <a href="../coupons/index.php" class="feature-item">
            <div class="feature-icon">
                <img src="https://s1.imagehub.cc/images/2025/05/02/4a7462a2cb0484130dc99d1516b8b8fd.png" alt="券包图标" class="custom-icon">
            </div>
            <div class="feature-label">券包</div>
        </a>
        <a href="../activities/index.php" class="feature-item">
            <div class="feature-icon">
                <img src="https://s1.imagehub.cc/images/2025/05/02/b98f1eaa2317e690a657c1645b1da2fd.png" alt="活动图标" class="custom-icon">
            </div>
            <div class="feature-label">活动</div>
        </a>
    </div>

    <!-- 功能菜单 -->
    <div class="menu-section">
        <div class="menu-group">
            <a href="../checkin/index.php" class="menu-item">
                <div class="menu-icon">
                    <img src="https://s1.imagehub.cc/images/2025/05/15/edd1856374128990a305e4d073dce806.png" alt="签到图标" class="custom-menu-icon">
                </div>
                <div class="menu-label">签到</div>
                <div class="menu-arrow"><i class="fas fa-chevron-right"></i></div>
            </a>
            <a href="../shop/index.php" class="menu-item">
                <div class="menu-icon">
                    <img src="https://s1.imagehub.cc/images/2025/05/15/22ec85915d15b8c50cc748a6ce4844ec.png" alt="商城图标" class="custom-menu-icon">
                </div>
                <div class="menu-label">商城</div>
                <div class="menu-arrow"><i class="fas fa-chevron-right"></i></div>
            </a>
        </div>

        <div class="menu-group">
            <a href="../guild/index.php" class="menu-item">
                <div class="menu-icon">
                    <img src="https://s1.imagehub.cc/images/2025/05/15/5ea04e6fa3367a51e9b68f488e83b413.png" alt="公会图标" class="custom-menu-icon">
                </div>
                <div class="menu-label">公会</div>
                <div class="menu-arrow"><i class="fas fa-chevron-right"></i></div>
            </a>
            <a href="../universe/my_posts.php" class="menu-item">
                <div class="menu-icon">
                    <img src="https://s1.imagehub.cc/images/2025/05/15/6170b857e298fe2d9720eb6117dc0eb4.png" alt="作品图标" class="custom-menu-icon">
                </div>
                <div class="menu-label">作品</div>
                <div class="menu-arrow"><i class="fas fa-chevron-right"></i></div>
            </a>
            <a href="../groups/index.php" class="menu-item">
                <div class="menu-icon">
                    <img src="https://s1.imagehub.cc/images/2025/05/15/f8225ab1b81db016da0e237ddc70e5e1.png" alt="组局图标" class="custom-menu-icon">
                </div>
                <div class="menu-label">组局</div>
                <div class="menu-arrow"><i class="fas fa-chevron-right"></i></div>
            </a>
        </div>

        <div class="menu-group">
            <a href="../creator/index.php" class="menu-item">
                <div class="menu-icon">
                    <img src="https://s1.imagehub.cc/images/2025/05/15/4a79ab1cef2adf2cf63f10a228119f72.png" alt="创作者中心图标" class="custom-menu-icon">
                </div>
                <div class="menu-label">创作者中心</div>
                <div class="menu-arrow"><i class="fas fa-chevron-right"></i></div>
            </a>
        </div>
    </div>

    <?php echo renderBottomNav('profile', true); ?>

    <script src="js/script.js"></script>
    <?php echo renderBottomNavJS(true); ?>
</body>
</html>
