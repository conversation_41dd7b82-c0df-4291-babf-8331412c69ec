<?php
// 提交服务评价API
header('Content-Type: application/json; charset=UTF-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// 处理预检请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

// 只接受POST请求
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['error' => '方法不允许']);
    exit;
}

session_start();

// 检查用户登录状态
if (!isset($_SESSION['user_id'])) {
    http_response_code(401);
    echo json_encode(['error' => '用户未登录']);
    exit;
}

// 获取POST数据
$input = json_decode(file_get_contents('php://input'), true);
$sessionId = $input['sessionId'] ?? '';
$rating = (int)($input['rating'] ?? 0);
$comment = trim($input['comment'] ?? '');

if (empty($sessionId) || $rating < 1 || $rating > 5) {
    http_response_code(400);
    echo json_encode(['error' => '参数不完整或评分无效']);
    exit;
}

// 引用数据库配置文件
require_once '../../../houtai_backup/db_config.php';

try {
    $pdo = getDbConnection();
    
    // 检查会话是否存在
    $stmt = $pdo->prepare("SELECT * FROM customer_service_sessions WHERE session_id = ?");
    $stmt->execute([$sessionId]);
    $session = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$session) {
        http_response_code(404);
        echo json_encode(['error' => '会话不存在']);
        exit;
    }
    
    // 更新会话的满意度评价
    $stmt = $pdo->prepare("
        UPDATE customer_service_sessions 
        SET satisfaction_score = ?, satisfaction_comment = ?, status = 'closed', ended_at = NOW()
        WHERE session_id = ?
    ");
    $stmt->execute([$rating, $comment, $sessionId]);
    
    // 添加系统消息记录评价
    $ratingText = '';
    switch ($rating) {
        case 5: $ratingText = '非常满意'; break;
        case 4: $ratingText = '满意'; break;
        case 3: $ratingText = '一般'; break;
        case 2: $ratingText = '不满意'; break;
        case 1: $ratingText = '非常不满意'; break;
    }
    
    $systemMessage = "用户对本次服务进行了评价：{$rating}星 - {$ratingText}";
    if (!empty($comment)) {
        $systemMessage .= "\n评价内容：{$comment}";
    }
    
    $stmt = $pdo->prepare("
        INSERT INTO customer_service_messages 
        (session_id, sender_type, sender_id, sender_name, message_type, content, created_at) 
        VALUES (?, 'system', NULL, '系统', 'system', ?, NOW())
    ");
    $stmt->execute([$sessionId, $systemMessage]);
    
    // 更新会话消息数量
    $stmt = $pdo->prepare("
        UPDATE customer_service_sessions 
        SET message_count = message_count + 1, updated_at = NOW() 
        WHERE session_id = ?
    ");
    $stmt->execute([$sessionId]);
    
    echo json_encode([
        'success' => true,
        'message' => '评价提交成功，感谢您的反馈！'
    ]);
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => '提交评价失败',
        'message' => $e->getMessage()
    ]);
}
?>
