<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>表单调试工具</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .debug-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            max-width: 800px;
            margin: 0 auto;
        }
        .debug-section {
            margin-bottom: 20px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .debug-section h3 {
            margin-top: 0;
            color: #333;
        }
        .field-info {
            margin: 10px 0;
            padding: 8px;
            background: #f9f9f9;
            border-left: 3px solid #40E0D0;
        }
        .field-name {
            font-weight: bold;
            color: #333;
        }
        .field-value {
            color: #666;
            margin-left: 10px;
        }
        .error {
            color: #e74c3c;
            background: #ffeaea;
            border-left-color: #e74c3c;
        }
        .success {
            color: #27ae60;
            background: #eafaf1;
            border-left-color: #27ae60;
        }
        .btn {
            background: #40E0D0;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        .btn:hover {
            background: #36c7b8;
        }
    </style>
</head>
<body>
    <div class="debug-container">
        <h2>引导页表单调试工具</h2>

        <div class="debug-section">
            <h3>操作按钮</h3>
            <button class="btn" onclick="checkFormData()">检查表单数据</button>
            <button class="btn" onclick="simulateSubmit()">模拟提交</button>
            <button class="btn" onclick="clearResults()">清除结果</button>
            <button class="btn" onclick="goToOnboarding()">返回引导页</button>
        </div>

        <div class="debug-section">
            <h3>表单字段状态</h3>
            <div id="form-fields"></div>
        </div>

        <div class="debug-section">
            <h3>验证结果</h3>
            <div id="validation-results"></div>
        </div>

        <div class="debug-section">
            <h3>提交数据预览</h3>
            <div id="submit-data"></div>
        </div>
    </div>

    <script>
        function checkFormData() {
            // 检查是否在引导页的iframe中或者有父窗口
            let targetWindow = window.parent !== window ? window.parent : window.opener;

            if (!targetWindow || !targetWindow.document.getElementById('onboarding-form')) {
                document.getElementById('form-fields').innerHTML =
                    '<div class="field-info error">错误：无法访问引导页表单。请在引导页中打开此调试工具。</div>';
                return;
            }

            const doc = targetWindow.document;
            const formData = new FormData(doc.getElementById('onboarding-form'));

            // 获取各个字段的值
            const phoneInput = doc.getElementById('phone');
            const phone = phoneInput.getAttribute('data-original') || targetWindow.verifiedPhone || sessionStorage.getItem('register_phone') || '';
            const nickname = formData.get('nickname');
            const gender = formData.get('gender');
            const birthDateElement = doc.getElementById('birth_date');
            const birthDate = birthDateElement.getAttribute('data-value') || birthDateElement.value;
            const region = formData.get('region');
            const email = formData.get('email');
            const password = formData.get('password');
            const confirmPassword = formData.get('confirm_password');
            const bio = formData.get('bio');

            // 显示字段信息
            const fieldsHtml = `
                <div class="field-info ${phone ? 'success' : 'error'}">
                    <span class="field-name">手机号:</span>
                    <span class="field-value">"${phone}" (长度: ${phone ? phone.length : 0})</span>
                </div>
                <div class="field-info ${nickname ? 'success' : 'error'}">
                    <span class="field-name">昵称:</span>
                    <span class="field-value">"${nickname}" (长度: ${nickname ? nickname.length : 0})</span>
                </div>
                <div class="field-info ${gender ? 'success' : 'error'}">
                    <span class="field-name">性别:</span>
                    <span class="field-value">"${gender}" (类型: ${typeof gender})</span>
                </div>
                <div class="field-info ${birthDate ? 'success' : 'error'}">
                    <span class="field-name">出生日期:</span>
                    <span class="field-value">"${birthDate}" (类型: ${typeof birthDate})</span>
                    <br><small>输入框值: "${birthDateElement.value}" | data-value: "${birthDateElement.getAttribute('data-value')}"</small>
                </div>
                <div class="field-info ${region ? 'success' : 'error'}">
                    <span class="field-name">地区:</span>
                    <span class="field-value">"${region}" (长度: ${region ? region.length : 0})</span>
                </div>
                <div class="field-info ${email ? 'success' : 'error'}">
                    <span class="field-name">邮箱:</span>
                    <span class="field-value">"${email}" (长度: ${email ? email.length : 0})</span>
                </div>
                <div class="field-info ${password ? 'success' : 'error'}">
                    <span class="field-name">密码:</span>
                    <span class="field-value">${password ? '已填写' : '未填写'} (长度: ${password ? password.length : 0})</span>
                </div>
                <div class="field-info ${confirmPassword ? 'success' : 'error'}">
                    <span class="field-name">确认密码:</span>
                    <span class="field-value">${confirmPassword ? '已填写' : '未填写'} (长度: ${confirmPassword ? confirmPassword.length : 0})</span>
                </div>
                <div class="field-info">
                    <span class="field-name">个人简介:</span>
                    <span class="field-value">"${bio}" (长度: ${bio ? bio.length : 0})</span>
                </div>
            `;

            document.getElementById('form-fields').innerHTML = fieldsHtml;

            // 验证字段
            validateFields(phone, nickname, gender, birthDate, region, email, password, confirmPassword);

            // 显示提交数据
            showSubmitData(phone, nickname, gender, birthDate, region, email, password, bio);
        }

        function validateFields(phone, nickname, gender, birthDate, region, email, password, confirmPassword) {
            const validations = [];

            // 验证必填字段
            if (!phone || phone.trim() === '') {
                validations.push('<div class="field-info error">❌ 手机号不能为空</div>');
            } else {
                validations.push('<div class="field-info success">✅ 手机号验证通过</div>');
            }

            if (!nickname || nickname.trim() === '') {
                validations.push('<div class="field-info error">❌ 昵称不能为空</div>');
            } else {
                validations.push('<div class="field-info success">✅ 昵称验证通过</div>');
            }

            if (!gender || gender.trim() === '') {
                validations.push('<div class="field-info error">❌ 性别不能为空</div>');
            } else {
                validations.push('<div class="field-info success">✅ 性别验证通过</div>');
            }

            if (!birthDate || birthDate.trim() === '') {
                validations.push('<div class="field-info error">❌ 出生日期不能为空</div>');
            } else {
                validations.push('<div class="field-info success">✅ 出生日期验证通过</div>');
            }

            if (!region || region.trim() === '') {
                validations.push('<div class="field-info error">❌ 地区不能为空</div>');
            } else {
                validations.push('<div class="field-info success">✅ 地区验证通过</div>');
            }

            if (!email || email.trim() === '') {
                validations.push('<div class="field-info error">❌ 邮箱不能为空</div>');
            } else {
                const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                if (!emailRegex.test(email)) {
                    validations.push('<div class="field-info error">❌ 邮箱格式不正确</div>');
                } else {
                    validations.push('<div class="field-info success">✅ 邮箱验证通过</div>');
                }
            }

            if (!password || password.trim() === '') {
                validations.push('<div class="field-info error">❌ 密码不能为空</div>');
            } else if (password.length < 6) {
                validations.push('<div class="field-info error">❌ 密码长度不能少于6位</div>');
            } else {
                validations.push('<div class="field-info success">✅ 密码验证通过</div>');
            }

            if (!confirmPassword || confirmPassword.trim() === '') {
                validations.push('<div class="field-info error">❌ 确认密码不能为空</div>');
            } else if (password !== confirmPassword) {
                validations.push('<div class="field-info error">❌ 两次输入的密码不一致</div>');
            } else {
                validations.push('<div class="field-info success">✅ 确认密码验证通过</div>');
            }

            document.getElementById('validation-results').innerHTML = validations.join('');
        }

        function showSubmitData(phone, nickname, gender, birthDate, region, email, password, bio) {
            const submitData = {
                phone: phone,
                nickname: nickname,
                gender: gender,
                birth_date: birthDate,
                region: region,
                email: email,
                password: password ? '已提供' : '未提供',
                bio: bio,
                avatar: ''
            };

            document.getElementById('submit-data').innerHTML =
                '<pre>' + JSON.stringify(submitData, null, 2) + '</pre>';
        }

        function simulateSubmit() {
            alert('这是模拟提交，实际提交请在引导页进行');
        }

        function clearResults() {
            document.getElementById('form-fields').innerHTML = '';
            document.getElementById('validation-results').innerHTML = '';
            document.getElementById('submit-data').innerHTML = '';
        }

        function goToOnboarding() {
            window.location.href = 'index.php';
        }
    </script>
</body>
</html>
