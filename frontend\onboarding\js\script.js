// 全局定义Toast提示函数
function showToast(message, duration = 3000) {
    console.log('显示Toast提示:', message);

    const toast = document.getElementById('toast');
    if (!toast) {
        console.error('未找到toast元素');
        alert(message); // 如果找不到toast元素，使用alert作为备选
        return;
    }

    // 如果已经有toast在显示，先清除之前的定时器
    if (toast.timer) {
        clearTimeout(toast.timer);
    }

    // 先隐藏toast，触发重绘，重置动画
    toast.style.display = 'none';
    void toast.offsetWidth; // 触发重绘

    // 设置消息并显示
    toast.textContent = message;
    toast.style.display = 'block';

    // 设置定时器关闭toast
    toast.timer = setTimeout(() => {
        toast.style.display = 'none';
    }, duration);
}

// 确保window.showToast也可用
window.showToast = showToast;

document.addEventListener('DOMContentLoaded', function() {
    // 头像预览功能
    const avatarInput = document.getElementById('avatar');
    const avatarPreview = document.getElementById('avatar-preview');
    const avatarUpload = document.querySelector('.avatar-upload');

    // 设置默认头像URL（确保在任何情况下都能正确显示）
    const defaultAvatarUrl = 'https://s1.imagehub.cc/images/2025/04/26/f5c2f26c4564aabcbb56c5fd30f1d652.jpg';

    // 如果本地图片加载失败，使用在线链接
    if (avatarPreview && avatarPreview.style.backgroundImage.includes('default-avatar')) {
        const img = new Image();
        img.onload = function() {
            // 本地图片加载成功，不需要操作
        };
        img.onerror = function() {
            // 本地图片加载失败，使用在线链接
            avatarPreview.style.backgroundImage = `url(${defaultAvatarUrl})`;
        };
        img.src = 'img/default-avatar.jpg';
    }

    if (avatarInput && avatarPreview) {
        // 点击头像预览区域打开文件选择
        avatarPreview.addEventListener('click', function() {
            avatarInput.click();
        });

        // 添加拖放功能
        avatarUpload.addEventListener('dragover', function(e) {
            e.preventDefault();
            e.stopPropagation();
            this.classList.add('dragging');
        });

        avatarUpload.addEventListener('dragleave', function(e) {
            e.preventDefault();
            e.stopPropagation();
            this.classList.remove('dragging');
        });

        avatarUpload.addEventListener('drop', function(e) {
            e.preventDefault();
            e.stopPropagation();
            this.classList.remove('dragging');

            if (e.dataTransfer.files && e.dataTransfer.files[0]) {
                const file = e.dataTransfer.files[0];
                // 检查文件类型
                if (file.type.match('image.*')) {
                    avatarInput.files = e.dataTransfer.files;
                    uploadAvatar(file);
                } else {
                    showToast('请选择图片文件');
                }
            }
        });

        // 文件选择事件
        avatarInput.addEventListener('change', function() {
            const file = this.files[0];
            if (file) {
                uploadAvatar(file);
            }
        });

        // 头像上传函数
        async function uploadAvatar(file) {
            // 检查文件大小
            if (file.size > 5 * 1024 * 1024) { // 5MB
                showToast('图片大小不能超过5MB');
                return;
            }

            // 检查文件类型
            const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png'];
            if (!allowedTypes.includes(file.type)) {
                showToast('只支持JPG、JPEG、PNG格式的图片');
                return;
            }

            // 显示审核动画
            showModerationAnimation();

            try {
                const formData = new FormData();
                formData.append('avatar', file);

                const response = await fetch('upload_avatar.php', {
                    method: 'POST',
                    body: formData
                });

                const data = await response.json();

                if (data.success) {
                    // 预览头像
                    avatarPreview.style.backgroundImage = `url(${data.avatar_url})`;
                    avatarPreview.classList.add('preview-loaded');
                    setTimeout(() => {
                        avatarPreview.classList.remove('preview-loaded');
                    }, 500);

                    // 保存头像URL到全局变量
                    window.uploadedAvatarUrl = data.avatar_url;

                    // 检查审核结果
                    if (data.moderation.approved) {
                        hideModerationAnimation();
                        showToast('头像上传成功！审核通过');
                        // 保存表单数据
                        saveFormData();
                    } else {
                        hideModerationAnimation();
                        showToast('头像审核未通过：' + data.moderation.reason);
                        // 重置头像
                        avatarPreview.style.backgroundImage = `url(${defaultAvatarUrl})`;
                        avatarInput.value = '';
                        window.uploadedAvatarUrl = '';
                        // 保存表单数据（清除头像）
                        saveFormData();
                    }
                } else {
                    hideModerationAnimation();
                    showToast('头像上传失败：' + data.error);
                }
            } catch (error) {
                hideModerationAnimation();
                console.error('头像上传失败:', error);
                showToast('头像上传失败，请重试');
            }
        }

        // 显示审核动画
        function showModerationAnimation() {
            const overlay = document.getElementById('avatar-moderation-overlay');
            if (overlay) {
                overlay.style.display = 'flex';
            }
        }

        // 隐藏审核动画
        function hideModerationAnimation() {
            const overlay = document.getElementById('avatar-moderation-overlay');
            if (overlay) {
                overlay.style.display = 'none';
            }
        }

        // 添加重置头像功能
        const resetAvatar = document.createElement('button');
        resetAvatar.type = 'button';
        resetAvatar.className = 'reset-avatar';
        resetAvatar.title = '重置头像';
        resetAvatar.innerHTML = '重置';
        resetAvatar.onclick = function(e) {
            e.preventDefault();
            avatarPreview.style.backgroundImage = `url(${defaultAvatarUrl})`;
            avatarInput.value = '';
            // 添加动画效果
            avatarPreview.classList.add('preview-loaded');
            setTimeout(() => {
                avatarPreview.classList.remove('preview-loaded');
            }, 500);
            showToast('已重置为默认头像');
        };

        // 将重置按钮添加到头像上传区域
        const avatarText = document.querySelector('.avatar-text');
        if (avatarText) {
            avatarText.parentNode.insertBefore(resetAvatar, avatarText.nextSibling);
        }
    }

    // 表单提交处理
    const onboardingForm = document.getElementById('onboarding-form');
    if (onboardingForm) {
        onboardingForm.addEventListener('submit', async function(e) {
            e.preventDefault();

            // 获取表单数据
            const formData = new FormData(this);
            // 从手机号字段获取原始手机号
            const phoneInput = document.getElementById('phone');
            const phone = phoneInput.getAttribute('data-original') || window.verifiedPhone || sessionStorage.getItem('register_phone') || '';
            const nickname = formData.get('nickname');
            const gender = formData.get('gender');
            // 获取自定义日期选择器的值
            const birthDateElement = document.getElementById('birth_date');
            const birthDate = birthDateElement.getAttribute('data-value') || birthDateElement.value;
            const region = formData.get('region');
            const email = formData.get('email');
            const password = formData.get('password');
            const confirmPassword = formData.get('confirm_password');
            const bio = formData.get('bio');

            // 验证必填字段
            if (!phone) {
                showToast('手机号信息丢失，请重新注册');
                window.location.href = '../register/index.php';
                return;
            }



            // 逐个检查必填字段，给出具体提示
            if (!nickname || nickname.trim() === '') {
                console.log('昵称验证失败:', nickname);
                showToast('请输入昵称');
                document.getElementById('nickname').focus();
                return;
            }

            if (!gender || gender.trim() === '') {
                console.log('性别验证失败:', gender);
                showToast('请选择性别');
                return;
            }

            if (!birthDate || birthDate.trim() === '') {
                console.log('出生日期验证失败:', birthDate);
                console.log('出生日期输入框值:', birthDateElement.value);
                console.log('出生日期data-value:', birthDateElement.getAttribute('data-value'));
                showToast('请点击日历图标选择出生日期');
                // 高亮出生日期字段
                birthDateElement.style.borderColor = '#ff4757';
                birthDateElement.focus();
                return;
            }

            if (!region || region.trim() === '') {
                console.log('地区验证失败:', region);
                showToast('请选择所在地区');
                return;
            }

            if (!email || email.trim() === '') {
                console.log('邮箱验证失败:', email);
                showToast('请输入邮箱地址');
                document.getElementById('email').focus();
                return;
            }

            if (!password || password.trim() === '') {
                console.log('密码验证失败:', password);
                showToast('请输入密码');
                document.getElementById('password').focus();
                return;
            }

            if (!confirmPassword || confirmPassword.trim() === '') {
                console.log('确认密码验证失败:', confirmPassword);
                showToast('请输入确认密码');
                document.getElementById('confirm_password').focus();
                return;
            }

            console.log('所有字段验证通过!');

            // 验证昵称
            const nicknameValidation = validateNickname(nickname);
            if (!nicknameValidation.valid) {
                showToast(nicknameValidation.message);
                return;
            }

            // 验证密码
            if (password !== confirmPassword) {
                showToast('两次输入的密码不一致');
                return;
            }

            if (password.length < 6) {
                showToast('密码长度不能少于6位');
                return;
            }

            // 验证年龄（必须年满18岁）
            if (birthDate) {
                const today = new Date();
                const birth = new Date(birthDate);
                let age = today.getFullYear() - birth.getFullYear();
                const monthDiff = today.getMonth() - birth.getMonth();

                if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birth.getDate())) {
                    age--;
                }

                if (age < 18) {
                    showToast('趣玩星球仅对年满十八周岁的用户提供服务', 4000);
                    return;
                }
            }

            // 验证邮箱格式
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            if (!emailRegex.test(email)) {
                showToast('请输入有效的邮箱地址');
                return;
            }

            // 显示提交中的提示
            showToast('正在创建账户，请稍候...');

            try {
                // 提交注册数据
                const response = await fetch('complete_registration.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        phone: phone,
                        nickname: nickname,
                        gender: gender,
                        birth_date: birthDate,
                        region: region,
                        email: email,
                        password: password,
                        bio: bio,
                        avatar: window.uploadedAvatarUrl || ''
                    })
                });

                const data = await response.json();

                if (data.success) {
                    showToast('注册成功！欢迎加入趣玩星球');

                    // 清除注册相关的sessionStorage
                    sessionStorage.removeItem('register_phone');
                    sessionStorage.removeItem('phone_verified');
                    sessionStorage.removeItem('onboarding_form_data');

                    // 跳转到首页
                    setTimeout(() => {
                        window.location.href = '../home/<USER>';
                    }, 2000);
                } else {
                    showToast('注册失败：' + data.error);
                }
            } catch (error) {
                console.error('注册失败:', error);
                showToast('注册失败，请重试');
            }
        });
    }

    // 检查URL参数，显示成功提示
    const urlParams = new URLSearchParams(window.location.search);
    if (urlParams.has('success')) {
        showToast('个人信息更新成功！');
    }

    // 检查是否有选择的城市
    const selectedCity = sessionStorage.getItem('selected_city');
    if (selectedCity) {
        const regionInput = document.getElementById('region');
        if (regionInput) {
            regionInput.value = selectedCity;
        }
        // 清除sessionStorage
        sessionStorage.removeItem('selected_city');
        sessionStorage.removeItem('selected_address');
    }

    // 恢复之前保存的表单数据
    restoreFormData();

    // 添加表单字段变化监听器，自动保存数据
    const formFields = ['nickname', 'email', 'password', 'confirm_password', 'bio'];
    formFields.forEach(fieldId => {
        const field = document.getElementById(fieldId);
        if (field) {
            field.addEventListener('input', saveFormData);
        }
    });

    // 监听性别选择变化
    const genderRadios = document.querySelectorAll('input[name="gender"]');
    genderRadios.forEach(radio => {
        radio.addEventListener('change', saveFormData);
    });



    // 设置手机号字段的值
    const phoneInput = document.getElementById('phone');
    const verifiedPhone = window.verifiedPhone || sessionStorage.getItem('register_phone') || '';

    if (phoneInput && verifiedPhone) {
        // 脱敏处理：显示前3位和后4位，中间用****代替
        const maskedPhone = verifiedPhone.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2');
        phoneInput.value = maskedPhone;
        phoneInput.setAttribute('data-original', verifiedPhone); // 保存原始手机号
    } else {
        if (phoneInput) {
            phoneInput.value = '未获取到手机号';
            phoneInput.style.borderColor = '#ff4757';
        }
    }



    // 地区选择功能
    const regionInput = document.getElementById('region');
    const regionSelectBtn = document.querySelector('.region-select-btn');

    if (regionInput) {
        // 点击输入框打开城市选择
        regionInput.addEventListener('click', function() {
            openCitySelector();
        });

        // 阻止输入框的键盘输入
        regionInput.addEventListener('keydown', function(e) {
            e.preventDefault();
        });
    }

    if (regionSelectBtn) {
        // 点击定位按钮打开城市选择
        regionSelectBtn.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();
            openCitySelector();
        });
    }

    // 日期选择功能
    const birthDateInput = document.getElementById('birth_date');
    const dateSelectBtn = document.querySelector('.date-select-btn');

    if (birthDateInput) {
        // 点击输入框打开日期选择器
        birthDateInput.addEventListener('click', function() {
            openDatePicker();
        });

        // 阻止输入框的键盘输入
        birthDateInput.addEventListener('keydown', function(e) {
            e.preventDefault();
        });
    }

    if (dateSelectBtn) {
        // 点击日历按钮打开日期选择器
        dateSelectBtn.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();
            openDatePicker();
        });
    }
});



// 保存当前表单数据到sessionStorage
function saveFormData() {
    const formData = {
        nickname: document.getElementById('nickname').value,
        gender: document.querySelector('input[name="gender"]:checked')?.value || '',
        birth_date_display: document.getElementById('birth_date').value,
        birth_date_value: document.getElementById('birth_date').getAttribute('data-value') || '',
        email: document.getElementById('email').value,
        password: document.getElementById('password').value,
        confirm_password: document.getElementById('confirm_password').value,
        bio: document.getElementById('bio').value,
        avatar_url: window.uploadedAvatarUrl || ''
    };

    sessionStorage.setItem('onboarding_form_data', JSON.stringify(formData));
    console.log('表单数据已保存:', formData);
}

// 恢复表单数据从sessionStorage
function restoreFormData() {
    const savedData = sessionStorage.getItem('onboarding_form_data');
    if (savedData) {
        try {
            const formData = JSON.parse(savedData);
            console.log('恢复表单数据:', formData);

            // 恢复基本字段
            if (formData.nickname) document.getElementById('nickname').value = formData.nickname;
            if (formData.email) document.getElementById('email').value = formData.email;
            if (formData.password) document.getElementById('password').value = formData.password;
            if (formData.confirm_password) document.getElementById('confirm_password').value = formData.confirm_password;
            if (formData.bio) document.getElementById('bio').value = formData.bio;

            // 恢复性别选择
            if (formData.gender) {
                const genderRadio = document.querySelector(`input[name="gender"][value="${formData.gender}"]`);
                if (genderRadio) genderRadio.checked = true;
            }

            // 恢复出生日期
            if (formData.birth_date_display && formData.birth_date_value) {
                const birthDateInput = document.getElementById('birth_date');
                birthDateInput.value = formData.birth_date_display;
                birthDateInput.setAttribute('data-value', formData.birth_date_value);
            }

            // 恢复头像
            if (formData.avatar_url) {
                window.uploadedAvatarUrl = formData.avatar_url;
                updateAvatarDisplay(formData.avatar_url);
            }

        } catch (error) {
            console.error('恢复表单数据失败:', error);
        }
    }
}

// 更新头像显示
function updateAvatarDisplay(avatarUrl) {
    const avatarPreview = document.getElementById('avatar-preview');
    const avatarText = document.querySelector('.avatar-text');

    if (avatarPreview && avatarUrl) {
        // 使用backgroundImage方式显示头像（与原来的方式一致）
        avatarPreview.style.backgroundImage = `url(${avatarUrl})`;
        avatarPreview.classList.add('preview-loaded');

        // 更新文本提示
        if (avatarText) {
            avatarText.textContent = '点击更换头像';
        }

        setTimeout(() => {
            avatarPreview.classList.remove('preview-loaded');
        }, 500);
    }
}

// 打开城市选择页面
window.openCitySelector = function() {
    // 保存当前表单数据
    saveFormData();
    window.location.href = 'city_selector.php';
}

// 更新地区信息（从城市选择页面调用）
window.updateRegion = function(cityName) {
    document.getElementById('region').value = cityName;
    showToast('已选择城市：' + cityName);
}

// 日期选择器功能
let selectedYear = null;
let selectedMonth = null;
let selectedDay = null;

// 打开日期选择器
window.openDatePicker = function() {
    const overlay = document.getElementById('date-picker-overlay');
    if (overlay) {
        // 初始化年份选项
        window.initializeYearOptions();

        // 初始化月份和日期事件监听器
        window.initializeDateSelectors();

        // 设置默认值（当前日期减去18年）
        const defaultDate = new Date();
        defaultDate.setFullYear(defaultDate.getFullYear() - 18);

        const yearSelector = document.getElementById('year-selector');
        const monthSelector = document.getElementById('month-selector');
        const daySelector = document.getElementById('day-selector');

        yearSelector.value = defaultDate.getFullYear();
        monthSelector.value = defaultDate.getMonth() + 1;
        selectedYear = defaultDate.getFullYear();
        selectedMonth = defaultDate.getMonth() + 1;

        // 初始化日期选项
        window.updateDayOptions();

        daySelector.value = defaultDate.getDate();
        selectedDay = defaultDate.getDate();



        overlay.style.display = 'flex';
    } else {
        console.error('找不到日期选择器覆盖层');
    }
}

// 关闭日期选择器
window.closeDatePicker = function() {
    const overlay = document.getElementById('date-picker-overlay');
    if (overlay) {
        overlay.style.display = 'none';
    }
}

// 初始化年份选项
window.initializeYearOptions = function() {
    const yearSelector = document.getElementById('year-selector');
    const currentYear = new Date().getFullYear();
    const startYear = currentYear - 80; // 80岁
    const endYear = currentYear - 18;   // 18岁

    yearSelector.innerHTML = '';

    for (let year = endYear; year >= startYear; year--) {
        const option = document.createElement('option');
        option.value = year;
        option.textContent = year + '年';
        yearSelector.appendChild(option);
    }
}

// 初始化日期选择器事件监听器
window.initializeDateSelectors = function() {
    const yearSelector = document.getElementById('year-selector');
    const monthSelector = document.getElementById('month-selector');
    const daySelector = document.getElementById('day-selector');

    // 检查是否已经绑定过事件监听器
    if (!yearSelector.hasAttribute('data-events-bound')) {
        // 添加事件监听器
        yearSelector.addEventListener('change', function() {
            selectedYear = parseInt(this.value);
            window.updateDayOptions();
        });

        monthSelector.addEventListener('change', function() {
            selectedMonth = parseInt(this.value);
            window.updateDayOptions();
        });

        daySelector.addEventListener('change', function() {
            selectedDay = parseInt(this.value);
        });

        // 标记已绑定事件
        yearSelector.setAttribute('data-events-bound', 'true');
        monthSelector.setAttribute('data-events-bound', 'true');
        daySelector.setAttribute('data-events-bound', 'true');
    }
}

// 更新日期选项
window.updateDayOptions = function() {
    const daySelector = document.getElementById('day-selector');
    const monthSelector = document.getElementById('month-selector');

    const year = selectedYear || parseInt(document.getElementById('year-selector').value);
    const month = selectedMonth || parseInt(document.getElementById('month-selector').value);

    // 获取该月的天数
    const daysInMonth = new Date(year, month, 0).getDate();

    daySelector.innerHTML = '';

    for (let day = 1; day <= daysInMonth; day++) {
        const option = document.createElement('option');
        option.value = day;
        option.textContent = day + '日';
        daySelector.appendChild(option);
    }
}

// 确认日期选择
window.confirmDateSelection = function() {
    const yearSelector = document.getElementById('year-selector');
    const monthSelector = document.getElementById('month-selector');
    const daySelector = document.getElementById('day-selector');

    const year = parseInt(yearSelector.value);
    const month = parseInt(monthSelector.value);
    const day = parseInt(daySelector.value);

    if (!year || !month || !day) {
        showToast('请选择完整的日期');
        return;
    }

    // 验证年龄是否满18岁
    const selectedDate = new Date(year, month - 1, day);
    const today = new Date();
    let age = today.getFullYear() - selectedDate.getFullYear();
    const monthDiff = today.getMonth() - selectedDate.getMonth();

    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < selectedDate.getDate())) {
        age--;
    }

    if (age < 18) {
        showToast('趣玩星球仅对年满十八周岁的用户提供服务', 4000);
        return;
    }

    // 格式化日期
    const formattedDate = `${year}-${month.toString().padStart(2, '0')}-${day.toString().padStart(2, '0')}`;
    const displayDate = `${year}年${month}月${day}日`;

    // 设置到输入框
    const birthDateInput = document.getElementById('birth_date');
    birthDateInput.value = displayDate;
    birthDateInput.setAttribute('data-value', formattedDate);

    // 重置边框颜色（如果之前有错误高亮）
    birthDateInput.style.borderColor = '';

    // 保存表单数据
    saveFormData();

    // 关闭弹窗
    closeDatePicker();

    showToast('已选择出生日期：' + displayDate);
}

// 昵称验证函数
function validateNickname(nickname) {
    if (!nickname || nickname.trim().length === 0) {
        return { valid: false, message: '请输入昵称' };
    }

    // 长度验证：1-5个字符
    if (nickname.length < 1 || nickname.length > 5) {
        return { valid: false, message: '昵称长度应为1-5个字符' };
    }

    // 字符类型验证：只能包含中文、英文、数字
    const validChars = /^[\u4e00-\u9fa5a-zA-Z0-9]+$/;
    if (!validChars.test(nickname)) {
        return { valid: false, message: '昵称只能包含中文、英文和数字' };
    }

    // 检查是否包含表情符号
    const emojiRegex = /[\u{1F600}-\u{1F64F}]|[\u{1F300}-\u{1F5FF}]|[\u{1F680}-\u{1F6FF}]|[\u{1F1E0}-\u{1F1FF}]|[\u{2600}-\u{26FF}]|[\u{2700}-\u{27BF}]/u;
    if (emojiRegex.test(nickname)) {
        return { valid: false, message: '昵称不能包含表情符号' };
    }

    // 敏感词检测
    const sensitiveCheck = checkSensitiveWords(nickname);
    if (!sensitiveCheck.valid) {
        return sensitiveCheck;
    }

    return { valid: true, message: '昵称格式正确' };
}

// 敏感词检测函数
function checkSensitiveWords(text) {
    const lowerText = text.toLowerCase();

    // 国家领导人相关
    const leaderWords = [
        '习近平', 'xi', 'jinping', 'xijinping', 'xiaoxi', 'dada',
        '主席', 'zhuxxi', 'chairman', 'president',
        '总书记', 'zongshuji', 'secretary',
        '总理', 'zongli', 'premier',
        '国家主席', 'guojiazhuxi'
    ];

    // 国家机关相关
    const governmentWords = [
        '中央', 'zhongyang', 'central',
        '国务院', 'guowuyuan', 'statecouncil',
        '人大', 'renda', 'npc',
        '政协', 'zhengxie', 'cppcc',
        '中共', 'zhonggong', 'ccp',
        '党中央', 'dangzhongyang'
    ];

    // 辱骂词汇
    const offensiveWords = [
        // 中文脏话
        '傻逼', 'sb', '草泥马', 'cnm', '妈的', 'md', '操', 'cao',
        '滚', 'gun', '死', 'si', '蠢', 'chun', '白痴', 'baichi',
        '智障', 'zhizhang', '废物', 'feiwu', '垃圾', 'laji',

        // 英文脏话
        'fuck', 'shit', 'damn', 'bitch', 'asshole', 'bastard',
        'idiot', 'stupid', 'moron', 'retard', 'loser',

        // 拼音脏话
        'shabi', 'caonima', 'made', 'gundan', 'zhineng'
    ];

    // 不雅词汇
    const inappropriateWords = [
        // 性相关
        '性', 'xing', 'sex', '做爱', 'zuoai', '性交', 'xingjiao',
        '阴茎', 'yinjing', '阴道', 'yindao', '乳房', 'rufang',
        '胸部', 'xiongbu', '屁股', 'pigu', '大腿', 'datui',

        // 暴力相关
        '杀', 'sha', 'kill', '死亡', 'siwang', 'death',
        '血', 'xue', 'blood', '暴力', 'baoli', 'violence'
    ];

    // 检查所有敏感词
    const allSensitiveWords = [...leaderWords, ...governmentWords, ...offensiveWords, ...inappropriateWords];

    for (let word of allSensitiveWords) {
        if (lowerText.includes(word.toLowerCase()) || text.includes(word)) {
            return { valid: false, message: '昵称包含不当内容，请重新输入' };
        }
    }

    // 检查是否全是数字
    if (/^\d+$/.test(text)) {
        return { valid: false, message: '昵称不能全是数字' };
    }

    // 检查是否包含连续相同字符（超过2个）
    if (/(.)\1{2,}/.test(text)) {
        return { valid: false, message: '昵称不能包含连续相同字符' };
    }

    return { valid: true, message: '昵称检测通过' };
}
