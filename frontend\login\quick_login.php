<?php
/**
 * 快速登录API（无需密码）
 * 仅在安全检测通过时使用
 */

session_start();
header('Content-Type: application/json; charset=utf-8');

// 数据库连接配置
$db_config = [
    'host' => 'localhost',
    'username' => 'quwanplanet',
    'password' => 'nJmJm23FB4Xn6Fc3',
    'dbname' => 'quwanplanet',
    'port' => 3306,
    'charset' => 'utf8mb4'
];

try {
    $pdo = new PDO(
        "mysql:host={$db_config['host']};port={$db_config['port']};dbname={$db_config['dbname']};charset={$db_config['charset']}",
        $db_config['username'],
        $db_config['password'],
        [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
            PDO::ATTR_EMULATE_PREPARES => false
        ]
    );
} catch (PDOException $e) {
    echo json_encode(['success' => false, 'message' => '数据库连接失败']);
    exit;
}

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode(['success' => false, 'message' => '请求方法错误']);
    exit;
}

$input = json_decode(file_get_contents('php://input'), true);
$user_id = intval($input['user_id'] ?? 0);

if (!$user_id) {
    echo json_encode(['success' => false, 'message' => '用户ID无效']);
    exit;
}

// 获取用户信息
$stmt = $pdo->prepare("SELECT * FROM users WHERE id = ?");
$stmt->execute([$user_id]);
$user = $stmt->fetch();

if (!$user) {
    echo json_encode(['success' => false, 'message' => '用户不存在']);
    exit;
}

// 检查用户状态
if ($user['status'] === 'banned') {
    echo json_encode(['success' => false, 'message' => '账户已被封禁，请联系客服']);
    exit;
}

// 再次进行安全检测确保安全
$current_ip = $_SERVER['REMOTE_ADDR'];
$current_user_agent = $_SERVER['HTTP_USER_AGENT'];
$current_device_info = parseUserAgent($current_user_agent);

// 简化的安全检测
$security_check = quickSecurityCheck($pdo, $user['id'], $current_ip, $current_user_agent);

if (!$security_check['trusted']) {
    echo json_encode(['success' => false, 'message' => '安全检测未通过，请使用密码登录']);
    exit;
}

// 执行登录
$_SESSION['user_id'] = $user['id'];
$_SESSION['username'] = $user['username'];
$_SESSION['quwan_id'] = $user['quwan_id'];

// 更新最后登录时间
$stmt = $pdo->prepare("UPDATE users SET last_login = NOW() WHERE id = ?");
$stmt->execute([$user['id']]);

// 记录登录日志
$device_fingerprint = generateDeviceFingerprint($current_device_info);
$stmt = $pdo->prepare("
    INSERT INTO login_logs (user_id, login_time, ip_address, user_agent, status, device_fingerprint, login_type) 
    VALUES (?, NOW(), ?, ?, 'success', ?, 'quick_login')
");
$stmt->execute([
    $user['id'],
    $current_ip,
    $current_user_agent,
    $device_fingerprint
]);

echo json_encode([
    'success' => true,
    'message' => '登录成功',
    'redirect' => '../home/<USER>'
]);

/**
 * 快速安全检测
 */
function quickSecurityCheck($pdo, $user_id, $current_ip, $current_user_agent) {
    // 检查IP是否在最近使用过
    $stmt = $pdo->prepare("
        SELECT COUNT(*) as count
        FROM login_logs 
        WHERE user_id = ? AND ip_address = ? AND status = 'success'
        AND login_time >= DATE_SUB(NOW(), INTERVAL 30 DAY)
    ");
    $stmt->execute([$user_id, $current_ip]);
    $ip_count = $stmt->fetchColumn();
    
    // 检查设备指纹是否熟悉
    $device_info = parseUserAgent($current_user_agent);
    $device_fingerprint = generateDeviceFingerprint($device_info);
    
    $stmt = $pdo->prepare("
        SELECT COUNT(*) as count
        FROM login_logs 
        WHERE user_id = ? AND device_fingerprint = ? AND status = 'success'
        AND login_time >= DATE_SUB(NOW(), INTERVAL 60 DAY)
    ");
    $stmt->execute([$user_id, $device_fingerprint]);
    $device_count = $stmt->fetchColumn();
    
    return [
        'trusted' => $ip_count >= 2 && $device_count >= 1,
        'ip_count' => $ip_count,
        'device_count' => $device_count
    ];
}

/**
 * 解析User Agent
 */
function parseUserAgent($user_agent) {
    $device = 'Unknown';
    $os = 'Unknown';
    $browser = 'Unknown';
    
    // 检测设备类型
    if (preg_match('/Mobile|Android|iPhone|iPad/', $user_agent)) {
        $device = 'Mobile';
    } else {
        $device = 'Desktop';
    }
    
    // 检测操作系统
    if (preg_match('/Windows NT ([0-9.]+)/', $user_agent, $matches)) {
        $os = 'Windows ' . $matches[1];
    } elseif (preg_match('/Mac OS X ([0-9_]+)/', $user_agent, $matches)) {
        $os = 'macOS ' . str_replace('_', '.', $matches[1]);
    } elseif (preg_match('/Android ([0-9.]+)/', $user_agent, $matches)) {
        $os = 'Android ' . $matches[1];
    } elseif (preg_match('/iPhone OS ([0-9_]+)/', $user_agent, $matches)) {
        $os = 'iOS ' . str_replace('_', '.', $matches[1]);
    }
    
    // 检测浏览器
    if (preg_match('/Chrome\/([0-9.]+)/', $user_agent, $matches)) {
        $browser = 'Chrome ' . $matches[1];
    } elseif (preg_match('/Firefox\/([0-9.]+)/', $user_agent, $matches)) {
        $browser = 'Firefox ' . $matches[1];
    } elseif (preg_match('/Safari\/([0-9.]+)/', $user_agent, $matches)) {
        $browser = 'Safari ' . $matches[1];
    } elseif (preg_match('/Edge\/([0-9.]+)/', $user_agent, $matches)) {
        $browser = 'Edge ' . $matches[1];
    }
    
    return [
        'device' => $device,
        'os' => $os,
        'browser' => $browser,
        'full' => $user_agent
    ];
}

/**
 * 生成设备指纹
 */
function generateDeviceFingerprint($device_info) {
    return md5($device_info['os'] . '|' . $device_info['browser'] . '|' . $device_info['device']);
}
?>
