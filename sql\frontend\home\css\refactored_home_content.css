/* frontend/home/<USER>/refactored_home_content.css */

/* General styles for the refactored content area */
.refactored-content-display-area {
    padding: 15px;
    background-color: var(--bg-light, #F8F9FA); /* Fallback if CSS var not defined */
    min-height: 300px; /* Ensure some space is visible */
}

.content-section {
    display: none; /* Hidden by default, JS will manage visibility */
    animation: fadeIn 0.5s ease-in-out;
}

.content-section.active {
    display: block;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

.section-header {
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 1px solid #e0e0e0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.section-header h2 {
    font-size: 1.4rem;
    font-weight: 600;
    color: var(--text-primary, #333);
    margin: 0;
}

/* Optional: Placeholder for filters/sort controls */
.section-filters {
    display: flex;
    gap: 10px;
}

.filter-btn {
    padding: 6px 12px;
    font-size: 0.85rem;
    background-color: #fff;
    border: 1px solid #ddd;
    border-radius: var(--radius-sm, 8px);
    cursor: pointer;
    transition: background-color 0.2s, color 0.2s;
}

.filter-btn:hover, .filter-btn.active {
    background-color: var(--primary-color, #6F7BF5);
    color: white;
    border-color: var(--primary-color, #6F7BF5);
}

/* Card container styles */
.cards-container {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr)); /* Responsive grid */
    gap: 20px;
}

/* Individual card styles */
.content-card {
    background-color: var(--bg-white, #FFFFFF);
    border-radius: var(--radius-lg, 16px);
    box-shadow: var(--shadow-md, 0 4px 16px rgba(0, 0, 0, 0.12));
    overflow: hidden;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    display: flex;
    flex-direction: column;
}

.content-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg, 0 8px 32px rgba(0, 0, 0, 0.16));
}

.card-image {
    width: 100%;
    height: 180px;
    object-fit: cover;
    border-bottom: 1px solid #eee;
}

.card-body {
    padding: 15px;
    flex-grow: 1;
    display: flex;
    flex-direction: column;
}

.card-title {
    font-size: 1.15rem;
    font-weight: 600;
    color: var(--text-primary, #333);
    margin-bottom: 8px;
}

.card-description {
    font-size: 0.9rem;
    color: var(--text-secondary, #666);
    margin-bottom: 12px;
    line-height: 1.5;
    flex-grow: 1; /* Allows description to take available space */
}

.card-meta {
    font-size: 0.8rem;
    color: var(--text-light, #999);
    margin-bottom: 15px;
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
}

.card-meta span {
    display: flex;
    align-items: center;
}

.card-meta i {
    margin-right: 5px;
    color: var(--secondary-color, #8B95F7);
}

.card-cta-button {
    background: var(--gradient-primary, linear-gradient(135deg, #6F7BF5, #8B95F7));
    color: white;
    padding: 10px 15px;
    border-radius: var(--radius-md, 12px);
    font-weight: 500;
    border: none;
    cursor: pointer;
    font-size: 0.95rem;
    text-align: center;
    transition: background 0.3s ease, transform 0.2s ease;
    margin-top: auto; /* Pushes button to the bottom */
}

.card-cta-button:hover {
    background: var(--gradient-secondary, linear-gradient(135deg, #A8B2F8, #6F7BF5));
    transform: scale(1.03);
}

/* Specific card type adjustments (optional) */
.game-card .card-image {
    /* background-color: #e0f7fa; /* Light cyan for game placeholders */
}
.city-activity-card .card-image {
    /* background-color: #fff9c4; /* Light yellow for city placeholders */
}
.ticket-card .card-image {
    /* background-color: #ffe0b2; /* Light orange for ticket placeholders */
}
.group-card .card-image {
    /* background-color: #d1c4e9; /* Light purple for group placeholders */
}

/* Placeholder for empty state */
.empty-state-message {
    text-align: center;
    padding: 40px 20px;
    color: var(--text-light, #999);
    font-size: 1rem;
}

.empty-state-message i {
    font-size: 2.5rem;
    margin-bottom: 15px;
    display: block;
    color: var(--primary-light, #A8B2F8);
}
