<?php
// 检查用户登录状态API
header('Content-Type: application/json; charset=UTF-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// 处理预检请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

session_start();

try {
    // 检查用户登录状态
    if (!isset($_SESSION['user_id'])) {
        echo json_encode([
            'success' => false,
            'error' => '用户未登录',
            'login_required' => true
        ]);
        exit;
    }

    // 返回用户信息
    echo json_encode([
        'success' => true,
        'user_id' => $_SESSION['user_id'],
        'nickname' => $_SESSION['nickname'] ?? '用户',
        'phone' => $_SESSION['phone'] ?? '',
        'login_time' => $_SESSION['login_time'] ?? null,
        'session_data' => [
            'user_id' => $_SESSION['user_id'],
            'nickname' => $_SESSION['nickname'] ?? '用户',
            'phone' => $_SESSION['phone'] ?? '',
            'avatar' => $_SESSION['avatar'] ?? '',
            'session_start' => session_id()
        ]
    ]);

} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'error' => '检查会话状态失败: ' . $e->getMessage()
    ]);
}
?>
