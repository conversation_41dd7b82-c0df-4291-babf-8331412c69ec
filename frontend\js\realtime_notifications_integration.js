/**
 * 前台实时通知集成
 * 在所有前台页面中自动启用WebSocket通知
 */

(function() {
    'use strict';
    
    // 检查是否已经初始化
    if (window.realtimeNotificationsInitialized) {
        return;
    }
    
    window.realtimeNotificationsInitialized = true;
    
    // 配置参数
    const config = {
        autoConnect: true,
        showConnectionStatus: true,
        enableSound: true,
        enableToast: true,
        debugMode: false
    };
    
    // 日志函数
    function log(message, type = 'info') {
        if (config.debugMode) {
            console.log(`[实时通知] ${message}`);
        }
    }
    
    // 获取用户ID
    function getUserId() {
        // 从多个地方尝试获取用户ID
        const sources = [
            () => window.currentUserId,
            () => document.querySelector('meta[name="user-id"]')?.content,
            () => localStorage.getItem('user_id'),
            () => sessionStorage.getItem('user_id'),
            () => document.body.dataset.userId,
            () => {
                // 从cookie中获取
                const match = document.cookie.match(/user_id=([^;]+)/);
                return match ? match[1] : null;
            }
        ];
        
        for (const source of sources) {
            try {
                const userId = source();
                if (userId && userId !== '0' && userId !== 'null') {
                    return parseInt(userId);
                }
            } catch (e) {
                // 忽略错误，继续尝试下一个源
            }
        }
        
        return null;
    }
    
    // 显示连接状态
    function showConnectionStatus(status, message) {
        if (!config.showConnectionStatus) return;
        
        // 移除现有状态
        const existing = document.getElementById('realtime-connection-status');
        if (existing) {
            existing.remove();
        }
        
        // 创建状态指示器
        const statusDiv = document.createElement('div');
        statusDiv.id = 'realtime-connection-status';
        statusDiv.style.cssText = `
            position: fixed;
            bottom: 20px;
            left: 20px;
            z-index: 9999;
            padding: 8px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 500;
            color: white;
            transition: all 0.3s ease;
            pointer-events: none;
        `;
        
        const colors = {
            connecting: '#ffc107',
            connected: '#28a745',
            disconnected: '#dc3545',
            error: '#dc3545'
        };
        
        const icons = {
            connecting: '🔄',
            connected: '🟢',
            disconnected: '🔴',
            error: '❌'
        };
        
        statusDiv.style.background = colors[status] || colors.error;
        statusDiv.innerHTML = `${icons[status] || '❓'} ${message}`;
        
        document.body.appendChild(statusDiv);
        
        // 自动隐藏（除了错误状态）
        if (status !== 'error') {
            setTimeout(() => {
                if (statusDiv.parentNode) {
                    statusDiv.style.opacity = '0';
                    setTimeout(() => {
                        if (statusDiv.parentNode) {
                            statusDiv.remove();
                        }
                    }, 300);
                }
            }, status === 'connected' ? 3000 : 5000);
        }
    }
    
    // 初始化通知系统
    function initializeNotifications() {
        const userId = getUserId();
        
        if (!userId) {
            log('未找到用户ID，跳过实时通知初始化');
            return;
        }
        
        log(`初始化实时通知系统，用户ID: ${userId}`);
        
        // 动态加载WebSocket通知组件
        loadWebSocketComponent().then(() => {
            if (window.WebSocketNotifications) {
                // 创建通知实例
                window.realtimeNotifications = new window.WebSocketNotifications(userId);
                
                // 自动连接
                if (config.autoConnect) {
                    showConnectionStatus('connecting', '正在连接实时通知...');
                    window.realtimeNotifications.connect();
                    
                    // 监听连接状态
                    setTimeout(() => {
                        if (window.realtimeNotifications.isConnected) {
                            showConnectionStatus('connected', '实时通知已连接');
                            log('实时通知连接成功');
                        } else {
                            showConnectionStatus('error', '实时通知连接失败');
                            log('实时通知连接失败');
                        }
                    }, 3000);
                }
                
                // 页面卸载时断开连接
                window.addEventListener('beforeunload', () => {
                    if (window.realtimeNotifications) {
                        window.realtimeNotifications.disconnect();
                    }
                });
                
                log('实时通知系统初始化完成');
            } else {
                log('WebSocket通知组件加载失败', 'error');
                showConnectionStatus('error', '通知组件加载失败');
            }
        }).catch(error => {
            log('加载WebSocket组件失败: ' + error.message, 'error');
            showConnectionStatus('error', '通知组件加载失败');
        });
    }
    
    // 动态加载WebSocket组件
    function loadWebSocketComponent() {
        return new Promise((resolve, reject) => {
            // 检查是否已经加载
            if (window.WebSocketNotifications) {
                resolve();
                return;
            }
            
            // 创建script标签
            const script = document.createElement('script');
            script.src = '/frontend/components/websocket_notifications.js';
            script.onload = resolve;
            script.onerror = () => reject(new Error('Failed to load WebSocket component'));
            
            document.head.appendChild(script);
        });
    }
    
    // 提供全局API
    window.RealtimeNotifications = {
        // 手动连接
        connect: function() {
            if (window.realtimeNotifications) {
                window.realtimeNotifications.connect();
            } else {
                initializeNotifications();
            }
        },
        
        // 断开连接
        disconnect: function() {
            if (window.realtimeNotifications) {
                window.realtimeNotifications.disconnect();
            }
        },
        
        // 发送测试通知
        sendTestNotification: function(title, message, type = 'info') {
            if (window.realtimeNotifications) {
                window.realtimeNotifications.showToastNotification(title, message, type);
            }
        },
        
        // 获取连接状态
        isConnected: function() {
            return window.realtimeNotifications ? window.realtimeNotifications.isConnected : false;
        },
        
        // 设置配置
        setConfig: function(newConfig) {
            Object.assign(config, newConfig);
        }
    };
    
    // 页面加载完成后初始化
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initializeNotifications);
    } else {
        // 延迟初始化，确保页面完全加载
        setTimeout(initializeNotifications, 100);
    }
    
    log('实时通知集成脚本加载完成');
})();

// 为了兼容性，也提供一个简单的初始化函数
window.initRealtimeNotifications = function(userId) {
    if (userId) {
        window.currentUserId = userId;
    }
    window.RealtimeNotifications.connect();
};
