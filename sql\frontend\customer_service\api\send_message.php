<?php
// 发送消息API
header('Content-Type: application/json; charset=UTF-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// 处理预检请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

// 只接受POST请求
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['error' => '方法不允许']);
    exit;
}

session_start();

// 检查用户登录状态
if (!isset($_SESSION['user_id'])) {
    http_response_code(401);
    echo json_encode(['error' => '用户未登录']);
    exit;
}

// 获取POST数据
$input = json_decode(file_get_contents('php://input'), true);
$sessionId = $input['sessionId'] ?? '';
$message = trim($input['message'] ?? '');

if (empty($sessionId) || empty($message)) {
    http_response_code(400);
    echo json_encode(['error' => '参数不完整']);
    exit;
}

// 引用数据库配置文件
require_once '../../../houtai_backup/db_config.php';

try {
    $pdo = getDbConnection();

    // 检查或创建会话
    $stmt = $pdo->prepare("SELECT * FROM customer_service_sessions WHERE session_id = ?");
    $stmt->execute([$sessionId]);
    $session = $stmt->fetch(PDO::FETCH_ASSOC);

    if (!$session) {
        // 创建新会话
        $stmt = $pdo->prepare("
            INSERT INTO customer_service_sessions
            (session_id, user_id, user_phone, user_name, status, priority, source, started_at)
            VALUES (?, ?, ?, ?, 'waiting', 'normal', 'web', NOW())
        ");
        $stmt->execute([
            $sessionId,
            $_SESSION['user_id'],
            $_SESSION['phone'] ?? null,
            $_SESSION['nickname'] ?? '用户'
        ]);
    }

    // 保存用户消息
    $stmt = $pdo->prepare("
        INSERT INTO customer_service_messages
        (session_id, sender_type, sender_id, sender_name, message_type, content, created_at)
        VALUES (?, 'user', ?, ?, 'text', ?, NOW())
    ");
    $stmt->execute([
        $sessionId,
        $_SESSION['user_id'],
        $_SESSION['nickname'] ?? '用户',
        $message
    ]);

    // 更新会话消息数量
    $stmt = $pdo->prepare("
        UPDATE customer_service_sessions
        SET message_count = message_count + 1, updated_at = NOW()
        WHERE session_id = ?
    ");
    $stmt->execute([$sessionId]);

    // 如果会话已分配给客服，发送实时通知给后台客服
    $stmt = $pdo->prepare("SELECT customer_service_id FROM customer_service_sessions WHERE session_id = ?");
    $stmt->execute([$sessionId]);
    $session_info = $stmt->fetch(PDO::FETCH_ASSOC);

    if ($session_info && $session_info['customer_service_id']) {
        // 创建给客服的实时通知
        $notificationData = [
            'type' => 'new_user_message',
            'session_id' => $sessionId,
            'user_name' => $_SESSION['nickname'] ?? '用户',
            'message' => $message,
            'timestamp' => time()
        ];

        // 这里可以扩展为给客服发送通知
        // 暂时记录到日志或其他方式
    }

    // 获取机器人配置
    $stmt = $pdo->query("
        SELECT * FROM customer_service_bot_config
        WHERE status = 'active'
        ORDER BY id DESC
        LIMIT 1
    ");
    $botConfig = $stmt->fetch(PDO::FETCH_ASSOC);

    // 生成机器人回复
    $botReply = generateBotReply($message, $botConfig);

    // 保存机器人回复
    if ($botReply) {
        $stmt = $pdo->prepare("
            INSERT INTO customer_service_messages
            (session_id, sender_type, sender_id, sender_name, message_type, content, created_at)
            VALUES (?, 'bot', NULL, ?, 'text', ?, NOW())
        ");
        $stmt->execute([
            $sessionId,
            $botConfig['name'] ?? '趣玩小助手',
            $botReply
        ]);

        // 更新会话消息数量
        $stmt = $pdo->prepare("
            UPDATE customer_service_sessions
            SET message_count = message_count + 1, updated_at = NOW()
            WHERE session_id = ?
        ");
        $stmt->execute([$sessionId]);
    }

    echo json_encode([
        'success' => true,
        'bot_reply' => $botReply,
        'bot_name' => $botConfig['name'] ?? '趣玩小助手'
    ]);

} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => '发送消息失败',
        'message' => $e->getMessage()
    ]);
}

/**
 * 生成机器人回复
 */
function generateBotReply($userMessage, $botConfig) {
    // 简单的关键词匹配回复
    $keywords = [
        '你好' => '您好！很高兴为您服务，请问有什么可以帮助您的吗？',
        '帮助' => '我可以为您提供以下帮助：\n1. 账户相关问题\n2. 功能使用指导\n3. 技术支持\n4. 其他问题咨询',
        '账户' => '关于账户问题，我可以帮您：\n- 登录问题\n- 密码重置\n- 个人信息修改\n- 账户安全设置',
        '密码' => '如果您忘记了密码，可以通过以下方式重置：\n1. 点击登录页面的"忘记密码"\n2. 输入您的手机号\n3. 获取验证码\n4. 设置新密码',
        '登录' => '登录遇到问题了吗？请检查：\n1. 手机号是否正确\n2. 密码是否正确\n3. 网络连接是否正常\n如果仍有问题，我可以为您转接人工客服。',
        '功能' => '趣玩星球主要功能包括：\n- 个人资料管理\n- 社交互动\n- 活动参与\n- 积分系统\n请问您想了解哪个功能？',
        '客服' => '我是智能客服，可以为您解答常见问题。如需人工客服，请说"转人工"。',
        '转人工' => '好的，正在为您转接人工客服，请稍候...',
        '谢谢' => '不客气！如果还有其他问题，随时可以问我哦～',
        '再见' => '再见！感谢您使用趣玩星球，祝您生活愉快！'
    ];

    $userMessage = strtolower($userMessage);

    foreach ($keywords as $keyword => $reply) {
        if (strpos($userMessage, $keyword) !== false) {
            return $reply;
        }
    }

    // 如果没有匹配的关键词，返回默认回复
    return $botConfig['default_reply'] ?? '抱歉，我暂时无法理解您的问题，正在为您转接人工客服，请稍候...';
}
?>
