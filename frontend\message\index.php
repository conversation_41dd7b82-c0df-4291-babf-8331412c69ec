<?php
session_start();
require_once '../../includes/db_config.php';

// 检查用户是否登录
if (!isset($_SESSION['user_id'])) {
    header('Location: ../login/index.php');
    exit();
}

$user_id = $_SESSION['user_id'];

// 连接数据库
try {
    $dsn = "mysql:host={$db_config['host']};dbname={$db_config['dbname']};charset={$db_config['charset']};port={$db_config['port']}";
    $pdo = new PDO($dsn, $db_config['username'], $db_config['password']);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
} catch (PDOException $e) {
    die("数据库连接失败: " . $e->getMessage());
}

// 获取用户的聊天列表（群聊和私聊）
function getChatList($pdo, $user_id) {
    $chats = [];

    // 获取用户参与的群聊
    $stmt = $pdo->prepare("
        SELECT g.id, g.name, g.avatar, g.member_count,
               m.content as last_message, m.created_at as last_message_time,
               u.username as last_sender_name
        FROM groups g
        JOIN group_members gm ON g.id = gm.group_id
        LEFT JOIN messages m ON g.id = m.chat_id AND m.chat_type = 'group'
        LEFT JOIN users u ON m.sender_id = u.id
        WHERE gm.user_id = :user_id
        AND (m.id IS NULL OR m.id = (
            SELECT MAX(id) FROM messages
            WHERE chat_id = g.id AND chat_type = 'group'
        ))
        ORDER BY m.created_at DESC
    ");
    $stmt->execute(['user_id' => $user_id]);
    $groups = $stmt->fetchAll(PDO::FETCH_ASSOC);

    foreach ($groups as $group) {
        $chats[] = [
            'type' => 'group',
            'id' => $group['id'],
            'name' => $group['name'],
            'avatar' => $group['avatar'] ?: 'https://s1.imagehub.cc/images/2025/04/26/group_avatar.jpg',
            'last_message' => $group['last_message'] ?: '暂无消息',
            'last_message_time' => $group['last_message_time'],
            'last_sender_name' => $group['last_sender_name'],
            'member_count' => $group['member_count']
        ];
    }

    return $chats;
}

$chat_list = getChatList($pdo, $user_id);
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <meta name="theme-color" content="#40E0D0">
    <title>消息 - 趣玩星球</title>
    <link rel="stylesheet" href="css/style.css">
    <?php
    include '../includes/bottom_nav.php';
    echo renderBottomNavCSS();
    ?>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <!-- Toast提示 -->
    <div class="toast" id="toast"></div>

    <!-- 顶部导航栏 -->
    <div class="header-bar">
        <div class="header-title">消息</div>
        <div class="header-actions">
            <button class="add-button" id="addButton">
                <i class="fas fa-plus"></i>
            </button>
        </div>
    </div>

    <!-- 标签栏 -->
    <div class="tab-bar">
        <div class="tab-item active" data-tab="messages">消息</div>
        <div class="tab-item" data-tab="friends">好友</div>
        <div class="tab-item" data-tab="groups">群聊</div>
    </div>

    <!-- 内容区域 -->
    <div class="content-area">
        <!-- 消息列表 -->
        <div class="tab-content active" id="messages">
            <div class="chat-list">
                <?php if (empty($chat_list)): ?>
                    <div class="empty-state">
                        <i class="fas fa-comments"></i>
                        <p>暂无聊天记录</p>
                        <p>开始与朋友聊天吧！</p>
                    </div>
                <?php else: ?>
                    <?php foreach ($chat_list as $chat): ?>
                        <div class="chat-item" onclick="openChat('<?php echo $chat['type']; ?>', <?php echo $chat['id']; ?>)">
                            <div class="chat-avatar">
                                <img src="<?php echo htmlspecialchars($chat['avatar']); ?>" alt="头像">
                                <?php if ($chat['type'] === 'group'): ?>
                                    <span class="member-count"><?php echo $chat['member_count']; ?></span>
                                <?php endif; ?>
                            </div>
                            <div class="chat-info">
                                <div class="chat-name"><?php echo htmlspecialchars($chat['name']); ?></div>
                                <div class="chat-last-message">
                                    <?php if ($chat['last_sender_name']): ?>
                                        <span class="sender-name"><?php echo htmlspecialchars($chat['last_sender_name']); ?>:</span>
                                    <?php endif; ?>
                                    <?php echo htmlspecialchars($chat['last_message']); ?>
                                </div>
                            </div>
                            <div class="chat-meta">
                                <?php if ($chat['last_message_time']): ?>
                                    <div class="chat-time"><?php echo date('H:i', strtotime($chat['last_message_time'])); ?></div>
                                <?php endif; ?>
                            </div>
                        </div>
                    <?php endforeach; ?>
                <?php endif; ?>
            </div>
        </div>

        <!-- 好友列表 -->
        <div class="tab-content" id="friends">
            <div class="empty-state">
                <i class="fas fa-user-friends"></i>
                <p>暂无好友</p>
                <p>添加好友开始聊天吧！</p>
            </div>
        </div>

        <!-- 群聊列表 -->
        <div class="tab-content" id="groups">
            <div class="group-list">
                <?php foreach ($chat_list as $chat): ?>
                    <?php if ($chat['type'] === 'group'): ?>
                        <div class="group-item" onclick="openChat('group', <?php echo $chat['id']; ?>)">
                            <div class="group-avatar">
                                <img src="<?php echo htmlspecialchars($chat['avatar']); ?>" alt="群头像">
                            </div>
                            <div class="group-info">
                                <div class="group-name"><?php echo htmlspecialchars($chat['name']); ?></div>
                                <div class="group-members"><?php echo $chat['member_count']; ?>人</div>
                            </div>
                        </div>
                    <?php endif; ?>
                <?php endforeach; ?>
            </div>
        </div>
    </div>

    <!-- 添加功能弹窗 -->
    <div class="add-menu" id="addMenu">
        <div class="add-menu-overlay" onclick="closeAddMenu()"></div>
        <div class="add-menu-content">
            <div class="add-menu-item" onclick="scanQR()">
                <i class="fas fa-qrcode"></i>
                <span>扫一扫</span>
            </div>
            <div class="add-menu-item" onclick="createGroup()">
                <i class="fas fa-users"></i>
                <span>创建群聊</span>
            </div>
            <div class="add-menu-item" onclick="addFriend()">
                <i class="fas fa-user-plus"></i>
                <span>添加好友</span>
            </div>
        </div>
    </div>

    <?php echo renderBottomNav('message', true); ?>

    <script src="js/script.js"></script>
    <?php echo renderBottomNavJS(true); ?>
</body>
</html>
