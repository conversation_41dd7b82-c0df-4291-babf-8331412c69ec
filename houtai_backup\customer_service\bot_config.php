<?php
/**
 * 智能客服机器人配置页面
 * 包含机器人基础设置、工作时间、自动转人工等配置
 */

session_start();

// 检查管理员登录状态
if (!isset($_SESSION['admin_id'])) {
    header('Location: ../login.php');
    exit;
}

$admin_id = $_SESSION['admin_id'];
$admin_username = $_SESSION['admin_username'] ?? '管理员';

// 处理AJAX请求
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    header('Content-Type: application/json; charset=utf-8');
    
    try {
        $pdo = new PDO(
            "mysql:host=localhost;dbname=quwanplanet;charset=utf8mb4",
            "quwanplanet",
            "nJmJm23FB4Xn6Fc3",
            [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC
            ]
        );
        
        switch ($_POST['action']) {
            case 'save_config':
                $bot_name = trim($_POST['bot_name'] ?? '');
                $bot_description = trim($_POST['bot_description'] ?? '');
                $bot_avatar = trim($_POST['bot_avatar'] ?? '');
                $welcome_message = trim($_POST['welcome_message'] ?? '');
                $default_reply = trim($_POST['default_reply'] ?? '');
                $is_enabled = intval($_POST['is_enabled'] ?? 1);
                $greeting_delay = intval($_POST['greeting_delay'] ?? 2);
                $typing_delay = intval($_POST['typing_delay'] ?? 1);
                $max_session_duration = intval($_POST['max_session_duration'] ?? 1800);
                $enable_smart_suggestions = intval($_POST['enable_smart_suggestions'] ?? 1);
                $enable_satisfaction_survey = intval($_POST['enable_satisfaction_survey'] ?? 1);
                $fallback_to_human = intval($_POST['fallback_to_human'] ?? 1);
                $collect_user_info = intval($_POST['collect_user_info'] ?? 0);
                
                // 工作时间配置
                $working_hours = [
                    'enabled' => intval($_POST['working_hours_enabled'] ?? 1),
                    'timezone' => $_POST['timezone'] ?? 'Asia/Shanghai',
                    'schedule' => []
                ];
                
                $days = ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday'];
                foreach ($days as $day) {
                    if (isset($_POST["work_{$day}"])) {
                        $working_hours['schedule'][] = [
                            'day' => $day,
                            'start' => $_POST["work_{$day}_start"] ?? '09:00',
                            'end' => $_POST["work_{$day}_end"] ?? '18:00'
                        ];
                    }
                }
                
                // 自动转人工关键词
                $auto_transfer_keywords = [];
                if (!empty($_POST['auto_transfer_keywords'])) {
                    $keywords = explode(',', $_POST['auto_transfer_keywords']);
                    foreach ($keywords as $keyword) {
                        $keyword = trim($keyword);
                        if (!empty($keyword)) {
                            $auto_transfer_keywords[] = $keyword;
                        }
                    }
                }
                
                // 更新或插入配置
                $stmt = $pdo->prepare("
                    INSERT INTO customer_service_bot 
                    (id, bot_name, bot_description, bot_avatar, welcome_message, default_reply, 
                     is_enabled, greeting_delay, typing_delay, max_session_duration,
                     enable_smart_suggestions, enable_satisfaction_survey, fallback_to_human, 
                     collect_user_info, working_hours, auto_transfer_keywords, updated_at)
                    VALUES (1, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW())
                    ON DUPLICATE KEY UPDATE
                    bot_name = VALUES(bot_name),
                    bot_description = VALUES(bot_description),
                    bot_avatar = VALUES(bot_avatar),
                    welcome_message = VALUES(welcome_message),
                    default_reply = VALUES(default_reply),
                    is_enabled = VALUES(is_enabled),
                    greeting_delay = VALUES(greeting_delay),
                    typing_delay = VALUES(typing_delay),
                    max_session_duration = VALUES(max_session_duration),
                    enable_smart_suggestions = VALUES(enable_smart_suggestions),
                    enable_satisfaction_survey = VALUES(enable_satisfaction_survey),
                    fallback_to_human = VALUES(fallback_to_human),
                    collect_user_info = VALUES(collect_user_info),
                    working_hours = VALUES(working_hours),
                    auto_transfer_keywords = VALUES(auto_transfer_keywords),
                    updated_at = NOW()
                ");
                
                $stmt->execute([
                    $bot_name, $bot_description, $bot_avatar, $welcome_message, $default_reply,
                    $is_enabled, $greeting_delay, $typing_delay, $max_session_duration,
                    $enable_smart_suggestions, $enable_satisfaction_survey, $fallback_to_human,
                    $collect_user_info, json_encode($working_hours), json_encode($auto_transfer_keywords)
                ]);
                
                echo json_encode(['success' => true, 'message' => '配置保存成功']);
                exit;
                
            case 'test_avatar':
                $avatar_url = trim($_POST['avatar_url'] ?? '');
                if (empty($avatar_url)) {
                    echo json_encode(['success' => false, 'message' => '请输入头像URL']);
                    exit;
                }
                
                // 简单的URL验证
                if (!filter_var($avatar_url, FILTER_VALIDATE_URL)) {
                    echo json_encode(['success' => false, 'message' => '无效的URL格式']);
                    exit;
                }
                
                echo json_encode(['success' => true, 'message' => '头像URL有效', 'url' => $avatar_url]);
                exit;
                
            default:
                echo json_encode(['success' => false, 'message' => '未知操作']);
                exit;
        }
        
    } catch (Exception $e) {
        echo json_encode(['success' => false, 'message' => '操作失败：' . $e->getMessage()]);
        exit;
    }
}

// 获取当前配置
try {
    $pdo = new PDO(
        "mysql:host=localhost;dbname=quwanplanet;charset=utf8mb4",
        "quwanplanet",
        "nJmJm23FB4Xn6Fc3",
        [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC
        ]
    );
    
    $stmt = $pdo->query("SELECT * FROM customer_service_bot WHERE id = 1");
    $config = $stmt->fetch();
    
    if (!$config) {
        // 创建默认配置
        $default_config = [
            'bot_name' => '趣玩小助手',
            'bot_description' => '趣玩星球智能客服助手，7x24小时为您服务',
            'bot_avatar' => 'https://s1.imagehub.cc/images/2025/05/26/2cdd63b39f96072fc60cd733027d2a30.png',
            'welcome_message' => "您好！我是趣玩星球智能客服小助手🤖\n\n我可以帮助您解决以下问题：\n• 账号相关问题\n• 功能使用指导\n• 常见问题解答\n\n请描述您遇到的问题，我会尽力为您解答！",
            'default_reply' => "抱歉，我暂时无法理解您的问题😅\n\n您可以：\n• 换个方式描述问题\n• 联系人工客服\n• 查看帮助文档\n\n如需人工客服，请回复\"人工客服\"",
            'is_enabled' => 1,
            'greeting_delay' => 2,
            'typing_delay' => 1,
            'max_session_duration' => 1800,
            'enable_smart_suggestions' => 1,
            'enable_satisfaction_survey' => 1,
            'fallback_to_human' => 1,
            'collect_user_info' => 0,
            'working_hours' => json_encode([
                'enabled' => true,
                'timezone' => 'Asia/Shanghai',
                'schedule' => [
                    ['day' => 'monday', 'start' => '09:00', 'end' => '18:00'],
                    ['day' => 'tuesday', 'start' => '09:00', 'end' => '18:00'],
                    ['day' => 'wednesday', 'start' => '09:00', 'end' => '18:00'],
                    ['day' => 'thursday', 'start' => '09:00', 'end' => '18:00'],
                    ['day' => 'friday', 'start' => '09:00', 'end' => '18:00']
                ]
            ]),
            'auto_transfer_keywords' => json_encode(['人工客服', '转人工', '人工', '客服', '投诉', '退款'])
        ];
        
        $config = $default_config;
    }
    
} catch (Exception $e) {
    $error_message = '数据库连接失败：' . $e->getMessage();
    $config = null;
}

// 解析JSON配置
$working_hours = [];
$auto_transfer_keywords = [];

if ($config) {
    if (!empty($config['working_hours'])) {
        $working_hours = json_decode($config['working_hours'], true) ?: [];
    }
    if (!empty($config['auto_transfer_keywords'])) {
        $auto_transfer_keywords = json_decode($config['auto_transfer_keywords'], true) ?: [];
    }
}
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>机器人配置 - 趣玩星球管理后台</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        .main-content {
            margin-left: 250px;
            padding: 20px;
        }
        .config-card {
            background: white;
            border-radius: 16px;
            padding: 24px;
            margin-bottom: 20px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            border: none;
        }
        .config-card h5 {
            color: #2d3748;
            font-weight: 600;
            margin-bottom: 20px;
            padding-bottom: 12px;
            border-bottom: 2px solid #f1f5f9;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .config-card h5 i {
            color: #667eea;
        }
        .avatar-preview {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            object-fit: cover;
            border: 3px solid #e2e8f0;
        }
        .working-hours-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 16px;
        }
        .day-config {
            background: #f8f9fa;
            padding: 16px;
            border-radius: 8px;
            border: 1px solid #e9ecef;
        }
        .keyword-tag {
            background: #667eea;
            color: white;
            padding: 4px 12px;
            border-radius: 16px;
            font-size: 0.875rem;
            margin: 2px;
            display: inline-block;
        }
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
        }
        .btn-primary:hover {
            background: linear-gradient(135deg, #5a67d8 0%, #805ad5 100%);
            transform: translateY(-1px);
        }
    </style>
</head>
<body>
    <?php include '../includes/sidebar.php'; ?>
    
    <div class="main-content">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <div>
                <h2><i class="bi bi-robot"></i> 机器人配置</h2>
                <p class="text-muted mb-0">配置智能客服机器人的基础信息和行为设置</p>
            </div>
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="../dashboard.php">首页</a></li>
                    <li class="breadcrumb-item"><a href="index.php">智能客服管理</a></li>
                    <li class="breadcrumb-item active">机器人配置</li>
                </ol>
            </nav>
        </div>

        <?php if (isset($error_message)): ?>
            <div class="alert alert-danger">
                <i class="bi bi-exclamation-triangle"></i> <?= htmlspecialchars($error_message) ?>
            </div>
        <?php endif; ?>

        <form id="botConfigForm">
            <!-- 基础配置 -->
            <div class="config-card">
                <h5><i class="bi bi-gear"></i> 基础配置</h5>
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label">机器人名称</label>
                            <input type="text" class="form-control" name="bot_name" 
                                   value="<?= htmlspecialchars($config['bot_name'] ?? '') ?>" required>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">机器人描述</label>
                            <textarea class="form-control" name="bot_description" rows="3"><?= htmlspecialchars($config['bot_description'] ?? '') ?></textarea>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">启用状态</label>
                            <div class="form-check form-switch">
                                <input class="form-check-input" type="checkbox" name="is_enabled" value="1" 
                                       <?= ($config['is_enabled'] ?? 1) ? 'checked' : '' ?>>
                                <label class="form-check-label">启用机器人</label>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label">机器人头像</label>
                            <div class="input-group">
                                <input type="url" class="form-control" name="bot_avatar" id="botAvatar"
                                       value="<?= htmlspecialchars($config['bot_avatar'] ?? '') ?>" 
                                       placeholder="https://example.com/avatar.png">
                                <button type="button" class="btn btn-outline-secondary" onclick="testAvatar()">测试</button>
                            </div>
                            <div class="mt-2">
                                <img id="avatarPreview" src="<?= htmlspecialchars($config['bot_avatar'] ?? '') ?>" 
                                     alt="头像预览" class="avatar-preview" style="<?= empty($config['bot_avatar']) ? 'display:none;' : '' ?>">
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 消息配置 -->
            <div class="config-card">
                <h5><i class="bi bi-chat-dots"></i> 消息配置</h5>
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label">欢迎消息</label>
                            <textarea class="form-control" name="welcome_message" rows="6" required><?= htmlspecialchars($config['welcome_message'] ?? '') ?></textarea>
                            <div class="form-text">用户开始对话时显示的欢迎语</div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label">默认回复</label>
                            <textarea class="form-control" name="default_reply" rows="6" required><?= htmlspecialchars($config['default_reply'] ?? '') ?></textarea>
                            <div class="form-text">无法匹配任何规则时的默认回复</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 行为配置 -->
            <div class="config-card">
                <h5><i class="bi bi-sliders"></i> 行为配置</h5>
                <div class="row">
                    <div class="col-md-3">
                        <div class="mb-3">
                            <label class="form-label">问候延迟 (秒)</label>
                            <input type="number" class="form-control" name="greeting_delay" min="0" max="10"
                                   value="<?= htmlspecialchars($config['greeting_delay'] ?? 2) ?>">
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="mb-3">
                            <label class="form-label">打字延迟 (秒)</label>
                            <input type="number" class="form-control" name="typing_delay" min="0" max="5"
                                   value="<?= htmlspecialchars($config['typing_delay'] ?? 1) ?>">
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="mb-3">
                            <label class="form-label">会话时长 (秒)</label>
                            <input type="number" class="form-control" name="max_session_duration" min="300" max="7200"
                                   value="<?= htmlspecialchars($config['max_session_duration'] ?? 1800) ?>">
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="mb-3">
                            <label class="form-label">功能开关</label>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" name="enable_smart_suggestions" value="1"
                                       <?= ($config['enable_smart_suggestions'] ?? 1) ? 'checked' : '' ?>>
                                <label class="form-check-label">智能建议</label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" name="enable_satisfaction_survey" value="1"
                                       <?= ($config['enable_satisfaction_survey'] ?? 1) ? 'checked' : '' ?>>
                                <label class="form-check-label">满意度调查</label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" name="fallback_to_human" value="1"
                                       <?= ($config['fallback_to_human'] ?? 1) ? 'checked' : '' ?>>
                                <label class="form-check-label">自动转人工</label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" name="collect_user_info" value="1"
                                       <?= ($config['collect_user_info'] ?? 0) ? 'checked' : '' ?>>
                                <label class="form-check-label">收集用户信息</label>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 自动转人工关键词 -->
            <div class="config-card">
                <h5><i class="bi bi-arrow-right-circle"></i> 自动转人工关键词</h5>
                <div class="mb-3">
                    <label class="form-label">关键词列表</label>
                    <input type="text" class="form-control" name="auto_transfer_keywords" 
                           value="<?= htmlspecialchars(implode(', ', $auto_transfer_keywords)) ?>"
                           placeholder="人工客服, 转人工, 投诉, 退款">
                    <div class="form-text">用逗号分隔多个关键词，用户发送包含这些关键词的消息时会自动转人工</div>
                </div>
                <div class="mt-2">
                    <?php foreach ($auto_transfer_keywords as $keyword): ?>
                        <span class="keyword-tag"><?= htmlspecialchars($keyword) ?></span>
                    <?php endforeach; ?>
                </div>
            </div>

            <!-- 保存按钮 -->
            <div class="d-flex justify-content-end gap-2">
                <a href="index.php" class="btn btn-secondary">
                    <i class="bi bi-arrow-left"></i> 返回
                </a>
                <button type="submit" class="btn btn-primary">
                    <i class="bi bi-check-lg"></i> 保存配置
                </button>
            </div>
        </form>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // 测试头像
        function testAvatar() {
            const avatarUrl = document.getElementById('botAvatar').value;
            if (!avatarUrl) {
                alert('请输入头像URL');
                return;
            }
            
            const preview = document.getElementById('avatarPreview');
            preview.src = avatarUrl;
            preview.style.display = 'block';
            preview.onerror = function() {
                alert('头像加载失败，请检查URL是否正确');
                this.style.display = 'none';
            };
        }

        // 保存配置
        document.getElementById('botConfigForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const formData = new FormData(this);
            formData.append('action', 'save_config');
            
            try {
                const response = await fetch('', {
                    method: 'POST',
                    body: formData
                });
                
                const data = await response.json();
                
                if (data.success) {
                    alert('配置保存成功！');
                } else {
                    alert('保存失败：' + data.message);
                }
            } catch (error) {
                alert('保存失败：' + error.message);
            }
        });

        // 头像URL输入时实时预览
        document.getElementById('botAvatar').addEventListener('input', function() {
            const url = this.value;
            if (url) {
                testAvatar();
            }
        });
    </script>
</body>
</html>
