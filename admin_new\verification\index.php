<?php
// 设置字符编码
header('Content-Type: text/html; charset=UTF-8');
ini_set('default_charset', 'UTF-8');

session_start();

// 检查登录状态
if (!isset($_SESSION['admin_id'])) {
    header('Location: login.php');
    exit;
}

// 直接引用当前目录的数据库配置文件
require_once 'db_config.php';

try {
    $pdo = getDbConnection();

    // 获取筛选条件
    $status_filter = $_GET['status'] ?? 'all';
    $search = $_GET['search'] ?? '';
    $page = max(1, intval($_GET['page'] ?? 1));
    $per_page = 20;
    $offset = ($page - 1) * $per_page;

    // 构建查询条件
    $where_conditions = [];
    $params = [];

    if ($status_filter !== 'all') {
        $where_conditions[] = "rv.verification_status = ?";
        $params[] = $status_filter;
    }

    if (!empty($search)) {
        $where_conditions[] = "(rv.real_name LIKE ? OR rv.id_card_number LIKE ? OR u.username LIKE ?)";
        $search_param = "%$search%";
        $params[] = $search_param;
        $params[] = $search_param;
        $params[] = $search_param;
    }

    $where_clause = !empty($where_conditions) ? 'WHERE ' . implode(' AND ', $where_conditions) : '';

    // 获取总数
    $count_sql = "
        SELECT COUNT(*)
        FROM realname_verification rv
        LEFT JOIN users u ON rv.user_id = u.id
        $where_clause
    ";
    $count_stmt = $pdo->prepare($count_sql);
    $count_stmt->execute($params);
    $total_count = $count_stmt->fetchColumn();

    // 获取列表数据
    $sql = "
        SELECT
            rv.*,
            u.username,
            u.phone,
            au.name as admin_name
        FROM realname_verification rv
        LEFT JOIN users u ON rv.user_id = u.id
        LEFT JOIN admin_users au ON rv.verified_by = au.id
        $where_clause
        ORDER BY rv.submitted_at DESC
        LIMIT $per_page OFFSET $offset
    ";

    $stmt = $pdo->prepare($sql);
    $stmt->execute($params);
    $verifications = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // 计算分页
    $total_pages = ceil($total_count / $per_page);

    // 获取统计数据
    $stats_sql = "
        SELECT
            verification_status,
            COUNT(*) as count
        FROM realname_verification
        GROUP BY verification_status
    ";
    $stats_stmt = $pdo->query($stats_sql);
    $stats = [];
    while ($row = $stats_stmt->fetch(PDO::FETCH_ASSOC)) {
        $stats[$row['verification_status']] = $row['count'];
    }

} catch (PDOException $e) {
    $error_message = '数据库错误：' . $e->getMessage();
}

// 状态标签
function getStatusBadge($status) {
    switch ($status) {
        case 'pending':
            return '<span class="badge badge-warning"><i class="fas fa-clock"></i> 待审核</span>';
        case 'approved':
            return '<span class="badge badge-success"><i class="fas fa-check-circle"></i> 已通过</span>';
        case 'rejected':
            return '<span class="badge badge-danger"><i class="fas fa-times-circle"></i> 已拒绝</span>';
        default:
            return '<span class="badge badge-secondary">未知</span>';
    }
}
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>实名认证审核管理</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #333;
            min-height: 100vh;
        }

        .header {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            padding: 20px 30px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
            display: flex;
            justify-content: space-between;
            align-items: center;
            border-bottom: 1px solid rgba(255,255,255,0.2);
        }

        .header h1 {
            color: #2c3e50;
            font-size: 28px;
            font-weight: 700;
            background: linear-gradient(135deg, #667eea, #764ba2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .user-info {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .logout-btn {
            background: linear-gradient(135deg, #ff6b6b, #ee5a52);
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 25px;
            text-decoration: none;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(238, 90, 82, 0.3);
        }

        .logout-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(238, 90, 82, 0.4);
        }

        .container {
            max-width: 1200px;
            margin: 20px auto;
            padding: 0 20px;
        }

        .stats-cards {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .stat-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            padding: 25px;
            border-radius: 20px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
            text-align: center;
            border: 1px solid rgba(255,255,255,0.2);
            transition: all 0.3s ease;
        }

        .stat-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 40px rgba(0,0,0,0.15);
        }

        .stat-card h3 {
            font-size: 32px;
            margin-bottom: 10px;
        }

        .stat-card.pending h3 { color: #f39c12; }
        .stat-card.approved h3 { color: #27ae60; }
        .stat-card.rejected h3 { color: #e74c3c; }
        .stat-card.total h3 { color: #3498db; }

        .filters {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }

        .filter-row {
            display: flex;
            gap: 15px;
            align-items: center;
            flex-wrap: wrap;
        }

        .filter-group {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .filter-group select,
        .filter-group input {
            padding: 8px 12px;
            border: 2px solid #e1e5e9;
            border-radius: 6px;
            font-size: 14px;
        }

        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 5px;
            font-size: 14px;
        }

        .btn-primary {
            background: #3498db;
            color: white;
        }

        .table-container {
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .table {
            width: 100%;
            border-collapse: collapse;
        }

        .table th,
        .table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #eee;
        }

        .table th {
            background: #f8f9fa;
            font-weight: 600;
            color: #555;
        }

        .table tr:hover {
            background: #f8f9fa;
        }

        .badge {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
            display: inline-flex;
            align-items: center;
            gap: 4px;
        }

        .badge-warning {
            background: #fff3cd;
            color: #856404;
        }

        .badge-success {
            background: #d4edda;
            color: #155724;
        }

        .badge-danger {
            background: #f8d7da;
            color: #721c24;
        }

        .actions {
            display: flex;
            gap: 5px;
        }

        .btn-sm {
            padding: 4px 8px;
            font-size: 12px;
        }

        .btn-success {
            background: #27ae60;
            color: white;
        }

        .btn-danger {
            background: #e74c3c;
            color: white;
        }

        .btn-info {
            background: #3498db;
            color: white;
        }

        .pagination {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 10px;
            margin-top: 20px;
        }

        .pagination a,
        .pagination span {
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            text-decoration: none;
            color: #333;
        }

        .pagination .current {
            background: #3498db;
            color: white;
            border-color: #3498db;
        }

        .id-card {
            font-family: monospace;
            font-size: 13px;
        }

        @media (max-width: 768px) {
            .filter-row {
                flex-direction: column;
                align-items: stretch;
            }

            .table-container {
                overflow-x: auto;
            }

            .actions {
                flex-direction: column;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1><i class="fas fa-user-check"></i> 实名认证审核管理</h1>
        <div class="user-info">
            <span>欢迎，<?php echo htmlspecialchars($_SESSION['admin_name']); ?></span>
            <a href="logout.php" class="logout-btn">
                <i class="fas fa-sign-out-alt"></i> 退出
            </a>
        </div>
    </div>

    <div class="container">
        <!-- 统计卡片 -->
        <div class="stats-cards">
            <div class="stat-card pending">
                <h3><?php echo $stats['pending'] ?? 0; ?></h3>
                <p>待审核</p>
            </div>
            <div class="stat-card approved">
                <h3><?php echo $stats['approved'] ?? 0; ?></h3>
                <p>已通过</p>
            </div>
            <div class="stat-card rejected">
                <h3><?php echo $stats['rejected'] ?? 0; ?></h3>
                <p>已拒绝</p>
            </div>
            <div class="stat-card total">
                <h3><?php echo $total_count; ?></h3>
                <p>总计</p>
            </div>
        </div>

        <!-- 筛选器 -->
        <div class="filters">
            <form method="GET" class="filter-row">
                <div class="filter-group">
                    <label>状态：</label>
                    <select name="status">
                        <option value="all" <?php echo $status_filter === 'all' ? 'selected' : ''; ?>>全部</option>
                        <option value="pending" <?php echo $status_filter === 'pending' ? 'selected' : ''; ?>>待审核</option>
                        <option value="approved" <?php echo $status_filter === 'approved' ? 'selected' : ''; ?>>已通过</option>
                        <option value="rejected" <?php echo $status_filter === 'rejected' ? 'selected' : ''; ?>>已拒绝</option>
                    </select>
                </div>

                <div class="filter-group">
                    <label>搜索：</label>
                    <input type="text" name="search" placeholder="姓名、身份证号、用户名"
                           value="<?php echo htmlspecialchars($search); ?>">
                </div>

                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-search"></i> 搜索
                </button>
            </form>
        </div>

        <!-- 数据表格 -->
        <div class="table-container">
            <table class="table">
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>用户信息</th>
                        <th>真实姓名</th>
                        <th>身份证号</th>
                        <th>身份证照片</th>
                        <th>状态</th>
                        <th>提交时间</th>
                        <th>审核人</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody>
                    <?php if (empty($verifications)): ?>
                        <tr>
                            <td colspan="8" style="text-align: center; padding: 40px; color: #999;">
                                <i class="fas fa-inbox"></i><br>
                                暂无数据
                            </td>
                        </tr>
                    <?php else: ?>
                        <?php foreach ($verifications as $item): ?>
                            <tr>
                                <td><?php echo $item['id']; ?></td>
                                <td>
                                    <div><?php echo htmlspecialchars($item['username']); ?></div>
                                    <small style="color: #666;"><?php echo htmlspecialchars($item['phone'] ?? ''); ?></small>
                                </td>
                                <td><?php echo htmlspecialchars($item['real_name']); ?></td>
                                <td>
                                    <?php
                                    $id_card = $item['id_card_number'];
                                    if (strlen($id_card) >= 10) {
                                        echo htmlspecialchars(substr($id_card, 0, 6) . '****' . substr($id_card, -4));
                                    } else {
                                        echo '****';
                                    }
                                    ?>
                                </td>
                                <td>
                                    <div class="id-card-photos">
                                        <?php if (!empty($item['id_card_front_url'])): ?>
                                            <img src="<?php echo htmlspecialchars($item['id_card_front_url']); ?>"
                                                 alt="身份证正面" class="id-card-thumb"
                                                 onclick="showImageModal('<?php echo htmlspecialchars($item['id_card_front_url']); ?>', '身份证正面')">
                                        <?php endif; ?>
                                        <?php if (!empty($item['id_card_back_url'])): ?>
                                            <img src="<?php echo htmlspecialchars($item['id_card_back_url']); ?>"
                                                 alt="身份证反面" class="id-card-thumb"
                                                 onclick="showImageModal('<?php echo htmlspecialchars($item['id_card_back_url']); ?>', '身份证反面')">
                                        <?php endif; ?>
                                        <?php if (empty($item['id_card_front_url']) && empty($item['id_card_back_url'])): ?>
                                            <span class="no-image">暂无照片</span>
                                        <?php endif; ?>
                                    </div>
                                </td>
                                <td><?php echo getStatusBadge($item['verification_status']); ?></td>
                                <td>
                                    <?php
                                    if ($item['submitted_at'] && $item['submitted_at'] !== '0000-00-00 00:00:00') {
                                        echo date('Y-m-d H:i', strtotime($item['submitted_at']));
                                    } else {
                                        echo '-';
                                    }
                                    ?>
                                </td>
                                <td><?php echo htmlspecialchars($item['admin_name'] ?? '-'); ?></td>
                                <td>
                                    <div class="actions">
                                        <a href="verification_detail.php?id=<?php echo $item['id']; ?>"
                                           class="btn btn-info btn-sm">
                                            <i class="fas fa-eye"></i> 查看
                                        </a>
                                        <?php if ($item['verification_status'] === 'pending'): ?>
                                            <a href="verification_approve.php?id=<?php echo $item['id']; ?>"
                                               class="btn btn-success btn-sm">
                                                <i class="fas fa-check"></i> 通过
                                            </a>
                                            <a href="verification_reject.php?id=<?php echo $item['id']; ?>"
                                               class="btn btn-danger btn-sm">
                                                <i class="fas fa-times"></i> 拒绝
                                            </a>
                                        <?php endif; ?>
                                    </div>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    <?php endif; ?>
                </tbody>
            </table>
        </div>

        <!-- 分页 -->
        <?php if ($total_pages > 1): ?>
            <div class="pagination">
                <?php if ($page > 1): ?>
                    <a href="?page=<?php echo $page - 1; ?>&status=<?php echo $status_filter; ?>&search=<?php echo urlencode($search); ?>">
                        <i class="fas fa-chevron-left"></i> 上一页
                    </a>
                <?php endif; ?>

                <?php for ($i = max(1, $page - 2); $i <= min($total_pages, $page + 2); $i++): ?>
                    <?php if ($i === $page): ?>
                        <span class="current"><?php echo $i; ?></span>
                    <?php else: ?>
                        <a href="?page=<?php echo $i; ?>&status=<?php echo $status_filter; ?>&search=<?php echo urlencode($search); ?>">
                            <?php echo $i; ?>
                        </a>
                    <?php endif; ?>
                <?php endfor; ?>

                <?php if ($page < $total_pages): ?>
                    <a href="?page=<?php echo $page + 1; ?>&status=<?php echo $status_filter; ?>&search=<?php echo urlencode($search); ?>">
                        下一页 <i class="fas fa-chevron-right"></i>
                    </a>
                <?php endif; ?>
            </div>
        <?php endif; ?>
    </div>

    <!-- 图片查看模态框 -->
    <div id="imageModal" class="modal" style="display: none;">
        <div class="modal-content">
            <span class="close" onclick="closeImageModal()">&times;</span>
            <h3 id="modalTitle">图片查看</h3>
            <img id="modalImage" src="" alt="身份证照片">
        </div>
    </div>

    <script>
        function showImageModal(imageUrl, title) {
            document.getElementById('modalImage').src = imageUrl;
            document.getElementById('modalTitle').textContent = title;
            document.getElementById('imageModal').style.display = 'block';
        }

        function closeImageModal() {
            document.getElementById('imageModal').style.display = 'none';
        }

        // 点击模态框外部关闭
        window.onclick = function(event) {
            const modal = document.getElementById('imageModal');
            if (event.target === modal) {
                closeImageModal();
            }
        }

        // ESC键关闭模态框
        document.addEventListener('keydown', function(event) {
            if (event.key === 'Escape') {
                closeImageModal();
            }
        });
    </script>

    <style>
        /* 身份证照片缩略图样式 */
        .id-card-photos {
            display: flex;
            gap: 5px;
            align-items: center;
        }

        .id-card-thumb {
            width: 40px;
            height: 25px;
            object-fit: cover;
            border-radius: 4px;
            cursor: pointer;
            border: 1px solid #ddd;
            transition: all 0.3s ease;
        }

        .id-card-thumb:hover {
            transform: scale(1.1);
            border-color: #40E0D0;
            box-shadow: 0 2px 8px rgba(64, 224, 208, 0.3);
        }

        .no-image {
            color: #999;
            font-size: 12px;
            font-style: italic;
        }

        /* 模态框样式 */
        .modal {
            position: fixed;
            z-index: 10000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.8);
            backdrop-filter: blur(5px);
        }

        .modal-content {
            position: relative;
            background-color: white;
            margin: 5% auto;
            padding: 20px;
            border-radius: 12px;
            width: 80%;
            max-width: 600px;
            max-height: 80vh;
            overflow: auto;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
        }

        .modal-content h3 {
            margin-top: 0;
            color: #333;
            text-align: center;
            margin-bottom: 20px;
        }

        .modal-content img {
            width: 100%;
            height: auto;
            border-radius: 8px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        }

        .close {
            position: absolute;
            right: 15px;
            top: 15px;
            color: #aaa;
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
            transition: color 0.3s ease;
        }

        .close:hover {
            color: #ff4757;
        }
    </style>
</body>
</html>
