<?php
/**
 * 统一底部导航栏组件
 *
 * 使用方法：
 * include '../includes/bottom_nav.php';
 * echo renderBottomNav('home'); // 传入当前页面标识
 */

function renderBottomNav($currentPage = '', $isLoggedIn = false) {
    $navItems = [
        'home' => [
            'url' => '../home/<USER>',
            'label' => '首页',
            'requireLogin' => false
        ],
        'guide' => [
            'url' => '../guide/index.php',
            'label' => '攻略',
            'requireLogin' => false
        ],
        'publish' => [
            'url' => '#',
            'label' => '',
            'requireLogin' => true,
            'isSpecial' => true
        ],
        'message' => [
            'url' => '../message/index.php',
            'label' => '消息',
            'requireLogin' => true
        ],
        'profile' => [
            'url' => '../profile/index.php',
            'label' => $isLoggedIn ? '我的' : '未登录',
            'requireLogin' => false
        ]
    ];

    $html = '<div class="bottom-nav">';

    foreach ($navItems as $key => $item) {
        $activeClass = ($currentPage === $key) ? ' active' : '';
        $url = ($item['requireLogin'] && !$isLoggedIn) ? '../login/index.php' : $item['url'];

        if (isset($item['isSpecial']) && $item['isSpecial']) {
            // 特殊的加号按钮
            $html .= '<a href="#" class="nav-item" onclick="showPublishMenu(); return false;">';
            $html .= '<div class="nav-add-button"></div>';
            $html .= '</a>';
        } else {
            $html .= '<a href="' . $url . '" class="nav-item' . $activeClass . '">';
            $html .= '<span>' . $item['label'] . '</span>';
            $html .= '</a>';
        }
    }

    $html .= '</div>';

    // 添加发布菜单弹窗和实名认证弹窗
    $html .= '
    <!-- 发布菜单弹窗 -->
    <div class="publish-menu" id="publishMenu">
        <div class="publish-menu-overlay" onclick="closePublishMenu()"></div>
        <div class="publish-menu-content">
            <div class="publish-menu-item" onclick="publishWork()">
                <i class="fas fa-camera"></i>
                <span>发布作品</span>
                <small>分享精彩瞬间</small>
            </div>
            <div class="publish-menu-item" onclick="publishActivity()">
                <i class="fas fa-users"></i>
                <span>发布组局</span>
                <small>邀请朋友一起玩</small>
            </div>
        </div>
    </div>

    <!-- 自定义弹窗 -->
    <div class="custom-dialog" id="customDialog">
        <div class="custom-dialog-overlay" onclick="closeCustomDialog()"></div>
        <div class="custom-dialog-content">
            <div class="dialog-icon" id="dialogIcon">🔒</div>
            <h3 class="dialog-title" id="dialogTitle">标题</h3>
            <p class="dialog-message" id="dialogMessage">消息内容</p>
            <p class="dialog-description" id="dialogDescription">详细描述</p>
            <div class="dialog-buttons">
                <button class="dialog-btn dialog-btn-cancel" id="dialogCancelBtn">取消</button>
                <button class="dialog-btn dialog-btn-confirm" id="dialogConfirmBtn">确认</button>
            </div>
        </div>
    </div>';

    // 添加底部安全区域
    $html .= '<div class="bottom-safe-area"></div>';

    return $html;
}

function renderBottomNavCSS() {
    return '
    <link rel="stylesheet" href="../home/<USER>/new-nav.css">
    ';
}

function renderBottomNavJS($isLoggedIn = false) {
    $loginCheck = $isLoggedIn ? '' : 'window.location.href = "../login/index.php"; return;';

    return '
    <script>
        // 发布菜单功能
        function showPublishMenu() {
            ' . $loginCheck . '
            document.getElementById("publishMenu").classList.add("show");
        }

        function closePublishMenu() {
            document.getElementById("publishMenu").classList.remove("show");
        }

        function publishWork() {
            closePublishMenu();
            checkRealNameAndRedirect("work");
        }

        function publishActivity() {
            closePublishMenu();
            checkRealNameAndRedirect("activity");
        }

        // 检查实名认证并跳转
        function checkRealNameAndRedirect(type) {
            // 发送AJAX请求检查实名认证状态
            fetch("../api/check_verification.php")
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        if (data.is_verified) {
                            // 已实名认证，可以发布
                            if (type === "work") {
                                window.location.href = "../publish/work.php";
                            } else if (type === "activity") {
                                window.location.href = "../publish/activity.php";
                            }
                        } else {
                            // 未实名认证，跳转到认证页面
                            showVerificationDialog(type);
                        }
                    } else {
                        showToast("检查认证状态失败，请稍后重试");
                    }
                })
                .catch(error => {
                    console.error("Error:", error);
                    showToast("网络错误，请稍后重试");
                });
        }

        // 显示实名认证提示对话框
        function showVerificationDialog(type) {
            const actionText = type === "work" ? "发布作品" : "发布组局";
            const iconText = type === "work" ? "📸" : "👥";

            showCustomDialog({
                title: "需要实名认证",
                icon: iconText,
                message: `${actionText}需要完成实名认证才能继续`,
                description: "为了保障平台安全，发布内容需要先完成实名认证",
                confirmText: "前往认证",
                cancelText: "取消",
                onConfirm: function() {
                    // 智能路径检测
                    const currentPath = window.location.pathname;
                    const currentDir = currentPath.split("/").slice(-2, -1)[0]; // 获取当前目录名

                    let redirectPath = "";

                    // 根据当前所在目录确定正确的相对路径
                    if (currentDir === "home" || currentDir === "message" || currentDir === "guide") {
                        redirectPath = "../profile/verification.php";
                    } else if (currentDir === "profile") {
                        redirectPath = "verification.php";
                    } else {
                        // 默认使用绝对路径（从根目录开始）
                        redirectPath = "frontend/profile/verification.php";
                    }

                    console.log("Current path:", currentPath);
                    console.log("Current dir:", currentDir);
                    console.log("Redirect path:", redirectPath);

                    window.location.href = redirectPath + "?redirect=" + encodeURIComponent(type);
                }
            });
        }

        // Toast提示函数
        function showToast(message) {
            const toast = document.getElementById("toast") || createToast();
            toast.textContent = message;
            toast.style.display = "block";
            toast.style.animation = "fadeIn 0.3s forwards";

            setTimeout(() => {
                toast.style.animation = "fadeOut 0.3s forwards";
                setTimeout(() => {
                    toast.style.display = "none";
                }, 300);
            }, 3000);
        }

        function createToast() {
            const toast = document.createElement("div");
            toast.id = "toast";
            toast.className = "toast";
            document.body.appendChild(toast);
            return toast;
        }

        // 自定义弹窗函数
        function showCustomDialog(options) {
            const dialog = document.getElementById("customDialog");
            const icon = document.getElementById("dialogIcon");
            const title = document.getElementById("dialogTitle");
            const message = document.getElementById("dialogMessage");
            const description = document.getElementById("dialogDescription");
            const cancelBtn = document.getElementById("dialogCancelBtn");
            const confirmBtn = document.getElementById("dialogConfirmBtn");

            // 设置内容
            if (options.icon) icon.textContent = options.icon;
            if (options.title) title.textContent = options.title;
            if (options.message) message.textContent = options.message;
            if (options.description) {
                description.textContent = options.description;
                description.style.display = "block";
            } else {
                description.style.display = "none";
            }

            // 设置按钮文本
            if (options.cancelText) cancelBtn.textContent = options.cancelText;
            if (options.confirmText) confirmBtn.textContent = options.confirmText;

            // 清除之前的事件监听器
            const newCancelBtn = cancelBtn.cloneNode(true);
            const newConfirmBtn = confirmBtn.cloneNode(true);
            cancelBtn.parentNode.replaceChild(newCancelBtn, cancelBtn);
            confirmBtn.parentNode.replaceChild(newConfirmBtn, confirmBtn);

            // 添加事件监听器
            newCancelBtn.addEventListener("click", function() {
                closeCustomDialog();
                if (options.onCancel) options.onCancel();
            });

            newConfirmBtn.addEventListener("click", function() {
                closeCustomDialog();
                if (options.onConfirm) options.onConfirm();
            });

            // 显示弹窗
            dialog.classList.add("show");
        }

        function closeCustomDialog() {
            const dialog = document.getElementById("customDialog");
            dialog.classList.remove("show");
        }
    </script>';
}
?>
