/* 消息页面样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    font-family: 'PingFang SC', 'Helvetica Neue', Arial, sans-serif;
}

body {
    background-color: #f8f9fa;
    color: #333333;
    line-height: 1.6;
    min-height: 100vh;
    position: relative;
    padding-bottom: 60px;
}

/* Toast提示 */
.toast {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background-color: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 12px 20px;
    border-radius: 8px;
    font-size: 14px;
    z-index: 9999;
    display: none;
    max-width: 80%;
    text-align: center;
}

/* 顶部导航栏 */
.header-bar {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    height: 60px;
    background-color: white;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 20px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    z-index: 100;
}

.header-title {
    font-size: 18px;
    font-weight: bold;
    color: #333;
}

.header-actions {
    display: flex;
    align-items: center;
}

.add-button {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background-color: #40E0D0;
    border: none;
    color: white;
    font-size: 16px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
}

.add-button:hover {
    background-color: #36c7b8;
    transform: scale(1.05);
}

/* 标签栏 */
.tab-bar {
    position: fixed;
    top: 60px;
    left: 0;
    right: 0;
    height: 50px;
    background-color: white;
    display: flex;
    align-items: center;
    border-bottom: 1px solid #eee;
    z-index: 99;
}

.tab-item {
    flex: 1;
    text-align: center;
    padding: 15px 0;
    font-size: 16px;
    color: #666;
    cursor: pointer;
    position: relative;
    transition: color 0.3s ease;
}

.tab-item.active {
    color: #40E0D0;
    font-weight: bold;
}

.tab-item.active::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 30px;
    height: 3px;
    background-color: #40E0D0;
    border-radius: 2px;
}

/* 内容区域 */
.content-area {
    margin-top: 110px;
    padding: 0;
}

.tab-content {
    display: none;
}

.tab-content.active {
    display: block;
}

/* 聊天列表 */
.chat-list {
    background-color: white;
}

.chat-item {
    display: flex;
    align-items: center;
    padding: 15px 20px;
    border-bottom: 1px solid #f0f0f0;
    cursor: pointer;
    transition: background-color 0.2s ease;
}

.chat-item:hover {
    background-color: #f8f9fa;
}

.chat-item:last-child {
    border-bottom: none;
}

.chat-avatar {
    position: relative;
    margin-right: 15px;
}

.chat-avatar img {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    object-fit: cover;
}

.member-count {
    position: absolute;
    top: -5px;
    right: -5px;
    background-color: #ff4757;
    color: white;
    font-size: 10px;
    padding: 2px 6px;
    border-radius: 10px;
    min-width: 18px;
    text-align: center;
}

.chat-info {
    flex: 1;
    min-width: 0;
}

.chat-name {
    font-size: 16px;
    font-weight: bold;
    color: #333;
    margin-bottom: 5px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.chat-last-message {
    font-size: 14px;
    color: #666;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.sender-name {
    color: #40E0D0;
    font-weight: bold;
}

.chat-meta {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
}

.chat-time {
    font-size: 12px;
    color: #999;
}

/* 群聊列表 */
.group-list {
    background-color: white;
}

.group-item {
    display: flex;
    align-items: center;
    padding: 15px 20px;
    border-bottom: 1px solid #f0f0f0;
    cursor: pointer;
    transition: background-color 0.2s ease;
}

.group-item:hover {
    background-color: #f8f9fa;
}

.group-avatar img {
    width: 50px;
    height: 50px;
    border-radius: 8px;
    object-fit: cover;
    margin-right: 15px;
}

.group-info {
    flex: 1;
}

.group-name {
    font-size: 16px;
    font-weight: bold;
    color: #333;
    margin-bottom: 5px;
}

.group-members {
    font-size: 14px;
    color: #666;
}

/* 空状态 */
.empty-state {
    text-align: center;
    padding: 80px 20px;
    color: #999;
}

.empty-state i {
    font-size: 60px;
    margin-bottom: 20px;
    color: #ddd;
}

.empty-state p {
    font-size: 16px;
    margin-bottom: 10px;
}

/* 添加功能弹窗 */
.add-menu {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 1000;
    display: none;
}

.add-menu.show {
    display: block;
}

.add-menu-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
}

.add-menu-content {
    position: absolute;
    bottom: 60px;
    left: 0;
    right: 0;
    background-color: white;
    border-radius: 20px 20px 0 0;
    padding: 20px;
    transform: translateY(100%);
    transition: transform 0.3s ease;
}

.add-menu.show .add-menu-content {
    transform: translateY(0);
}

.add-menu-item {
    display: flex;
    align-items: center;
    padding: 15px 0;
    cursor: pointer;
    transition: background-color 0.2s ease;
    border-radius: 8px;
    margin-bottom: 5px;
}

.add-menu-item:hover {
    background-color: #f8f9fa;
}

.add-menu-item i {
    width: 40px;
    height: 40px;
    background-color: #40E0D0;
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 15px;
    font-size: 18px;
}

.add-menu-item span {
    font-size: 16px;
    color: #333;
}

/* 底部导航栏 */
.bottom-nav {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background-color: white;
    display: flex;
    justify-content: space-around;
    padding: 8px 0;
    box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.05);
    z-index: 100;
}

.nav-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 5px 0;
    width: 20%;
    text-decoration: none;
    transition: transform 0.2s ease;
}

.nav-item:hover {
    transform: scale(1.05);
}

.nav-item i {
    font-size: 24px;
    margin-bottom: 3px;
    color: #999;
    transition: color 0.3s ease;
}

.nav-item span {
    font-size: 12px;
    color: #999;
    transition: color 0.3s ease;
}

.nav-item.active i,
.nav-item.active span {
    color: #40E0D0;
}

/* 动画效果 */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translate(-50%, -50%) scale(0.8);
    }
    to {
        opacity: 1;
        transform: translate(-50%, -50%) scale(1);
    }
}

@keyframes fadeOut {
    from {
        opacity: 1;
        transform: translate(-50%, -50%) scale(1);
    }
    to {
        opacity: 0;
        transform: translate(-50%, -50%) scale(0.8);
    }
}
