-- 修复客服系统数据库字段长度问题
-- 解决 "Data too long for column 'type'" 错误

-- 1. 修复 realtime_notifications 表的字段长度
ALTER TABLE realtime_notifications 
MODIFY COLUMN type VARCHAR(100) NOT NULL COMMENT '通知类型';

ALTER TABLE realtime_notifications 
MODIFY COLUMN title VARCHAR(255) NOT NULL COMMENT '通知标题';

-- 如果 message 字段存在，也修复其长度
ALTER TABLE realtime_notifications 
MODIFY COLUMN message TEXT NULL COMMENT '通知消息内容';

ALTER TABLE realtime_notifications 
MODIFY COLUMN data TEXT NULL COMMENT '通知数据';

-- 2. 修复 customer_service_messages 表的字段长度
ALTER TABLE customer_service_messages 
MODIFY COLUMN sender_type VARCHAR(50) NULL DEFAULT 'user' COMMENT '发送者类型';

ALTER TABLE customer_service_messages 
MODIFY COLUMN sender_name VARCHAR(100) NULL DEFAULT '' COMMENT '发送者姓名';

ALTER TABLE customer_service_messages 
MODIFY COLUMN message_type VARCHAR(50) NULL DEFAULT 'text' COMMENT '消息类型';

ALTER TABLE customer_service_messages 
MODIFY COLUMN content TEXT NULL COMMENT '消息内容';

ALTER TABLE customer_service_messages 
MODIFY COLUMN attachment_url TEXT NULL COMMENT '附件URL';

ALTER TABLE customer_service_messages 
MODIFY COLUMN attachment_type VARCHAR(50) NULL COMMENT '附件类型';

-- 3. 修复 customer_service_sessions 表的字段长度
ALTER TABLE customer_service_sessions 
MODIFY COLUMN session_id VARCHAR(255) NOT NULL COMMENT '会话ID';

ALTER TABLE customer_service_sessions 
MODIFY COLUMN user_name VARCHAR(100) NULL DEFAULT '用户' COMMENT '用户名称';

ALTER TABLE customer_service_sessions 
MODIFY COLUMN priority VARCHAR(20) NULL DEFAULT 'normal' COMMENT '优先级';

ALTER TABLE customer_service_sessions 
MODIFY COLUMN category VARCHAR(50) NULL COMMENT '分类';

ALTER TABLE customer_service_sessions 
MODIFY COLUMN source VARCHAR(50) NULL DEFAULT 'web' COMMENT '来源';

ALTER TABLE customer_service_sessions 
MODIFY COLUMN status VARCHAR(20) NOT NULL DEFAULT 'waiting' COMMENT '状态';

-- 4. 检查修复结果
DESCRIBE realtime_notifications;
DESCRIBE customer_service_messages;
DESCRIBE customer_service_sessions;
