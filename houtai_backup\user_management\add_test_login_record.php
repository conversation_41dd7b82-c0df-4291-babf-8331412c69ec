<?php
/**
 * 添加测试登录记录
 */

require_once '../db_config.php';
require_once '../../frontend/login/login_logger.php';

$user_id = intval($_GET['user_id'] ?? 1);

echo "<h2>为用户 {$user_id} 添加测试登录记录</h2>";

try {
    $pdo = getDbConnection();
    
    // 确保login_logs表存在
    ensureLoginLogsTableExists($pdo);
    
    // 添加多条测试记录
    $test_records = [
        [
            'ip' => '*************',
            'ua' => 'Mozilla/5.0 (iPhone; CPU iPhone OS 16_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148 MicroMessenger/8.0.29',
            'location' => '北京市',
            'type' => 'normal_login'
        ],
        [
            'ip' => '**************',
            'ua' => 'Mozilla/5.0 (Linux; Android 13; SM-G998B) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/********* Mobile Safari/537.36 MicroMessenger/8.0.29',
            'location' => '广东省 深圳市',
            'type' => 'quick_login'
        ],
        [
            'ip' => '************',
            'ua' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'location' => '上海市',
            'type' => 'normal_login'
        ],
        [
            'ip' => '************',
            'ua' => 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'location' => '江苏省 南京市',
            'type' => 'secure_login'
        ],
        [
            'ip' => '*************',
            'ua' => 'Mozilla/5.0 (iPad; CPU OS 17_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.0 Mobile/15E148 Safari/604.1',
            'location' => '浙江省 杭州市',
            'type' => 'normal_login'
        ]
    ];
    
    $success_count = 0;
    
    foreach ($test_records as $record) {
        // 模拟不同的登录时间
        $login_time = date('Y-m-d H:i:s', time() - rand(0, 86400 * 7)); // 过去7天内的随机时间
        
        $stmt = $pdo->prepare("
            INSERT INTO login_logs (
                user_id, login_time, ip_address, user_agent, status, login_type, location, created_at
            ) VALUES (?, ?, ?, ?, 'success', ?, ?, NOW())
        ");
        
        $result = $stmt->execute([
            $user_id,
            $login_time,
            $record['ip'],
            $record['ua'],
            $record['type'],
            $record['location']
        ]);
        
        if ($result) {
            $success_count++;
            echo "✅ 添加记录成功: IP {$record['ip']}, 位置 {$record['location']}<br>";
        } else {
            echo "❌ 添加记录失败: IP {$record['ip']}<br>";
        }
    }
    
    echo "<br><strong>总共添加了 {$success_count} 条测试记录</strong><br>";
    
    // 验证添加的记录
    $stmt = $pdo->prepare("SELECT COUNT(*) as count FROM login_logs WHERE user_id = ?");
    $stmt->execute([$user_id]);
    $total = $stmt->fetch();
    
    echo "<br>用户 {$user_id} 现在总共有 {$total['count']} 条登录记录<br>";
    
    echo "<br><a href='debug_get_user_ips.php'>返回调试页面</a><br>";
    echo "<a href='get_user_ips.php?user_id={$user_id}' target='_blank'>测试API接口</a><br>";
    echo "<a href='detail.php?id={$user_id}' target='_blank'>查看用户详情页</a><br>";
    
} catch (Exception $e) {
    echo "❌ 添加测试记录失败: " . $e->getMessage() . "<br>";
}

?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; }
h2 { color: #333; }
a { color: #007cba; text-decoration: none; margin-right: 10px; }
a:hover { text-decoration: underline; }
</style>
