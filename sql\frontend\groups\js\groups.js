/**
 * 组局页面JavaScript
 * 处理活动管理相关功能
 */

class GroupsPage {
    constructor() {
        this.toast = document.getElementById('toast');
        this.init();
    }

    init() {
        this.bindEvents();
        this.disableBrowserAlerts();
    }

    bindEvents() {
        // 返回按钮事件
        const backBtn = document.querySelector('.back-button');
        if (backBtn) {
            backBtn.addEventListener('click', (e) => {
                e.preventDefault();
                this.goBack();
            });
        }
    }

    // 禁用浏览器原生弹窗
    disableBrowserAlerts() {
        window.alert = (message) => {
            this.showToast(message);
        };

        window.confirm = (message) => {
            this.showToast(message);
            return true;
        };
    }

    // 编辑活动
    editActivity(activityId) {
        this.showToast('编辑功能开发中...');
        // TODO: 跳转到编辑页面
        // window.location.href = `../camping/edit-activity.php?id=${activityId}`;
    }

    // 发布活动（从草稿状态）
    publishActivity(activityId) {
        if (confirm('确定要发布这个活动吗？发布后将开始招募参与者。')) {
            this.showLoading('正在发布活动...');
            
            fetch('api/publish_activity.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    activity_id: activityId
                })
            })
            .then(response => response.json())
            .then(data => {
                this.hideLoading();
                if (data.success) {
                    this.showToast('活动发布成功！');
                    setTimeout(() => {
                        location.reload();
                    }, 1500);
                } else {
                    this.showToast(data.message || '发布失败，请重试');
                }
            })
            .catch(error => {
                this.hideLoading();
                console.error('Error:', error);
                this.showToast('网络错误，请重试');
            });
        }
    }

    // 查看活动详情
    viewActivity(activityId) {
        this.showToast('活动详情页面开发中...');
        // TODO: 跳转到活动详情页面
        // window.location.href = `../camping/activity-detail.php?id=${activityId}`;
    }

    // 管理活动
    manageActivity(activityId) {
        this.showToast('活动管理功能开发中...');
        // TODO: 跳转到活动管理页面
        // window.location.href = `../camping/manage-activity.php?id=${activityId}`;
    }

    // 返回上一页
    goBack() {
        history.back();
    }

    // 显示加载动画
    showLoading(text = '正在处理...') {
        // 创建加载遮罩
        if (!document.getElementById('loading-overlay')) {
            const loadingOverlay = document.createElement('div');
            loadingOverlay.id = 'loading-overlay';
            loadingOverlay.innerHTML = `
                <div class="four-star-loader">
                    <div class="star-point"></div>
                    <div class="star-point"></div>
                    <div class="star-point"></div>
                    <div class="star-point"></div>
                </div>
                <div class="loading-text">${text}</div>
            `;
            loadingOverlay.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                background: rgba(255, 255, 255, 0.9);
                backdrop-filter: blur(8px);
                display: flex;
                align-items: center;
                justify-content: center;
                flex-direction: column;
                z-index: 9999;
            `;
            document.body.appendChild(loadingOverlay);
        } else {
            document.getElementById('loading-overlay').style.display = 'flex';
            document.querySelector('.loading-text').textContent = text;
        }
    }

    // 隐藏加载动画
    hideLoading() {
        const loadingOverlay = document.getElementById('loading-overlay');
        if (loadingOverlay) {
            loadingOverlay.style.display = 'none';
        }
    }

    // 显示Toast提示
    showToast(message, duration = 3000) {
        if (!this.toast) return;

        this.toast.textContent = message;
        this.toast.style.display = 'block';
        this.toast.style.opacity = '0';
        this.toast.style.transform = 'translateX(-50%) translateY(20px)';

        setTimeout(() => {
            this.toast.style.transition = 'all 0.3s ease';
            this.toast.style.opacity = '1';
            this.toast.style.transform = 'translateX(-50%) translateY(0)';
        }, 10);

        setTimeout(() => {
            this.toast.style.opacity = '0';
            this.toast.style.transform = 'translateX(-50%) translateY(20px)';
            
            setTimeout(() => {
                this.toast.style.display = 'none';
            }, 300);
        }, duration);
    }
}

// 全局函数供HTML调用
function editActivity(activityId) {
    if (window.groupsPage) {
        window.groupsPage.editActivity(activityId);
    }
}

function publishActivity(activityId) {
    if (window.groupsPage) {
        window.groupsPage.publishActivity(activityId);
    }
}

function viewActivity(activityId) {
    if (window.groupsPage) {
        window.groupsPage.viewActivity(activityId);
    }
}

function manageActivity(activityId) {
    if (window.groupsPage) {
        window.groupsPage.manageActivity(activityId);
    }
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    window.groupsPage = new GroupsPage();
});

// 添加四角星加载动画样式
const style = document.createElement('style');
style.textContent = `
    .four-star-loader {
        position: relative;
        width: 60px;
        height: 60px;
        margin-bottom: 20px;
    }

    .star-point {
        position: absolute;
        width: 12px;
        height: 12px;
        background: #6F7BF5;
        border-radius: 2px;
        animation: starRotate 1.5s ease-in-out infinite;
    }

    .star-point:nth-child(1) {
        top: 0;
        left: 50%;
        transform: translateX(-50%);
        animation-delay: 0s;
        background: #6F7BF5;
    }

    .star-point:nth-child(2) {
        top: 50%;
        right: 0;
        transform: translateY(-50%);
        animation-delay: 0.375s;
        background: #FF6B9D;
    }

    .star-point:nth-child(3) {
        bottom: 0;
        left: 50%;
        transform: translateX(-50%);
        animation-delay: 0.75s;
        background: #FFD166;
    }

    .star-point:nth-child(4) {
        top: 50%;
        left: 0;
        transform: translateY(-50%);
        animation-delay: 1.125s;
        background: #6F7BF5;
    }

    @keyframes starRotate {
        0%, 100% {
            transform: scale(1) rotate(0deg);
            opacity: 1;
        }
        25% {
            transform: scale(1.2) rotate(90deg);
            opacity: 0.8;
        }
        50% {
            transform: scale(0.8) rotate(180deg);
            opacity: 0.6;
        }
        75% {
            transform: scale(1.1) rotate(270deg);
            opacity: 0.8;
        }
    }

    .loading-text {
        color: #666;
        font-size: 0.875rem;
        font-weight: 500;
    }
`;
document.head.appendChild(style);
