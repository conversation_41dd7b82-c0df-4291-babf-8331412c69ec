document.addEventListener('DOMContentLoaded', function() {
    // 初始化富文本编辑器
    const quill = new Quill('#editor', {
        theme: 'snow',
        placeholder: '请输入内容...',
        modules: {
            toolbar: [
                [{ 'header': [1, 2, 3, 4, 5, 6, false] }],
                ['bold', 'italic', 'underline', 'strike'],
                [{ 'color': [] }, { 'background': [] }],
                [{ 'align': [] }],
                ['blockquote', 'code-block'],
                [{ 'list': 'ordered' }, { 'list': 'bullet' }],
                ['link'],
                ['clean']
            ]
        }
    });

    // 初始化表情选择器
    const emojiButton = document.getElementById('emoji-button');
    const emojiPickerContainer = document.getElementById('emoji-picker-container');
    let emojiPicker = null;

    emojiButton.addEventListener('click', function() {
        if (emojiPickerContainer.style.display === 'block') {
            emojiPickerContainer.style.display = 'none';
            return;
        }

        if (!emojiPicker) {
            emojiPicker = document.createElement('emoji-picker');
            emojiPickerContainer.appendChild(emojiPicker);

            emojiPicker.addEventListener('emoji-click', event => {
                const emoji = event.detail.unicode;
                const range = quill.getSelection(true);
                quill.insertText(range.index, emoji);
                quill.setSelection(range.index + emoji.length);
                emojiPickerContainer.style.display = 'none';
            });
        }

        emojiPickerContainer.style.display = 'block';
    });

    // 点击其他地方关闭表情选择器
    document.addEventListener('click', function(event) {
        if (!emojiPickerContainer.contains(event.target) && event.target !== emojiButton) {
            emojiPickerContainer.style.display = 'none';
        }
    });

    // 处理分类选择
    const categorySelect = document.getElementById('category');
    const subcategorySelect = document.getElementById('subcategory');

    // 分类数据（从PHP获取）
    const subcategoriesData = {};

    // 检查是否有错误消息
    const errorMessage = document.querySelector('.error-message');
    if (errorMessage) {
        // 如果有错误消息，禁用发布按钮
        document.getElementById('publish-button').disabled = true;
        document.getElementById('publish-button').classList.add('disabled');
    }

    // 监听分类变化
    categorySelect.addEventListener('change', function() {
        const categoryId = this.value;

        // 清空子分类
        subcategorySelect.innerHTML = '';

        if (!categoryId) {
            subcategorySelect.disabled = true;
            subcategorySelect.innerHTML = '<option value="">请先选择主分类</option>';
            return;
        }

        // 启用子分类选择器
        subcategorySelect.disabled = false;

        // 添加默认选项
        const defaultOption = document.createElement('option');
        defaultOption.value = '';
        defaultOption.textContent = '请选择子分类';
        subcategorySelect.appendChild(defaultOption);

        // 显示加载中
        showLoading();

        // 获取该分类下的子分类
        fetch(`../../api/universe/get_subcategories.php?category_id=${categoryId}`)
            .then(response => {
                if (!response.ok) {
                    throw new Error('网络响应不正常');
                }
                return response.json();
            })
            .then(data => {
                hideLoading();

                if (data.success && Array.isArray(data.subcategories)) {
                    // 保存子分类数据
                    subcategoriesData[categoryId] = data.subcategories;

                    // 添加子分类选项
                    data.subcategories.forEach(subcategory => {
                        const option = document.createElement('option');
                        option.value = subcategory.id;
                        option.textContent = subcategory.name;
                        subcategorySelect.appendChild(option);
                    });

                    // 如果没有子分类，显示提示
                    if (data.subcategories.length === 0) {
                        const noDataOption = document.createElement('option');
                        noDataOption.value = '';
                        noDataOption.textContent = '该分类下暂无子分类';
                        subcategorySelect.appendChild(noDataOption);
                    }
                } else {
                    showToast('获取子分类失败');

                    // 添加一个提示选项
                    const errorOption = document.createElement('option');
                    errorOption.value = '';
                    errorOption.textContent = '获取子分类失败，请重试';
                    subcategorySelect.appendChild(errorOption);
                }
            })
            .catch(error => {
                hideLoading();
                console.error('Error:', error);
                showToast('网络错误，请稍后再试');

                // 添加一个提示选项
                const errorOption = document.createElement('option');
                errorOption.value = '';
                errorOption.textContent = '网络错误，请重试';
                subcategorySelect.appendChild(errorOption);
            });
    });

    // 处理图片上传
    const imageUploadButton = document.getElementById('image-upload-button');
    const imageUploadInput = document.getElementById('image-upload');
    const mediaPreview = document.getElementById('media-preview');

    imageUploadButton.addEventListener('click', function() {
        imageUploadInput.click();
    });

    imageUploadInput.addEventListener('change', function() {
        if (this.files.length === 0) return;

        // 显示加载中
        showLoading();

        const formData = new FormData();

        // 添加所有选择的图片
        for (let i = 0; i < this.files.length; i++) {
            formData.append('images[]', this.files[i]);
        }

        // 上传图片
        fetch('../../api/universe/upload_media.php', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            hideLoading();

            if (data.success) {
                // 处理上传成功的图片
                data.files.forEach(file => {
                    // 在编辑器中插入图片
                    const range = quill.getSelection(true);
                    quill.insertEmbed(range.index, 'image', file.url);
                    quill.setSelection(range.index + 1);

                    // 添加到预览区域
                    addMediaPreview('image', file.url, file.id);
                });

                // 清空文件输入
                imageUploadInput.value = '';
            } else {
                showToast(data.message || '上传失败，请重试');
            }
        })
        .catch(error => {
            hideLoading();
            console.error('Error:', error);
            showToast('网络错误，请稍后再试');
        });
    });

    // 处理视频上传
    const videoUploadButton = document.getElementById('video-upload-button');
    const videoUploadInput = document.getElementById('video-upload');

    videoUploadButton.addEventListener('click', function() {
        videoUploadInput.click();
    });

    videoUploadInput.addEventListener('change', function() {
        if (this.files.length === 0) return;

        // 显示加载中
        showLoading();

        // 检查文件类型和大小
        const file = this.files[0];
        const fileType = file.type;
        const fileSize = file.size / 1024 / 1024; // 转换为MB

        console.log('上传视频:', file.name, '类型:', fileType, '大小:', fileSize.toFixed(2) + 'MB');

        // 验证文件类型
        const allowedTypes = ['video/mp4', 'video/webm', 'video/ogg'];
        if (!allowedTypes.includes(fileType)) {
            hideLoading();
            showToast(`不支持的视频格式: ${fileType}，请使用MP4、WebM或OGG格式`);
            videoUploadInput.value = '';
            return;
        }

        // 验证文件大小
        if (fileSize > 50) { // 限制50MB
            hideLoading();
            showToast(`视频文件过大: ${fileSize.toFixed(2)}MB，请上传小于50MB的视频`);
            videoUploadInput.value = '';
            return;
        }

        const formData = new FormData();
        formData.append('video', file);

        // 上传视频
        fetch('../../api/universe/upload_media.php', {
            method: 'POST',
            body: formData,
            credentials: 'same-origin' // 确保发送cookies
        })
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP错误: ${response.status}`);
            }
            return response.json();
        })
        .then(data => {
            hideLoading();
            console.log('上传响应:', data);

            if (data.success) {
                // 处理上传成功的视频
                const file = data.files[0];

                // 在编辑器中插入视频
                const range = quill.getSelection(true);
                quill.insertText(range.index, `[视频]`);
                quill.setSelection(range.index + 4);

                // 添加到预览区域
                addMediaPreview('video', file.url, file.id);

                // 清空文件输入
                videoUploadInput.value = '';

                showToast('视频上传成功');
            } else {
                // 显示详细错误信息
                let errorMsg = data.message || '上传失败，请重试';
                if (data.debug) {
                    console.error('上传错误详情:', data.debug);
                    errorMsg += ' (查看控制台获取详细信息)';
                }
                showToast(errorMsg);
                videoUploadInput.value = '';
            }
        })
        .catch(error => {
            hideLoading();
            console.error('上传错误:', error);
            showToast('网络错误，请稍后再试: ' + error.message);
            videoUploadInput.value = '';
        });
    });

    // 处理音频上传
    const audioUploadButton = document.getElementById('audio-upload-button');
    const audioUploadInput = document.getElementById('audio-upload');

    audioUploadButton.addEventListener('click', function() {
        audioUploadInput.click();
    });

    audioUploadInput.addEventListener('change', function() {
        if (this.files.length === 0) return;

        // 显示加载中
        showLoading();

        const formData = new FormData();
        formData.append('audio', this.files[0]);

        // 上传音频
        fetch('../../api/universe/upload_media.php', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            hideLoading();

            if (data.success) {
                // 处理上传成功的音频
                const file = data.files[0];

                // 在编辑器中插入音频
                const range = quill.getSelection(true);
                quill.insertText(range.index, `[音频]`);
                quill.setSelection(range.index + 4);

                // 添加到预览区域
                addMediaPreview('audio', file.url, file.id, this.files[0].name);

                // 清空文件输入
                audioUploadInput.value = '';
            } else {
                showToast(data.message || '上传失败，请重试');
            }
        })
        .catch(error => {
            hideLoading();
            console.error('Error:', error);
            showToast('网络错误，请稍后再试');
        });
    });

    // 处理封面图片上传
    const coverPreview = document.getElementById('cover-preview');
    const coverUpload = document.getElementById('cover-upload');
    const coverImagePreview = document.getElementById('cover-image-preview');
    const coverImageUrl = document.getElementById('cover_image_url');

    coverPreview.addEventListener('click', function() {
        coverUpload.click();
    });

    coverUpload.addEventListener('change', function() {
        if (this.files.length === 0) return;

        // 显示加载中
        showLoading();

        const formData = new FormData();
        formData.append('cover', this.files[0]);

        // 上传封面图片
        fetch('../../api/universe/upload_media.php', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            hideLoading();

            if (data.success) {
                // 处理上传成功的封面图片
                const file = data.files[0];

                // 显示预览
                coverImagePreview.src = file.url;
                coverImagePreview.style.display = 'block';
                document.querySelector('.upload-placeholder').style.display = 'none';

                // 保存URL
                coverImageUrl.value = file.url;
            } else {
                showToast(data.message || '上传失败，请重试');
            }
        })
        .catch(error => {
            hideLoading();
            console.error('Error:', error);
            showToast('网络错误，请稍后再试');
        });
    });

    // 处理发布按钮点击
    const publishButton = document.getElementById('publish-button');
    const publishForm = document.getElementById('publish-form');
    const contentInput = document.getElementById('content');

    publishButton.addEventListener('click', function() {
        // 获取富文本内容
        const content = quill.root.innerHTML;
        contentInput.value = content;

        // 验证表单
        const title = document.getElementById('title').value.trim();
        const categoryId = categorySelect.value;
        const subcategoryId = subcategorySelect.value;

        if (!title) {
            showToast('请输入标题');
            return;
        }

        if (!categoryId) {
            showToast('请选择主分类');
            return;
        }

        if (!subcategoryId) {
            showToast('请选择子分类');
            return;
        }

        if (!content || content === '<p><br></p>') {
            showToast('请输入内容');
            return;
        }

        // 显示加载中
        showLoading();

        // 收集表单数据
        const formData = new FormData(publishForm);

        // 发布内容
        fetch('../../api/universe/publish.php', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            hideLoading();

            if (data.success) {
                showToast('发布成功');

                // 延迟跳转
                setTimeout(() => {
                    window.location.href = '../index.php';
                }, 1500);
            } else {
                showToast(data.message || '发布失败，请重试');
            }
        })
        .catch(error => {
            hideLoading();
            console.error('Error:', error);
            showToast('网络错误，请稍后再试');
        });
    });

    // 辅助函数：添加媒体预览
    function addMediaPreview(type, url, id, name) {
        const mediaItem = document.createElement('div');
        mediaItem.className = 'media-item';
        mediaItem.dataset.id = id;

        let mediaContent = '';

        if (type === 'image') {
            mediaContent = `<img src="${url}" alt="图片">`;
        } else if (type === 'video') {
            mediaContent = `<video src="${url}" controls></video>`;
        } else if (type === 'audio') {
            mediaContent = `
                <div class="audio-container">
                    <div class="audio-icon"><i class="fas fa-music"></i></div>
                    <div class="audio-name">${name || '音频文件'}</div>
                    <audio src="${url}" controls></audio>
                </div>
            `;
        }

        mediaItem.innerHTML = `
            ${mediaContent}
            <div class="remove-media" data-id="${id}"><i class="fas fa-times"></i></div>
        `;

        mediaPreview.appendChild(mediaItem);

        // 添加删除事件
        const removeButton = mediaItem.querySelector('.remove-media');
        removeButton.addEventListener('click', function(e) {
            e.stopPropagation();
            const mediaId = this.dataset.id;

            // 从预览中移除
            mediaItem.remove();

            // 可以选择是否从服务器删除
            // 这里暂时不实现删除逻辑，因为内容可能已经插入编辑器
        });
    }

    // 辅助函数：显示Toast提示
    function showToast(message) {
        const toast = document.getElementById('toast');
        toast.textContent = message;
        toast.style.display = 'block';

        // 添加动画
        toast.style.animation = 'fadeIn 0.3s forwards';

        // 3秒后隐藏
        setTimeout(() => {
            toast.style.animation = 'fadeOut 0.3s forwards';
            setTimeout(() => {
                toast.style.display = 'none';
            }, 300);
        }, 3000);
    }

    // 辅助函数：显示加载中
    function showLoading() {
        let loadingOverlay = document.querySelector('.loading-overlay');

        if (!loadingOverlay) {
            loadingOverlay = document.createElement('div');
            loadingOverlay.className = 'loading-overlay';
            loadingOverlay.innerHTML = '<div class="loading-spinner"></div>';
            document.body.appendChild(loadingOverlay);
        }

        loadingOverlay.style.display = 'flex';
    }

    // 辅助函数：隐藏加载中
    function hideLoading() {
        const loadingOverlay = document.querySelector('.loading-overlay');

        if (loadingOverlay) {
            loadingOverlay.style.display = 'none';
        }
    }
});
