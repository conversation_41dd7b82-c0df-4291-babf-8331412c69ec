<?php
/**
 * 设备信息解析测试API
 */

require_once 'login_logger.php';

header('Content-Type: application/json');

$input = json_decode(file_get_contents('php://input'), true);

if (!isset($input['user_agents']) || !is_array($input['user_agents'])) {
    echo json_encode(['error' => 'User Agent数组不能为空']);
    exit;
}

$results = [];

foreach ($input['user_agents'] as $userAgent) {
    $deviceInfo = parseUserAgent($userAgent);
    $results[] = $deviceInfo;
}

echo json_encode(['results' => $results]);
?>
