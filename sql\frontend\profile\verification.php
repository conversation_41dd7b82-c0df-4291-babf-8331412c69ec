<?php
// 引入session配置
require_once '../../sql/session_config.php';
initLoginSession();

// 检查用户是否已登录
if (!isUserLoggedIn()) {
    header('Location: ../login/index.php');
    exit;
}

// 数据库连接
require_once '../../sql/db_config.php';

$user_id = $_SESSION['user_id'];
$redirect_type = $_GET['redirect'] ?? '';

// 获取数据库连接
$pdo = getDbConnection();

// 查询用户当前的实名认证状态
try {
    // 先查询用户基本信息
    $stmt = $pdo->prepare("SELECT username FROM users WHERE id = ?");
    $stmt->execute([$user_id]);
    $user_info = $stmt->fetch(PDO::FETCH_ASSOC);

    if (!$user_info) {
        header('Location: ../login/index.php');
        exit;
    }

    // 尝试查询实名认证信息
    $verification_status = 'none';
    $is_verified = false;

    try {
        $stmt = $pdo->prepare("
            SELECT
                verification_status,
                real_name,
                id_card_number,
                verification_reason,
                submitted_at,
                verified_at
            FROM realname_verification
            WHERE user_id = ?
        ");
        $stmt->execute([$user_id]);
        $verification_info = $stmt->fetch(PDO::FETCH_ASSOC);

        if ($verification_info) {
            $verification_status = $verification_info['verification_status'];
            $is_verified = ($verification_status === 'approved');
            $user_info = array_merge($user_info, $verification_info);
        }
    } catch (PDOException $e) {
        // 实名认证表可能不存在，使用默认值
        error_log("Realname verification table not found: " . $e->getMessage());
    }

} catch (PDOException $e) {
    error_log("Database error: " . $e->getMessage());
    $verification_status = 'none';
    $is_verified = false;
    $user_info = ['username' => ''];
}

// 处理表单提交
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['submit_verification'])) {
    // 检查是否已经提交过认证（防止重复提交）
    if ($verification_status === 'pending') {
        $error_message = '您已提交实名认证申请，请等待审核结果';
    } elseif ($verification_status === 'approved') {
        $error_message = '您已通过实名认证，无需重复提交';
    } else {
        $real_name = trim($_POST['real_name'] ?? '');
        $id_card_number = trim($_POST['id_card_number'] ?? '');
        $id_card_front_url = trim($_POST['id_card_front_url'] ?? '');
        $id_card_back_url = trim($_POST['id_card_back_url'] ?? '');

        // 基本验证
        if (empty($real_name) || empty($id_card_number)) {
            $error_message = '请填写完整的姓名和身份证号码';
        } elseif (empty($id_card_front_url) || empty($id_card_back_url)) {
            $error_message = '请上传身份证正反面照片';
        } elseif (!preg_match('/^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/', $id_card_number)) {
            $error_message = '身份证号码格式不正确';
        } else {
        try {
            // 确保实名认证表存在
            $createTableSQL = "
                CREATE TABLE IF NOT EXISTS realname_verification (
                    id int(11) NOT NULL AUTO_INCREMENT,
                    user_id int(11) NOT NULL,
                    real_name varchar(50) NOT NULL,
                    id_card_number varchar(18) NOT NULL,
                    id_card_front_url varchar(500) DEFAULT NULL COMMENT '身份证正面照片URL',
                    id_card_back_url varchar(500) DEFAULT NULL COMMENT '身份证反面照片URL',
                    face_photo_url varchar(500) DEFAULT NULL COMMENT '手持身份证照片URL',
                    verification_status enum('pending','approved','rejected') DEFAULT 'pending',
                    verification_reason text DEFAULT NULL,
                    submitted_at timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
                    verified_at timestamp NULL DEFAULT NULL,
                    verified_by int(11) DEFAULT NULL COMMENT '审核员ID',
                    created_at timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
                    updated_at timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                    PRIMARY KEY (id),
                    UNIQUE KEY user_id (user_id),
                    UNIQUE KEY id_card_number (id_card_number)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci
            ";
            $pdo->exec($createTableSQL);

            // 确保现有表有所需字段
            $alterTableSQLs = [
                "ALTER TABLE realname_verification ADD COLUMN IF NOT EXISTS id_card_front_url varchar(500) DEFAULT NULL COMMENT '身份证正面照片URL' AFTER id_card_number",
                "ALTER TABLE realname_verification ADD COLUMN IF NOT EXISTS id_card_back_url varchar(500) DEFAULT NULL COMMENT '身份证反面照片URL' AFTER id_card_front_url",
                "ALTER TABLE realname_verification ADD COLUMN IF NOT EXISTS face_photo_url varchar(500) DEFAULT NULL COMMENT '手持身份证照片URL' AFTER id_card_back_url",
                "ALTER TABLE realname_verification ADD COLUMN IF NOT EXISTS verified_by int(11) DEFAULT NULL COMMENT '审核员ID' AFTER verified_at"
            ];

            foreach ($alterTableSQLs as $alterSQL) {
                try {
                    $pdo->exec($alterSQL);
                } catch (PDOException $e) {
                    // 字段可能已存在，忽略错误
                    error_log("Alter table warning: " . $e->getMessage());
                }
            }

            // 检查身份证号码是否已被使用
            $stmt = $pdo->prepare("SELECT id FROM realname_verification WHERE id_card_number = ? AND user_id != ?");
            $stmt->execute([$id_card_number, $user_id]);

            if ($stmt->fetch()) {
                $error_message = '该身份证号码已被其他用户使用';
            } else {
                // 插入或更新实名认证信息
                $stmt = $pdo->prepare("
                    INSERT INTO realname_verification (user_id, real_name, id_card_number, id_card_front_url, id_card_back_url, verification_status, submitted_at)
                    VALUES (?, ?, ?, ?, ?, 'pending', NOW())
                    ON DUPLICATE KEY UPDATE
                    real_name = VALUES(real_name),
                    id_card_number = VALUES(id_card_number),
                    id_card_front_url = VALUES(id_card_front_url),
                    id_card_back_url = VALUES(id_card_back_url),
                    verification_status = 'pending',
                    submitted_at = NOW(),
                    verification_reason = NULL
                ");

                if ($stmt->execute([$user_id, $real_name, $id_card_number, $id_card_front_url, $id_card_back_url])) {
                    $success_message = '实名认证信息已提交，请等待审核（通常1-3个工作日）';
                    $verification_status = 'pending';
                    // 重定向到同一页面，防止刷新重复提交
                    header('Location: ' . $_SERVER['PHP_SELF'] . ($redirect_type ? '?redirect=' . $redirect_type : ''));
                    exit;
                } else {
                    $error_message = '提交失败，请稍后重试';
                }
            }
        } catch (PDOException $e) {
            error_log("Database error: " . $e->getMessage());
            $error_message = '系统错误，请稍后重试';
        }
        }
    }
}
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <meta name="theme-color" content="#40E0D0">
    <title>实名认证 - 趣玩星球</title>
    <link rel="stylesheet" href="css/verification.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <!-- 顶部导航栏 -->
    <div class="header-bar">
        <a href="index.php" class="back-button">
            <i class="fas fa-arrow-left"></i>
        </a>
        <div class="header-title">实名认证</div>
    </div>

    <div class="container">
        <?php if ($is_verified): ?>
            <!-- 已认证状态 -->
            <div class="verification-status verified">
                <div class="status-icon">
                    <i class="fas fa-check-circle"></i>
                </div>
                <h2>认证已通过</h2>
                <p>您已完成实名认证，可以正常发布内容</p>
                <div class="user-info">
                    <div class="info-item">
                        <span class="label">姓名：</span>
                        <span class="value"><?php echo htmlspecialchars($user_info['real_name'] ?? ''); ?></span>
                    </div>
                    <div class="info-item">
                        <span class="label">身份证：</span>
                        <span class="value"><?php
                            $id_card = $user_info['id_card_number'] ?? '';
                            if (strlen($id_card) >= 10) {
                                echo htmlspecialchars(substr($id_card, 0, 6) . '********' . substr($id_card, -4));
                            } else {
                                echo '****';
                            }
                        ?></span>
                    </div>
                    <div class="info-item">
                        <span class="label">认证时间：</span>
                        <span class="value"><?php
                            $verified_time = $user_info['verified_at'] ?? '';
                            if ($verified_time && $verified_time != '0000-00-00 00:00:00') {
                                echo date('Y-m-d H:i', strtotime($verified_time));
                            } else {
                                echo '待审核';
                            }
                        ?></span>
                    </div>
                </div>

                <!-- 审核信息 -->
                <div class="audit-info">
                    <h4><i class="fas fa-clipboard-check"></i> 审核信息</h4>
                    <div class="audit-status">
                        <span class="status-badge status-approved">
                            <i class="fas fa-check-circle"></i>
                            审核通过
                        </span>
                    </div>
                    <p class="audit-desc">恭喜！您的实名认证已通过审核，现在可以正常使用所有功能。</p>
                    <?php if (!empty($user_info['verified_at'])): ?>
                        <div class="audit-time">
                            <i class="fas fa-calendar-check"></i>
                            审核完成时间：<?php echo date('Y年m月d日 H:i', strtotime($user_info['verified_at'])); ?>
                        </div>
                    <?php endif; ?>
                </div>

                <?php if ($redirect_type): ?>
                    <div class="action-buttons">
                        <a href="<?php echo $redirect_type === 'work' ? '../publish/work.php' : '../publish/activity.php'; ?>" class="btn-primary">
                            继续<?php echo $redirect_type === 'work' ? '发布作品' : '发布组局'; ?>
                        </a>
                    </div>
                <?php endif; ?>
            </div>

        <?php elseif ($verification_status === 'pending'): ?>
            <!-- 审核中状态 -->
            <div class="verification-status pending">
                <div class="status-icon">
                    <i class="fas fa-clock"></i>
                </div>
                <h2>审核中</h2>
                <p>您的实名认证信息已提交，请耐心等待审核</p>
                <div class="user-info">
                    <div class="info-item">
                        <span class="label">姓名：</span>
                        <span class="value"><?php echo htmlspecialchars($user_info['real_name'] ?? ''); ?></span>
                    </div>
                    <div class="info-item">
                        <span class="label">身份证：</span>
                        <span class="value"><?php
                            $id_card = $user_info['id_card_number'] ?? '';
                            if (strlen($id_card) >= 10) {
                                echo htmlspecialchars(substr($id_card, 0, 6) . '********' . substr($id_card, -4));
                            } else {
                                echo '****';
                            }
                        ?></span>
                    </div>
                    <div class="info-item">
                        <span class="label">提交时间：</span>
                        <span class="value"><?php
                            $submitted_time = $user_info['submitted_at'] ?? '';
                            if ($submitted_time && $submitted_time != '0000-00-00 00:00:00') {
                                echo date('Y-m-d H:i', strtotime($submitted_time));
                            } else {
                                echo '未知时间';
                            }
                        ?></span>
                    </div>
                </div>
                <!-- 审核信息 -->
                <div class="audit-info">
                    <h4><i class="fas fa-clipboard-list"></i> 审核信息</h4>
                    <div class="audit-status">
                        <span class="status-badge status-pending">
                            <i class="fas fa-hourglass-half"></i>
                            正在审核中
                        </span>
                    </div>
                    <p class="audit-desc">您的实名认证申请已提交，我们的工作人员正在仔细审核您的信息，请耐心等待。</p>
                </div>

                <div class="tips">
                    <p><i class="fas fa-info-circle"></i> 审核通常需要1-3个工作日，请耐心等待</p>
                </div>
            </div>

        <?php elseif ($verification_status === 'rejected'): ?>
            <!-- 审核被拒状态 -->
            <div class="verification-status rejected">
                <div class="status-icon">
                    <i class="fas fa-times-circle"></i>
                </div>
                <h2>审核未通过</h2>
                <p>您的实名认证未通过审核，请根据以下原因重新提交</p>

                <!-- 审核信息 -->
                <div class="audit-info">
                    <h4><i class="fas fa-clipboard-list"></i> 审核信息</h4>
                    <div class="audit-status">
                        <span class="status-badge status-rejected">
                            <i class="fas fa-times-circle"></i>
                            审核未通过
                        </span>
                    </div>
                    <?php if (!empty($user_info['verification_reason'])): ?>
                        <div class="rejection-reason">
                            <h5><i class="fas fa-exclamation-triangle"></i> 拒绝原因：</h5>
                            <p class="reason-text"><?php echo htmlspecialchars($user_info['verification_reason']); ?></p>
                        </div>
                    <?php endif; ?>
                    <p class="audit-desc">请根据上述原因修正您的信息后重新提交认证申请。</p>
                </div>
            </div>

            <!-- 重新提交表单 -->
            <div class="verification-form">
                <h3>重新提交认证</h3>
                <?php if (isset($error_message)): ?>
                    <div class="error-message">
                        <i class="fas fa-exclamation-triangle"></i>
                        <?php echo htmlspecialchars($error_message); ?>
                    </div>
                <?php endif; ?>

                <form method="POST" action="">
                    <div class="form-group">
                        <label for="real_name">真实姓名</label>
                        <input type="text" id="real_name" name="real_name" required
                               value="<?php echo htmlspecialchars($user_info['real_name'] ?? ''); ?>"
                               placeholder="请输入身份证上的真实姓名">
                    </div>

                    <div class="form-group">
                        <label for="id_card_number">身份证号码</label>
                        <input type="text" id="id_card_number" name="id_card_number" required
                               value="<?php echo htmlspecialchars($user_info['id_card_number'] ?? ''); ?>"
                               placeholder="请输入18位身份证号码" maxlength="18">
                    </div>

                    <div class="form-group">
                        <label for="id_card_front">身份证正面照片</label>
                        <div class="upload-container">
                            <div class="upload-area" id="frontUploadArea">
                                <i class="fas fa-cloud-upload-alt"></i>
                                <p>点击上传身份证正面照片</p>
                                <small>支持 JPG、PNG 格式，文件大小不超过 5MB</small>
                            </div>
                            <input type="file" id="id_card_front" accept="image/*" style="display: none;">
                            <input type="hidden" name="id_card_front_url" id="id_card_front_url" required>
                            <div class="upload-preview" id="frontPreview" style="display: none;">
                                <img id="frontPreviewImg" src="" alt="身份证正面预览">
                                <button type="button" class="remove-btn" onclick="removeImage('front')">
                                    <i class="fas fa-times"></i>
                                </button>
                            </div>
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="id_card_back">身份证反面照片</label>
                        <div class="upload-container">
                            <div class="upload-area" id="backUploadArea">
                                <i class="fas fa-cloud-upload-alt"></i>
                                <p>点击上传身份证反面照片</p>
                                <small>支持 JPG、PNG 格式，文件大小不超过 5MB</small>
                            </div>
                            <input type="file" id="id_card_back" accept="image/*" style="display: none;">
                            <input type="hidden" name="id_card_back_url" id="id_card_back_url" required>
                            <div class="upload-preview" id="backPreview" style="display: none;">
                                <img id="backPreviewImg" src="" alt="身份证反面预览">
                                <button type="button" class="remove-btn" onclick="removeImage('back')">
                                    <i class="fas fa-times"></i>
                                </button>
                            </div>
                        </div>
                    </div>

                    <div class="privacy-notice">
                        <p><i class="fas fa-lock"></i> 您的个人信息将被严格保密，仅用于身份验证</p>
                    </div>

                    <button type="submit" name="submit_verification" class="btn-primary">
                        重新提交认证
                    </button>
                </form>
            </div>

        <?php else: ?>
            <!-- 未认证状态 -->
            <div class="verification-intro">
                <div class="intro-icon">
                    <i class="fas fa-shield-alt"></i>
                </div>
                <h2>实名认证</h2>
                <p>为了保障平台安全，发布作品和组局需要完成实名认证</p>

                <div class="benefits">
                    <h4>认证后可以：</h4>
                    <ul>
                        <li><i class="fas fa-check"></i> 发布个人作品</li>
                        <li><i class="fas fa-check"></i> 发起组局活动</li>
                        <li><i class="fas fa-check"></i> 获得更多信任</li>
                        <li><i class="fas fa-check"></i> 享受更多功能</li>
                    </ul>
                </div>
            </div>

            <div class="verification-form">
                <?php if (isset($error_message)): ?>
                    <div class="error-message">
                        <i class="fas fa-exclamation-triangle"></i>
                        <?php echo htmlspecialchars($error_message); ?>
                    </div>
                <?php endif; ?>

                <?php if (isset($success_message)): ?>
                    <div class="success-message">
                        <i class="fas fa-check-circle"></i>
                        <?php echo htmlspecialchars($success_message); ?>
                    </div>
                <?php endif; ?>

                <form method="POST" action="">
                    <div class="form-group">
                        <label for="real_name">真实姓名</label>
                        <input type="text" id="real_name" name="real_name" required
                               placeholder="请输入身份证上的真实姓名">
                    </div>

                    <div class="form-group">
                        <label for="id_card_number">身份证号码</label>
                        <input type="text" id="id_card_number" name="id_card_number" required
                               placeholder="请输入18位身份证号码" maxlength="18">
                    </div>

                    <div class="form-group">
                        <label for="id_card_front">身份证正面照片</label>
                        <div class="upload-container">
                            <div class="upload-area" id="frontUploadArea">
                                <i class="fas fa-cloud-upload-alt"></i>
                                <p>点击上传身份证正面照片</p>
                                <small>支持 JPG、PNG 格式，文件大小不超过 5MB</small>
                            </div>
                            <input type="file" id="id_card_front" accept="image/*" style="display: none;">
                            <input type="hidden" name="id_card_front_url" id="id_card_front_url" required>
                            <div class="upload-preview" id="frontPreview" style="display: none;">
                                <img id="frontPreviewImg" src="" alt="身份证正面预览">
                                <button type="button" class="remove-btn" onclick="removeImage('front')">
                                    <i class="fas fa-times"></i>
                                </button>
                            </div>
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="id_card_back">身份证反面照片</label>
                        <div class="upload-container">
                            <div class="upload-area" id="backUploadArea">
                                <i class="fas fa-cloud-upload-alt"></i>
                                <p>点击上传身份证反面照片</p>
                                <small>支持 JPG、PNG 格式，文件大小不超过 5MB</small>
                            </div>
                            <input type="file" id="id_card_back" accept="image/*" style="display: none;">
                            <input type="hidden" name="id_card_back_url" id="id_card_back_url" required>
                            <div class="upload-preview" id="backPreview" style="display: none;">
                                <img id="backPreviewImg" src="" alt="身份证反面预览">
                                <button type="button" class="remove-btn" onclick="removeImage('back')">
                                    <i class="fas fa-times"></i>
                                </button>
                            </div>
                        </div>
                    </div>

                    <div class="privacy-notice">
                        <p><i class="fas fa-lock"></i> 您的个人信息将被严格保密，仅用于身份验证</p>
                    </div>

                    <button type="submit" name="submit_verification" class="btn-primary">
                        提交认证
                    </button>
                </form>
            </div>
        <?php endif; ?>
    </div>

    <script>
        // Cloudinary配置
        const CLOUDINARY_CONFIG = {
            cloud_name: 'dwcauq0wy',
            upload_preset: 'chat_app_preset'
        };

        // 身份证号码格式化
        document.addEventListener('DOMContentLoaded', function() {
            const idCardInput = document.getElementById('id_card_number');
            const realNameInput = document.getElementById('real_name');

            if (idCardInput) {
                idCardInput.addEventListener('input', function(e) {
                    let value = e.target.value.replace(/[^0-9Xx]/g, '');
                    if (value.length > 18) {
                        value = value.substring(0, 18);
                    }
                    e.target.value = value.toUpperCase();
                });
            }

            if (realNameInput) {
                realNameInput.addEventListener('input', function(e) {
                    let value = e.target.value.replace(/[^a-zA-Z\u4e00-\u9fa5·]/g, '');
                    if (value.length > 20) {
                        value = value.substring(0, 20);
                    }
                    e.target.value = value;
                });
            }

            // 图片上传功能
            setupImageUpload('front');
            setupImageUpload('back');
        });

        function setupImageUpload(type) {
            const uploadArea = document.getElementById(type + 'UploadArea');
            const fileInput = document.getElementById('id_card_' + type);
            const hiddenInput = document.getElementById('id_card_' + type + '_url');
            const preview = document.getElementById(type + 'Preview');
            const previewImg = document.getElementById(type + 'PreviewImg');

            if (!uploadArea || !fileInput) return;

            uploadArea.addEventListener('click', () => {
                fileInput.click();
            });

            fileInput.addEventListener('change', function(e) {
                const file = e.target.files[0];
                if (file) {
                    // 验证文件类型
                    if (!file.type.startsWith('image/')) {
                        showToast('请选择图片文件', 'error');
                        return;
                    }

                    // 验证文件大小 (5MB)
                    if (file.size > 5 * 1024 * 1024) {
                        showToast('图片大小不能超过5MB', 'error');
                        return;
                    }

                    uploadToCloudinary(file, type);
                }
            });
        }

        function uploadToCloudinary(file, type) {
            const uploadArea = document.getElementById(type + 'UploadArea');
            const preview = document.getElementById(type + 'Preview');
            const previewImg = document.getElementById(type + 'PreviewImg');
            const hiddenInput = document.getElementById('id_card_' + type + '_url');

            // 显示上传中状态
            uploadArea.innerHTML = '<i class="fas fa-spinner fa-spin"></i><p>上传中...</p>';

            const formData = new FormData();
            formData.append('file', file);
            formData.append('upload_preset', CLOUDINARY_CONFIG.upload_preset);

            fetch(`https://api.cloudinary.com/v1_1/${CLOUDINARY_CONFIG.cloud_name}/image/upload`, {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.secure_url) {
                    // 上传成功
                    hiddenInput.value = data.secure_url;
                    previewImg.src = data.secure_url;
                    uploadArea.style.display = 'none';
                    preview.style.display = 'block';
                    showToast('图片上传成功', 'success');
                } else {
                    throw new Error('上传失败');
                }
            })
            .catch(error => {
                console.error('Upload error:', error);
                showToast('图片上传失败，请重试', 'error');
                // 恢复上传区域
                uploadArea.innerHTML = `
                    <i class="fas fa-cloud-upload-alt"></i>
                    <p>点击上传身份证${type === 'front' ? '正面' : '反面'}照片</p>
                    <small>支持 JPG、PNG 格式，文件大小不超过 5MB</small>
                `;
            });
        }

        function removeImage(type) {
            const uploadArea = document.getElementById(type + 'UploadArea');
            const preview = document.getElementById(type + 'Preview');
            const hiddenInput = document.getElementById('id_card_' + type + '_url');
            const fileInput = document.getElementById('id_card_' + type);

            hiddenInput.value = '';
            fileInput.value = '';
            uploadArea.style.display = 'block';
            preview.style.display = 'none';
        }

        function showToast(message, type = 'info') {
            // 创建toast元素
            const toast = document.createElement('div');
            toast.className = `toast toast-${type}`;
            toast.innerHTML = `
                <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-circle' : 'info-circle'}"></i>
                <span>${message}</span>
            `;

            // 添加到页面
            document.body.appendChild(toast);

            // 显示动画
            setTimeout(() => toast.classList.add('show'), 100);

            // 3秒后移除
            setTimeout(() => {
                toast.classList.remove('show');
                setTimeout(() => document.body.removeChild(toast), 300);
            }, 3000);
        }
    </script>
</body>
</html>
