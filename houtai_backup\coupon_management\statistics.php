<?php
session_start();
require_once '../db_config.php';

// 检查管理员登录状态
if (!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
    header('Location: ../login.php');
    exit;
}

$error_message = '';
$success_message = '';

// 获取数据库连接
$pdo = getDbConnection();

// 获取优惠券统计数据
try {
    // 检查表是否存在
    $stmt = $pdo->query("SHOW TABLES LIKE 'camping_coupons'");
    if ($stmt->rowCount() == 0) {
        throw new Exception('优惠券表不存在，请先创建优惠券表');
    }

    // 检查字段是否存在
    $stmt = $pdo->query("DESCRIBE camping_coupons");
    $columns = $stmt->fetchAll(PDO::FETCH_COLUMN);
    $has_used_quantity = in_array('used_quantity', $columns);

    // 总体统计 - 根据字段存在情况调整查询
    if ($has_used_quantity) {
        $stmt = $pdo->prepare("
            SELECT
                COUNT(*) as total_coupons,
                SUM(CASE WHEN status = 'active' THEN 1 ELSE 0 END) as active_coupons,
                SUM(CASE WHEN status = 'inactive' THEN 1 ELSE 0 END) as inactive_coupons,
                SUM(total_quantity) as total_issued,
                SUM(claimed_quantity) as total_claimed,
                SUM(COALESCE(used_quantity, 0)) as total_used,
                SUM(total_quantity * discount_amount) as total_value,
                SUM(COALESCE(used_quantity, 0) * discount_amount) as used_value
            FROM camping_coupons
        ");
    } else {
        // 如果没有 used_quantity 字段，从用户优惠券表计算
        $stmt = $pdo->prepare("
            SELECT
                COUNT(*) as total_coupons,
                SUM(CASE WHEN status = 'active' THEN 1 ELSE 0 END) as active_coupons,
                SUM(CASE WHEN status = 'inactive' THEN 1 ELSE 0 END) as inactive_coupons,
                SUM(total_quantity) as total_issued,
                SUM(claimed_quantity) as total_claimed,
                (SELECT COUNT(*) FROM user_camping_coupons WHERE status = 'used') as total_used,
                SUM(total_quantity * discount_amount) as total_value,
                (SELECT SUM(cc2.discount_amount) FROM user_camping_coupons ucc2
                 JOIN camping_coupons cc2 ON ucc2.coupon_id = cc2.id
                 WHERE ucc2.status = 'used') as used_value
            FROM camping_coupons
        ");
    }
    $stmt->execute();
    $overall_stats = $stmt->fetch(PDO::FETCH_ASSOC);

    // 确保数据不为空
    if (!$overall_stats) {
        $overall_stats = [
            'total_coupons' => 0,
            'active_coupons' => 0,
            'inactive_coupons' => 0,
            'total_issued' => 0,
            'total_claimed' => 0,
            'total_used' => 0,
            'total_value' => 0,
            'used_value' => 0
        ];
    }

    // 计算统计率
    $claim_rate = $overall_stats['total_issued'] > 0 ?
        round(($overall_stats['total_claimed'] / $overall_stats['total_issued']) * 100, 2) : 0;
    $usage_rate = $overall_stats['total_claimed'] > 0 ?
        round(($overall_stats['total_used'] / $overall_stats['total_claimed']) * 100, 2) : 0;
    $churn_rate = $overall_stats['total_claimed'] > 0 ?
        round((($overall_stats['total_claimed'] - $overall_stats['total_used']) / $overall_stats['total_claimed']) * 100, 2) : 0;

    // 按类型统计 - 根据字段存在情况调整查询
    if ($has_used_quantity) {
        $stmt = $pdo->prepare("
            SELECT
                type,
                COUNT(*) as coupon_count,
                SUM(total_quantity) as total_issued,
                SUM(claimed_quantity) as total_claimed,
                SUM(COALESCE(used_quantity, 0)) as total_used,
                SUM(total_quantity * discount_amount) as total_value,
                SUM(COALESCE(used_quantity, 0) * discount_amount) as used_value
            FROM camping_coupons
            GROUP BY type
            ORDER BY total_issued DESC
        ");
    } else {
        $stmt = $pdo->prepare("
            SELECT
                cc.type,
                COUNT(*) as coupon_count,
                SUM(cc.total_quantity) as total_issued,
                SUM(cc.claimed_quantity) as total_claimed,
                COALESCE(used_stats.total_used, 0) as total_used,
                SUM(cc.total_quantity * cc.discount_amount) as total_value,
                COALESCE(used_stats.used_value, 0) as used_value
            FROM camping_coupons cc
            LEFT JOIN (
                SELECT
                    cc2.type,
                    COUNT(*) as total_used,
                    SUM(cc2.discount_amount) as used_value
                FROM user_camping_coupons ucc2
                JOIN camping_coupons cc2 ON ucc2.coupon_id = cc2.id
                WHERE ucc2.status = 'used'
                GROUP BY cc2.type
            ) used_stats ON cc.type = used_stats.type
            GROUP BY cc.type
            ORDER BY total_issued DESC
        ");
    }
    $stmt->execute();
    $type_stats = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // 确保 type_stats 不为空
    if (!$type_stats) {
        $type_stats = [];
    }

    // 最近30天的优惠券使用趋势
    $stmt = $pdo->prepare("
        SELECT
            DATE(ucc.claimed_at) as date,
            COUNT(*) as claimed_count,
            COUNT(CASE WHEN ucc.used_at IS NOT NULL THEN 1 END) as used_count
        FROM user_camping_coupons ucc
        WHERE ucc.claimed_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)
        GROUP BY DATE(ucc.claimed_at)
        ORDER BY date DESC
        LIMIT 30
    ");
    $stmt->execute();
    $trend_data = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // 热门优惠券排行 - 根据字段存在情况调整查询
    if ($has_used_quantity) {
        $stmt = $pdo->prepare("
            SELECT
                cc.id,
                cc.title,
                cc.type,
                cc.discount_amount,
                cc.total_quantity,
                cc.claimed_quantity,
                COALESCE(cc.used_quantity, 0) as used_quantity,
                ROUND((cc.claimed_quantity / cc.total_quantity) * 100, 2) as claim_rate,
                CASE
                    WHEN cc.claimed_quantity > 0 THEN ROUND((COALESCE(cc.used_quantity, 0) / cc.claimed_quantity) * 100, 2)
                    ELSE 0
                END as usage_rate
            FROM camping_coupons cc
            WHERE cc.total_quantity > 0
            ORDER BY cc.claimed_quantity DESC
            LIMIT 10
        ");
    } else {
        $stmt = $pdo->prepare("
            SELECT
                cc.id,
                cc.title,
                cc.type,
                cc.discount_amount,
                cc.total_quantity,
                cc.claimed_quantity,
                COALESCE(used_stats.used_quantity, 0) as used_quantity,
                ROUND((cc.claimed_quantity / cc.total_quantity) * 100, 2) as claim_rate,
                CASE
                    WHEN cc.claimed_quantity > 0 THEN ROUND((COALESCE(used_stats.used_quantity, 0) / cc.claimed_quantity) * 100, 2)
                    ELSE 0
                END as usage_rate
            FROM camping_coupons cc
            LEFT JOIN (
                SELECT
                    coupon_id,
                    COUNT(*) as used_quantity
                FROM user_camping_coupons
                WHERE status = 'used'
                GROUP BY coupon_id
            ) used_stats ON cc.id = used_stats.coupon_id
            WHERE cc.total_quantity > 0
            ORDER BY cc.claimed_quantity DESC
            LIMIT 10
        ");
    }
    $stmt->execute();
    $popular_coupons = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // 确保 popular_coupons 不为空
    if (!$popular_coupons) {
        $popular_coupons = [];
    }

    // 用户领取统计 - 检查字段是否存在
    $stmt = $pdo->query("SHOW COLUMNS FROM users");
    $user_columns = $stmt->fetchAll(PDO::FETCH_COLUMN);
    $has_quwanplanet_id = in_array('quwanplanet_id', $user_columns);
    $has_quwan_id = in_array('quwan_id', $user_columns);

    // 根据字段存在情况选择查询
    $user_id_field = 'u.id';
    if ($has_quwanplanet_id) {
        $user_id_field = 'u.quwanplanet_id';
    } elseif ($has_quwan_id) {
        $user_id_field = 'u.quwan_id';
    }

    $stmt = $pdo->prepare("
        SELECT
            u.id,
            u.username,
            {$user_id_field} as quwanplanet_id,
            COUNT(ucc.id) as total_claimed,
            COUNT(CASE WHEN ucc.status = 'used' THEN 1 END) as total_used,
            SUM(cc.discount_amount) as total_value,
            SUM(CASE WHEN ucc.status = 'used' THEN cc.discount_amount ELSE 0 END) as used_value
        FROM users u
        LEFT JOIN user_camping_coupons ucc ON u.id = ucc.user_id
        LEFT JOIN camping_coupons cc ON ucc.coupon_id = cc.id
        WHERE ucc.id IS NOT NULL
        GROUP BY u.id
        ORDER BY total_claimed DESC
        LIMIT 20
    ");
    $stmt->execute();
    $user_stats = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // 确保 user_stats 不为空
    if (!$user_stats) {
        $user_stats = [];
    }

    // 确保 trend_data 不为空
    if (!$trend_data) {
        $trend_data = [];
    }

} catch (Exception $e) {
    $error_message = $e->getMessage();
} catch (PDOException $e) {
    $error_message = '数据库错误：' . $e->getMessage();
}

// 类型名称映射
$type_names = [
    'join_discount' => '参加活动优惠券',
    'organize_discount' => '组局活动优惠券',
    'newbie_discount' => '新人专享优惠券'
];
?>

<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>优惠券统计分析 - 趣玩星球管理后台</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="../assets/css/admin-layout.css">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        :root {
            --primary-color: #40E0D0;
            --primary-light: #AFFBF2;
            --success-color: #10B981;
            --warning-color: #F59E0B;
            --error-color: #EF4444;
            --info-color: #3B82F6;
            --gray-50: #F9FAFB;
            --gray-100: #F3F4F6;
            --gray-200: #E5E7EB;
            --gray-300: #D1D5DB;
            --gray-400: #9CA3AF;
            --gray-500: #6B7280;
            --gray-600: #4B5563;
            --gray-700: #374151;
            --gray-800: #1F2937;
            --gray-900: #111827;
            --spacing-xs: 4px;
            --spacing-sm: 8px;
            --spacing-md: 16px;
            --spacing-lg: 24px;
            --spacing-xl: 32px;
            --radius-sm: 4px;
            --radius-md: 8px;
            --radius-lg: 12px;
            --radius-xl: 16px;
        }

        .statistics-content {
            padding: var(--spacing-xl);
        }

        .page-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: var(--spacing-xl);
            padding-bottom: 20px;
            border-bottom: 2px solid var(--gray-100);
        }

        .page-title {
            display: flex;
            align-items: center;
            gap: var(--spacing-md);
        }

        .page-title h1 {
            font-size: 2rem;
            font-weight: 700;
            color: var(--gray-900);
            margin: 0;
        }

        .page-icon {
            width: 48px;
            height: 48px;
            background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
            border-radius: var(--radius-xl);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 20px;
            box-shadow: 0 8px 20px rgba(64, 224, 208, 0.3);
        }

        .back-btn {
            display: inline-flex;
            align-items: center;
            gap: var(--spacing-sm);
            padding: 12px 20px;
            background: linear-gradient(135deg, var(--gray-200), var(--gray-300));
            color: var(--gray-800);
            text-decoration: none;
            border-radius: var(--radius-lg);
            font-weight: 600;
            transition: all 0.3s ease;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }

        .back-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 16px rgba(0, 0, 0, 0.15);
            text-decoration: none;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: var(--spacing-lg);
            margin-bottom: var(--spacing-xl);
        }

        .stat-card {
            background: white;
            border-radius: var(--radius-xl);
            padding: var(--spacing-lg);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
            border: 2px solid transparent;
            transition: all 0.3s ease;
        }

        .stat-card:hover {
            border-color: var(--primary-color);
            transform: translateY(-2px);
            box-shadow: 0 12px 40px rgba(0, 0, 0, 0.12);
        }

        .stat-header {
            display: flex;
            align-items: center;
            gap: var(--spacing-md);
            margin-bottom: var(--spacing-md);
        }

        .stat-icon {
            width: 40px;
            height: 40px;
            border-radius: var(--radius-lg);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 18px;
        }

        .stat-icon.primary { background: var(--primary-color); }
        .stat-icon.success { background: var(--success-color); }
        .stat-icon.warning { background: var(--warning-color); }
        .stat-icon.info { background: var(--info-color); }

        .stat-title {
            font-size: 0.875rem;
            color: var(--gray-600);
            font-weight: 500;
        }

        .stat-value {
            font-size: 2rem;
            font-weight: 700;
            color: var(--gray-900);
            margin-bottom: var(--spacing-xs);
        }

        .stat-change {
            font-size: 0.75rem;
            font-weight: 500;
        }

        .stat-change.positive { color: var(--success-color); }
        .stat-change.negative { color: var(--error-color); }

        .section-card {
            background: white;
            border-radius: var(--radius-xl);
            padding: 0;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
            border: 2px solid transparent;
            transition: all 0.3s ease;
            overflow: hidden;
            margin-bottom: var(--spacing-xl);
        }

        .section-card:hover {
            border-color: var(--primary-color);
            box-shadow: 0 12px 40px rgba(0, 0, 0, 0.12);
        }

        .card-header {
            background: linear-gradient(135deg, var(--gray-50), var(--gray-100));
            padding: var(--spacing-lg) var(--spacing-xl);
            border-bottom: 2px solid var(--gray-200);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .card-header h3 {
            font-size: 1.25rem;
            font-weight: 700;
            color: var(--gray-900);
            margin: 0;
            display: flex;
            align-items: center;
            gap: var(--spacing-md);
        }

        .card-header h3 i {
            color: var(--primary-color);
        }

        .card-body {
            padding: var(--spacing-xl);
        }

        .chart-container {
            position: relative;
            height: 400px;
            margin-bottom: var(--spacing-lg);
        }

        .table-responsive {
            overflow-x: auto;
        }

        .stats-table {
            width: 100%;
            border-collapse: collapse;
            font-size: 0.875rem;
        }

        .stats-table th,
        .stats-table td {
            padding: var(--spacing-md);
            text-align: left;
            border-bottom: 1px solid var(--gray-200);
        }

        .stats-table th {
            background: var(--gray-50);
            font-weight: 600;
            color: var(--gray-700);
        }

        .stats-table tr:hover {
            background: var(--gray-50);
        }

        .badge {
            padding: 4px 8px;
            border-radius: var(--radius-sm);
            font-size: 0.75rem;
            font-weight: 600;
        }

        .badge.primary { background: rgba(64, 224, 208, 0.1); color: var(--primary-color); }
        .badge.success { background: rgba(16, 185, 129, 0.1); color: var(--success-color); }
        .badge.warning { background: rgba(245, 158, 11, 0.1); color: var(--warning-color); }
        .badge.info { background: rgba(59, 130, 246, 0.1); color: var(--info-color); }

        .progress-bar {
            width: 100%;
            height: 8px;
            background: var(--gray-200);
            border-radius: var(--radius-sm);
            overflow: hidden;
        }

        .progress-fill {
            height: 100%;
            background: var(--primary-color);
            transition: width 0.3s ease;
        }

        .empty-state {
            text-align: center;
            padding: var(--spacing-xl);
            color: var(--gray-500);
        }

        .empty-state i {
            font-size: 3rem;
            margin-bottom: var(--spacing-md);
            color: var(--gray-300);
        }

        @media (max-width: 768px) {
            .statistics-content {
                padding: var(--spacing-lg);
            }

            .stats-grid {
                grid-template-columns: 1fr;
            }

            .page-header {
                flex-direction: column;
                align-items: flex-start;
                gap: var(--spacing-md);
            }

            .chart-container {
                height: 300px;
            }
        }
    </style>
</head>
<body>
    <div class="admin-layout">
        <!-- 引入侧边栏 -->
        <?php include '../includes/sidebar.php'; ?>

        <!-- 主内容区域 -->
        <div class="main-content">
            <!-- 引入顶部栏 -->
            <?php include '../includes/topbar.php'; ?>

            <!-- 页面内容 -->
            <main class="content-area">
                <div class="statistics-content">
                    <!-- 页面标题 -->
                    <div class="page-header">
                        <div class="page-title">
                            <div class="page-icon">
                                <i class="fas fa-chart-bar"></i>
                            </div>
                            <h1>优惠券统计分析</h1>
                        </div>
                        <a href="index.php" class="back-btn">
                            <i class="fas fa-arrow-left"></i>
                            返回优惠券管理
                        </a>
                    </div>

                    <?php if ($error_message): ?>
                        <div class="alert alert-danger">
                            <i class="fas fa-exclamation-circle"></i>
                            <?php echo htmlspecialchars($error_message); ?>
                        </div>
                    <?php endif; ?>

                    <!-- 总体统计 -->
                    <div class="stats-grid">
                        <div class="stat-card">
                            <div class="stat-header">
                                <div class="stat-icon primary">
                                    <i class="fas fa-ticket-alt"></i>
                                </div>
                                <div class="stat-title">总优惠券数</div>
                            </div>
                            <div class="stat-value"><?php echo number_format($overall_stats['total_coupons']); ?></div>
                            <div class="stat-change">
                                活跃: <?php echo $overall_stats['active_coupons']; ?> |
                                停用: <?php echo $overall_stats['inactive_coupons']; ?>
                            </div>
                        </div>

                        <div class="stat-card">
                            <div class="stat-header">
                                <div class="stat-icon info">
                                    <i class="fas fa-share"></i>
                                </div>
                                <div class="stat-title">领取率</div>
                            </div>
                            <div class="stat-value"><?php echo $claim_rate; ?>%</div>
                            <div class="stat-change">
                                已领取: <?php echo number_format($overall_stats['total_claimed']); ?> /
                                总发放: <?php echo number_format($overall_stats['total_issued']); ?>
                            </div>
                        </div>

                        <div class="stat-card">
                            <div class="stat-header">
                                <div class="stat-icon success">
                                    <i class="fas fa-check-circle"></i>
                                </div>
                                <div class="stat-title">使用率</div>
                            </div>
                            <div class="stat-value"><?php echo $usage_rate; ?>%</div>
                            <div class="stat-change">
                                已使用: <?php echo number_format($overall_stats['total_used']); ?> /
                                已领取: <?php echo number_format($overall_stats['total_claimed']); ?>
                            </div>
                        </div>

                        <div class="stat-card">
                            <div class="stat-header">
                                <div class="stat-icon warning">
                                    <i class="fas fa-exclamation-triangle"></i>
                                </div>
                                <div class="stat-title">流失率</div>
                            </div>
                            <div class="stat-value"><?php echo $churn_rate; ?>%</div>
                            <div class="stat-change">
                                未使用: <?php echo number_format($overall_stats['total_claimed'] - $overall_stats['total_used']); ?>
                            </div>
                        </div>

                        <div class="stat-card">
                            <div class="stat-header">
                                <div class="stat-icon primary">
                                    <i class="fas fa-yen-sign"></i>
                                </div>
                                <div class="stat-title">总价值</div>
                            </div>
                            <div class="stat-value">¥<?php echo number_format($overall_stats['total_value'], 2); ?></div>
                            <div class="stat-change">
                                已使用价值: ¥<?php echo number_format($overall_stats['used_value'], 2); ?>
                            </div>
                        </div>

                        <div class="stat-card">
                            <div class="stat-header">
                                <div class="stat-icon success">
                                    <i class="fas fa-coins"></i>
                                </div>
                                <div class="stat-title">节省金额</div>
                            </div>
                            <div class="stat-value">¥<?php echo number_format($overall_stats['used_value'], 2); ?></div>
                            <div class="stat-change">
                                用户实际节省的金额
                            </div>
                        </div>
                    </div>

                    <!-- 按类型统计 -->
                    <div class="section-card">
                        <div class="card-header">
                            <h3><i class="fas fa-chart-pie"></i> 按类型统计</h3>
                        </div>
                        <div class="card-body">
                            <?php if (!empty($type_stats)): ?>
                                <div class="chart-container">
                                    <canvas id="typeChart"></canvas>
                                </div>
                                <div class="table-responsive">
                                    <table class="stats-table">
                                        <thead>
                                            <tr>
                                                <th>优惠券类型</th>
                                                <th>数量</th>
                                                <th>总发放</th>
                                                <th>已领取</th>
                                                <th>已使用</th>
                                                <th>领取率</th>
                                                <th>使用率</th>
                                                <th>总价值</th>
                                                <th>已用价值</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php foreach ($type_stats as $stat): ?>
                                            <?php
                                            $type_claim_rate = $stat['total_issued'] > 0 ?
                                                round(($stat['total_claimed'] / $stat['total_issued']) * 100, 2) : 0;
                                            $type_usage_rate = $stat['total_claimed'] > 0 ?
                                                round(($stat['total_used'] / $stat['total_claimed']) * 100, 2) : 0;
                                            $type_name = isset($type_names[$stat['type']]) ? $type_names[$stat['type']] : $stat['type'];
                                            ?>
                                            <tr>
                                                <td>
                                                    <span class="badge primary"><?php echo htmlspecialchars($type_name); ?></span>
                                                </td>
                                                <td><?php echo number_format($stat['coupon_count']); ?></td>
                                                <td><?php echo number_format($stat['total_issued']); ?></td>
                                                <td><?php echo number_format($stat['total_claimed']); ?></td>
                                                <td><?php echo number_format($stat['total_used']); ?></td>
                                                <td>
                                                    <div style="display: flex; align-items: center; gap: 8px;">
                                                        <span><?php echo $type_claim_rate; ?>%</span>
                                                        <div class="progress-bar" style="width: 60px;">
                                                            <div class="progress-fill" style="width: <?php echo $type_claim_rate; ?>%;"></div>
                                                        </div>
                                                    </div>
                                                </td>
                                                <td>
                                                    <div style="display: flex; align-items: center; gap: 8px;">
                                                        <span><?php echo $type_usage_rate; ?>%</span>
                                                        <div class="progress-bar" style="width: 60px;">
                                                            <div class="progress-fill" style="width: <?php echo $type_usage_rate; ?>%;"></div>
                                                        </div>
                                                    </div>
                                                </td>
                                                <td>¥<?php echo number_format($stat['total_value'], 2); ?></td>
                                                <td>¥<?php echo number_format($stat['used_value'], 2); ?></td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                            <?php else: ?>
                                <div class="empty-state">
                                    <i class="fas fa-chart-pie"></i>
                                    <p>暂无类型统计数据</p>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>

                    <!-- 使用趋势 -->
                    <div class="section-card">
                        <div class="card-header">
                            <h3><i class="fas fa-chart-line"></i> 最近30天使用趋势</h3>
                        </div>
                        <div class="card-body">
                            <div class="chart-container">
                                <canvas id="trendChart"></canvas>
                            </div>
                        </div>
                    </div>

                    <!-- 热门优惠券排行 -->
                    <div class="section-card">
                        <div class="card-header">
                            <h3><i class="fas fa-trophy"></i> 热门优惠券排行</h3>
                        </div>
                        <div class="card-body">
                            <?php if (!empty($popular_coupons)): ?>
                                <div class="table-responsive">
                                    <table class="stats-table">
                                        <thead>
                                            <tr>
                                                <th>排名</th>
                                                <th>优惠券名称</th>
                                                <th>类型</th>
                                                <th>面额</th>
                                                <th>总发放</th>
                                                <th>已领取</th>
                                                <th>已使用</th>
                                                <th>领取率</th>
                                                <th>使用率</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php foreach ($popular_coupons as $index => $coupon): ?>
                                                <?php
                                                $type_name = isset($type_names[$coupon['type']]) ? $type_names[$coupon['type']] : $coupon['type'];
                                                $rank_class = '';
                                                if ($index === 0) $rank_class = 'success';
                                                elseif ($index === 1) $rank_class = 'warning';
                                                elseif ($index === 2) $rank_class = 'info';
                                                ?>
                                                <tr>
                                                    <td>
                                                        <span class="badge <?php echo $rank_class; ?>">
                                                            <?php if ($index < 3): ?>
                                                                <i class="fas fa-medal"></i>
                                                            <?php endif; ?>
                                                            #<?php echo $index + 1; ?>
                                                        </span>
                                                    </td>
                                                    <td><?php echo htmlspecialchars($coupon['title']); ?></td>
                                                    <td>
                                                        <span class="badge primary"><?php echo htmlspecialchars($type_name); ?></span>
                                                    </td>
                                                    <td>¥<?php echo number_format($coupon['discount_amount'], 0); ?></td>
                                                    <td><?php echo number_format($coupon['total_quantity']); ?></td>
                                                    <td><?php echo number_format($coupon['claimed_quantity']); ?></td>
                                                    <td><?php echo number_format($coupon['used_quantity']); ?></td>
                                                    <td>
                                                        <div style="display: flex; align-items: center; gap: 8px;">
                                                            <span><?php echo $coupon['claim_rate']; ?>%</span>
                                                            <div class="progress-bar" style="width: 60px;">
                                                                <div class="progress-fill" style="width: <?php echo $coupon['claim_rate']; ?>%;"></div>
                                                            </div>
                                                        </div>
                                                    </td>
                                                    <td>
                                                        <div style="display: flex; align-items: center; gap: 8px;">
                                                            <span><?php echo $coupon['usage_rate'] ?: '0'; ?>%</span>
                                                            <div class="progress-bar" style="width: 60px;">
                                                                <div class="progress-fill" style="width: <?php echo $coupon['usage_rate'] ?: 0; ?>%;"></div>
                                                            </div>
                                                        </div>
                                                    </td>
                                                </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                    </table>
                                </div>
                            <?php else: ?>
                                <div class="empty-state">
                                    <i class="fas fa-chart-bar"></i>
                                    <p>暂无优惠券数据</p>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>

                    <!-- 用户领取统计 -->
                    <div class="section-card">
                        <div class="card-header">
                            <h3><i class="fas fa-users"></i> 用户领取统计 TOP 20</h3>
                        </div>
                        <div class="card-body">
                            <?php if (!empty($user_stats)): ?>
                                <div class="table-responsive">
                                    <table class="stats-table">
                                        <thead>
                                            <tr>
                                                <th>排名</th>
                                                <th>用户</th>
                                                <th>趣玩ID</th>
                                                <th>总领取</th>
                                                <th>已使用</th>
                                                <th>使用率</th>
                                                <th>总价值</th>
                                                <th>已用价值</th>
                                                <th>操作</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php foreach ($user_stats as $index => $user): ?>
                                                <?php
                                                $user_usage_rate = $user['total_claimed'] > 0 ?
                                                    round(($user['total_used'] / $user['total_claimed']) * 100, 2) : 0;
                                                ?>
                                                <tr>
                                                    <td>
                                                        <span class="badge info">#<?php echo $index + 1; ?></span>
                                                    </td>
                                                    <td><?php echo htmlspecialchars($user['username']); ?></td>
                                                    <td><?php echo htmlspecialchars($user['quwanplanet_id']); ?></td>
                                                    <td><?php echo number_format($user['total_claimed']); ?></td>
                                                    <td><?php echo number_format($user['total_used']); ?></td>
                                                    <td>
                                                        <div style="display: flex; align-items: center; gap: 8px;">
                                                            <span><?php echo $user_usage_rate; ?>%</span>
                                                            <div class="progress-bar" style="width: 60px;">
                                                                <div class="progress-fill" style="width: <?php echo $user_usage_rate; ?>%;"></div>
                                                            </div>
                                                        </div>
                                                    </td>
                                                    <td>¥<?php echo number_format($user['total_value'], 2); ?></td>
                                                    <td>¥<?php echo number_format($user['used_value'], 2); ?></td>
                                                    <td>
                                                        <a href="../user_management/detail.php?id=<?php echo $user['id']; ?>"
                                                           class="badge primary" style="text-decoration: none;">
                                                            <i class="fas fa-eye"></i> 查看详情
                                                        </a>
                                                    </td>
                                                </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                    </table>
                                </div>
                            <?php else: ?>
                                <div class="empty-state">
                                    <i class="fas fa-users"></i>
                                    <p>暂无用户数据</p>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <!-- 引入管理后台布局脚本 -->
    <script src="../assets/js/admin-layout.js"></script>

    <script>
        // 图表数据
        const typeData = <?php echo json_encode($type_stats); ?>;
        const trendData = <?php echo json_encode(array_reverse($trend_data)); ?>;
        const typeNames = <?php echo json_encode($type_names); ?>;

        // 类型分布饼图
        if (typeData && typeData.length > 0) {
            const typeCtx = document.getElementById('typeChart');
            if (typeCtx) {
                new Chart(typeCtx.getContext('2d'), {
                    type: 'doughnut',
                    data: {
                        labels: typeData.map(item => typeNames[item.type] || item.type),
                        datasets: [{
                            data: typeData.map(item => item.total_claimed),
                            backgroundColor: [
                                '#40E0D0',
                                '#10B981',
                                '#F59E0B',
                                '#3B82F6',
                                '#EF4444'
                            ],
                            borderWidth: 2,
                            borderColor: '#fff'
                        }]
                    },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom',
                        labels: {
                            padding: 20,
                            usePointStyle: true
                        }
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                const total = context.dataset.data.reduce((a, b) => a + b, 0);
                                const percentage = ((context.parsed / total) * 100).toFixed(1);
                                return context.label + ': ' + context.parsed.toLocaleString() + ' (' + percentage + '%)';
                            }
                        }
                    }
                }
            }
                });
            }
        }

        // 趋势折线图
        if (trendData && trendData.length > 0) {
            const trendCtx = document.getElementById('trendChart');
            if (trendCtx) {
                new Chart(trendCtx.getContext('2d'), {
            type: 'line',
            data: {
                labels: trendData.map(item => {
                    const date = new Date(item.date);
                    return (date.getMonth() + 1) + '/' + date.getDate();
                }),
                datasets: [{
                    label: '领取数量',
                    data: trendData.map(item => item.claimed_count),
                    borderColor: '#40E0D0',
                    backgroundColor: 'rgba(64, 224, 208, 0.1)',
                    tension: 0.4,
                    fill: true
                }, {
                    label: '使用数量',
                    data: trendData.map(item => item.used_count),
                    borderColor: '#10B981',
                    backgroundColor: 'rgba(16, 185, 129, 0.1)',
                    tension: 0.4,
                    fill: true
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'top',
                        labels: {
                            usePointStyle: true,
                            padding: 20
                        }
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        grid: {
                            color: 'rgba(0, 0, 0, 0.1)'
                        }
                    },
                    x: {
                        grid: {
                            color: 'rgba(0, 0, 0, 0.1)'
                        }
                    }
                },
                interaction: {
                    intersect: false,
                    mode: 'index'
                }
            }
                });
            }
        }

        // 页面加载动画
        document.addEventListener('DOMContentLoaded', function() {
            const elements = document.querySelectorAll('.stat-card, .section-card');
            elements.forEach((el, index) => {
                el.style.opacity = '0';
                el.style.transform = 'translateY(30px)';
                setTimeout(() => {
                    el.style.transition = 'all 0.6s ease';
                    el.style.opacity = '1';
                    el.style.transform = 'translateY(0)';
                }, index * 100);
            });
        });
    </script>
</body>
</html>
