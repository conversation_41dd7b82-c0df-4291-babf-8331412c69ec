/* =====================================================
   趣玩星球客服管理系统样式
   现代化、年轻化、企业级设计
   ===================================================== */

/* 基础重置 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', 'Microsoft YaHei', Tahoma, Geneva, Verdana, sans-serif;
    background: #f8f9fa;
    color: #333333;
    line-height: 1.6;
    overflow-x: hidden;
}

/* 主容器 */
.cs-container {
    display: flex;
    min-height: 100vh;
    transition: all 0.3s ease;
}

/* ===== 侧边栏样式 ===== */
.cs-sidebar {
    width: 280px;
    background: linear-gradient(180deg, #6F7BF5 0%, #4D5DFB 100%);
    color: white;
    position: fixed;
    left: 0;
    top: 0;
    height: 100vh;
    z-index: 1000;
    transition: all 0.3s ease;
    box-shadow: 4px 0 20px rgba(0,0,0,0.1);
    display: flex;
    flex-direction: column;
}

.cs-sidebar-header {
    padding: 20px;
    border-bottom: 1px solid rgba(255,255,255,0.1);
}

.cs-logo {
    display: flex;
    align-items: center;
    gap: 12px;
}

.cs-logo-icon {
    width: 45px;
    height: 45px;
    background: rgba(255,255,255,0.15);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    backdrop-filter: blur(10px);
}

.cs-logo-image {
    width: 30px;
    height: 30px;
    object-fit: contain;
}

.cs-logo-text h2 {
    font-size: 18px;
    font-weight: 700;
    margin-bottom: 2px;
}

.cs-logo-text p {
    font-size: 12px;
    opacity: 0.8;
    font-weight: 500;
}

/* 导航菜单 */
.cs-sidebar-nav {
    flex: 1;
    padding: 20px 0;
    overflow-y: auto;
}

.cs-nav-list {
    list-style: none;
}

.cs-nav-item {
    margin-bottom: 4px;
}

.cs-nav-link {
    display: flex;
    align-items: center;
    padding: 12px 20px;
    color: rgba(255,255,255,0.8);
    text-decoration: none;
    transition: all 0.3s ease;
    position: relative;
    gap: 12px;
}

.cs-nav-link:hover {
    background: rgba(255,255,255,0.1);
    color: white;
    transform: translateX(4px);
}

.cs-nav-item.active .cs-nav-link {
    background: rgba(255,255,255,0.15);
    color: white;
    border-right: 3px solid #FFD166;
}

.cs-nav-link i {
    width: 20px;
    text-align: center;
    font-size: 16px;
}

.cs-nav-link-text {
    font-size: 14px;
    font-weight: 500;
}

.cs-nav-badge {
    background: #FF6B6B;
    color: white;
    font-size: 11px;
    padding: 2px 6px;
    border-radius: 10px;
    margin-left: auto;
    font-weight: 600;
    animation: pulse 2s infinite;
}

.cs-admin-only {
    background: #FFD166;
    color: #333;
    font-size: 10px;
    padding: 2px 6px;
    border-radius: 8px;
    margin-left: auto;
    font-weight: 600;
}

.cs-nav-divider {
    height: 1px;
    background: rgba(255,255,255,0.1);
    margin: 15px 20px;
}

/* 用户信息区域 */
.cs-user-info {
    padding: 20px;
    border-top: 1px solid rgba(255,255,255,0.1);
    display: flex;
    align-items: center;
    gap: 12px;
}

.cs-user-avatar {
    width: 45px;
    height: 45px;
    background: rgba(255,255,255,0.15);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
}

.cs-user-details {
    flex: 1;
}

.cs-user-name {
    font-size: 14px;
    font-weight: 600;
    margin-bottom: 2px;
}

.cs-user-role {
    font-size: 12px;
    opacity: 0.8;
    margin-bottom: 2px;
}

.cs-user-department {
    font-size: 11px;
    opacity: 0.7;
}

.cs-logout-btn {
    width: 35px;
    height: 35px;
    background: rgba(255,255,255,0.1);
    border: none;
    border-radius: 8px;
    color: white;
    cursor: pointer;
    transition: all 0.3s ease;
}

.cs-logout-btn:hover {
    background: #FF6B6B;
    transform: scale(1.05);
}

/* ===== 主内容区域 ===== */
.cs-main {
    flex: 1;
    margin-left: 280px;
    display: flex;
    flex-direction: column;
    min-height: 100vh;
    transition: all 0.3s ease;
}

/* 顶部导航栏 */
.cs-topbar {
    height: 70px;
    background: white;
    border-bottom: 1px solid #e9ecef;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 30px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.05);
    position: sticky;
    top: 0;
    z-index: 999;
}

.cs-topbar-left {
    display: flex;
    align-items: center;
    gap: 20px;
}

.cs-sidebar-toggle {
    width: 40px;
    height: 40px;
    background: #f8f9fa;
    border: none;
    border-radius: 8px;
    color: #6F7BF5;
    cursor: pointer;
    transition: all 0.3s ease;
}

.cs-sidebar-toggle:hover {
    background: #6F7BF5;
    color: white;
}

.cs-breadcrumb {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 14px;
}

.cs-breadcrumb-item a {
    color: #6F7BF5;
    text-decoration: none;
}

.cs-breadcrumb-separator {
    color: #999;
}

.cs-breadcrumb-current {
    color: #333;
    font-weight: 600;
}

/* 状态指示器 */
.cs-topbar-center {
    display: flex;
    align-items: center;
}

.cs-status-indicators {
    display: flex;
    gap: 30px;
}

.cs-status-item {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 16px;
    background: #f8f9fa;
    border-radius: 20px;
}

.cs-status-label {
    font-size: 12px;
    color: #666;
}

.cs-status-value {
    font-size: 14px;
    font-weight: 600;
    color: #333;
}

.text-success { color: #06D6A0 !important; }
.text-warning { color: #FFD166 !important; }
.text-info { color: #6F7BF5 !important; }
.text-danger { color: #FF6B6B !important; }
.text-primary { color: #6F7BF5 !important; }

/* 右侧操作区 */
.cs-topbar-right {
    display: flex;
    align-items: center;
    gap: 20px;
}

.cs-quick-actions {
    display: flex;
    align-items: center;
    gap: 10px;
}

.cs-quick-btn {
    width: 40px;
    height: 40px;
    background: #f8f9fa;
    border: none;
    border-radius: 8px;
    color: #666;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
}

.cs-quick-btn:hover {
    background: #6F7BF5;
    color: white;
    transform: translateY(-2px);
}

/* 通知系统 */
.cs-notifications {
    position: relative;
}

.cs-notification-badge {
    position: absolute;
    top: -5px;
    right: -5px;
    background: #FF6B6B;
    color: white;
    font-size: 10px;
    padding: 2px 5px;
    border-radius: 10px;
    font-weight: 600;
}

.cs-notification-dropdown {
    position: absolute;
    top: 50px;
    right: 0;
    width: 350px;
    background: white;
    border-radius: 12px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.15);
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: all 0.3s ease;
    z-index: 1000;
}

.cs-notification-dropdown.show {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

/* 内容区域 */
.cs-content {
    flex: 1;
    padding: 30px;
    background: #f8f9fa;
}

.cs-header {
    margin-bottom: 30px;
}

.cs-header h1 {
    font-size: 28px;
    font-weight: 700;
    color: #333;
    margin-bottom: 8px;
    display: flex;
    align-items: center;
    gap: 12px;
}

.cs-header p {
    color: #666;
    font-size: 16px;
}

/* 统计卡片 */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.stat-card {
    background: white;
    padding: 25px;
    border-radius: 16px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.08);
    display: flex;
    align-items: center;
    gap: 20px;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.stat-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 30px rgba(0,0,0,0.12);
}

.stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 4px;
    height: 100%;
    background: var(--card-color);
}

.stat-card.primary { --card-color: #6F7BF5; }
.stat-card.warning { --card-color: #FFD166; }
.stat-card.success { --card-color: #06D6A0; }
.stat-card.info { --card-color: #4D5DFB; }

.stat-icon {
    width: 60px;
    height: 60px;
    border-radius: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
    color: white;
    background: var(--card-color);
}

.stat-content h3 {
    font-size: 32px;
    font-weight: 700;
    color: #333;
    margin-bottom: 4px;
}

.stat-content p {
    color: #666;
    font-size: 14px;
    font-weight: 500;
}

.stat-trend {
    margin-left: auto;
    display: flex;
    align-items: center;
    gap: 4px;
    font-size: 12px;
    font-weight: 600;
}

.stat-trend.up {
    color: #06D6A0;
}

.stat-action {
    margin-left: auto;
}

.btn-sm {
    padding: 6px 12px;
    background: var(--card-color);
    color: white;
    text-decoration: none;
    border-radius: 6px;
    font-size: 12px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn-sm:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.2);
}

/* 快捷操作 */
.quick-actions {
    margin-bottom: 30px;
}

.quick-actions h2 {
    font-size: 20px;
    font-weight: 600;
    color: #333;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    gap: 10px;
}

.action-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
}

.action-card {
    background: white;
    padding: 25px;
    border-radius: 16px;
    text-decoration: none;
    color: #333;
    transition: all 0.3s ease;
    box-shadow: 0 4px 20px rgba(0,0,0,0.08);
    text-align: center;
}

.action-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 30px rgba(0,0,0,0.12);
    color: #6F7BF5;
}

.action-icon {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, #6F7BF5, #4D5DFB);
    border-radius: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
    color: white;
    margin: 0 auto 15px;
    transition: all 0.3s ease;
}

.action-card:hover .action-icon {
    transform: scale(1.1);
}

.action-card h3 {
    font-size: 16px;
    font-weight: 600;
    margin-bottom: 8px;
}

.action-card p {
    font-size: 14px;
    color: #666;
    line-height: 1.5;
}

/* 最近会话 */
.recent-sessions {
    background: white;
    border-radius: 16px;
    padding: 25px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.08);
}

.recent-sessions h2 {
    font-size: 20px;
    font-weight: 600;
    color: #333;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    gap: 10px;
}

.loading {
    text-align: center;
    padding: 40px;
    color: #666;
}

.loading i {
    font-size: 24px;
    margin-bottom: 10px;
    color: #6F7BF5;
}

.no-data {
    text-align: center;
    padding: 40px;
    color: #999;
    font-style: italic;
}

/* 动画效果 */
@keyframes pulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.05); }
}

/* 响应式设计 */
@media (max-width: 768px) {
    .cs-sidebar {
        width: 260px;
        transform: translateX(-100%);
    }
    
    .cs-main {
        margin-left: 0;
    }
    
    .cs-container.sidebar-open .cs-sidebar {
        transform: translateX(0);
    }
    
    .stats-grid {
        grid-template-columns: 1fr;
    }
    
    .action-grid {
        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    }
    
    .cs-topbar {
        padding: 0 15px;
    }
    
    .cs-content {
        padding: 20px 15px;
    }
}
