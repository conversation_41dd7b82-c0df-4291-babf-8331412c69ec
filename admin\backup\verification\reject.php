<?php
// 设置字符编码
header('Content-Type: text/html; charset=UTF-8');
ini_set('default_charset', 'UTF-8');

session_start();

// 检查登录状态
if (!isset($_SESSION['admin_id'])) {
    header('Location: login.php');
    exit;
}

// 直接引用当前目录的数据库配置文件
require_once 'db_config.php';

$id = intval($_GET['id'] ?? 0);
if (!$id) {
    header('Location: verification_list.php');
    exit;
}

$message = '';
$error = '';

// 预设拒绝原因
$reject_reasons = [
    '身份证照片不清晰',
    '身份证信息与填写信息不符',
    '身份证已过期',
    '提供的身份证为临时身份证',
    '身份证照片存在PS痕迹',
    '姓名信息不符',
    '身份证号码格式错误',
    '提供的不是有效身份证件',
    '其他原因'
];

try {
    $pdo = getDbConnection();

    // 获取认证信息
    $stmt = $pdo->prepare("
        SELECT rv.*, u.username
        FROM realname_verification rv
        LEFT JOIN users u ON rv.user_id = u.id
        WHERE rv.id = ?
    ");
    $stmt->execute([$id]);
    $verification = $stmt->fetch(PDO::FETCH_ASSOC);

    if (!$verification) {
        header('Location: verification_list.php');
        exit;
    }

    // 处理审核拒绝
    if ($_SERVER['REQUEST_METHOD'] === 'POST') {
        $reason = trim($_POST['reason'] ?? '');
        $custom_reason = trim($_POST['custom_reason'] ?? '');

        if ($verification['verification_status'] !== 'pending') {
            $error = '该申请已经被处理过了';
        } elseif (empty($reason) && empty($custom_reason)) {
            $error = '请选择或填写拒绝原因';
        } else {
            // 确定最终的拒绝原因
            $final_reason = $reason === '其他原因' ? $custom_reason : $reason;

            if (empty($final_reason)) {
                $error = '请填写具体的拒绝原因';
            } else {
                try {
                    // 更新认证状态
                    $stmt = $pdo->prepare("
                        UPDATE realname_verification
                        SET verification_status = 'rejected',
                            verified_by = ?,
                            verification_reason = ?,
                            verified_at = NOW()
                        WHERE id = ?
                    ");
                    $stmt->execute([$_SESSION['admin_id'], $final_reason, $id]);

                    $message = '实名认证审核拒绝成功！';

                    // 3秒后跳转
                    header("refresh:3;url=verification_list.php");

                } catch (Exception $e) {
                    $error = '操作失败：' . $e->getMessage();
                }
            }
        }
    }

} catch (PDOException $e) {
    $error = '数据库错误：' . $e->getMessage();
}
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>审核拒绝 - <?php echo htmlspecialchars($verification['real_name'] ?? ''); ?></title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #f5f6fa;
            color: #333;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .container {
            background: white;
            padding: 40px;
            border-radius: 15px;
            box-shadow: 0 15px 35px rgba(0,0,0,0.1);
            width: 100%;
            max-width: 600px;
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
        }

        .header h1 {
            color: #e74c3c;
            margin-bottom: 10px;
            font-size: 28px;
        }

        .header p {
            color: #666;
            font-size: 16px;
        }

        .verification-info {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 30px;
        }

        .info-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
            padding: 8px 0;
            border-bottom: 1px solid #eee;
        }

        .info-row:last-child {
            border-bottom: none;
            margin-bottom: 0;
        }

        .info-label {
            font-weight: 600;
            color: #555;
        }

        .info-value {
            color: #333;
        }

        .id-card {
            font-family: monospace;
            letter-spacing: 1px;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #333;
        }

        .reason-options {
            display: grid;
            grid-template-columns: 1fr;
            gap: 10px;
            margin-bottom: 15px;
        }

        .reason-option {
            display: flex;
            align-items: center;
            padding: 12px;
            border: 2px solid #e1e5e9;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s;
        }

        .reason-option:hover {
            border-color: #e74c3c;
            background: #fdf2f2;
        }

        .reason-option.selected {
            border-color: #e74c3c;
            background: #fdf2f2;
        }

        .reason-option input[type="radio"] {
            margin-right: 10px;
        }

        .custom-reason {
            margin-top: 15px;
            display: none;
        }

        .custom-reason.show {
            display: block;
        }

        .custom-reason textarea {
            width: 100%;
            padding: 12px;
            border: 2px solid #e1e5e9;
            border-radius: 8px;
            font-size: 14px;
            resize: vertical;
            min-height: 100px;
        }

        .custom-reason textarea:focus {
            outline: none;
            border-color: #e74c3c;
        }

        .actions {
            display: flex;
            gap: 15px;
            justify-content: center;
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            font-size: 16px;
            font-weight: 500;
            transition: all 0.3s;
        }

        .btn-danger {
            background: #e74c3c;
            color: white;
        }

        .btn-secondary {
            background: #95a5a6;
            color: white;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 15px rgba(0,0,0,0.2);
        }

        .alert {
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .alert-success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }

        .alert-danger {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }

        .warning-box {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
        }

        .warning-box h4 {
            margin-bottom: 10px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        @media (max-width: 768px) {
            .container {
                margin: 20px;
                padding: 30px 20px;
            }

            .actions {
                flex-direction: column;
            }

            .info-row {
                flex-direction: column;
                gap: 5px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1><i class="fas fa-times-circle"></i> 审核拒绝</h1>
            <p>实名认证审核</p>
        </div>

        <?php if ($message): ?>
            <div class="alert alert-success">
                <i class="fas fa-check-circle"></i>
                <?php echo htmlspecialchars($message); ?>
                <br><small>页面将在3秒后自动跳转...</small>
            </div>
        <?php endif; ?>

        <?php if ($error): ?>
            <div class="alert alert-danger">
                <i class="fas fa-exclamation-triangle"></i>
                <?php echo htmlspecialchars($error); ?>
            </div>
        <?php endif; ?>

        <?php if (!$message): ?>
            <div class="verification-info">
                <div class="info-row">
                    <span class="info-label">申请ID：</span>
                    <span class="info-value"><?php echo $verification['id']; ?></span>
                </div>
                <div class="info-row">
                    <span class="info-label">用户名：</span>
                    <span class="info-value"><?php echo htmlspecialchars($verification['username']); ?></span>
                </div>
                <div class="info-row">
                    <span class="info-label">真实姓名：</span>
                    <span class="info-value"><?php echo htmlspecialchars($verification['real_name']); ?></span>
                </div>
                <div class="info-row">
                    <span class="info-label">身份证号：</span>
                    <span class="info-value id-card"><?php echo htmlspecialchars($verification['id_card_number']); ?></span>
                </div>
                <div class="info-row">
                    <span class="info-label">提交时间：</span>
                    <span class="info-value">
                        <?php
                        if ($verification['submitted_at'] && $verification['submitted_at'] !== '0000-00-00 00:00:00') {
                            echo date('Y-m-d H:i:s', strtotime($verification['submitted_at']));
                        } else {
                            echo '-';
                        }
                        ?>
                    </span>
                </div>
            </div>

            <?php if ($verification['verification_status'] === 'pending'): ?>
                <form method="POST">
                    <div class="form-group">
                        <label>拒绝原因：</label>
                        <div class="reason-options">
                            <?php foreach ($reject_reasons as $reason): ?>
                                <label class="reason-option">
                                    <input type="radio" name="reason" value="<?php echo htmlspecialchars($reason); ?>"
                                           onchange="toggleCustomReason(this)">
                                    <?php echo htmlspecialchars($reason); ?>
                                </label>
                            <?php endforeach; ?>
                        </div>

                        <div class="custom-reason" id="customReason">
                            <label for="custom_reason">请详细说明拒绝原因：</label>
                            <textarea name="custom_reason" id="custom_reason"
                                      placeholder="请详细说明拒绝的具体原因..."></textarea>
                        </div>
                    </div>

                    <div class="warning-box">
                        <h4><i class="fas fa-exclamation-triangle"></i> 确认操作</h4>
                        <p>您确定要拒绝此实名认证申请吗？拒绝后：</p>
                        <ul style="margin: 10px 0 0 20px;">
                            <li>用户将收到拒绝通知</li>
                            <li>用户可以重新提交认证申请</li>
                            <li>此操作将被记录</li>
                        </ul>
                    </div>

                    <div class="actions">
                        <button type="submit" class="btn btn-danger"
                                onclick="return confirm('确认拒绝此实名认证申请？')">
                            <i class="fas fa-times"></i> 确认拒绝
                        </button>
                        <a href="verification_list.php" class="btn btn-secondary">
                            <i class="fas fa-arrow-left"></i> 返回列表
                        </a>
                    </div>
                </form>
            <?php else: ?>
                <div class="alert alert-danger">
                    <i class="fas fa-info-circle"></i>
                    该申请状态为：<?php echo $verification['verification_status']; ?>，无法进行审核操作
                </div>
                <div class="actions">
                    <a href="verification_list.php" class="btn btn-secondary">
                        <i class="fas fa-arrow-left"></i> 返回列表
                    </a>
                </div>
            <?php endif; ?>
        <?php else: ?>
            <div class="actions">
                <a href="verification_list.php" class="btn btn-success">
                    <i class="fas fa-list"></i> 返回列表
                </a>
            </div>
        <?php endif; ?>
    </div>

    <script>
        function toggleCustomReason(radio) {
            const customReason = document.getElementById('customReason');
            const customTextarea = document.getElementById('custom_reason');

            // 移除所有选中状态
            document.querySelectorAll('.reason-option').forEach(option => {
                option.classList.remove('selected');
            });

            // 添加当前选中状态
            radio.closest('.reason-option').classList.add('selected');

            if (radio.value === '其他原因') {
                customReason.classList.add('show');
                customTextarea.focus();
                customTextarea.required = true;
            } else {
                customReason.classList.remove('show');
                customTextarea.required = false;
                customTextarea.value = '';
            }
        }

        // 页面加载时检查是否有选中的选项
        document.addEventListener('DOMContentLoaded', function() {
            const checkedRadio = document.querySelector('input[name="reason"]:checked');
            if (checkedRadio) {
                toggleCustomReason(checkedRadio);
            }
        });
    </script>
</body>
</html>
