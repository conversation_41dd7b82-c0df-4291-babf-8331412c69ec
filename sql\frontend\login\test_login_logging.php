<?php
/**
 * 测试前台登录记录功能
 */

require_once '../../sql/db_config.php';

header('Content-Type: text/plain; charset=utf-8');

echo "=== 前台登录记录功能测试 ===\n\n";

try {
    $pdo = getDbConnection();
    echo "✓ 数据库连接成功\n";
} catch (Exception $e) {
    echo "✗ 数据库连接失败: " . $e->getMessage() . "\n";
    exit;
}

// 检查login_logs表
echo "\n1. 检查login_logs表结构:\n";
try {
    $stmt = $pdo->query("DESCRIBE login_logs");
    $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "表字段:\n";
    foreach ($columns as $column) {
        echo "  - {$column['Field']} ({$column['Type']}) {$column['Null']} {$column['Key']}\n";
    }
    
    // 检查数据总数
    $stmt = $pdo->query("SELECT COUNT(*) as total FROM login_logs");
    $total = $stmt->fetch()['total'];
    echo "\n总记录数: $total\n";
    
} catch (Exception $e) {
    echo "✗ 检查表结构失败: " . $e->getMessage() . "\n";
}

// 检查最近的登录记录
echo "\n2. 最近10条登录记录:\n";
try {
    $stmt = $pdo->query("
        SELECT 
            l.id,
            l.user_id,
            u.username,
            u.quwan_id,
            l.login_time,
            l.ip_address,
            l.login_type,
            l.status,
            l.location,
            SUBSTRING(l.user_agent, 1, 50) as user_agent_short
        FROM login_logs l
        LEFT JOIN users u ON l.user_id = u.id
        ORDER BY l.login_time DESC
        LIMIT 10
    ");
    
    $records = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (empty($records)) {
        echo "  暂无登录记录\n";
    } else {
        foreach ($records as $record) {
            echo sprintf(
                "  [%d] 用户%d(%s/%s) %s %s %s %s %s\n",
                $record['id'],
                $record['user_id'],
                $record['username'] ?? '未知',
                $record['quwan_id'] ?? '未知',
                $record['login_time'],
                $record['ip_address'],
                $record['login_type'],
                $record['status'],
                $record['location'] ?? '未知位置'
            );
        }
    }
    
} catch (Exception $e) {
    echo "✗ 查询登录记录失败: " . $e->getMessage() . "\n";
}

// 按用户统计
echo "\n3. 用户登录统计 (前10名):\n";
try {
    $stmt = $pdo->query("
        SELECT 
            l.user_id,
            u.username,
            u.quwan_id,
            COUNT(*) as login_count,
            COUNT(DISTINCT l.ip_address) as unique_ips,
            MAX(l.login_time) as last_login,
            MIN(l.login_time) as first_login
        FROM login_logs l
        LEFT JOIN users u ON l.user_id = u.id
        WHERE l.status = 'success'
        GROUP BY l.user_id
        ORDER BY login_count DESC
        LIMIT 10
    ");
    
    $stats = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (empty($stats)) {
        echo "  暂无统计数据\n";
    } else {
        foreach ($stats as $stat) {
            echo sprintf(
                "  用户%d(%s/%s): %d次登录, %d个IP, 最后登录: %s\n",
                $stat['user_id'],
                $stat['username'] ?? '未知',
                $stat['quwan_id'] ?? '未知',
                $stat['login_count'],
                $stat['unique_ips'],
                $stat['last_login']
            );
        }
    }
    
} catch (Exception $e) {
    echo "✗ 查询统计数据失败: " . $e->getMessage() . "\n";
}

// 按登录类型统计
echo "\n4. 登录类型统计:\n";
try {
    $stmt = $pdo->query("
        SELECT 
            login_type,
            COUNT(*) as count,
            COUNT(DISTINCT user_id) as unique_users
        FROM login_logs 
        WHERE status = 'success'
        GROUP BY login_type
        ORDER BY count DESC
    ");
    
    $types = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (empty($types)) {
        echo "  暂无数据\n";
    } else {
        foreach ($types as $type) {
            echo sprintf(
                "  %s: %d次 (%d个用户)\n",
                $type['login_type'],
                $type['count'],
                $type['unique_users']
            );
        }
    }
    
} catch (Exception $e) {
    echo "✗ 查询登录类型统计失败: " . $e->getMessage() . "\n";
}

// 测试login_logger.php功能
echo "\n5. 测试login_logger.php功能:\n";
try {
    require_once 'login_logger.php';
    
    // 测试函数是否存在
    if (function_exists('recordUserLoginLog')) {
        echo "✓ recordUserLoginLog函数存在\n";
        
        // 测试记录一条日志（使用用户ID 1）
        $test_user_id = 1;
        $result = recordUserLoginLog($pdo, $test_user_id, 'test_login', 'success');
        
        if ($result) {
            echo "✓ 测试记录登录日志成功\n";
            
            // 查询刚刚插入的记录
            $stmt = $pdo->prepare("
                SELECT * FROM login_logs 
                WHERE user_id = ? AND login_type = 'test_login' 
                ORDER BY login_time DESC 
                LIMIT 1
            ");
            $stmt->execute([$test_user_id]);
            $test_record = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if ($test_record) {
                echo "✓ 测试记录已成功插入数据库\n";
                echo "  记录详情: ID={$test_record['id']}, IP={$test_record['ip_address']}, 位置={$test_record['location']}\n";
                
                // 删除测试记录
                $stmt = $pdo->prepare("DELETE FROM login_logs WHERE id = ?");
                $stmt->execute([$test_record['id']]);
                echo "✓ 测试记录已清理\n";
            } else {
                echo "✗ 未找到测试记录\n";
            }
        } else {
            echo "✗ 测试记录登录日志失败\n";
        }
    } else {
        echo "✗ recordUserLoginLog函数不存在\n";
    }
    
} catch (Exception $e) {
    echo "✗ 测试login_logger.php失败: " . $e->getMessage() . "\n";
}

echo "\n=== 测试完成 ===\n";
echo "\n如果看到登录记录，说明前台登录功能正常。\n";
echo "如果没有记录，请检查:\n";
echo "1. 用户是否实际进行了登录操作\n";
echo "2. 前台登录API是否正确调用了记录函数\n";
echo "3. 数据库权限是否正常\n";
echo "\n可以通过实际登录测试来验证功能。\n";
?>
