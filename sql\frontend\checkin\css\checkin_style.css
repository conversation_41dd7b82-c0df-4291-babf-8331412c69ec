/**
 * 每日签到页面样式
 * 精美、交互性强的设计
 */

/* ===== CSS变量定义 ===== */
:root {
    /* 主题色系 */
    --primary-color: #6F7BF5;
    --secondary-color: #AFFBF2;
    --accent-color: #40E0D0;
    --success-color: #4ECDC4;
    --warning-color: #FF8A65;
    --error-color: #FF5722;
    --gold-color: #FFD700;
    --orange-color: #FF8A65;
    
    /* 渐变色 */
    --primary-gradient: linear-gradient(135deg, #6F7BF5 0%, #40E0D0 100%);
    --success-gradient: linear-gradient(135deg, #4ECDC4 0%, #44A08D 100%);
    --gold-gradient: linear-gradient(135deg, #FFD700 0%, #FFA500 100%);
    --orange-gradient: linear-gradient(135deg, #FF8A65 0%, #FF7043 100%);
    
    /* 背景和文字 */
    --bg-primary: #F8F9FA;
    --bg-secondary: #FFFFFF;
    --bg-card: #FFFFFF;
    --text-primary: #2C3E50;
    --text-secondary: #7F8C8D;
    --text-light: #BDC3C7;
    --border-color: #E9ECEF;
    
    /* 阴影和圆角 */
    --shadow-light: 0 2px 8px rgba(0, 0, 0, 0.08);
    --shadow-medium: 0 4px 16px rgba(0, 0, 0, 0.12);
    --shadow-strong: 0 8px 32px rgba(0, 0, 0, 0.16);
    --shadow-colored: 0 8px 32px rgba(111, 123, 245, 0.2);
    --border-radius: 12px;
    --border-radius-large: 16px;
    --border-radius-xl: 24px;
    
    /* 动画 */
    --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    --transition-fast: all 0.2s ease;
    --transition-slow: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
}

/* ===== 全局样式 ===== */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
    background: var(--bg-primary);
    color: var(--text-primary);
    line-height: 1.6;
    min-height: 100vh;
    padding-bottom: 80px;
}

/* ===== 顶部导航 ===== */
.checkin-header {
    position: sticky;
    top: 0;
    z-index: 100;
    background: var(--bg-secondary);
    border-bottom: 1px solid var(--border-color);
    backdrop-filter: blur(10px);
}

.header-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 16px 20px;
    max-width: 100%;
}

.back-btn, .history-btn {
    width: 40px;
    height: 40px;
    background: var(--bg-primary);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--text-secondary);
    transition: var(--transition-fast);
    border: 1px solid var(--border-color);
    text-decoration: none;
}

.back-btn:hover, .history-btn:hover {
    background: var(--primary-color);
    color: white;
    transform: scale(1.05);
}

.header-title {
    font-size: 18px;
    font-weight: 600;
    color: var(--text-primary);
    text-align: center;
    flex: 1;
}

.header-actions {
    width: 40px;
    display: flex;
    justify-content: flex-end;
}

/* ===== 用户信息卡片 ===== */
.user-info-card {
    margin: 20px 16px;
    background: var(--bg-card);
    border-radius: var(--border-radius-xl);
    padding: 20px;
    box-shadow: var(--shadow-medium);
    border: 1px solid var(--border-color);
    display: flex;
    align-items: center;
    gap: 16px;
}

.user-avatar {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    overflow: hidden;
    border: 3px solid var(--primary-color);
    box-shadow: var(--shadow-colored);
}

.user-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.user-details {
    flex: 1;
}

.username {
    font-size: 18px;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 12px;
}

.user-stats {
    display: flex;
    gap: 24px;
}

.stat-item {
    text-align: center;
}

.stat-value {
    display: block;
    font-size: 20px;
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: 4px;
}

.stat-label {
    font-size: 12px;
    color: var(--text-secondary);
    font-weight: 500;
}

/* ===== 签到主区域 ===== */
.checkin-main {
    margin: 0 16px 24px;
}

.checkin-card {
    background: var(--bg-card);
    border-radius: var(--border-radius-xl);
    padding: 32px 24px;
    text-align: center;
    box-shadow: var(--shadow-medium);
    border: 1px solid var(--border-color);
    position: relative;
    overflow: hidden;
}

.checkin-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: var(--primary-gradient);
    opacity: 0.05;
    pointer-events: none;
}

.checkin-icon {
    width: 80px;
    height: 80px;
    margin: 0 auto 20px;
    background: var(--primary-gradient);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 36px;
    color: white;
    box-shadow: var(--shadow-colored);
    position: relative;
    z-index: 2;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.05); }
}

.checkin-content {
    margin-bottom: 24px;
    position: relative;
    z-index: 2;
}

.checkin-title {
    font-size: 24px;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: 8px;
}

.checkin-desc {
    font-size: 14px;
    color: var(--text-secondary);
    margin-bottom: 16px;
}

.checkin-reward {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    margin-bottom: 8px;
}

.reward-text {
    font-size: 14px;
    color: var(--text-secondary);
}

.reward-points {
    font-size: 18px;
    font-weight: 700;
    color: var(--success-color);
}

.bonus-reward {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    padding: 8px 16px;
    background: rgba(255, 138, 101, 0.1);
    border-radius: var(--border-radius);
    margin: 0 auto;
    width: fit-content;
}

.bonus-text {
    font-size: 12px;
    color: var(--orange-color);
}

.bonus-points {
    font-size: 16px;
    font-weight: 700;
    color: var(--orange-color);
}

/* ===== 签到按钮 ===== */
.checkin-btn {
    width: 100%;
    max-width: 200px;
    padding: 16px 24px;
    background: var(--primary-gradient);
    color: white;
    border: none;
    border-radius: var(--border-radius-xl);
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition);
    box-shadow: var(--shadow-colored);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    margin: 0 auto;
    position: relative;
    z-index: 2;
    overflow: hidden;
}

.checkin-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
    transition: left 0.6s;
}

.checkin-btn:hover::before {
    left: 100%;
}

.checkin-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 12px 40px rgba(111, 123, 245, 0.3);
}

.checkin-btn:active {
    transform: translateY(0);
}

.checkin-btn.checked {
    background: var(--success-gradient);
    cursor: not-allowed;
    opacity: 0.8;
}

.checkin-btn.checked:hover {
    transform: none;
    box-shadow: var(--shadow-medium);
}

/* ===== 签到日历 ===== */
.checkin-calendar {
    margin: 0 16px 24px;
    background: var(--bg-card);
    border-radius: var(--border-radius-xl);
    padding: 20px;
    box-shadow: var(--shadow-medium);
    border: 1px solid var(--border-color);
}

.calendar-header {
    margin-bottom: 16px;
    text-align: center;
}

.calendar-title {
    font-size: 16px;
    font-weight: 600;
    color: var(--text-primary);
}

.calendar-grid {
    display: grid;
    grid-template-columns: repeat(7, 1fr);
    gap: 8px;
}

.calendar-day {
    aspect-ratio: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    border-radius: var(--border-radius);
    font-size: 12px;
    font-weight: 500;
    position: relative;
    transition: var(--transition-fast);
    background: var(--bg-primary);
    border: 1px solid transparent;
}

.calendar-day.empty {
    background: transparent;
    border: none;
}

.calendar-day.today {
    border-color: var(--primary-color);
    background: rgba(111, 123, 245, 0.1);
    color: var(--primary-color);
    font-weight: 600;
}

.calendar-day.checked {
    background: var(--success-gradient);
    color: white;
    box-shadow: var(--shadow-light);
}

.calendar-day.future {
    color: var(--text-light);
    opacity: 0.5;
}

.calendar-day .day-number {
    margin-bottom: 2px;
}

.calendar-day i {
    font-size: 10px;
}

/* ===== 奖励规则 ===== */
.reward-rules {
    margin: 0 16px 24px;
    background: var(--bg-card);
    border-radius: var(--border-radius-xl);
    padding: 20px;
    box-shadow: var(--shadow-medium);
    border: 1px solid var(--border-color);
}

.rules-header {
    margin-bottom: 16px;
    text-align: center;
}

.rules-title {
    font-size: 16px;
    font-weight: 600;
    color: var(--text-primary);
}

.rules-list {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.rule-item {
    display: flex;
    align-items: center;
    padding: 16px;
    background: var(--bg-primary);
    border-radius: var(--border-radius);
    transition: var(--transition-fast);
}

.rule-item:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-light);
}

.rule-icon {
    width: 48px;
    height: 48px;
    border-radius: var(--border-radius);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 16px;
    color: white;
    font-size: 20px;
}

.rule-item:nth-child(1) .rule-icon {
    background: var(--success-gradient);
}

.rule-item:nth-child(2) .rule-icon {
    background: var(--orange-gradient);
}

.rule-item:nth-child(3) .rule-icon {
    background: var(--gold-gradient);
}

.rule-content {
    flex: 1;
}

.rule-title {
    font-size: 14px;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 4px;
}

.rule-desc {
    font-size: 12px;
    color: var(--text-secondary);
}

/* ===== 成功弹窗 ===== */
.success-modal {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10000;
    animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

.modal-content {
    background: var(--bg-card);
    border-radius: var(--border-radius-xl);
    padding: 32px 24px;
    text-align: center;
    max-width: 90%;
    width: 300px;
    box-shadow: var(--shadow-strong);
    position: relative;
    animation: slideUp 0.3s ease;
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.success-icon {
    width: 80px;
    height: 80px;
    margin: 0 auto 20px;
    background: var(--success-gradient);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 36px;
    color: white;
    animation: bounceIn 0.6s ease;
}

@keyframes bounceIn {
    0% { transform: scale(0.3); }
    50% { transform: scale(1.05); }
    70% { transform: scale(0.9); }
    100% { transform: scale(1); }
}

.success-title {
    font-size: 20px;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: 16px;
}

.success-reward {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    margin-bottom: 20px;
}

.success-text {
    font-size: 14px;
    color: var(--text-secondary);
}

.success-points {
    font-size: 24px;
    font-weight: 700;
    color: var(--success-color);
}

.close-modal-btn {
    position: absolute;
    top: 16px;
    right: 16px;
    width: 32px;
    height: 32px;
    background: var(--bg-primary);
    border: none;
    border-radius: 50%;
    color: var(--text-secondary);
    cursor: pointer;
    transition: var(--transition-fast);
    display: flex;
    align-items: center;
    justify-content: center;
}

.close-modal-btn:hover {
    background: var(--error-color);
    color: white;
}

/* ===== 响应式设计 ===== */
@media (max-width: 480px) {
    .header-content {
        padding: 12px 16px;
    }
    
    .user-info-card {
        margin: 16px 12px;
        padding: 16px;
    }
    
    .user-avatar {
        width: 50px;
        height: 50px;
    }
    
    .username {
        font-size: 16px;
    }
    
    .checkin-main {
        margin: 0 12px 20px;
    }
    
    .checkin-card {
        padding: 24px 20px;
    }
    
    .checkin-icon {
        width: 60px;
        height: 60px;
        font-size: 28px;
    }
    
    .checkin-title {
        font-size: 20px;
    }
    
    .calendar-grid {
        gap: 6px;
    }
    
    .calendar-day {
        font-size: 11px;
    }
}
