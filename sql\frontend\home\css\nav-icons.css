/* 底部导航栏图标样式 */

/* 通用样式 */
.bottom-nav {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background-color: white;
    display: flex;
    justify-content: space-around;
    padding: 8px 0;
    box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.05);
    z-index: 100;
}

.nav-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 5px 0;
    width: 20%;
    position: relative;
    text-decoration: none;
}

.nav-item span {
    font-size: 12px;
    color: #999;
    transition: color 0.3s ease;
    margin-top: 5px;
}

/* 通用图标容器 */
.nav-icon {
    position: relative;
    width: 24px;
    height: 24px;
}

/* 首页图标 - 未选中 */
.home-icon .nav-icon-inactive {
    position: absolute;
    width: 20px;
    height: 20px;
    left: 8.33%;
    right: 8.33%;
    top: 8.33%;
    bottom: 8.33%;
    background: rgb(0, 0, 0);
    -webkit-mask-size: contain;
    mask-size: contain;
    -webkit-mask-repeat: no-repeat;
    mask-repeat: no-repeat;
    -webkit-mask-position: center;
    mask-position: center;
    transition: opacity 0.3s ease;
}

/* 首页图标 - 选中 */
.home-icon .nav-icon-active {
    position: absolute;
    width: 18px;
    height: 18px;
    left: 12.5%;
    right: 12.5%;
    top: 12.5%;
    bottom: 12.5%;
    background: linear-gradient(180deg, rgb(123, 104, 238), rgb(85, 228, 245) 100%);
    -webkit-mask-size: contain;
    mask-size: contain;
    -webkit-mask-repeat: no-repeat;
    mask-repeat: no-repeat;
    -webkit-mask-position: center;
    mask-position: center;
    opacity: 0;
    transition: opacity 0.3s ease;
}

/* 攻略图标 - 未选中 */
.guide-icon .nav-icon-inactive {
    position: absolute;
    width: 22.01px;
    height: 22px;
    left: 4.17%;
    right: 4.13%;
    top: 4.17%;
    bottom: 4.17%;
    background: rgb(0, 0, 0);
    -webkit-mask-size: contain;
    mask-size: contain;
    -webkit-mask-repeat: no-repeat;
    mask-repeat: no-repeat;
    -webkit-mask-position: center;
    mask-position: center;
    transition: opacity 0.3s ease;
}

/* 攻略图标 - 选中 */
.guide-icon .nav-icon-active {
    position: absolute;
    width: 22px;
    height: 22px;
    left: 4.55%;
    right: 4.55%;
    top: 4.55%;
    bottom: 4.55%;
    background: linear-gradient(180deg, rgb(123, 104, 238), rgb(85, 228, 245) 100%);
    -webkit-mask-size: contain;
    mask-size: contain;
    -webkit-mask-repeat: no-repeat;
    mask-repeat: no-repeat;
    -webkit-mask-position: center;
    mask-position: center;
    opacity: 0;
    transition: opacity 0.3s ease;
}

/* 组局图标 - 未选中 */
.activity-icon .nav-icon-inactive {
    position: absolute;
    width: 20px;
    height: 22px;
    left: 8.33%;
    right: 8.33%;
    top: 4.17%;
    bottom: 4.17%;
    background: rgb(0, 0, 0);
    -webkit-mask-size: contain;
    mask-size: contain;
    -webkit-mask-repeat: no-repeat;
    mask-repeat: no-repeat;
    -webkit-mask-position: center;
    mask-position: center;
    transition: opacity 0.3s ease;
}

/* 组局图标 - 选中 */
.activity-icon .nav-icon-active {
    position: absolute;
    width: 18px;
    height: 20px;
    left: 12.5%;
    right: 12.5%;
    top: 8.33%;
    bottom: 8.33%;
    background: linear-gradient(180deg, rgb(123, 104, 238), rgb(85, 228, 245) 100%);
    -webkit-mask-size: contain;
    mask-size: contain;
    -webkit-mask-repeat: no-repeat;
    mask-repeat: no-repeat;
    -webkit-mask-position: center;
    mask-position: center;
    opacity: 0;
    transition: opacity 0.3s ease;
}

/* 消息图标 - 未选中 */
.message-icon .nav-icon-inactive {
    position: absolute;
    width: 24px;
    height: 24px;
    left: 0;
    right: 0;
    top: 0;
    bottom: 0;
    background: rgb(0, 0, 0);
    -webkit-mask-size: contain;
    mask-size: contain;
    -webkit-mask-repeat: no-repeat;
    mask-repeat: no-repeat;
    -webkit-mask-position: center;
    mask-position: center;
    transition: opacity 0.3s ease;
}

/* 消息图标 - 选中 */
.message-icon .nav-icon-active {
    position: absolute;
    width: 24px;
    height: 24px;
    left: 0;
    right: 0;
    top: 0;
    bottom: 0;
    background: linear-gradient(180deg, rgb(123, 104, 238), rgb(85, 228, 245) 100%);
    -webkit-mask-size: contain;
    mask-size: contain;
    -webkit-mask-repeat: no-repeat;
    mask-repeat: no-repeat;
    -webkit-mask-position: center;
    mask-position: center;
    opacity: 0;
    transition: opacity 0.3s ease;
}

/* 我的图标 - 未选中 */
.profile-icon .nav-icon-inactive {
    position: absolute;
    width: 24px;
    height: 24px;
    left: 0;
    right: 0;
    top: 0;
    bottom: 0;
    background: rgb(0, 0, 0);
    -webkit-mask-size: contain;
    mask-size: contain;
    -webkit-mask-repeat: no-repeat;
    mask-repeat: no-repeat;
    -webkit-mask-position: center;
    mask-position: center;
    transition: opacity 0.3s ease;
}

/* 我的图标 - 选中 */
.profile-icon .nav-icon-active {
    position: absolute;
    width: 20px;
    height: 20px;
    left: 8.33%;
    right: 8.33%;
    top: 8.33%;
    bottom: 8.33%;
    background: linear-gradient(180deg, rgb(123, 104, 238), rgb(85, 228, 245) 100%);
    -webkit-mask-size: contain;
    mask-size: contain;
    -webkit-mask-repeat: no-repeat;
    mask-repeat: no-repeat;
    -webkit-mask-position: center;
    mask-position: center;
    opacity: 0;
    transition: opacity 0.3s ease;
}

/* 选中状态样式 */
.nav-item.active .nav-icon-active {
    opacity: 1;
}

.nav-item.active .nav-icon-inactive {
    opacity: 0;
}

.nav-item.active span {
    color: rgb(123, 104, 238);
}

/* 首页图标 */
.home-icon .nav-icon-inactive,
.home-icon .nav-icon-active {
    -webkit-mask-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M3 9l9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z"></path><polyline points="9 22 9 12 15 12 15 22"></polyline></svg>');
    mask-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M3 9l9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z"></path><polyline points="9 22 9 12 15 12 15 22"></polyline></svg>');
}

/* 攻略图标 */
.guide-icon .nav-icon-inactive,
.guide-icon .nav-icon-active {
    -webkit-mask-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M2 3h6a4 4 0 0 1 4 4v14a3 3 0 0 0-3-3H2z"></path><path d="M22 3h-6a4 4 0 0 0-4 4v14a3 3 0 0 1 3-3h7z"></path></svg>');
    mask-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M2 3h6a4 4 0 0 1 4 4v14a3 3 0 0 0-3-3H2z"></path><path d="M22 3h-6a4 4 0 0 0-4 4v14a3 3 0 0 1 3-3h7z"></path></svg>');
}

/* 组局图标 */
.activity-icon .nav-icon-inactive,
.activity-icon .nav-icon-active {
    -webkit-mask-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><rect x="3" y="4" width="18" height="18" rx="2" ry="2"></rect><line x1="16" y1="2" x2="16" y2="6"></line><line x1="8" y1="2" x2="8" y2="6"></line><line x1="3" y1="10" x2="21" y2="10"></line></svg>');
    mask-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><rect x="3" y="4" width="18" height="18" rx="2" ry="2"></rect><line x1="16" y1="2" x2="16" y2="6"></line><line x1="8" y1="2" x2="8" y2="6"></line><line x1="3" y1="10" x2="21" y2="10"></line></svg>');
}

/* 消息图标 */
.message-icon .nav-icon-inactive,
.message-icon .nav-icon-active {
    -webkit-mask-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M21 11.5a8.38 8.38 0 0 1-.9 3.8 8.5 8.5 0 0 1-7.6 4.7 8.38 8.38 0 0 1-3.8-.9L3 21l1.9-5.7a8.38 8.38 0 0 1-.9-3.8 8.5 8.5 0 0 1 4.7-7.6 8.38 8.38 0 0 1 3.8-.9h.5a8.48 8.48 0 0 1 8 8v.5z"></path></svg>');
    mask-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M21 11.5a8.38 8.38 0 0 1-.9 3.8 8.5 8.5 0 0 1-7.6 4.7 8.38 8.38 0 0 1-3.8-.9L3 21l1.9-5.7a8.38 8.38 0 0 1-.9-3.8 8.5 8.5 0 0 1 4.7-7.6 8.38 8.38 0 0 1 3.8-.9h.5a8.48 8.48 0 0 1 8 8v.5z"></path></svg>');
}

/* 我的图标 */
.profile-icon .nav-icon-inactive,
.profile-icon .nav-icon-active {
    -webkit-mask-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"></path><circle cx="12" cy="7" r="4"></circle></svg>');
    mask-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"></path><circle cx="12" cy="7" r="4"></circle></svg>');
}
