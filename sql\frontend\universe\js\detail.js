document.addEventListener('DOMContentLoaded', function() {
    // 获取页面元素
    const likeButton = document.getElementById('like-button');
    const favoriteButton = document.getElementById('favorite-button');
    const commentButton = document.getElementById('comment-button');
    const commentInput = document.getElementById('comment-input');
    const sendCommentButton = document.getElementById('send-comment');
    const shareIcon = document.querySelector('.share-icon');
    
    // 获取内容ID
    const postId = new URLSearchParams(window.location.search).get('id');
    
    // 点赞功能
    if (likeButton) {
        likeButton.addEventListener('click', function() {
            const isLiked = this.classList.contains('active');
            const likeCount = this.querySelector('span');
            const likeIcon = this.querySelector('i');
            
            // 发送请求
            fetch('../api/universe/like.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: `post_id=${postId}&action=${isLiked ? 'unlike' : 'like'}`
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // 更新UI
                    if (isLiked) {
                        this.classList.remove('active');
                        likeIcon.className = 'far fa-heart';
                        likeCount.textContent = parseInt(likeCount.textContent) - 1;
                    } else {
                        this.classList.add('active');
                        likeIcon.className = 'fas fa-heart';
                        likeCount.textContent = parseInt(likeCount.textContent) + 1;
                    }
                } else {
                    showToast(data.message || '操作失败，请重试');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showToast('网络错误，请稍后再试');
            });
        });
    }
    
    // 收藏功能
    if (favoriteButton) {
        favoriteButton.addEventListener('click', function() {
            const isFavorited = this.classList.contains('active');
            const favoriteIcon = this.querySelector('i');
            
            // 发送请求
            fetch('../api/universe/favorite.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: `post_id=${postId}&action=${isFavorited ? 'unfavorite' : 'favorite'}`
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // 更新UI
                    if (isFavorited) {
                        this.classList.remove('active');
                        favoriteIcon.className = 'far fa-star';
                    } else {
                        this.classList.add('active');
                        favoriteIcon.className = 'fas fa-star';
                    }
                    
                    showToast(data.message || (isFavorited ? '已取消收藏' : '收藏成功'));
                } else {
                    showToast(data.message || '操作失败，请重试');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showToast('网络错误，请稍后再试');
            });
        });
    }
    
    // 评论功能
    if (commentButton) {
        commentButton.addEventListener('click', function() {
            // 聚焦评论输入框
            commentInput.focus();
        });
    }
    
    // 发送评论
    if (sendCommentButton && commentInput) {
        sendCommentButton.addEventListener('click', function() {
            const content = commentInput.value.trim();
            
            if (!content) {
                showToast('请输入评论内容');
                return;
            }
            
            // 发送请求
            fetch('../api/universe/comment.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: `post_id=${postId}&content=${encodeURIComponent(content)}&parent_id=0`
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // 清空输入框
                    commentInput.value = '';
                    
                    // 刷新页面显示新评论
                    showToast('评论成功');
                    setTimeout(() => {
                        location.reload();
                    }, 1500);
                } else {
                    showToast(data.message || '评论失败，请重试');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showToast('网络错误，请稍后再试');
            });
        });
    }
    
    // 回复评论
    const replyButtons = document.querySelectorAll('.reply-button');
    replyButtons.forEach(button => {
        button.addEventListener('click', function() {
            const commentId = this.dataset.id;
            const commentUser = this.closest('.comment-item').querySelector('.comment-user').textContent;
            
            // 聚焦评论输入框并添加回复前缀
            commentInput.focus();
            commentInput.value = `回复 ${commentUser}：`;
            
            // 保存回复的评论ID
            commentInput.dataset.replyTo = commentId;
            
            // 修改发送按钮文本
            sendCommentButton.textContent = '回复';
            
            // 添加取消回复的按钮
            if (!document.querySelector('.cancel-reply')) {
                const cancelButton = document.createElement('button');
                cancelButton.className = 'cancel-reply';
                cancelButton.textContent = '取消';
                cancelButton.addEventListener('click', function() {
                    // 清空输入框
                    commentInput.value = '';
                    delete commentInput.dataset.replyTo;
                    
                    // 恢复发送按钮文本
                    sendCommentButton.textContent = '发送';
                    
                    // 移除取消按钮
                    this.remove();
                });
                
                document.querySelector('.comment-input-container').insertBefore(
                    cancelButton,
                    sendCommentButton
                );
            }
        });
    });
    
    // 修改发送评论按钮的行为，支持回复
    if (sendCommentButton && commentInput) {
        const originalClickHandler = sendCommentButton.onclick;
        sendCommentButton.onclick = function() {
            const content = commentInput.value.trim();
            const parentId = commentInput.dataset.replyTo || 0;
            
            if (!content) {
                showToast('请输入评论内容');
                return;
            }
            
            // 发送请求
            fetch('../api/universe/comment.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: `post_id=${postId}&content=${encodeURIComponent(content)}&parent_id=${parentId}`
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // 清空输入框
                    commentInput.value = '';
                    delete commentInput.dataset.replyTo;
                    
                    // 恢复发送按钮文本
                    sendCommentButton.textContent = '发送';
                    
                    // 移除取消按钮
                    const cancelButton = document.querySelector('.cancel-reply');
                    if (cancelButton) {
                        cancelButton.remove();
                    }
                    
                    // 刷新页面显示新评论
                    showToast(parentId > 0 ? '回复成功' : '评论成功');
                    setTimeout(() => {
                        location.reload();
                    }, 1500);
                } else {
                    showToast(data.message || '操作失败，请重试');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showToast('网络错误，请稍后再试');
            });
        };
    }
    
    // 分享功能
    if (shareIcon) {
        shareIcon.addEventListener('click', function() {
            // 获取当前页面URL
            const url = window.location.href;
            const title = document.querySelector('.post-title').textContent;
            
            // 尝试使用Web Share API
            if (navigator.share) {
                navigator.share({
                    title: title,
                    url: url
                })
                .catch(error => {
                    console.error('Error sharing:', error);
                    // 如果分享失败，复制链接
                    copyToClipboard(url);
                });
            } else {
                // 不支持Web Share API，复制链接
                copyToClipboard(url);
            }
        });
    }
    
    // 复制到剪贴板
    function copyToClipboard(text) {
        const textarea = document.createElement('textarea');
        textarea.value = text;
        textarea.style.position = 'fixed';
        textarea.style.opacity = 0;
        document.body.appendChild(textarea);
        textarea.select();
        
        try {
            document.execCommand('copy');
            showToast('链接已复制，快去分享吧');
        } catch (err) {
            console.error('Failed to copy:', err);
            showToast('复制失败，请手动复制链接');
        }
        
        document.body.removeChild(textarea);
    }
    
    // Toast提示函数
    function showToast(message) {
        const toast = document.getElementById('toast');
        toast.textContent = message;
        toast.style.display = 'block';
        
        // 添加动画
        toast.style.animation = 'fadeIn 0.3s forwards';
        
        // 3秒后隐藏
        setTimeout(() => {
            toast.style.animation = 'fadeOut 0.3s forwards';
            setTimeout(() => {
                toast.style.display = 'none';
            }, 300);
        }, 3000);
    }
    
    // 确保window.showToast也可用
    window.showToast = showToast;
});
