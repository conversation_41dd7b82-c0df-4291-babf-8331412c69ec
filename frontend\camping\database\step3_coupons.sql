-- 第三步：创建优惠券相关表
-- 请在宝塔数据库中执行以下SQL语句

-- 3. 露营活动优惠券表
CREATE TABLE IF NOT EXISTS `camping_coupons` (
    `id` INT(11) NOT NULL AUTO_INCREMENT COMMENT '优惠券ID',
    `title` VARCHAR(100) NOT NULL COMMENT '优惠券标题',
    `description` VARCHAR(200) COMMENT '优惠券描述',
    `type` ENUM('join_discount', 'organize_discount', 'newbie_discount') NOT NULL COMMENT '优惠券类型',
    `discount_amount` DECIMAL(10,2) NOT NULL COMMENT '优惠金额',
    `min_amount` DECIMAL(10,2) NOT NULL DEFAULT 0.00 COMMENT '最低消费金额',
    `total_quantity` INT(11) NOT NULL COMMENT '总发放数量',
    `claimed_quantity` INT(11) NOT NULL DEFAULT 0 COMMENT '已领取数量',
    `valid_from` DATETIME NOT NULL COMMENT '有效期开始时间',
    `valid_until` DATETIME NOT NULL COMMENT '有效期结束时间',
    `status` ENUM('active', 'inactive', 'expired') NOT NULL DEFAULT 'active' COMMENT '优惠券状态',
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    KEY `idx_type` (`type`),
    KEY `idx_status` (`status`),
    KEY `idx_valid_period` (`valid_from`, `valid_until`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='露营活动优惠券表';

-- 4. 用户优惠券领取记录表
CREATE TABLE IF NOT EXISTS `user_camping_coupons` (
    `id` INT(11) NOT NULL AUTO_INCREMENT COMMENT '记录ID',
    `user_id` INT(11) NOT NULL COMMENT '用户ID',
    `coupon_id` INT(11) NOT NULL COMMENT '优惠券ID',
    `claimed_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '领取时间',
    `used_at` TIMESTAMP NULL COMMENT '使用时间',
    `used_activity_id` INT(11) DEFAULT NULL COMMENT '使用的活动ID',
    `status` ENUM('claimed', 'used', 'expired') NOT NULL DEFAULT 'claimed' COMMENT '优惠券状态',
    PRIMARY KEY (`id`),
    UNIQUE KEY `unique_user_coupon` (`user_id`, `coupon_id`),
    KEY `idx_user` (`user_id`),
    KEY `idx_coupon` (`coupon_id`),
    KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户优惠券领取记录表';
