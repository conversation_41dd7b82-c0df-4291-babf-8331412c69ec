<?php
/**
 * 用户管理页面
 * 趣玩星球管理后台 v3.0
 */

session_start();

// 简单的登录检查
if (!isset($_SESSION['admin_id'])) {
    header('Location: ../login.php');
    exit;
}

$admin_name = $_SESSION['admin_name'] ?? '管理员';
$admin_employee_id = $_SESSION['admin_employee_id'] ?? '12001';

// 获取搜索参数
$search = trim($_GET['search'] ?? '');
$status_filter = $_GET['status'] ?? 'all';
$show_results = !empty($search);

// 如果有搜索条件，尝试连接数据库
$users = [];
$total = 0;
$error_message = '';

if ($show_results) {
    try {
        require_once '../db_config.php';
        $pdo = getDbConnection();

        // 构建查询
        $where_conditions = [];
        $params = [];

        // 搜索条件
        $where_conditions[] = "(u.username LIKE ? OR u.phone LIKE ? OR u.email LIKE ? OR u.quwan_id LIKE ? OR u.id = ?)";
        $search_param = "%$search%";
        $params = [$search_param, $search_param, $search_param, $search_param, $search];

        // 状态筛选
        if ($status_filter !== 'all') {
            if ($status_filter === 'verified') {
                $where_conditions[] = "u.is_verified = 1";
            } elseif ($status_filter === 'unverified') {
                $where_conditions[] = "(u.is_verified = 0 OR u.is_verified IS NULL)";
            }
        }

        $where_clause = 'WHERE ' . implode(' AND ', $where_conditions);

        // 获取用户列表
        $sql = "
            SELECT
                u.*,
                rv.verification_status,
                rv.real_name,
                rv.verified_at
            FROM users u
            LEFT JOIN realname_verification rv ON u.id = rv.user_id
            $where_clause
            ORDER BY u.created_at DESC
            LIMIT 50
        ";

        $stmt = $pdo->prepare($sql);
        $stmt->execute($params);
        $users = $stmt->fetchAll(PDO::FETCH_ASSOC);
        $total = count($users);

    } catch (Exception $e) {
        $error_message = '数据库查询错误：' . $e->getMessage();
    }
}

// 状态标签函数
function getVerificationBadge($user) {
    if ($user['verification_status'] === 'approved') {
        return '<span class="status-badge active"><i class="fas fa-check-circle"></i> 已认证</span>';
    } elseif ($user['verification_status'] === 'pending') {
        return '<span class="status-badge pending"><i class="fas fa-clock"></i> 审核中</span>';
    } elseif ($user['verification_status'] === 'rejected') {
        return '<span class="status-badge error"><i class="fas fa-times-circle"></i> 已拒绝</span>';
    } else {
        return '<span class="status-badge inactive"><i class="fas fa-user"></i> 未认证</span>';
    }
}
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>用户管理 - 趣玩星球管理后台</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="../assets/css/admin-layout.css">
    <link rel="stylesheet" href="../assets/css/modern-components.css">
    <style>

        /* 用户管理页面特定样式 */
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: var(--spacing-xl);
            margin-bottom: var(--spacing-2xl);
        }

        .stat-card {
            background: var(--white);
            border-radius: var(--border-radius-xl);
            padding: var(--spacing-xl);
            box-shadow: var(--shadow);
            border: 1px solid var(--gray-200);
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }

        .stat-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: var(--primary-gradient);
        }

        .stat-card:hover {
            transform: translateY(-4px);
            box-shadow: var(--shadow-lg);
        }

        .stat-card-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: var(--spacing-md);
        }

        .stat-card-icon {
            width: 48px;
            height: 48px;
            background: var(--primary-gradient);
            border-radius: var(--border-radius-lg);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 20px;
            box-shadow: var(--shadow-primary);
        }

        .stat-card-value {
            font-size: var(--font-size-3xl);
            font-weight: 800;
            color: var(--gray-900);
            margin-bottom: var(--spacing-xs);
        }

        .stat-card-label {
            font-size: var(--font-size-sm);
            color: var(--gray-600);
            font-weight: 600;
        }

        .search-card {
            background: var(--white);
            border-radius: var(--border-radius-xl);
            padding: var(--spacing-2xl);
            box-shadow: var(--shadow);
            border: 1px solid var(--gray-200);
            margin-bottom: var(--spacing-2xl);
        }

        .search-form {
            display: grid;
            grid-template-columns: 2fr 1fr auto;
            gap: var(--spacing-lg);
            align-items: end;
        }

        .btn-group {
            display: flex;
            flex-direction: column;
            gap: var(--spacing-sm);
            min-width: 120px;
        }

        .user-avatar {
            width: 48px;
            height: 48px;
            background: var(--primary-gradient);
            border-radius: var(--border-radius-lg);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: 700;
            font-size: 16px;
            box-shadow: var(--shadow-primary);
            transition: var(--transition);
        }

        .user-avatar:hover {
            transform: scale(1.1);
        }

        .user-info {
            display: flex;
            align-items: center;
            gap: var(--spacing-md);
        }

        .user-details {
            display: flex;
            flex-direction: column;
            gap: var(--spacing-xs);
        }

        .user-name {
            font-weight: 700;
            color: var(--gray-900);
            font-size: var(--font-size-base);
        }

        .user-id {
            font-size: var(--font-size-sm);
            color: var(--gray-500);
            font-weight: 500;
        }

        .quwanplanet-id {
            font-weight: 700;
            color: var(--primary-color);
            font-size: var(--font-size-lg);
        }

        /* 响应式设计优化 */
        @media (max-width: 768px) {
            .search-form {
                grid-template-columns: 1fr;
                gap: var(--spacing-md);
            }

            .btn-group {
                flex-direction: column;
                width: 100%;
            }

            .stats-grid {
                grid-template-columns: repeat(2, 1fr);
                gap: var(--spacing-md);
            }
        }

        /* 清理完成 - 使用现代化组件样式 */

        /* 页面特定样式 - 使用现代化组件系统 */
        .error-message {
            background: rgba(239, 68, 68, 0.1);
            color: var(--error-color);
            padding: var(--spacing-lg);
            border-radius: var(--border-radius-lg);
            margin-bottom: var(--spacing-lg);
            display: flex;
            align-items: center;
            gap: var(--spacing-md);
            border: 1px solid rgba(239, 68, 68, 0.2);
            box-shadow: var(--shadow-sm);
        }

        /* 用户信息样式已迁移到组件系统 */

        /* 页面重构完成 - 使用现代化组件系统 */
    </style>
</head>
<body>
    <div class="admin-layout">
        <!-- 引入侧边栏 -->
        <?php include '../includes/sidebar.php'; ?>

        <!-- 主内容区域 -->
        <div class="main-content">
            <!-- 页面头部 -->
            <div class="page-header">
                <div class="page-header-content">
                    <div class="page-title">
                        <div class="page-title-icon">
                            <i class="fas fa-users-cog"></i>
                        </div>
                        <div>
                            <h1>用户管理</h1>
                            <p class="page-subtitle">智能用户查询与管理系统</p>
                        </div>
                    </div>
                    <div class="page-actions">
                        <button class="btn btn-secondary" onclick="window.location.reload()">
                            <i class="fas fa-sync-alt"></i>
                            刷新页面
                        </button>
                    </div>
                </div>
            </div>

            <!-- 页面内容 -->
            <div class="page-content">
                <!-- 搜索卡片 -->
                <div class="card search-card">
                    <div class="card-header">
                        <h3 class="card-title">
                            <i class="fas fa-search"></i>
                            智能用户查询系统
                        </h3>
                        <p class="card-subtitle">为保护用户隐私，请输入具体的搜索条件查询用户信息</p>
                    </div>
                    <div class="card-body">

                        <form method="GET" class="search-form">
                            <div class="form-group">
                                <label for="search" class="form-label">
                                    <i class="fas fa-keyboard"></i>
                                    搜索条件
                                </label>
                                <input type="text"
                                       id="search"
                                       name="search"
                                       class="form-control"
                                       value="<?php echo htmlspecialchars($search); ?>"
                                       placeholder="🔍 请输入用户名、手机号、邮箱、趣玩ID或用户ID"
                                       required>
                            </div>

                            <div class="form-group">
                                <label for="status" class="form-label">
                                    <i class="fas fa-filter"></i>
                                    认证状态
                                </label>
                                <select name="status" id="status" class="form-control">
                                    <option value="all" <?php echo $status_filter === 'all' ? 'selected' : ''; ?>>🌟 全部状态</option>
                                    <option value="verified" <?php echo $status_filter === 'verified' ? 'selected' : ''; ?>>✅ 已认证</option>
                                    <option value="unverified" <?php echo $status_filter === 'unverified' ? 'selected' : ''; ?>>⏳ 未认证</option>
                                </select>
                            </div>

                            <div class="btn-group">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-search"></i>
                                    查询用户
                                </button>
                                <button type="button" class="btn btn-secondary" onclick="resetSearch()">
                                    <i class="fas fa-undo"></i>
                                    重置
                                </button>
                            </div>
                        </form>
                    </div>
                </div>

                    <?php if ($error_message): ?>
                        <div class="error-message">
                            <i class="fas fa-exclamation-triangle"></i>
                            <?php echo htmlspecialchars($error_message); ?>
                        </div>
                    <?php endif; ?>

                <?php if ($show_results): ?>
                    <?php if (!empty($users)): ?>
                        <!-- 搜索结果卡片 -->
                        <div class="card">
                            <div class="card-header">
                                <h3 class="card-title">
                                    <i class="fas fa-list-ul"></i>
                                    搜索结果
                                </h3>
                                <p class="card-subtitle">找到 <?php echo $total; ?> 个用户</p>
                            </div>
                            <div class="card-body" style="padding: 0;">
                                <div class="table-container">
                                    <table class="table">
                                        <thead>
                                            <tr>
                                                <th><i class="fas fa-user"></i> 用户信息</th>
                                                <th><i class="fas fa-id-card"></i> 趣玩ID</th>
                                                <th><i class="fas fa-address-book"></i> 联系方式</th>
                                                <th><i class="fas fa-shield-check"></i> 认证状态</th>
                                                <th><i class="fas fa-calendar"></i> 注册时间</th>
                                                <th><i class="fas fa-cogs"></i> 操作</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php foreach ($users as $user): ?>
                                                <tr>
                                                    <td>
                                                        <div class="user-info">
                                                            <div class="user-avatar">
                                                                <?php echo mb_substr($user['username'], 0, 1, 'UTF-8'); ?>
                                                            </div>
                                                            <div class="user-details">
                                                                <div class="user-name"><?php echo htmlspecialchars($user['username']); ?></div>
                                                                <div class="user-id">ID: <?php echo $user['id']; ?></div>
                                                            </div>
                                                        </div>
                                                    </td>
                                                    <td>
                                                        <div class="quwanplanet-id">
                                                            <?php echo htmlspecialchars($user['quwan_id']); ?>
                                                        </div>
                                                    </td>
                                                    <td>
                                                        <div style="font-weight: 600; color: var(--gray-800);">
                                                            <?php echo htmlspecialchars($user['phone'] ?? '未绑定'); ?>
                                                        </div>
                                                        <div style="font-size: 12px; color: var(--gray-500);">
                                                            <?php echo htmlspecialchars($user['email'] ?? '未绑定'); ?>
                                                        </div>
                                                    </td>
                                                    <td><?php echo getVerificationBadge($user); ?></td>
                                                    <td>
                                                        <div style="font-weight: 600; color: var(--gray-800);">
                                                            <?php echo date('Y-m-d', strtotime($user['created_at'])); ?>
                                                        </div>
                                                        <div style="font-size: 12px; color: var(--gray-500);">
                                                            <?php echo date('H:i', strtotime($user['created_at'])); ?>
                                                        </div>
                                                    </td>
                                                    <td>
                                                        <a href="detail.php?id=<?php echo $user['id']; ?>" class="btn btn-primary btn-sm">
                                                            <i class="fas fa-eye"></i>
                                                            查看详情
                                                        </a>
                                                    </td>
                                                </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    <?php else: ?>
                        <!-- 无结果提示卡片 -->
                        <div class="card">
                            <div class="card-body" style="text-align: center; padding: var(--spacing-3xl);">
                                <div style="font-size: 4rem; color: var(--gray-400); margin-bottom: var(--spacing-lg);">
                                    <i class="fas fa-search-minus"></i>
                                </div>
                                <h3 style="color: var(--gray-700); margin-bottom: var(--spacing-md);">🔍 未找到匹配的用户</h3>
                                <p style="color: var(--gray-500);">请检查搜索条件是否正确，或尝试使用其他关键词</p>
                            </div>
                        </div>
                    <?php endif; ?>
                <?php else: ?>
                    <!-- 隐私保护系统介绍卡片 -->
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">
                                <i class="fas fa-shield-alt"></i>
                                用户隐私保护系统
                            </h3>
                            <p class="card-subtitle">为了保护用户隐私安全，系统采用按需查询模式</p>
                        </div>
                        <div class="card-body">
                            <div class="stats-grid">
                                <div class="stat-card">
                                    <div class="stat-card-header">
                                        <div class="stat-card-icon">
                                            <i class="fas fa-lock"></i>
                                        </div>
                                    </div>
                                    <div class="stat-card-value">🔒</div>
                                    <div class="stat-card-label">数据加密存储</div>
                                </div>
                                <div class="stat-card">
                                    <div class="stat-card-header">
                                        <div class="stat-card-icon">
                                            <i class="fas fa-eye-slash"></i>
                                        </div>
                                    </div>
                                    <div class="stat-card-value">👁️</div>
                                    <div class="stat-card-label">按需查询显示</div>
                                </div>
                                <div class="stat-card">
                                    <div class="stat-card-header">
                                        <div class="stat-card-icon">
                                            <i class="fas fa-user-shield"></i>
                                        </div>
                                    </div>
                                    <div class="stat-card-value">🛡️</div>
                                    <div class="stat-card-label">权限分级管理</div>
                                </div>
                                <div class="stat-card">
                                    <div class="stat-card-header">
                                        <div class="stat-card-icon">
                                            <i class="fas fa-history"></i>
                                        </div>
                                    </div>
                                    <div class="stat-card-value">📝</div>
                                    <div class="stat-card-label">操作日志记录</div>
                                </div>
                            </div>
                        </div>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- 引入管理后台布局脚本 -->
    <script src="../assets/js/admin-layout.js"></script>

    <script>
        function resetSearch() {
            document.getElementById('search').value = '';
            document.getElementById('status').value = 'all';
            window.location.href = 'index.php';
        }

        // 搜索表单验证和增强
        document.querySelector('form').addEventListener('submit', function(e) {
            const searchInput = document.getElementById('search');
            if (searchInput.value.trim().length < 2) {
                e.preventDefault();

                // 创建现代化提示
                const toast = document.createElement('div');
                toast.style.cssText = `
                    position: fixed;
                    top: 20px;
                    right: 20px;
                    background: linear-gradient(135deg, #EF4444, #DC2626);
                    color: white;
                    padding: 16px 24px;
                    border-radius: 12px;
                    box-shadow: 0 8px 32px rgba(239, 68, 68, 0.3);
                    z-index: 10000;
                    font-weight: 600;
                    transform: translateX(100%);
                    transition: transform 0.3s ease;
                `;
                toast.innerHTML = '<i class="fas fa-exclamation-triangle"></i> 请输入至少2个字符进行搜索';
                document.body.appendChild(toast);

                setTimeout(() => toast.style.transform = 'translateX(0)', 100);
                setTimeout(() => {
                    toast.style.transform = 'translateX(100%)';
                    setTimeout(() => document.body.removeChild(toast), 300);
                }, 3000);

                searchInput.focus();
                return false;
            }
        });

        // 添加搜索输入框的实时反馈
        document.getElementById('search').addEventListener('input', function(e) {
            const value = e.target.value.trim();
            const submitBtn = document.querySelector('button[type="submit"]');

            if (value.length >= 2) {
                submitBtn.style.background = 'var(--primary-gradient)';
                submitBtn.style.transform = 'scale(1.02)';
                submitBtn.style.boxShadow = 'var(--shadow-primary)';
            } else {
                submitBtn.style.background = 'var(--gray-200)';
                submitBtn.style.transform = 'scale(1)';
                submitBtn.style.boxShadow = 'var(--shadow)';
            }
        });

        // 表格行点击效果
        document.querySelectorAll('.table tbody tr').forEach(row => {
            row.addEventListener('click', function(e) {
                if (e.target.tagName !== 'A' && e.target.tagName !== 'BUTTON') {
                    const detailLink = this.querySelector('a[href*="detail.php"]');
                    if (detailLink) {
                        window.location.href = detailLink.href;
                    }
                }
            });
        });

        // 页面加载动画
        document.addEventListener('DOMContentLoaded', function() {
            const elements = document.querySelectorAll('.card, .page-header');
            elements.forEach((el, index) => {
                el.style.opacity = '0';
                el.style.transform = 'translateY(20px)';
                setTimeout(() => {
                    el.style.transition = 'all 0.6s ease';
                    el.style.opacity = '1';
                    el.style.transform = 'translateY(0)';
                }, index * 150);
            });
        });
    </script>
</body>
</html>
