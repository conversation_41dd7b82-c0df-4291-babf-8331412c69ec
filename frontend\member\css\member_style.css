/**
 * 会员中心样式 - 黑金主题
 * 年轻化、简洁、功能实用、UI美观
 */

/* ===== CSS变量定义 ===== */
:root {
    /* 黑金主题色系 */
    --primary-black: #1A1A1A;
    --secondary-black: #2D2D2D;
    --accent-black: #3A3A3A;
    --gold-primary: #FFD700;
    --gold-secondary: #FFA500;
    --gold-dark: #B8860B;
    --gold-gradient: linear-gradient(135deg, #FFD700 0%, #FFA500 100%);
    --black-gradient: linear-gradient(135deg, #1A1A1A 0%, #2D2D2D 100%);

    /* 状态色 */
    --success-color: #00C851;
    --warning-color: #FF8800;
    --error-color: #FF4444;
    --info-color: #33B5E5;

    /* 文字颜色 */
    --text-primary: #FFFFFF;
    --text-secondary: #CCCCCC;
    --text-muted: #999999;
    --text-dark: #1A1A1A;

    /* 背景色 */
    --bg-primary: #0F0F0F;
    --bg-secondary: #1A1A1A;
    --bg-card: #2D2D2D;
    --bg-overlay: rgba(0, 0, 0, 0.8);

    /* 边框和阴影 */
    --border-color: #3A3A3A;
    --shadow-gold: 0 4px 20px rgba(255, 215, 0, 0.3);
    --shadow-black: 0 4px 20px rgba(0, 0, 0, 0.5);
    --shadow-light: 0 2px 8px rgba(0, 0, 0, 0.3);

    /* 圆角 */
    --border-radius: 12px;
    --border-radius-large: 20px;
    --border-radius-xl: 24px;

    /* 动画 */
    --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    --transition-fast: all 0.2s ease;
}

/* ===== 全局样式 ===== */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
    background: var(--bg-primary);
    color: var(--text-primary);
    line-height: 1.6;
    min-height: 100vh;
    padding-bottom: 80px;
}

/* ===== 顶部导航 ===== */
.member-header {
    position: sticky;
    top: 0;
    z-index: 100;
    background: var(--black-gradient);
    border-bottom: 1px solid var(--border-color);
    backdrop-filter: blur(10px);
}

.header-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 16px 20px;
    max-width: 100%;
}

.back-btn, .help-btn {
    width: 40px;
    height: 40px;
    background: var(--bg-card);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--text-secondary);
    transition: var(--transition-fast);
    border: 1px solid var(--border-color);
}

.back-btn:hover, .help-btn:hover {
    background: var(--accent-black);
    color: var(--gold-primary);
    transform: scale(1.05);
}

.header-title {
    font-size: 18px;
    font-weight: 600;
    color: var(--text-primary);
    text-align: center;
    flex: 1;
}

.header-actions {
    width: 40px;
    display: flex;
    justify-content: flex-end;
}

/* ===== 会员状态卡片 ===== */
.member-status-card {
    position: relative;
    margin: 20px 16px;
    background: var(--bg-card);
    border-radius: var(--border-radius-xl);
    overflow: hidden;
    border: 1px solid var(--border-color);
}

.status-bg {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: var(--gold-gradient);
    opacity: 0.1;
}

.status-content {
    position: relative;
    padding: 24px;
    z-index: 2;
}

.user-info {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
}

.user-avatar {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    overflow: hidden;
    margin-right: 16px;
    border: 2px solid var(--gold-primary);
    box-shadow: var(--shadow-gold);
}

.user-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.user-details {
    flex: 1;
}

.username {
    font-size: 20px;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 8px;
}

.member-badge {
    display: flex;
    align-items: center;
    gap: 6px;
    padding: 6px 12px;
    border-radius: var(--border-radius);
    font-size: 14px;
    font-weight: 500;
}

.member-badge.active {
    background: var(--gold-gradient);
    color: var(--text-dark);
    box-shadow: var(--shadow-gold);
}

.member-badge.inactive {
    background: var(--accent-black);
    color: var(--text-muted);
    border: 1px solid var(--border-color);
}

.member-info {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 16px;
}

.expire-date, .member-points {
    text-align: center;
    padding: 12px;
    background: rgba(255, 215, 0, 0.1);
    border-radius: var(--border-radius);
    border: 1px solid rgba(255, 215, 0, 0.2);
}

.expire-date .label, .member-points .label {
    display: block;
    font-size: 12px;
    color: var(--text-muted);
    margin-bottom: 4px;
}

.expire-date .value, .member-points .value {
    font-size: 16px;
    font-weight: 600;
    color: var(--gold-primary);
}

.upgrade-section {
    text-align: center;
}

.upgrade-btn {
    background: var(--gold-gradient);
    color: var(--text-dark);
    border: none;
    padding: 12px 32px;
    border-radius: var(--border-radius-large);
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition-fast);
    box-shadow: var(--shadow-gold);
    display: flex;
    align-items: center;
    gap: 8px;
    margin: 0 auto;
}

.upgrade-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 24px rgba(255, 215, 0, 0.4);
}

/* ===== 区块标题 ===== */
.section-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin: 32px 16px 16px;
}

.section-title {
    font-size: 18px;
    font-weight: 600;
    color: var(--text-primary);
}

.section-subtitle {
    font-size: 12px;
    color: var(--text-muted);
    margin-left: 8px;
}

.view-all {
    font-size: 14px;
    color: var(--gold-primary);
    text-decoration: none;
    transition: var(--transition-fast);
}

.view-all:hover {
    color: var(--gold-secondary);
}

/* ===== 会员权益 ===== */
.benefits-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: 12px;
    margin: 0 16px;
}

.benefit-item {
    display: flex;
    align-items: center;
    padding: 16px;
    background: var(--bg-card);
    border-radius: var(--border-radius);
    border: 1px solid var(--border-color);
    transition: var(--transition-fast);
}

.benefit-item:hover {
    border-color: var(--gold-primary);
    box-shadow: var(--shadow-light);
}

.benefit-icon {
    width: 48px;
    height: 48px;
    background: var(--gold-gradient);
    border-radius: var(--border-radius);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 16px;
    color: var(--text-dark);
    font-size: 20px;
}

.benefit-content {
    flex: 1;
}

.benefit-title {
    font-size: 16px;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 4px;
}

.benefit-desc {
    font-size: 14px;
    color: var(--text-secondary);
}

.benefit-status {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 14px;
}

.benefit-status.active {
    background: var(--success-color);
    color: white;
}

.benefit-status.inactive {
    background: var(--accent-black);
    color: var(--text-muted);
    border: 1px solid var(--border-color);
}

/* ===== 会员套餐 ===== */
.packages-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: 16px;
    margin: 0 16px;
}

.package-item {
    position: relative;
    background: var(--bg-card);
    border-radius: var(--border-radius-large);
    padding: 24px;
    border: 1px solid var(--border-color);
    transition: var(--transition-fast);
}

.package-item:hover {
    border-color: var(--gold-primary);
    box-shadow: var(--shadow-gold);
}

.package-item.recommended {
    border-color: var(--gold-primary);
    box-shadow: var(--shadow-gold);
}

.recommend-badge {
    position: absolute;
    top: -8px;
    left: 50%;
    transform: translateX(-50%);
    background: var(--gold-gradient);
    color: var(--text-dark);
    padding: 4px 16px;
    border-radius: var(--border-radius);
    font-size: 12px;
    font-weight: 600;
}

.package-header {
    text-align: center;
    margin-bottom: 20px;
}

.package-name {
    font-size: 18px;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 8px;
}

.package-price {
    display: flex;
    align-items: baseline;
    justify-content: center;
    gap: 4px;
    margin-bottom: 8px;
}

.currency {
    font-size: 16px;
    color: var(--gold-primary);
}

.amount {
    font-size: 32px;
    font-weight: 700;
    color: var(--gold-primary);
}

.period {
    font-size: 14px;
    color: var(--text-secondary);
}

.package-save {
    font-size: 12px;
    color: var(--success-color);
    background: rgba(0, 200, 81, 0.1);
    padding: 4px 8px;
    border-radius: var(--border-radius);
    display: inline-block;
}

.package-features {
    margin-bottom: 20px;
}

.feature {
    padding: 8px 0;
    color: var(--text-secondary);
    font-size: 14px;
    border-bottom: 1px solid var(--border-color);
}

.feature:last-child {
    border-bottom: none;
}

.package-btn {
    width: 100%;
    padding: 12px;
    border-radius: var(--border-radius);
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition-fast);
    border: 1px solid var(--border-color);
    background: var(--bg-secondary);
    color: var(--text-primary);
}

.package-btn.primary {
    background: var(--gold-gradient);
    color: var(--text-dark);
    border-color: var(--gold-primary);
    box-shadow: var(--shadow-gold);
}

.package-btn:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-light);
}

.package-btn.primary:hover {
    box-shadow: 0 6px 24px rgba(255, 215, 0, 0.4);
}

/* ===== 开通会员弹窗 ===== */
.upgrade-modal {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: var(--bg-overlay);
    display: none;
    align-items: center;
    justify-content: center;
    z-index: 10000;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.upgrade-modal.show {
    opacity: 1;
}

.modal-content {
    background: var(--bg-card);
    border-radius: var(--border-radius-large);
    max-width: 90%;
    width: 400px;
    max-height: 80vh;
    overflow-y: auto;
    border: 1px solid var(--border-color);
    box-shadow: var(--shadow-black);
    transform: scale(0.9);
    transition: transform 0.3s ease;
}

.upgrade-modal.show .modal-content {
    transform: scale(1);
}

.modal-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 20px 24px;
    border-bottom: 1px solid var(--border-color);
}

.modal-header h3 {
    font-size: 18px;
    font-weight: 600;
    color: var(--text-primary);
}

.close-btn {
    width: 32px;
    height: 32px;
    background: var(--accent-black);
    border: none;
    border-radius: 50%;
    color: var(--text-secondary);
    cursor: pointer;
    transition: var(--transition-fast);
    display: flex;
    align-items: center;
    justify-content: center;
}

.close-btn:hover {
    background: var(--gold-primary);
    color: var(--text-dark);
}

.modal-body {
    padding: 24px;
    text-align: center;
}

.modal-body p {
    color: var(--text-secondary);
    margin-bottom: 20px;
}

/* ===== 会员记录 ===== */
.records-list {
    margin: 0 16px;
}

.record-item {
    display: flex;
    align-items: center;
    padding: 16px;
    background: var(--bg-card);
    border-radius: var(--border-radius);
    border: 1px solid var(--border-color);
    margin-bottom: 12px;
    transition: var(--transition-fast);
}

.record-item:hover {
    border-color: var(--gold-primary);
}

.record-icon {
    width: 40px;
    height: 40px;
    background: var(--gold-gradient);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 16px;
    color: var(--text-dark);
    font-size: 16px;
}

.record-content {
    flex: 1;
}

.record-title {
    font-size: 14px;
    font-weight: 500;
    color: var(--text-primary);
    margin-bottom: 4px;
}

.record-time {
    font-size: 12px;
    color: var(--text-muted);
}

.record-value {
    font-size: 14px;
    font-weight: 600;
    color: var(--success-color);
}

/* ===== 响应式设计 ===== */
@media (min-width: 768px) {
    .packages-grid {
        grid-template-columns: repeat(3, 1fr);
    }

    .benefits-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 480px) {
    .header-content {
        padding: 12px 16px;
    }

    .status-content {
        padding: 20px;
    }

    .user-avatar {
        width: 50px;
        height: 50px;
    }

    .username {
        font-size: 18px;
    }

    .modal-content {
        width: 95%;
        margin: 20px;
    }

    .modal-header, .modal-body {
        padding: 16px 20px;
    }
}
