<?php
/**
 * 获取用户画像分析数据API
 * 趣玩星球管理后台
 */

session_start();

// 检查管理员权限
if (!isset($_SESSION['admin_id'])) {
    echo json_encode(['success' => false, 'message' => '未授权访问']);
    exit;
}

require_once '../db_config.php';

$response = ['success' => false, 'message' => '', 'data' => []];

// 获取分析类型
$type = $_GET['type'] ?? '';

if (empty($type)) {
    $response['message'] = '缺少分析类型参数';
    echo json_encode($response);
    exit;
}

try {
    $pdo = getDbConnection();
    
    switch ($type) {
        case 'distribution':
            $response['data'] = getUserDistributionData($pdo);
            break;
        case 'activity':
            $response['data'] = getActivityData($pdo);
            break;
        case 'consumption':
            $response['data'] = getConsumptionData($pdo);
            break;
        case 'geographic':
            $response['data'] = getGeographicData($pdo);
            break;
        case 'device':
            $response['data'] = getDeviceData($pdo);
            break;
        case 'value':
            $response['data'] = getValueData($pdo);
            break;
        default:
            $response['message'] = '无效的分析类型';
            echo json_encode($response);
            exit;
    }
    
    $response['success'] = true;

} catch (PDOException $e) {
    $response['message'] = '数据库错误：' . $e->getMessage();
}

echo json_encode($response);

/**
 * 获取用户分布数据
 */
function getUserDistributionData($pdo) {
    $data = [];
    
    try {
        // 获取用户等级分布
        $stmt = $pdo->query("
            SELECT 
                user_level,
                COUNT(*) as count,
                ROUND(COUNT(*) * 100.0 / (SELECT COUNT(*) FROM users WHERE status != 'deleted'), 2) as percentage
            FROM users 
            WHERE status != 'deleted'
            GROUP BY user_level
            ORDER BY user_level
        ");
        $level_distribution = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        // 获取用户类型分布
        $stmt = $pdo->query("
            SELECT 
                CASE 
                    WHEN total_consumption = 0 THEN 'basic'
                    WHEN total_consumption > 0 AND total_consumption <= 500 THEN 'paying'
                    ELSE 'premium'
                END as user_type,
                COUNT(*) as count,
                ROUND(COUNT(*) * 100.0 / (SELECT COUNT(*) FROM users WHERE status != 'deleted'), 2) as percentage
            FROM users 
            WHERE status != 'deleted'
            GROUP BY user_type
        ");
        $type_distribution = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        $data = [
            'level_distribution' => $level_distribution,
            'type_distribution' => $type_distribution
        ];
        
    } catch (Exception $e) {
        error_log("获取用户分布数据失败: " . $e->getMessage());
    }
    
    return $data;
}

/**
 * 获取活跃度数据
 */
function getActivityData($pdo) {
    $data = [];
    
    try {
        // 检查login_logs表是否存在
        $stmt = $pdo->query("SHOW TABLES LIKE 'login_logs'");
        if ($stmt->rowCount() == 0) {
            return ['message' => 'login_logs表不存在，无法分析活跃度'];
        }
        
        // 获取活跃度分布
        $stmt = $pdo->query("
            SELECT 
                CASE 
                    WHEN login_count >= 20 THEN 'high'
                    WHEN login_count >= 5 THEN 'medium'
                    ELSE 'low'
                END as activity_level,
                COUNT(*) as user_count,
                ROUND(COUNT(*) * 100.0 / (SELECT COUNT(DISTINCT user_id) FROM login_logs WHERE login_time >= DATE_SUB(NOW(), INTERVAL 30 DAY)), 2) as percentage
            FROM (
                SELECT 
                    user_id,
                    COUNT(*) as login_count
                FROM login_logs 
                WHERE login_time >= DATE_SUB(NOW(), INTERVAL 30 DAY)
                AND status = 'success'
                GROUP BY user_id
            ) as user_activity
            GROUP BY activity_level
        ");
        $activity_distribution = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        // 获取24小时活跃分布
        $stmt = $pdo->query("
            SELECT 
                HOUR(login_time) as hour,
                COUNT(*) as login_count
            FROM login_logs 
            WHERE login_time >= DATE_SUB(NOW(), INTERVAL 7 DAY)
            AND status = 'success'
            GROUP BY HOUR(login_time)
            ORDER BY hour
        ");
        $hourly_activity = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        // 填充24小时数据
        $hourly_data = array_fill(0, 24, 0);
        foreach ($hourly_activity as $hour_data) {
            $hourly_data[$hour_data['hour']] = intval($hour_data['login_count']);
        }
        
        $data = [
            'activity_distribution' => $activity_distribution,
            'hourly_activity' => $hourly_data
        ];
        
    } catch (Exception $e) {
        error_log("获取活跃度数据失败: " . $e->getMessage());
        $data = ['message' => '获取活跃度数据失败'];
    }
    
    return $data;
}

/**
 * 获取消费数据
 */
function getConsumptionData($pdo) {
    $data = [];
    
    try {
        // 获取消费统计
        $stmt = $pdo->query("
            SELECT 
                AVG(total_consumption) as avg_consumption,
                COUNT(CASE WHEN total_consumption > 0 THEN 1 END) as paying_users,
                COUNT(*) as total_users,
                SUM(total_consumption) as total_revenue
            FROM users 
            WHERE status != 'deleted'
        ");
        $consumption_stats = $stmt->fetch(PDO::FETCH_ASSOC);
        
        // 获取消费分布
        $stmt = $pdo->query("
            SELECT 
                CASE 
                    WHEN total_consumption = 0 THEN '0元'
                    WHEN total_consumption <= 50 THEN '1-50元'
                    WHEN total_consumption <= 200 THEN '51-200元'
                    WHEN total_consumption <= 500 THEN '201-500元'
                    WHEN total_consumption <= 1000 THEN '501-1000元'
                    ELSE '1000元以上'
                END as consumption_range,
                COUNT(*) as user_count,
                ROUND(COUNT(*) * 100.0 / (SELECT COUNT(*) FROM users WHERE status != 'deleted'), 2) as percentage
            FROM users 
            WHERE status != 'deleted'
            GROUP BY consumption_range
            ORDER BY 
                CASE 
                    WHEN total_consumption = 0 THEN 1
                    WHEN total_consumption <= 50 THEN 2
                    WHEN total_consumption <= 200 THEN 3
                    WHEN total_consumption <= 500 THEN 4
                    WHEN total_consumption <= 1000 THEN 5
                    ELSE 6
                END
        ");
        $consumption_distribution = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        $data = [
            'stats' => $consumption_stats,
            'distribution' => $consumption_distribution
        ];
        
    } catch (Exception $e) {
        error_log("获取消费数据失败: " . $e->getMessage());
    }
    
    return $data;
}

/**
 * 获取地理分布数据
 */
function getGeographicData($pdo) {
    $data = [];
    
    try {
        // 检查login_logs表是否存在
        $stmt = $pdo->query("SHOW TABLES LIKE 'login_logs'");
        if ($stmt->rowCount() == 0) {
            return ['message' => 'login_logs表不存在，无法分析地理分布'];
        }
        
        // 获取地理分布
        $stmt = $pdo->query("
            SELECT 
                location,
                COUNT(DISTINCT user_id) as user_count,
                ROUND(COUNT(DISTINCT user_id) * 100.0 / (SELECT COUNT(DISTINCT user_id) FROM login_logs WHERE location IS NOT NULL AND location != ''), 2) as percentage
            FROM login_logs 
            WHERE location IS NOT NULL 
            AND location != ''
            AND login_time >= DATE_SUB(NOW(), INTERVAL 30 DAY)
            GROUP BY location
            ORDER BY user_count DESC
            LIMIT 10
        ");
        $geographic_distribution = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        $data = [
            'distribution' => $geographic_distribution
        ];
        
    } catch (Exception $e) {
        error_log("获取地理分布数据失败: " . $e->getMessage());
        $data = ['message' => '获取地理分布数据失败'];
    }
    
    return $data;
}

/**
 * 获取设备数据
 */
function getDeviceData($pdo) {
    $data = [];
    
    try {
        // 检查login_logs表是否存在
        $stmt = $pdo->query("SHOW TABLES LIKE 'login_logs'");
        if ($stmt->rowCount() == 0) {
            return ['message' => 'login_logs表不存在，无法分析设备使用'];
        }
        
        // 获取设备类型分布（基于User Agent分析）
        $stmt = $pdo->query("
            SELECT 
                user_agent,
                COUNT(DISTINCT user_id) as user_count
            FROM login_logs 
            WHERE user_agent IS NOT NULL 
            AND user_agent != ''
            AND login_time >= DATE_SUB(NOW(), INTERVAL 30 DAY)
            GROUP BY user_agent
            ORDER BY user_count DESC
        ");
        $user_agents = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        // 分析设备类型
        $device_stats = [
            'mobile' => 0,
            'desktop' => 0,
            'tablet' => 0,
            'ios' => 0,
            'android' => 0,
            'windows' => 0,
            'other' => 0
        ];
        
        foreach ($user_agents as $ua) {
            $agent = $ua['user_agent'];
            $count = $ua['user_count'];
            
            if (preg_match('/iPhone|iPod/i', $agent)) {
                $device_stats['mobile'] += $count;
                $device_stats['ios'] += $count;
            } elseif (preg_match('/iPad/i', $agent)) {
                $device_stats['tablet'] += $count;
                $device_stats['ios'] += $count;
            } elseif (preg_match('/Android.*Mobile/i', $agent)) {
                $device_stats['mobile'] += $count;
                $device_stats['android'] += $count;
            } elseif (preg_match('/Android/i', $agent)) {
                $device_stats['tablet'] += $count;
                $device_stats['android'] += $count;
            } elseif (preg_match('/Windows/i', $agent)) {
                $device_stats['desktop'] += $count;
                $device_stats['windows'] += $count;
            } else {
                $device_stats['other'] += $count;
            }
        }
        
        $total_users = array_sum(array_slice($device_stats, 0, 4)); // mobile, desktop, tablet, other
        
        // 计算百分比
        $device_percentages = [];
        foreach ($device_stats as $type => $count) {
            $device_percentages[$type] = $total_users > 0 ? round($count * 100.0 / $total_users, 2) : 0;
        }
        
        $data = [
            'stats' => $device_stats,
            'percentages' => $device_percentages
        ];
        
    } catch (Exception $e) {
        error_log("获取设备数据失败: " . $e->getMessage());
        $data = ['message' => '获取设备数据失败'];
    }
    
    return $data;
}

/**
 * 获取用户价值数据
 */
function getValueData($pdo) {
    $data = [];
    
    try {
        // 获取用户价值分布
        $stmt = $pdo->query("
            SELECT 
                CASE 
                    WHEN total_consumption >= 1000 THEN 'high'
                    WHEN total_consumption >= 100 THEN 'medium'
                    ELSE 'potential'
                END as value_level,
                COUNT(*) as user_count,
                ROUND(COUNT(*) * 100.0 / (SELECT COUNT(*) FROM users WHERE status != 'deleted'), 2) as percentage,
                AVG(total_consumption) as avg_consumption
            FROM users 
            WHERE status != 'deleted'
            GROUP BY value_level
        ");
        $value_distribution = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        // 获取整体价值指标
        $stmt = $pdo->query("
            SELECT 
                AVG(total_consumption) as avg_ltv,
                COUNT(CASE WHEN last_login >= DATE_SUB(NOW(), INTERVAL 30 DAY) THEN 1 END) * 100.0 / COUNT(*) as retention_rate,
                COUNT(CASE WHEN total_consumption > 0 THEN 1 END) * 100.0 / COUNT(*) as conversion_rate
            FROM users 
            WHERE status != 'deleted'
        ");
        $value_metrics = $stmt->fetch(PDO::FETCH_ASSOC);
        
        $data = [
            'distribution' => $value_distribution,
            'metrics' => $value_metrics
        ];
        
    } catch (Exception $e) {
        error_log("获取用户价值数据失败: " . $e->getMessage());
    }
    
    return $data;
}
?>
