<?php
// 获取新消息API（轮询降级方案）
header('Content-Type: application/json; charset=UTF-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// 处理预检请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

session_start();

// 检查用户登录状态
if (!isset($_SESSION['user_id'])) {
    http_response_code(401);
    echo json_encode(['success' => false, 'error' => '用户未登录']);
    exit;
}

// 获取参数
$session_id = $_GET['session_id'] ?? '';
$last_check = $_GET['last_check'] ?? 0;
$last_message_id = $_GET['last_message_id'] ?? 0;

if (empty($session_id)) {
    http_response_code(400);
    echo json_encode(['success' => false, 'error' => '会话ID不能为空']);
    exit;
}

// 引用数据库配置文件
require_once '../../../houtai_backup/db_config.php';

try {
    $pdo = getDbConnection();

    // 检查会话是否属于当前用户
    $stmt = $pdo->prepare("SELECT * FROM customer_service_sessions WHERE session_id = ?");
    $stmt->execute([$session_id]);
    $session = $stmt->fetch(PDO::FETCH_ASSOC);

    if (!$session) {
        http_response_code(404);
        echo json_encode(['success' => false, 'error' => '会话不存在']);
        exit;
    }

    // 根据参数选择查询方式
    if ($last_message_id > 0) {
        // 按消息ID查询（更可靠）
        $stmt = $pdo->prepare("
            SELECT id, sender_type, sender_name, content, created_at
            FROM customer_service_messages
            WHERE session_id = ?
            AND sender_type = 'customer_service'
            AND id > ?
            ORDER BY id ASC
        ");
        $stmt->execute([$session_id, $last_message_id]);
    } else {
        // 按时间查询（兼容旧版本）
        // 如果 last_check 是秒级时间戳，直接使用；如果是毫秒级，除以1000
        $timestamp = $last_check > 1000000000000 ? $last_check / 1000 : $last_check;
        $check_time = date('Y-m-d H:i:s', $timestamp);

        $stmt = $pdo->prepare("
            SELECT id, sender_type, sender_name, content, created_at
            FROM customer_service_messages
            WHERE session_id = ?
            AND sender_type = 'customer_service'
            AND created_at > ?
            ORDER BY created_at ASC
        ");
        $stmt->execute([$session_id, $check_time]);
    }

    $messages = $stmt->fetchAll(PDO::FETCH_ASSOC);

    echo json_encode([
        'success' => true,
        'messages' => $messages,
        'count' => count($messages),
        'timestamp' => time(),
        'session_id' => $session_id,
        'last_message_id' => $last_message_id,
        'debug' => [
            'user_id' => $_SESSION['user_id'],
            'query_type' => $last_message_id > 0 ? 'by_id' : 'by_time'
        ]
    ]);

} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => '获取消息失败',
        'message' => $e->getMessage(),
        'debug' => [
            'session_id' => $session_id,
            'last_message_id' => $last_message_id,
            'last_check' => $last_check
        ]
    ]);
}
?>
