<?php
/**
 * 测试设备和IP信息功能
 */

session_start();

// 模拟管理员登录
$_SESSION['admin_id'] = 1;

require_once '../db_config.php';

header('Content-Type: application/json');

$user_id = intval($_GET['user_id'] ?? 1);

echo "测试用户ID: $user_id\n\n";

// 测试数据库连接
try {
    $pdo = getDbConnection();
    echo "✓ 数据库连接成功\n";
} catch (Exception $e) {
    echo "✗ 数据库连接失败: " . $e->getMessage() . "\n";
    exit;
}

// 检查login_logs表
try {
    $stmt = $pdo->query("SHOW TABLES LIKE 'login_logs'");
    if ($stmt->rowCount() > 0) {
        echo "✓ login_logs表存在\n";
        
        // 检查表中的数据
        $stmt = $pdo->prepare("SELECT COUNT(*) as count FROM login_logs WHERE user_id = ?");
        $stmt->execute([$user_id]);
        $count = $stmt->fetch()['count'];
        echo "✓ 用户 $user_id 有 $count 条登录记录\n";
        
        if ($count == 0) {
            echo "⚠ 没有登录记录，正在添加测试数据...\n";
            
            // 添加测试数据
            $testData = [
                [$user_id, date('Y-m-d H:i:s', strtotime('-1 hour')), '*************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36', 'success', 'normal_login', '北京市'],
                [$user_id, date('Y-m-d H:i:s', strtotime('-2 hours')), '*************', 'Mozilla/5.0 (iPhone; CPU iPhone OS 17_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.0 Mobile/15E148 Safari/604.1', 'success', 'quick_login', '北京市'],
                [$user_id, date('Y-m-d H:i:s', strtotime('-1 day')), '**************', 'Mozilla/5.0 (Linux; Android 13; SM-G991B) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Mobile Safari/537.36', 'success', 'normal_login', '上海市'],
                [$user_id, date('Y-m-d H:i:s', strtotime('-2 days')), '********', 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36', 'success', 'normal_login', '内网IP']
            ];
            
            $insertStmt = $pdo->prepare("
                INSERT INTO login_logs (user_id, login_time, ip_address, user_agent, status, login_type, location) 
                VALUES (?, ?, ?, ?, ?, ?, ?)
            ");
            
            foreach ($testData as $data) {
                $insertStmt->execute($data);
            }
            
            echo "✓ 已添加 " . count($testData) . " 条测试数据\n";
        }
        
    } else {
        echo "✗ login_logs表不存在\n";
    }
} catch (Exception $e) {
    echo "✗ 检查login_logs表失败: " . $e->getMessage() . "\n";
}

echo "\n";

// 测试get_user_ips.php API
echo "测试 get_user_ips.php API:\n";
$api_url = "get_user_ips.php?user_id=$user_id";

// 模拟API调用
ob_start();
include 'get_user_ips.php';
$api_response = ob_get_clean();

echo "API响应: \n";
echo $api_response . "\n";

$response_data = json_decode($api_response, true);
if ($response_data && $response_data['success']) {
    echo "✓ API调用成功\n";
    echo "✓ 返回 " . count($response_data['records']) . " 条记录\n";
    
    if (!empty($response_data['records'])) {
        echo "\n记录详情:\n";
        foreach ($response_data['records'] as $i => $record) {
            echo "记录 " . ($i + 1) . ":\n";
            echo "  IP: " . $record['ip_address'] . "\n";
            echo "  设备: " . $record['device'] . "\n";
            echo "  系统: " . $record['os'] . "\n";
            echo "  浏览器: " . $record['browser'] . "\n";
            echo "  位置: " . $record['location'] . "\n";
            echo "  登录次数: " . $record['login_count'] . "\n";
            echo "  最后登录: " . $record['login_time'] . "\n";
            echo "  设备图标: " . $record['device_icon'] . "\n";
            echo "\n";
        }
    }
} else {
    echo "✗ API调用失败\n";
    if ($response_data && isset($response_data['message'])) {
        echo "错误信息: " . $response_data['message'] . "\n";
    }
}

echo "\n";
echo "测试完成！\n";
echo "如果看到设备和IP信息，说明功能正常。\n";
echo "现在可以在用户详情页面点击'设备信息'和'IP信息'按钮测试。\n";
?>
