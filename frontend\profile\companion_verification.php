<?php
session_start();

// 检查用户是否已登录
if (!isset($_SESSION['user_id'])) {
    header('Location: ../login/index.php');
    exit;
}

// 数据库连接配置 (确保这个路径正确，或者使用全局配置文件)
require_once __DIR__ . '/../../config.php'; // 假设您的配置文件在此

$user_id = $_SESSION['user_id'];
$current_application_status = 'not_applied'; // 默认状态
$application_message = '';
$applicant_details = []; // 用于预填表单（如果适用，例如被拒绝后修改）

try {
    $dsn = "mysql:host={$db_config['host']};dbname={$db_config['dbname']};charset={$db_config['charset']};port={$db_config['port']}";
    $pdo = new PDO($dsn, $db_config['username'], $db_config['password']);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

    // 1. 检查 companion_verification 表中的状态
    $stmt_cv = $pdo->prepare("SELECT verification_status FROM companion_verification WHERE user_id = :user_id");
    $stmt_cv->execute(['user_id' => $user_id]);
    $cv_result = $stmt_cv->fetch(PDO::FETCH_ASSOC);

    if ($cv_result) {
        $current_application_status = $cv_result['verification_status'];
    } else {
        // 如果 companion_verification 中没有记录，视为 'not_applied'
        // 也可以在这里为用户插入一条 'not_applied' 记录，如果业务逻辑需要
        // $insertStmt = $pdo->prepare("INSERT INTO companion_verification (user_id, verification_status) VALUES (:user_id, 'not_applied') ON DUPLICATE KEY UPDATE user_id=user_id");
        // $insertStmt->execute(['user_id' => $user_id]);
        $current_application_status = 'not_applied';
    }

    // 2. 如果状态是 pending, approved, rejected，尝试获取最新的申请详情
    if (in_array($current_application_status, ['pending', 'approved', 'rejected'])) {
        $stmt_app = $pdo->prepare("SELECT * FROM companion_applications WHERE user_id = :user_id ORDER BY application_date DESC LIMIT 1");
        $stmt_app->execute(['user_id' => $user_id]);
        $app_details = $stmt_app->fetch(PDO::FETCH_ASSOC);
        if($app_details) {
            $applicant_details = $app_details;
        }
    }

    // 设置提示信息
    switch ($current_application_status) {
        case 'pending':
            $application_message = '您的陪玩申请正在审核中，请耐心等待。';
            break;
        case 'approved':
            $application_message = '恭喜您！您的陪玩申请已通过。您现在是认证陪玩了！';
            break;
        case 'rejected':
            $application_message = '很遗憾，您的陪玩申请未通过。';
            if (!empty($applicant_details['admin_notes'])) {
                $application_message .= ' 拒绝原因：' . htmlspecialchars($applicant_details['admin_notes']);
            }
            $application_message .= ' 您可以修改信息后重新提交。';
            break;
        case 'not_applied':
            $application_message = '您尚未申请成为陪玩，请填写下面的表单进行申请。';
            break;
        default:
            $application_message = '欢迎申请成为陪玩！';
            break;
    }

} catch (PDOException $e) {
    error_log("陪玩认证页面数据库错误 (companion_verification.php): " . $e->getMessage());
    $current_application_status = 'error_loading';
    $application_message = '加载申请状态失败，请稍后再试或联系客服。';
}

$page_title = "申请成为陪玩";
if ($current_application_status === 'pending') $page_title = "陪玩申请审核中";
if ($current_application_status === 'approved') $page_title = "陪玩认证已通过";
if ($current_application_status === 'rejected') $page_title = "陪玩申请未通过";

?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <meta name="theme-color" content="#6F7BF5"> <!-- 主题色 -->
    <title><?php echo htmlspecialchars($page_title); ?> - 趣玩星球</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
/* companion_form.css - Embedded for now */

/* Basic Reset & Body Styling */
body {
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
    background-color: #f4f6f8; /* Light grey background */
    color: #333;
    line-height: 1.6;
    margin: 0;
    padding-top: 76px; /* Account for fixed header + a little space */
}

/* Header Bar Styling */
.header-bar {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    height: 56px;
    background-color: #6F7BF5; /* Theme color */
    display: flex;
    align-items: center;
    padding: 0 16px;
    z-index: 1000;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    color: white;
}
.header-bar .back-button {
    font-size: 20px;
    color: white;
    text-decoration: none;
    margin-right: 16px;
}
.header-bar .title {
    font-size: 18px;
    font-weight: 600;
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
}

/* Page Container */
.page-container {
    max-width: 700px; /* Max width for the form content */
    margin: 20px auto; /* Centering the content */
    padding: 0px; 
    background-color: transparent; 
    border-radius: 8px;
}

/* Application Form General Styling */
.application-form > h2 {
    color: #333;
    text-align: center;
    margin-bottom: 30px;
    font-size: 1.8em;
    font-weight: 600;
    padding: 20px;
    background-color: #fff;
    border-radius: 8px 8px 0 0; 
    border: 1px solid #e0e0e0;
    border-bottom: none;
    margin-top: 0; 
}

.application-form > h2 .form-title-icon {
    margin-right: 10px;
    color: #6F7BF5; 
}

/* Form Sections */
.form-section {
    background-color: #ffffff; 
    padding: 25px;
    margin-bottom: 20px;
    border-radius: 8px;
    border: 1px solid #e0e0e0; 
    box-shadow: 0 2px 8px rgba(0,0,0,0.06);
}

.form-section .section-title {
    font-size: 1.3em; 
    color: #333; 
    margin-top: 0;
    margin-bottom: 25px;
    padding-bottom: 12px;
    border-bottom: 1px solid #eee; 
    font-weight: 600; 
    display: flex;
    align-items: center;
}

.form-section .section-title i {
    margin-right: 12px;
    color: #6F7BF5;
    font-size: 1.2em;
}


/* Form Groups */
.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    font-weight: 500;
    margin-bottom: 8px;
    color: #454f5b;
    font-size: 0.9em;
}

.form-group label i.label-icon {
    margin-right: 8px;
    color: #6F7BF5; 
    width: 18px; 
    text-align: center;
    font-size: 1.1em;
}

/* Input, Select, Textarea General Styling */
input[type="text"],
input[type="email"],
input[type="tel"],
input[type="password"],
input[type="url"],
input[type="number"],
select.form-control,
textarea.form-control {
    width: 100%;
    padding: 10px 14px;
    border: 1px solid #d9dce1;
    border-radius: 6px;
    box-sizing: border-box;
    font-size: 0.95em;
    color: #333;
    background-color: #fff;
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

input[type="text"]:focus,
input[type="email"]:focus,
input[type="tel"]:focus,
input[type="password"]:focus,
input[type="url"]:focus,
input[type="number"]:focus,
select.form-control:focus,
textarea.form-control:focus {
    border-color: #6F7BF5;
    outline: 0;
    box-shadow: 0 0 0 0.15rem rgba(111, 123, 245, 0.2);
}

/* Custom Select Styling */
select.form-control {
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    background-image: url("data:image/svg+xml;charset=US-ASCII,%3Csvg%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20width%3D%2216%22%20height%3D%2216%22%20fill%3D%22%236F7BF5%22%20viewBox%3D%220%200%2016%2016%22%3E%3Cpath%20fill-rule%3D%22evenodd%22%20d%3D%22M1.646%204.646a.5.5%200%200%201%20.708%200L8%2010.293l5.646-5.647a.5.5%200%200%201%20.708.708l-6%206a.5.5%200%200%201-.708%200l-6-6a.5.5%200%200%201%200-.708z%22%2F%3E%3C%2Fsvg%3E");
    background-repeat: no-repeat;
    background-position: right 0.9rem center;
    background-size: 14px 10px;
    padding-right: 2.5rem; 
    cursor: pointer;
}

select.form-control::-ms-expand { 
    display: none;
}

textarea.form-control {
    resize: vertical;
    min-height: 100px;
}


::placeholder {
    color: #99a3b0;
    opacity: 1; 
}


.radio-group label {
    display: inline-flex; 
    align-items: center;
    margin-right: 25px;
    font-weight: normal;
    cursor: pointer;
    font-size: 0.95em;
    color: #454f5b;
}

.radio-group input[type="radio"] {
    margin-right: 10px;
    appearance: none;
    -webkit-appearance: none;
    width: 20px;
    height: 20px;
    border: 2px solid #d9dce1;
    border-radius: 50%;
    outline: none;
    cursor: pointer;
    position: relative; 
    top: -1px; 
    transition: border-color 0.15s ease-in-out, background-color 0.15s ease-in-out;
}

.radio-group input[type="radio"]:hover {
    border-color: #b0b9c4;
}

.radio-group input[type="radio"]:checked {
    border-color: #6F7BF5;
    background-color: #fff; 
}

.radio-group input[type="radio"]:checked::before {
    content: '';
    display: block;
    width: 10px;
    height: 10px;
    margin: 3px; 
    background-color: #6F7BF5;
    border-radius: 50%;
}


.file-upload-group label {
    margin-bottom: 10px;
}

.file-input-wrapper {
    display: flex;
    align-items: center;
}

.file-input-wrapper .file-input-label {
    display: inline-block;
    padding: 10px 18px;
    background-color: #6F7BF5;
    color: white;
    border-radius: 6px;
    cursor: pointer;
    font-size: 0.9em;
    transition: background-color 0.2s ease;
    margin-right: 10px;
    white-space: nowrap;
}

.file-input-wrapper .file-input-label:hover {
    background-color: #5a67d8;
}

.file-input-wrapper input[type="file"] {
    display: none; 
}

.file-name-display {
    font-size: 0.85em;
    color: #555;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.file-preview {
    margin-top: 12px;
    max-width: 200px; 
}

.file-preview img, .file-preview audio {
    max-width: 100%;
    height: auto;
    border-radius: 6px;
    border: 1px solid #eee;
    display: block;
}


.submit-btn {
    display: flex; 
    align-items: center;
    justify-content: center;
    width: 100%;
    padding: 14px 20px;
    background-image: linear-gradient(to right, #6F7BF5, #4A56E2);
    color: white;
    border: none;
    border-radius: 8px;
    font-size: 1.05em;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.25s ease;
    box-shadow: 0 4px 10px rgba(111, 123, 245, 0.3);
    margin-top: 10px; 
}

.submit-btn:hover {
    background-image: linear-gradient(to right, #5a67d8, #3a45c1);
    box-shadow: 0 6px 12px rgba(111, 123, 245, 0.4);
    transform: translateY(-2px);
}

.submit-btn:active {
    transform: translateY(0px) scale(0.98);
    box-shadow: 0 2px 5px rgba(111, 123, 245, 0.3);
}

.submit-btn i {
    margin-right: 10px;
    font-size: 1.1em;
}


.terms-group {
    display: flex;
    align-items: center;
    margin-top: 25px;
    margin-bottom: 25px;
    padding: 15px;
    background-color: #f9f9f9;
    border-radius: 6px;
}

.terms-group input[type="checkbox"] {
    flex-shrink: 0; 
    margin-right: 12px;
    width: 20px; 
    height: 20px;
    appearance: none;
    -webkit-appearance: none;
    border: 2px solid #d9dce1;
    border-radius: 4px;
    outline: none;
    cursor: pointer;
    position: relative;
    transition: border-color 0.15s ease-in-out, background-color 0.15s ease-in-out;
}

.terms-group input[type="checkbox"]:hover {
    border-color: #b0b9c4;
}

.terms-group input[type="checkbox"]:checked {
    background-color: #6F7BF5;
    border-color: #6F7BF5;
}

.terms-group input[type="checkbox"]:checked::before {
    content: '\2713'; 
    display: block;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -52%); 
    font-size: 14px;
    color: white;
    font-weight: bold;
}

.terms-group label {
    font-size: 0.9em;
    color: #454f5b;
    margin-bottom: 0; 
    line-height: 1.4;
}

.terms-group label a {
    color: #6F7BF5;
    text-decoration: none;
    font-weight: 500;
}

.terms-group label a:hover {
    text-decoration: underline;
}


.status-message {
    padding: 15px 20px;
    margin-bottom: 25px;
    border-radius: 8px;
    text-align: left;
    font-size: 0.95em;
    border: 1px solid transparent;
}

.status-message.success {
    background-color: #e6f7f0;
    color: #00642e;
    border-color: #a3e0bf;
}

.status-message.error {
    background-color: #fdecea;
    color: #8A1B1B;
    border-color: #f8c0c0;
}

.status-message.info {
    background-color: #e6f3f7;
    color: #005169;
    border-color: #a3d9e8;
}

.status-message h3 {
    margin-top: 0;
    margin-bottom: 8px;
    font-size: 1.1em;
    font-weight: 600;
}


.section-description {
    font-size: 0.85em;
    color: #555e69;
    margin-bottom: 15px;
    padding: 12px 15px;
    background-color: #f8f9fa;
    border-left: 4px solid #6F7BF5;
    border-radius: 0 6px 6px 0;
    line-height: 1.5;
}


.hidden-by-js {
    display: none !important;
}


@media (max-width: 600px) {
    .page-container {
        margin: 0;
        padding: 0;
        border-radius: 0;
        box-shadow: none;
        padding-top: 0; 
    }
    .application-form > h2 {
        border-radius: 0;
        margin-bottom: 0;
        border-left: none;
        border-right: none;
    }
    .form-section {
        margin-bottom: 10px;
        border-radius: 0;
        border-left: none;
        border-right: none;
        box-shadow: none;
    }
    .form-section:last-child {
        margin-bottom: 0;
        border-bottom: none;
    }
    body {
        padding-top: 56px; 
    }
}

    /* Basic Toast CSS */
.toast-notification {
    visibility: hidden;
    min-width: 250px;
    margin-left: -125px;
    background-color: #333;
    color: #fff;
    text-align: center;
    border-radius: 2px;
    padding: 16px;
    position: fixed;
    z-index: 1001; /* Higher than header */
    left: 50%;
    bottom: 30px;
    font-size: 17px;
}

.toast-notification.show {
    visibility: visible;
    -webkit-animation: fadein 0.5s, fadeout 0.5s 2.5s;
    animation: fadein 0.5s, fadeout 0.5s 2.5s;
}

@-webkit-keyframes fadein {
    from {bottom: 0; opacity: 0;}
    to {bottom: 30px; opacity: 1;}
}

@keyframes fadein {
    from {bottom: 0; opacity: 0;}
    to {bottom: 30px; opacity: 1;}
}

@-webkit-keyframes fadeout {
    from {bottom: 30px; opacity: 1;}
    to {bottom: 0; opacity: 0;}
}

@keyframes fadeout {
    from {bottom: 30px; opacity: 1;}
    to {bottom: 0; opacity: 0;}
}
</style>
</head>
<body>

<div class="header-bar">
    <a href="../profile/index.php" class="back-button"><i class="fas fa-arrow-left"></i></a>
    <span class="title"><?php echo htmlspecialchars($page_title); ?></span>
    <!-- 为了让标题在视觉上更居中，可以放一个和返回按钮等宽的透明元素在右边，或者用flex布局调整 -->
</div>

<div class="page-container" id="app">
    <!-- Vue.js 或其他前端框架的挂载点，或者直接用原生JS操作 -->
    <div id="application-area">
        <!-- 状态显示区域 -->
        <div class="status-message" id="status-display" style="display: none;">
            <h3>申请状态</h3>
            <p id="current-status-message">您的申请正在处理中...</p>
        </div>

        <!-- 申请表单 -->
        <form id="companion-application-form" class="application-form" style="display: none;" enctype="multipart/form-data">
            <h2><i class="fas fa-user-check form-title-icon"></i> 成为认证陪玩</h2>
            
            <div class="form-section">
                <h3 class="section-title">基本信息</h3>
                <div class="form-group">
                    <label for="real_name"><i class="fas fa-user"></i> 真实姓名</label>
                    <input type="text" id="real_name" name="real_name" placeholder="请输入您的真实姓名" required>
                </div>
                <div class="form-group">
                    <label for="id_number"><i class="fas fa-id-card"></i> 身份证号</label>
                    <input type="text" id="id_number" name="id_number" placeholder="请输入您的18位身份证号" pattern="^\d{17}(\d|X|x)$" required>
                </div>
                <div class="form-group">
                    <label for="contact_phone"><i class="fas fa-mobile-alt"></i> 联系手机号</label>
                    <input type="tel" id="contact_phone" name="contact_phone" placeholder="点击自动填写或输入手机号" pattern="[0-9]{11}">
                </div>
            </div>

            <div class="form-section">
                <h3 class="section-title">陪玩资料</h3>
                <div class="form-group">
                    <label><i class="fas fa-user-tag"></i> 申请类型</label>
                    <div class="radio-group">
                        <label for="type_game"><input type="radio" id="type_game" name="application_type" value="game" checked> 游戏陪玩</label>
                        <label for="type_city"><input type="radio" id="type_city" name="application_type" value="city"> 城市陪玩</label>
                    </div>
                </div>
                <div class="form-group" id="game-interests-group">
                    <label for="main_game"><i class="fas fa-gamepad"></i> 主打游戏</label>
                    <select id="main_game" name="main_game" class="form-control" required>
                        <option value="" disabled selected>请选择主打游戏</option>
                        <option value="王者荣耀">王者荣耀</option>
                        <option value="和平精英">和平精英</option>
                        <option value="英雄联盟-手游">英雄联盟 (手游)</option>
                        <option value="英雄联盟-端游">英雄联盟 (端游)</option>
                        <option value="金铲铲之战">金铲铲之战</option>
                        <option value="永劫无间">永劫无间</option>
                        <option value="CSGO">CS:GO</option>
                        <option value="光遇">光遇</option>
                        <option value="apex">APEX英雄</option>
                        <option value="valorant">无畏契约 (Valorant)</option>
                        <option value="dota2">Dota 2</option>
                        <option value="原神">原神</option>
                        <option value="崩坏星穹铁道">崩坏：星穹铁道</option>
                        <option value="蛋仔派对">蛋仔派对</option>
                        <option value="第五人格">第五人格</option>
                        <option value="使命召唤手游">使命召唤手游</option>
                        <option value="other_game">其他 (请在自我介绍中注明)</option>
                    </select>

                    <div id="game-specific-details-container" style="margin-top: 15px;">
                        <!-- Dynamic content based on main_game selection will appear here -->
                        <div class="form-group" id="game_rank_group" style="display:none;">
                            <label for="game_rank"><i class="fas fa-trophy"></i> 游戏段位</label>
                            <select id="game_rank" name="game_rank" class="form-control">
                                <option value="" disabled selected>请先选择主打游戏</option>
                                <!-- Options will be populated by JavaScript -->
                            </select>
                        </div>

                        <div class="form-group" id="game_role_lane_group" style="display:none;">
                            <label for="game_role_lane"><i class="fas fa-map-signs"></i> 分路/位置 (如适用)</label>
                            <select id="game_role_lane" name="game_role_lane" class="form-control">
                                <!-- Options will be populated by JavaScript -->
                            </select>
                        </div>
                        
                        <div class="form-group" id="game_kd_group" style="display:none;">
                             <label for="game_kd_input"><i class="fas fa-crosshairs"></i> K/D 或其他指标 (如适用)</label>
                             <input type="text" id="game_kd_input" name="game_kd" class="form-control" placeholder="例如：王者荣耀场均KDA 10.5 / 吃鸡 KD 3.5">
                        </div>
                        <div class="form-group"> <!-- Game Self Intro Group -->
                            <label for="game_self_intro"><i class="fas fa-comment-dots"></i> 游戏陪玩自我介绍</label>
                            <textarea id="game_self_intro" name="game_self_intro" class="form-control" rows="4" placeholder="例如：我声音甜美/稳重，擅长英雄联盟的打野和辅助位置，熟悉各种战术，乐于沟通，能带你轻松上分，体验游戏的乐趣！"></textarea>
                        </div>
                    </div>

                    <div class="form-group file-upload-group" id="game_rank_screenshot_group" style="display:none; margin-top: 15px;">
                        <label for="game_rank_screenshot"><i class="fas fa-image"></i> 游戏段位截图 (选填, JPG/PNG, <2MB)</label>
                        <div class="file-input-wrapper">
                            <input type="file" id="game_rank_screenshot" name="game_rank_screenshot" accept=".jpg,.jpeg,.png">
                            <span class="file-input-label">选择文件</span>
                        </div>
                        <div class="file-preview" id="game-rank-screenshot-preview"></div>
                    </div>
                </div>
                <div id="city-specific-fields" style="display: none;">
                    <div class="form-group">
                        <label for="service_city"><i class="fas fa-city"></i> 服务城市</label>
                        <input type="text" id="service_city" name="service_city" placeholder="如：上海、北京">
                    </div>
                    <div class="form-group">
                        <label for="service_items"><i class="fas fa-concierge-bell"></i> 服务项目</label>
                        <select id="service_items" name="service_items[]" class="form-control" multiple size="5">
                            <option value="酒吧狂欢">酒吧狂欢</option>
                            <option value="KTV派对">KTV派对</option>
                            <option value="桌球助教">桌球助教</option>
                            <option value="吃饭应酬">吃饭应酬</option>
                            <option value="电影逛街">电影逛街</option>
                            <option value="剧本杀">剧本杀</option>
                            <option value="密室逃脱">密室逃脱</option>
                            <option value="旅游导游">旅游导游</option>
                        </select>
                        <small class="form-text text-muted">按住 Ctrl (或 Mac上的 Command) 键可选择多个项目。</small>
                    </div>
                    <div class="form-group">
                        <label for="city_self_intro"><i class="fas fa-microphone-alt"></i> 自我介绍/陪玩宣言</label>
                        <textarea id="city_self_intro" name="city_self_intro" class="form-control" rows="4" placeholder="例如：我是本地通，熟悉各种有趣的吃喝玩乐地点。性格开朗活泼，擅长活跃气氛，可以陪您体验不一样的城市精彩！"></textarea>
                    </div>
                </div>

                <div class="form-group file-upload-group">
                    <label for="voice_sample_url"><i class="fas fa-volume-up"></i> 语音介绍 (选填, MP3/WAV, <2MB)</label>
                    <div class="file-input-wrapper">
                        <input type="file" id="voice_sample_url" name="voice_sample_url" accept=".mp3,.wav">
                        <span class="file-input-label">选择文件</span>
                    </div>
                    <div class="file-preview" id="voice-preview"></div>
                </div>
            </div>

            <div class="form-section">
                <h3 class="section-title">身份验证材料</h3>
                <p class="section-description">请上传清晰、未经过修改的身份证照片，仅用于身份验证，我们会严格保密您的信息。</p>
                <div class="form-group file-upload-group">
                    <label for="id_card_front_url"><i class="fas fa-address-card"></i> 身份证正面照片 (JPG/PNG, <2MB)</label>
                    <div class="file-input-wrapper">
                        <input type="file" id="id_card_front_url" name="id_card_front_url" accept=".jpg,.jpeg,.png" required>
                        <span class="file-input-label">选择文件</span>
                    </div>
                    <div class="file-preview" id="id-card-front-preview"></div>
                </div>
                <div class="form-group file-upload-group">
                    <label for="id_card_back_url"><i class="fas fa-id-card-alt"></i> 身份证背面照片 (JPG/PNG, <2MB)</label>
                    <div class="file-input-wrapper">
                        <input type="file" id="id_card_back_url" name="id_card_back_url" accept=".jpg,.jpeg,.png" required>
                        <span class="file-input-label">选择文件</span>
                    </div>
                    <div class="file-preview" id="id-card-back-preview"></div>
                </div>
            </div>
            
            <div class="form-group terms-group">
                <input type="checkbox" id="agree_terms" name="agree_terms" required>
                <label for="agree_terms">我已阅读并同意 <a href="companion_service_agreement.php" target="_blank" style="color: #6F7BF5; text-decoration: none;">《趣玩星球陪玩服务协议》</a></label>
            </div>

            <button type="submit" class="submit-btn"><i class="fas fa-paper-plane"></i> 提交申请</button>
        </form>
    </div>

</div>

<script>
const currentApplicationStatus = "<?php echo $current_application_status; ?>";
const applicationMessage = "<?php echo addslashes($application_message); ?>";
const applicantDetails = <?php echo json_encode($applicant_details); ?>; // PHP array to JS object

document.addEventListener('DOMContentLoaded', function() {
    const applicationForm = document.getElementById('companion-application-form');
    const statusDisplay = document.getElementById('status-display');
    const statusMessageEl = document.getElementById('current-status-message');

    // --- START: Cascade - Toast Notification for Contact Phone ---
    function showToast(message) {
        const toast = document.createElement('div');
        toast.textContent = message;
        toast.style.position = 'fixed';
        toast.style.bottom = '30px'; // Adjusted for better visibility
        toast.style.left = '50%';
        toast.style.transform = 'translateX(-50%)';
        toast.style.backgroundColor = 'rgba(0,0,0,0.75)'; // Darker, slightly transparent
        toast.style.color = 'white';
        toast.style.padding = '12px 25px';
        toast.style.borderRadius = '25px'; // More rounded
        toast.style.zIndex = '1005'; 
        toast.style.opacity = '0';
        toast.style.transition = 'opacity 0.3s ease, bottom 0.3s ease';
        toast.style.fontSize = '0.9em';
        document.body.appendChild(toast);

        setTimeout(() => {
            toast.style.opacity = '1';
            toast.style.bottom = '50px';
        }, 100);

        setTimeout(() => {
            toast.style.opacity = '0';
            toast.style.bottom = '30px';
            setTimeout(() => {
                if (document.body.contains(toast)) {
                    document.body.removeChild(toast);
                }
            }, 300);
        }, 3000);
    }

    const contactPhoneInput = document.getElementById('contact_phone');
    if (contactPhoneInput) {
        contactPhoneInput.addEventListener('focus', function() {
            if (!this.dataset.toastShown) {
                showToast('同意并授权获取手机号码');
                // Actual phone number fetching and pre-filling would require backend interaction.
                // For now, this just shows the toast.
                // As a placeholder, you might want to visually indicate it's interactive:
                // this.placeholder = '授权后将尝试自动填写'; 
                this.dataset.toastShown = 'true'; 
            }
        });
    }
    // --- END: Cascade - Toast Notification for Contact Phone ---

    // --- START: Cascade - Logic for Application Type and Dynamic Fields ---
    const typeGameRadio = document.getElementById('type_game');
    const typeCityRadio = document.getElementById('type_city');
    const gameInterestsGroup = document.getElementById('game-interests-group');
    // const gameInterestsInput = document.getElementById('game_interests'); // No longer used directly
    const citySpecificFieldsDiv = document.getElementById('city-specific-fields');
    const serviceCityInput = document.getElementById('service_city');
    const gameSelfIntro = document.getElementById('game_self_intro');
    const citySelfIntro = document.getElementById('city_self_intro');
    // const selfDescriptionTextarea = document.getElementById('self_description'); // Removed

    // --- START: Cascade - New Game Specific Logic ---
    const mainGameSelect = document.getElementById('main_game');
    const gameRankGroup = document.getElementById('game_rank_group');
    const gameRankSelect = document.getElementById('game_rank');
    const gameRoleLaneGroup = document.getElementById('game_role_lane_group');
    const gameRoleLaneSelect = document.getElementById('game_role_lane');
    const gameKdGroup = document.getElementById('game_kd_group');
    const gameKdInput = document.getElementById('game_kd');
    const gameRankScreenshotGroup = document.getElementById('game_rank_screenshot_group');
    const gameRankScreenshotInput = document.getElementById('game_rank_screenshot');
    const gameRankScreenshotPreview = document.getElementById('game-rank-screenshot-preview');

    const gameData = {
        '王者荣耀': {
            ranks: ['倔强青铜', '秩序白银', '荣耀黄金', '尊贵铂金', '永恒钻石', '至尊星耀', '最强王者', '荣耀王者', '传奇王者'],
            roles: ['对抗路', '打野', '中路', '发育路', '游走'],
            hasKD: false,
            requiresRankScreenshot: true
        },
        '和平精英': {
            ranks: ['热血青铜', '不屈白银', '英勇黄金', '坚韧铂金', '荣耀皇冠', '超级王牌', '无敌战神'],
            roles: ['突击手', '狙击手', '自由人', '指挥'],
            hasKD: true,
            requiresRankScreenshot: true
        },
        '英雄联盟-手游': {
            ranks: ['黑铁', '青铜', '白银', '黄金', '铂金', '翡翠', '钻石', '大师', '宗师', '王者'],
            roles: ['上路', '打野', '中路', '下路', '辅助'],
            hasKD: false,
            requiresRankScreenshot: true
        },
        '英雄联盟-端游': {
            ranks: ['坚韧黑铁', '英勇青铜', '不屈白银', '荣耀黄金', '华贵铂金', '璀璨钻石', '超凡大师', '傲世宗师', '最强王者'],
            roles: ['上单', '打野', '中单', 'ADC', '辅助'],
            hasKD: false,
            requiresRankScreenshot: true
        },
        '金铲铲之战': {
            ranks: ['黑铁', '青铜', '白银', '黄金', '铂金', '钻石', '大师', '宗师', '王者'],
            roles: null, // No specific roles needed for selection
            hasKD: false,
            requiresRankScreenshot: true
        },
        '永劫无间': {
            ranks: ['青铜', '白银', '黄金', '铂金', '陨星', '蚀月', '无间修罗', '殿堂'],
            roles: null,
            hasKD: true,
            requiresRankScreenshot: true
        },
        'CSGO': {
            ranks: ['白银I', '白银II', '白银III', '白银IV', '白银精英', '大师级白银精英', '黄金新星I', '黄金新星II', '黄金新星III', '大师级黄金新星', '大师级守护者I', '大师级守护者II', '大师级守护者精英', '卓越大师级守护者', '传奇之鹰', '大师级传奇之鹰', '无上之首席大师', '全球精英'],
            roles: null,
            hasKD: true,
            requiresRankScreenshot: true
        },
        '光遇': {
            ranks: null, // No competitive ranks
            roles: null,
            hasKD: false,
            requiresRankScreenshot: false // Or true if they want to show something else
        },
        'apex': {
            ranks: ['青铜', '白银', '黄金', '铂金', '钻石', '大师', '顶尖猎杀者'],
            roles: null,
            hasKD: true,
            requiresRankScreenshot: true
        },
        'valorant': {
            ranks: ['黑铁', '青铜', '白银', '黄金', '铂金', '钻石', '超凡入圣', '神话', '辐能战魂'],
            roles: ['决斗', '控场', '先锋', '哨位'],
            hasKD: true,
            requiresRankScreenshot: true
        },
        'dota2': {
            ranks: ['先锋', '卫士', '中军', '统帅', '传奇', '万古流芳', '超凡入圣', '冠绝一世'],
            roles: ['核心(1号位)', '核心(2号位)', '核心(3号位)', '辅助(4号位)', '辅助(5号位)'],
            hasKD: false,
            requiresRankScreenshot: true
        },
        '原神': { ranks: null, roles: null, hasKD: false, requiresRankScreenshot: false },
        '崩坏星穹铁道': { ranks: null, roles: null, hasKD: false, requiresRankScreenshot: false },
        '蛋仔派对': { ranks: ['鹌鹑蛋', '鸽子蛋', '鸡蛋', '鸭蛋', '鹅蛋', '鸵鸟蛋', '恐龙蛋', '巅峰凤凰蛋'], roles: null, hasKD: false, requiresRankScreenshot: true },
        '第五人格': { ranks: ['一阶', '二阶', '三阶', '四阶', '五阶', '六阶', '七阶', '巅峰七阶'], roles: ['求生者', '监管者'], hasKD: false, requiresRankScreenshot: true },
        '使命召唤手游': {
            ranks: ['新秀', '老兵', '精英', '职业', '大师', '宗师', '传奇'],
            roles: null,
            hasKD: true,
            requiresRankScreenshot: true
        },
        'other_game': { ranks: null, roles: null, hasKD: true, requiresRankScreenshot: true } // For 'other', allow KD and screenshot
    };

    function populateSelect(selectElement, optionsArray, defaultOptionText = '请选择') {
        selectElement.innerHTML = `<option value="" disabled selected>${defaultOptionText}</option>`;
        if (optionsArray) {
            optionsArray.forEach(option => {
                selectElement.add(new Option(option, option));
            });
        }
    }

    function updateGameSpecificFields(selectedGameValue) {
        const selectedGameConfig = gameData[selectedGameValue];

        if (selectedGameConfig) {
            // Game Rank
            if (selectedGameConfig.ranks && gameRankGroup && gameRankSelect) {
                populateSelect(gameRankSelect, selectedGameConfig.ranks, '请选择段位');
                gameRankGroup.style.display = '';
                gameRankSelect.required = true;
            } else if (gameRankGroup && gameRankSelect) {
                gameRankGroup.style.display = 'none';
                gameRankSelect.required = false;
                gameRankSelect.innerHTML = '<option value="" disabled selected>不适用</option>';
            }

            // Game Role/Lane
            if (selectedGameConfig.roles && gameRoleLaneGroup && gameRoleLaneSelect) {
                populateSelect(gameRoleLaneSelect, selectedGameConfig.roles, '请选择分路/位置');
                gameRoleLaneGroup.style.display = '';
                gameRoleLaneSelect.required = true; // Or false if optional for some games
            } else if (gameRoleLaneGroup && gameRoleLaneSelect) {
                gameRoleLaneGroup.style.display = 'none';
                gameRoleLaneSelect.required = false;
                gameRoleLaneSelect.innerHTML = '<option value="" disabled selected>不适用</option>';
            }

            // Game K/D
            if (selectedGameConfig.hasKD && gameKdGroup && gameKdInput) {
                gameKdGroup.style.display = '';
                gameKdInput.required = true; // Or false if optional
            } else if (gameKdGroup && gameKdInput) {
                gameKdGroup.style.display = 'none';
                gameKdInput.required = false;
            }

            // Game Rank Screenshot
            if (selectedGameConfig.requiresRankScreenshot && gameRankScreenshotGroup) {
                gameRankScreenshotGroup.style.display = '';
                // gameRankScreenshotInput.required = true; // Making it optional as per label
            } else if (gameRankScreenshotGroup) {
                gameRankScreenshotGroup.style.display = 'none';
                // gameRankScreenshotInput.required = false;
            }
        } else { // No config or 'other_game' without specific ranks/roles defined here
            if (gameRankGroup) gameRankGroup.style.display = selectedGameValue === 'other_game' ? '' : 'none';
            if (gameRankSelect) gameRankSelect.innerHTML = selectedGameValue === 'other_game' ? '<option value="">请在自我介绍中说明段位</option>' : '<option value="" disabled selected>请先选择主打游戏</option>';
            if (gameRankSelect) gameRankSelect.required = false;

            if (gameRoleLaneGroup) gameRoleLaneGroup.style.display = 'none';
            if (gameRoleLaneSelect) gameRoleLaneSelect.innerHTML = '<option value="" disabled selected>不适用</option>';
            if (gameRoleLaneSelect) gameRoleLaneSelect.required = false;

            if (gameKdGroup) gameKdGroup.style.display = selectedGameValue === 'other_game' ? '' : 'none'; // Show for 'other'
            if (gameKdInput) gameKdInput.required = false;
            
            if (gameRankScreenshotGroup) gameRankScreenshotGroup.style.display = selectedGameValue === 'other_game' ? '' : 'none'; // Show for 'other'
            // if (gameRankScreenshotInput) gameRankScreenshotInput.required = false;
        }
    }

    if (mainGameSelect) {
        mainGameSelect.addEventListener('change', function() {
            updateGameSpecificFields(this.value);
        });
        // Initialize for the default selected game if any (though default is '请选择')
        if(mainGameSelect.value) {
             updateGameSpecificFields(mainGameSelect.value);
        }
    }
    // --- END: Cascade - New Game Specific Logic ---

    function toggleCompanionFields(applicationType) {
        if (applicationType === 'game') {
            if (gameInterestsGroup) gameInterestsGroup.style.display = '';
            if (mainGameSelect) mainGameSelect.required = true;
            // Visibility of sub-elements within gameInterestsGroup is handled by updateGameSpecificFields
            if (mainGameSelect && mainGameSelect.value && mainGameSelect.value !== "") {
                updateGameSpecificFields(mainGameSelect.value);
            } else {
                 // If no game is selected yet (e.g. placeholder is selected), hide all sub-fields
                if (gameRankGroup) gameRankGroup.style.display = 'none';
                if (gameRoleLaneGroup) gameRoleLaneGroup.style.display = 'none';
                if (gameKdGroup) gameKdGroup.style.display = 'none';
                if (gameRankScreenshotGroup) gameRankScreenshotGroup.style.display = 'none';
            }

            if (citySpecificFieldsDiv) citySpecificFieldsDiv.style.display = 'none';
            if (serviceCityInput) serviceCityInput.required = false;
            if (gameSelfIntro) gameSelfIntro.required = true;
            if (citySelfIntro) citySelfIntro.required = false;
        } else if (applicationType === 'city') {
            if (gameInterestsGroup) gameInterestsGroup.style.display = 'none';
            if (mainGameSelect) mainGameSelect.required = false;
            // Also ensure all sub-game fields are not required
            if (gameRankSelect) gameRankSelect.required = false;
            if (gameRoleLaneSelect) gameRoleLaneSelect.required = false;
            if (gameKdInput) gameKdInput.required = false;
            // gameRankScreenshotInput is optional by default

            if (citySpecificFieldsDiv) citySpecificFieldsDiv.style.display = '';
            if (serviceCityInput) serviceCityInput.required = true;
            if (gameSelfIntro) gameSelfIntro.required = false;
            if (citySelfIntro) citySelfIntro.required = true;
        } else { // Default or error case, show game fields
            if (gameInterestsGroup) gameInterestsGroup.style.display = '';
            if (gameInterestsInput) gameInterestsInput.required = true;
            if (citySpecificFieldsDiv) citySpecificFieldsDiv.style.display = 'none';
            if (serviceCityInput) serviceCityInput.required = false;
            if (serviceItemsTextarea) serviceItemsTextarea.required = false;
        }
    }

    if (typeGameRadio && typeCityRadio) {
        typeGameRadio.addEventListener('change', function() {
            if (this.checked) {
                toggleCompanionFields('game');
            }
        });

        typeCityRadio.addEventListener('change', function() {
            if (this.checked) {
                toggleCompanionFields('city');
            }
        });
    }

    // Initial state and pre-filling based on applicantDetails (which is populated by PHP)
    let initialApplicationType = 'game'; // Default
    // Check HTML default checked state first
    if (typeGameRadio && typeGameRadio.checked) initialApplicationType = 'game'; 
    if (typeCityRadio && typeCityRadio.checked) initialApplicationType = 'city';

    if (typeof applicantDetails !== 'undefined' && applicantDetails && Object.keys(applicantDetails).length > 0) {
        // Pre-fill common fields (these might be pre-filled by other logic too, this is a fallback/enhancement)
        if (document.getElementById('real_name') && applicantDetails.real_name) document.getElementById('real_name').value = applicantDetails.real_name;
        if (document.getElementById('id_number') && applicantDetails.id_number) document.getElementById('id_number').value = applicantDetails.id_number;
        if (document.getElementById('contact_info') && applicantDetails.contact_info) document.getElementById('contact_info').value = applicantDetails.contact_info;
        if (document.getElementById('self_description') && applicantDetails.self_description) document.getElementById('self_description').value = applicantDetails.self_description;

        if (applicantDetails.application_type) {
            initialApplicationType = applicantDetails.application_type;
            if (initialApplicationType === 'city') {
                if (typeCityRadio) typeCityRadio.checked = true;
                if (typeGameRadio) typeGameRadio.checked = false;
                if (applicantDetails.type_specific_details && typeof applicantDetails.type_specific_details === 'object') { // Ensure it's an object
                    try {
                        let cityDetails = applicantDetails.type_specific_details;
                        if (typeof cityDetails === 'string') {
                           cityDetails = JSON.parse(cityDetails);
                        }
                        if (serviceCityInput && cityDetails && cityDetails.service_city) serviceCityInput.value = cityDetails.service_city;
                        if (serviceItemsTextarea && cityDetails && cityDetails.service_items) {
                            serviceItemsTextarea.value = Array.isArray(cityDetails.service_items) ? cityDetails.service_items.join(', ') : cityDetails.service_items;
                        }
                    } catch (e) {
                        console.error('Error parsing type_specific_details for city companion:', e);
                    }
                }
            } else { // 'game' or other, default to game
                if (typeGameRadio) typeGameRadio.checked = true;
                if (typeCityRadio) typeCityRadio.checked = false;
                // New pre-filling for game specific details
                if (applicantDetails.type_specific_details && typeof applicantDetails.type_specific_details === 'object') {
                    const gameDetails = applicantDetails.type_specific_details;
                    if (mainGameSelect && gameDetails.main_game) {
                        mainGameSelect.value = gameDetails.main_game;
                        // IMPORTANT: Trigger change to populate dependent fields BEFORE setting their values
                        mainGameSelect.dispatchEvent(new Event('change')); 
                    }
                    // Now that dependent dropdowns are populated, try to set their values
                    // A slight delay might be needed if dispatchEvent is too fast for DOM updates, but usually not.
                    if (gameRankSelect && gameDetails.game_rank) gameRankSelect.value = gameDetails.game_rank;
                    if (gameRoleLaneSelect && gameDetails.game_role_lane) gameRoleLaneSelect.value = gameDetails.game_role_lane;
                    if (gameKdInput && gameDetails.game_kd) gameKdInput.value = gameDetails.game_kd;
                    
                    // Pre-fill screenshot preview if URL exists (assuming you have a function for this or will add one)
                    if (gameRankScreenshotPreview && gameDetails.game_rank_screenshot_url) {
                        // This part needs a function to show preview, similar to ID card uploads
                        // For now, just logging it. You'll need to adapt your file preview logic.
                        // console.log('Need to show preview for game_rank_screenshot_url:', gameDetails.game_rank_screenshot_url);
                        // Example: gameRankScreenshotPreview.innerHTML = `<img src="${gameDetails.game_rank_screenshot_url}" style="max-width:100px; max-height:100px;">`;
                    }
                } else if (gameInterestsInput && applicantDetails.game_interests) {
                     // Fallback for old data structure if type_specific_details is not an object for game type
                     // This 'gameInterestsInput' doesn't exist anymore, so this path is less relevant
                     // console.warn('Old game_interests field found, new structure preferred.');
                }
            }
        } else { // No application_type in applicantDetails, default to game based on HTML or 'game'
             if (typeGameRadio) typeGameRadio.checked = true; 
             if (typeCityRadio) typeCityRadio.checked = false;
             // No specific game details to pre-fill if applicantDetails.application_type is missing
             // but ensure game fields are shown by default if type_game is checked.
             // The old gameInterestsInput is gone.
        }
    }
    
    // Set initial visibility and required attributes based on determined type
    toggleCompanionFields(initialApplicationType);

    // --- START: Toast Notification Logic ---
    function showToast(message) {
        var toast = document.getElementById("toast-notification-area");
        if (!toast) {
            toast = document.createElement('div');
            toast.id = "toast-notification-area";
            toast.className = "toast-notification";
            document.body.appendChild(toast);
        }
        toast.textContent = message;
        toast.className = "toast-notification show";
        setTimeout(function(){ toast.className = toast.className.replace("show", ""); }, 3000);
    }

    const contactInfoInput = document.getElementById('contact_info');
    if (contactInfoInput) {
        contactInfoInput.addEventListener('focus', function() {
            showToast('同意并授权获取手机号码');
            // You could potentially pre-fill with a desensitized number if available from PHP
            // For example, if a desensitized phone number is passed from PHP:
            // const desensitizedPhone = "<?php echo isset($applicantDetails['desensitized_phone']) ? $applicantDetails['desensitized_phone'] : ''; ?>";
            // if (this.value === '' && desensitizedPhone) { 
            //    this.value = desensitizedPhone; 
            // }
        });
    }
    // --- END: Toast Notification Logic ---
    // --- END: Cascade - Logic for Application Type and Dynamic Fields ---

    // Initialize page based on status
    if (currentApplicationStatus === 'not_applied' || currentApplicationStatus === 'rejected') {
        statusDisplay.style.display = 'none';
        applicationForm.style.display = 'block';
        if (currentApplicationStatus === 'rejected' && applicantDetails) {
            // Pre-fill form if rejected and details are available
            document.getElementById('real_name').value = applicantDetails.real_name || '';
            document.getElementById('id_number').value = applicantDetails.id_number || '';
            document.getElementById('contact_info').value = applicantDetails.contact_info || '';
            document.getElementById('game_interests').value = applicantDetails.game_interests || '';
            document.getElementById('self_description').value = applicantDetails.self_description || '';
            // Note: File inputs cannot be pre-filled for security reasons.
            // User will need to re-upload files.
            if (applicationMessage) {
                const rejectedNotice = document.createElement('p');
                rejectedNotice.className = 'status-message rejected'; // Use CSS for styling
                rejectedNotice.innerHTML = `<strong>申请未通过:</strong> ${applicationMessage.replace(' 您可以修改信息后重新提交。', '')}`;
                applicationForm.insertBefore(rejectedNotice, applicationForm.firstChild);
            }
        }
    } else if (currentApplicationStatus === 'pending' || currentApplicationStatus === 'approved' || currentApplicationStatus === 'error_loading') {
        applicationForm.style.display = 'none';
        statusMessageEl.innerHTML = applicationMessage;
        statusDisplay.className = `status-message ${currentApplicationStatus}`;
        statusDisplay.style.display = 'block';
    } else {
        // Default or unknown status, show form
        applicationForm.style.display = 'block';
        statusDisplay.style.display = 'none';
    }

    // File Preview Logic
    function setupFilePreview(fileInputId, previewElementId, isImage = true) {
        const fileInput = document.getElementById(fileInputId);
        const previewElement = document.getElementById(previewElementId);
        if (!fileInput || !previewElement) return;

        fileInput.addEventListener('change', function(event) {
            const file = event.target.files[0];
            previewElement.innerHTML = ''; // Clear previous preview
            if (file) {
                const fileNameSpan = document.createElement('span');
                fileNameSpan.textContent = `已选择: ${file.name} (${(file.size / 1024 / 1024).toFixed(2)}MB)`;
                previewElement.appendChild(fileNameSpan);

                if (isImage && file.type.startsWith('image/')) {
                    const reader = new FileReader();
                    reader.onload = function(e) {
                        const img = document.createElement('img');
                        img.src = e.target.result;
                        previewElement.appendChild(img);
                    }
                    reader.readAsDataURL(file);
                } else if (!isImage && file.type.startsWith('audio/')) {
                    const reader = new FileReader();
                    reader.onload = function(e) {
                        const audio = document.createElement('audio');
                        audio.controls = true;
                        audio.src = e.target.result;
                        previewElement.appendChild(audio);
                    }
                    reader.readAsDataURL(file);
                }
            }
        });
    }

    setupFilePreview('voice_sample_url', 'voice-preview', false);
    setupFilePreview('id_card_front_url', 'id-card-front-preview', true);
    setupFilePreview('id_card_back_url', 'id-card-back-preview', true);

    // Form Submission Logic
    if (applicationForm) {
        applicationForm.addEventListener('submit', function(event) {
            event.preventDefault();
            const submitButton = applicationForm.querySelector('button[type="submit"]');
            const originalButtonText = submitButton.innerHTML;
            submitButton.disabled = true;
            submitButton.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 正在提交...';

            const formData = new FormData(applicationForm);
            // You might want to add user_id or other necessary data to formData if not already in form
            // formData.append('user_id', <?php echo $user_id; ?>);

            fetch('api/submit_companion_application.php', { // Ensure this API endpoint is correct
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    applicationForm.style.display = 'none';
                    statusMessageEl.innerHTML = data.message || '申请提交成功！我们会在1-3个工作日内完成审核。';
                    statusDisplay.className = 'status-message success'; // Or 'pending' if that's the immediate next state
                    statusDisplay.style.display = 'block';
                    // Optionally, update page title or redirect
                    document.querySelector('.header-bar .title').textContent = '陪玩申请审核中';
                } else {
                    // Display error message near the submit button or in a dedicated error area
                    let errorMsg = data.message || '提交失败，请检查您的输入并重试。';
                    if (data.errors) { // Detailed errors
                        errorMsg += '<ul>';
                        for (const key in data.errors) {
                            errorMsg += `<li>${data.errors[key]}</li>`;
                        }
                        errorMsg += '</ul>';
                    }
                    const errorDiv = document.createElement('div');
                    errorDiv.className = 'status-message error'; // Use CSS for styling
                    errorDiv.innerHTML = `<h3>提交失败</h3><p>${errorMsg}</p>`;
                    // Insert error message before the submit button or at the top of the form
                    applicationForm.insertBefore(errorDiv, submitButton);
                    // Remove previous error messages if any
                    const existingError = applicationForm.querySelector('.status-message.error');
                    if(existingError && existingError !== errorDiv) existingError.remove();

                    submitButton.disabled = false;
                    submitButton.innerHTML = originalButtonText;
                }
            })
            .catch(error => {
                console.error('Submission error:', error);
                const errorDiv = document.createElement('div');
                errorDiv.className = 'status-message error';
                errorDiv.innerHTML = '<h3>提交错误</h3><p>网络错误或服务器无响应，请稍后重试。</p>';
                applicationForm.insertBefore(errorDiv, submitButton);
                const existingError = applicationForm.querySelector('.status-message.error');
                if(existingError && existingError !== errorDiv) existingError.remove();

                submitButton.disabled = false;
                submitButton.innerHTML = originalButtonText;
            });
        });
    }
});

</script>

</body>
</html>
