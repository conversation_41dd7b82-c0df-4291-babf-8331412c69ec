# 趣玩星球 Apache 配置文件

# 启用重写引擎
RewriteEngine On

# 防止访问敏感文件
<Files "*.md">
    Order allow,deny
    Deny from all
</Files>

<Files "*.sql">
    Order allow,deny
    Deny from all
</Files>

<Files ".env">
    Order allow,deny
    Deny from all
</Files>

# 允许静态文件直接访问
RewriteCond %{REQUEST_FILENAME} -f
RewriteCond %{REQUEST_URI} \.(html|css|js|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ [NC]
RewriteRule ^ - [L]

# 允许PHP文件直接访问（除了根目录的index.php）
RewriteCond %{REQUEST_FILENAME} -f
RewriteCond %{REQUEST_URI} \.php$ [NC]
RewriteCond %{REQUEST_URI} !^/index\.php$ [NC]
RewriteRule ^ - [L]

# 只有根目录访问才重定向到首页
RewriteCond %{REQUEST_URI} ^/$
RewriteRule ^$ /frontend/home/<USER>

# 如果访问根目录的index.php，重定向到首页
RewriteCond %{REQUEST_URI} ^/index\.php$
RewriteRule ^index\.php$ /frontend/home/<USER>

# 设置默认字符集
AddDefaultCharset UTF-8

# 启用压缩
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/xml
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE application/xml
    AddOutputFilterByType DEFLATE application/xhtml+xml
    AddOutputFilterByType DEFLATE application/rss+xml
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/x-javascript
</IfModule>

# 设置缓存
<IfModule mod_expires.c>
    ExpiresActive on
    ExpiresByType text/css "access plus 1 year"
    ExpiresByType application/javascript "access plus 1 year"
    ExpiresByType image/png "access plus 1 year"
    ExpiresByType image/jpg "access plus 1 year"
    ExpiresByType image/jpeg "access plus 1 year"
    ExpiresByType image/gif "access plus 1 year"
    ExpiresByType image/svg+xml "access plus 1 year"
</IfModule>
