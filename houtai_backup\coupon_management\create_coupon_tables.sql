-- =====================================================
-- 优惠券管理系统数据库表创建脚本
-- 趣玩星球管理后台
-- =====================================================

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- =====================================================
-- 1. 露营活动优惠券表
-- =====================================================
CREATE TABLE IF NOT EXISTS `camping_coupons` (
    `id` INT(11) NOT NULL AUTO_INCREMENT COMMENT '优惠券ID',
    `title` VARCHAR(100) NOT NULL COMMENT '优惠券标题',
    `description` VARCHAR(200) COMMENT '优惠券描述',
    `type` ENUM('join_discount', 'organize_discount', 'newbie_discount') NOT NULL COMMENT '优惠券类型',
    `discount_amount` DECIMAL(10,2) NOT NULL COMMENT '优惠金额',
    `min_amount` DECIMAL(10,2) NOT NULL DEFAULT 0.00 COMMENT '最低消费金额',
    `total_quantity` INT(11) NOT NULL COMMENT '总发放数量',
    `claimed_quantity` INT(11) NOT NULL DEFAULT 0 COMMENT '已领取数量',
    `used_quantity` INT(11) NOT NULL DEFAULT 0 COMMENT '已使用数量',
    `valid_from` DATETIME NOT NULL COMMENT '有效期开始时间',
    `valid_until` DATETIME NOT NULL COMMENT '有效期结束时间',
    `status` ENUM('active', 'inactive', 'expired') NOT NULL DEFAULT 'active' COMMENT '优惠券状态',
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    KEY `idx_type` (`type`),
    KEY `idx_status` (`status`),
    KEY `idx_valid_period` (`valid_from`, `valid_until`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='露营活动优惠券表';

-- =====================================================
-- 2. 用户优惠券领取记录表
-- =====================================================
CREATE TABLE IF NOT EXISTS `user_camping_coupons` (
    `id` INT(11) NOT NULL AUTO_INCREMENT COMMENT '记录ID',
    `user_id` INT(11) NOT NULL COMMENT '用户ID',
    `coupon_id` INT(11) NOT NULL COMMENT '优惠券ID',
    `claimed_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '领取时间',
    `used_at` TIMESTAMP NULL COMMENT '使用时间',
    `used_activity_id` INT(11) DEFAULT NULL COMMENT '使用的活动ID',
    `status` ENUM('claimed', 'used', 'expired') NOT NULL DEFAULT 'claimed' COMMENT '优惠券状态',
    PRIMARY KEY (`id`),
    UNIQUE KEY `unique_user_coupon` (`user_id`, `coupon_id`),
    KEY `idx_user` (`user_id`),
    KEY `idx_coupon` (`coupon_id`),
    KEY `idx_status` (`status`),
    KEY `idx_claimed_at` (`claimed_at`),
    KEY `idx_used_at` (`used_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户优惠券领取记录表';

-- =====================================================
-- 3. 露营活动表（如果不存在）
-- =====================================================
CREATE TABLE IF NOT EXISTS `camping_activities` (
    `id` INT(11) NOT NULL AUTO_INCREMENT COMMENT '活动ID',
    `title` VARCHAR(200) NOT NULL COMMENT '活动标题',
    `description` TEXT COMMENT '活动描述',
    `organizer_id` INT(11) NOT NULL COMMENT '组局者用户ID',
    `location` VARCHAR(200) NOT NULL COMMENT '活动地点',
    `start_date` DATETIME NOT NULL COMMENT '活动开始时间',
    `end_date` DATETIME NOT NULL COMMENT '活动结束时间',
    `max_participants` INT(11) NOT NULL DEFAULT 20 COMMENT '最大参与人数',
    `current_participants` INT(11) NOT NULL DEFAULT 0 COMMENT '当前参与人数',
    `price` DECIMAL(10,2) NOT NULL DEFAULT 0.00 COMMENT '活动费用',
    `original_price` DECIMAL(10,2) DEFAULT NULL COMMENT '原价（用于显示优惠）',
    `category` ENUM('mountain', 'lake', 'forest', 'beach') NOT NULL DEFAULT 'mountain' COMMENT '活动分类',
    `features` JSON COMMENT '活动特色标签（JSON格式）',
    `image_url` VARCHAR(500) COMMENT '活动主图',
    `status` ENUM('draft', 'recruiting', 'full', 'ongoing', 'completed', 'cancelled') NOT NULL DEFAULT 'recruiting' COMMENT '活动状态',
    `rules_accepted` TINYINT(1) NOT NULL DEFAULT 0 COMMENT '是否需要接受活动规则',
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    KEY `idx_organizer` (`organizer_id`),
    KEY `idx_category` (`category`),
    KEY `idx_status` (`status`),
    KEY `idx_start_date` (`start_date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='露营活动表';

-- =====================================================
-- 4. 插入初始优惠券数据
-- =====================================================
INSERT IGNORE INTO `camping_coupons` (`title`, `description`, `type`, `discount_amount`, `min_amount`, `total_quantity`, `claimed_quantity`, `used_quantity`, `valid_from`, `valid_until`) VALUES
('新人专享券', '首次参加露营活动专享优惠', 'newbie_discount', 20.00, 80.00, 1000, 150, 45, NOW(), DATE_ADD(NOW(), INTERVAL 30 DAY)),
('放肆趣玩券', '参加任意露营活动立减优惠', 'join_discount', 30.00, 100.00, 500, 280, 120, NOW(), DATE_ADD(NOW(), INTERVAL 30 DAY)),
('组局优惠券', '发起露营活动减免费用', 'organize_discount', 50.00, 200.00, 300, 85, 32, NOW(), DATE_ADD(NOW(), INTERVAL 30 DAY)),
('春季特惠券', '春季露营活动专享优惠', 'join_discount', 25.00, 120.00, 800, 320, 180, NOW(), DATE_ADD(NOW(), INTERVAL 45 DAY)),
('周末狂欢券', '周末露营活动专用', 'join_discount', 35.00, 150.00, 600, 240, 95, NOW(), DATE_ADD(NOW(), INTERVAL 60 DAY));

-- =====================================================
-- 5. 插入测试用户优惠券数据（如果users表存在）
-- =====================================================
-- 注意：这里需要确保users表中有对应的用户ID
INSERT IGNORE INTO `user_camping_coupons` (`user_id`, `coupon_id`, `claimed_at`, `used_at`, `status`) VALUES
-- 用户1的优惠券
(1, 1, DATE_SUB(NOW(), INTERVAL 5 DAY), DATE_SUB(NOW(), INTERVAL 2 DAY), 'used'),
(1, 2, DATE_SUB(NOW(), INTERVAL 3 DAY), NULL, 'claimed'),
(1, 3, DATE_SUB(NOW(), INTERVAL 1 DAY), NULL, 'claimed'),

-- 用户2的优惠券
(2, 1, DATE_SUB(NOW(), INTERVAL 7 DAY), NULL, 'claimed'),
(2, 4, DATE_SUB(NOW(), INTERVAL 4 DAY), DATE_SUB(NOW(), INTERVAL 1 DAY), 'used'),

-- 用户3的优惠券
(3, 2, DATE_SUB(NOW(), INTERVAL 6 DAY), NULL, 'claimed'),
(3, 5, DATE_SUB(NOW(), INTERVAL 2 DAY), NULL, 'claimed'),

-- 用户4的优惠券
(4, 1, DATE_SUB(NOW(), INTERVAL 10 DAY), DATE_SUB(NOW(), INTERVAL 5 DAY), 'used'),
(4, 3, DATE_SUB(NOW(), INTERVAL 8 DAY), NULL, 'claimed'),
(4, 4, DATE_SUB(NOW(), INTERVAL 3 DAY), NULL, 'claimed'),

-- 用户5的优惠券
(5, 2, DATE_SUB(NOW(), INTERVAL 9 DAY), DATE_SUB(NOW(), INTERVAL 4 DAY), 'used'),
(5, 5, DATE_SUB(NOW(), INTERVAL 1 DAY), NULL, 'claimed');

-- =====================================================
-- 6. 更新优惠券统计数据
-- =====================================================
-- 更新已领取数量
UPDATE `camping_coupons` SET 
    `claimed_quantity` = (
        SELECT COUNT(*) 
        FROM `user_camping_coupons` 
        WHERE `user_camping_coupons`.`coupon_id` = `camping_coupons`.`id`
    );

-- 更新已使用数量
UPDATE `camping_coupons` SET 
    `used_quantity` = (
        SELECT COUNT(*) 
        FROM `user_camping_coupons` 
        WHERE `user_camping_coupons`.`coupon_id` = `camping_coupons`.`id` 
        AND `user_camping_coupons`.`status` = 'used'
    );

-- =====================================================
-- 7. 创建触发器自动更新统计数据
-- =====================================================
DELIMITER $$

-- 插入用户优惠券时更新统计
CREATE TRIGGER IF NOT EXISTS `update_coupon_claimed_count_insert`
AFTER INSERT ON `user_camping_coupons`
FOR EACH ROW
BEGIN
    UPDATE `camping_coupons` 
    SET `claimed_quantity` = `claimed_quantity` + 1
    WHERE `id` = NEW.`coupon_id`;
END$$

-- 更新用户优惠券状态时更新统计
CREATE TRIGGER IF NOT EXISTS `update_coupon_used_count_update`
AFTER UPDATE ON `user_camping_coupons`
FOR EACH ROW
BEGIN
    -- 如果状态从非used变为used，增加used_quantity
    IF OLD.`status` != 'used' AND NEW.`status` = 'used' THEN
        UPDATE `camping_coupons` 
        SET `used_quantity` = `used_quantity` + 1
        WHERE `id` = NEW.`coupon_id`;
    END IF;
    
    -- 如果状态从used变为非used，减少used_quantity
    IF OLD.`status` = 'used' AND NEW.`status` != 'used' THEN
        UPDATE `camping_coupons` 
        SET `used_quantity` = `used_quantity` - 1
        WHERE `id` = NEW.`coupon_id`;
    END IF;
END$$

-- 删除用户优惠券时更新统计
CREATE TRIGGER IF NOT EXISTS `update_coupon_count_delete`
AFTER DELETE ON `user_camping_coupons`
FOR EACH ROW
BEGIN
    UPDATE `camping_coupons` 
    SET `claimed_quantity` = `claimed_quantity` - 1
    WHERE `id` = OLD.`coupon_id`;
    
    IF OLD.`status` = 'used' THEN
        UPDATE `camping_coupons` 
        SET `used_quantity` = `used_quantity` - 1
        WHERE `id` = OLD.`coupon_id`;
    END IF;
END$$

DELIMITER ;

SET FOREIGN_KEY_CHECKS = 1;

-- =====================================================
-- 执行完成提示
-- =====================================================
SELECT 'Coupon management tables created successfully!' as message;
