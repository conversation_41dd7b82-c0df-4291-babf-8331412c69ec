<?php
/**
 * Vue客服系统 - 图片上传接口
 * 使用Cloudinary服务上传图片
 */

header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// 处理预检请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

// 只允许POST请求
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => '只允许POST请求']);
    exit;
}

// Cloudinary配置
$cloudinary_config = [
    'cloud_name' => 'dwcauq0wy',
    'api_key' => '965165511998959',
    'api_secret' => 'JYnkxTIAAC3GLuf3u6iiQpfqfMA',
    'upload_preset' => 'chat_app_preset'
];

try {
    // 检查是否有文件上传
    if (!isset($_FILES['image']) || $_FILES['image']['error'] !== UPLOAD_ERR_OK) {
        echo json_encode([
            'success' => false,
            'message' => '没有文件上传或上传失败'
        ]);
        exit;
    }

    $file = $_FILES['image'];
    
    // 验证文件类型
    $allowed_types = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
    if (!in_array($file['type'], $allowed_types)) {
        echo json_encode([
            'success' => false,
            'message' => '不支持的文件类型，只支持 JPEG, PNG, GIF, WebP'
        ]);
        exit;
    }
    
    // 验证文件大小 (最大5MB)
    $max_size = 5 * 1024 * 1024; // 5MB
    if ($file['size'] > $max_size) {
        echo json_encode([
            'success' => false,
            'message' => '文件大小超过限制，最大支持5MB'
        ]);
        exit;
    }
    
    // 生成唯一文件名
    $file_extension = pathinfo($file['name'], PATHINFO_EXTENSION);
    $unique_filename = 'customer_service_' . date('YmdHis') . '_' . rand(1000, 9999) . '.' . $file_extension;
    
    // 准备上传到Cloudinary的数据
    $upload_data = [
        'file' => new CURLFile($file['tmp_name'], $file['type'], $unique_filename),
        'upload_preset' => $cloudinary_config['upload_preset'],
        'folder' => 'customer_service',
        'public_id' => pathinfo($unique_filename, PATHINFO_FILENAME),
        'overwrite' => true,
        'resource_type' => 'image'
    ];
    
    // 上传到Cloudinary
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, "https://api.cloudinary.com/v1_1/{$cloudinary_config['cloud_name']}/image/upload");
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, $upload_data);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($ch, CURLOPT_TIMEOUT, 30);
    
    $response = curl_exec($ch);
    $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $curl_error = curl_error($ch);
    curl_close($ch);
    
    if ($curl_error) {
        throw new Exception("CURL错误: " . $curl_error);
    }
    
    if ($http_code !== 200) {
        throw new Exception("Cloudinary上传失败，HTTP状态码: " . $http_code);
    }
    
    $cloudinary_response = json_decode($response, true);
    
    if (!$cloudinary_response || !isset($cloudinary_response['secure_url'])) {
        throw new Exception("Cloudinary响应格式错误");
    }
    
    // 返回成功响应
    echo json_encode([
        'success' => true,
        'message' => '图片上传成功',
        'data' => [
            'url' => $cloudinary_response['secure_url'],
            'public_id' => $cloudinary_response['public_id'],
            'width' => $cloudinary_response['width'] ?? 0,
            'height' => $cloudinary_response['height'] ?? 0,
            'format' => $cloudinary_response['format'] ?? '',
            'bytes' => $cloudinary_response['bytes'] ?? 0,
            'original_filename' => $file['name']
        ]
    ]);
    
} catch (Exception $e) {
    error_log("图片上传失败: " . $e->getMessage());
    
    echo json_encode([
        'success' => false,
        'message' => '图片上传失败: ' . $e->getMessage()
    ]);
}

/**
 * 备用上传方法 - 本地存储
 * 如果Cloudinary不可用，可以使用本地存储
 */
function uploadToLocal($file) {
    $upload_dir = '../../uploads/customer_service/';
    
    // 创建上传目录
    if (!is_dir($upload_dir)) {
        mkdir($upload_dir, 0755, true);
    }
    
    // 生成唯一文件名
    $file_extension = pathinfo($file['name'], PATHINFO_EXTENSION);
    $unique_filename = 'cs_' . date('YmdHis') . '_' . rand(1000, 9999) . '.' . $file_extension;
    $file_path = $upload_dir . $unique_filename;
    
    // 移动文件
    if (move_uploaded_file($file['tmp_name'], $file_path)) {
        // 构建访问URL
        $base_url = (isset($_SERVER['HTTPS']) ? 'https' : 'http') . '://' . $_SERVER['HTTP_HOST'];
        $file_url = $base_url . '/frontend/uploads/customer_service/' . $unique_filename;
        
        return [
            'success' => true,
            'url' => $file_url,
            'filename' => $unique_filename,
            'size' => $file['size']
        ];
    } else {
        throw new Exception('文件保存失败');
    }
}
?>
