<?php
session_start();

// 检查用户是否已登录
if (!isset($_SESSION['user_id'])) {
    header('Location: ../login/index.php');
    exit;
}
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <meta name="theme-color" content="#FFFFFF">
    <title>组局 - 趣玩星球</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'PingFang SC', 'Helvetica Neue', Arial, sans-serif;
        }
        
        body {
            background-color: #f8f9fa;
            color: #333333;
            line-height: 1.6;
            min-height: 100vh;
            position: relative;
            padding-bottom: 60px;
        }
        
        .container {
            padding: 20px;
            text-align: center;
        }
        
        h1 {
            margin-top: 100px;
            margin-bottom: 20px;
            color: #1E90FF;
        }
        
        p {
            margin-bottom: 30px;
            color: #666;
        }
        
        .btn {
            display: inline-block;
            padding: 10px 20px;
            background-color: #1E90FF;
            color: white;
            text-decoration: none;
            border-radius: 5px;
            font-weight: bold;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
        }
        
        /* 底部导航栏样式 */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            background-color: white;
            display: flex;
            justify-content: space-around;
            padding: 8px 0;
            box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.05);
            z-index: 100;
        }
        
        .nav-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 5px 0;
            width: 20%;
            text-decoration: none;
        }
        
        .nav-item i {
            font-size: 24px;
            margin-bottom: 3px;
            color: #999;
        }
        
        .nav-item span {
            font-size: 12px;
            color: #999;
        }
        
        .nav-item.active i,
        .nav-item.active span {
            color: #1E90FF;
        }
    </style>
    <!-- 引入字体图标库 -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <div class="container">
        <h1>组局功能</h1>
        <p>该功能正在开发中，敬请期待！</p>
        <a href="../home/<USER>" class="btn">返回首页</a>
    </div>
    
    <!-- 底部导航栏 -->
    <div class="bottom-nav">
        <a href="../home/<USER>" class="nav-item">
            <i class="fas fa-home"></i>
            <span>首页</span>
        </a>
        <a href="index.php" class="nav-item active">
            <i class="fas fa-calendar-alt"></i>
            <span>组局</span>
        </a>
        <a href="../guide/index.php" class="nav-item">
            <i class="fas fa-book-open"></i>
            <span>攻略</span>
        </a>
        <a href="../message/index.php" class="nav-item">
            <i class="fas fa-comment"></i>
            <span>消息</span>
        </a>
        <a href="../profile/index.php" class="nav-item">
            <i class="fas fa-user"></i>
            <span>我的</span>
        </a>
    </div>
</body>
</html>
