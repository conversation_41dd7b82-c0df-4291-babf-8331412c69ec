<?php
/**
 * 登录安全检测API
 * 检测IP、设备、浏览器等信息判断是否为可信登录
 */

session_start();
header('Content-Type: application/json; charset=utf-8');

// 错误处理
error_reporting(0);
ini_set('display_errors', 0);

// 捕获所有错误并返回JSON
set_error_handler(function($severity, $message, $file, $line) {
    echo json_encode(['success' => false, 'message' => '系统错误，请稍后重试']);
    exit;
});

set_exception_handler(function($exception) {
    echo json_encode(['success' => false, 'message' => '系统错误，请稍后重试']);
    exit;
});

// 数据库连接配置
$db_config = [
    'host' => 'localhost',
    'username' => 'quwanplanet',
    'password' => 'nJmJm23FB4Xn6Fc3',
    'dbname' => 'quwanplanet',
    'port' => 3306,
    'charset' => 'utf8mb4'
];

try {
    $pdo = new PDO(
        "mysql:host={$db_config['host']};port={$db_config['port']};dbname={$db_config['dbname']};charset={$db_config['charset']}",
        $db_config['username'],
        $db_config['password'],
        [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
            PDO::ATTR_EMULATE_PREPARES => false
        ]
    );
} catch (PDOException $e) {
    echo json_encode(['success' => false, 'message' => '数据库连接失败']);
    exit;
}

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode(['success' => false, 'message' => '请求方法错误']);
    exit;
}

$input = json_decode(file_get_contents('php://input'), true);
$identifier = trim($input['identifier'] ?? '');

if (empty($identifier)) {
    echo json_encode(['success' => false, 'message' => '请输入手机号或趣玩ID']);
    exit;
}

// 查找用户
$user = null;
if (preg_match('/^\d{11}$/', $identifier)) {
    // 手机号格式
    $stmt = $pdo->prepare("SELECT * FROM users WHERE phone = ?");
    $stmt->execute([$identifier]);
    $user = $stmt->fetch();
} elseif (preg_match('/^\d{7,9}$/', $identifier)) {
    // 趣玩ID格式
    $stmt = $pdo->prepare("SELECT * FROM users WHERE quwan_id = ?");
    $stmt->execute([$identifier]);
    $user = $stmt->fetch();
}

if (!$user) {
    echo json_encode(['success' => false, 'message' => '用户不存在']);
    exit;
}

// 检查用户状态
if ($user['status'] === 'banned') {
    echo json_encode(['success' => false, 'message' => '账户已被封禁，请联系客服']);
    exit;
}

// 获取当前环境信息
$current_ip = $_SERVER['REMOTE_ADDR'];
$current_user_agent = $_SERVER['HTTP_USER_AGENT'];
$current_device_info = parseUserAgent($current_user_agent);

// 进行安全检测
$security_result = performSecurityCheck($pdo, $user['id'], $current_ip, $current_user_agent, $current_device_info);

// 返回结果
echo json_encode([
    'success' => true,
    'user' => [
        'id' => $user['id'],
        'username' => $user['username'],
        'quwan_id' => $user['quwan_id'],
        'avatar' => $user['avatar'] ?? 'https://s1.imagehub.cc/images/2025/04/26/f5c2f26c4564aabcbb56c5fd30f1d652.jpg'
    ],
    'security' => $security_result
]);

/**
 * 执行安全检测
 */
function performSecurityCheck($pdo, $user_id, $current_ip, $current_user_agent, $current_device_info) {
    $risks = [];
    $risk_level = 'low'; // low, medium, high

    // 1. IP检测 - 检查是否为常用IP
    $ip_check = checkTrustedIP($pdo, $user_id, $current_ip);
    if (!$ip_check['trusted']) {
        $risks[] = 'unknown_ip';
        $risk_level = 'medium';
    }

    // 2. 设备检测 - 检查是否为常用设备
    $device_check = checkTrustedDevice($pdo, $user_id, $current_device_info);
    if (!$device_check['trusted']) {
        $risks[] = 'unknown_device';
        $risk_level = 'medium';
    }

    // 3. 浏览器检测 - 检查是否为常用浏览器
    $browser_check = checkTrustedBrowser($pdo, $user_id, $current_device_info['browser']);
    if (!$browser_check['trusted']) {
        $risks[] = 'unknown_browser';
        if ($risk_level === 'low') $risk_level = 'medium';
    }

    // 4. 地理位置检测（基于IP）
    $location_check = checkLocationRisk($pdo, $user_id, $current_ip);
    if ($location_check['risk']) {
        $risks[] = 'location_risk';
        $risk_level = 'high';
    }

    // 5. 登录频率检测
    $frequency_check = checkLoginFrequency($pdo, $user_id);
    if ($frequency_check['risk']) {
        $risks[] = 'high_frequency';
        $risk_level = 'high';
    }

    return [
        'risk_level' => $risk_level,
        'risks' => $risks,
        'trusted' => $risk_level === 'low',
        'details' => [
            'ip' => $ip_check,
            'device' => $device_check,
            'browser' => $browser_check,
            'location' => $location_check,
            'frequency' => $frequency_check
        ]
    ];
}

/**
 * 检查可信IP
 */
function checkTrustedIP($pdo, $user_id, $current_ip) {
    // 查询最近30天的登录记录
    $stmt = $pdo->prepare("
        SELECT ip_address, COUNT(*) as count, MAX(login_time) as last_used
        FROM login_logs
        WHERE user_id = ? AND status = 'success'
        AND login_time >= DATE_SUB(NOW(), INTERVAL 30 DAY)
        GROUP BY ip_address
        ORDER BY count DESC, last_used DESC
    ");
    $stmt->execute([$user_id]);
    $ip_history = $stmt->fetchAll();

    foreach ($ip_history as $record) {
        if ($record['ip_address'] === $current_ip && $record['count'] >= 3) {
            return ['trusted' => true, 'reason' => 'frequent_use', 'last_used' => $record['last_used']];
        }
    }

    return ['trusted' => false, 'reason' => 'new_ip', 'history_count' => count($ip_history)];
}

/**
 * 检查可信设备
 */
function checkTrustedDevice($pdo, $user_id, $current_device_info) {
    $device_fingerprint = generateDeviceFingerprint($current_device_info);

    $stmt = $pdo->prepare("
        SELECT device_fingerprint, COUNT(*) as count, MAX(login_time) as last_used
        FROM login_logs
        WHERE user_id = ? AND status = 'success'
        AND login_time >= DATE_SUB(NOW(), INTERVAL 60 DAY)
        GROUP BY device_fingerprint
        HAVING count >= 2
        ORDER BY count DESC, last_used DESC
    ");
    $stmt->execute([$user_id]);
    $device_history = $stmt->fetchAll();

    foreach ($device_history as $record) {
        if ($record['device_fingerprint'] === $device_fingerprint) {
            return ['trusted' => true, 'reason' => 'known_device', 'last_used' => $record['last_used']];
        }
    }

    return ['trusted' => false, 'reason' => 'new_device', 'fingerprint' => $device_fingerprint];
}

/**
 * 检查可信浏览器
 */
function checkTrustedBrowser($pdo, $user_id, $browser_info) {
    $stmt = $pdo->prepare("
        SELECT user_agent, COUNT(*) as count
        FROM login_logs
        WHERE user_id = ? AND status = 'success'
        AND login_time >= DATE_SUB(NOW(), INTERVAL 30 DAY)
        AND user_agent LIKE ?
        GROUP BY user_agent
        HAVING count >= 2
    ");
    $stmt->execute([$user_id, '%' . $browser_info . '%']);
    $browser_history = $stmt->fetchAll();

    return [
        'trusted' => count($browser_history) > 0,
        'reason' => count($browser_history) > 0 ? 'known_browser' : 'new_browser',
        'history_count' => count($browser_history)
    ];
}

/**
 * 检查地理位置风险
 */
function checkLocationRisk($pdo, $user_id, $current_ip) {
    // 简单的地理位置检测（实际项目中可以使用IP地理位置API）
    $location = getLocationFromIP($current_ip);

    // 检查是否有异常的地理位置变化
    $stmt = $pdo->prepare("
        SELECT ip_address, login_time
        FROM login_logs
        WHERE user_id = ? AND status = 'success'
        ORDER BY login_time DESC
        LIMIT 5
    ");
    $stmt->execute([$user_id]);
    $recent_logins = $stmt->fetchAll();

    // 这里可以实现更复杂的地理位置风险检测逻辑
    return ['risk' => false, 'location' => $location];
}

/**
 * 检查登录频率
 */
function checkLoginFrequency($pdo, $user_id) {
    $stmt = $pdo->prepare("
        SELECT COUNT(*) as count
        FROM login_logs
        WHERE user_id = ?
        AND login_time >= DATE_SUB(NOW(), INTERVAL 1 HOUR)
    ");
    $stmt->execute([$user_id]);
    $recent_count = $stmt->fetchColumn();

    return [
        'risk' => $recent_count > 10, // 1小时内超过10次登录尝试
        'count' => $recent_count
    ];
}

/**
 * 解析User Agent
 */
function parseUserAgent($user_agent) {
    $device = 'Unknown';
    $os = 'Unknown';
    $browser = 'Unknown';

    // 检测设备类型
    if (preg_match('/Mobile|Android|iPhone|iPad/', $user_agent)) {
        $device = 'Mobile';
    } else {
        $device = 'Desktop';
    }

    // 检测操作系统
    if (preg_match('/Windows NT ([0-9.]+)/', $user_agent, $matches)) {
        $os = 'Windows ' . $matches[1];
    } elseif (preg_match('/Mac OS X ([0-9_]+)/', $user_agent, $matches)) {
        $os = 'macOS ' . str_replace('_', '.', $matches[1]);
    } elseif (preg_match('/Android ([0-9.]+)/', $user_agent, $matches)) {
        $os = 'Android ' . $matches[1];
    } elseif (preg_match('/iPhone OS ([0-9_]+)/', $user_agent, $matches)) {
        $os = 'iOS ' . str_replace('_', '.', $matches[1]);
    }

    // 检测浏览器
    if (preg_match('/Chrome\/([0-9.]+)/', $user_agent, $matches)) {
        $browser = 'Chrome ' . $matches[1];
    } elseif (preg_match('/Firefox\/([0-9.]+)/', $user_agent, $matches)) {
        $browser = 'Firefox ' . $matches[1];
    } elseif (preg_match('/Safari\/([0-9.]+)/', $user_agent, $matches)) {
        $browser = 'Safari ' . $matches[1];
    } elseif (preg_match('/Edge\/([0-9.]+)/', $user_agent, $matches)) {
        $browser = 'Edge ' . $matches[1];
    }

    return [
        'device' => $device,
        'os' => $os,
        'browser' => $browser,
        'full' => $user_agent
    ];
}

/**
 * 生成设备指纹
 */
function generateDeviceFingerprint($device_info) {
    return md5($device_info['os'] . '|' . $device_info['browser'] . '|' . $device_info['device']);
}

/**
 * 根据IP获取地理位置（简化版）
 */
function getLocationFromIP($ip) {
    // 这里可以集成第三方IP地理位置服务
    // 目前返回简化的结果
    if ($ip === '127.0.0.1' || $ip === '::1') {
        return '本地';
    }
    return '未知地区';
}
?>
