<?php
/**
 * 新版用户管理页面
 * 趣玩星球管理后台 v2.0
 */

session_start();

// 简单的登录检查
if (!isset($_SESSION['admin_id'])) {
    header('Location: login.php');
    exit;
}

$admin_name = $_SESSION['admin_name'] ?? '管理员';

// 获取搜索参数
$search = trim($_GET['search'] ?? '');
$status_filter = $_GET['status'] ?? 'all';
$show_results = !empty($search);

// 如果有搜索条件，尝试连接数据库
$users = [];
$total = 0;
$error_message = '';

if ($show_results) {
    try {
        require_once 'db_config.php';
        $pdo = getDbConnection();

        // 构建查询
        $where_conditions = [];
        $params = [];

        // 搜索条件
        $where_conditions[] = "(u.username LIKE ? OR u.phone LIKE ? OR u.email LIKE ? OR u.quwan_id LIKE ? OR u.id = ?)";
        $search_param = "%$search%";
        $params = [$search_param, $search_param, $search_param, $search_param, $search];

        // 状态筛选
        if ($status_filter !== 'all') {
            if ($status_filter === 'verified') {
                $where_conditions[] = "u.is_verified = 1";
            } elseif ($status_filter === 'unverified') {
                $where_conditions[] = "(u.is_verified = 0 OR u.is_verified IS NULL)";
            }
        }

        $where_clause = 'WHERE ' . implode(' AND ', $where_conditions);

        // 获取用户列表
        $sql = "
            SELECT
                u.*,
                rv.verification_status,
                rv.real_name,
                rv.verified_at
            FROM users u
            LEFT JOIN realname_verification rv ON u.id = rv.user_id
            $where_clause
            ORDER BY u.created_at DESC
            LIMIT 50
        ";

        $stmt = $pdo->prepare($sql);
        $stmt->execute($params);
        $users = $stmt->fetchAll(PDO::FETCH_ASSOC);
        $total = count($users);

    } catch (Exception $e) {
        $error_message = '数据库查询错误：' . $e->getMessage();
    }
}

// 状态标签函数
function getVerificationBadge($user) {
    if ($user['verification_status'] === 'approved') {
        return '<span class="badge success"><i class="fas fa-check-circle"></i> 已认证</span>';
    } elseif ($user['verification_status'] === 'pending') {
        return '<span class="badge warning"><i class="fas fa-clock"></i> 审核中</span>';
    } elseif ($user['verification_status'] === 'rejected') {
        return '<span class="badge danger"><i class="fas fa-times-circle"></i> 已拒绝</span>';
    } else {
        return '<span class="badge secondary"><i class="fas fa-user"></i> 未认证</span>';
    }
}
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>用户管理 - 趣玩星球</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        :root {
            --primary-color: #40E0D0;
            --primary-light: #AFFBF2;
            --success-color: #10B981;
            --warning-color: #F59E0B;
            --error-color: #EF4444;
            --gray-100: #F3F4F6;
            --gray-200: #E5E7EB;
            --gray-600: #4B5563;
            --gray-800: #1F2937;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            min-height: 100vh;
        }

        .top-nav {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 16px;
            padding: 16px 24px;
            margin-bottom: 24px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
        }

        .brand {
            display: flex;
            align-items: center;
            gap: 12px;
            font-size: 1.25rem;
            font-weight: 700;
            color: var(--gray-800);
        }

        .brand-icon {
            width: 32px;
            height: 32px;
            background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
        }

        .breadcrumb {
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 14px;
            color: var(--gray-600);
        }

        .breadcrumb a {
            color: var(--primary-color);
            text-decoration: none;
        }

        .main-content {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 16px;
            padding: 32px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }

        .search-section {
            background: var(--gray-100);
            border-radius: 12px;
            padding: 24px;
            margin-bottom: 24px;
        }

        .search-form {
            display: grid;
            grid-template-columns: 1fr 200px auto;
            gap: 16px;
            align-items: end;
        }

        .form-group {
            display: flex;
            flex-direction: column;
            gap: 8px;
        }

        .form-group label {
            font-size: 14px;
            font-weight: 600;
            color: var(--gray-800);
        }

        .form-group input,
        .form-group select {
            padding: 12px;
            border: 2px solid var(--gray-200);
            border-radius: 8px;
            font-size: 14px;
            background: white;
            transition: all 0.3s ease;
        }

        .form-group input:focus,
        .form-group select:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(64, 224, 208, 0.1);
        }

        .btn {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            padding: 12px 20px;
            border: none;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
        }

        .btn-primary {
            background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
            color: white;
        }

        .btn-secondary {
            background: var(--gray-200);
            color: var(--gray-800);
        }

        .btn:hover {
            transform: translateY(-2px);
            text-decoration: none;
        }

        .results-section {
            margin-top: 24px;
        }

        .results-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
            padding-bottom: 16px;
            border-bottom: 2px solid var(--gray-100);
        }

        .table-container {
            overflow-x: auto;
            border-radius: 12px;
            border: 1px solid var(--gray-200);
        }

        .user-table {
            width: 100%;
            border-collapse: collapse;
            background: white;
        }

        .user-table th {
            background: var(--gray-100);
            padding: 16px;
            text-align: left;
            font-size: 14px;
            font-weight: 600;
            color: var(--gray-800);
            border-bottom: 2px solid var(--gray-200);
        }

        .user-table td {
            padding: 16px;
            border-bottom: 1px solid var(--gray-100);
            vertical-align: middle;
        }

        .user-table tr:hover {
            background: rgba(64, 224, 208, 0.02);
        }

        .user-info {
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .user-avatar {
            width: 40px;
            height: 40px;
            background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: 600;
        }

        .badge {
            display: inline-flex;
            align-items: center;
            gap: 4px;
            padding: 4px 8px;
            border-radius: 6px;
            font-size: 12px;
            font-weight: 600;
        }

        .badge.success {
            background: rgba(16, 185, 129, 0.1);
            color: var(--success-color);
        }

        .badge.warning {
            background: rgba(245, 158, 11, 0.1);
            color: var(--warning-color);
        }

        .badge.danger {
            background: rgba(239, 68, 68, 0.1);
            color: var(--error-color);
        }

        .badge.secondary {
            background: var(--gray-100);
            color: var(--gray-600);
        }

        .welcome-section {
            text-align: center;
            padding: 48px 24px;
        }

        .welcome-icon {
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 24px;
            font-size: 32px;
            color: white;
        }

        .error-message {
            background: rgba(239, 68, 68, 0.1);
            color: var(--error-color);
            padding: 16px;
            border-radius: 8px;
            margin-bottom: 24px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        @media (max-width: 768px) {
            .container {
                padding: 16px;
            }

            .search-form {
                grid-template-columns: 1fr;
                gap: 12px;
            }

            .top-nav {
                flex-direction: column;
                gap: 16px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 顶部导航 -->
        <nav class="top-nav">
            <div class="brand">
                <div class="brand-icon">
                    <i class="fas fa-star"></i>
                </div>
                <span>用户管理</span>
            </div>

            <div class="breadcrumb">
                <a href="new_dashboard.php">主菜单</a>
                <i class="fas fa-chevron-right"></i>
                <span>用户管理</span>
            </div>
        </nav>

        <!-- 主内容 -->
        <main class="main-content">
            <!-- 搜索区域 -->
            <div class="search-section">
                <h2 style="margin-bottom: 16px; color: var(--gray-800);">
                    <i class="fas fa-search"></i> 用户查询系统
                </h2>
                <p style="margin-bottom: 20px; color: var(--gray-600); font-size: 14px;">
                    为保护用户隐私，请输入具体的搜索条件查询用户信息
                </p>

                <form method="GET" class="search-form">
                    <div class="form-group">
                        <label for="search">搜索条件</label>
                        <input type="text"
                               id="search"
                               name="search"
                               value="<?php echo htmlspecialchars($search); ?>"
                               placeholder="请输入用户名、手机号、邮箱、趣玩ID或用户ID"
                               required>
                    </div>

                    <div class="form-group">
                        <label for="status">认证状态</label>
                        <select name="status" id="status">
                            <option value="all" <?php echo $status_filter === 'all' ? 'selected' : ''; ?>>全部状态</option>
                            <option value="verified" <?php echo $status_filter === 'verified' ? 'selected' : ''; ?>>已认证</option>
                            <option value="unverified" <?php echo $status_filter === 'unverified' ? 'selected' : ''; ?>>未认证</option>
                        </select>
                    </div>

                    <div style="display: flex; gap: 8px;">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-search"></i>
                            查询用户
                        </button>
                        <button type="button" class="btn btn-secondary" onclick="resetSearch()">
                            <i class="fas fa-undo"></i>
                            重置
                        </button>
                    </div>
                </form>
            </div>

            <?php if ($error_message): ?>
                <div class="error-message">
                    <i class="fas fa-exclamation-triangle"></i>
                    <?php echo htmlspecialchars($error_message); ?>
                </div>
            <?php endif; ?>

            <?php if ($show_results): ?>
                <?php if (!empty($users)): ?>
                    <!-- 搜索结果 -->
                    <div class="results-section">
                        <div class="results-header">
                            <h3><i class="fas fa-list"></i> 搜索结果</h3>
                            <div>找到 <strong><?php echo $total; ?></strong> 个匹配的用户</div>
                        </div>

                        <div class="table-container">
                            <table class="user-table">
                                <thead>
                                    <tr>
                                        <th>用户信息</th>
                                        <th>趣玩ID</th>
                                        <th>联系方式</th>
                                        <th>认证状态</th>
                                        <th>注册时间</th>
                                        <th>操作</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($users as $user): ?>
                                        <tr>
                                            <td>
                                                <div class="user-info">
                                                    <div class="user-avatar">
                                                        <?php echo mb_substr($user['username'], 0, 1, 'UTF-8'); ?>
                                                    </div>
                                                    <div>
                                                        <div style="font-weight: 600;"><?php echo htmlspecialchars($user['username']); ?></div>
                                                        <div style="font-size: 12px; color: var(--gray-600);">ID: <?php echo $user['id']; ?></div>
                                                    </div>
                                                </div>
                                            </td>
                                            <td>
                                                <strong style="color: var(--primary-color);">
                                                    <?php echo htmlspecialchars($user['quwan_id']); ?>
                                                </strong>
                                            </td>
                                            <td>
                                                <div><?php echo htmlspecialchars($user['phone'] ?? '-'); ?></div>
                                                <div style="font-size: 12px; color: var(--gray-600);"><?php echo htmlspecialchars($user['email'] ?? '-'); ?></div>
                                            </td>
                                            <td><?php echo getVerificationBadge($user); ?></td>
                                            <td><?php echo date('Y-m-d H:i', strtotime($user['created_at'])); ?></td>
                                            <td>
                                                <a href="user_detail.php?id=<?php echo $user['id']; ?>" class="btn btn-primary" style="padding: 6px 12px; font-size: 12px;">
                                                    <i class="fas fa-eye"></i> 查看详情
                                                </a>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                <?php else: ?>
                    <!-- 无结果提示 -->
                    <div class="welcome-section">
                        <i class="fas fa-search-minus" style="font-size: 48px; color: var(--gray-600); margin-bottom: 16px;"></i>
                        <h3>未找到匹配的用户</h3>
                        <p style="color: var(--gray-600);">请检查搜索条件是否正确，或尝试使用其他关键词</p>
                    </div>
                <?php endif; ?>
            <?php else: ?>
                <!-- 默认提示 -->
                <div class="welcome-section">
                    <div class="welcome-icon">
                        <i class="fas fa-shield-alt"></i>
                    </div>
                    <h3>用户隐私保护</h3>
                    <p style="color: var(--gray-600); margin-bottom: 24px;">为了保护用户隐私安全，系统采用按需查询模式</p>
                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(150px, 1fr)); gap: 16px; max-width: 600px; margin: 0 auto;">
                        <div style="text-align: center; padding: 16px; background: var(--gray-100); border-radius: 8px;">
                            <i class="fas fa-lock" style="font-size: 24px; color: var(--primary-color); margin-bottom: 8px;"></i>
                            <div style="font-size: 14px; font-weight: 600;">数据加密存储</div>
                        </div>
                        <div style="text-align: center; padding: 16px; background: var(--gray-100); border-radius: 8px;">
                            <i class="fas fa-eye-slash" style="font-size: 24px; color: var(--primary-color); margin-bottom: 8px;"></i>
                            <div style="font-size: 14px; font-weight: 600;">按需查询显示</div>
                        </div>
                        <div style="text-align: center; padding: 16px; background: var(--gray-100); border-radius: 8px;">
                            <i class="fas fa-user-shield" style="font-size: 24px; color: var(--primary-color); margin-bottom: 8px;"></i>
                            <div style="font-size: 14px; font-weight: 600;">权限分级管理</div>
                        </div>
                        <div style="text-align: center; padding: 16px; background: var(--gray-100); border-radius: 8px;">
                            <i class="fas fa-history" style="font-size: 24px; color: var(--primary-color); margin-bottom: 8px;"></i>
                            <div style="font-size: 14px; font-weight: 600;">操作日志记录</div>
                        </div>
                    </div>
                </div>
            <?php endif; ?>
        </main>
    </div>

    <script>
        function resetSearch() {
            document.getElementById('search').value = '';
            document.getElementById('status').value = 'all';
            window.location.href = 'new_user_management.php';
        }

        // 搜索表单验证
        document.querySelector('form').addEventListener('submit', function(e) {
            const searchInput = document.getElementById('search');
            if (searchInput.value.trim().length < 2) {
                e.preventDefault();
                alert('请输入至少2个字符进行搜索');
                searchInput.focus();
                return false;
            }
        });
    </script>
</body>
</html>
