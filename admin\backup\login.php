<?php
// 设置字符编码
header('Content-Type: text/html; charset=UTF-8');
ini_set('default_charset', 'UTF-8');

session_start();

// 如果已登录，跳转到审核页面
if (isset($_SESSION['admin_id'])) {
    header('Location: verification_list.php');
    exit;
}

// 直接引用当前目录的数据库配置文件
require_once 'db_config.php';

$error_message = '';

// 处理登录
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $login_field = trim($_POST['login_field'] ?? '');
    $password = $_POST['password'] ?? '';
    $captcha = $_POST['captcha'] ?? '';

    // 验证验证码
    if (empty($captcha) || strtoupper($captcha) !== ($_SESSION['captcha'] ?? '')) {
        $error_message = '验证码错误';
    } elseif (empty($login_field) || empty($password)) {
        $error_message = '请输入工号/邮箱和密码';
    } else {
        try {
            $pdo = getDbConnection();

            // 查询管理员账号
            $stmt = $pdo->prepare("
                SELECT * FROM admin_users
                WHERE (employee_id = ? OR email = ?) AND status = 1
            ");
            $stmt->execute([$login_field, $login_field]);
            $admin = $stmt->fetch(PDO::FETCH_ASSOC);

            // 检查密码（支持明文和哈希两种方式）
            $password_valid = false;
            if (password_verify($password, $admin['password'])) {
                // 哈希密码验证成功
                $password_valid = true;
            } elseif ($password === $admin['password']) {
                // 明文密码验证成功（临时调试用）
                $password_valid = true;
            }

            if ($admin && $password_valid) {
                // 登录成功
                $_SESSION['admin_logged_in'] = true;
                $_SESSION['admin_id'] = $admin['id'];
                $_SESSION['admin_name'] = $admin['name'] ?? $admin['nickname'] ?? $admin['username'];
                $_SESSION['admin_role'] = $admin['role_name'] ?? '审核员';
                $_SESSION['admin_employee_id'] = $admin['employee_id'];
                $_SESSION['admin_department'] = $admin['department'] ?? '总经办';

                // 更新最后登录时间
                $stmt = $pdo->prepare("UPDATE admin_users SET last_login = NOW() WHERE id = ?");
                $stmt->execute([$admin['id']]);

                header('Location: main_menu.php');
                exit;
            } else {
                $error_message = '工号/邮箱或密码错误';
                // 调试信息（生产环境请删除）
                if ($admin) {
                    error_log("登录失败 - 用户存在，密码不匹配。输入密码: $password, 数据库密码: " . $admin['password']);
                } else {
                    error_log("登录失败 - 用户不存在。登录字段: $login_field");
                }
            }
        } catch (PDOException $e) {
            $error_message = '系统错误，请稍后重试';
        }
    }
}
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>实名认证审核后台 - 登录</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .login-container {
            background: white;
            padding: 40px;
            border-radius: 15px;
            box-shadow: 0 15px 35px rgba(0,0,0,0.1);
            width: 100%;
            max-width: 400px;
        }

        .login-header {
            text-align: center;
            margin-bottom: 30px;
        }

        .login-header h1 {
            color: #333;
            margin-bottom: 10px;
        }

        .login-header p {
            color: #666;
            font-size: 14px;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            color: #333;
            font-weight: 500;
        }

        .input-group {
            position: relative;
        }

        .input-group i {
            position: absolute;
            left: 15px;
            top: 50%;
            transform: translateY(-50%);
            color: #999;
        }

        .input-group input {
            width: 100%;
            padding: 12px 15px 12px 45px;
            border: 2px solid #e1e5e9;
            border-radius: 8px;
            font-size: 16px;
            transition: border-color 0.3s;
        }

        .input-group input:focus {
            outline: none;
            border-color: #667eea;
        }

        .captcha-group {
            display: flex;
            gap: 10px;
            align-items: flex-end;
        }

        .captcha-group .input-group {
            flex: 1;
        }

        .captcha-image {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 5px;
        }

        .captcha-image img {
            width: 120px;
            height: 40px;
            border: 2px solid #e1e5e9;
            border-radius: 8px;
            cursor: pointer;
        }

        .refresh-captcha {
            background: #667eea;
            color: white;
            border: none;
            border-radius: 6px;
            padding: 6px 8px;
            cursor: pointer;
            font-size: 12px;
        }

        .login-btn {
            width: 100%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 15px;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: transform 0.3s;
        }

        .login-btn:hover {
            transform: translateY(-2px);
        }

        .error-message {
            background: #fff5f5;
            border: 1px solid #fed7d7;
            color: #e53e3e;
            padding: 12px;
            border-radius: 8px;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
        }

        .error-message i {
            margin-right: 8px;
        }

        .login-footer {
            text-align: center;
            margin-top: 20px;
            color: #666;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="login-header">
            <h1><i class="fas fa-user-check"></i> 实名认证审核</h1>
            <p>管理员登录</p>
        </div>

        <?php if ($error_message): ?>
            <div class="error-message">
                <i class="fas fa-exclamation-triangle"></i>
                <?php echo htmlspecialchars($error_message); ?>
            </div>
        <?php endif; ?>

        <form method="POST" action="">
            <div class="form-group">
                <label for="login_field">工号/邮箱</label>
                <div class="input-group">
                    <i class="fas fa-user"></i>
                    <input type="text" id="login_field" name="login_field"
                           placeholder="请输入工号或邮箱" required>
                </div>
            </div>

            <div class="form-group">
                <label for="password">密码</label>
                <div class="input-group">
                    <i class="fas fa-lock"></i>
                    <input type="password" id="password" name="password"
                           placeholder="请输入密码" required>
                </div>
            </div>

            <div class="form-group">
                <label for="captcha">验证码</label>
                <div class="captcha-group">
                    <div class="input-group">
                        <i class="fas fa-shield-alt"></i>
                        <input type="text" id="captcha" name="captcha"
                               placeholder="请输入验证码" required maxlength="4">
                    </div>
                    <div class="captcha-image">
                        <img src="captcha.php" alt="验证码" id="captchaImg" onclick="refreshCaptcha()">
                        <button type="button" class="refresh-captcha" onclick="refreshCaptcha()">
                            <i class="fas fa-sync-alt"></i>
                        </button>
                    </div>
                </div>
            </div>

            <button type="submit" class="login-btn">
                <i class="fas fa-sign-in-alt"></i>
                登录
            </button>
        </form>

        <div class="login-footer">
            <p>© 2024 趣玩星球实名认证审核系统</p>
        </div>
    </div>

    <script>
        function refreshCaptcha() {
            const captchaImg = document.getElementById('captchaImg');
            captchaImg.src = 'captcha.php?' + new Date().getTime();
        }

        document.addEventListener('DOMContentLoaded', function() {
            document.getElementById('login_field').focus();
        });
    </script>
</body>
</html>
