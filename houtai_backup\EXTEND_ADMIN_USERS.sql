-- =====================================================
-- 扩展admin_users表 - 兼容版本
-- 只有在列不存在时才添加
-- =====================================================

SET NAMES utf8mb4;

-- 检查并添加department_id列
SET @sql = CONCAT('ALTER TABLE admin_users ADD COLUMN department_id INT DEFAULT NULL COMMENT "所属部门"');
SET @sql = IF((SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'admin_users' AND COLUMN_NAME = 'department_id') > 0, 'SELECT "department_id already exists"', @sql);
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 检查并添加position列
SET @sql = CONCAT('ALTER TABLE admin_users ADD COLUMN position VARCHAR(100) DEFAULT NULL COMMENT "职位"');
SET @sql = IF((SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'admin_users' AND COLUMN_NAME = 'position') > 0, 'SELECT "position already exists"', @sql);
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 检查并添加level列
SET @sql = CONCAT('ALTER TABLE admin_users ADD COLUMN level INT DEFAULT 1 COMMENT "职级"');
SET @sql = IF((SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'admin_users' AND COLUMN_NAME = 'level') > 0, 'SELECT "level already exists"', @sql);
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 检查并添加direct_manager_id列
SET @sql = CONCAT('ALTER TABLE admin_users ADD COLUMN direct_manager_id INT DEFAULT NULL COMMENT "直属上级"');
SET @sql = IF((SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'admin_users' AND COLUMN_NAME = 'direct_manager_id') > 0, 'SELECT "direct_manager_id already exists"', @sql);
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 检查并添加hire_date列
SET @sql = CONCAT('ALTER TABLE admin_users ADD COLUMN hire_date DATE DEFAULT NULL COMMENT "入职日期"');
SET @sql = IF((SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'admin_users' AND COLUMN_NAME = 'hire_date') > 0, 'SELECT "hire_date already exists"', @sql);
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;
