<?php
/**
 * 数据库结构检查页面
 * 检查智能客服相关的数据库表和字段
 */

session_start();

// 检查管理员登录状态
if (!isset($_SESSION['admin_id'])) {
    header('Location: ../login.php');
    exit;
}

$admin_id = $_SESSION['admin_id'];
$admin_username = $_SESSION['admin_username'] ?? '管理员';

?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>客服系统数据库测试 - 趣玩星球后台</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', '<PERSON>Fang SC', 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 20px;
            min-height: 100vh;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }
        h1 {
            color: #6F7BF5;
            text-align: center;
            margin-bottom: 30px;
            font-size: 2rem;
        }
        .section {
            margin-bottom: 30px;
            padding: 20px;
            border-radius: 12px;
            border-left: 4px solid #6F7BF5;
        }
        .success {
            background: rgba(16, 185, 129, 0.1);
            border-left-color: #10B981;
        }
        .error {
            background: rgba(239, 68, 68, 0.1);
            border-left-color: #EF4444;
        }
        .warning {
            background: rgba(245, 158, 11, 0.1);
            border-left-color: #F59E0B;
        }
        .info {
            background: rgba(59, 130, 246, 0.1);
            border-left-color: #3B82F6;
        }
        .btn {
            display: inline-block;
            padding: 12px 24px;
            background: linear-gradient(135deg, #6F7BF5, #8B93F7);
            color: white;
            text-decoration: none;
            border-radius: 8px;
            font-weight: 600;
            margin: 10px 5px;
            border: none;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(111, 123, 245, 0.3);
        }
        .btn-success {
            background: linear-gradient(135deg, #10B981, #34D399);
        }
        .btn-danger {
            background: linear-gradient(135deg, #EF4444, #F87171);
        }
        pre {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            overflow-x: auto;
            font-size: 14px;
        }
        .status-icon {
            margin-right: 8px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1><i class="fas fa-database"></i> 客服系统数据库测试</h1>

        <?php
        echo "<div class='section info'>";
        echo "<h3><i class='fas fa-info-circle status-icon'></i>开始数据库检测</h3>";
        echo "<p>正在检查数据库连接和相关表结构...</p>";
        echo "</div>";

        // 测试数据库连接
        try {
            $pdo = new PDO(
                "mysql:host=localhost;dbname=quwanplanet;charset=utf8mb4",
                "quwanplanet",
                "nJmJm23FB4Xn6Fc3",
                [
                    PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                    PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC
                ]
            );
            echo "<div class='section success'>";
            echo "<h3><i class='fas fa-check-circle status-icon'></i>数据库连接成功</h3>";
            echo "<p>成功连接到数据库: quwanplanet</p>";
            echo "</div>";
        } catch (Exception $e) {
            echo "<div class='section error'>";
            echo "<h3><i class='fas fa-times-circle status-icon'></i>数据库连接失败</h3>";
            echo "<p>错误信息: " . htmlspecialchars($e->getMessage()) . "</p>";
            echo "<p>请检查数据库配置文件中的连接信息。</p>";
            echo "</div>";
            exit;
        }

        // 检查必需的表
        $required_tables = [
            'customer_service_bot' => '客服机器人配置表',
            'customer_service_replies' => '客服回复规则表',
            'customer_service_conversations' => '客服对话记录表',
            'users' => '用户表'
        ];

        $missing_tables = [];
        $existing_tables = [];

        foreach ($required_tables as $table => $description) {
            try {
                $stmt = $pdo->query("SHOW TABLES LIKE '$table'");
                if ($stmt->rowCount() > 0) {
                    $existing_tables[] = $table;
                    echo "<div class='section success'>";
                    echo "<h4><i class='fas fa-check status-icon'></i>表 $table 存在</h4>";
                    echo "<p>$description - 状态正常</p>";

                    // 检查表结构
                    $stmt = $pdo->query("DESCRIBE $table");
                    $columns = $stmt->fetchAll();
                    echo "<details style='margin-top: 10px;'>";
                    echo "<summary style='cursor: pointer; font-weight: 600;'>查看表结构</summary>";
                    echo "<pre>";
                    foreach ($columns as $column) {
                        echo sprintf("%-20s %-15s %s\n",
                            $column['Field'],
                            $column['Type'],
                            $column['Null'] === 'NO' ? 'NOT NULL' : 'NULL'
                        );
                    }
                    echo "</pre>";
                    echo "</details>";
                    echo "</div>";
                } else {
                    $missing_tables[] = $table;
                    echo "<div class='section error'>";
                    echo "<h4><i class='fas fa-times status-icon'></i>表 $table 不存在</h4>";
                    echo "<p>$description - 需要创建</p>";
                    echo "</div>";
                }
            } catch (Exception $e) {
                echo "<div class='section error'>";
                echo "<h4><i class='fas fa-exclamation-triangle status-icon'></i>检查表 $table 时出错</h4>";
                echo "<p>错误: " . htmlspecialchars($e->getMessage()) . "</p>";
                echo "</div>";
            }
        }

        // 如果有缺失的表，提供创建选项
        if (!empty($missing_tables)) {
            echo "<div class='section warning'>";
            echo "<h3><i class='fas fa-exclamation-triangle status-icon'></i>发现缺失的表</h3>";
            echo "<p>以下表需要创建: " . implode(', ', $missing_tables) . "</p>";
            echo "<form method='post' style='margin-top: 15px;'>";
            echo "<button type='submit' name='create_tables' class='btn btn-success'>";
            echo "<i class='fas fa-plus'></i> 创建缺失的表";
            echo "</button>";
            echo "</form>";
            echo "</div>";
        }

        // 处理创建表的请求
        if (isset($_POST['create_tables'])) {
            echo "<div class='section info'>";
            echo "<h3><i class='fas fa-cog status-icon'></i>正在创建数据库表</h3>";

            $sql_statements = [
                'customer_service_bot' => "
                    CREATE TABLE IF NOT EXISTS customer_service_bot (
                        id INT AUTO_INCREMENT PRIMARY KEY,
                        bot_name VARCHAR(100) NOT NULL DEFAULT '趣玩小助手' COMMENT '机器人名称',
                        bot_avatar VARCHAR(500) DEFAULT 'https://s1.imagehub.cc/images/2025/05/26/2cdd63b39f96072fc60cd733027d2a30.png' COMMENT '机器人头像',
                        welcome_message TEXT NOT NULL COMMENT '欢迎消息',
                        default_reply TEXT NOT NULL COMMENT '默认回复',
                        is_enabled TINYINT(1) DEFAULT 1 COMMENT '是否启用',
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
                    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='客服机器人配置表'
                ",
                'customer_service_replies' => "
                    CREATE TABLE IF NOT EXISTS customer_service_replies (
                        id INT AUTO_INCREMENT PRIMARY KEY,
                        keywords JSON NOT NULL COMMENT '关键词列表',
                        reply_content TEXT NOT NULL COMMENT '回复内容',
                        priority INT DEFAULT 1 COMMENT '优先级，数字越大优先级越高',
                        is_enabled TINYINT(1) DEFAULT 1 COMMENT '是否启用',
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                        INDEX idx_priority (priority),
                        INDEX idx_enabled (is_enabled)
                    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='客服回复规则表'
                ",
                'customer_service_conversations' => "
                    CREATE TABLE IF NOT EXISTS customer_service_conversations (
                        id INT AUTO_INCREMENT PRIMARY KEY,
                        session_id VARCHAR(100) NOT NULL COMMENT '会话ID',
                        user_id INT COMMENT '用户ID（如果已登录）',
                        user_message TEXT NOT NULL COMMENT '用户消息',
                        bot_reply TEXT COMMENT '机器人回复',
                        is_human_service TINYINT(1) DEFAULT 0 COMMENT '是否转人工客服',
                        admin_id INT COMMENT '客服人员ID',
                        admin_reply TEXT COMMENT '人工客服回复',
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        INDEX idx_session_id (session_id),
                        INDEX idx_user_id (user_id),
                        INDEX idx_created_at (created_at),
                        INDEX idx_admin_id (admin_id)
                    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='客服对话记录表'
                "
            ];

            foreach ($missing_tables as $table) {
                if (isset($sql_statements[$table])) {
                    try {
                        $pdo->exec($sql_statements[$table]);
                        echo "<p><i class='fas fa-check text-success'></i> 成功创建表: $table</p>";
                    } catch (Exception $e) {
                        echo "<p><i class='fas fa-times text-danger'></i> 创建表 $table 失败: " . htmlspecialchars($e->getMessage()) . "</p>";
                    }
                }
            }

            // 插入默认数据
            try {
                $stmt = $pdo->query("SELECT COUNT(*) FROM customer_service_bot");
                if ($stmt->fetchColumn() == 0) {
                    $pdo->exec("
                        INSERT INTO customer_service_bot (bot_name, welcome_message, default_reply) VALUES
                        ('趣玩小助手',
                         '您好！我是趣玩星球智能客服小助手🤖\n\n我可以帮助您解决以下问题：\n• 账号相关问题\n• 功能使用指导\n• 常见问题解答\n\n请描述您遇到的问题，我会尽力为您解答！',
                         '抱歉，我暂时无法理解您的问题😅\n\n您可以：\n• 换个方式描述问题\n• 联系人工客服\n• 查看帮助文档\n\n如需人工客服，请回复\"人工客服\"')
                    ");
                    echo "<p><i class='fas fa-check text-success'></i> 成功插入默认机器人配置</p>";
                }
            } catch (Exception $e) {
                echo "<p><i class='fas fa-exclamation-triangle text-warning'></i> 插入默认数据时出错: " . htmlspecialchars($e->getMessage()) . "</p>";
            }

            echo "<p style='margin-top: 15px;'>";
            echo "<a href='index.php' class='btn'><i class='fas fa-arrow-left'></i> 返回客服管理</a>";
            echo "<button onclick='location.reload()' class='btn btn-success'><i class='fas fa-redo'></i> 重新检测</button>";
            echo "</p>";
            echo "</div>";
        }

        // 显示操作按钮
        if (empty($missing_tables)) {
            echo "<div class='section success'>";
            echo "<h3><i class='fas fa-check-circle status-icon'></i>数据库检查完成</h3>";
            echo "<p>所有必需的表都已存在，客服系统可以正常使用。</p>";
            echo "<a href='index.php' class='btn'><i class='fas fa-arrow-left'></i> 返回客服管理</a>";
            echo "</div>";
        }
        ?>

        <div class="section info">
            <h3><i class="fas fa-info-circle status-icon"></i>说明</h3>
            <p>此脚本用于检查和创建客服系统所需的数据库表。如果发现问题，请联系系统管理员。</p>
            <p><strong>注意：</strong>创建表操作需要数据库写入权限。</p>
        </div>
    </div>
</body>
</html>
