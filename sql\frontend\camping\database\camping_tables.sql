-- 露营活动组局功能相关数据库表结构
-- 请在宝塔数据库中执行以下SQL语句

-- 1. 检查并创建露营活动表
CREATE TABLE IF NOT EXISTS `camping_activities` (
    `id` INT(11) NOT NULL AUTO_INCREMENT COMMENT '活动ID',
    `title` VARCHAR(200) NOT NULL COMMENT '活动标题',
    `description` TEXT COMMENT '活动描述',
    `organizer_id` INT(11) NOT NULL COMMENT '组局者用户ID',
    `location` VARCHAR(200) NOT NULL COMMENT '活动地点',
    `start_date` DATETIME NOT NULL COMMENT '活动开始时间',
    `end_date` DATETIME NOT NULL COMMENT '活动结束时间',
    `max_participants` INT(11) NOT NULL DEFAULT 20 COMMENT '最大参与人数',
    `current_participants` INT(11) NOT NULL DEFAULT 0 COMMENT '当前参与人数',
    `price` DECIMAL(10,2) NOT NULL DEFAULT 0.00 COMMENT '活动费用',
    `original_price` DECIMAL(10,2) DEFAULT NULL COMMENT '原价（用于显示优惠）',
    `category` ENUM('mountain', 'lake', 'forest', 'beach') NOT NULL DEFAULT 'mountain' COMMENT '活动分类',
    `features` JSON COMMENT '活动特色标签（JSON格式）',
    `image_url` VARCHAR(500) COMMENT '活动主图',
    `status` ENUM('draft', 'recruiting', 'full', 'ongoing', 'completed', 'cancelled') NOT NULL DEFAULT 'recruiting' COMMENT '活动状态',
    `rules_accepted` TINYINT(1) NOT NULL DEFAULT 0 COMMENT '是否需要接受活动规则',
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    KEY `idx_organizer` (`organizer_id`),
    KEY `idx_category` (`category`),
    KEY `idx_status` (`status`),
    KEY `idx_start_date` (`start_date`),
    FOREIGN KEY (`organizer_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='露营活动表';

-- 2. 活动参与记录表
CREATE TABLE IF NOT EXISTS `camping_participants` (
    `id` INT(11) NOT NULL AUTO_INCREMENT COMMENT '记录ID',
    `activity_id` INT(11) NOT NULL COMMENT '活动ID',
    `user_id` INT(11) NOT NULL COMMENT '参与者用户ID',
    `join_time` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '参与时间',
    `status` ENUM('joined', 'cancelled', 'completed') NOT NULL DEFAULT 'joined' COMMENT '参与状态',
    `payment_status` ENUM('unpaid', 'paid', 'refunded') NOT NULL DEFAULT 'unpaid' COMMENT '支付状态',
    `coupon_used` INT(11) DEFAULT NULL COMMENT '使用的优惠券ID',
    `actual_price` DECIMAL(10,2) NOT NULL DEFAULT 0.00 COMMENT '实际支付金额',
    `notes` TEXT COMMENT '备注信息',
    PRIMARY KEY (`id`),
    UNIQUE KEY `unique_participation` (`activity_id`, `user_id`),
    KEY `idx_user` (`user_id`),
    KEY `idx_activity` (`activity_id`),
    KEY `idx_status` (`status`),
    FOREIGN KEY (`activity_id`) REFERENCES `camping_activities` (`id`) ON DELETE CASCADE,
    FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='活动参与记录表';

-- 3. 露营活动优惠券表
CREATE TABLE IF NOT EXISTS `camping_coupons` (
    `id` INT(11) NOT NULL AUTO_INCREMENT COMMENT '优惠券ID',
    `title` VARCHAR(100) NOT NULL COMMENT '优惠券标题',
    `description` VARCHAR(200) COMMENT '优惠券描述',
    `type` ENUM('join_discount', 'organize_discount', 'newbie_discount') NOT NULL COMMENT '优惠券类型',
    `discount_amount` DECIMAL(10,2) NOT NULL COMMENT '优惠金额',
    `min_amount` DECIMAL(10,2) NOT NULL DEFAULT 0.00 COMMENT '最低消费金额',
    `total_quantity` INT(11) NOT NULL COMMENT '总发放数量',
    `claimed_quantity` INT(11) NOT NULL DEFAULT 0 COMMENT '已领取数量',
    `valid_from` DATETIME NOT NULL COMMENT '有效期开始时间',
    `valid_until` DATETIME NOT NULL COMMENT '有效期结束时间',
    `status` ENUM('active', 'inactive', 'expired') NOT NULL DEFAULT 'active' COMMENT '优惠券状态',
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    KEY `idx_type` (`type`),
    KEY `idx_status` (`status`),
    KEY `idx_valid_period` (`valid_from`, `valid_until`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='露营活动优惠券表';

-- 4. 用户优惠券领取记录表
CREATE TABLE IF NOT EXISTS `user_camping_coupons` (
    `id` INT(11) NOT NULL AUTO_INCREMENT COMMENT '记录ID',
    `user_id` INT(11) NOT NULL COMMENT '用户ID',
    `coupon_id` INT(11) NOT NULL COMMENT '优惠券ID',
    `claimed_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '领取时间',
    `used_at` TIMESTAMP NULL COMMENT '使用时间',
    `used_activity_id` INT(11) DEFAULT NULL COMMENT '使用的活动ID',
    `status` ENUM('claimed', 'used', 'expired') NOT NULL DEFAULT 'claimed' COMMENT '优惠券状态',
    PRIMARY KEY (`id`),
    UNIQUE KEY `unique_user_coupon` (`user_id`, `coupon_id`),
    KEY `idx_user` (`user_id`),
    KEY `idx_coupon` (`coupon_id`),
    KEY `idx_status` (`status`),
    FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
    FOREIGN KEY (`coupon_id`) REFERENCES `camping_coupons` (`id`) ON DELETE CASCADE,
    FOREIGN KEY (`used_activity_id`) REFERENCES `camping_activities` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户优惠券领取记录表';

-- 5. 露营攻略表
CREATE TABLE IF NOT EXISTS `camping_guides` (
    `id` INT(11) NOT NULL AUTO_INCREMENT COMMENT '攻略ID',
    `title` VARCHAR(200) NOT NULL COMMENT '攻略标题',
    `content` LONGTEXT NOT NULL COMMENT '攻略内容',
    `summary` VARCHAR(500) COMMENT '攻略摘要',
    `author_id` INT(11) NOT NULL COMMENT '作者用户ID',
    `cover_image` VARCHAR(500) COMMENT '封面图片',
    `category` ENUM('beginner', 'equipment', 'cooking', 'safety', 'location') NOT NULL COMMENT '攻略分类',
    `view_count` INT(11) NOT NULL DEFAULT 0 COMMENT '阅读量',
    `like_count` INT(11) NOT NULL DEFAULT 0 COMMENT '点赞数',
    `status` ENUM('draft', 'published', 'hidden') NOT NULL DEFAULT 'draft' COMMENT '发布状态',
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    KEY `idx_author` (`author_id`),
    KEY `idx_category` (`category`),
    KEY `idx_status` (`status`),
    KEY `idx_view_count` (`view_count`),
    FOREIGN KEY (`author_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='露营攻略表';

-- 6. 活动规则确认记录表
CREATE TABLE IF NOT EXISTS `activity_rules_confirmations` (
    `id` INT(11) NOT NULL AUTO_INCREMENT COMMENT '记录ID',
    `user_id` INT(11) NOT NULL COMMENT '用户ID',
    `confirmed_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '确认时间',
    `ip_address` VARCHAR(45) COMMENT '确认时的IP地址',
    `user_agent` TEXT COMMENT '用户代理信息',
    PRIMARY KEY (`id`),
    KEY `idx_user` (`user_id`),
    KEY `idx_confirmed_at` (`confirmed_at`),
    FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='活动规则确认记录表';

-- 插入初始优惠券数据
INSERT INTO `camping_coupons` (`title`, `description`, `type`, `discount_amount`, `min_amount`, `total_quantity`, `valid_from`, `valid_until`) VALUES
('露营活动券', '参加露营活动立减30元', 'join_discount', 30.00, 100.00, 1000, NOW(), DATE_ADD(NOW(), INTERVAL 30 DAY)),
('组局优惠券', '发起露营活动减免费用', 'organize_discount', 50.00, 200.00, 500, NOW(), DATE_ADD(NOW(), INTERVAL 30 DAY)),
('新手体验券', '首次参加露营活动专享', 'newbie_discount', 20.00, 80.00, 2000, NOW(), DATE_ADD(NOW(), INTERVAL 30 DAY));

-- 插入示例露营活动数据
INSERT INTO `camping_activities` (`title`, `description`, `organizer_id`, `location`, `start_date`, `end_date`, `max_participants`, `price`, `original_price`, `category`, `features`, `image_url`, `status`) VALUES
('春季山谷露营 · 观星赏月', '在美丽的山谷中度过难忘的露营时光，观星赏月，篝火晚会，与大自然亲密接触。', 1, '北京·怀柔山谷', DATE_ADD(NOW(), INTERVAL 7 DAY), DATE_ADD(NOW(), INTERVAL 8 DAY), 20, 168.00, 198.00, 'mountain', '["篝火晚会", "观星", "烧烤"]', 'https://s1.imagehub.cc/images/2025/05/15/camping_activity_1.jpg', 'recruiting'),
('湖边露营 · 日出摄影', '在宁静的湖边扎营，欣赏美丽的日出，进行摄影创作，享受钓鱼和划船的乐趣。', 1, '上海·淀山湖', DATE_ADD(NOW(), INTERVAL 14 DAY), DATE_ADD(NOW(), INTERVAL 15 DAY), 15, 228.00, 258.00, 'lake', '["日出摄影", "钓鱼", "划船"]', 'https://s1.imagehub.cc/images/2025/05/15/camping_activity_2.jpg', 'recruiting');

-- 插入示例露营攻略数据
INSERT INTO `camping_guides` (`title`, `content`, `summary`, `author_id`, `category`, `status`) VALUES
('新手露营完全指南', '详细的新手露营指南内容...', '从装备准备到安全注意事项，一篇文章带你入门露营', 1, 'beginner', 'published'),
('露营美食制作技巧', '露营美食制作的详细教程...', '在野外也能享受美味，简单易做的露营料理分享', 1, 'cooking', 'published');

-- 更新攻略的阅读量和点赞数
UPDATE `camping_guides` SET `view_count` = 1200, `like_count` = 89 WHERE `title` = '新手露营完全指南';
UPDATE `camping_guides` SET `view_count` = 856, `like_count` = 67 WHERE `title` = '露营美食制作技巧';
