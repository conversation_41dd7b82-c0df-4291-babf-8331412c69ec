<?php
session_start();
header('Content-Type: application/json');

/**
 * 生成唯一的趣玩ID
 * 规则：
 * 1. 从7位数字开始（1000000-9999999）
 * 2. 不能以0开头
 * 3. 不能是豹子号（连续3个相同数字）
 * 4. 不能是爱情号（包含520、1314等）
 * 5. 不能是其他靓号模式
 * 6. 7位数字用完后自动升级到8位数字
 *
 * @param PDO $pdo 数据库连接
 * @return string|false 生成的ID或失败时返回false
 */
function generateQuwanId($pdo) {
    $maxAttempts = 100; // 增加尝试次数

    // 先尝试7位数字
    for ($attempts = 0; $attempts < $maxAttempts; $attempts++) {
        $id = generate7DigitId();
        if ($id && isValidId($id) && !idExists($pdo, $id)) {
            return $id;
        }
    }

    // 如果7位数字生成失败，尝试8位数字
    for ($attempts = 0; $attempts < $maxAttempts; $attempts++) {
        $id = generate8DigitId();
        if ($id && isValidId($id) && !idExists($pdo, $id)) {
            return $id;
        }
    }

    // 如果还是失败，尝试9位数字
    for ($attempts = 0; $attempts < $maxAttempts; $attempts++) {
        $id = generate9DigitId();
        if ($id && isValidId($id) && !idExists($pdo, $id)) {
            return $id;
        }
    }

    return false;
}

/**
 * 生成7位数字ID
 */
function generate7DigitId() {
    return (string)mt_rand(1000000, 9999999);
}

/**
 * 生成8位数字ID
 */
function generate8DigitId() {
    return (string)mt_rand(10000000, 99999999);
}

/**
 * 生成9位数字ID
 */
function generate9DigitId() {
    return (string)mt_rand(100000000, 999999999);
}

/**
 * 验证ID是否符合规则
 */
function isValidId($id) {
    // 不能以0开头
    if ($id[0] === '0') {
        return false;
    }

    // 检查豹子号（连续3个相同数字）
    if (hasConsecutiveRepeats($id, 3)) {
        return false;
    }

    // 检查爱情号和其他靓号
    if (isSpecialNumber($id)) {
        return false;
    }

    // 检查顺子号（连续递增或递减）
    if (hasSequentialDigits($id)) {
        return false;
    }

    return true;
}

/**
 * 检查是否有连续重复数字
 */
function hasConsecutiveRepeats($id, $count = 3) {
    $length = strlen($id);
    for ($i = 0; $i <= $length - $count; $i++) {
        $allSame = true;
        for ($j = 1; $j < $count; $j++) {
            if ($id[$i] !== $id[$i + $j]) {
                $allSame = false;
                break;
            }
        }
        if ($allSame) {
            return true;
        }
    }
    return false;
}

/**
 * 检查是否是特殊号码（爱情号等）
 */
function isSpecialNumber($id) {
    $specialPatterns = [
        '520', '521', '1314', '1413', '1314520', '5201314',
        '666', '888', '999', '168', '188', '288', '388',
        '588', '688', '788', '988', '123', '234', '345',
        '456', '567', '678', '789', '987', '876', '765',
        '654', '543', '432', '321'
    ];

    foreach ($specialPatterns as $pattern) {
        if (strpos($id, $pattern) !== false) {
            return true;
        }
    }

    return false;
}

/**
 * 检查是否有顺子号（连续数字）
 */
function hasSequentialDigits($id) {
    $length = strlen($id);

    // 检查连续4位递增或递减
    for ($i = 0; $i <= $length - 4; $i++) {
        $isAscending = true;
        $isDescending = true;

        for ($j = 1; $j < 4; $j++) {
            if ((int)$id[$i + $j] !== (int)$id[$i + $j - 1] + 1) {
                $isAscending = false;
            }
            if ((int)$id[$i + $j] !== (int)$id[$i + $j - 1] - 1) {
                $isDescending = false;
            }
        }

        if ($isAscending || $isDescending) {
            return true;
        }
    }

    return false;
}

/**
 * 检查ID是否已存在
 */
function idExists($pdo, $id) {
    try {
        $stmt = $pdo->prepare("SELECT COUNT(*) FROM users WHERE quwan_id = ?");
        $stmt->execute([$id]);
        return $stmt->fetchColumn() > 0;
    } catch (Exception $e) {
        error_log("检查ID存在性失败: " . $e->getMessage());
        return true; // 出错时假设存在，避免重复
    }
}

// 获取POST数据
$input = json_decode(file_get_contents('php://input'), true);

// 检查JSON解析是否成功
if ($input === null) {
    echo json_encode(['success' => false, 'error' => '无效的请求数据']);
    exit;
}

// 获取并清理数据
$phone = trim($input['phone'] ?? '');
$nickname = trim($input['nickname'] ?? '');
$gender = trim($input['gender'] ?? '');
$birth_date = trim($input['birth_date'] ?? '');
$region = trim($input['region'] ?? '');
$email = trim($input['email'] ?? '');
$password = trim($input['password'] ?? '');
$bio = trim($input['bio'] ?? ''); // bio是可选字段
$avatar = trim($input['avatar'] ?? ''); // avatar是可选字段

// 记录调试信息
error_log("注册数据: " . json_encode([
    'phone' => $phone,
    'nickname' => $nickname,
    'gender' => $gender,
    'birth_date' => $birth_date,
    'region' => $region,
    'email' => $email,
    'password' => $password ? '已提供' : '未提供',
    'bio' => $bio,
    'avatar' => $avatar ? '已提供' : '未提供'
]));

// 验证必填字段
if (empty($phone)) {
    echo json_encode(['success' => false, 'error' => '手机号不能为空']);
    exit;
}

if (empty($nickname)) {
    echo json_encode(['success' => false, 'error' => '昵称不能为空']);
    exit;
}

if (empty($gender)) {
    echo json_encode(['success' => false, 'error' => '性别不能为空']);
    exit;
}

if (empty($birth_date)) {
    echo json_encode(['success' => false, 'error' => '出生日期不能为空']);
    exit;
}

if (empty($region)) {
    echo json_encode(['success' => false, 'error' => '所在地区不能为空']);
    exit;
}

if (empty($email)) {
    echo json_encode(['success' => false, 'error' => '邮箱不能为空']);
    exit;
}

if (empty($password)) {
    echo json_encode(['success' => false, 'error' => '密码不能为空']);
    exit;
}

// 数据库配置
$host = 'localhost';
$dbname = 'quwanplanet';
$db_username = 'quwanplanet';
$db_password = 'nJmJm23FB4Xn6Fc3';

try {
    $pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $db_username, $db_password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
} catch(PDOException $e) {
    echo json_encode(['success' => false, 'error' => '数据库连接失败']);
    exit;
}

// 验证邮箱格式
if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
    echo json_encode(['success' => false, 'error' => '邮箱格式不正确']);
    exit;
}

// 验证密码长度
if (strlen($password) < 6) {
    echo json_encode(['success' => false, 'error' => '密码长度至少6位']);
    exit;
}

try {
    // 检查邮箱是否已被使用
    $stmt = $pdo->prepare("SELECT COUNT(*) FROM users WHERE email = ?");
    $stmt->execute([$email]);
    if ($stmt->fetchColumn() > 0) {
        echo json_encode(['success' => false, 'error' => '该邮箱已被使用']);
        exit;
    }

    // 检查昵称是否已被使用
    $stmt = $pdo->prepare("SELECT COUNT(*) FROM users WHERE nickname = ?");
    $stmt->execute([$nickname]);
    if ($stmt->fetchColumn() > 0) {
        echo json_encode(['success' => false, 'error' => '该昵称已被使用']);
        exit;
    }



    // 加密密码
    $hashed_password = password_hash($password, PASSWORD_DEFAULT);

    // 生成趣玩ID
    $quwanId = generateQuwanId($pdo);
    if (!$quwanId) {
        echo json_encode(['success' => false, 'error' => '生成用户ID失败，请重试']);
        exit;
    }

    // 创建新用户记录
    $stmt = $pdo->prepare("
        INSERT INTO users (
            phone, quwan_id, username, nickname, gender, birth_date, region, email, password, bio, avatar, status, created_at, updated_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 'active', NOW(), NOW())
    ");

    // 使用昵称作为用户名（可以后续在个人中心修改）
    $stmt->execute([
        $phone,
        $quwanId,
        $nickname,  // username
        $nickname,  // nickname
        $gender,
        $birth_date,
        $region,
        $email,
        $hashed_password,
        $bio,
        $avatar ?: null  // 如果没有头像则为null
    ]);

    $userId = $pdo->lastInsertId();

    // 发送欢迎系统消息
    $welcomeResult = sendWelcomeMessage($pdo, $userId, $nickname);
    if (!$welcomeResult) {
        error_log("发送欢迎消息失败 - 用户ID: $userId, 昵称: $nickname");
    }

    // 记录登录日志
    require_once '../login/login_logger.php';
    $loginLogResult = recordUserLoginLog($pdo, $userId, 'normal_login', 'success');
    if (!$loginLogResult) {
        error_log("记录登录日志失败 - 用户ID: $userId");
    }

    // 设置会话信息
    $_SESSION['user_id'] = $userId;
    $_SESSION['nickname'] = $nickname;
    $_SESSION['email'] = $email;
    $_SESSION['avatar'] = $avatar;
    $_SESSION['quwan_id'] = $quwanId;
    $_SESSION['logged_in'] = true;

    echo json_encode([
        'success' => true,
        'message' => '注册完成！欢迎加入趣玩星球',
        'user_id' => $userId,
        'quwan_id' => $quwanId
    ]);

} catch(PDOException $e) {
    echo json_encode(['success' => false, 'error' => '数据库操作失败: ' . $e->getMessage()]);
}



/**
 * 发送欢迎系统消息
 */
function sendWelcomeMessage($pdo, $userId, $nickname) {
    try {
        // 首先检查system_messages表是否存在
        $stmt = $pdo->query("SHOW TABLES LIKE 'system_messages'");
        if ($stmt->rowCount() == 0) {
            // 如果表不存在，创建表
            $createTableSQL = "
            CREATE TABLE `system_messages` (
              `id` int(11) NOT NULL AUTO_INCREMENT,
              `user_id` int(11) NOT NULL COMMENT '接收用户ID',
              `title` varchar(255) NOT NULL COMMENT '消息标题',
              `content` text NOT NULL COMMENT '消息内容',
              `type` enum('welcome','notice','warning','promotion','system') NOT NULL DEFAULT 'system' COMMENT '消息类型',
              `is_read` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否已读：0-未读，1-已读',
              `read_at` timestamp NULL DEFAULT NULL COMMENT '阅读时间',
              `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
              `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
              PRIMARY KEY (`id`),
              KEY `idx_user_id` (`user_id`),
              KEY `idx_type` (`type`),
              KEY `idx_is_read` (`is_read`),
              KEY `idx_created_at` (`created_at`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='系统消息表'
            ";
            $pdo->exec($createTableSQL);
            error_log("system_messages表不存在，已自动创建");
        }

        // 专业化的欢迎消息内容
        $welcomeMessage = "亲爱的 {$nickname}，\n\n" .
                         "热烈欢迎您加入趣玩星球大家庭！🎉\n\n" .
                         "作为我们社区的新成员，您现在可以：\n" .
                         "• 探索丰富多彩的社区内容\n" .
                         "• 与志同道合的朋友互动交流\n" .
                         "• 分享您的精彩生活瞬间\n" .
                         "• 参与各种有趣的社区活动\n\n" .
                         "为了营造良好的社区环境，请您务必遵守《趣玩星球社区公约》。如果您在使用过程中遇到任何问题或需要帮助，请随时联系我们的在线客服团队。\n\n" .
                         "再次欢迎您的到来，祝您在趣玩星球度过愉快的时光！\n\n" .
                         "——趣玩星球运营团队";

        // 插入系统消息
        $stmt = $pdo->prepare("
            INSERT INTO system_messages (
                user_id,
                title,
                content,
                type,
                is_read,
                created_at,
                updated_at
            ) VALUES (?, ?, ?, 'welcome', 0, NOW(), NOW())
        ");

        $result = $stmt->execute([
            $userId,
            '欢迎加入趣玩星球！',
            $welcomeMessage
        ]);

        if ($result) {
            error_log("成功为用户 {$nickname} (ID: {$userId}) 发送欢迎消息");
            return true;
        } else {
            error_log("为用户 {$nickname} (ID: {$userId}) 发送欢迎消息失败 - SQL执行失败");
            return false;
        }
    } catch (PDOException $e) {
        error_log("发送欢迎消息数据库错误: " . $e->getMessage() . " - 用户: {$nickname} (ID: {$userId})");
        return false;
    } catch (Exception $e) {
        error_log("发送欢迎消息失败: " . $e->getMessage() . " - 用户: {$nickname} (ID: {$userId})");
        return false;
    }
}
?>
