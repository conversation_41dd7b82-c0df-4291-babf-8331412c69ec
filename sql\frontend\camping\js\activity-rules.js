/**
 * 活动规则页面JavaScript
 * 处理页面交互逻辑
 */

class ActivityRulesPage {
    constructor() {
        this.toast = document.getElementById('toast');
        this.init();
    }

    init() {
        this.bindEvents();
        this.initAnimations();
        this.disableBrowserAlerts();
    }

    bindEvents() {
        // 返回按钮事件
        const backBtn = document.querySelector('.back-btn');
        if (backBtn) {
            backBtn.addEventListener('click', () => {
                this.goBack();
            });
        }

        // 联系方式点击事件
        const contactItems = document.querySelectorAll('.contact-item');
        contactItems.forEach(item => {
            item.addEventListener('click', () => {
                this.handleContactClick(item);
            });
        });

        // 规则项点击事件（用于高亮显示）
        const ruleItems = document.querySelectorAll('.rule-item');
        ruleItems.forEach(item => {
            item.addEventListener('click', () => {
                this.highlightRule(item);
            });
        });
    }

    // 禁用浏览器原生弹窗，使用toast样式
    disableBrowserAlerts() {
        // 重写alert函数
        window.alert = (message) => {
            this.showToast(message);
        };

        // 重写confirm函数
        window.confirm = (message) => {
            this.showToast(message);
            return true; // 默认返回true，实际项目中可以实现自定义确认框
        };

        // 阻止默认的错误弹窗
        window.addEventListener('error', (e) => {
            e.preventDefault();
            this.showToast('页面出现错误，请刷新重试');
        });
    }

    // 返回上一页
    goBack() {
        this.showToast('正在返回...');
        setTimeout(() => {
            if (document.referrer && document.referrer.includes('camping')) {
                history.back();
            } else {
                window.location.href = 'index.php';
            }
        }, 500);
    }

    // 处理联系方式点击
    handleContactClick(item) {
        const contactValue = item.querySelector('.contact-value').textContent;
        const contactLabel = item.querySelector('.contact-label').textContent;

        if (contactLabel === '客服热线') {
            // 尝试拨打电话
            if (navigator.userAgent.match(/(iPhone|iPod|Android|BlackBerry)/)) {
                window.location.href = `tel:${contactValue}`;
            } else {
                this.copyToClipboard(contactValue, '客服电话已复制到剪贴板');
            }
        } else if (contactLabel === '紧急救援') {
            this.showToast('紧急情况请直接拨打救援电话：' + contactValue);
        } else if (contactLabel === '举报邮箱') {
            // 尝试打开邮件客户端
            window.location.href = `mailto:${contactValue}`;
        }
    }

    // 高亮显示规则项
    highlightRule(item) {
        // 移除其他项的高亮
        document.querySelectorAll('.rule-item').forEach(rule => {
            rule.classList.remove('highlighted');
        });

        // 添加高亮效果
        item.classList.add('highlighted');
        
        // 3秒后移除高亮
        setTimeout(() => {
            item.classList.remove('highlighted');
        }, 3000);
    }

    // 复制到剪贴板
    copyToClipboard(text, message) {
        if (navigator.clipboard) {
            navigator.clipboard.writeText(text).then(() => {
                this.showToast(message || '已复制到剪贴板');
            }).catch(() => {
                this.showToast('复制失败，请手动复制');
            });
        } else {
            // 兼容旧浏览器
            const textArea = document.createElement('textarea');
            textArea.value = text;
            document.body.appendChild(textArea);
            textArea.select();
            try {
                document.execCommand('copy');
                this.showToast(message || '已复制到剪贴板');
            } catch (err) {
                this.showToast('复制失败，请手动复制');
            }
            document.body.removeChild(textArea);
        }
    }

    // 初始化动画
    initAnimations() {
        // 页面加载动画
        const sections = document.querySelectorAll('.rule-section, .important-notice, .contact-section, .confirm-section');
        sections.forEach((section, index) => {
            section.style.opacity = '0';
            section.style.transform = 'translateY(20px)';
            
            setTimeout(() => {
                section.style.transition = 'all 0.6s ease';
                section.style.opacity = '1';
                section.style.transform = 'translateY(0)';
            }, index * 100);
        });

        // 规则项动画
        setTimeout(() => {
            const ruleItems = document.querySelectorAll('.rule-item');
            ruleItems.forEach((item, index) => {
                setTimeout(() => {
                    item.style.transition = 'all 0.3s ease';
                    item.style.transform = 'translateX(0)';
                }, index * 50);
            });
        }, 500);
    }

    // 显示Toast提示
    showToast(message, duration = 3000) {
        if (!this.toast) return;

        this.toast.textContent = message;
        this.toast.style.display = 'block';
        this.toast.style.opacity = '0';
        this.toast.style.transform = 'translateX(-50%) translateY(20px)';

        // 动画显示
        setTimeout(() => {
            this.toast.style.transition = 'all 0.3s ease';
            this.toast.style.opacity = '1';
            this.toast.style.transform = 'translateX(-50%) translateY(0)';
        }, 10);

        // 自动隐藏
        setTimeout(() => {
            this.toast.style.opacity = '0';
            this.toast.style.transform = 'translateX(-50%) translateY(20px)';
            
            setTimeout(() => {
                this.toast.style.display = 'none';
            }, 300);
        }, duration);
    }
}

// 确认规则函数（全局函数）
function confirmRules() {
    const rulesPage = new ActivityRulesPage();
    rulesPage.showToast('感谢您认真阅读活动规则！');
    
    // 模拟确认操作
    setTimeout(() => {
        rulesPage.showToast('规则确认成功，正在返回...');
        setTimeout(() => {
            history.back();
        }, 1000);
    }, 1500);
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    new ActivityRulesPage();
});

// 添加高亮样式
const style = document.createElement('style');
style.textContent = `
    .rule-item.highlighted {
        background: rgba(111, 123, 245, 0.15) !important;
        border-left: 4px solid var(--primary-color) !important;
        transform: translateX(8px) !important;
        box-shadow: var(--shadow-md) !important;
    }
    
    .rule-item.highlighted .rule-number {
        background: var(--primary-color) !important;
        transform: scale(1.1) !important;
    }
    
    .rule-item.highlighted .rule-text {
        color: var(--primary-color) !important;
    }
`;
document.head.appendChild(style);
