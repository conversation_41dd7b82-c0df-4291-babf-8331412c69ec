<?php
// 获取新用户消息API（后台客服使用）
header('Content-Type: application/json; charset=UTF-8');
ini_set('default_charset', 'UTF-8');

session_start();

// 检查客服登录状态
if (!isset($_SESSION['cs_logged_in']) || !$_SESSION['cs_logged_in']) {
    http_response_code(401);
    echo json_encode(['error' => '客服未登录']);
    exit;
}

// 获取参数
$session_id = $_GET['session_id'] ?? '';
$last_check = $_GET['last_check'] ?? 0;

if (empty($session_id)) {
    http_response_code(400);
    echo json_encode(['error' => '会话ID不能为空']);
    exit;
}

// 引用数据库配置文件
require_once '../../db_config.php';

try {
    $pdo = getDbConnection();
    
    // 检查会话是否存在
    $stmt = $pdo->prepare("SELECT * FROM customer_service_sessions WHERE session_id = ?");
    $stmt->execute([$session_id]);
    $session = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$session) {
        http_response_code(404);
        echo json_encode(['error' => '会话不存在']);
        exit;
    }
    
    // 检查客服权限（只有分配的客服或超级管理员可以查看）
    if ($session['customer_service_id'] != $_SESSION['cs_user_id'] && $_SESSION['cs_role'] !== 'super_admin') {
        http_response_code(403);
        echo json_encode(['error' => '无权限访问此会话']);
        exit;
    }
    
    // 计算时间戳（从last_check开始）
    $check_time = date('Y-m-d H:i:s', $last_check / 1000);
    
    // 获取新消息（用户发送的消息）
    $stmt = $pdo->prepare("
        SELECT * FROM customer_service_messages 
        WHERE session_id = ? 
        AND sender_type = 'user' 
        AND created_at > ? 
        ORDER BY created_at ASC
    ");
    $stmt->execute([$session_id, $check_time]);
    $messages = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo json_encode([
        'success' => true,
        'messages' => $messages,
        'count' => count($messages),
        'timestamp' => time()
    ]);
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => '获取消息失败',
        'message' => $e->getMessage()
    ]);
}
?>
