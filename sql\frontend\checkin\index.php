<?php
session_start();

// 检查用户是否登录
if (!isset($_SESSION['user_id'])) {
    header('Location: ../login/index.php');
    exit();
}

// 数据库连接
require_once '../db_config.php';

$user_id = $_SESSION['user_id'];

// 获取数据库连接
try {
    $pdo = getDbConnection();
} catch (Exception $e) {
    die('数据库连接失败');
}

// 获取用户信息
$stmt = $pdo->prepare("SELECT username, avatar FROM users WHERE id = ?");
$stmt->execute([$user_id]);
$user = $stmt->fetch();

if (!$user) {
    header('Location: ../login/index.php');
    exit();
}

$user_username = $user['username'];
$user_avatar = $user['avatar'] ?: 'https://s1.imagehub.cc/images/2025/05/02/default-avatar.png';

// 获取今日签到状态
$today = date('Y-m-d');
$stmt = $pdo->prepare("SELECT * FROM daily_checkins WHERE user_id = ? AND checkin_date = ?");
$stmt->execute([$user_id, $today]);
$today_checkin = $stmt->fetch();

$is_checked_today = !empty($today_checkin);

// 获取连续签到天数
$stmt = $pdo->prepare("
    SELECT COUNT(*) as consecutive_days 
    FROM daily_checkins 
    WHERE user_id = ? 
    AND checkin_date >= DATE_SUB(CURDATE(), INTERVAL 30 DAY)
    AND checkin_date <= CURDATE()
    ORDER BY checkin_date DESC
");
$stmt->execute([$user_id]);
$consecutive_result = $stmt->fetch();
$consecutive_days = $consecutive_result['consecutive_days'] ?? 0;

// 获取本月签到记录
$current_month = date('Y-m');
$stmt = $pdo->prepare("
    SELECT checkin_date, points_earned 
    FROM daily_checkins 
    WHERE user_id = ? 
    AND DATE_FORMAT(checkin_date, '%Y-%m') = ?
    ORDER BY checkin_date ASC
");
$stmt->execute([$user_id, $current_month]);
$month_checkins = $stmt->fetchAll();

// 获取用户总积分
$stmt = $pdo->prepare("SELECT SUM(points_earned) as total_points FROM daily_checkins WHERE user_id = ?");
$stmt->execute([$user_id]);
$total_points_result = $stmt->fetch();
$total_points = $total_points_result['total_points'] ?? 0;

// 处理签到请求
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['checkin']) && !$is_checked_today) {
    // 计算签到奖励
    $base_points = 10;
    $bonus_points = 0;
    
    // 连续签到奖励
    if ($consecutive_days >= 7) {
        $bonus_points += 20;
    } elseif ($consecutive_days >= 3) {
        $bonus_points += 10;
    }
    
    $total_earned = $base_points + $bonus_points;
    
    // 插入签到记录
    $stmt = $pdo->prepare("
        INSERT INTO daily_checkins (user_id, checkin_date, points_earned, consecutive_days, created_at) 
        VALUES (?, ?, ?, ?, NOW())
    ");
    
    if ($stmt->execute([$user_id, $today, $total_earned, $consecutive_days + 1])) {
        $is_checked_today = true;
        $consecutive_days += 1;
        $total_points += $total_earned;
        $checkin_success = true;
        $earned_points = $total_earned;
    }
}
?>

<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>每日签到 - 趣玩星球</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="css/checkin_style.css">
    <?php
    include '../includes/bottom_nav.php';
    echo renderBottomNavCSS();
    ?>
</head>
<body>
    <!-- 顶部导航 -->
    <div class="checkin-header">
        <div class="header-content">
            <a href="../profile/index.php" class="back-btn">
                <i class="fas fa-arrow-left"></i>
            </a>
            <h1 class="header-title">每日签到</h1>
            <div class="header-actions">
                <a href="history.php" class="history-btn">
                    <i class="fas fa-history"></i>
                </a>
            </div>
        </div>
    </div>

    <!-- 用户信息卡片 -->
    <div class="user-info-card">
        <div class="user-avatar">
            <img src="<?php echo htmlspecialchars($user_avatar); ?>" alt="用户头像">
        </div>
        <div class="user-details">
            <div class="username"><?php echo htmlspecialchars($user_username); ?></div>
            <div class="user-stats">
                <div class="stat-item">
                    <span class="stat-value"><?php echo $consecutive_days; ?></span>
                    <span class="stat-label">连续签到</span>
                </div>
                <div class="stat-item">
                    <span class="stat-value"><?php echo $total_points; ?></span>
                    <span class="stat-label">总积分</span>
                </div>
            </div>
        </div>
    </div>

    <!-- 签到主区域 -->
    <div class="checkin-main">
        <div class="checkin-card">
            <div class="checkin-icon">
                <?php if ($is_checked_today): ?>
                    <i class="fas fa-check-circle"></i>
                <?php else: ?>
                    <i class="fas fa-calendar-check"></i>
                <?php endif; ?>
            </div>
            
            <div class="checkin-content">
                <?php if ($is_checked_today): ?>
                    <h2 class="checkin-title">今日已签到</h2>
                    <p class="checkin-desc">明天再来签到吧！</p>
                    <div class="checkin-reward">
                        <span class="reward-text">今日获得</span>
                        <span class="reward-points">+<?php echo $earned_points ?? $today_checkin['points_earned']; ?> 积分</span>
                    </div>
                <?php else: ?>
                    <h2 class="checkin-title">签到领积分</h2>
                    <p class="checkin-desc">每日签到可获得积分奖励</p>
                    <div class="checkin-reward">
                        <span class="reward-text">基础奖励</span>
                        <span class="reward-points">+10 积分</span>
                    </div>
                    <?php if ($consecutive_days >= 3): ?>
                        <div class="bonus-reward">
                            <span class="bonus-text">连续奖励</span>
                            <span class="bonus-points">+<?php echo $consecutive_days >= 7 ? 20 : 10; ?> 积分</span>
                        </div>
                    <?php endif; ?>
                <?php endif; ?>
            </div>
            
            <?php if (!$is_checked_today): ?>
                <form method="POST" action="">
                    <button type="submit" name="checkin" class="checkin-btn">
                        <i class="fas fa-hand-point-up"></i>
                        <span>立即签到</span>
                    </button>
                </form>
            <?php else: ?>
                <button class="checkin-btn checked" disabled>
                    <i class="fas fa-check"></i>
                    <span>已签到</span>
                </button>
            <?php endif; ?>
        </div>
    </div>

    <!-- 签到日历 -->
    <div class="checkin-calendar">
        <div class="calendar-header">
            <h3 class="calendar-title"><?php echo date('Y年m月'); ?> 签到记录</h3>
        </div>
        
        <div class="calendar-grid">
            <?php
            $days_in_month = date('t');
            $first_day_of_month = date('w', strtotime(date('Y-m-01')));
            $checkin_dates = array_column($month_checkins, 'checkin_date');
            
            // 空白天数
            for ($i = 0; $i < $first_day_of_month; $i++) {
                echo '<div class="calendar-day empty"></div>';
            }
            
            // 月份天数
            for ($day = 1; $day <= $days_in_month; $day++) {
                $date = date('Y-m-') . sprintf('%02d', $day);
                $is_today = ($date === $today);
                $is_checked = in_array($date, $checkin_dates);
                $is_future = ($date > $today);
                
                $classes = ['calendar-day'];
                if ($is_today) $classes[] = 'today';
                if ($is_checked) $classes[] = 'checked';
                if ($is_future) $classes[] = 'future';
                
                echo '<div class="' . implode(' ', $classes) . '">';
                echo '<span class="day-number">' . $day . '</span>';
                if ($is_checked) {
                    echo '<i class="fas fa-check"></i>';
                }
                echo '</div>';
            }
            ?>
        </div>
    </div>

    <!-- 签到奖励说明 -->
    <div class="reward-rules">
        <div class="rules-header">
            <h3 class="rules-title">签到奖励规则</h3>
        </div>
        
        <div class="rules-list">
            <div class="rule-item">
                <div class="rule-icon">
                    <i class="fas fa-coins"></i>
                </div>
                <div class="rule-content">
                    <div class="rule-title">基础奖励</div>
                    <div class="rule-desc">每日签到可获得10积分</div>
                </div>
            </div>
            
            <div class="rule-item">
                <div class="rule-icon">
                    <i class="fas fa-fire"></i>
                </div>
                <div class="rule-content">
                    <div class="rule-title">连续3天</div>
                    <div class="rule-desc">额外获得10积分奖励</div>
                </div>
            </div>
            
            <div class="rule-item">
                <div class="rule-icon">
                    <i class="fas fa-trophy"></i>
                </div>
                <div class="rule-content">
                    <div class="rule-title">连续7天</div>
                    <div class="rule-desc">额外获得20积分奖励</div>
                </div>
            </div>
        </div>
    </div>

    <!-- 签到成功弹窗 -->
    <?php if (isset($checkin_success) && $checkin_success): ?>
    <div class="success-modal" id="successModal">
        <div class="modal-content">
            <div class="success-icon">
                <i class="fas fa-check-circle"></i>
            </div>
            <h3 class="success-title">签到成功！</h3>
            <div class="success-reward">
                <span class="success-text">获得积分</span>
                <span class="success-points">+<?php echo $earned_points; ?></span>
            </div>
            <button class="close-modal-btn" onclick="hideSuccessModal()">
                <i class="fas fa-times"></i>
            </button>
        </div>
    </div>
    <?php endif; ?>

    <!-- 底部导航 -->
    <?php echo renderBottomNav('profile'); ?>

    <script src="js/checkin.js"></script>
</body>
</html>
