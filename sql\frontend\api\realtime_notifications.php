<?php
/**
 * 实时通知SSE服务
 * 用于前后台联动推送验证码等通知
 */

// 设置SSE响应头
header('Content-Type: text/event-stream');
header('Cache-Control: no-cache');
header('Connection: keep-alive');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Headers: Cache-Control');

// 防止输出缓冲
if (ob_get_level()) {
    ob_end_clean();
}

// 获取用户ID
$user_id = intval($_GET['user_id'] ?? 0);

if ($user_id <= 0) {
    echo "data: " . json_encode(['error' => '无效的用户ID']) . "\n\n";
    flush();
    exit;
}

// 数据库配置（直接配置，避免路径问题）
try {
    $pdo = new PDO(
        "mysql:host=localhost;dbname=quwanplanet;charset=utf8mb4",
        "quwanplanet",
        "nJmJm23FB4Xn6Fc3",
        [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC
        ]
    );
} catch (PDOException $e) {
    echo "data: " . json_encode(['error' => '数据库连接失败: ' . $e->getMessage()]) . "\n\n";
    flush();
    exit;
}

// 更新用户在线状态
try {
    $stmt = $pdo->prepare("
        UPDATE users
        SET online_status = 'online', last_activity = NOW()
        WHERE id = ?
    ");
    $stmt->execute([$user_id]);
} catch (Exception $e) {
    error_log("更新用户在线状态失败: " . $e->getMessage());
}

// 发送初始连接确认
echo "data: " . json_encode([
    'type' => 'connection',
    'message' => '实时通知连接已建立',
    'user_id' => $user_id,
    'timestamp' => time()
]) . "\n\n";
flush();

$last_check = time();
$heartbeat_interval = 30; // 心跳间隔30秒
$max_execution_time = 300; // 最大执行时间5分钟
$start_time = time();

// 主循环
while (true) {
    // 检查执行时间限制
    if (time() - $start_time > $max_execution_time) {
        echo "data: " . json_encode([
            'type' => 'timeout',
            'message' => '连接超时，请刷新页面重新连接'
        ]) . "\n\n";
        flush();
        break;
    }

    // 检查连接状态
    if (connection_aborted()) {
        break;
    }

    try {
        // 查询待推送的通知（包括pending和unread状态）
        $stmt = $pdo->prepare("
            SELECT id, type, title, content, data, priority, created_at
            FROM realtime_notifications
            WHERE user_id = ?
            AND status IN ('pending', 'unread')
            AND (expires_at IS NULL OR expires_at > NOW())
            ORDER BY priority DESC, created_at ASC
            LIMIT 10
        ");
        $stmt->execute([$user_id]);
        $notifications = $stmt->fetchAll();

        if (!empty($notifications)) {
            foreach ($notifications as $notification) {
                // 解析附加数据
                $data = json_decode($notification['data'], true) ?? [];

                // 构建推送消息
                $message = [
                    'type' => $notification['type'],
                    'id' => $notification['id'],
                    'title' => $notification['title'],
                    'content' => $notification['content'],
                    'data' => $data,
                    'priority' => $notification['priority'],
                    'timestamp' => strtotime($notification['created_at'])
                ];

                // 发送通知
                echo "data: " . json_encode($message) . "\n\n";
                flush();

                // 标记为已推送
                $updateStmt = $pdo->prepare("
                    UPDATE realtime_notifications
                    SET status = 'delivered', delivered_at = NOW()
                    WHERE id = ?
                ");
                $updateStmt->execute([$notification['id']]);

                // 记录推送日志
                error_log("推送通知给用户 {$user_id}: " . $notification['title']);
            }
        }

        // 发送心跳包
        if (time() - $last_check >= $heartbeat_interval) {
            echo "data: " . json_encode([
                'type' => 'heartbeat',
                'timestamp' => time(),
                'user_id' => $user_id
            ]) . "\n\n";
            flush();
            $last_check = time();

            // 更新用户活动时间
            $stmt = $pdo->prepare("
                UPDATE users
                SET last_activity = NOW()
                WHERE id = ?
            ");
            $stmt->execute([$user_id]);
        }

    } catch (Exception $e) {
        error_log("SSE通知服务错误: " . $e->getMessage());
        echo "data: " . json_encode([
            'type' => 'error',
            'message' => '服务暂时不可用'
        ]) . "\n\n";
        flush();
    }

    // 短暂休眠，避免过度占用CPU
    sleep(2);
}

// 连接结束时更新用户状态
try {
    $stmt = $pdo->prepare("
        UPDATE users
        SET online_status = 'offline', last_activity = NOW()
        WHERE id = ?
    ");
    $stmt->execute([$user_id]);
} catch (Exception $e) {
    error_log("更新用户离线状态失败: " . $e->getMessage());
}
?>
