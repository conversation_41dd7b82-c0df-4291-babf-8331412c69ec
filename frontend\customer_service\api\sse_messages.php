<?php
// Server-Sent Events 实时消息推送
header('Content-Type: text/event-stream');
header('Cache-Control: no-cache');
header('Connection: keep-alive');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Credentials: true');

// 防止输出缓冲
if (ob_get_level()) {
    ob_end_clean();
}

session_start();

// 检查用户登录状态
if (!isset($_SESSION['user_id'])) {
    echo "data: " . json_encode(['error' => '用户未登录']) . "\n\n";
    flush();
    exit;
}

$userId = $_SESSION['user_id'];
$sessionId = $_GET['session_id'] ?? '';

if (empty($sessionId)) {
    echo "data: " . json_encode(['error' => '会话ID不能为空']) . "\n\n";
    flush();
    exit;
}

// 引用数据库配置文件
require_once '../../../houtai_backup/db_config.php';

try {
    $pdo = getDbConnection();
    
    // 验证会话权限
    $stmt = $pdo->prepare("SELECT * FROM customer_service_sessions WHERE session_id = ? AND user_id = ?");
    $stmt->execute([$sessionId, $userId]);
    $session = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$session) {
        echo "data: " . json_encode(['error' => '会话不存在或无权限']) . "\n\n";
        flush();
        exit;
    }
    
    // 发送连接成功消息
    echo "data: " . json_encode([
        'type' => 'connected',
        'message' => '实时连接已建立',
        'session_id' => $sessionId,
        'timestamp' => time()
    ]) . "\n\n";
    flush();
    
    $lastCheckTime = time();
    
    // 持续监听新消息
    while (true) {
        try {
            // 检查连接是否还活着
            if (connection_aborted()) {
                break;
            }
            
            // 获取新消息（客服发送的）
            $stmt = $pdo->prepare("
                SELECT id, sender_type, sender_name, content, created_at, UNIX_TIMESTAMP(created_at) as timestamp
                FROM customer_service_messages 
                WHERE session_id = ? 
                AND sender_type = 'customer_service' 
                AND UNIX_TIMESTAMP(created_at) > ?
                ORDER BY created_at ASC
            ");
            $stmt->execute([$sessionId, $lastCheckTime]);
            $newMessages = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            if (!empty($newMessages)) {
                foreach ($newMessages as $message) {
                    $eventData = [
                        'type' => 'new_message',
                        'message_id' => $message['id'],
                        'sender_type' => $message['sender_type'],
                        'sender_name' => $message['sender_name'],
                        'content' => $message['content'],
                        'created_at' => $message['created_at'],
                        'timestamp' => $message['timestamp'],
                        'session_id' => $sessionId
                    ];
                    
                    echo "data: " . json_encode($eventData) . "\n\n";
                    flush();
                    
                    $lastCheckTime = $message['timestamp'];
                }
            }
            
            // 检查会话状态变化
            $stmt = $pdo->prepare("SELECT status, customer_service_id FROM customer_service_sessions WHERE session_id = ?");
            $stmt->execute([$sessionId]);
            $currentSession = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if ($currentSession && $currentSession['status'] !== $session['status']) {
                $eventData = [
                    'type' => 'session_status_changed',
                    'old_status' => $session['status'],
                    'new_status' => $currentSession['status'],
                    'customer_service_id' => $currentSession['customer_service_id'],
                    'session_id' => $sessionId,
                    'timestamp' => time()
                ];
                
                echo "data: " . json_encode($eventData) . "\n\n";
                flush();
                
                $session['status'] = $currentSession['status'];
            }
            
            // 发送心跳
            if (time() % 30 == 0) {
                echo "data: " . json_encode([
                    'type' => 'heartbeat',
                    'timestamp' => time()
                ]) . "\n\n";
                flush();
            }
            
            // 等待2秒再检查
            sleep(2);
            
        } catch (Exception $e) {
            echo "data: " . json_encode([
                'type' => 'error',
                'message' => $e->getMessage(),
                'timestamp' => time()
            ]) . "\n\n";
            flush();
            break;
        }
    }
    
} catch (Exception $e) {
    echo "data: " . json_encode([
        'type' => 'error',
        'message' => '数据库连接失败: ' . $e->getMessage(),
        'timestamp' => time()
    ]) . "\n\n";
    flush();
}
?>
