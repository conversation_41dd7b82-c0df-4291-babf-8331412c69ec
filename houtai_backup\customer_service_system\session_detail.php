<?php
// 客服会话详情页面
header('Content-Type: text/html; charset=UTF-8');
ini_set('default_charset', 'UTF-8');

session_start();

// 检查登录状态
if (!isset($_SESSION['cs_logged_in']) || !$_SESSION['cs_logged_in']) {
    echo '<script>alert("请先登录客服系统"); window.close();</script>';
    exit;
}

// 引用数据库配置文件
require_once '../db_config.php';

// 获取会话ID
$session_id = $_GET['id'] ?? '';
if (empty($session_id)) {
    echo '<script>alert("会话ID不能为空"); window.close();</script>';
    exit;
}

// 获取客服信息
$cs_name = $_SESSION['cs_name'] ?? '客服';
$cs_role = $_SESSION['cs_role'] ?? 'customer_service';
$cs_user_id = $_SESSION['cs_user_id'] ?? 0;

// 获取会话详情
try {
    $pdo = getDbConnection();

    // 获取会话基本信息
    $stmt = $pdo->prepare("
        SELECT s.*,
               cs.name as cs_name,
               cs.employee_id as cs_employee_id,
               cs.avatar as cs_avatar
        FROM customer_service_sessions s
        LEFT JOIN customer_service_users cs ON s.customer_service_id = cs.id
        WHERE s.session_id = ?
    ");
    $stmt->execute([$session_id]);
    $session = $stmt->fetch(PDO::FETCH_ASSOC);

    if (!$session) {
        echo '<script>alert("会话不存在"); window.close();</script>';
        exit;
    }

    // 获取会话消息
    $stmt = $pdo->prepare("
        SELECT * FROM customer_service_messages
        WHERE session_id = ?
        ORDER BY created_at ASC
    ");
    $stmt->execute([$session_id]);
    $messages = $stmt->fetchAll(PDO::FETCH_ASSOC);

} catch (Exception $e) {
    echo '<script>alert("数据加载失败: ' . $e->getMessage() . '"); window.close();</script>';
    exit;
}

// 状态标签映射
$status_labels = [
    'waiting' => '等待中',
    'active' => '进行中',
    'closed' => '已结束',
    'transferred' => '已转接'
];

$priority_labels = [
    'low' => '低',
    'normal' => '普通',
    'high' => '高',
    'urgent' => '紧急'
];
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>会话详情 - <?php echo htmlspecialchars($session['session_id']); ?></title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        /* 基础重置 */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', 'Microsoft YaHei', Tahoma, Geneva, Verdana, sans-serif;
            background: #f8f9fa;
            color: #333333;
            line-height: 1.6;
            height: 100vh;
            overflow: hidden;
        }

        /* 主容器 */
        .session-container {
            display: flex;
            flex-direction: column;
            height: 100vh;
            background: white;
        }

        /* 顶部工具栏 */
        .session-header {
            background: linear-gradient(135deg, #6F7BF5 0%, #4D5DFB 100%);
            color: white;
            padding: 15px 20px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            flex-shrink: 0;
        }

        .session-info {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .session-avatar {
            width: 40px;
            height: 40px;
            background: rgba(255,255,255,0.2);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 18px;
        }

        .session-details h2 {
            font-size: 16px;
            font-weight: 600;
            margin-bottom: 2px;
        }

        .session-meta {
            font-size: 12px;
            opacity: 0.9;
            display: flex;
            gap: 15px;
        }

        .session-actions {
            display: flex;
            gap: 10px;
        }

        .header-btn {
            padding: 8px 16px;
            background: rgba(255,255,255,0.2);
            border: none;
            border-radius: 6px;
            color: white;
            cursor: pointer;
            font-size: 12px;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 6px;
        }

        .header-btn:hover {
            background: rgba(255,255,255,0.3);
            transform: translateY(-1px);
        }

        /* 主内容区域 */
        .session-main {
            display: flex;
            flex: 1;
            overflow: hidden;
        }

        /* 左侧信息面板 */
        .session-sidebar {
            width: 300px;
            background: #f8f9fa;
            border-right: 1px solid #e9ecef;
            display: flex;
            flex-direction: column;
            overflow-y: auto;
        }

        .sidebar-section {
            padding: 20px;
            border-bottom: 1px solid #e9ecef;
        }

        .sidebar-section h3 {
            font-size: 14px;
            font-weight: 600;
            color: #333;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .info-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 12px;
            font-size: 13px;
        }

        .info-label {
            color: #666;
            font-weight: 500;
        }

        .info-value {
            color: #333;
            font-weight: 600;
        }

        .status-badge {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 11px;
            font-weight: 500;
            text-transform: uppercase;
        }

        .status-waiting { background: #fff3cd; color: #856404; }
        .status-active { background: #d1ecf1; color: #0c5460; }
        .status-closed { background: #d4edda; color: #155724; }
        .status-transferred { background: #f8d7da; color: #721c24; }

        .priority-badge {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 11px;
            font-weight: 500;
        }

        .priority-low { background: #e2e3e5; color: #6c757d; }
        .priority-normal { background: #cce5ff; color: #004085; }
        .priority-high { background: #fff3cd; color: #856404; }
        .priority-urgent { background: #f8d7da; color: #721c24; }

        /* 聊天区域 */
        .chat-area {
            flex: 1;
            display: flex;
            flex-direction: column;
            background: white;
        }

        .chat-messages {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
            background: #fafbfc;
        }

        .message {
            margin-bottom: 20px;
            display: flex;
            align-items: flex-start;
            gap: 12px;
        }

        .message.user {
            flex-direction: row-reverse;
        }

        .message-avatar {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 14px;
            color: white;
            flex-shrink: 0;
        }

        .message.user .message-avatar {
            background: linear-gradient(135deg, #FF6B6B, #FF8E8E);
        }

        .message.customer_service .message-avatar {
            background: linear-gradient(135deg, #6F7BF5, #4D5DFB);
        }

        .message.system .message-avatar {
            background: linear-gradient(135deg, #06D6A0, #4ECDC4);
        }

        .message.bot .message-avatar {
            background: linear-gradient(135deg, #FFD166, #FFA726);
        }

        .message-content {
            flex: 1;
            max-width: 70%;
        }

        .message-header {
            display: flex;
            align-items: center;
            gap: 8px;
            margin-bottom: 4px;
        }

        .message-sender {
            font-size: 12px;
            font-weight: 600;
            color: #666;
        }

        .message-time {
            font-size: 11px;
            color: #999;
        }

        .message-bubble {
            background: white;
            padding: 12px 16px;
            border-radius: 16px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            font-size: 14px;
            line-height: 1.5;
            position: relative;
        }

        .message.user .message-bubble {
            background: linear-gradient(135deg, #6F7BF5, #4D5DFB);
            color: white;
        }

        .message.system .message-bubble {
            background: #f8f9fa;
            color: #666;
            font-style: italic;
            text-align: center;
        }

        /* 输入区域 */
        .chat-input {
            padding: 20px;
            background: white;
            border-top: 1px solid #e9ecef;
            flex-shrink: 0;
        }

        .input-container {
            display: flex;
            gap: 12px;
            align-items: flex-end;
        }

        .input-box {
            flex: 1;
            min-height: 40px;
            max-height: 120px;
            padding: 12px 16px;
            border: 2px solid #e9ecef;
            border-radius: 20px;
            font-size: 14px;
            resize: none;
            outline: none;
            transition: all 0.3s ease;
            font-family: inherit;
        }

        .input-box:focus {
            border-color: #6F7BF5;
            box-shadow: 0 0 0 3px rgba(111,123,245,0.1);
        }

        .input-actions {
            display: flex;
            gap: 8px;
        }

        .input-btn {
            width: 40px;
            height: 40px;
            border: none;
            border-radius: 50%;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
            font-size: 16px;
        }

        .send-btn {
            background: linear-gradient(135deg, #6F7BF5, #4D5DFB);
            color: white;
        }

        .send-btn:hover {
            transform: scale(1.05);
            box-shadow: 0 4px 12px rgba(111,123,245,0.3);
        }

        .attach-btn {
            background: #f8f9fa;
            color: #666;
        }

        .attach-btn:hover {
            background: #e9ecef;
            color: #333;
        }

        .send-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none;
        }

        /* 表情面板 */
        .emoji-panel {
            position: absolute;
            bottom: 100%;
            left: 0;
            right: 0;
            background: white;
            border: 1px solid #e9ecef;
            border-radius: 12px;
            padding: 16px;
            box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
            z-index: 1000;
            max-height: 200px;
            overflow-y: auto;
        }

        .emoji-grid {
            display: grid;
            grid-template-columns: repeat(8, 1fr);
            gap: 8px;
        }

        .emoji-item {
            width: 32px;
            height: 32px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 20px;
            cursor: pointer;
            border-radius: 6px;
            transition: background 0.2s;
        }

        .emoji-item:hover {
            background: #f0f0f0;
        }

        /* 快捷回复 */
        .quick-replies {
            display: flex;
            gap: 8px;
            margin-top: 12px;
            flex-wrap: wrap;
        }

        .quick-reply-btn {
            padding: 6px 12px;
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 16px;
            font-size: 12px;
            color: #666;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 4px;
        }

        .quick-reply-btn:hover {
            background: #6F7BF5;
            color: white;
            border-color: #6F7BF5;
            transform: translateY(-1px);
        }

        /* 禁用状态 */
        .chat-input-disabled {
            padding: 20px;
            background: #f8f9fa;
            border-top: 1px solid #e9ecef;
            text-align: center;
        }

        .disabled-message {
            color: #999;
            font-size: 14px;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
        }

        /* 空状态 */
        .empty-messages {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            height: 100%;
            color: #999;
            text-align: center;
        }

        .empty-messages i {
            font-size: 48px;
            margin-bottom: 16px;
            color: #ddd;
        }

        /* 滚动条样式 */
        .chat-messages::-webkit-scrollbar,
        .session-sidebar::-webkit-scrollbar {
            width: 6px;
        }

        .chat-messages::-webkit-scrollbar-track,
        .session-sidebar::-webkit-scrollbar-track {
            background: #f1f1f1;
        }

        .chat-messages::-webkit-scrollbar-thumb,
        .session-sidebar::-webkit-scrollbar-thumb {
            background: #c1c1c1;
            border-radius: 3px;
        }

        .chat-messages::-webkit-scrollbar-thumb:hover,
        .session-sidebar::-webkit-scrollbar-thumb:hover {
            background: #a8a8a8;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .session-sidebar {
                width: 250px;
            }

            .message-content {
                max-width: 85%;
            }
        }
    </style>
</head>
<body>
    <div class="session-container">
        <!-- 顶部工具栏 -->
        <div class="session-header">
            <div class="session-info">
                <div class="session-avatar">
                    <i class="fas fa-user"></i>
                </div>
                <div class="session-details">
                    <h2><?php echo htmlspecialchars($session['user_name'] ?? '未知用户'); ?></h2>
                    <div class="session-meta">
                        <span><i class="fas fa-id-card"></i> <?php echo htmlspecialchars($session['session_id']); ?></span>
                        <span><i class="fas fa-phone"></i> <?php echo htmlspecialchars($session['user_phone'] ?? '未知'); ?></span>
                        <span><i class="fas fa-clock"></i> <?php echo date('Y-m-d H:i', strtotime($session['started_at'])); ?></span>
                    </div>
                </div>
            </div>
            <div class="session-actions">
                <?php if ($session['status'] === 'active'): ?>
                    <button class="header-btn" onclick="transferSession()">
                        <i class="fas fa-exchange-alt"></i> 转接
                    </button>
                    <button class="header-btn" onclick="endSession()">
                        <i class="fas fa-times"></i> 结束会话
                    </button>
                <?php endif; ?>
                <button class="header-btn" onclick="window.close()">
                    <i class="fas fa-times"></i> 关闭
                </button>
            </div>
        </div>

        <!-- 主内容区域 -->
        <div class="session-main">
            <!-- 左侧信息面板 -->
            <div class="session-sidebar">
                <!-- 会话信息 -->
                <div class="sidebar-section">
                    <h3><i class="fas fa-info-circle"></i> 会话信息</h3>
                    <div class="info-item">
                        <span class="info-label">状态</span>
                        <span class="status-badge status-<?php echo $session['status']; ?>">
                            <?php echo $status_labels[$session['status']] ?? $session['status']; ?>
                        </span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">优先级</span>
                        <span class="priority-badge priority-<?php echo $session['priority']; ?>">
                            <?php echo $priority_labels[$session['priority']] ?? $session['priority']; ?>
                        </span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">来源</span>
                        <span class="info-value"><?php echo strtoupper($session['source']); ?></span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">消息数</span>
                        <span class="info-value"><?php echo $session['message_count']; ?></span>
                    </div>
                    <?php if ($session['duration'] > 0): ?>
                    <div class="info-item">
                        <span class="info-label">时长</span>
                        <span class="info-value"><?php echo gmdate('H:i:s', $session['duration']); ?></span>
                    </div>
                    <?php endif; ?>
                </div>

                <!-- 客服信息 -->
                <div class="sidebar-section">
                    <h3><i class="fas fa-headset"></i> 客服信息</h3>
                    <?php if ($session['cs_name']): ?>
                        <div class="info-item">
                            <span class="info-label">姓名</span>
                            <span class="info-value"><?php echo htmlspecialchars($session['cs_name']); ?></span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">工号</span>
                            <span class="info-value"><?php echo htmlspecialchars($session['cs_employee_id']); ?></span>
                        </div>
                    <?php else: ?>
                        <div class="info-item">
                            <span class="info-value" style="color: #999;">未分配客服</span>
                        </div>
                    <?php endif; ?>
                </div>

                <!-- 满意度评价 -->
                <?php if ($session['satisfaction_score']): ?>
                <div class="sidebar-section">
                    <h3><i class="fas fa-star"></i> 满意度评价</h3>
                    <div class="info-item">
                        <span class="info-label">评分</span>
                        <span class="info-value">
                            <?php for ($i = 1; $i <= 5; $i++): ?>
                                <i class="fas fa-star" style="color: <?php echo $i <= $session['satisfaction_score'] ? '#FFD166' : '#ddd'; ?>"></i>
                            <?php endfor; ?>
                        </span>
                    </div>
                    <?php if ($session['satisfaction_comment']): ?>
                    <div class="info-item" style="flex-direction: column; align-items: flex-start;">
                        <span class="info-label">评价</span>
                        <span class="info-value" style="margin-top: 5px; font-size: 12px; line-height: 1.4;">
                            <?php echo htmlspecialchars($session['satisfaction_comment']); ?>
                        </span>
                    </div>
                    <?php endif; ?>
                </div>
                <?php endif; ?>
            </div>

            <!-- 聊天区域 -->
            <div class="chat-area">
                <div class="chat-messages" id="chatMessages">
                    <?php if (empty($messages)): ?>
                        <div class="empty-messages">
                            <i class="fas fa-comments"></i>
                            <h3>暂无消息</h3>
                            <p>这个会话还没有任何消息记录</p>
                        </div>
                    <?php else: ?>
                        <?php foreach ($messages as $message): ?>
                            <div class="message <?php echo $message['sender_type']; ?>">
                                <div class="message-avatar">
                                    <?php
                                    $icons = [
                                        'user' => 'fas fa-user',
                                        'customer_service' => 'fas fa-headset',
                                        'system' => 'fas fa-cog',
                                        'bot' => 'fas fa-robot'
                                    ];
                                    ?>
                                    <i class="<?php echo $icons[$message['sender_type']] ?? 'fas fa-user'; ?>"></i>
                                </div>
                                <div class="message-content">
                                    <div class="message-header">
                                        <span class="message-sender">
                                            <?php echo htmlspecialchars($message['sender_name'] ?? ucfirst($message['sender_type'])); ?>
                                        </span>
                                        <span class="message-time">
                                            <?php echo date('H:i', strtotime($message['created_at'])); ?>
                                        </span>
                                    </div>
                                    <div class="message-bubble">
                                        <?php echo nl2br(htmlspecialchars($message['content'])); ?>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    <?php endif; ?>
                </div>

                <!-- 输入区域 -->
                <?php if ($session['status'] === 'active' || $session['status'] === 'waiting'): ?>
                <div class="chat-input">
                    <div class="input-container">
                        <textarea class="input-box" placeholder="输入回复消息..." id="messageInput" rows="1"></textarea>
                        <div class="input-actions">
                            <button class="input-btn attach-btn" title="附件" onclick="selectFile()">
                                <i class="fas fa-paperclip"></i>
                            </button>
                            <button class="input-btn emoji-btn" title="表情" onclick="toggleEmoji()">
                                <i class="fas fa-smile"></i>
                            </button>
                            <button class="input-btn send-btn" onclick="sendMessage()" title="发送" id="sendBtn">
                                <i class="fas fa-paper-plane"></i>
                            </button>
                        </div>

                        <!-- 表情面板 -->
                        <div class="emoji-panel" id="emojiPanel" style="display: none;">
                            <div class="emoji-grid" id="emojiGrid">
                                <!-- 表情将通过JavaScript动态生成 -->
                            </div>
                        </div>
                    </div>

                    <!-- 快捷回复 -->
                    <div class="quick-replies">
                        <button class="quick-reply-btn" onclick="insertQuickReply('您好！我是客服，很高兴为您服务。')">
                            <i class="fas fa-bolt"></i> 问候语
                        </button>
                        <button class="quick-reply-btn" onclick="insertQuickReply('请问还有其他问题需要帮助吗？')">
                            <i class="fas fa-question"></i> 询问
                        </button>
                        <button class="quick-reply-btn" onclick="insertQuickReply('感谢您的耐心等待，问题已为您解决。')">
                            <i class="fas fa-check"></i> 解决
                        </button>
                        <button class="quick-reply-btn" onclick="insertQuickReply('如有其他问题，欢迎随时联系我们。')">
                            <i class="fas fa-heart"></i> 结束语
                        </button>
                    </div>

                    <!-- 隐藏的文件输入 -->
                    <input type="file" id="fileInput" style="display: none;" onchange="handleFileUpload(event)">
                </div>
                <?php elseif ($session['status'] === 'closed'): ?>
                <div class="chat-input-disabled">
                    <div class="disabled-message">
                        <i class="fas fa-lock"></i>
                        <span>会话已结束，无法发送消息</span>
                    </div>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <script>
        // 常用表情列表
        const emojis = [
            '😀', '😃', '😄', '😁', '😆', '😅', '😂', '🤣',
            '😊', '😇', '🙂', '🙃', '😉', '😌', '😍', '🥰',
            '😘', '😗', '😙', '😚', '😋', '😛', '😝', '😜',
            '🤪', '🤨', '🧐', '🤓', '😎', '🤩', '🥳', '😏',
            '😒', '😞', '😔', '😟', '😕', '🙁', '☹️', '😣',
            '😖', '😫', '😩', '🥺', '😢', '😭', '😤', '😠',
            '😡', '🤬', '🤯', '😳', '🥵', '🥶', '😱', '😨',
            '😰', '😥', '😓', '🤗', '🤔', '🤭', '🤫', '🤥',
            '👍', '👎', '👌', '✌️', '🤞', '🤟', '🤘', '👏',
            '🙌', '👐', '🤲', '🤝', '🙏', '❤️', '💕', '💖'
        ];

        // 页面加载时初始化
        document.addEventListener('DOMContentLoaded', function() {
            initializeEmojis();
            setupInputEvents();
        });

        // 初始化表情面板
        function initializeEmojis() {
            const emojiGrid = document.getElementById('emojiGrid');
            if (emojiGrid) {
                emojis.forEach(emoji => {
                    const emojiItem = document.createElement('div');
                    emojiItem.className = 'emoji-item';
                    emojiItem.textContent = emoji;
                    emojiItem.onclick = () => insertEmoji(emoji);
                    emojiGrid.appendChild(emojiItem);
                });
            }
        }

        // 设置输入事件
        function setupInputEvents() {
            const messageInput = document.getElementById('messageInput');
            const sendBtn = document.getElementById('sendBtn');

            if (messageInput) {
                // 自动调整输入框高度
                messageInput.addEventListener('input', function() {
                    this.style.height = 'auto';
                    this.style.height = Math.min(this.scrollHeight, 120) + 'px';

                    // 控制发送按钮状态
                    if (sendBtn) {
                        sendBtn.disabled = !this.value.trim();
                    }
                });

                // 回车发送消息
                messageInput.addEventListener('keydown', function(e) {
                    if (e.key === 'Enter' && !e.shiftKey) {
                        e.preventDefault();
                        sendMessage();
                    }
                });

                // 初始化发送按钮状态
                if (sendBtn) {
                    sendBtn.disabled = !messageInput.value.trim();
                }
            }

            // 点击外部关闭表情面板
            document.addEventListener('click', function(e) {
                if (!e.target.closest('.input-container')) {
                    hideEmoji();
                }
            });
        }

        // 滚动到底部
        function scrollToBottom() {
            const chatMessages = document.getElementById('chatMessages');
            chatMessages.scrollTop = chatMessages.scrollHeight;
        }

        // 切换表情面板
        function toggleEmoji() {
            const panel = document.getElementById('emojiPanel');
            if (panel.style.display === 'block') {
                hideEmoji();
            } else {
                showEmoji();
            }
        }

        // 显示表情面板
        function showEmoji() {
            const panel = document.getElementById('emojiPanel');
            panel.style.display = 'block';
        }

        // 隐藏表情面板
        function hideEmoji() {
            const panel = document.getElementById('emojiPanel');
            panel.style.display = 'none';
        }

        // 插入表情
        function insertEmoji(emoji) {
            const input = document.getElementById('messageInput');
            const start = input.selectionStart;
            const end = input.selectionEnd;
            const text = input.value;

            input.value = text.substring(0, start) + emoji + text.substring(end);
            input.selectionStart = input.selectionEnd = start + emoji.length;
            input.focus();

            // 触发input事件以调整高度和按钮状态
            input.dispatchEvent(new Event('input'));

            hideEmoji();
        }

        // 插入快捷回复
        function insertQuickReply(text) {
            const input = document.getElementById('messageInput');
            input.value = text;
            input.focus();

            // 触发input事件以调整高度和按钮状态
            input.dispatchEvent(new Event('input'));
        }

        // 选择文件
        function selectFile() {
            document.getElementById('fileInput').click();
        }

        // 处理文件上传
        function handleFileUpload(event) {
            const file = event.target.files[0];
            if (file) {
                // 检查文件大小（限制为10MB）
                if (file.size > 10 * 1024 * 1024) {
                    alert('文件大小不能超过10MB');
                    return;
                }

                // 显示上传中消息
                addMessage('customer_service', '正在上传文件...');

                // 这里应该实现实际的文件上传逻辑
                // 暂时模拟上传成功
                setTimeout(() => {
                    addMessage('customer_service', `[文件] ${file.name}`);
                }, 1000);
            }
        }

        // 发送消息
        async function sendMessage() {
            const input = document.getElementById('messageInput');
            const message = input.value.trim();

            if (!message) return;

            // 添加客服消息到界面
            addMessage('customer_service', message);

            // 清空输入框
            input.value = '';
            input.style.height = 'auto';
            document.getElementById('sendBtn').disabled = true;

            try {
                // 发送消息到服务器
                const response = await fetch('../api/send_cs_message.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        sessionId: '<?php echo $session_id; ?>',
                        message: message
                    })
                });

                const data = await response.json();

                if (!data.success) {
                    console.error('发送消息失败:', data.error);
                    // 可以在这里显示错误提示
                }
            } catch (error) {
                console.error('发送消息失败:', error);
            }
        }

        // 添加消息到聊天区域
        function addMessage(type, content, senderName = null) {
            const messagesContainer = document.getElementById('chatMessages');

            // 如果是空状态，先清除
            const emptyMessages = messagesContainer.querySelector('.empty-messages');
            if (emptyMessages) {
                emptyMessages.remove();
            }

            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${type}`;

            const avatar = document.createElement('div');
            avatar.className = 'message-avatar';

            // 根据消息类型设置头像
            let avatarIcon = '';
            switch(type) {
                case 'user':
                    avatarIcon = '<i class="fas fa-user"></i>';
                    break;
                case 'customer_service':
                    avatarIcon = '<i class="fas fa-headset"></i>';
                    break;
                case 'system':
                    avatarIcon = '<i class="fas fa-cog"></i>';
                    break;
                case 'bot':
                    avatarIcon = '<i class="fas fa-robot"></i>';
                    break;
                default:
                    avatarIcon = '<i class="fas fa-comment"></i>';
            }
            avatar.innerHTML = avatarIcon;

            const messageContent = document.createElement('div');
            messageContent.className = 'message-content';

            // 创建消息头部
            const messageHeader = document.createElement('div');
            messageHeader.className = 'message-header';

            const senderSpan = document.createElement('span');
            senderSpan.className = 'message-sender';
            senderSpan.textContent = senderName || (type === 'customer_service' ? '<?php echo htmlspecialchars($cs_name); ?>' : '用户');

            const timeSpan = document.createElement('span');
            timeSpan.className = 'message-time';
            timeSpan.textContent = new Date().toLocaleTimeString('zh-CN', {
                hour: '2-digit',
                minute: '2-digit'
            });

            messageHeader.appendChild(senderSpan);
            messageHeader.appendChild(timeSpan);

            // 创建消息气泡
            const messageBubble = document.createElement('div');
            messageBubble.className = 'message-bubble';
            messageBubble.innerHTML = content.replace(/\n/g, '<br>');

            messageContent.appendChild(messageHeader);
            messageContent.appendChild(messageBubble);

            messageDiv.appendChild(avatar);
            messageDiv.appendChild(messageContent);

            messagesContainer.appendChild(messageDiv);

            // 滚动到底部
            scrollToBottom();
        }

        // 转接会话
        function transferSession() {
            if (confirm('确定要转接这个会话吗？')) {
                // 实现转接逻辑
                alert('转接功能开发中...');
            }
        }

        // 结束会话
        async function endSession() {
            const reason = prompt('请输入结束会话的原因（可选）：');
            if (reason !== null) { // 用户没有取消
                try {
                    const response = await fetch('../api/close_session.php', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({
                            sessionId: '<?php echo $session_id; ?>',
                            reason: reason
                        })
                    });

                    const data = await response.json();

                    if (data.success) {
                        alert('会话已成功结束！');
                        window.close();
                    } else {
                        alert('结束失败：' + (data.error || '未知错误'));
                    }
                } catch (error) {
                    console.error('结束会话失败:', error);
                    alert('网络错误，请稍后重试');
                }
            }
        }

        // 页面加载完成后滚动到底部
        window.addEventListener('load', function() {
            scrollToBottom();
            startPollingForNewMessages();
        });

        // 防止意外关闭
        window.addEventListener('beforeunload', function(e) {
            if (document.getElementById('messageInput') && document.getElementById('messageInput').value.trim()) {
                e.preventDefault();
                e.returnValue = '';
            }
        });

        // 实时更新会话状态（每30秒）
        setInterval(function() {
            updateSessionStatus();
        }, 30000);

        // 开始轮询新消息
        let lastCheckTime = Math.floor(Date.now() / 1000);
        let pollingInterval = null;

        function startPollingForNewMessages() {
            console.log('🔄 后台开始轮询用户消息，会话ID: <?php echo $session_id; ?>');
            console.log('📅 初始检查时间:', lastCheckTime);

            // 清除之前的轮询
            if (pollingInterval) {
                clearInterval(pollingInterval);
            }

            pollingInterval = setInterval(async function() {
                try {
                    const url = `../api/get_new_user_messages.php?session_id=<?php echo $session_id; ?>&last_check=${lastCheckTime}`;
                    console.log('📡 后台轮询请求:', url);

                    const response = await fetch(url);
                    const data = await response.json();

                    console.log('📨 后台轮询响应:', data);

                    if (data.success && data.messages && data.messages.length > 0) {
                        console.log('🎉 后台收到新用户消息:', data.messages.length, '条');
                        data.messages.forEach(message => {
                            console.log('💬 处理用户消息:', message);
                            if (message.sender_type === 'user') {
                                addMessage('user', message.content, message.sender_name);
                                // 播放提示音
                                playNotificationSound();
                            }
                        });
                        // 更新最后检查时间
                        lastCheckTime = Math.floor(Date.now() / 1000);
                        console.log('⏰ 更新检查时间:', lastCheckTime);
                    } else {
                        console.log('📭 没有新用户消息');
                    }
                } catch (error) {
                    console.error('❌ 后台轮询新消息失败:', error);
                }
            }, 2000); // 每2秒检查一次，更频繁
        }

        // 更新会话状态
        async function updateSessionStatus() {
            try {
                const response = await fetch(`../api/get_session_status.php?session_id=<?php echo $session_id; ?>`);
                const data = await response.json();

                if (data.success && data.session) {
                    // 更新页面上的状态信息
                    updateSessionInfo(data.session);
                }
            } catch (error) {
                console.error('更新会话状态失败:', error);
            }
        }

        // 更新会话信息
        function updateSessionInfo(session) {
            // 更新状态标签
            const statusElement = document.querySelector('.status-badge');
            if (statusElement) {
                statusElement.className = `status-badge status-${session.status}`;
                const statusLabels = {
                    'waiting': '等待中',
                    'active': '进行中',
                    'closed': '已结束',
                    'transferred': '已转接'
                };
                statusElement.textContent = statusLabels[session.status] || session.status;
            }

            // 更新消息数量
            const messageCountElement = document.querySelector('.info-item:nth-child(4) .info-value');
            if (messageCountElement) {
                messageCountElement.textContent = session.message_count;
            }
        }

        // 播放通知音
        function playNotificationSound() {
            const audio = new Audio('data:audio/wav;base64,UklGRnoGAABXQVZFZm10IBAAAAABAAEAQB8AAEAfAAABAAgAZGF0YQoGAACBhYqFbF1fdJivrJBhNjVgodDbq2EcBj+a2/LDciUFLIHO8tiJNwgZaLvt559NEAxQp+PwtmMcBjiR1/LMeSwFJHfH8N2QQAoUXrTp66hVFApGn+DyvmwhBSuBzvLZiTYIG2m98OScTgwOUarm7blmGgU7k9n1unEiBC13yO/eizEIHWq+8+OWT');
            audio.volume = 0.2;
            audio.play().catch(e => console.log('无法播放提示音'));
        }
    </script>
</body>
</html>
