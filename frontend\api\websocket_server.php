<?php
/**
 * WebSocket服务器 - 实时通知推送
 * 使用轮询方式模拟WebSocket功能
 */

// 禁用错误输出
error_reporting(0);
ini_set('display_errors', 0);

header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// 处理OPTIONS预检请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

try {
    $pdo = new PDO(
        "mysql:host=localhost;dbname=quwanplanet;charset=utf8mb4",
        "quwanplanet",
        "nJmJm23FB4Xn6Fc3",
        [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC
        ]
    );

    $user_id = intval($_GET['user_id'] ?? 0);
    $action = $_GET['action'] ?? 'poll';
    $last_check = $_GET['last_check'] ?? '';

    $response = [
        'success' => true,
        'action' => $action,
        'user_id' => $user_id,
        'timestamp' => date('Y-m-d H:i:s'),
        'data' => []
    ];

    switch ($action) {
        case 'poll':
            // 轮询新通知 - 查询未送达的通知
            $where_clause = "user_id = ? AND status = 'pending' AND delivered_at IS NULL";
            $params = [$user_id];

            $stmt = $pdo->prepare("
                SELECT id, type, title, content, data, created_at, expires_at
                FROM realtime_notifications
                WHERE {$where_clause}
                ORDER BY created_at ASC
                LIMIT 10
            ");
            $stmt->execute($params);
            $notifications = $stmt->fetchAll();

            $response['data']['notifications'] = $notifications;
            $response['data']['count'] = count($notifications);

            // 添加调试信息
            $response['debug'] = [
                'query' => $where_clause,
                'params' => $params,
                'found_notifications' => count($notifications)
            ];

            // 更新用户最后检查时间
            if ($user_id > 0) {
                $stmt = $pdo->prepare("
                    UPDATE users
                    SET last_notification_check = NOW(), last_activity = NOW()
                    WHERE id = ?
                ");
                $stmt->execute([$user_id]);
            }

            // 标记通知为已送达（但不改变status）
            if (!empty($notifications)) {
                $notification_ids = array_column($notifications, 'id');
                $placeholders = str_repeat('?,', count($notification_ids) - 1) . '?';
                $stmt = $pdo->prepare("
                    UPDATE realtime_notifications
                    SET delivered_at = NOW()
                    WHERE id IN ({$placeholders})
                ");
                $stmt->execute($notification_ids);

                $response['debug']['delivered_ids'] = $notification_ids;
            }

            break;

        case 'mark_read':
            // 标记通知为已读
            $notification_id = intval($_GET['notification_id'] ?? 0);
            if ($notification_id > 0) {
                $stmt = $pdo->prepare("
                    UPDATE realtime_notifications
                    SET status = 'read', read_at = NOW()
                    WHERE id = ? AND user_id = ?
                ");
                $stmt->execute([$notification_id, $user_id]);
                $response['data']['marked_read'] = $notification_id;
            }
            break;

        case 'heartbeat':
            // 心跳检测
            if ($user_id > 0) {
                $stmt = $pdo->prepare("
                    UPDATE users
                    SET online_status = 'online', last_activity = NOW()
                    WHERE id = ?
                ");
                $stmt->execute([$user_id]);
            }
            $response['data']['status'] = 'online';
            break;

        case 'disconnect':
            // 断开连接
            if ($user_id > 0) {
                $stmt = $pdo->prepare("
                    UPDATE users
                    SET online_status = 'offline'
                    WHERE id = ?
                ");
                $stmt->execute([$user_id]);
            }
            $response['data']['status'] = 'offline';
            break;

        default:
            $response['success'] = false;
            $response['error'] = '未知操作';
    }

} catch (Exception $e) {
    $response = [
        'success' => false,
        'error' => $e->getMessage(),
        'action' => $action ?? 'unknown'
    ];
}

echo json_encode($response, JSON_UNESCAPED_UNICODE);
?>
