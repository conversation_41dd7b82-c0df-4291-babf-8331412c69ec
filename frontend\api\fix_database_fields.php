<?php
/**
 * 修复数据库字段问题
 * 添加缺失的字段
 */

header('Content-Type: application/json; charset=utf-8');

try {
    $pdo = new PDO(
        "mysql:host=localhost;dbname=quwanplanet;charset=utf8mb4",
        "quwanplanet",
        "nJmJm23FB4Xn6Fc3",
        [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC
        ]
    );
    
    $response = [
        'success' => true,
        'message' => '数据库字段修复完成',
        'operations' => []
    ];
    
    // 检查并添加 realtime_notifications 表的 priority 字段
    try {
        $stmt = $pdo->query("SHOW COLUMNS FROM realtime_notifications LIKE 'priority'");
        if ($stmt->rowCount() == 0) {
            $pdo->exec("ALTER TABLE realtime_notifications ADD COLUMN priority tinyint(1) NOT NULL DEFAULT 1 COMMENT '优先级 1-5'");
            $response['operations'][] = '添加 realtime_notifications.priority 字段';
        } else {
            $response['operations'][] = 'realtime_notifications.priority 字段已存在';
        }
    } catch (Exception $e) {
        $response['operations'][] = 'realtime_notifications.priority 字段添加失败: ' . $e->getMessage();
    }
    
    // 检查并添加 realtime_notifications 表的其他可能缺失的字段
    $fields_to_check = [
        'expires_at' => "ALTER TABLE realtime_notifications ADD COLUMN expires_at datetime DEFAULT NULL COMMENT '过期时间'",
        'delivered_at' => "ALTER TABLE realtime_notifications ADD COLUMN delivered_at datetime DEFAULT NULL COMMENT '送达时间'",
        'read_at' => "ALTER TABLE realtime_notifications ADD COLUMN read_at datetime DEFAULT NULL COMMENT '阅读时间'"
    ];
    
    foreach ($fields_to_check as $field => $sql) {
        try {
            $stmt = $pdo->query("SHOW COLUMNS FROM realtime_notifications LIKE '{$field}'");
            if ($stmt->rowCount() == 0) {
                $pdo->exec($sql);
                $response['operations'][] = "添加 realtime_notifications.{$field} 字段";
            } else {
                $response['operations'][] = "realtime_notifications.{$field} 字段已存在";
            }
        } catch (Exception $e) {
            $response['operations'][] = "realtime_notifications.{$field} 字段添加失败: " . $e->getMessage();
        }
    }
    
    // 检查并添加 users 表的字段
    $user_fields = [
        'online_status' => "ALTER TABLE users ADD COLUMN online_status varchar(10) DEFAULT 'offline' COMMENT '在线状态'",
        'last_activity' => "ALTER TABLE users ADD COLUMN last_activity datetime DEFAULT NULL COMMENT '最后活动时间'",
        'last_notification_check' => "ALTER TABLE users ADD COLUMN last_notification_check datetime DEFAULT NULL COMMENT '最后检查通知时间'",
        'notification_token' => "ALTER TABLE users ADD COLUMN notification_token varchar(64) DEFAULT NULL COMMENT '通知令牌'"
    ];
    
    foreach ($user_fields as $field => $sql) {
        try {
            $stmt = $pdo->query("SHOW COLUMNS FROM users LIKE '{$field}'");
            if ($stmt->rowCount() == 0) {
                $pdo->exec($sql);
                $response['operations'][] = "添加 users.{$field} 字段";
            } else {
                $response['operations'][] = "users.{$field} 字段已存在";
            }
        } catch (Exception $e) {
            $response['operations'][] = "users.{$field} 字段添加失败: " . $e->getMessage();
        }
    }
    
    // 显示当前表结构
    $response['table_structures'] = [];
    
    $tables = ['realtime_notifications', 'verification_codes', 'users'];
    foreach ($tables as $table) {
        try {
            $stmt = $pdo->query("DESCRIBE {$table}");
            $response['table_structures'][$table] = $stmt->fetchAll();
        } catch (Exception $e) {
            $response['table_structures'][$table] = "错误: " . $e->getMessage();
        }
    }
    
} catch (Exception $e) {
    $response = [
        'success' => false,
        'error' => $e->getMessage()
    ];
}

echo json_encode($response, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);
?>
