<?php
session_start();
require_once '../../includes/db_config.php';

// 检查用户是否登录
if (!isset($_SESSION['user_id'])) {
    header('Location: ../login/index.php');
    exit();
}

$user_id = $_SESSION['user_id'];
$chat_type = $_GET['type'] ?? '';
$chat_id = $_GET['id'] ?? '';

if (empty($chat_type) || empty($chat_id)) {
    header('Location: index.php');
    exit();
}

// 连接数据库
try {
    $dsn = "mysql:host={$db_config['host']};dbname={$db_config['dbname']};charset={$db_config['charset']};port={$db_config['port']}";
    $pdo = new PDO($dsn, $db_config['username'], $db_config['password']);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
} catch (PDOException $e) {
    die("数据库连接失败: " . $e->getMessage());
}

// 获取聊天信息
$chat_info = null;
if ($chat_type === 'group') {
    $stmt = $pdo->prepare("SELECT * FROM groups WHERE id = :id");
    $stmt->execute(['id' => $chat_id]);
    $chat_info = $stmt->fetch(PDO::FETCH_ASSOC);
    
    // 检查用户是否是群成员
    $stmt = $pdo->prepare("SELECT * FROM group_members WHERE group_id = :group_id AND user_id = :user_id");
    $stmt->execute(['group_id' => $chat_id, 'user_id' => $user_id]);
    if (!$stmt->fetch()) {
        header('Location: index.php');
        exit();
    }
}

if (!$chat_info) {
    header('Location: index.php');
    exit();
}

// 获取聊天消息
function getChatMessages($pdo, $chat_type, $chat_id, $limit = 50) {
    $stmt = $pdo->prepare("
        SELECT m.*, u.username, u.avatar 
        FROM messages m
        JOIN users u ON m.sender_id = u.id
        WHERE m.chat_type = :chat_type AND m.chat_id = :chat_id
        ORDER BY m.created_at DESC
        LIMIT :limit
    ");
    $stmt->bindParam(':chat_type', $chat_type);
    $stmt->bindParam(':chat_id', $chat_id, PDO::PARAM_INT);
    $stmt->bindParam(':limit', $limit, PDO::PARAM_INT);
    $stmt->execute();
    
    return array_reverse($stmt->fetchAll(PDO::FETCH_ASSOC));
}

$messages = getChatMessages($pdo, $chat_type, $chat_id);

// 获取当前用户信息
$stmt = $pdo->prepare("SELECT username, avatar FROM users WHERE id = :id");
$stmt->execute(['id' => $user_id]);
$current_user = $stmt->fetch(PDO::FETCH_ASSOC);
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <meta name="theme-color" content="#40E0D0">
    <title><?php echo htmlspecialchars($chat_info['name']); ?> - 趣玩星球</title>
    <link rel="stylesheet" href="css/chat.css">
    <!-- 引入字体图标库 -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <!-- Toast提示 -->
    <div class="toast" id="toast"></div>

    <!-- 顶部导航栏 -->
    <div class="header-bar">
        <button class="back-button" onclick="goBack()">
            <i class="fas fa-arrow-left"></i>
        </button>
        <div class="header-info">
            <div class="chat-title"><?php echo htmlspecialchars($chat_info['name']); ?></div>
            <?php if ($chat_type === 'group'): ?>
                <div class="chat-subtitle"><?php echo $chat_info['member_count']; ?>人</div>
            <?php endif; ?>
        </div>
        <button class="more-button" onclick="showChatMenu()">
            <i class="fas fa-ellipsis-h"></i>
        </button>
    </div>

    <!-- 消息列表 -->
    <div class="message-container" id="messageContainer">
        <div class="message-list" id="messageList">
            <?php foreach ($messages as $message): ?>
                <div class="message-item <?php echo $message['sender_id'] == $user_id ? 'own' : 'other'; ?>">
                    <?php if ($message['sender_id'] != $user_id): ?>
                        <div class="message-avatar">
                            <img src="<?php echo htmlspecialchars($message['avatar'] ?: 'https://s1.imagehub.cc/images/2025/04/26/f5c2f26c4564aabcbb56c5fd30f1d652.jpg'); ?>" alt="头像">
                        </div>
                    <?php endif; ?>
                    <div class="message-content">
                        <?php if ($chat_type === 'group' && $message['sender_id'] != $user_id): ?>
                            <div class="message-sender"><?php echo htmlspecialchars($message['username']); ?></div>
                        <?php endif; ?>
                        <div class="message-bubble">
                            <?php echo htmlspecialchars($message['content']); ?>
                        </div>
                        <div class="message-time"><?php echo date('H:i', strtotime($message['created_at'])); ?></div>
                    </div>
                </div>
            <?php endforeach; ?>
        </div>
    </div>

    <!-- 输入区域 -->
    <div class="input-area">
        <div class="input-toolbar">
            <button class="tool-button" id="emojiButton">
                <i class="fas fa-smile"></i>
            </button>
            <button class="tool-button" id="voiceButton">
                <i class="fas fa-microphone"></i>
            </button>
            <div class="input-wrapper">
                <textarea id="messageInput" placeholder="输入消息..." rows="1"></textarea>
            </div>
            <button class="tool-button" id="moreButton">
                <i class="fas fa-plus"></i>
            </button>
            <button class="send-button" id="sendButton" style="display: none;">
                <i class="fas fa-paper-plane"></i>
            </button>
        </div>
    </div>

    <!-- 更多功能弹窗 -->
    <div class="more-menu" id="moreMenu">
        <div class="more-menu-overlay" onclick="closeMoreMenu()"></div>
        <div class="more-menu-content">
            <div class="more-menu-grid">
                <div class="more-menu-item" onclick="selectImage()">
                    <i class="fas fa-image"></i>
                    <span>图片</span>
                </div>
                <div class="more-menu-item" onclick="selectVideo()">
                    <i class="fas fa-video"></i>
                    <span>视频聊天</span>
                </div>
                <div class="more-menu-item" onclick="selectAudio()">
                    <i class="fas fa-phone"></i>
                    <span>语音聊天</span>
                </div>
                <div class="more-menu-item" onclick="selectFile()">
                    <i class="fas fa-file"></i>
                    <span>文件</span>
                </div>
            </div>
        </div>
    </div>

    <!-- 隐藏的文件输入 -->
    <input type="file" id="imageInput" accept="image/*" style="display: none;">
    <input type="file" id="fileInput" style="display: none;">

    <script>
        // 传递PHP变量到JavaScript
        window.chatData = {
            chatType: '<?php echo $chat_type; ?>',
            chatId: <?php echo $chat_id; ?>,
            userId: <?php echo $user_id; ?>,
            userName: '<?php echo htmlspecialchars($current_user['username']); ?>',
            userAvatar: '<?php echo htmlspecialchars($current_user['avatar'] ?: 'https://s1.imagehub.cc/images/2025/04/26/f5c2f26c4564aabcbb56c5fd30f1d652.jpg'); ?>'
        };
    </script>
    <script src="js/chat.js"></script>
</body>
</html>
