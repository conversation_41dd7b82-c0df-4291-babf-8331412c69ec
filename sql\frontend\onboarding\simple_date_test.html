<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>简单日期选择器测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            max-width: 500px;
            margin: 0 auto;
        }
        .form-group {
            margin-bottom: 20px;
        }
        .date-input-container {
            position: relative;
            display: flex;
            align-items: center;
        }
        .date-input-container input {
            flex: 1;
            padding: 12px;
            border: 2px solid #ddd;
            border-radius: 5px;
            font-size: 16px;
        }
        .date-select-btn {
            position: absolute;
            right: 10px;
            background: none;
            border: none;
            cursor: pointer;
            color: #40E0D0;
            font-size: 18px;
        }
        .btn {
            background: #40E0D0;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            background: #f9f9f9;
            border-radius: 5px;
            white-space: pre-wrap;
        }
        .date-picker-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.5);
            display: none;
            justify-content: center;
            align-items: center;
            z-index: 1000;
        }
        .date-picker-modal {
            background: white;
            padding: 20px;
            border-radius: 10px;
            width: 90%;
            max-width: 400px;
        }
        .date-selectors {
            display: flex;
            gap: 10px;
            margin: 20px 0;
        }
        .selector-group {
            flex: 1;
        }
        .date-selector {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .date-picker-actions {
            display: flex;
            gap: 10px;
            justify-content: flex-end;
            margin-top: 20px;
        }
        .btn-cancel, .btn-confirm {
            border: none;
            padding: 8px 16px;
            border-radius: 5px;
            cursor: pointer;
        }
        .btn-cancel {
            background: #666;
            color: white;
        }
        .btn-confirm {
            background: #40E0D0;
            color: white;
        }
    </style>
</head>
<body>
    <div class="container">
        <h2>简单日期选择器测试</h2>
        
        <div class="form-group">
            <label for="birth_date">出生日期</label>
            <div class="date-input-container">
                <input type="text" id="birth_date" name="birth_date" placeholder="请点击日历图标选择出生日期" readonly>
                <button type="button" class="date-select-btn" onclick="openDatePicker()">📅</button>
            </div>
        </div>

        <button class="btn" onclick="testDate()">测试日期值</button>
        <button class="btn" onclick="clearDate()">清除日期</button>
        <button class="btn" onclick="goBack()">返回引导页</button>

        <div id="result" class="result" style="display: none;"></div>
    </div>

    <!-- 日期选择器 -->
    <div class="date-picker-overlay" id="date-picker-overlay">
        <div class="date-picker-modal">
            <h3>选择出生日期</h3>
            <div class="date-selectors">
                <div class="selector-group">
                    <label>年份</label>
                    <select id="year-selector" class="date-selector"></select>
                </div>
                <div class="selector-group">
                    <label>月份</label>
                    <select id="month-selector" class="date-selector">
                        <option value="1">1月</option>
                        <option value="2">2月</option>
                        <option value="3">3月</option>
                        <option value="4">4月</option>
                        <option value="5">5月</option>
                        <option value="6">6月</option>
                        <option value="7">7月</option>
                        <option value="8">8月</option>
                        <option value="9">9月</option>
                        <option value="10">10月</option>
                        <option value="11">11月</option>
                        <option value="12">12月</option>
                    </select>
                </div>
                <div class="selector-group">
                    <label>日期</label>
                    <select id="day-selector" class="date-selector"></select>
                </div>
            </div>
            <div class="date-picker-actions">
                <button class="btn-cancel" onclick="closeDatePicker()">取消</button>
                <button class="btn-confirm" onclick="confirmDateSelection()">确认</button>
            </div>
        </div>
    </div>

    <script>
        let selectedYear = null;
        let selectedMonth = null;
        let selectedDay = null;
        let eventsInitialized = false;

        function openDatePicker() {
            console.log('打开日期选择器');
            
            // 初始化年份选项
            initializeYearOptions();
            
            // 初始化事件监听器（只初始化一次）
            if (!eventsInitialized) {
                initializeDateSelectors();
                eventsInitialized = true;
            }
            
            // 设置默认值
            const defaultDate = new Date();
            defaultDate.setFullYear(defaultDate.getFullYear() - 18);
            
            document.getElementById('year-selector').value = defaultDate.getFullYear();
            document.getElementById('month-selector').value = defaultDate.getMonth() + 1;
            selectedYear = defaultDate.getFullYear();
            selectedMonth = defaultDate.getMonth() + 1;
            
            updateDayOptions();
            document.getElementById('day-selector').value = defaultDate.getDate();
            selectedDay = defaultDate.getDate();
            
            document.getElementById('date-picker-overlay').style.display = 'flex';
        }

        function closeDatePicker() {
            document.getElementById('date-picker-overlay').style.display = 'none';
        }

        function initializeYearOptions() {
            const yearSelector = document.getElementById('year-selector');
            const currentYear = new Date().getFullYear();
            const startYear = currentYear - 80;
            const endYear = currentYear - 18;

            yearSelector.innerHTML = '';
            for (let year = endYear; year >= startYear; year--) {
                const option = document.createElement('option');
                option.value = year;
                option.textContent = year + '年';
                yearSelector.appendChild(option);
            }
        }

        function initializeDateSelectors() {
            console.log('初始化事件监听器');
            
            document.getElementById('year-selector').addEventListener('change', function() {
                selectedYear = parseInt(this.value);
                console.log('年份改变:', selectedYear);
                updateDayOptions();
            });

            document.getElementById('month-selector').addEventListener('change', function() {
                selectedMonth = parseInt(this.value);
                console.log('月份改变:', selectedMonth);
                updateDayOptions();
            });

            document.getElementById('day-selector').addEventListener('change', function() {
                selectedDay = parseInt(this.value);
                console.log('日期改变:', selectedDay);
            });
        }

        function updateDayOptions() {
            const daySelector = document.getElementById('day-selector');
            const year = selectedYear || parseInt(document.getElementById('year-selector').value);
            const month = selectedMonth || parseInt(document.getElementById('month-selector').value);

            const daysInMonth = new Date(year, month, 0).getDate();
            daySelector.innerHTML = '';

            for (let day = 1; day <= daysInMonth; day++) {
                const option = document.createElement('option');
                option.value = day;
                option.textContent = day + '日';
                daySelector.appendChild(option);
            }
        }

        function confirmDateSelection() {
            console.log('确认日期选择');
            
            const year = parseInt(document.getElementById('year-selector').value);
            const month = parseInt(document.getElementById('month-selector').value);
            const day = parseInt(document.getElementById('day-selector').value);

            console.log('选择的日期:', { year, month, day });

            if (!year || !month || !day) {
                alert('请选择完整的日期');
                return;
            }

            // 验证年龄
            const selectedDate = new Date(year, month - 1, day);
            const today = new Date();
            let age = today.getFullYear() - selectedDate.getFullYear();
            const monthDiff = today.getMonth() - selectedDate.getMonth();

            if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < selectedDate.getDate())) {
                age--;
            }

            if (age < 18) {
                alert('趣玩星球仅对年满十八周岁的用户提供服务');
                return;
            }

            // 格式化日期
            const formattedDate = `${year}-${month.toString().padStart(2, '0')}-${day.toString().padStart(2, '0')}`;
            const displayDate = `${year}年${month}月${day}日`;

            // 设置到输入框
            const birthDateInput = document.getElementById('birth_date');
            birthDateInput.value = displayDate;
            birthDateInput.setAttribute('data-value', formattedDate);

            console.log('日期设置完成:', {
                displayDate: displayDate,
                formattedDate: formattedDate,
                inputValue: birthDateInput.value,
                dataValue: birthDateInput.getAttribute('data-value')
            });

            closeDatePicker();
            alert('已选择出生日期：' + displayDate);
        }

        function testDate() {
            const birthDateElement = document.getElementById('birth_date');
            const birthDate = birthDateElement.getAttribute('data-value') || birthDateElement.value;
            
            const result = document.getElementById('result');
            result.style.display = 'block';
            result.textContent = `测试结果：
获取到的值: "${birthDate}"
输入框显示值: "${birthDateElement.value}"
data-value属性: "${birthDateElement.getAttribute('data-value')}"
是否为空: ${!birthDate || birthDate.trim() === '' ? '是' : '否'}
验证结果: ${!birthDate || birthDate.trim() === '' ? '❌ 失败' : '✅ 通过'}`;
        }

        function clearDate() {
            const birthDateInput = document.getElementById('birth_date');
            birthDateInput.value = '';
            birthDateInput.removeAttribute('data-value');
            document.getElementById('result').style.display = 'none';
        }

        function goBack() {
            window.location.href = 'index.php';
        }
    </script>
</body>
</html>
