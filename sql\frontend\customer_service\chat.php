<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>在线客服 - 趣玩星球</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            height: 100vh;
            overflow: hidden;
            overscroll-behavior: none;
            touch-action: pan-y;
            -webkit-overflow-scrolling: touch;
        }

        .chat-container {
            height: 100vh;
            display: flex;
            flex-direction: column;
            background: white;
            max-width: 800px;
            margin: 0 auto;
            box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
        }

        .chat-header {
            background: linear-gradient(135deg, #6F7BF5, #8B93F7);
            color: white;
            padding: 16px 20px;
            display: flex;
            align-items: center;
            gap: 12px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        .back-btn {
            background: none;
            border: none;
            color: white;
            font-size: 20px;
            cursor: pointer;
            padding: 8px;
            border-radius: 50%;
            transition: background 0.3s;
        }

        .back-btn:hover {
            background: rgba(255, 255, 255, 0.2);
        }

        .chat-title {
            flex: 1;
        }

        .chat-title h1 {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 2px;
        }

        .chat-subtitle {
            font-size: 12px;
            opacity: 0.8;
        }

        .chat-messages {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
            background: #f8f9fa;
        }

        .message {
            margin-bottom: 16px;
            display: flex;
            align-items: flex-start;
            gap: 12px;
        }

        .message.user {
            flex-direction: row-reverse;
        }

        .message-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: linear-gradient(135deg, #6F7BF5, #8B93F7);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 16px;
            flex-shrink: 0;
        }

        .message.user .message-avatar {
            background: linear-gradient(135deg, #10B981, #34D399);
        }

        .message-content {
            background: white;
            padding: 12px 16px 8px 16px;
            border-radius: 18px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            position: relative;
            display: inline-block;
            max-width: fit-content;
            min-width: 80px;
            word-wrap: break-word;
            white-space: pre-wrap;
        }

        .message.user .message-content {
            background: linear-gradient(135deg, #6F7BF5, #8B93F7);
            color: white;
            max-width: 80%;
            display: inline-block;
        }

        .message.bot .message-content {
            max-width: 70%;
        }

        .message-text {
            display: block;
            margin-bottom: 4px;
            line-height: 1.4;
        }

        .message-time {
            font-size: 10px;
            opacity: 0.7;
            text-align: right;
            margin-top: 4px;
            line-height: 1;
        }

        .message.user .message-time {
            color: rgba(255, 255, 255, 0.8);
        }

        .message.bot .message-time {
            color: #999;
        }

        .chat-input {
            padding: 16px 20px;
            background: white;
            border-top: 1px solid #e5e7eb;
            display: flex;
            gap: 12px;
            align-items: flex-end;
        }

        .input-wrapper {
            flex: 1;
            position: relative;
        }

        .message-input {
            width: 100%;
            min-height: 40px;
            max-height: 120px;
            padding: 10px 16px;
            border: 2px solid #e5e7eb;
            border-radius: 20px;
            font-size: 14px;
            resize: none;
            outline: none;
            transition: border-color 0.3s;
            font-size: 16px; /* 防止iOS自动缩放 */
            -webkit-appearance: none;
            -webkit-user-select: text;
        }

        .message-input:focus {
            border-color: #6F7BF5;
            transform: none; /* 防止焦点时的变换 */
        }

        .send-btn {
            width: 40px;
            height: 40px;
            background: linear-gradient(135deg, #6F7BF5, #8B93F7);
            border: none;
            border-radius: 50%;
            color: white;
            font-size: 16px;
            cursor: pointer;
            transition: transform 0.2s;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .send-btn:hover {
            transform: scale(1.05);
        }

        .send-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none;
        }

        .typing-indicator {
            display: none;
            padding: 12px 16px;
            color: #666;
            font-style: italic;
            font-size: 14px;
        }

        .welcome-message {
            text-align: center;
            padding: 20px;
            color: #666;
            background: white;
            border-radius: 12px;
            margin-bottom: 20px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
        }

        .welcome-message h3 {
            color: #6F7BF5;
            margin-bottom: 8px;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .chat-container {
                height: 100vh;
                border-radius: 0;
            }

            .message.user .message-content {
                max-width: 85%;
            }

            .message.bot .message-content {
                max-width: 80%;
            }

            /* 防止移动端页面缩放 */
            .message-input {
                font-size: 16px !important;
            }
        }

        /* 加载动画 */
        .loading-dots {
            display: inline-block;
        }

        .loading-dots::after {
            content: '';
            animation: dots 1.5s infinite;
        }

        @keyframes dots {
            0%, 20% { content: ''; }
            40% { content: '.'; }
            60% { content: '..'; }
            80%, 100% { content: '...'; }
        }
    </style>
</head>
<body>
    <div class="chat-container">
        <!-- 聊天头部 -->
        <div class="chat-header">
            <button class="back-btn" onclick="goBack()">
                <i class="fas fa-arrow-left"></i>
            </button>
            <div class="chat-title">
                <h1>在线客服</h1>
                <div class="chat-subtitle">我们将竭诚为您服务</div>
            </div>
        </div>

        <!-- 聊天消息区域 -->
        <div class="chat-messages" id="chatMessages">
            <div class="welcome-message">
                <h3><i class="fas fa-robot"></i> 趣玩小助手</h3>
                <p>您好！我是趣玩星球智能客服，有什么可以帮助您的吗？</p>
            </div>
        </div>

        <!-- 输入提示 -->
        <div class="typing-indicator" id="typingIndicator">
            客服正在输入<span class="loading-dots"></span>
        </div>

        <!-- 聊天输入区域 -->
        <div class="chat-input">
            <div class="input-wrapper">
                <textarea
                    class="message-input"
                    id="messageInput"
                    placeholder="请输入您的问题..."
                    rows="1"
                ></textarea>
            </div>
            <button class="send-btn" id="sendBtn" onclick="sendMessage()">
                <i class="fas fa-paper-plane"></i>
            </button>
        </div>
    </div>

    <script>
        let sessionId = generateSessionId();
        let botConfig = null;

        // 页面加载时初始化
        document.addEventListener('DOMContentLoaded', function() {
            loadBotConfig();
            setupInputEvents();
        });

        // 生成会话ID
        function generateSessionId() {
            return 'session_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
        }

        // 加载机器人配置
        async function loadBotConfig() {
            try {
                const response = await fetch('get_bot_config.php');
                const data = await response.json();

                if (data.success) {
                    botConfig = data.config;
                    // 显示欢迎消息
                    if (botConfig.welcome_message) {
                        addMessage('bot', botConfig.welcome_message);
                    }
                }
            } catch (error) {
                console.error('加载机器人配置失败:', error);
            }
        }

        // 设置输入事件
        function setupInputEvents() {
            const input = document.getElementById('messageInput');
            const sendBtn = document.getElementById('sendBtn');

            // 自动调整输入框高度
            input.addEventListener('input', function() {
                this.style.height = 'auto';
                this.style.height = Math.min(this.scrollHeight, 120) + 'px';

                // 控制发送按钮状态
                sendBtn.disabled = !this.value.trim();
            });

            // 回车发送消息
            input.addEventListener('keydown', function(e) {
                if (e.key === 'Enter' && !e.shiftKey) {
                    e.preventDefault();
                    sendMessage();
                }
            });
        }

        // 发送消息
        async function sendMessage() {
            const input = document.getElementById('messageInput');
            const message = input.value.trim();

            if (!message) return;

            // 添加用户消息
            addMessage('user', message);
            input.value = '';
            input.style.height = 'auto';
            document.getElementById('sendBtn').disabled = true;

            // 显示输入提示
            showTypingIndicator();

            try {
                // 发送到后端处理
                const response = await fetch('chat_handler.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        message: message,
                        session_id: sessionId
                    })
                });

                const data = await response.json();

                // 隐藏输入提示
                hideTypingIndicator();

                if (data.success) {
                    // 添加机器人回复
                    setTimeout(() => {
                        addMessage('bot', data.reply);
                    }, 500);
                } else {
                    addMessage('bot', '抱歉，系统暂时无法处理您的问题，请稍后重试。');
                }
            } catch (error) {
                console.error('发送消息失败:', error);
                hideTypingIndicator();
                addMessage('bot', '网络连接异常，请检查网络后重试。');
            }
        }

        // 添加消息到聊天区域
        function addMessage(type, content) {
            const messagesContainer = document.getElementById('chatMessages');
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${type}`;

            const avatar = document.createElement('div');
            avatar.className = 'message-avatar';
            avatar.innerHTML = type === 'user' ? '<i class="fas fa-user"></i>' : '<i class="fas fa-robot"></i>';

            const messageContent = document.createElement('div');
            messageContent.className = 'message-content';

            // 创建消息文本
            const messageText = document.createElement('div');
            messageText.className = 'message-text';
            messageText.innerHTML = content.replace(/\n/g, '<br>');

            // 创建时间戳
            const messageTime = document.createElement('div');
            messageTime.className = 'message-time';
            messageTime.textContent = new Date().toLocaleTimeString('zh-CN', {
                hour: '2-digit',
                minute: '2-digit'
            });

            // 将文本和时间戳都放在气泡内
            messageContent.appendChild(messageText);
            messageContent.appendChild(messageTime);

            messageDiv.appendChild(avatar);
            messageDiv.appendChild(messageContent);

            messagesContainer.appendChild(messageDiv);

            // 滚动到底部
            messagesContainer.scrollTop = messagesContainer.scrollHeight;
        }

        // 显示输入提示
        function showTypingIndicator() {
            document.getElementById('typingIndicator').style.display = 'block';
        }

        // 隐藏输入提示
        function hideTypingIndicator() {
            document.getElementById('typingIndicator').style.display = 'none';
        }

        // 返回上一页
        function goBack() {
            if (window.history.length > 1) {
                window.history.back();
            } else {
                window.location.href = '../login/index.php';
            }
        }
    </script>
</body>
</html>
