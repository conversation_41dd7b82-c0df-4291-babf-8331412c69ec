<?php
/**
 * 后台发送验证码API
 * 实现前后台联动功能
 */

// 禁用错误输出，避免破坏JSON格式
error_reporting(0);
ini_set('display_errors', 0);

// 清理输出缓冲区
if (ob_get_level()) {
    ob_clean();
}

session_start();

// 设置JSON响应头
header('Content-Type: application/json; charset=utf-8');

// 检查管理员权限
if (!isset($_SESSION['admin_id'])) {
    echo json_encode(['success' => false, 'message' => '未授权访问'], JSON_UNESCAPED_UNICODE);
    exit;
}

$response = ['success' => false, 'message' => ''];

try {
    // 获取数据库连接
    $pdo = getDbConnection();

    // 获取POST数据
    $input = json_decode(file_get_contents('php://input'), true);

    if (!$input) {
        $response['message'] = '无效的请求数据';
        echo json_encode($response, JSON_UNESCAPED_UNICODE);
        exit;
    }

    $user_id = intval($input['user_id'] ?? 0);
    $phone = trim($input['phone'] ?? '');
    $type = trim($input['type'] ?? '');
    $note = trim($input['note'] ?? '');
    $expiry_minutes = intval($input['expiry'] ?? 5);

    // 验证必需参数
    if ($user_id <= 0 || empty($phone) || empty($type) || empty($note)) {
        $response['message'] = '请填写完整的验证码信息';
        echo json_encode($response, JSON_UNESCAPED_UNICODE);
        exit;
    }

    // 验证手机号格式
    if (!preg_match('/^1[3-9]\d{9}$/', $phone)) {
        $response['message'] = '手机号格式不正确';
        echo json_encode($response, JSON_UNESCAPED_UNICODE);
        exit;
    }

    // 检查用户是否存在
    $stmt = $pdo->prepare("SELECT id, username FROM users WHERE id = ?");
    $stmt->execute([$user_id]);
    $user = $stmt->fetch(PDO::FETCH_ASSOC);

    if (!$user) {
        $response['message'] = '用户不存在';
        echo json_encode($response, JSON_UNESCAPED_UNICODE);
        exit;
    }

    // 生成6位数验证码
    $verification_code = sprintf('%06d', mt_rand(100000, 999999));

    // 计算过期时间
    $expires_at = date('Y-m-d H:i:s', time() + ($expiry_minutes * 60));

    // 开始事务
    $pdo->beginTransaction();

    try {
        // 1. 将之前的验证码设为过期
        $stmt = $pdo->prepare("
            UPDATE verification_codes
            SET status = 'expired'
            WHERE user_id = ? AND status = 'pending'
        ");
        $stmt->execute([$user_id]);

        // 2. 插入新的验证码记录
        $stmt = $pdo->prepare("
            INSERT INTO verification_codes
            (user_id, phone, code, type, status, sent_by_admin, admin_note, expires_at)
            VALUES (?, ?, ?, ?, 'pending', ?, ?, ?)
        ");
        $stmt->execute([
            $user_id,
            $phone,
            $verification_code,
            $type,
            $_SESSION['admin_id'],
            $note,
            $expires_at
        ]);

        $verification_id = $pdo->lastInsertId();

        // 3. 创建实时通知记录
        $notification_title = '管理员验证码';
        $notification_content = "您收到一条来自管理员的验证码：{$verification_code}";

        // 根据类型设置不同的通知内容
        switch ($type) {
            case 'security_verify':
                $notification_title = '安全验证码';
                $notification_content = "安全验证码：{$verification_code}，请在{$expiry_minutes}分钟内使用";
                break;
            case 'system_notice':
                $notification_title = '系统通知验证码';
                $notification_content = "系统验证码：{$verification_code}，有效期{$expiry_minutes}分钟";
                break;
            default:
                $notification_title = '管理员验证码';
                $notification_content = "管理员发送的验证码：{$verification_code}，请在{$expiry_minutes}分钟内使用";
        }

        $notification_data = [
            'verification_id' => $verification_id,
            'code' => $verification_code,
            'type' => $type,
            'expires_at' => $expires_at,
            'admin_note' => $note,
            'sent_by' => $_SESSION['admin_username'] ?? '管理员'
        ];

        $stmt = $pdo->prepare("
            INSERT INTO realtime_notifications
            (user_id, type, title, content, data, status, priority, expires_at)
            VALUES (?, 'verification_code', ?, ?, ?, 'pending', 5, ?)
        ");
        $stmt->execute([
            $user_id,
            $notification_title,
            $notification_content,
            json_encode($notification_data),
            $expires_at
        ]);

        // 4. 记录管理员操作日志
        $operation_details = [
            'user_id' => $user_id,
            'username' => $user['username'],
            'phone' => $phone,
            'verification_type' => $type,
            'expiry_minutes' => $expiry_minutes,
            'admin_note' => $note
        ];

        $stmt = $pdo->prepare("
            INSERT INTO admin_operation_logs
            (admin_id, operation_type, target_type, target_id, description, details, ip_address, user_agent)
            VALUES (?, '发送验证码', 'user', ?, ?, ?, ?, ?)
        ");
        $stmt->execute([
            $_SESSION['admin_id'],
            $user_id,
            "向用户 {$user['username']} ({$phone}) 发送{$type}验证码",
            json_encode($operation_details),
            $_SERVER['REMOTE_ADDR'] ?? '',
            $_SERVER['HTTP_USER_AGENT'] ?? ''
        ]);

        // 提交事务
        $pdo->commit();

        $response['success'] = true;
        $response['message'] = '验证码发送成功';
        $response['data'] = [
            'verification_id' => $verification_id,
            'code' => $verification_code, // 仅用于调试，生产环境应移除
            'expires_at' => $expires_at,
            'user' => [
                'id' => $user_id,
                'username' => $user['username'],
                'phone' => $phone
            ]
        ];

    } catch (Exception $e) {
        $pdo->rollBack();
        throw $e;
    }

} catch (PDOException $e) {
    error_log("发送验证码数据库错误: " . $e->getMessage());
    $response['message'] = '数据库操作失败';
} catch (Exception $e) {
    error_log("发送验证码错误: " . $e->getMessage());
    $response['message'] = '发送验证码失败：' . $e->getMessage();
}

echo json_encode($response, JSON_UNESCAPED_UNICODE);

/**
 * 获取数据库连接
 */
function getDbConnection() {
    try {
        // 直接使用数据库配置，避免require错误
        $host = 'localhost';
        $dbname = 'quwanplanet';
        $username = 'quwanplanet';
        $password = 'nJmJm23FB4Xn6Fc3';

        return new PDO(
            "mysql:host={$host};dbname={$dbname};charset=utf8mb4",
            $username,
            $password,
            [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                PDO::ATTR_EMULATE_PREPARES => false
            ]
        );
    } catch (PDOException $e) {
        error_log("数据库连接失败: " . $e->getMessage());
        throw new Exception("数据库连接失败");
    }
}
?>
