<?php
/**
 * 趣玩星球管理后台 - 首页
 * 年轻化、现代化的工作台设计
 */

session_start();

// 登录检查
if (!isset($_SESSION['admin_id'])) {
    header('Location: login.php');
    exit;
}

$admin_name = $_SESSION['admin_name'] ?? '管理员';
$admin_id = $_SESSION['admin_id'] ?? '';
$admin_employee_id = $_SESSION['admin_employee_id'] ?? '000001';
$admin_department = $_SESSION['admin_department'] ?? '总经办';

// 获取实时数据
require_once 'db_config.php';

try {
    $pdo = getDbConnection();

    // 待审核认证数
    $stmt = $pdo->query("SELECT COUNT(*) as pending_verifications FROM real_name_verification WHERE status = 'pending'");
    $pending_verifications = $stmt->fetch()['pending_verifications'] ?? 0;

    // 今日新增用户
    $stmt = $pdo->query("SELECT COUNT(*) as today_users FROM users WHERE DATE(created_at) = CURDATE()");
    $today_users = $stmt->fetch()['today_users'] ?? 0;

    // 我今日处理的认证数
    $stmt = $pdo->prepare("SELECT COUNT(*) as my_today_processed FROM real_name_verification WHERE admin_id = ? AND DATE(updated_at) = CURDATE() AND status != 'pending'");
    $stmt->execute([$admin_id]);
    $my_today_processed = $stmt->fetch()['my_today_processed'] ?? 0;

    // 获取管理员入职时间
    $stmt = $pdo->prepare("SELECT hire_date FROM admin_users WHERE id = ?");
    $stmt->execute([$admin_id]);
    $admin_info = $stmt->fetch();
    $join_date = $admin_info['hire_date'] ?? date('Y-m-d');

} catch (Exception $e) {
    $pending_verifications = 0;
    $today_users = 0;
    $my_today_processed = 0;
    $join_date = date('Y-m-d');
}

// 计算入职天数
$join_timestamp = strtotime($join_date);
$today_timestamp = time();
$days_since_join = floor(($today_timestamp - $join_timestamp) / (24 * 60 * 60)) + 1;

// 获取当前日期信息
$current_date = date('Y年m月d日');
$current_weekday = ['日', '一', '二', '三', '四', '五', '六'][date('w')];

// 激励话术数组
$motivational_quotes = [
    "今天也要元气满满地工作哦！✨",
    "每一次审核都是对用户的关爱 💝",
    "你的专业让平台更安全 🛡️",
    "今天又是充满可能的一天 🌟",
    "用心服务，让每个用户都感受到温暖 🤗",
    "你的努力让趣玩星球更美好 🌈",
    "保持微笑，传递正能量 😊",
    "每个认真的瞬间都闪闪发光 ✨",
    "今天的你依然很棒 👏",
    "用专业和热情点亮每一天 🔥"
];

$daily_quote = $motivational_quotes[date('j') % count($motivational_quotes)];
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>工作台 - 趣玩星球管理后台</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="assets/css/admin-layout.css">
    <style>
        /* 首页专用样式 */
        .home-container {
            padding: 24px;
            max-width: 1400px;
            margin: 0 auto;
        }

        /* 欢迎区域 */
        .welcome-section {
            background: linear-gradient(135deg, #40E0D0 0%, #667eea 100%);
            border-radius: 20px;
            padding: 32px;
            color: white;
            margin-bottom: 32px;
            position: relative;
            overflow: hidden;
        }

        .welcome-section::before {
            content: '';
            position: absolute;
            top: -50%;
            right: -50%;
            width: 200%;
            height: 200%;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="50" cy="50" r="2" fill="rgba(255,255,255,0.1)"/></svg>') repeat;
            animation: float 20s infinite linear;
        }

        @keyframes float {
            0% { transform: translateX(0) translateY(0); }
            100% { transform: translateX(-50px) translateY(-50px); }
        }

        .welcome-content {
            position: relative;
            z-index: 1;
        }

        .welcome-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 24px;
        }

        .welcome-info h1 {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 8px;
            text-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .welcome-info .subtitle {
            font-size: 1.2rem;
            opacity: 0.9;
            margin-bottom: 16px;
        }

        .date-info {
            text-align: right;
            font-size: 1.1rem;
            opacity: 0.9;
        }

        .motivational-quote {
            background: rgba(255, 255, 255, 0.15);
            backdrop-filter: blur(10px);
            border-radius: 16px;
            padding: 20px;
            font-size: 1.1rem;
            font-weight: 500;
            text-align: center;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        /* 个人信息卡片 */
        .personal-info {
            background: white;
            border-radius: 16px;
            padding: 24px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
            margin-bottom: 32px;
        }

        .info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
        }

        .info-item {
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 16px;
            background: #F8FAFC;
            border-radius: 12px;
            border-left: 4px solid #40E0D0;
        }

        .info-icon {
            width: 40px;
            height: 40px;
            background: linear-gradient(135deg, #40E0D0, #AFFBF2);
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 18px;
        }

        .info-content h4 {
            font-size: 0.875rem;
            color: #64748B;
            margin-bottom: 4px;
        }

        .info-content p {
            font-size: 1.1rem;
            font-weight: 600;
            color: #1E293B;
        }

        /* 快捷操作区域 */
        .quick-actions {
            background: white;
            border-radius: 16px;
            padding: 24px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
            margin-bottom: 32px;
        }

        .section-title {
            font-size: 1.5rem;
            font-weight: 700;
            color: #1E293B;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .actions-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 20px;
        }

        .action-card {
            background: linear-gradient(135deg, #F8FAFC 0%, #F1F5F9 100%);
            border-radius: 16px;
            padding: 24px;
            text-decoration: none;
            color: inherit;
            transition: all 0.3s ease;
            border: 2px solid transparent;
            position: relative;
            overflow: hidden;
        }

        .action-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #40E0D0, #667eea);
        }

        .action-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 12px 32px rgba(0, 0, 0, 0.15);
            border-color: #40E0D0;
            text-decoration: none;
            color: inherit;
        }

        .action-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }

        .action-icon {
            width: 56px;
            height: 56px;
            background: linear-gradient(135deg, #40E0D0, #AFFBF2);
            border-radius: 14px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 24px;
        }

        .action-badge {
            background: #EF4444;
            color: white;
            font-size: 12px;
            font-weight: 600;
            padding: 4px 8px;
            border-radius: 12px;
            min-width: 24px;
            text-align: center;
        }

        .action-title {
            font-size: 1.25rem;
            font-weight: 600;
            color: #1E293B;
            margin-bottom: 8px;
        }

        .action-desc {
            color: #64748B;
            font-size: 0.875rem;
            line-height: 1.5;
        }

        .action-card.has-notification {
            border: 2px solid #F59E0B;
            box-shadow: 0 4px 20px rgba(245, 158, 11, 0.2);
        }

        /* 待处理事项样式 */
        .urgent-tasks {
            background: linear-gradient(135deg, #FEF3C7, #FDE68A);
            border-radius: 16px;
            padding: 24px;
            margin-bottom: 32px;
            border: 2px solid #F59E0B;
            position: relative;
            overflow: hidden;
        }

        .urgent-tasks::before {
            content: '';
            position: absolute;
            top: -50%;
            right: -50%;
            width: 200%;
            height: 200%;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="50" cy="50" r="1" fill="rgba(245,158,11,0.1)"/></svg>') repeat;
            animation: float 15s infinite linear;
        }

        .urgent-badge {
            background: #EF4444;
            color: white;
            font-size: 0.75rem;
            font-weight: 700;
            padding: 4px 8px;
            border-radius: 12px;
            margin-left: 8px;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.1); }
        }

        .urgent-card {
            background: white;
            border-radius: 12px;
            padding: 20px;
            display: flex;
            align-items: flex-start;
            gap: 16px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            position: relative;
            z-index: 1;
        }

        .urgent-icon {
            width: 60px;
            height: 60px;
            background: linear-gradient(135deg, #F59E0B, #FBBF24);
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 24px;
            flex-shrink: 0;
        }

        .urgent-content {
            flex: 1;
        }

        .urgent-content h3 {
            font-size: 1.25rem;
            font-weight: 700;
            color: #1F2937;
            margin-bottom: 8px;
        }

        .urgent-content p {
            color: #4B5563;
            margin-bottom: 16px;
            line-height: 1.5;
        }

        .urgent-actions {
            display: flex;
            gap: 12px;
            flex-wrap: wrap;
        }

        .urgent-btn {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            padding: 10px 16px;
            border-radius: 8px;
            text-decoration: none;
            font-weight: 600;
            font-size: 0.875rem;
            transition: all 0.3s ease;
        }

        .urgent-btn.primary {
            background: #F59E0B;
            color: white;
        }

        .urgent-btn.primary:hover {
            background: #D97706;
            transform: translateY(-2px);
            text-decoration: none;
            color: white;
        }

        .urgent-btn.secondary {
            background: #F3F4F6;
            color: #4B5563;
            border: 1px solid #D1D5DB;
        }

        .urgent-btn.secondary:hover {
            background: #E5E7EB;
            transform: translateY(-2px);
            text-decoration: none;
            color: #374151;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .home-container {
                padding: 16px;
            }

            .welcome-header {
                flex-direction: column;
                gap: 16px;
            }

            .welcome-info h1 {
                font-size: 2rem;
            }

            .info-grid,
            .actions-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="admin-layout">
        <!-- 左侧菜单 -->
        <?php include 'includes/sidebar.php'; ?>

        <!-- 主内容区域 -->
        <main class="main-content">
            <!-- 顶部导航栏 -->
            <?php include 'includes/topbar.php'; ?>
            <div class="home-container">
                <!-- 欢迎区域 -->
                <div class="welcome-section">
                    <div class="welcome-content">
                        <div class="welcome-header">
                            <div class="welcome-info">
                                <h1>你好，<?php echo htmlspecialchars($admin_name); ?>！</h1>
                                <p class="subtitle"><?php echo $daily_quote; ?></p>
                            </div>
                            <div class="date-info">
                                <div><?php echo $current_date; ?></div>
                                <div>星期<?php echo $current_weekday; ?></div>
                            </div>
                        </div>
                        <div class="motivational-quote">
                            🎉 今天是你入职的第 <strong><?php echo $days_since_join; ?></strong> 天，感谢你为趣玩星球的付出！
                        </div>
                    </div>
                </div>

                <!-- 个人信息 -->
                <div class="personal-info">
                    <h2 class="section-title">
                        <i class="fas fa-user-circle" style="color: #40E0D0;"></i>
                        个人信息
                    </h2>
                    <div class="info-grid">
                        <div class="info-item">
                            <div class="info-icon">
                                <i class="fas fa-id-badge"></i>
                            </div>
                            <div class="info-content">
                                <h4>姓名</h4>
                                <p><?php echo htmlspecialchars($admin_name); ?></p>
                            </div>
                        </div>
                        <div class="info-item">
                            <div class="info-icon">
                                <i class="fas fa-hashtag"></i>
                            </div>
                            <div class="info-content">
                                <h4>工号</h4>
                                <p><?php echo htmlspecialchars($admin_employee_id); ?></p>
                            </div>
                        </div>
                        <div class="info-item">
                            <div class="info-icon">
                                <i class="fas fa-building"></i>
                            </div>
                            <div class="info-content">
                                <h4>部门</h4>
                                <p><?php echo htmlspecialchars($admin_department); ?></p>
                            </div>
                        </div>
                        <div class="info-item">
                            <div class="info-icon">
                                <i class="fas fa-calendar-check"></i>
                            </div>
                            <div class="info-content">
                                <h4>今日处理</h4>
                                <p><?php echo $my_today_processed; ?> 件</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 待处理事项 -->
                <?php if ($pending_verifications > 0): ?>
                <div class="urgent-tasks">
                    <h2 class="section-title">
                        <i class="fas fa-exclamation-triangle" style="color: #F59E0B;"></i>
                        待处理事项
                        <span class="urgent-badge"><?php echo $pending_verifications; ?></span>
                    </h2>
                    <div class="urgent-card">
                        <div class="urgent-icon">
                            <i class="fas fa-user-check"></i>
                        </div>
                        <div class="urgent-content">
                            <h3>实名认证审核</h3>
                            <p>有 <strong><?php echo $pending_verifications; ?></strong> 个用户提交了实名认证申请，需要您的审核</p>
                            <div class="urgent-actions">
                                <a href="verification/index.php" class="urgent-btn primary">
                                    <i class="fas fa-eye"></i>
                                    立即审核
                                </a>
                                <a href="verification/index.php?status=pending" class="urgent-btn secondary">
                                    <i class="fas fa-list"></i>
                                    查看列表
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
                <?php endif; ?>

                <!-- 快捷操作 -->
                <div class="quick-actions">
                    <h2 class="section-title">
                        <i class="fas fa-bolt" style="color: #40E0D0;"></i>
                        快捷操作
                    </h2>
                    <div class="actions-grid">
                        <a href="verification/index.php" class="action-card <?php echo $pending_verifications > 0 ? 'has-notification' : ''; ?>">
                            <div class="action-header">
                                <div class="action-icon">
                                    <i class="fas fa-user-check"></i>
                                </div>
                                <?php if ($pending_verifications > 0): ?>
                                    <div class="action-badge"><?php echo $pending_verifications; ?></div>
                                <?php endif; ?>
                            </div>
                            <div class="action-title">实名认证审核</div>
                            <div class="action-desc">
                                <?php if ($pending_verifications > 0): ?>
                                    有 <?php echo $pending_verifications; ?> 个待审核的认证申请
                                <?php else: ?>
                                    暂无待审核的认证申请
                                <?php endif; ?>
                            </div>
                        </a>

                        <a href="user_management/index.php" class="action-card">
                            <div class="action-header">
                                <div class="action-icon" style="background: linear-gradient(135deg, #667eea, #764ba2);">
                                    <i class="fas fa-users"></i>
                                </div>
                            </div>
                            <div class="action-title">用户管理</div>
                            <div class="action-desc">查看和管理平台用户信息</div>
                        </a>

                        <a href="user_management/index.php?filter=today" class="action-card">
                            <div class="action-header">
                                <div class="action-icon" style="background: linear-gradient(135deg, #10B981, #34D399);">
                                    <i class="fas fa-user-plus"></i>
                                </div>
                                <?php if ($today_users > 0): ?>
                                    <div class="action-badge" style="background: #10B981;"><?php echo $today_users; ?></div>
                                <?php endif; ?>
                            </div>
                            <div class="action-title">今日新用户</div>
                            <div class="action-desc">
                                今天有 <?php echo $today_users; ?> 个新用户注册
                            </div>
                        </a>

                        <a href="dashboard.php" class="action-card">
                            <div class="action-header">
                                <div class="action-icon" style="background: linear-gradient(135deg, #F59E0B, #FBBF24);">
                                    <i class="fas fa-chart-line"></i>
                                </div>
                            </div>
                            <div class="action-title">数据统计</div>
                            <div class="action-desc">查看详细的平台运营数据</div>
                        </a>

                        <a href="#" onclick="showQuickStats()" class="action-card">
                            <div class="action-header">
                                <div class="action-icon" style="background: linear-gradient(135deg, #8B5CF6, #A78BFA);">
                                    <i class="fas fa-tachometer-alt"></i>
                                </div>
                            </div>
                            <div class="action-title">快速统计</div>
                            <div class="action-desc">查看今日工作概览</div>
                        </a>

                        <a href="#" onclick="exportData()" class="action-card">
                            <div class="action-header">
                                <div class="action-icon" style="background: linear-gradient(135deg, #06B6D4, #67E8F9);">
                                    <i class="fas fa-download"></i>
                                </div>
                            </div>
                            <div class="action-title">数据导出</div>
                            <div class="action-desc">导出用户和认证数据</div>
                        </a>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <script src="assets/js/admin-layout.js"></script>
    <script>
        // 首页特殊交互
        document.addEventListener('DOMContentLoaded', function() {
            // 更新页面标题
            updatePageTitle('首页');

            // 动画效果
            const cards = document.querySelectorAll('.action-card, .info-item, .urgent-card');
            cards.forEach((card, index) => {
                card.style.opacity = '0';
                card.style.transform = 'translateY(20px)';
                setTimeout(() => {
                    card.style.transition = 'all 0.5s ease';
                    card.style.opacity = '1';
                    card.style.transform = 'translateY(0)';
                }, index * 100);
            });
        });

        // 快速统计
        function showQuickStats() {
            const stats = {
                totalUsers: <?php echo $total_users; ?>,
                pendingVerifications: <?php echo $pending_verifications; ?>,
                todayUsers: <?php echo $today_users; ?>,
                myTodayProcessed: <?php echo $my_today_processed; ?>
            };

            alert(`📊 今日工作概览\n\n` +
                  `总用户数：${stats.totalUsers.toLocaleString()}\n` +
                  `待审核认证：${stats.pendingVerifications}\n` +
                  `今日新增用户：${stats.todayUsers}\n` +
                  `我今日已处理：${stats.myTodayProcessed}`);
        }

        // 数据导出
        function exportData() {
            if (confirm('确定要导出数据吗？\n\n将导出用户列表和认证数据')) {
                alert('数据导出功能开发中...\n\n将支持导出：\n- 用户列表\n- 认证记录\n- 操作日志');
            }
        }

        console.log('🏠 趣玩星球管理后台首页已加载');
        console.log('📊 当前数据：', {
            totalUsers: <?php echo $total_users; ?>,
            pendingVerifications: <?php echo $pending_verifications; ?>,
            todayUsers: <?php echo $today_users; ?>,
            myTodayProcessed: <?php echo $my_today_processed; ?>
        });
    </script>
</body>
</html>
