<?php
session_start();
require_once '../db_config.php';

// 检查管理员登录状态
if (!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
    header('Location: ../login.php');
    exit;
}

echo "<h1>用户表字段检查</h1>";
echo "<style>
    table { border-collapse: collapse; margin: 20px 0; }
    th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
    th { background-color: #f2f2f2; }
    .success { color: green; font-weight: bold; }
    .error { color: red; font-weight: bold; }
    .warning { color: orange; font-weight: bold; }
</style>";

try {
    $pdo = getDbConnection();
    echo "<p class='success'>✅ 数据库连接成功！</p>";
    
    // 检查 users 表结构
    echo "<h2>users 表结构</h2>";
    $stmt = $pdo->query("DESCRIBE users");
    $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<table>";
    echo "<tr><th>字段名</th><th>类型</th><th>允许NULL</th><th>键</th><th>默认值</th><th>额外</th></tr>";
    
    $has_quwanplanet_id = false;
    $has_quwan_id = false;
    $user_id_fields = [];
    
    foreach ($columns as $column) {
        echo "<tr>";
        echo "<td>" . htmlspecialchars($column['Field']) . "</td>";
        echo "<td>" . htmlspecialchars($column['Type']) . "</td>";
        echo "<td>" . htmlspecialchars($column['Null']) . "</td>";
        echo "<td>" . htmlspecialchars($column['Key']) . "</td>";
        echo "<td>" . htmlspecialchars($column['Default'] ?? 'NULL') . "</td>";
        echo "<td>" . htmlspecialchars($column['Extra']) . "</td>";
        echo "</tr>";
        
        if ($column['Field'] === 'quwanplanet_id') {
            $has_quwanplanet_id = true;
            $user_id_fields[] = 'quwanplanet_id';
        }
        if ($column['Field'] === 'quwan_id') {
            $has_quwan_id = true;
            $user_id_fields[] = 'quwan_id';
        }
    }
    echo "</table>";
    
    echo "<h2>字段检查结果</h2>";
    echo "<ul>";
    echo "<li>" . ($has_quwanplanet_id ? "<span class='success'>✅ quwanplanet_id 字段存在</span>" : "<span class='error'>❌ quwanplanet_id 字段缺失</span>") . "</li>";
    echo "<li>" . ($has_quwan_id ? "<span class='success'>✅ quwan_id 字段存在</span>" : "<span class='error'>❌ quwan_id 字段缺失</span>") . "</li>";
    echo "</ul>";
    
    if (!empty($user_id_fields)) {
        echo "<p class='success'>可用的用户ID字段：" . implode(', ', $user_id_fields) . "</p>";
    } else {
        echo "<p class='warning'>⚠️ 没有找到专用的用户ID字段，将使用数据库ID</p>";
    }
    
    // 显示用户数据样例
    echo "<h2>用户数据样例</h2>";
    $user_id_field = 'id';
    if ($has_quwanplanet_id) {
        $user_id_field = 'quwanplanet_id';
    } elseif ($has_quwan_id) {
        $user_id_field = 'quwan_id';
    }
    
    $stmt = $pdo->query("
        SELECT 
            id,
            username,
            {$user_id_field} as user_display_id
        FROM users 
        LIMIT 5
    ");
    $users = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (!empty($users)) {
        echo "<table>";
        echo "<tr><th>数据库ID</th><th>用户名</th><th>显示ID</th></tr>";
        foreach ($users as $user) {
            echo "<tr>";
            echo "<td>" . $user['id'] . "</td>";
            echo "<td>" . htmlspecialchars($user['username']) . "</td>";
            echo "<td>" . htmlspecialchars($user['user_display_id'] ?? '无') . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p class='warning'>⚠️ 暂无用户数据</p>";
    }
    
    // 测试用户统计查询
    echo "<h2>测试用户统计查询</h2>";
    try {
        $stmt = $pdo->prepare("
            SELECT
                u.id,
                u.username,
                {$user_id_field} as quwanplanet_id,
                COUNT(ucc.id) as total_claimed,
                COUNT(CASE WHEN ucc.status = 'used' THEN 1 END) as total_used
            FROM users u
            LEFT JOIN user_camping_coupons ucc ON u.id = ucc.user_id
            LEFT JOIN camping_coupons cc ON ucc.coupon_id = cc.id
            WHERE ucc.id IS NOT NULL
            GROUP BY u.id
            ORDER BY total_claimed DESC
            LIMIT 5
        ");
        $stmt->execute();
        $user_stats = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo "<p class='success'>✅ 用户统计查询成功</p>";
        
        if (!empty($user_stats)) {
            echo "<table>";
            echo "<tr><th>用户名</th><th>用户ID</th><th>总领取</th><th>已使用</th></tr>";
            foreach ($user_stats as $stat) {
                echo "<tr>";
                echo "<td>" . htmlspecialchars($stat['username']) . "</td>";
                echo "<td>" . htmlspecialchars($stat['quwanplanet_id'] ?? '无') . "</td>";
                echo "<td>" . number_format($stat['total_claimed']) . "</td>";
                echo "<td>" . number_format($stat['total_used']) . "</td>";
                echo "</tr>";
            }
            echo "</table>";
        } else {
            echo "<p class='warning'>⚠️ 暂无用户统计数据</p>";
        }
        
    } catch (PDOException $e) {
        echo "<p class='error'>❌ 用户统计查询失败：" . htmlspecialchars($e->getMessage()) . "</p>";
    }
    
    // 修复建议
    echo "<h2>修复建议</h2>";
    
    if (!$has_quwanplanet_id && !$has_quwan_id) {
        echo "<div style='background: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 5px;'>";
        echo "<h3>🔧 建议添加用户ID字段</h3>";
        echo "<p>为了更好的用户识别，建议添加专用的用户ID字段：</p>";
        echo "<pre>ALTER TABLE users ADD COLUMN quwanplanet_id VARCHAR(20) UNIQUE;</pre>";
        echo "<p>或者使用现有的字段名：</p>";
        echo "<pre>ALTER TABLE users ADD COLUMN quwan_id VARCHAR(20) UNIQUE;</pre>";
        echo "</div>";
    } else {
        echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; padding: 15px; border-radius: 5px;'>";
        echo "<h3>✅ 用户表结构正常</h3>";
        echo "<p>用户表包含必要的字段，统计功能应该可以正常使用。</p>";
        echo "</div>";
    }
    
} catch (PDOException $e) {
    echo "<p class='error'>❌ 数据库错误：" . htmlspecialchars($e->getMessage()) . "</p>";
}

echo "<br><a href='statistics.php'>访问统计分析页面</a>";
echo " | <a href='index.php'>返回优惠券管理</a>";
?>
