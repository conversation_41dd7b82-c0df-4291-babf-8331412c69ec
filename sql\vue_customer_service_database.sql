-- Vue客服系统数据库补充脚本
-- 执行时间：2025年
-- 说明：为Vue客服系统添加必要的数据库字段和索引优化

-- =====================================================
-- 1. 检查现有表结构
-- =====================================================

-- 检查用户表是否有必要字段
SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE, COLUMN_DEFAULT
FROM INFORMATION_SCHEMA.COLUMNS
WHERE TABLE_SCHEMA = 'quwanplanet'
AND TABLE_NAME = 'users'
AND COLUMN_NAME IN ('id', 'phone', 'nickname', 'avatar', 'status');

-- 检查客服系统相关表是否存在
SHOW TABLES LIKE 'customer_service_%';

-- =====================================================
-- 2. 为Vue客服系统优化索引
-- =====================================================

-- 优化客服会话表索引
ALTER TABLE customer_service_sessions 
ADD INDEX IF NOT EXISTS idx_status_priority (status, priority);

ALTER TABLE customer_service_sessions 
ADD INDEX IF NOT EXISTS idx_user_status (user_id, status);

-- 优化客服消息表索引
ALTER TABLE customer_service_messages 
ADD INDEX IF NOT EXISTS idx_session_sender (session_id, sender_type);

ALTER TABLE customer_service_messages 
ADD INDEX IF NOT EXISTS idx_created_read (created_at, is_read);

-- =====================================================
-- 3. 创建Vue客服系统专用表（如果需要）
-- =====================================================

-- 创建实时通知表（用于SSE推送）
CREATE TABLE IF NOT EXISTS vue_customer_service_notifications (
    id INT AUTO_INCREMENT PRIMARY KEY,
    session_id VARCHAR(50) NOT NULL COMMENT '会话ID',
    user_id INT DEFAULT NULL COMMENT '用户ID',
    customer_service_id INT DEFAULT NULL COMMENT '客服ID',
    notification_type ENUM('new_message', 'session_start', 'session_end', 'transfer', 'system') NOT NULL COMMENT '通知类型',
    message TEXT DEFAULT NULL COMMENT '通知内容',
    is_read BOOLEAN DEFAULT FALSE COMMENT '是否已读',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_session_id (session_id),
    INDEX idx_user_id (user_id),
    INDEX idx_customer_service_id (customer_service_id),
    INDEX idx_type_read (notification_type, is_read),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Vue客服系统实时通知表';

-- 创建在线状态表
CREATE TABLE IF NOT EXISTS vue_customer_service_online_status (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL COMMENT '用户ID',
    user_type ENUM('user', 'customer_service') NOT NULL COMMENT '用户类型',
    session_id VARCHAR(50) DEFAULT NULL COMMENT '当前会话ID',
    is_online BOOLEAN DEFAULT TRUE COMMENT '是否在线',
    last_activity TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后活动时间',
    browser_info TEXT DEFAULT NULL COMMENT '浏览器信息',
    ip_address VARCHAR(45) DEFAULT NULL COMMENT 'IP地址',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE KEY unique_user_type (user_id, user_type),
    INDEX idx_user_type (user_type),
    INDEX idx_online_status (is_online),
    INDEX idx_last_activity (last_activity)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Vue客服系统在线状态表';

-- =====================================================
-- 4. 插入初始配置数据
-- =====================================================

-- 插入默认机器人配置（如果不存在）
INSERT IGNORE INTO customer_service_bot_config (
    name, 
    avatar, 
    welcome_message, 
    default_reply, 
    status, 
    auto_reply_enabled,
    transfer_threshold
) VALUES (
    '趣玩小助手',
    'https://s1.imagehub.cc/images/2025/05/26/2cdd63b39f96072fc60cd733027d2a30.png',
    '您好！欢迎来到趣玩星球客服中心！🌟\n\n我是您的专属客服助手，很高兴为您服务！\n\n请问有什么可以帮助您的吗？',
    '抱歉，我暂时无法理解您的问题。正在为您转接人工客服，请稍候...',
    'active',
    TRUE,
    3
);

-- 插入默认客服账号（如果不存在）
INSERT IGNORE INTO customer_service_users (
    employee_id,
    name,
    password,
    department,
    role,
    status
) VALUES 
('12001', '姚家荣', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '客服部', 'customer_service', 'active'),
('1207001', '赖武浩', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '客服部', 'customer_service', 'active');

-- =====================================================
-- 5. 验证安装结果
-- =====================================================

-- 检查表是否创建成功
SELECT 
    TABLE_NAME,
    TABLE_COMMENT,
    TABLE_ROWS
FROM INFORMATION_SCHEMA.TABLES 
WHERE TABLE_SCHEMA = 'quwanplanet' 
AND TABLE_NAME LIKE '%customer_service%'
ORDER BY TABLE_NAME;

-- 检查索引是否创建成功
SELECT 
    TABLE_NAME,
    INDEX_NAME,
    COLUMN_NAME,
    NON_UNIQUE
FROM INFORMATION_SCHEMA.STATISTICS 
WHERE TABLE_SCHEMA = 'quwanplanet' 
AND TABLE_NAME IN ('customer_service_sessions', 'customer_service_messages')
ORDER BY TABLE_NAME, INDEX_NAME;

-- 显示配置信息
SELECT 'Vue客服系统数据库配置完成' as status;
SELECT '请将此SQL脚本在宝塔phpMyAdmin中执行' as instruction;
