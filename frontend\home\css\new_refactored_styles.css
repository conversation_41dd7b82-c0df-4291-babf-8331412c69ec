/**
 * 全新现代化重构样式文件
 * 年轻化、交互性强的设计风格
 */

/* 主题色变量 - 年轻化配色 - 统一黄色主题 */
:root {
    --primary-color: #FFC300; /* Bright Yellow (Meituan-like) */
    --primary-light: #FFD700; /* Richer Light Yellow */
    --primary-dark: #E6B000;  /* Darker Bright Yellow */
    --secondary-color: #FFDE59; /* Vibrant Lighter Yellow */
    --accent-color: #FF6B9D; /* Consistent accent */
    --warning-color: #FF6B6B;
    --success-color: #4ECDC4;
    --bright-yellow: #FFD166;
    --bright-orange: #FF8A65;
    --watermelon-red: #FF5722;
    --background-color: #F8F9FA;
    --card-background: #FFFFFF;
    --text-primary: #2C3E50;
    --text-secondary: #7F8C8D;
    --text-light: #BDC3C7;
    --border-color: #E9ECEF;
    --shadow-light: 0 2px 8px rgba(0, 0, 0, 0.08);
    --shadow-medium: 0 4px 16px rgba(0, 0, 0, 0.12);
    --shadow-strong: 0 8px 32px rgba(0, 0, 0, 0.16);
    --border-radius: 16px;
    --border-radius-small: 12px;
    --border-radius-large: 24px;
    --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    --transition-fast: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
    --gradient-primary: linear-gradient(135deg, var(--primary-color) 0%, var(--accent-color) 100%);
    --gradient-secondary: linear-gradient(135deg, var(--secondary-color) 0%, var(--accent-color) 100%);
    --gradient-warm: linear-gradient(135deg, var(--bright-orange) 0%, var(--watermelon-red) 100%);
}

/* ===== 隐藏旧的子菜单 ===== */
.sub-options-wrapper {
    display: none !important;
}

.sub-options-list {
    display: none !important;
}

.selected-info-item {
    display: none !important;
}

#game-companion-content-area {
    display: none !important;
}

/* 确保新的胶囊式菜单显示 */
.modern-content-wrapper {
    display: block !important;
}

.smart-filter-bar {
    display: block !important;
}

.sub-options-pills {
    display: block !important;
}

.pills-group {
    display: none !important;
}

.pills-group.active {
    display: flex !important;
}

/* ===== 现代化内容包装器 ===== */
.modern-content-wrapper {
    background: var(--background-color);
    min-height: 100vh;
    padding-bottom: 100px; /* 为底部导航留空间 */
}

/* ===== 智能筛选栏 ===== */
.smart-filter-bar {
    background: var(--card-background);
    padding: 16px 0;
    box-shadow: var(--shadow-light);
    position: sticky;
    top: 0;
    z-index: 100;
    border-bottom: 1px solid var(--border-color);
}

.filter-container {
    padding: 0 16px;
}

/* 子选项胶囊容器 */
.sub-options-pills {
    position: relative;
}

.pills-group {
    display: none;
    gap: 8px;
    flex-wrap: nowrap;
    overflow-x: auto;
    padding: 8px 0;
    scrollbar-width: none;
    -ms-overflow-style: none;
}

.pills-group::-webkit-scrollbar {
    display: none;
}

.pills-group.active {
    display: flex;
}

/* 胶囊样式 */
.pill-item {
    display: flex;
    align-items: center;
    gap: 6px;
    padding: 10px 16px;
    background: var(--background-color);
    color: var(--text-secondary);
    border-radius: var(--border-radius-large);
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition);
    border: 2px solid transparent;
    white-space: nowrap;
    min-width: fit-content;
    position: relative;
    overflow: hidden;
}

.pill-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: var(--gradient-primary);
    transition: left 0.3s ease;
    z-index: -1;
}

.pill-item:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-medium);
    border-color: var(--primary-color);
}

.pill-item:hover::before {
    left: 0;
}

.pill-item:hover {
    color: white;
}

.pill-item.active {
    background: var(--gradient-primary);
    color: white;
    border-color: var(--primary-color);
    box-shadow: var(--shadow-medium);
    transform: translateY(-1px);
}

.pill-item.active::before {
    left: 0;
}

.pill-item i {
    font-size: 16px;
    transition: var(--transition-fast);
}

.pill-item:hover i,
.pill-item.active i {
    transform: scale(1.1);
}

/* ===== 快速工具栏 ===== */
.quick-toolbar {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 12px 16px;
    background: var(--card-background);
    border-bottom: 1px solid var(--border-color);
    position: sticky;
    top: 73px; /* 筛选栏高度 */
    z-index: 99;
}

.toolbar-left {
    display: flex;
    gap: 12px;
    align-items: center;
}

.toolbar-right {
    display: flex;
    gap: 8px;
    align-items: center;
}

/* 位置和日期选择器 */
.location-selector,
.date-selector {
    display: flex;
    align-items: center;
    gap: 6px;
    padding: 8px 12px;
    background: var(--background-color);
    border-radius: var(--border-radius-small);
    font-size: 13px;
    color: var(--text-primary);
    font-weight: 500;
    cursor: pointer;
    transition: var(--transition);
    border: 1px solid var(--border-color);
    min-width: fit-content;
}

.location-selector:hover,
.date-selector:hover {
    background: var(--gradient-secondary);
    transform: translateY(-1px);
    box-shadow: var(--shadow-light);
    border-color: var(--accent-color);
}

.location-selector i:first-child,
.date-selector i:first-child {
    color: var(--primary-color);
    font-size: 12px;
}

.location-selector i:last-child,
.date-selector i:last-child {
    color: var(--text-light);
    font-size: 10px;
    transition: var(--transition-fast);
}

.location-selector:hover i:last-child,
.date-selector:hover i:last-child {
    transform: rotate(180deg);
}

/* 工具栏按钮 */
.filter-toggle-btn,
.sort-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 36px;
    height: 36px;
    background: var(--background-color);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-small);
    color: var(--text-secondary);
    cursor: pointer;
    transition: var(--transition);
}

.filter-toggle-btn:hover,
.sort-btn:hover {
    background: var(--primary-color);
    color: white;
    transform: translateY(-1px);
    box-shadow: var(--shadow-light);
}

.filter-toggle-btn.active {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

/* ===== 动态内容展示区域 ===== */
.dynamic-content-display {
    padding: 16px;
    background: var(--background-color);
    min-height: 60vh;
}

/* 内容板块样式 */
.content-section {
    display: none;
    animation: fadeInUp 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.content-section.active {
    display: block;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 板块标题 */
.section-title {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 24px;
    padding: 20px;
    background: var(--card-background);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-light);
}

.section-title h2 {
    display: flex;
    align-items: center;
    gap: 12px;
    font-size: 22px;
    font-weight: 700;
    color: var(--text-primary);
    margin: 0;
}

.section-title i {
    color: var(--primary-color);
    font-size: 20px;
    padding: 8px;
    background: var(--gradient-secondary);
    border-radius: var(--border-radius-small);
}

/* 筛选按钮组 */
.filter-buttons {
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
}

.filter-btn {
    padding: 8px 16px;
    background: var(--background-color);
    color: var(--text-secondary);
    border: 2px solid transparent;
    border-radius: var(--border-radius-large);
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition);
    position: relative;
    overflow: hidden;
}

.filter-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: var(--gradient-primary);
    transition: left 0.3s ease;
    z-index: -1;
}

.filter-btn:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-light);
    border-color: var(--primary-color);
}

.filter-btn:hover::before {
    left: 0;
}

.filter-btn:hover {
    color: white;
}

.filter-btn.active {
    background: var(--gradient-primary);
    color: white;
    border-color: var(--primary-color);
    box-shadow: var(--shadow-medium);
    transform: translateY(-1px);
}

.filter-btn.active::before {
    left: 0;
}

/* ===== 内容网格 ===== */
.content-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
    gap: 20px;
    margin-top: 24px;
}

/* ===== 现代化内容卡片 ===== */
.content-card {
    background: var(--card-background);
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: var(--shadow-light);
    transition: var(--transition);
    cursor: pointer;
    position: relative;
    border: 1px solid var(--border-color);
}

.content-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--gradient-primary);
    transform: scaleX(0);
    transition: transform 0.3s ease;
}

.content-card:hover {
    transform: translateY(-8px);
    box-shadow: var(--shadow-strong);
    border-color: var(--primary-color);
}

.content-card:hover::before {
    transform: scaleX(1);
}

/* 卡片图片容器 */
.card-image-container {
    position: relative;
    overflow: hidden;
}

.card-image {
    width: 100%;
    height: 200px;
    object-fit: cover;
    background: var(--background-color);
    transition: var(--transition);
}

.content-card:hover .card-image {
    transform: scale(1.05);
}

/* 卡片内容 */
.card-content {
    padding: 20px;
}

.card-title {
    font-size: 18px;
    font-weight: 700;
    color: var(--text-primary);
    margin: 0 0 12px 0;
    line-height: 1.4;
}

.card-description {
    font-size: 14px;
    color: var(--text-secondary);
    line-height: 1.6;
    margin: 0 0 16px 0;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

/* 卡片标签 */
.card-tags {
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
    margin-bottom: 16px;
}

.card-tag {
    padding: 6px 12px;
    background: var(--gradient-secondary);
    color: var(--primary-color);
    border-radius: var(--border-radius-small);
    font-size: 12px;
    font-weight: 600;
    border: 1px solid var(--accent-color);
}

/* 卡片操作按钮 */
.card-action {
    width: 100%;
    padding: 12px;
    background: var(--gradient-primary);
    color: white;
    border: none;
    border-radius: var(--border-radius-small);
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition);
    position: relative;
    overflow: hidden;
}

.card-action::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: var(--gradient-warm);
    transition: left 0.3s ease;
    z-index: -1;
}

.card-action:hover::before {
    left: 0;
}

.card-action:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-medium);
}

/* ===== 加载更多按钮 ===== */
.load-more-btn {
    display: block;
    margin: 40px auto 0;
    padding: 14px 32px;
    background: transparent;
    color: var(--primary-color);
    border: 2px solid var(--primary-color);
    border-radius: var(--border-radius-large);
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition);
    position: relative;
    overflow: hidden;
}

.load-more-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: var(--gradient-primary);
    transition: left 0.3s ease;
    z-index: -1;
}

.load-more-btn:hover::before {
    left: 0;
}

.load-more-btn:hover {
    color: white;
    transform: translateY(-2px);
    box-shadow: var(--shadow-medium);
}

/* ===== 空状态 ===== */
.empty-state {
    text-align: center;
    padding: 80px 20px;
    color: var(--text-secondary);
    background: var(--card-background);
    border-radius: var(--border-radius);
    margin: 20px 0;
}

.empty-state i {
    font-size: 64px;
    color: var(--text-light);
    margin-bottom: 20px;
    opacity: 0.6;
}

.empty-state p {
    font-size: 18px;
    margin: 0;
    font-weight: 500;
}

/* ===== 响应式设计 ===== */
@media (max-width: 768px) {
    .modern-content-wrapper {
        padding-bottom: 80px;
    }

    .smart-filter-bar {
        position: relative;
        top: auto;
    }

    .quick-toolbar {
        position: relative;
        top: auto;
        flex-direction: column;
        gap: 12px;
        align-items: stretch;
    }

    .toolbar-left {
        justify-content: center;
    }

    .toolbar-right {
        justify-content: center;
    }

    .pills-group {
        justify-content: flex-start;
        padding: 12px 0;
    }

    .pill-item {
        font-size: 13px;
        padding: 8px 14px;
    }

    .content-grid {
        grid-template-columns: 1fr;
        gap: 16px;
    }

    .filter-buttons {
        justify-content: center;
    }

    .section-title {
        flex-direction: column;
        gap: 16px;
        align-items: flex-start;
        padding: 16px;
    }

    .section-title h2 {
        font-size: 20px;
    }

    .card-content {
        padding: 16px;
    }

    .card-title {
        font-size: 16px;
    }
}

@media (max-width: 480px) {
    .dynamic-content-display {
        padding: 12px;
    }

    .filter-container {
        padding: 0 12px;
    }

    .quick-toolbar {
        padding: 12px;
    }

    .pill-item {
        font-size: 12px;
        padding: 6px 12px;
    }

    .content-grid {
        gap: 12px;
    }

    .section-title {
        padding: 12px;
    }
}
