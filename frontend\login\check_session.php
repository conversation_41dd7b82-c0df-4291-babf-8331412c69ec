<?php
/**
 * 检查会话状态，用于在登录/注册成功后设置会话存储
 */
session_start();

// 设置响应类型为JSON
header('Content-Type: application/json');

// 检查登录成功标志
if (isset($_SESSION['login_success'])) {
    // 清除会话中的标志
    unset($_SESSION['login_success']);

    echo json_encode([
        'success' => true,
        'type' => 'login',
        'message' => '登录成功！欢迎回来'
    ]);
    exit;
}

// 检查注册成功标志
if (isset($_SESSION['register_success'])) {
    // 清除会话中的标志
    unset($_SESSION['register_success']);

    echo json_encode([
        'success' => true,
        'type' => 'register',
        'message' => '注册成功！欢迎加入趣玩星球'
    ]);
    exit;
}

// 检查登录错误标志
if (isset($_SESSION['login_error'])) {
    $error_message = $_SESSION['login_error'];
    // 清除会话中的标志
    unset($_SESSION['login_error']);

    echo json_encode([
        'success' => false,
        'error' => true,
        'type' => 'login',
        'message' => $error_message
    ]);
    exit;
}

// 检查注册错误标志
if (isset($_SESSION['register_error'])) {
    $error_message = $_SESSION['register_error'];
    // 清除会话中的标志
    unset($_SESSION['register_error']);

    echo json_encode([
        'success' => false,
        'error' => true,
        'type' => 'register',
        'message' => $error_message
    ]);
    exit;
}

// 没有成功或错误标志
echo json_encode([
    'success' => false,
    'error' => false
]);
exit;
