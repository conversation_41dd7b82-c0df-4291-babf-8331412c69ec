<?php
session_start();
header('Content-Type: application/json');

// 检查用户是否登录
if (!isset($_SESSION['user_id'])) {
    echo json_encode([
        'success' => false,
        'message' => '用户未登录'
    ]);
    exit;
}

// 检查请求方法
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode([
        'success' => false,
        'message' => '请求方法错误'
    ]);
    exit;
}

// 数据库连接
require_once '../../sql/db_config.php';

// 获取数据库连接
$pdo = getDbConnection();
if (!$pdo) {
    echo json_encode([
        'success' => false,
        'message' => '数据库连接失败'
    ]);
    exit;
}

try {
    $user_id = $_SESSION['user_id'];
    
    // 验证必填字段
    $title = trim($_POST['title'] ?? '');
    $category = trim($_POST['category'] ?? '');
    
    if (empty($title)) {
        echo json_encode([
            'success' => false,
            'message' => '请输入作品标题'
        ]);
        exit;
    }
    
    if (empty($category)) {
        echo json_encode([
            'success' => false,
            'message' => '请选择作品分类'
        ]);
        exit;
    }
    
    // 检查是否有上传的图片
    if (!isset($_FILES['images']) || empty($_FILES['images']['name'][0])) {
        echo json_encode([
            'success' => false,
            'message' => '请至少上传一张图片'
        ]);
        exit;
    }
    
    // 处理图片上传
    $uploadDir = '../../uploads/works/';
    if (!is_dir($uploadDir)) {
        mkdir($uploadDir, 0755, true);
    }
    
    $imageUrls = [];
    $allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
    $maxFileSize = 10 * 1024 * 1024; // 10MB
    
    foreach ($_FILES['images']['tmp_name'] as $index => $tmpName) {
        if (empty($tmpName)) continue;
        
        $fileName = $_FILES['images']['name'][$index];
        $fileSize = $_FILES['images']['size'][$index];
        $fileType = $_FILES['images']['type'][$index];
        
        // 验证文件类型
        if (!in_array($fileType, $allowedTypes)) {
            echo json_encode([
                'success' => false,
                'message' => '不支持的图片格式'
            ]);
            exit;
        }
        
        // 验证文件大小
        if ($fileSize > $maxFileSize) {
            echo json_encode([
                'success' => false,
                'message' => '图片大小不能超过10MB'
            ]);
            exit;
        }
        
        // 生成唯一文件名
        $extension = pathinfo($fileName, PATHINFO_EXTENSION);
        $newFileName = uniqid() . '_' . time() . '.' . $extension;
        $filePath = $uploadDir . $newFileName;
        
        // 移动文件
        if (move_uploaded_file($tmpName, $filePath)) {
            $imageUrls[] = 'uploads/works/' . $newFileName;
        } else {
            echo json_encode([
                'success' => false,
                'message' => '图片上传失败'
            ]);
            exit;
        }
    }
    
    // 准备数据
    $description = trim($_POST['description'] ?? '');
    $location = trim($_POST['location'] ?? '');
    $latitude = !empty($_POST['latitude']) ? floatval($_POST['latitude']) : null;
    $longitude = !empty($_POST['longitude']) ? floatval($_POST['longitude']) : null;
    $tags = !empty($_POST['tags']) ? $_POST['tags'] : '[]';
    $privacy = $_POST['privacy'] ?? 'public';
    $status = ($privacy === 'public') ? 'published' : 'draft';
    
    // 检查是否存在user_works表，如果不存在则创建简化版本
    try {
        $stmt = $pdo->prepare("SELECT 1 FROM user_works LIMIT 1");
        $stmt->execute();
    } catch (PDOException $e) {
        // 表不存在，创建简化版本
        $createTableSQL = "
            CREATE TABLE IF NOT EXISTS user_works (
                id int(11) NOT NULL AUTO_INCREMENT,
                user_id int(11) NOT NULL,
                title varchar(200) NOT NULL,
                description text DEFAULT NULL,
                images json DEFAULT NULL,
                location varchar(100) DEFAULT NULL,
                latitude decimal(10,8) DEFAULT NULL,
                longitude decimal(11,8) DEFAULT NULL,
                tags json DEFAULT NULL,
                category varchar(50) DEFAULT NULL,
                status enum('draft','published','hidden','deleted') DEFAULT 'published',
                view_count int(11) DEFAULT 0,
                like_count int(11) DEFAULT 0,
                comment_count int(11) DEFAULT 0,
                share_count int(11) DEFAULT 0,
                is_featured tinyint(1) DEFAULT 0,
                created_at timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
                updated_at timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                PRIMARY KEY (id),
                KEY user_id (user_id),
                KEY status (status),
                KEY category (category),
                KEY created_at (created_at)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci
        ";
        $pdo->exec($createTableSQL);
    }
    
    // 插入作品数据
    $stmt = $pdo->prepare("
        INSERT INTO user_works (
            user_id, title, description, images, location, latitude, longitude, 
            tags, category, status, created_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW())
    ");
    
    $result = $stmt->execute([
        $user_id,
        $title,
        $description,
        json_encode($imageUrls),
        $location,
        $latitude,
        $longitude,
        $tags,
        $category,
        $status
    ]);
    
    if ($result) {
        echo json_encode([
            'success' => true,
            'message' => '作品发布成功',
            'work_id' => $pdo->lastInsertId()
        ]);
    } else {
        echo json_encode([
            'success' => false,
            'message' => '作品发布失败'
        ]);
    }
    
} catch (PDOException $e) {
    error_log("Database error in publish_work.php: " . $e->getMessage());
    echo json_encode([
        'success' => false,
        'message' => '数据库错误'
    ]);
} catch (Exception $e) {
    error_log("Error in publish_work.php: " . $e->getMessage());
    echo json_encode([
        'success' => false,
        'message' => '系统错误'
    ]);
}
?>
