<?php
session_start();

// 生成6位随机验证码
$verification_code = sprintf('%06d', mt_rand(100000, 999999));
$_SESSION['verification_code'] = $verification_code;
$_SESSION['code_generated_time'] = time();
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0, user-scalable=no, shrink-to-fit=no">
    <meta name="format-detection" content="telephone=no">
    <meta name="theme-color" content="#FFFFFF">
    <meta name="apple-mobile-web-app-status-bar-style" content="light-content">
    <meta name="msapplication-navbutton-color" content="#FFFFFF">
    <title>验证码验证 - 趣玩星球</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        /* CSS变量定义 - 主题色系统 */
        :root {
            --primary-color: #6F7BF5;
            --primary-light: #8A94F7;
            --primary-dark: #5A67E8;
            --secondary-color: #7C3AED;
            --accent-color: #FF6B9D;
            --gradient-primary: linear-gradient(135deg, #6F7BF5, #7C3AED);
            --gradient-secondary: linear-gradient(135deg, #8A94F7, #6F7BF5);
            --gradient-accent: linear-gradient(135deg, #FF6B9D, #FFB6C1);
            --text-primary: #333333;
            --text-secondary: #666666;
            --text-light: #999999;
            --bg-white: #FFFFFF;
            --bg-light: #F8F9FA;
            --shadow-sm: 0 2px 8px rgba(111, 123, 245, 0.08);
            --shadow-md: 0 4px 16px rgba(111, 123, 245, 0.12);
            --shadow-lg: 0 8px 32px rgba(111, 123, 245, 0.16);
            --radius-sm: 8px;
            --radius-md: 12px;
            --radius-lg: 16px;
            --radius-xl: 24px;
            --transition-fast: 0.2s ease;
            --transition-normal: 0.3s ease;
            --transition-slow: 0.4s ease;
        }

        /* 防止内容被选择和双击放大 */
        * {
            -webkit-touch-callout: none;
            -webkit-user-select: none;
            -khtml-user-select: none;
            -moz-user-select: none;
            -ms-user-select: none;
            user-select: none;
            -webkit-tap-highlight-color: transparent;
        }

        /* 允许输入框选择文本 */
        input, textarea {
            -webkit-user-select: text;
            -khtml-user-select: text;
            -moz-user-select: text;
            -ms-user-select: text;
            user-select: text;
        }

        html {
            height: 100%;
            -ms-touch-action: manipulation;
            touch-action: manipulation;
        }

        body {
            background: var(--bg-white);
            color: var(--text-primary);
            font-family: var(--font-family);
            margin: 0;
            padding: 0;
            min-height: 100vh;
            width: 100vw;
            max-width: 100vw;
            overflow-x: hidden; /* 禁止水平滚动 */
            touch-action: pan-y; /* 只允许垂直滑动 */
            -webkit-user-select: none; /* 禁止选择文本 */
            -moz-user-select: none;
            -ms-user-select: none;
            user-select: none;
            box-sizing: border-box;
        }

        * {
            box-sizing: border-box;
        }

        /* 登录容器样式 - 与登录页面完全一致 */
        .login-container {
            min-height: 100vh;
            width: 100vw;
            max-width: 100vw;
            background: var(--bg-white);
            position: relative;
            display: flex;
            flex-direction: column;
            overflow-x: hidden;
            box-sizing: border-box;
        }

        /* 顶部安全区域 */
        .safe-area-top {
            height: env(safe-area-inset-top);
            background: var(--bg-white);
        }

        /* 底部安全区域 */
        .safe-area-bottom {
            height: env(safe-area-inset-bottom);
            background: var(--bg-white);
        }

        /* 返回按钮 */
        .back-button {
            position: absolute;
            top: calc(env(safe-area-inset-top) + 16px);
            left: 20px;
            width: 40px;
            height: 40px;
            border: none;
            background: rgba(255, 255, 255, 0.9);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            z-index: 100;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            transition: var(--transition-normal);
        }

        .back-button:hover {
            background: var(--bg-white);
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }

        .back-button i {
            color: var(--text-primary);
            font-size: 16px;
        }

        /* 主要内容区域 */
        .main-content {
            flex: 1;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            padding: 20px;
            max-width: 400px;
            margin: 0 auto;
            width: 100%;
            box-sizing: border-box;
            overflow-x: hidden;
        }

        /* 品牌区域样式 */
        .brand-section {
            text-align: center;
            margin-bottom: 40px;
        }

        .brand-logo {
            width: 80px;
            height: 80px;
            margin: 0 auto 16px;
            border-radius: 20px;
            overflow: hidden;
            box-shadow: 0 8px 24px rgba(111, 123, 245, 0.15);
        }

        .brand-text img {
            max-width: 200px;
            height: auto;
        }

        /* 移动端适配 */
        @media (max-width: 480px) {
            .verification-inputs {
                gap: 6px;
                max-width: 280px;
            }

            .verification-input {
                width: 38px;
                height: 48px;
                font-size: 18px;
            }

            .form-section {
                padding: 0 16px;
                max-width: 340px;
            }

            .main-content {
                padding: 16px;
            }
        }

        @media (max-width: 360px) {
            .verification-inputs {
                gap: 4px;
                max-width: 240px;
            }

            .verification-input {
                width: 32px;
                height: 44px;
                font-size: 16px;
            }

            .form-section {
                padding: 0 12px;
                max-width: 300px;
            }

            .main-content {
                padding: 12px;
            }
        }

        @media (max-width: 320px) {
            .verification-inputs {
                gap: 3px;
                max-width: 220px;
            }

            .verification-input {
                width: 30px;
                height: 40px;
                font-size: 14px;
            }

            .form-section {
                padding: 0 10px;
                max-width: 280px;
            }
        }

        /* 重新发送按钮激活状态 */
        .btn-resend-active {
            background: var(--primary-color) !important;
            color: white !important;
            border: 1px solid var(--primary-color) !important;
        }

        .btn-resend-active:hover {
            background: var(--primary-dark) !important;
            color: white !important;
        }

        /* 返回链接 */
        .back-link {
            text-align: center;
            margin-top: 24px;
        }

        .back-link a {
            color: var(--primary-color);
            text-decoration: none;
            font-size: 0.9rem;
            transition: var(--transition-normal);
        }

        .back-link a:hover {
            color: var(--primary-dark);
            text-decoration: underline;
        }

        /* 短信弹窗样式 - 与登录页面完全一致 */
        .sms-modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100vw;
            height: 100vh;
            background: rgba(0, 0, 0, 0.6);
            z-index: 10000;
            display: none;
            align-items: center;
            justify-content: center;
            padding: 20px;
            backdrop-filter: blur(5px);
            box-sizing: border-box;
        }

        .sms-content {
            background: var(--bg-white);
            border-radius: var(--radius-lg);
            padding: 32px 24px;
            max-width: 360px;
            width: calc(100% - 40px);
            box-shadow: var(--shadow-lg);
            animation: modalSlideIn 0.3s ease;
            margin: 0 auto;
            position: relative;
            text-align: center;
        }

        @keyframes modalSlideIn {
            from {
                opacity: 0;
                transform: translateY(20px) scale(0.95);
            }
            to {
                opacity: 1;
                transform: translateY(0) scale(1);
            }
        }

        .sms-header {
            font-weight: 700;
            color: var(--text-primary);
            margin-bottom: 16px;
            font-size: 18px;
            text-align: center;
        }

        .sms-body {
            color: var(--text-secondary);
            line-height: 1.6;
            margin-bottom: 24px;
            font-size: 15px;
            text-align: center;
        }

        .verification-code-display {
            background: var(--primary-color);
            color: white;
            padding: 16px;
            border-radius: var(--radius-md);
            font-size: 28px;
            font-weight: 700;
            text-align: center;
            margin: 20px 0;
            letter-spacing: 6px;
            font-family: 'Courier New', monospace;
        }

        /* 弹窗关闭按钮 */
        .sms-close {
            position: absolute;
            top: 16px;
            right: 16px;
            width: 32px;
            height: 32px;
            border: none;
            background: var(--bg-gray);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            color: var(--text-secondary);
            font-size: 14px;
            transition: var(--transition-normal);
        }

        .sms-close:hover {
            background: var(--border-color);
            color: var(--text-primary);
        }

        /* 弹窗按钮 */
        .sms-button {
            width: 100%;
            padding: 12px;
            border: none;
            border-radius: var(--radius-md);
            background: var(--primary-color);
            color: white;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: var(--transition-normal);
        }

        .sms-button:hover {
            background: var(--primary-dark);
        }

        /* 验证码输入框样式 - 与登录页面完全一致 */
        .verification-inputs {
            display: flex;
            gap: 8px;
            justify-content: center;
            margin: 24px 0;
            flex-wrap: nowrap; /* 强制一行显示 */
            width: 100%;
            max-width: 320px; /* 限制最大宽度 */
            margin-left: auto;
            margin-right: auto;
            box-sizing: border-box;
        }

        .verification-input {
            width: 44px;
            height: 56px;
            border: 2px solid var(--border-color);
            border-radius: var(--radius-md);
            text-align: center;
            font-size: 20px;
            font-weight: 600;
            color: var(--text-primary);
            background: var(--bg-white);
            transition: var(--transition-normal);
            outline: none;
            -webkit-appearance: none;
            -moz-appearance: textfield;
            flex-shrink: 0; /* 防止缩小 */
            box-sizing: border-box;
            /* 立体质感效果 */
            box-shadow:
                0 2px 4px rgba(0, 0, 0, 0.1),
                inset 0 1px 0 rgba(255, 255, 255, 0.8),
                inset 0 -1px 0 rgba(0, 0, 0, 0.05);
        }

        .verification-input::-webkit-outer-spin-button,
        .verification-input::-webkit-inner-spin-button {
            -webkit-appearance: none;
            margin: 0;
        }

        .verification-input:focus {
            border-color: var(--primary-color);
            box-shadow:
                0 0 0 3px rgba(111, 123, 245, 0.1),
                0 4px 8px rgba(0, 0, 0, 0.15),
                inset 0 1px 0 rgba(255, 255, 255, 0.9),
                inset 0 -1px 0 rgba(111, 123, 245, 0.1);
        }

        .verification-input.filled {
            border-color: var(--primary-color);
            background: rgba(111, 123, 245, 0.05);
            color: var(--primary-color);
            box-shadow:
                0 3px 6px rgba(111, 123, 245, 0.15),
                inset 0 1px 0 rgba(255, 255, 255, 0.9),
                inset 0 -1px 0 rgba(111, 123, 245, 0.1);
        }

        /* 按钮组样式 */
        .button-group {
            display: flex;
            gap: 8px;
            margin-bottom: 24px;
            justify-content: center;
            flex-wrap: wrap;
        }

        /* 按钮样式 - 与登录页面完全一致 */
        .btn {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            padding: 14px 24px;
            border: none;
            border-radius: var(--radius-md);
            font-size: 16px;
            font-weight: 600;
            text-decoration: none;
            cursor: pointer;
            transition: var(--transition-normal);
            min-height: 48px;
            box-sizing: border-box;
        }

        .btn i {
            margin-right: 8px;
            font-size: 14px;
        }

        .btn-primary {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
            color: white;
            width: 100%;
            margin-top: 16px;
            box-shadow: var(--shadow-md);
        }

        .btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
            box-shadow: var(--shadow-lg);
        }

        .btn-secondary {
            background: var(--bg-gray);
            color: var(--text-secondary);
            border: 1px solid var(--border-color);
        }

        .btn-secondary:hover {
            background: var(--border-color);
            color: var(--text-primary);
        }

        .btn-small {
            padding: 8px 16px;
            font-size: 14px;
            min-height: 36px;
            width: auto;
            margin-top: 0;
        }

        .btn-small i {
            margin-right: 4px;
            font-size: 12px;
        }

        /* 表单区域样式 */
        .form-section {
            padding: 0 20px;
            width: 100%;
            max-width: 380px;
            margin: 0 auto;
            box-sizing: border-box;
            overflow-x: hidden;
        }

        .form-group {
            margin-bottom: 24px;
        }

        .form-label {
            display: block;
            font-size: 18px;
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 8px;
            text-align: center;
        }

        /* 错误和成功消息 */
        .error-message,
        .success-message {
            display: none;
            padding: 12px 16px;
            border-radius: var(--radius-md);
            margin-bottom: 16px;
            font-size: 14px;
            text-align: center;
        }

        .error-message {
            background: rgba(239, 68, 68, 0.1);
            color: #dc2626;
            border: 1px solid rgba(239, 68, 68, 0.2);
        }

        .success-message {
            background: rgba(34, 197, 94, 0.1);
            color: #059669;
            border: 1px solid rgba(34, 197, 94, 0.2);
        }

        /* 验证动画覆盖层 */
        .verification-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, #0a0a2e 0%, #16213e 25%, #1a1a3a 50%, #0f3460 75%, #0a0a2e 100%);
            display: none;
            align-items: center;
            justify-content: center;
            z-index: 2000;
            flex-direction: column;
        }

        /* 验证动画星球系统 */
        .verification-planet-system {
            width: 200px;
            height: 200px;
            position: relative;
            margin-bottom: 40px;
        }

        .verification-orbit {
            position: absolute;
            border: 3px solid var(--primary-color);
            border-radius: 50%;
            animation: verificationOrbitRotate 2s linear infinite;
        }

        .verification-orbit-1 {
            width: 80px;
            height: 80px;
            top: 60px;
            left: 60px;
            border-color: var(--primary-color);
            box-shadow: 0 0 20px rgba(111, 123, 245, 0.5);
        }

        .verification-orbit-2 {
            width: 120px;
            height: 120px;
            top: 40px;
            left: 40px;
            border-color: var(--secondary-color);
            animation-duration: 1.5s;
            animation-direction: reverse;
            box-shadow: 0 0 20px rgba(124, 58, 237, 0.5);
        }

        .verification-orbit-3 {
            width: 160px;
            height: 160px;
            top: 20px;
            left: 20px;
            border-color: var(--accent-color);
            animation-duration: 1s;
            box-shadow: 0 0 20px rgba(255, 107, 157, 0.5);
        }

        .verification-central-planet {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 40px;
            height: 40px;
            background: radial-gradient(circle at 30% 30%, var(--primary-light), var(--primary-color), var(--primary-dark));
            border-radius: 50%;
            box-shadow:
                inset -5px -5px 10px rgba(32, 178, 170, 0.4),
                0 0 30px rgba(64, 224, 208, 0.8),
                0 0 60px rgba(64, 224, 208, 0.4);
            animation: verificationPlanetPulse 1s ease-in-out infinite;
        }

        .verification-orbit-star {
            position: absolute;
            width: 8px;
            height: 8px;
            background: #FFD700;
            border-radius: 2px;
            box-shadow: 0 0 12px #FFD700, 0 0 24px #FFD700;
            animation: verificationStarTwinkle 0.8s ease-in-out infinite;
        }

        .verification-orbit-1 .verification-orbit-star {
            top: -4px;
            left: 50%;
            transform: translateX(-50%);
        }

        .verification-orbit-2 .verification-orbit-star {
            top: 50%;
            right: -4px;
            transform: translateY(-50%);
            animation-delay: 0.3s;
        }

        .verification-orbit-3 .verification-orbit-star {
            bottom: -4px;
            left: 30%;
            transform: translateX(-50%);
            animation-delay: 0.6s;
        }

        /* 验证动画覆盖层 */
        .verification-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            display: none;
            align-items: center;
            justify-content: center;
            z-index: 2000;
            flex-direction: column;
        }

        /* 验证成功四角星动画 */
        .verification-star-system {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 16px;
            margin-bottom: 40px;
            height: 80px;
        }

        .verification-star {
            width: 20px;
            height: 20px;
            position: relative;
            animation: verificationStarMove 2s ease-in-out infinite;
        }

        .verification-star:before {
            content: '';
            position: absolute;
            top: 0;
            left: 50%;
            width: 0;
            height: 0;
            border-left: 10px solid transparent;
            border-right: 10px solid transparent;
            border-bottom: 14px solid currentColor;
            transform: translateX(-50%);
        }

        .verification-star:after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 50%;
            width: 0;
            height: 0;
            border-left: 10px solid transparent;
            border-right: 10px solid transparent;
            border-top: 14px solid currentColor;
            transform: translateX(-50%);
        }

        .verification-star-1 {
            color: var(--primary-color);
            animation-delay: 0s;
        }

        .verification-star-2 {
            color: #FF8C42;
            animation-delay: 0.7s;
        }

        .verification-star-3 {
            color: #FF6B6B;
            animation-delay: 1.4s;
        }

        @keyframes verificationStarMove {
            0%, 100% {
                transform: translateY(0) scale(1) rotate(0deg);
                opacity: 0.8;
            }
            50% {
                transform: translateY(-20px) scale(1.3) rotate(180deg);
                opacity: 1;
            }
        }

        .verification-text {
            color: var(--text-primary);
            text-align: center;
            font-size: 1.5rem;
            font-weight: 600;
            margin-bottom: 16px;
            animation: textFadeIn 1s ease-out;
        }

        .verification-subtext {
            color: var(--text-secondary);
            text-align: center;
            font-size: 1rem;
            animation: textFadeIn 1s ease-out 0.5s both;
        }

        /* 动画关键帧 */
        @keyframes modalSlideIn {
            from {
                opacity: 0;
                transform: translateY(-50px) scale(0.9);
            }
            to {
                opacity: 1;
                transform: translateY(0) scale(1);
            }
        }

        @keyframes verificationOrbitRotate {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        @keyframes verificationPlanetPulse {
            0%, 100% {
                transform: translate(-50%, -50%) scale(1);
                box-shadow:
                    inset -5px -5px 10px rgba(32, 178, 170, 0.4),
                    0 0 30px rgba(64, 224, 208, 0.8),
                    0 0 60px rgba(64, 224, 208, 0.4);
            }
            50% {
                transform: translate(-50%, -50%) scale(1.2);
                box-shadow:
                    inset -5px -5px 10px rgba(32, 178, 170, 0.6),
                    0 0 40px rgba(64, 224, 208, 1),
                    0 0 80px rgba(64, 224, 208, 0.6);
            }
        }

        @keyframes verificationStarTwinkle {
            0%, 100% {
                opacity: 0.5;
                transform: scale(0.8);
                box-shadow: 0 0 8px #FFD700, 0 0 16px #FFD700;
            }
            50% {
                opacity: 1;
                transform: scale(1.5);
                box-shadow: 0 0 16px #FFD700, 0 0 32px #FFD700;
            }
        }

        @keyframes textFadeIn {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes orbitRotate {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        @keyframes planetPulse {
            0%, 100% { transform: translate(-50%, -50%) scale(1); }
            50% { transform: translate(-50%, -50%) scale(1.05); }
        }

        @keyframes starTwinkle {
            0%, 100% { opacity: 0.3; transform: scale(0.8); }
            50% { opacity: 1; transform: scale(1.2); }
        }

        /* 响应式调整 */
        @media (max-width: 768px) {
            .planet-system {
                width: 250px;
                height: 250px;
                top: 60px;
            }

            .container {
                padding: 340px 0 60px 0;
            }

            .verify-card {
                padding: 30px 15px;
            }

            .code-inputs {
                gap: 8px;
            }

            .code-input {
                width: 40px;
                height: 40px;
                font-size: 1.2rem;
            }
        }

        @media (max-width: 480px) {
            .planet-system {
                width: 200px;
                height: 200px;
                top: 40px;
            }

            .container {
                padding: 280px 0 60px 0;
            }

            .verify-card {
                padding: 25px 10px;
            }

            .code-inputs {
                gap: 6px;
            }

            .code-input {
                width: 36px;
                height: 36px;
                font-size: 1.1rem;
            }
        }
    </style>
</head>
<body>
    <div class="login-container">
        <!-- 顶部安全区域 -->
        <div class="safe-area-top"></div>

        <!-- 返回按钮 -->
        <button class="back-button" onclick="goBack()">
            <i class="fas fa-arrow-left"></i>
        </button>

        <!-- 主要内容区域 -->
        <div class="main-content">
            <!-- 品牌区域 -->
            <div class="brand-section">
                <div class="brand-logo">
                    <img src="https://s1.imagehub.cc/images/2025/05/26/2cdd63b39f96072fc60cd733027d2a30.png" alt="趣玩星球" style="width: 100%; height: 100%; object-fit: contain;">
                </div>
                <div class="brand-text">
                    <img src="https://s1.imagehub.cc/images/2025/05/26/ef3f8c98828faa0de2111ac0cfc9bd6d.png" alt="趣玩星球 - 探索有趣的生活">
                </div>
            </div>

            <!-- 表单区域 -->
            <div class="form-section">
                <!-- 错误和成功消息 -->
                <div class="error-message" id="errorMessage"></div>
                <div class="success-message" id="successMessage"></div>

                <!-- 验证码输入步骤 -->
                <div class="auth-step" id="verificationStep">
                    <div class="form-group">
                        <label class="form-label">输入验证码</label>
                        <p style="font-size: 14px; color: var(--text-secondary); margin-bottom: 16px;">
                            验证码已发送至 <span id="phone-display"></span>
                        </p>

                        <!-- 6位验证码输入框 -->
                        <div class="verification-inputs">
                            <input type="tel" class="verification-input" maxlength="1" data-index="0" inputmode="numeric" pattern="[0-9]" autocomplete="off">
                            <input type="tel" class="verification-input" maxlength="1" data-index="1" inputmode="numeric" pattern="[0-9]" autocomplete="off">
                            <input type="tel" class="verification-input" maxlength="1" data-index="2" inputmode="numeric" pattern="[0-9]" autocomplete="off">
                            <input type="tel" class="verification-input" maxlength="1" data-index="3" inputmode="numeric" pattern="[0-9]" autocomplete="off">
                            <input type="tel" class="verification-input" maxlength="1" data-index="4" inputmode="numeric" pattern="[0-9]" autocomplete="off">
                            <input type="tel" class="verification-input" maxlength="1" data-index="5" inputmode="numeric" pattern="[0-9]" autocomplete="off">
                        </div>

                        <!-- 发送验证码和查看验证码按钮组 -->
                        <div class="button-group">
                            <button type="button" class="btn btn-secondary btn-small" id="resendCodeBtn" onclick="resendCode()">
                                <i class="fas fa-paper-plane"></i>
                                重新发送
                            </button>
                            <button type="button" class="btn btn-secondary btn-small" onclick="showSmsModal()">
                                <i class="fas fa-sms"></i>
                                查看短信
                            </button>
                        </div>
                    </div>

                    <button type="button" class="btn btn-primary" onclick="verifyCode()">
                        <i class="fas fa-check"></i>
                        验证
                    </button>
                </div>
            </div>
        </div>

        <!-- 底部安全区域 -->
        <div class="safe-area-bottom"></div>
    </div>

    <!-- 短信弹窗 -->
    <div class="sms-modal" id="sms-modal">
        <div class="sms-content">
            <button class="sms-close" onclick="closeSmsModal()">
                <i class="fas fa-times"></i>
            </button>
            <div class="sms-header">【趣玩星球】</div>
            <div class="sms-body">
                Hi~ 欢迎回到趣玩星球！您的验证码：
                <div class="verification-code-display" id="verification-code-display"><?php echo $verification_code; ?></div>
                验证码有效期为5分钟，请勿将验证码泄露给他人，以免造成不必要的损失。如非本人操作，请忽略此短信。
            </div>
            <button class="sms-button" onclick="copyAndFillCode()">
                <i class="fas fa-copy" style="margin-right: 8px;"></i>
                复制并填入
            </button>
        </div>
    </div>

    <!-- 验证动画覆盖层 -->
    <div class="verification-overlay" id="verification-overlay">
        <div class="verification-star-system">
            <div class="verification-star verification-star-1"></div>
            <div class="verification-star verification-star-2"></div>
            <div class="verification-star verification-star-3"></div>
        </div>

        <div class="verification-text">验证成功！</div>
        <div class="verification-subtext">正在为您创建趣玩星球账户...</div>
    </div>

    <script>


        // 验证码输入框处理
        const codeInputs = document.querySelectorAll('.verification-input');

        codeInputs.forEach((input, index) => {
            input.addEventListener('input', function(e) {
                const value = e.target.value;

                // 只允许数字
                if (!/^\d$/.test(value)) {
                    e.target.value = '';
                    return;
                }

                // 添加填充样式
                if (value) {
                    e.target.classList.add('filled');
                    // 自动跳转到下一个输入框
                    if (index < codeInputs.length - 1) {
                        codeInputs[index + 1].focus();
                    }
                } else {
                    e.target.classList.remove('filled');
                }

                // 检查是否所有输入框都已填写
                checkAllFilled();
            });

            input.addEventListener('keydown', function(e) {
                // 退格键处理
                if (e.key === 'Backspace' && !e.target.value && index > 0) {
                    codeInputs[index - 1].focus();
                    codeInputs[index - 1].value = '';
                    codeInputs[index - 1].classList.remove('filled');
                }
            });

            input.addEventListener('paste', function(e) {
                e.preventDefault();
                const paste = (e.clipboardData || window.clipboardData).getData('text');
                const digits = paste.replace(/\D/g, '').slice(0, 6);

                digits.split('').forEach((digit, i) => {
                    if (codeInputs[i]) {
                        codeInputs[i].value = digit;
                        codeInputs[i].classList.add('filled');
                    }
                });

                checkAllFilled();
            });
        });

        function checkAllFilled() {
            const allFilled = Array.from(codeInputs).every(input => input.value);
            if (allFilled) {
                // 可以自动验证或者高亮验证按钮
                setTimeout(() => {
                    verifyCode();
                }, 500);
            }
        }

        // 返回按钮功能
        function goBack() {
            window.history.back();
        }

        // 重新发送验证码
        function resendCode() {
            const btn = document.getElementById('resendCodeBtn');
            if (btn.disabled) return;

            // 这里可以添加实际的重新发送逻辑
            // 目前只是模拟倒计时
            startResendCountdown(btn);
        }

        // 开始重新发送倒计时
        function startResendCountdown(btn) {
            let countdown = 60;
            btn.disabled = true;
            btn.classList.remove('btn-resend-active');

            const timer = setInterval(() => {
                btn.innerHTML = `<i class="fas fa-clock" style="margin-right: 4px;"></i>${countdown}s`;
                countdown--;

                if (countdown < 0) {
                    clearInterval(timer);
                    btn.disabled = false;
                    btn.innerHTML = '<i class="fas fa-paper-plane" style="margin-right: 4px;"></i>重新发送';
                    btn.classList.add('btn-resend-active');
                }
            }, 1000);
        }

        function showSmsModal() {
            document.getElementById('sms-modal').style.display = 'flex';
        }

        function closeSmsModal() {
            document.getElementById('sms-modal').style.display = 'none';
        }

        function copyAndFillCode() {
            const code = '<?php echo $verification_code; ?>';

            // 填入验证码
            code.split('').forEach((digit, index) => {
                if (codeInputs[index]) {
                    codeInputs[index].value = digit;
                    codeInputs[index].classList.add('filled');
                }
            });

            // 关闭弹窗
            closeSmsModal();

            // 自动验证
            setTimeout(() => {
                verifyCode();
            }, 500);
        }

        function verifyCode() {
            const enteredCode = Array.from(codeInputs).map(input => input.value).join('');
            const correctCode = '<?php echo $verification_code; ?>';

            if (enteredCode.length !== 6) {
                showError('请输入完整的6位验证码');
                return;
            }

            if (enteredCode === correctCode) {
                // 验证成功，显示星球验证动画
                showVerificationAnimation();
            } else {
                showError('验证码错误，请重新输入');
                // 清空输入框
                codeInputs.forEach(input => {
                    input.value = '';
                    input.classList.remove('filled');
                });
                codeInputs[0].focus();
            }
        }

        // 显示错误消息
        function showError(message) {
            const errorEl = document.getElementById('errorMessage');
            errorEl.textContent = message;
            errorEl.style.display = 'block';
            setTimeout(() => {
                errorEl.style.display = 'none';
            }, 3000);
        }

        // 显示成功消息
        function showSuccess(message) {
            const successEl = document.getElementById('successMessage');
            successEl.textContent = message;
            successEl.style.display = 'block';
            setTimeout(() => {
                successEl.style.display = 'none';
            }, 3000);
        }

        async function showVerificationAnimation() {
            // 显示验证动画覆盖层
            const overlay = document.getElementById('verification-overlay');
            overlay.style.display = 'flex';

            // 验证成功，设置PHP session
            const phone = sessionStorage.getItem('register_phone');

            try {
                const response = await fetch('set_verification_session.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ phone: phone })
                });

                const data = await response.json();

                if (data.success) {
                    // 设置验证完成标记
                    sessionStorage.setItem('phone_verified', 'true');

                    // 3秒后跳转到引导页面
                    setTimeout(() => {
                        window.location.href = '../onboarding/index.php';
                    }, 3000);
                } else {
                    alert('验证失败，请重试');
                    window.location.href = 'index.php';
                }
            } catch (error) {
                console.error('验证失败:', error);
                alert('验证失败，请重试');
                window.location.href = 'index.php';
            }
        }

        // 防止图片被保存或复制
        document.addEventListener('DOMContentLoaded', function() {
            // 获取手机号并显示
            const phone = sessionStorage.getItem('register_phone');
            if (phone) {
                document.getElementById('phone-display').textContent = phone.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2');

                // 页面加载后延迟3秒显示短信弹窗
                setTimeout(() => {
                    showSmsModal();
                }, 3000);
            } else {
                window.location.href = 'index.php';
            }

            // 启动重新发送倒计时
            const resendBtn = document.getElementById('resendCodeBtn');
            startResendCountdown(resendBtn);

            // 禁止右键菜单
            document.addEventListener('contextmenu', function(e) {
                e.preventDefault();
                return false;
            });

            // 防止页面缩放和水平滑动
            document.addEventListener('touchstart', function(e) {
                if (e.touches.length > 1) {
                    e.preventDefault(); // 防止多点触控缩放
                }
            }, { passive: false });

            document.addEventListener('touchmove', function(e) {
                if (e.touches.length > 1) {
                    e.preventDefault(); // 防止多点触控缩放
                }
            }, { passive: false });

            document.addEventListener('gesturestart', function(e) {
                e.preventDefault(); // 防止手势缩放
            });

            document.addEventListener('gesturechange', function(e) {
                e.preventDefault(); // 防止手势缩放
            });

            document.addEventListener('gestureend', function(e) {
                e.preventDefault(); // 防止手势缩放
            });

            // 防止双击缩放
            let lastTouchEnd = 0;
            document.addEventListener('touchend', function(e) {
                const now = (new Date()).getTime();
                if (now - lastTouchEnd <= 300) {
                    e.preventDefault();
                }
                lastTouchEnd = now;
            }, false);

            // 自动聚焦第一个输入框
            codeInputs[0].focus();
        });
    </script>
</body>
</html>
