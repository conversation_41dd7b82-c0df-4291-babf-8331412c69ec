/**
 * 趣玩星球管理后台 - 现代化组件样式
 * 统一的页面组件和布局系统
 */

/* ==================== 页面布局组件 ==================== */

/* 主内容区域 */
.main-content {
    flex: 1;
    margin-left: var(--sidebar-width);
    min-height: 100vh;
    background: var(--gray-50);
    transition: var(--transition);
}

/* 页面头部 */
.page-header {
    background: var(--white);
    border-bottom: 1px solid var(--gray-200);
    padding: var(--spacing-xl) var(--spacing-2xl);
    margin-bottom: var(--spacing-xl);
    box-shadow: var(--shadow-sm);
    position: sticky;
    top: 0;
    z-index: var(--z-sticky);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
}

.page-header-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    max-width: 1400px;
    margin: 0 auto;
}

.page-title {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
}

.page-title h1 {
    font-size: var(--font-size-3xl);
    font-weight: 800;
    color: var(--gray-900);
    margin: 0;
    background: var(--primary-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    letter-spacing: -0.025em;
}

.page-title-icon {
    width: 48px;
    height: 48px;
    background: var(--primary-gradient);
    border-radius: var(--border-radius-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 20px;
    box-shadow: var(--shadow-primary);
}

.page-subtitle {
    font-size: var(--font-size-base);
    color: var(--gray-600);
    margin: var(--spacing-xs) 0 0 0;
    font-weight: 500;
}

.page-actions {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
}

/* 页面内容容器 */
.page-content {
    padding: 0 var(--spacing-2xl) var(--spacing-2xl);
    max-width: 1400px;
    margin: 0 auto;
}

/* ==================== 卡片组件 ==================== */

.card {
    background: var(--white);
    border-radius: var(--border-radius-xl);
    box-shadow: var(--shadow);
    border: 1px solid var(--gray-200);
    overflow: hidden;
    transition: var(--transition);
    position: relative;
}

.card:hover {
    box-shadow: var(--shadow-lg);
    transform: translateY(-2px);
}

.card-header {
    padding: var(--spacing-xl) var(--spacing-xl) var(--spacing-lg);
    border-bottom: 1px solid var(--gray-200);
    background: var(--gray-50);
    position: relative;
}

.card-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--primary-gradient);
}

.card-title {
    font-size: var(--font-size-xl);
    font-weight: 700;
    color: var(--gray-900);
    margin: 0;
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
}

.card-title i {
    color: var(--primary-color);
    font-size: var(--font-size-lg);
}

.card-subtitle {
    font-size: var(--font-size-sm);
    color: var(--gray-600);
    margin: var(--spacing-xs) 0 0 0;
    font-weight: 500;
}

.card-body {
    padding: var(--spacing-xl);
}

.card-footer {
    padding: var(--spacing-lg) var(--spacing-xl);
    border-top: 1px solid var(--gray-200);
    background: var(--gray-50);
    display: flex;
    align-items: center;
    justify-content: space-between;
}

/* ==================== 按钮组件 ==================== */

.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-md) var(--spacing-xl);
    font-size: var(--font-size-sm);
    font-weight: 600;
    line-height: 1;
    text-decoration: none;
    border: none;
    border-radius: var(--border-radius-lg);
    cursor: pointer;
    transition: var(--transition);
    position: relative;
    overflow: hidden;
    white-space: nowrap;
    user-select: none;
    letter-spacing: 0.025em;
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: var(--transition);
}

.btn:hover::before {
    left: 100%;
}

.btn:active {
    transform: scale(0.98);
}

.btn-primary {
    background: var(--primary-gradient);
    color: white;
    box-shadow: var(--shadow-primary);
}

.btn-primary:hover {
    background: var(--primary-gradient-hover);
    box-shadow: var(--shadow-primary-lg);
    transform: translateY(-2px);
    color: white;
}

.btn-secondary {
    background: var(--gray-100);
    color: var(--gray-700);
    border: 1px solid var(--gray-300);
}

.btn-secondary:hover {
    background: var(--gray-200);
    color: var(--gray-800);
    border-color: var(--gray-400);
}

.btn-success {
    background: linear-gradient(135deg, var(--success-color), var(--success-light));
    color: white;
    box-shadow: 0 10px 25px -5px rgba(16, 185, 129, 0.3);
}

.btn-success:hover {
    background: linear-gradient(135deg, var(--success-light), var(--success-lighter));
    transform: translateY(-2px);
    color: white;
}

.btn-danger {
    background: linear-gradient(135deg, var(--error-color), var(--error-light));
    color: white;
    box-shadow: 0 10px 25px -5px rgba(239, 68, 68, 0.3);
}

.btn-danger:hover {
    background: linear-gradient(135deg, var(--error-light), var(--error-lighter));
    transform: translateY(-2px);
    color: white;
}

.btn-warning {
    background: linear-gradient(135deg, var(--warning-color), var(--warning-light));
    color: white;
    box-shadow: 0 10px 25px -5px rgba(245, 158, 11, 0.3);
}

.btn-warning:hover {
    background: linear-gradient(135deg, var(--warning-light), var(--warning-lighter));
    transform: translateY(-2px);
    color: white;
}

.btn-info {
    background: linear-gradient(135deg, var(--info-color), var(--info-light));
    color: white;
    box-shadow: 0 10px 25px -5px rgba(59, 130, 246, 0.3);
}

.btn-info:hover {
    background: linear-gradient(135deg, var(--info-light), var(--info-lighter));
    transform: translateY(-2px);
    color: white;
}

/* 按钮尺寸 */
.btn-sm {
    padding: var(--spacing-sm) var(--spacing-md);
    font-size: var(--font-size-xs);
    border-radius: var(--border-radius);
}

.btn-lg {
    padding: var(--spacing-lg) var(--spacing-2xl);
    font-size: var(--font-size-base);
    border-radius: var(--border-radius-xl);
}

/* 按钮组 */
.btn-group {
    display: flex;
    gap: var(--spacing-sm);
}

.btn-group .btn {
    flex: 1;
}

/* ==================== 表格组件 ==================== */

.table-container {
    background: var(--white);
    border-radius: var(--border-radius-xl);
    overflow: hidden;
    box-shadow: var(--shadow);
    border: 1px solid var(--gray-200);
}

.table {
    width: 100%;
    border-collapse: collapse;
    font-size: var(--font-size-sm);
}

.table th {
    background: var(--gray-50);
    color: var(--gray-700);
    font-weight: 600;
    padding: var(--spacing-lg) var(--spacing-md);
    text-align: left;
    border-bottom: 2px solid var(--gray-200);
    font-size: var(--font-size-xs);
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.table td {
    padding: var(--spacing-md);
    border-bottom: 1px solid var(--gray-200);
    color: var(--gray-800);
    vertical-align: middle;
}

.table tbody tr {
    transition: var(--transition);
}

.table tbody tr:hover {
    background: var(--gray-50);
}

.table tbody tr:last-child td {
    border-bottom: none;
}

/* ==================== 表单组件 ==================== */

.form-group {
    margin-bottom: var(--spacing-lg);
}

.form-label {
    display: block;
    font-size: var(--font-size-sm);
    font-weight: 600;
    color: var(--gray-700);
    margin-bottom: var(--spacing-sm);
    letter-spacing: 0.025em;
}

.form-control {
    width: 100%;
    padding: var(--spacing-md) var(--spacing-lg);
    font-size: var(--font-size-sm);
    border: 2px solid var(--gray-300);
    border-radius: var(--border-radius-lg);
    background: var(--white);
    color: var(--gray-800);
    transition: var(--transition);
    font-family: var(--font-family);
}

.form-control:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(111, 123, 245, 0.1);
    background: var(--white);
}

.form-control::placeholder {
    color: var(--gray-500);
    font-weight: 400;
}

/* ==================== 状态指示器 ==================== */

.status-badge {
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-xs);
    padding: var(--spacing-xs) var(--spacing-md);
    font-size: var(--font-size-xs);
    font-weight: 600;
    border-radius: var(--border-radius-full);
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.status-badge.active {
    background: rgba(16, 185, 129, 0.1);
    color: var(--success-color);
}

.status-badge.inactive {
    background: rgba(107, 114, 128, 0.1);
    color: var(--gray-600);
}

.status-badge.pending {
    background: rgba(245, 158, 11, 0.1);
    color: var(--warning-color);
}

.status-badge.error {
    background: rgba(239, 68, 68, 0.1);
    color: var(--error-color);
}

/* ==================== 响应式设计 ==================== */

@media (max-width: 1024px) {
    .page-header {
        padding: var(--spacing-lg) var(--spacing-xl);
    }
    
    .page-content {
        padding: 0 var(--spacing-xl) var(--spacing-xl);
    }
    
    .card-header,
    .card-body,
    .card-footer {
        padding: var(--spacing-lg);
    }
}

@media (max-width: 768px) {
    .main-content {
        margin-left: 0;
    }
    
    .page-header {
        padding: var(--spacing-md) var(--spacing-lg);
    }
    
    .page-header-content {
        flex-direction: column;
        gap: var(--spacing-md);
        align-items: flex-start;
    }
    
    .page-title h1 {
        font-size: var(--font-size-2xl);
    }
    
    .page-content {
        padding: 0 var(--spacing-lg) var(--spacing-lg);
    }
    
    .btn-group {
        flex-direction: column;
    }
    
    .table-container {
        overflow-x: auto;
    }
}
