<?php
// 根目录重定向到首页
session_start();
header('Location: /frontend/home/<USER>');
exit;
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <meta name="theme-color" content="#279CF9"> <!-- 设置状态栏颜色为#279CF9 -->
    <meta name="apple-mobile-web-app-capable" content="yes"> <!-- 启用iOS全屏模式 -->
    <meta name="apple-mobile-web-app-status-bar-style" content="default"> <!-- iOS状态栏样式 -->
    <meta name="msapplication-navbutton-color" content="#279CF9"> <!-- Windows Phone状态栏颜色 -->
    <title>欢迎来到 趣玩星球</title>
    <style>
        :root {
            --theme-color: #40E0D0;
            --theme-color-dark: #30c0b0;
            --text-color: #333;
            --light-gray: #f0f0f0;
            --modal-backdrop: rgba(0, 0, 0, 0.6);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'PingFang SC', 'Helvetica Neue', 'Microsoft YaHei', Arial, sans-serif;
            -webkit-touch-callout: none; /* 禁止iOS长按弹出菜单 */
            -webkit-user-select: none; /* 禁止选择文本 */
            -khtml-user-select: none;
            -moz-user-select: none;
            -ms-user-select: none;
            user-select: none;
        }

        body {
            /* 使用背景图片 */
            background-image: url('https://s1.imagehub.cc/images/2025/05/21/a47ed0a62fb14f43ab520b52519f3162.png');
            background-position: center center;
            background-repeat: no-repeat;
            background-size: cover; /* 覆盖整个视口 */
            background-attachment: fixed; /* 固定背景，防止滚动 */
            color: var(--text-color);
            line-height: 1.6;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            min-height: 100vh;
            text-align: center;
            padding: 20px;
            overflow: hidden;
            position: relative; /* 为遮罩层定位 */
        }

        /* 添加一个透明遮罩层，防止背景图片被保存 */
        body::before {
            content: "";
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            z-index: -1; /* 放在背景图片上方，但在内容下方 */
            pointer-events: none; /* 允许点击穿透 */
        }

        /* 首页不需要内容，只保留背景图片和弹窗 */

        /* 针对不同屏幕尺寸的背景图片适配 */
        @media (max-width: 414px) { /* iPhone 11 Pro Max 及以下 */
            body {
                background-size: cover;
            }
        }

        @media (max-width: 375px) { /* iPhone X/11 Pro 及以下 */
            body {
                background-size: cover;
                background-position: center top;
            }
        }

        @media (max-width: 320px) { /* iPhone SE 及以下 */
            body {
                background-size: cover;
                background-position: center top;
            }
        }

        @media (min-width: 768px) { /* 平板及以上 */
            body {
                background-size: contain;
                background-position: center center;
            }
        }

        /* 用户协议弹窗样式 */
        .modal-overlay {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: rgba(255, 255, 255, 0); /* 全透明背景 */
            z-index: 10000;
            display: flex;
            justify-content: center;
            align-items: center;
            opacity: 0;
            visibility: hidden;
            transition: opacity 0.3s ease, visibility 0s linear 0.3s;
        }

        .modal-overlay.active {
            opacity: 1;
            visibility: visible;
            transition: opacity 0.3s ease;
        }

        .terms-modal-container {
            width: 92%;
            max-width: 480px; /* 增加最大宽度 */
            position: relative;
            transform: scale(0.95);
            transition: transform 0.3s ease;
            display: flex;
            flex-direction: column;
            align-items: center;
            background: transparent; /* 确保背景透明 */
        }

        .modal-overlay.active .terms-modal-container {
            transform: scale(1);
        }

        /* 使用图片作为整体弹窗 */
        .terms-modal {
            position: relative;
            width: 100%;
            display: flex;
            flex-direction: column;
            align-items: center;
            background: transparent; /* 确保背景透明 */
        }

        /* 弹窗图片容器 */
        .terms-modal-image {
            position: relative;
            width: 100%;
            overflow: hidden;
            /* 移除边框圆角和阴影 */
        }

        /* 背景图片 */
        .terms-modal-bg {
            width: 100%;
            height: auto;
            display: block;
            pointer-events: none; /* 禁止鼠标事件 */
            -webkit-touch-callout: none; /* 禁止iOS长按弹出菜单 */
            -webkit-user-select: none; /* 禁止选择 */
            -khtml-user-select: none;
            -moz-user-select: none;
            -ms-user-select: none;
            user-select: none;
            -webkit-user-drag: none; /* 禁止拖拽 */
            -khtml-user-drag: none;
            -moz-user-drag: none;
            -o-user-drag: none;
            user-drag: none;
        }

        /* 关闭按钮 */
        .terms-modal-close {
            position: absolute;
            top: 15px;
            right: 15px;
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background-color: rgba(255, 255, 255, 0.3);
            backdrop-filter: blur(5px);
            -webkit-backdrop-filter: blur(5px);
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            z-index: 10;
            border: none;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        .terms-modal-close:before,
        .terms-modal-close:after {
            content: '';
            position: absolute;
            width: 16px;
            height: 2px;
            background-color: #555; /* 改为深色，与新图片风格匹配 */
            border-radius: 1px;
        }

        .terms-modal-close:before {
            transform: rotate(45deg);
        }

        .terms-modal-close:after {
            transform: rotate(-45deg);
        }

        /* 协议内容覆盖在图片白色区域 */
        .terms-content-wrapper {
            position: absolute;
            top: 28%; /* 大幅减少顶部空白 */
            left: 8%;
            right: 8%;
            bottom: 5%; /* 留出底部空间 */
            padding: 10px 15px 20px;
            overflow-y: auto;
            max-height: 67%; /* 增加内容区域高度 */
            width: 84%; /* 进一步限制宽度，防止内容溢出 */
            box-sizing: border-box;
        }

        .terms-modal-title {
            font-size: 1rem;
            font-weight: bold;
            color: #333;
            margin-bottom: 6px;
            text-align: center;
        }

        .terms-scrollable-content {
            color: #444;
            text-align: left; /* 改为左对齐，更符合阅读习惯 */
            word-wrap: break-word; /* 允许长单词换行 */
            overflow-wrap: break-word; /* 现代浏览器支持 */
        }

        .terms-scrollable-content p {
            margin-bottom: 4px;
            font-size: 0.78rem;
            line-height: 1.25;
            text-align: left; /* 改为左对齐 */
            max-width: 100%; /* 确保不超出容器 */
            hyphens: auto; /* 允许单词断字 */
        }

        .terms-bullet-list {
            list-style-type: none;
            padding-left: 0;
            margin: 4px 0 6px;
            text-align: left; /* 改为左对齐 */
            max-width: 100%; /* 确保不超出容器 */
        }

        .terms-bullet-list li {
            position: relative;
            margin-bottom: 4px;
            font-size: 0.78rem;
            color: #444;
            padding-left: 10px; /* 进一步减小缩进 */
            text-align: left; /* 改为左对齐 */
            line-height: 1.25;
            max-width: 100%; /* 确保不超出容器 */
            box-sizing: border-box; /* 包含内边距在宽度内 */
            hyphens: auto; /* 允许单词断字 */
        }

        .terms-scrollable-content a {
            color: #40E0D0;
            text-decoration: none;
        }

        .terms-scrollable-content a:hover {
            text-decoration: underline;
        }

        .terms-bullet-list li:before {
            content: "•";
            position: absolute;
            left: 0; /* 进一步调整项目符号位置 */
            color: #444;
            font-size: 0.78rem; /* 减小项目符号大小 */
        }

        /* 按钮放在图片外部底部 */
        .terms-modal-actions {
            width: 100%;
            margin-top: 20px;
            padding: 0 10px;
        }

        .btn-agree {
            background: linear-gradient(90deg, #40E0D0, #4D5DFB); /* 使用项目主题色和辅助色 */
            color: white;
            padding: 14px 0;
            border-radius: 50px;
            font-weight: bold;
            border: none;
            cursor: pointer;
            font-size: 1.05rem;
            width: 100%;
            box-shadow: 0 4px 15px rgba(64, 224, 208, 0.3);
            text-align: center;
            display: block;
            transition: all 0.3s ease;
        }

        .btn-agree:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 18px rgba(64, 224, 208, 0.4);
        }

        .disagree-text {
            font-size: 0.85rem;
            color: #999;
            text-align: center;
            margin-top: 12px;
        }

        /* 响应式调整 */
        @media (max-width: 400px) {
            .terms-modal-container {
                max-width: calc(100% - 40px);
            }
            .terms-modal-title {
                font-size: 1rem;
                margin-bottom: 8px;
            }
            .terms-scrollable-content p,
            .terms-bullet-list li {
                font-size: 0.8rem;
                margin-bottom: 5px;
                line-height: 1.3;
            }
            .terms-content-wrapper {
                padding: 8px 12px 12px;
                top: 28%;
                max-height: 67%;
                left: 10%;
                right: 10%;
                width: 80%;
            }
        }

        /* 添加更大屏幕的响应式调整 */
        @media (min-width: 500px) {
            .terms-content-wrapper {
                top: 29%;
                bottom: 6%;
                max-height: 65%;
                left: 9%;
                right: 9%;
                width: 82%;
                padding: 10px 15px 20px;
            }
        }
    </style>
</head>
<body>

    <!-- 首页不需要内容，只保留背景图片和弹窗 -->

    <?php if (!$agreed_to_terms): ?>
    <div class="modal-overlay active" id="termsModalOverlay">
        <div class="terms-modal-container">
            <form method="POST" action="<?php echo htmlspecialchars($_SERVER['PHP_SELF']); ?>" class="terms-modal">
                <div class="terms-modal-image">
                    <img src="https://s1.imagehub.cc/images/2025/05/21/ce79d7821efab6a271a55b02baf17a0d.png" alt="弹窗背景" class="terms-modal-bg">
                    <button type="button" class="terms-modal-close" id="closeModal"></button>

                    <div class="terms-content-wrapper">
                        <div class="terms-modal-title">用户服务协议及隐私政策</div>
                        <div class="terms-scrollable-content">
                            <p>欢迎使用趣玩星球！</p>
                            <p>为了更好地保护您的权益，请您仔细阅读并充分理解《用户服务协议》和《隐私政策》的全部内容。点击"同意并继续"，即表示您已阅读并同意以下条款。</p>
                            <p><strong>用户协议摘要：</strong></p>
                            <ul class="terms-bullet-list">
                                <li><strong>服务内容：</strong> 趣玩星球为您提供社交互动、活动组织、内容分享等服务，我们保留随时变更、中断或终止部分或全部服务的权利。</li>
                                <li><strong>账号规范：</strong> 您需对账号安全负责，不得将账号出借、转让或用于违法活动，否则我们有权限制或终止您的使用权限。</li>
                                <li><strong>用户行为：</strong> 您应遵守法律法规，不得发布违法、侵权、色情、暴力等不良信息，否则将承担相应法律责任。</li>
                                <li><strong>知识产权：</strong> 您在平台发布的内容，授权我们以合理方式使用；平台内容的知识产权归趣玩星球或第三方所有。</li>
                            </ul>
                            <p><strong>隐私政策摘要：</strong></p>
                            <ul class="terms-bullet-list">
                                <li><strong>信息收集：</strong> 我们会收集您的注册信息、位置信息、设备信息及使用记录，用于提供和优化服务。</li>
                                <li><strong>信息使用：</strong> 我们使用您的信息用于身份验证、服务提供、安全保障、体验优化及法律要求等目的。</li>
                                <li><strong>信息保护：</strong> 我们采取严格的数据安全措施保护您的个人信息，未经您同意不会向第三方分享您的个人敏感信息。</li>
                                <li><strong>权利说明：</strong> 您有权查询、更正、删除个人信息，管理隐私设置，注销账号等。如有疑问，可通过客服联系我们。</li>
                            </ul>
                            <p>完整版协议请访问：<a href="../policies/user_agreement.php">《用户服务协议》</a>和<a href="../policies/privacy_policy.php">《隐私政策》</a></p>
                        </div>
                    </div>
                </div>

                <div class="terms-modal-actions">
                    <button type="submit" name="agree_terms" value="true" class="btn-agree">同意并继续</button>
                    <p class="disagree-text">若您不同意本协议，将无法使用我们的核心服务</p>
                </div>
            </form>
        </div>
    </div>
    <?php endif; ?>

    <script>
        // 关闭弹窗按钮功能
        document.addEventListener('DOMContentLoaded', function() {
            const closeBtn = document.getElementById('closeModal');
            const modalOverlay = document.getElementById('termsModalOverlay');

            if (closeBtn && modalOverlay) {
                closeBtn.addEventListener('click', function() {
                    modalOverlay.classList.remove('active');
                });
            }

            // 禁止右键菜单
            document.addEventListener('contextmenu', function(e) {
                e.preventDefault();
                return false;
            });

            // 禁止拖拽图片
            document.addEventListener('dragstart', function(e) {
                e.preventDefault();
                return false;
            });

            // 禁止长按选择
            document.addEventListener('selectstart', function(e) {
                e.preventDefault();
                return false;
            });

            // 禁止触摸长按弹出菜单
            document.addEventListener('touchstart', function(e) {
                // 允许按钮和链接的触摸事件
                if (e.target.tagName === 'BUTTON' || e.target.tagName === 'A') {
                    return true;
                }
                // 防止长按弹出菜单
                if (e.touches.length > 1 || e.targetTouches.length > 1) {
                    e.preventDefault();
                    return false;
                }
            }, { passive: false });

            // 禁止保存图片
            const images = document.querySelectorAll('img');
            images.forEach(function(img) {
                img.addEventListener('contextmenu', function(e) {
                    e.preventDefault();
                    return false;
                });
            });
        });
    </script>

</body>
</html>
