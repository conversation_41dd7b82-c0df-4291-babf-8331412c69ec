// 聊天页面JavaScript

document.addEventListener('DOMContentLoaded', function() {
    // 初始化聊天页面
    initializeChatPage();
});

// 初始化聊天页面
function initializeChatPage() {
    // 滚动到底部
    scrollToBottom();

    // 初始化输入框
    initializeInput();

    // 初始化按钮事件
    initializeButtons();

    // 自动调整输入框高度
    autoResizeTextarea();

    // 模拟智能回复（用于测试）
    initializeAutoReply();
}

// 滚动到底部
function scrollToBottom() {
    const messageContainer = document.getElementById('messageContainer');
    messageContainer.scrollTop = messageContainer.scrollHeight;
}

// 初始化输入框
function initializeInput() {
    const messageInput = document.getElementById('messageInput');
    const sendButton = document.getElementById('sendButton');
    const moreButton = document.getElementById('moreButton');

    messageInput.addEventListener('input', function() {
        const hasText = this.value.trim().length > 0;

        if (hasText) {
            sendButton.style.display = 'flex';
            moreButton.style.display = 'none';
        } else {
            sendButton.style.display = 'none';
            moreButton.style.display = 'flex';
        }
    });

    // 回车发送消息
    messageInput.addEventListener('keydown', function(e) {
        if (e.key === 'Enter' && !e.shiftKey) {
            e.preventDefault();
            sendMessage();
        }
    });
}

// 初始化按钮事件
function initializeButtons() {
    // 发送按钮
    document.getElementById('sendButton').addEventListener('click', sendMessage);

    // 更多按钮
    document.getElementById('moreButton').addEventListener('click', showMoreMenu);

    // 表情按钮
    document.getElementById('emojiButton').addEventListener('click', function() {
        showToast('表情功能开发中...');
    });

    // 语音按钮
    document.getElementById('voiceButton').addEventListener('click', function() {
        showToast('语音功能开发中...');
    });

    // 文件输入
    document.getElementById('imageInput').addEventListener('change', handleImageSelect);
    document.getElementById('fileInput').addEventListener('change', handleFileSelect);
}

// 发送消息
function sendMessage() {
    const messageInput = document.getElementById('messageInput');
    const content = messageInput.value.trim();

    if (!content) return;

    // 添加消息到界面
    addMessageToUI({
        sender_id: window.chatData.userId,
        username: window.chatData.userName,
        avatar: window.chatData.userAvatar,
        content: content,
        created_at: new Date().toISOString()
    }, true);

    // 清空输入框
    messageInput.value = '';
    messageInput.style.height = '36px';

    // 隐藏发送按钮，显示更多按钮
    document.getElementById('sendButton').style.display = 'none';
    document.getElementById('moreButton').style.display = 'flex';

    // 滚动到底部
    setTimeout(scrollToBottom, 100);

    // 发送到服务器（这里先模拟）
    sendMessageToServer(content);
}

// 发送消息到服务器
function sendMessageToServer(content) {
    // 检测危险内容
    const riskLevel = detectRiskContent(content);

    // 这里应该发送AJAX请求到服务器
    // 暂时模拟发送成功
    console.log('发送消息:', content);

    // 如果检测到高风险内容，立即触发安全警报
    if (riskLevel === 'high') {
        setTimeout(() => {
            triggerSafetyAlert(content, riskLevel);
        }, 500);
    } else if (riskLevel === 'medium') {
        // 中等风险，延迟触发警报
        setTimeout(() => {
            triggerSafetyAlert(content, riskLevel);
        }, 2000);
    }

    // 模拟智能回复
    setTimeout(() => {
        simulateReply(content);
    }, 1000 + Math.random() * 2000);
}

// 模拟智能回复
function simulateReply(originalMessage) {
    const replies = getSmartReply(originalMessage);
    const randomReply = replies[Math.floor(Math.random() * replies.length)];

    // Vancrest技术部13名成员（根据您的要求更新）
    const members = [
        { id: 1, name: '程威', role: '项目经理', avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face' },
        { id: 2, name: '张楚', role: '前端开发', avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face' },
        { id: 3, name: '张李冠', role: '前端开发', avatar: 'https://images.unsplash.com/photo-1519345182560-3f2917c472ef?w=150&h=150&fit=crop&crop=face' },
        { id: 4, name: '黄琳', role: 'UI设计师', avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150&h=150&fit=crop&crop=face' },
        { id: 5, name: '李明轩', role: '后端开发', avatar: 'https://images.unsplash.com/photo-1500648767791-00dcc994a43e?w=150&h=150&fit=crop&crop=face' },
        { id: 6, name: '王思雨', role: '测试工程师', avatar: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150&h=150&fit=crop&crop=face' },
        { id: 7, name: '陈浩然', role: '后端开发', avatar: 'https://images.unsplash.com/photo-1506794778202-cad84cf45f1d?w=150&h=150&fit=crop&crop=face' },
        { id: 8, name: '刘雅婷', role: 'UI设计师', avatar: 'https://images.unsplash.com/photo-1544005313-94ddf0286df2?w=150&h=150&fit=crop&crop=face' },
        { id: 9, name: '赵志强', role: '运维工程师', avatar: 'https://images.unsplash.com/photo-1507591064344-4c6ce005b128?w=150&h=150&fit=crop&crop=face' },
        { id: 10, name: '孙美玲', role: '测试工程师', avatar: 'https://images.unsplash.com/photo-1487412720507-e7ab37603c6f?w=150&h=150&fit=crop&crop=face' },
        { id: 11, name: 'David Miller', role: '全栈开发', avatar: 'https://images.unsplash.com/photo-1560250097-0b93528c311a?w=150&h=150&fit=crop&crop=face' },
        { id: 12, name: 'Sophia Johnson', role: '产品设计师', avatar: 'https://images.unsplash.com/photo-1580489944761-15a19d654956?w=150&h=150&fit=crop&crop=face' },
        { id: 13, name: '马建国', role: '运维工程师', avatar: 'https://images.unsplash.com/photo-1566492031773-4f4e44671d66?w=150&h=150&fit=crop&crop=face' }
    ];

    // 排除当前用户，避免自己回复自己
    const availableMembers = members.filter(member => member.id !== window.chatData.userId);
    const randomMember = availableMembers[Math.floor(Math.random() * availableMembers.length)];

    // 根据角色调整回复内容
    let finalReply = randomReply;
    if (randomMember.role.includes('前端') && originalMessage.toLowerCase().includes('前端')) {
        finalReply = replies[0]; // 选择更相关的回复
    } else if (randomMember.role.includes('后端') && originalMessage.toLowerCase().includes('后端')) {
        finalReply = replies[0];
    } else if (randomMember.role.includes('UI') && originalMessage.toLowerCase().includes('设计')) {
        finalReply = replies[0];
    } else if (randomMember.role.includes('测试') && originalMessage.toLowerCase().includes('测试')) {
        finalReply = replies[0];
    } else if (randomMember.role.includes('运维') && originalMessage.toLowerCase().includes('运维')) {
        finalReply = replies[0];
    }

    // 外国同事用英文回复的概率更高
    if (randomMember.name.includes('David') || randomMember.name.includes('Sophia')) {
        const englishReplies = [
            'Sounds good!', 'Great work!', 'I agree with that.', 'Let me check on this.',
            'Perfect timing!', 'Thanks for the update.', 'Looking forward to it!',
            'That makes sense.', 'I\'ll work on it.', 'Good point!', 'Absolutely!',
            'Let me know if you need help.', 'I\'m on it!', 'Excellent progress!'
        ];
        if (Math.random() > 0.6) {
            finalReply = englishReplies[Math.floor(Math.random() * englishReplies.length)];
        }
    }

    addMessageToUI({
        sender_id: randomMember.id,
        username: randomMember.name,
        avatar: randomMember.avatar,
        content: finalReply,
        created_at: new Date().toISOString()
    }, false);

    setTimeout(scrollToBottom, 100);
}

// 智能回复逻辑 - 增强版
function getSmartReply(message) {
    const msg = message.toLowerCase();

    // 问候语
    if (msg.includes('你好') || msg.includes('大家好') || msg.includes('早上好') || msg.includes('下午好') || msg.includes('晚上好')) {
        return ['你好！', '大家好！', '嗨！', '你好呀~', '早上好！', 'Hi there!', '下午好！', '晚上好！'];
    }

    // 确认回复
    if (msg.includes('收到') || msg.includes('好的') || msg.includes('ok') || msg.includes('明白')) {
        return ['收到！', '好的', '明白了', 'OK', '没问题', 'Roger that!', '了解', '👍'];
    }

    // 前端开发相关
    if (msg.includes('前端') || msg.includes('vue') || msg.includes('react') || msg.includes('javascript') || msg.includes('css') || msg.includes('html')) {
        return [
            '前端这边没问题！',
            'Vue3的Composition API真的很好用',
            'React Hooks让开发效率提升了不少',
            'CSS Grid布局解决了很多响应式问题',
            'TypeScript的类型检查帮我们避免了很多bug',
            'Webpack5的模块联邦功能很强大',
            '前端性能优化还有很大提升空间'
        ];
    }

    // 后端开发相关
    if (msg.includes('后端') || msg.includes('api') || msg.includes('数据库') || msg.includes('服务器') || msg.includes('mysql') || msg.includes('redis')) {
        return [
            '后端API已经优化完成',
            'MySQL索引优化后查询速度提升了30%',
            'Redis缓存策略需要再调整一下',
            '微服务架构的容错机制已经完善',
            'API文档已经更新到最新版本',
            '数据库连接池配置已经优化',
            '分布式锁的实现用Redis比较合适'
        ];
    }

    // UI设计相关
    if (msg.includes('ui') || msg.includes('设计') || msg.includes('界面') || msg.includes('用户体验') || msg.includes('原型')) {
        return [
            'UI设计稿已经更新了',
            '用户体验测试反馈很不错',
            'Figma原型已经分享给大家',
            '这个交互设计很符合用户习惯',
            '色彩搭配需要再调整一下',
            '移动端适配已经完成',
            'Design System已经建立完善'
        ];
    }

    // 测试相关
    if (msg.includes('测试') || msg.includes('bug') || msg.includes('质量') || msg.includes('自动化')) {
        return [
            '测试用例已经覆盖了所有功能点',
            '自动化测试脚本运行正常',
            '发现了一个边界条件的bug',
            '性能测试结果符合预期',
            '单元测试覆盖率达到95%',
            '集成测试已经通过',
            '这个bug的优先级比较高，需要尽快修复'
        ];
    }

    // 运维相关
    if (msg.includes('运维') || msg.includes('部署') || msg.includes('服务器') || msg.includes('监控') || msg.includes('docker')) {
        return [
            '服务器监控一切正常',
            'Docker容器已经部署完成',
            'CI/CD流水线运行顺畅',
            '负载均衡配置已经优化',
            'Kubernetes集群状态良好',
            '日志分析显示系统运行稳定',
            '备份策略已经完善'
        ];
    }

    // 项目管理相关
    if (msg.includes('项目') || msg.includes('进度') || msg.includes('计划') || msg.includes('需求')) {
        return [
            '项目进度按计划推进',
            '需求分析已经完成',
            '里程碑节点都能按时完成',
            '风险评估报告已经更新',
            '资源分配比较合理',
            '客户反馈整体满意',
            '下个迭代的需求已经确定'
        ];
    }

    // 会议相关
    if (msg.includes('会议') || msg.includes('开会') || msg.includes('讨论') || msg.includes('评审')) {
        return [
            '会议时间确定了吗？',
            '我会准时参加',
            '需要准备什么资料？',
            '会议室在哪里？',
            '议程已经发给大家了',
            '代码评审会议很有收获',
            '技术分享会安排在周五'
        ];
    }

    // 技术学习相关
    if (msg.includes('学习') || msg.includes('技术') || msg.includes('新技术') || msg.includes('框架')) {
        return [
            '最近在学习新的技术栈',
            'GitHub上有个很不错的开源项目',
            '这个技术方案值得深入研究',
            '技术选型需要考虑团队熟悉度',
            '新框架的学习成本还是比较高的',
            '技术债务需要逐步偿还',
            '代码重构能提升可维护性'
        ];
    }

    // 感谢和鼓励
    if (msg.includes('辛苦') || msg.includes('谢谢') || msg.includes('感谢') || msg.includes('加油')) {
        return [
            '不客气！',
            '应该的',
            '大家都辛苦了',
            '团队合作！',
            '一起加油！',
            '互相帮助是应该的',
            '团队的力量！',
            '继续保持！'
        ];
    }

    // 不文明用语和争吵回复（测试用）
    if (msg.includes('傻逼') || msg.includes('白痴') || msg.includes('蠢货') || msg.includes('垃圾') || msg.includes('废物')) {
        return [
            '说话注意点素质',
            '能不能好好说话？',
            '这样说话不太好吧',
            '大家都是同事，别这样',
            '冷静一下',
            '有话好好说',
            '素质呢？'
        ];
    }

    if (msg.includes('操') || msg.includes('草') || msg.includes('靠') || msg.includes('艹') || msg.includes('妈的')) {
        return [
            '注意用词',
            '能文明点吗？',
            '说脏话不好',
            '控制一下情绪',
            '这里有女同事在',
            '素质素质',
            '别爆粗口'
        ];
    }

    if (msg.includes('滚') || msg.includes('闭嘴') || msg.includes('shut up') || msg.includes('get out')) {
        return [
            '你这样说话过分了',
            '太没礼貌了',
            '这是工作群，注意言辞',
            '大家都是成年人，别这样',
            '冷静冷静',
            '有什么事好好沟通',
            '别这么激动'
        ];
    }

    // 争吵和冲突回复
    if (msg.includes('吵架') || msg.includes('争吵') || msg.includes('争执') || msg.includes('矛盾')) {
        return [
            '大家别吵了',
            '有什么事私下解决',
            '工作群里别争执',
            '冷静一下再说',
            '都是同事，没必要这样',
            '有分歧很正常，理性讨论',
            '先各自冷静一下'
        ];
    }

    // 情绪激动回复
    if (msg.includes('不爽') || msg.includes('生气') || msg.includes('愤怒') || msg.includes('火大')) {
        return [
            '怎么了？发生什么事了？',
            '别生气，有什么问题说出来',
            '深呼吸，冷静一下',
            '是工作上遇到困难了吗？',
            '有什么需要帮助的吗？',
            '别太激动，慢慢说',
            '先平复一下心情'
        ];
    }

    if (msg.includes('烦死了') || msg.includes('气死了') || msg.includes('受不了') || msg.includes('忍不了')) {
        return [
            '是什么事情让你这么烦？',
            '要不要休息一下？',
            '有什么困难可以说出来',
            '大家一起想办法解决',
            '别憋着，说出来会好一些',
            '工作压力大是正常的',
            '要不要出去透透气？'
        ];
    }

    // 投诉和抱怨回复
    if (msg.includes('投诉') || msg.includes('举报') || msg.includes('曝光') || msg.includes('差评')) {
        return [
            '具体是什么问题？',
            '可以详细说明一下情况吗？',
            '我们会认真处理的',
            '先了解一下具体情况',
            '有什么证据可以提供吗？',
            '我们一起分析一下问题',
            '会按流程处理的'
        ];
    }

    // 订单和服务问题回复
    if (msg.includes('退款') || msg.includes('退货') || msg.includes('假货') || msg.includes('质量问题')) {
        return [
            '什么产品出现问题了？',
            '可以提供订单号吗？',
            '我们马上核实情况',
            '先看看具体是什么问题',
            '会按照规定处理的',
            '客服会联系你的',
            '请保留好相关凭证'
        ];
    }

    if (msg.includes('服务差') || msg.includes('态度恶劣') || msg.includes('欺骗') || msg.includes('坑钱')) {
        return [
            '抱歉给您带来不好的体验',
            '我们会改进服务质量的',
            '具体是哪个环节出了问题？',
            '会向相关部门反馈的',
            '您的意见很重要',
            '我们会认真对待的',
            '感谢您的反馈'
        ];
    }

    // 技术问题和bug回复
    if (msg.includes('bug') || msg.includes('崩溃') || msg.includes('卡死') || msg.includes('闪退')) {
        return [
            '什么时候出现的问题？',
            '能复现这个bug吗？',
            '我来看看日志',
            '先回滚到上个版本',
            '这个问题优先级很高',
            '马上修复',
            '已经定位到问题了'
        ];
    }

    // 性能和优化回复
    if (msg.includes('慢') || msg.includes('卡') || msg.includes('延迟') || msg.includes('超时')) {
        return [
            '网络环境怎么样？',
            '可能是服务器压力大',
            '我们优化一下性能',
            '检查一下数据库查询',
            '可能需要加缓存',
            '负载均衡需要调整',
            '监控显示确实有延迟'
        ];
    }

    // 加班和工作压力回复
    if (msg.includes('加班') || msg.includes('累') || msg.includes('疲惫') || msg.includes('压力大')) {
        return [
            '辛苦了，注意休息',
            '身体最重要',
            '别太拼命',
            '工作做不完明天再说',
            '要不要调整一下任务优先级？',
            '团队一起分担',
            '健康第一'
        ];
    }

    // 学习和成长回复
    if (msg.includes('不会') || msg.includes('不懂') || msg.includes('学习') || msg.includes('请教')) {
        return [
            '我来教你',
            '这个我比较熟悉',
            '可以看看官方文档',
            '我发个教程给你',
            '一起研究一下',
            '学习态度很好',
            '不懂就问是对的'
        ];
    }

    // 英文技术讨论回复
    if (msg.includes('code') || msg.includes('function') || msg.includes('class') || msg.includes('method')) {
        return [
            'Let me review the code',
            'This function needs optimization',
            'Good implementation!',
            'We should refactor this part',
            'The logic looks correct',
            'Nice coding style',
            'This method is efficient'
        ];
    }

    // 项目进度和deadline回复
    if (msg.includes('deadline') || msg.includes('上线') || msg.includes('发布') || msg.includes('部署')) {
        return [
            '时间安排得过来吗？',
            '需要加人手吗？',
            '优先级怎么排？',
            '测试时间够吗？',
            '风险评估做了吗？',
            '准备工作都做好了',
            '按计划推进'
        ];
    }

    // 团队协作回复
    if (msg.includes('配合') || msg.includes('协作') || msg.includes('沟通') || msg.includes('对接')) {
        return [
            '我们密切配合',
            '有什么需要协调的？',
            '沟通很重要',
            '接口文档更新了吗？',
            '我们对一下进度',
            '团队协作很顺畅',
            '有问题随时沟通'
        ];
    }

    // 客户和需求回复
    if (msg.includes('客户') || msg.includes('需求') || msg.includes('反馈') || msg.includes('建议')) {
        return [
            '客户怎么说的？',
            '需求变更了吗？',
            '反馈很有价值',
            '我们评估一下可行性',
            '这个建议不错',
            '用户体验确实重要',
            '产品经理怎么看？'
        ];
    }

    // 问题求助
    if (msg.includes('问题') || msg.includes('帮助') || msg.includes('求助') || msg.includes('不会')) {
        return [
            '我来看看能不能帮到你',
            '具体是什么问题？',
            '可以详细描述一下吗？',
            '我之前遇到过类似的问题',
            '我们一起分析一下',
            'Stack Overflow上可能有答案',
            '可以先查看一下文档'
        ];
    }

    // 时间相关
    if (msg.includes('今天') || msg.includes('明天') || msg.includes('周末') || msg.includes('下班')) {
        return [
            '今天的任务基本完成了',
            '明天继续推进',
            '周末愉快！',
            '下班记得关电脑',
            '今天效率很高',
            '明天有个重要的deadline',
            '周末可以好好休息一下'
        ];
    }

    // 外国同事的回复
    if (Math.random() > 0.7) {
        return [
            'Sounds good!',
            'Great work!',
            'I agree with that.',
            'Let me check on this.',
            'Perfect timing!',
            'Thanks for the update.',
            'Looking forward to it!'
        ];
    }

    // 默认智能回复
    const defaultReplies = [
        '好的', '明白了', '收到', '没问题', '我看看',
        '正在处理', '稍等一下', '马上来', '了解', '同意',
        '这个想法不错', '我觉得可行', '需要再讨论一下',
        '让我想想', '有道理', '我赞同', '确实如此'
    ];

    return defaultReplies;
}

// 危险内容检测系统
function detectRiskContent(message) {
    const msg = message.toLowerCase();

    // 高风险关键词 - 立即触发警报
    const highRiskKeywords = [
        // 暴力威胁
        '杀', '死', '打死', '弄死', '干掉', '废了', '砍', '刺', '捅', '揍',
        '暴力', '威胁', '恐吓', '报复', '血', '刀', '枪', '炸', '爆',
        // 人身伤害
        '伤害', '打伤', '弄伤', '残废', '毁容', '下毒', '害人',
        // 极端情绪
        '想死', '自杀', '跳楼', '上吊', '割腕', '服毒', '轻生',
        // 严重冲突
        '打架', '斗殴', '群殴', '械斗', '冲突', '对抗',
        // 紧急求助
        '救命', '帮助', '报警', '急救', '受伤', '流血', '昏迷', '中毒',
        // 违法犯罪
        '犯罪', '违法', '诈骗', '盗窃', '抢劫', '绑架', '勒索',
        // 英文高风险词汇
        'kill', 'die', 'death', 'murder', 'violence', 'threat', 'hurt', 'harm',
        'suicide', 'help me', 'emergency', 'call police', 'bleeding', 'injured'
    ];

    // 中风险关键词 - 延迟触发警报
    const mediumRiskKeywords = [
        // 争吵冲突
        '吵架', '争吵', '争执', '矛盾', '不爽', '生气', '愤怒', '火大',
        '烦死了', '气死了', '讨厌', '恶心', '滚', '闭嘴', 'shut up',
        // 不文明用语
        '傻逼', '白痴', '蠢货', '垃圾', '废物', '混蛋', '王八蛋', '狗',
        '妈的', '操', '草', '靠', '艹', 'fuck', 'shit', 'damn', 'stupid',
        // 情绪激动
        '受不了', '忍不了', '疯了', '崩溃', '抓狂', '要疯', '气炸',
        // 投诉抱怨
        '投诉', '举报', '曝光', '差评', '退款', '骗子', '坑人', '黑心',
        // 订单争议
        '退货', '假货', '质量问题', '服务差', '态度恶劣', '欺骗', '坑钱'
    ];

    // 检测高风险内容
    for (let keyword of highRiskKeywords) {
        if (msg.includes(keyword)) {
            return 'high';
        }
    }

    // 检测中风险内容
    for (let keyword of mediumRiskKeywords) {
        if (msg.includes(keyword)) {
            return 'medium';
        }
    }

    // 检测连续大写字母（可能表示愤怒）
    if (/[A-Z]{5,}/.test(message)) {
        return 'medium';
    }

    // 检测过多感叹号（可能表示情绪激动）
    if ((message.match(/!/g) || []).length >= 3) {
        return 'medium';
    }

    return 'low';
}

// 触发安全警报
function triggerSafetyAlert(content, riskLevel) {
    const alertType = determineAlertType(content);

    // 添加系统警报消息
    addSystemAlert(content, riskLevel, alertType);

    // 如果是高风险，5秒后显示强制弹窗（测试用，正式环境改为60000）
    if (riskLevel === 'high') {
        console.log('高风险内容检测到，将在5秒后弹出紧急对话框');
        setTimeout(() => {
            console.log('正在显示紧急对话框');
            showEmergencyDialog(alertType);
        }, 5000); // 5秒后弹窗（测试用）
    }
}

// 判断警报类型
function determineAlertType(content) {
    const msg = content.toLowerCase();

    // 医疗紧急情况
    const medicalKeywords = ['受伤', '流血', '昏迷', '中毒', '急救', '救命', '心脏病', '过敏', 'injured', 'bleeding', 'emergency'];
    for (let keyword of medicalKeywords) {
        if (msg.includes(keyword)) {
            return 'medical';
        }
    }

    // 暴力冲突
    const violenceKeywords = ['打架', '斗殴', '威胁', '暴力', '冲突', '打死', '杀', 'violence', 'fight'];
    for (let keyword of violenceKeywords) {
        if (msg.includes(keyword)) {
            return 'violence';
        }
    }

    // 自杀倾向
    const suicideKeywords = ['自杀', '想死', '跳楼', '轻生', 'suicide', 'kill myself'];
    for (let keyword of suicideKeywords) {
        if (msg.includes(keyword)) {
            return 'suicide';
        }
    }

    // 订单争议
    const disputeKeywords = ['退款', '投诉', '举报', '骗子', '假货', '质量问题'];
    for (let keyword of disputeKeywords) {
        if (msg.includes(keyword)) {
            return 'dispute';
        }
    }

    return 'general';
}

// 添加系统警报消息
function addSystemAlert(content, riskLevel, alertType) {
    let alertMessage = '';
    let phoneNumber = '';

    switch (alertType) {
        case 'medical':
            alertMessage = '🚨 小助理检测到可能的医疗紧急情况，已向人工客服发送警报信息。如您遇到紧急医疗情况，请立即拨打急救电话：';
            phoneNumber = '120';
            break;
        case 'violence':
            alertMessage = '🚨 小助理检测到可能的暴力冲突情况，已向人工客服发送警报信息。如您遇到人身安全威胁，请立即拨打报警电话：';
            phoneNumber = '110';
            break;
        case 'suicide':
            alertMessage = '🚨 小助理检测到您可能遇到心理困扰，已向人工客服发送警报信息。如需心理援助，请拨打心理援助热线：************ 或报警电话：';
            phoneNumber = '110';
            break;
        case 'dispute':
            alertMessage = '⚠️ 小助理检测到订单争议问题，已向人工客服发送警报信息。如需投诉举报，请拨打消费者投诉热线：';
            phoneNumber = '12315';
            break;
        default:
            alertMessage = '⚠️ 小助理检测到异常情况，已向人工客服发送警报信息。如您遇到紧急情况，请拨打相应电话：';
            phoneNumber = '110（报警）/ 120（急救）';
    }

    // 添加系统消息到聊天界面
    addMessageToUI({
        sender_id: 0,
        username: '🤖 安全小助理',
        avatar: 'https://images.unsplash.com/photo-1485827404703-89b55fcc595e?w=150&h=150&fit=crop&crop=face',
        content: alertMessage + phoneNumber,
        created_at: new Date().toISOString()
    }, false);
}

// 显示紧急情况弹窗
function showEmergencyDialog(alertType) {
    let title = '';
    let message = '';

    switch (alertType) {
        case 'medical':
            title = '医疗紧急情况';
            message = '小助理似乎发现您可能遇到了医疗紧急情况，是否需要小助理帮您拨打急救电话120？';
            break;
        case 'violence':
            title = '安全威胁警报';
            message = '小助理似乎发现您可能遇到了安全威胁，是否需要小助理帮您报警处理？';
            break;
        case 'suicide':
            title = '心理援助';
            message = '小助理似乎发现您有不愉快的事情发生，是否需要小助理帮您联系心理援助热线或报警处理？';
            break;
        default:
            title = '紧急情况';
            message = '小助理似乎发现了您有不愉快的事情发生，是否需要小助理帮您报警处理？';
    }

    // 创建弹窗
    const dialog = document.createElement('div');
    dialog.className = 'emergency-dialog';
    dialog.innerHTML = `
        <div class="emergency-dialog-overlay">
            <div class="emergency-dialog-content">
                <div class="emergency-dialog-header">
                    <h3>${title}</h3>
                </div>
                <div class="emergency-dialog-body">
                    <p>${message}</p>
                </div>
                <div class="emergency-dialog-footer">
                    <button class="emergency-btn emergency-btn-yes" onclick="handleEmergencyResponse(true, '${alertType}')">是</button>
                    <button class="emergency-btn emergency-btn-no" onclick="handleEmergencyResponse(false, '${alertType}')">否</button>
                </div>
            </div>
        </div>
    `;

    document.body.appendChild(dialog);

    // 添加样式
    if (!document.getElementById('emergency-dialog-styles')) {
        const styles = document.createElement('style');
        styles.id = 'emergency-dialog-styles';
        styles.textContent = `
            .emergency-dialog {
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                z-index: 10000;
            }

            .emergency-dialog-overlay {
                position: absolute;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0, 0, 0, 0.8);
                display: flex;
                align-items: center;
                justify-content: center;
            }

            .emergency-dialog-content {
                background: white;
                border-radius: 12px;
                padding: 20px;
                margin: 20px;
                max-width: 400px;
                width: 90%;
                box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
            }

            .emergency-dialog-header h3 {
                margin: 0 0 15px 0;
                color: #ff4444;
                text-align: center;
                font-size: 18px;
            }

            .emergency-dialog-body p {
                margin: 0 0 20px 0;
                line-height: 1.5;
                color: #333;
                text-align: center;
            }

            .emergency-dialog-footer {
                display: flex;
                gap: 10px;
                justify-content: center;
            }

            .emergency-btn {
                padding: 10px 30px;
                border: none;
                border-radius: 6px;
                font-size: 16px;
                cursor: pointer;
                transition: all 0.3s;
            }

            .emergency-btn-yes {
                background: #ff4444;
                color: white;
            }

            .emergency-btn-yes:hover {
                background: #cc3333;
            }

            .emergency-btn-no {
                background: #666;
                color: white;
            }

            .emergency-btn-no:hover {
                background: #555;
            }
        `;
        document.head.appendChild(styles);
    }
}

// 处理紧急情况响应（全局函数）
window.handleEmergencyResponse = function(needHelp, alertType) {
    // 移除弹窗
    const dialog = document.querySelector('.emergency-dialog');
    if (dialog) {
        dialog.remove();
    }

    if (needHelp) {
        let helpMessage = '';
        switch (alertType) {
            case 'medical':
                helpMessage = '🚨 小助理正在为您联系急救服务，请保持冷静。急救电话：120';
                break;
            case 'violence':
                helpMessage = '🚨 小助理正在为您联系警方，请确保自身安全。报警电话：110';
                break;
            case 'suicide':
                helpMessage = '🚨 小助理正在为您联系心理援助服务。心理援助热线：************，紧急情况请拨打：110';
                break;
            default:
                helpMessage = '🚨 小助理正在为您联系相关服务，请保持冷静。紧急电话：110/120';
        }

        // 添加帮助消息
        addMessageToUI({
            sender_id: 0,
            username: '🤖 安全小助理',
            avatar: 'https://images.unsplash.com/photo-1485827404703-89b55fcc595e?w=150&h=150&fit=crop&crop=face',
            content: helpMessage,
            created_at: new Date().toISOString()
        }, false);

        // 模拟联系客服
        setTimeout(() => {
            addMessageToUI({
                sender_id: 0,
                username: '🤖 安全小助理',
                avatar: 'https://images.unsplash.com/photo-1485827404703-89b55fcc595e?w=150&h=150&fit=crop&crop=face',
                content: '✅ 已成功联系人工客服，客服将在1-3分钟内与您取得联系。请保持手机畅通。',
                created_at: new Date().toISOString()
            }, false);
        }, 2000);
    } else {
        // 用户选择不需要帮助
        addMessageToUI({
            sender_id: 0,
            username: '🤖 安全小助理',
            avatar: 'https://images.unsplash.com/photo-1485827404703-89b55fcc595e?w=150&h=150&fit=crop&crop=face',
            content: '好的，如果您之后需要帮助，随时可以联系小助理。祝您一切顺利！💙',
            created_at: new Date().toISOString()
        }, false);
    }
};

// 添加消息到UI
function addMessageToUI(message, isOwn) {
    const messageList = document.getElementById('messageList');
    const messageItem = document.createElement('div');
    messageItem.className = `message-item ${isOwn ? 'own' : 'other'}`;

    let avatarHtml = '';
    let senderHtml = '';

    if (!isOwn) {
        avatarHtml = `
            <div class="message-avatar">
                <img src="${message.avatar}" alt="头像">
            </div>
        `;

        if (window.chatData.chatType === 'group') {
            senderHtml = `<div class="message-sender">${message.username}</div>`;
        }
    }

    messageItem.innerHTML = `
        ${avatarHtml}
        <div class="message-content">
            ${senderHtml}
            <div class="message-bubble">
                ${message.content}
            </div>
            <div class="message-time">${formatTime(message.created_at)}</div>
        </div>
    `;

    messageList.appendChild(messageItem);
}

// 自动调整输入框高度
function autoResizeTextarea() {
    const textarea = document.getElementById('messageInput');

    textarea.addEventListener('input', function() {
        this.style.height = '36px';
        this.style.height = Math.min(this.scrollHeight, 100) + 'px';
    });
}

// 显示更多菜单
function showMoreMenu() {
    const moreMenu = document.getElementById('moreMenu');
    moreMenu.classList.add('show');
    document.body.style.overflow = 'hidden';
}

// 关闭更多菜单
function closeMoreMenu() {
    const moreMenu = document.getElementById('moreMenu');
    moreMenu.classList.remove('show');
    document.body.style.overflow = '';
}

// 返回上一页
function goBack() {
    window.history.back();
}

// 显示聊天菜单
function showChatMenu() {
    showToast('聊天设置功能开发中...');
}

// 选择图片
function selectImage() {
    closeMoreMenu();
    document.getElementById('imageInput').click();
}

// 选择视频聊天
function selectVideo() {
    closeMoreMenu();
    showToast('视频聊天功能开发中...');
}

// 选择语音聊天
function selectAudio() {
    closeMoreMenu();
    showToast('语音聊天功能开发中...');
}

// 选择文件
function selectFile() {
    closeMoreMenu();
    document.getElementById('fileInput').click();
}

// 处理图片选择
function handleImageSelect(event) {
    const file = event.target.files[0];
    if (file) {
        showToast('图片发送功能开发中...');
        // 这里应该上传图片并发送消息
    }
}

// 处理文件选择
function handleFileSelect(event) {
    const file = event.target.files[0];
    if (file) {
        showToast('文件发送功能开发中...');
        // 这里应该上传文件并发送消息
    }
}

// 格式化时间
function formatTime(timestamp) {
    const date = new Date(timestamp);
    return date.toLocaleTimeString('zh-CN', {
        hour: '2-digit',
        minute: '2-digit',
        hour12: false
    });
}

// Toast提示函数
function showToast(message) {
    const toast = document.getElementById('toast');
    toast.textContent = message;
    toast.style.display = 'block';
    toast.style.animation = 'fadeIn 0.3s forwards';

    setTimeout(() => {
        toast.style.animation = 'fadeOut 0.3s forwards';
        setTimeout(() => {
            toast.style.display = 'none';
        }, 300);
    }, 3000);
}

// 初始化自动回复（用于演示）
function initializeAutoReply() {
    // 如果是群聊，模拟其他成员的活跃度
    if (window.chatData.chatType === 'group') {
        // 随机发送一些消息来模拟群聊活跃度
        setTimeout(() => {
            const welcomeMessages = [
                '欢迎新同事！',
                '大家下午好！',
                '今天的任务都完成了吗？',
                '周末愉快！'
            ];

            // 随机选择是否发送欢迎消息
            if (Math.random() > 0.7) {
                const randomMessage = welcomeMessages[Math.floor(Math.random() * welcomeMessages.length)];
                simulateReply(randomMessage);
            }
        }, 3000);
    }
}

// 处理键盘事件
document.addEventListener('keydown', function(e) {
    if (e.key === 'Escape') {
        closeMoreMenu();
    }
});

// 处理页面可见性变化
document.addEventListener('visibilitychange', function() {
    if (document.visibilityState === 'visible') {
        // 页面变为可见时滚动到底部
        setTimeout(scrollToBottom, 100);
    }
});
