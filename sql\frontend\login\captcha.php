<?php
/**
 * 图片验证码生成器
 */
session_start();

// 生成4位数字验证码
$captcha_string = sprintf('%04d', mt_rand(1000, 9999));

// 保存验证码到会话
$_SESSION['captcha'] = $captcha_string;

// 创建图片
$width = 120;
$height = 40;
$image = imagecreate($width, $height);

// 设置颜色
$bg_color = imagecolorallocate($image, 240, 255, 255); // 浅蓝色背景
$text_color = imagecolorallocate($image, 64, 224, 208); // 主题色文字
$line_color = imagecolorallocate($image, 175, 251, 242); // 干扰线颜色

// 填充背景
imagefill($image, 0, 0, $bg_color);

// 添加干扰线
for ($i = 0; $i < 5; $i++) {
    imageline($image,
        mt_rand(0, $width), mt_rand(0, $height),
        mt_rand(0, $width), mt_rand(0, $height),
        $line_color
    );
}

// 添加干扰点
for ($i = 0; $i < 50; $i++) {
    imagesetpixel($image, mt_rand(0, $width), mt_rand(0, $height), $line_color);
}

// 写入验证码文字
$font_size = 16;
for ($i = 0; $i < 4; $i++) {
    $x = 20 + $i * 20;
    $y = 25;
    $angle = mt_rand(-15, 15);
    $char = $captcha_string[$i];

    // 使用内置字体
    imagestring($image, 5, $x, $y - 10, $char, $text_color);
}

// 输出图片
header('Content-Type: image/png');
header('Cache-Control: no-cache, no-store, must-revalidate');
header('Pragma: no-cache');
header('Expires: 0');

imagepng($image);
imagedestroy($image);
