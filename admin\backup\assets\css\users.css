/**
 * 用户管理页面样式
 * 趣玩星球管理后台 v2.0
 */

/* ===== 用户管理容器 ===== */
.user-management-container {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xl);
}

/* ===== 搜索区域 ===== */
.search-section {
    background: white;
    border-radius: var(--radius-2xl);
    padding: var(--spacing-xl);
    box-shadow: var(--shadow-lg);
    border: 1px solid var(--gray-200);
}

.search-header {
    margin-bottom: var(--spacing-lg);
}

.search-title h2 {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--gray-800);
    margin-bottom: var(--spacing-xs);
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.search-title h2 i {
    color: var(--primary-color);
}

.search-title p {
    color: var(--gray-600);
    font-size: 0.875rem;
    margin: 0;
}

.search-form-container {
    background: var(--gray-50);
    border-radius: var(--radius-xl);
    padding: var(--spacing-lg);
}

.advanced-search-form {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-lg);
}

.search-row {
    display: grid;
    grid-template-columns: 1fr 200px auto;
    gap: var(--spacing-lg);
    align-items: end;
}

.search-field {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xs);
}

.search-field label {
    font-size: 0.875rem;
    font-weight: 600;
    color: var(--gray-700);
}

.input-with-icon {
    position: relative;
}

.input-with-icon i {
    position: absolute;
    left: var(--spacing-md);
    top: 50%;
    transform: translateY(-50%);
    color: var(--gray-400);
    font-size: 0.875rem;
}

.input-with-icon input {
    padding-left: 2.5rem;
}

.search-field input,
.search-field select {
    padding: var(--spacing-sm) var(--spacing-md);
    border: 2px solid var(--gray-200);
    border-radius: var(--radius-lg);
    font-size: 0.875rem;
    background: white;
    transition: all 0.3s ease;
    width: 100%;
}

.search-field input:focus,
.search-field select:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(64, 224, 208, 0.1);
}

.field-hint {
    font-size: 0.75rem;
    color: var(--gray-500);
    margin-top: var(--spacing-xs);
}

.search-actions {
    display: flex;
    gap: var(--spacing-sm);
}

.btn-search,
.btn-reset {
    padding: var(--spacing-sm) var(--spacing-lg);
    border: none;
    border-radius: var(--radius-lg);
    font-size: 0.875rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    white-space: nowrap;
}

.btn-search {
    background: var(--gradient-primary);
    color: white;
}

.btn-search:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.btn-reset {
    background: var(--gray-200);
    color: var(--gray-700);
}

.btn-reset:hover {
    background: var(--gray-300);
}

/* ===== 搜索结果区域 ===== */
.results-section {
    background: white;
    border-radius: var(--radius-2xl);
    padding: var(--spacing-xl);
    box-shadow: var(--shadow-lg);
    border: 1px solid var(--gray-200);
}

.results-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-lg);
    padding-bottom: var(--spacing-md);
    border-bottom: 2px solid var(--gray-100);
}

.results-header h3 {
    font-size: 1.25rem;
    font-weight: 700;
    color: var(--gray-800);
    margin: 0;
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.results-header h3 i {
    color: var(--success-color);
}

.results-info {
    font-size: 0.875rem;
    color: var(--gray-600);
}

.results-info strong {
    color: var(--primary-color);
    font-weight: 700;
}

/* ===== 用户表格 ===== */
.table-container {
    overflow-x: auto;
    border-radius: var(--radius-xl);
    border: 1px solid var(--gray-200);
}

.user-table {
    width: 100%;
    border-collapse: collapse;
    background: white;
}

.user-table th {
    background: var(--gray-50);
    padding: var(--spacing-md) var(--spacing-lg);
    text-align: left;
    font-size: 0.875rem;
    font-weight: 600;
    color: var(--gray-700);
    border-bottom: 2px solid var(--gray-200);
}

.user-table td {
    padding: var(--spacing-md) var(--spacing-lg);
    border-bottom: 1px solid var(--gray-100);
    vertical-align: middle;
}

.user-table tr:hover {
    background: rgba(64, 224, 208, 0.02);
}

.user-info {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.user-avatar {
    width: 40px;
    height: 40px;
    border-radius: var(--radius-lg);
    object-fit: cover;
    border: 2px solid var(--gray-200);
}

.user-details {
    display: flex;
    flex-direction: column;
}

.user-name {
    font-size: 0.875rem;
    font-weight: 600;
    color: var(--gray-800);
    margin-bottom: 2px;
}

.user-id {
    font-size: 0.75rem;
    color: var(--gray-500);
}

.quwanplanet-id {
    font-family: var(--font-mono);
    font-size: 0.875rem;
    font-weight: 600;
    color: var(--primary-color);
}

.contact-info {
    display: flex;
    flex-direction: column;
    gap: 2px;
}

.contact-info div {
    font-size: 0.875rem;
    color: var(--gray-800);
}

.contact-info small {
    font-size: 0.75rem;
    color: var(--gray-500);
}

/* ===== 状态徽章 ===== */
.badge {
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-xs);
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--radius-md);
    font-size: 0.75rem;
    font-weight: 600;
    white-space: nowrap;
}

.badge.success {
    background: rgba(16, 185, 129, 0.1);
    color: var(--success-color);
}

.badge.warning {
    background: rgba(245, 158, 11, 0.1);
    color: var(--warning-color);
}

.badge.danger {
    background: rgba(239, 68, 68, 0.1);
    color: var(--error-color);
}

.badge.secondary {
    background: var(--gray-100);
    color: var(--gray-600);
}

/* ===== 操作按钮 ===== */
.btn-sm {
    padding: var(--spacing-xs) var(--spacing-sm);
    border: none;
    border-radius: var(--radius-md);
    font-size: 0.75rem;
    font-weight: 500;
    text-decoration: none;
    cursor: pointer;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-xs);
}

.btn-sm.primary {
    background: var(--gradient-primary);
    color: white;
}

.btn-sm.primary:hover {
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
    text-decoration: none;
    color: white;
}

/* ===== 分页 ===== */
.pagination {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: var(--spacing-lg);
    padding-top: var(--spacing-lg);
    border-top: 1px solid var(--gray-200);
}

.pagination-info {
    font-size: 0.875rem;
    color: var(--gray-600);
}

.pagination-links {
    display: flex;
    gap: var(--spacing-xs);
}

.page-link {
    padding: var(--spacing-xs) var(--spacing-sm);
    border: 1px solid var(--gray-200);
    border-radius: var(--radius-md);
    text-decoration: none;
    color: var(--gray-700);
    font-size: 0.875rem;
    transition: all 0.3s ease;
}

.page-link:hover {
    background: var(--primary-color);
    color: white;
    text-decoration: none;
}

.page-link.active {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

/* ===== 无结果提示 ===== */
.no-results-section {
    background: white;
    border-radius: var(--radius-2xl);
    padding: var(--spacing-2xl);
    box-shadow: var(--shadow-lg);
    border: 1px solid var(--gray-200);
    text-align: center;
}

.no-results-content {
    max-width: 500px;
    margin: 0 auto;
}

.no-results-content i {
    font-size: 4rem;
    color: var(--gray-300);
    margin-bottom: var(--spacing-lg);
}

.no-results-content h3 {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--gray-800);
    margin-bottom: var(--spacing-sm);
}

.no-results-content p {
    color: var(--gray-600);
    margin-bottom: var(--spacing-lg);
}

.search-tips {
    text-align: left;
    background: var(--gray-50);
    border-radius: var(--radius-lg);
    padding: var(--spacing-lg);
}

.search-tips h4 {
    font-size: 1rem;
    font-weight: 600;
    color: var(--gray-800);
    margin-bottom: var(--spacing-sm);
}

.search-tips ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.search-tips li {
    padding: var(--spacing-xs) 0;
    color: var(--gray-600);
    font-size: 0.875rem;
    position: relative;
    padding-left: var(--spacing-lg);
}

.search-tips li::before {
    content: '•';
    color: var(--primary-color);
    font-weight: bold;
    position: absolute;
    left: 0;
}

/* ===== 欢迎区域 ===== */
.welcome-section {
    background: white;
    border-radius: var(--radius-2xl);
    padding: var(--spacing-2xl);
    box-shadow: var(--shadow-lg);
    border: 1px solid var(--gray-200);
    text-align: center;
}

.welcome-content {
    max-width: 600px;
    margin: 0 auto;
}

.welcome-icon {
    width: 80px;
    height: 80px;
    background: var(--gradient-primary);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto var(--spacing-lg);
    font-size: 2rem;
    color: white;
}

.welcome-content h3 {
    font-size: 1.75rem;
    font-weight: 700;
    color: var(--gray-800);
    margin-bottom: var(--spacing-sm);
}

.welcome-content p {
    color: var(--gray-600);
    margin-bottom: var(--spacing-xl);
    font-size: 1.125rem;
}

.privacy-features {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: var(--spacing-lg);
    margin-bottom: var(--spacing-xl);
}

.feature-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-lg);
    background: var(--gray-50);
    border-radius: var(--radius-xl);
    transition: all 0.3s ease;
}

.feature-item:hover {
    background: rgba(64, 224, 208, 0.05);
    transform: translateY(-2px);
}

.feature-item i {
    font-size: 1.5rem;
    color: var(--primary-color);
}

.feature-item span {
    font-size: 0.875rem;
    font-weight: 500;
    color: var(--gray-700);
}

.search-guide {
    text-align: left;
    background: var(--gray-50);
    border-radius: var(--radius-xl);
    padding: var(--spacing-xl);
}

.search-guide h4 {
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--gray-800);
    margin-bottom: var(--spacing-md);
}

.search-guide ol {
    padding-left: var(--spacing-lg);
    margin: 0;
}

.search-guide li {
    padding: var(--spacing-xs) 0;
    color: var(--gray-600);
    font-size: 0.875rem;
    line-height: 1.5;
}

/* ===== 响应式设计 ===== */
@media (max-width: 768px) {
    .search-row {
        grid-template-columns: 1fr;
        gap: var(--spacing-md);
    }

    .search-actions {
        justify-content: stretch;
    }

    .btn-search,
    .btn-reset {
        flex: 1;
        justify-content: center;
    }

    .results-header {
        flex-direction: column;
        align-items: flex-start;
        gap: var(--spacing-sm);
    }

    .pagination {
        flex-direction: column;
        gap: var(--spacing-md);
    }

    .privacy-features {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 480px) {
    .user-management-container {
        gap: var(--spacing-lg);
    }

    .search-section,
    .results-section,
    .welcome-section,
    .no-results-section {
        padding: var(--spacing-lg);
    }

    .privacy-features {
        grid-template-columns: 1fr;
    }

    .user-table {
        font-size: 0.75rem;
    }

    .user-table th,
    .user-table td {
        padding: var(--spacing-sm);
    }

    .event-details {
        flex-direction: column;
        gap: 4px;
    }
}

/* ===== 露营活动相关样式 ===== */
.event-category {
    font-size: 0.75rem;
    color: var(--primary-color);
    background: rgba(64, 224, 208, 0.1);
    padding: 2px 8px;
    border-radius: var(--radius-sm);
    margin-bottom: var(--spacing-xs);
    display: inline-block;
    font-weight: 500;
}

.event-details {
    display: flex;
    flex-wrap: wrap;
    gap: var(--spacing-sm);
    font-size: 0.75rem;
    color: var(--gray-600);
}

.event-details span {
    display: flex;
    align-items: center;
    gap: 4px;
}

.event-details i {
    color: var(--gray-400);
}

.badge.primary {
    background: rgba(59, 130, 246, 0.1);
    color: #3B82F6;
}

.badge.info {
    background: rgba(59, 130, 246, 0.1);
    color: #3B82F6;
}

/* ===== 优惠券相关样式 ===== */
.coupon-stats {
    display: flex;
    gap: var(--spacing-lg);
    margin-bottom: var(--spacing-lg);
    padding: var(--spacing-lg);
    background: var(--gray-50);
    border-radius: var(--radius-lg);
}

.stat-item {
    text-align: center;
    flex: 1;
}

.stat-number {
    font-size: 2rem;
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: var(--spacing-xs);
}

.stat-label {
    font-size: 0.875rem;
    color: var(--gray-600);
    font-weight: 500;
}

.coupons-list {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
}

.coupon-item {
    display: flex;
    align-items: center;
    padding: var(--spacing-md);
    border: 2px solid var(--gray-200);
    border-radius: var(--radius-lg);
    background: white;
    transition: all 0.3s ease;
}

.coupon-item.available {
    border-color: var(--success-color);
    background: rgba(16, 185, 129, 0.02);
}

.coupon-item.used {
    border-color: var(--gray-300);
    background: var(--gray-50);
    opacity: 0.8;
}

.coupon-item.expired {
    border-color: var(--error-color);
    background: rgba(239, 68, 68, 0.02);
}

.coupon-content {
    flex: 1;
}

.coupon-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-sm);
}

.coupon-title {
    font-size: 1rem;
    font-weight: 600;
    color: var(--gray-800);
}

.coupon-amount {
    font-size: 1.25rem;
    font-weight: 700;
    color: var(--primary-color);
}

.coupon-details {
    display: flex;
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-sm);
}

.coupon-type,
.coupon-condition {
    font-size: 0.75rem;
    padding: 2px 8px;
    border-radius: var(--radius-sm);
    background: var(--gray-100);
    color: var(--gray-600);
}

.coupon-dates {
    display: flex;
    flex-direction: column;
    gap: 2px;
    font-size: 0.75rem;
    color: var(--gray-500);
}

.coupon-dates span {
    display: flex;
    align-items: center;
    gap: 4px;
}

.coupon-dates i {
    width: 12px;
    color: var(--gray-400);
}

.coupon-status {
    margin-left: var(--spacing-md);
}

.status-badge {
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--radius-md);
    font-size: 0.75rem;
    font-weight: 600;
    white-space: nowrap;
}

.status-badge.available {
    background: rgba(16, 185, 129, 0.1);
    color: var(--success-color);
}

.status-badge.used {
    background: var(--gray-100);
    color: var(--gray-600);
}

.status-badge.expired {
    background: rgba(239, 68, 68, 0.1);
    color: var(--error-color);
}
