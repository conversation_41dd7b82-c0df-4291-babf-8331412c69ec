-- 修复设备和IP信息功能的数据库脚本
-- 趣玩星球管理后台

-- 1. 创建login_logs表（如果不存在）
CREATE TABLE IF NOT EXISTS `login_logs` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `login_time` datetime NOT NULL,
  `ip_address` varchar(45) NOT NULL,
  `user_agent` text,
  `status` enum('success','failed') NOT NULL DEFAULT 'success',
  `device_fingerprint` varchar(32) DEFAULT NULL,
  `login_type` enum('quick_login','secure_login','normal_login') DEFAULT 'normal_login',
  `location` varchar(100) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_login_time` (`login_time`),
  KEY `idx_ip_address` (`ip_address`),
  KEY `idx_device_fingerprint` (`device_fingerprint`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 2. 检查是否有数据，如果没有则添加测试数据
-- 为前5个用户添加测试数据

-- 用户1的登录记录（正常使用模式）
INSERT IGNORE INTO login_logs (user_id, login_time, ip_address, user_agent, status, login_type, location) VALUES
(1, DATE_SUB(NOW(), INTERVAL 1 HOUR), '*************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36', 'success', 'normal_login', '北京市'),
(1, DATE_SUB(NOW(), INTERVAL 2 HOURS), '*************', 'Mozilla/5.0 (iPhone; CPU iPhone OS 17_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.0 Mobile/15E148 Safari/604.1', 'success', 'quick_login', '北京市'),
(1, DATE_SUB(NOW(), INTERVAL 1 DAY), '**************', 'Mozilla/5.0 (Linux; Android 13; SM-G991B) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Mobile Safari/537.36', 'success', 'normal_login', '上海市'),
(1, DATE_SUB(NOW(), INTERVAL 2 DAYS), '********', 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36', 'success', 'normal_login', '内网IP');

-- 用户2的登录记录（稳定IP使用）
INSERT IGNORE INTO login_logs (user_id, login_time, ip_address, user_agent, status, login_type, location) VALUES
(2, DATE_SUB(NOW(), INTERVAL 30 MINUTES), '*************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36', 'success', 'normal_login', '北京市'),
(2, DATE_SUB(NOW(), INTERVAL 1 HOUR), '*************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36', 'success', 'quick_login', '北京市'),
(2, DATE_SUB(NOW(), INTERVAL 6 HOURS), '*************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36', 'success', 'normal_login', '北京市');

-- 用户3的登录记录（移动设备为主）
INSERT IGNORE INTO login_logs (user_id, login_time, ip_address, user_agent, status, login_type, location) VALUES
(3, DATE_SUB(NOW(), INTERVAL 15 MINUTES), '**************', 'Mozilla/5.0 (iPhone; CPU iPhone OS 17_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.0 Mobile/15E148 Safari/604.1', 'success', 'quick_login', '上海市'),
(3, DATE_SUB(NOW(), INTERVAL 2 HOURS), '**************', 'Mozilla/5.0 (Linux; Android 13; SM-G991B) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Mobile Safari/537.36', 'success', 'normal_login', '上海市'),
(3, DATE_SUB(NOW(), INTERVAL 1 DAY), '***************', 'Mozilla/5.0 (iPad; CPU OS 17_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.0 Mobile/15E148 Safari/604.1', 'success', 'normal_login', '广东省');

-- 用户4的登录记录（多地区登录）
INSERT IGNORE INTO login_logs (user_id, login_time, ip_address, user_agent, status, login_type, location) VALUES
(4, DATE_SUB(NOW(), INTERVAL 1 HOUR), '**************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36', 'success', 'normal_login', '上海市'),
(4, DATE_SUB(NOW(), INTERVAL 1 DAY), '***************', 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36', 'success', 'normal_login', '广东省'),
(4, DATE_SUB(NOW(), INTERVAL 2 DAYS), '*************', 'Mozilla/5.0 (iPhone; CPU iPhone OS 17_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.0 Mobile/15E148 Safari/604.1', 'success', 'quick_login', '江苏省'),
(4, DATE_SUB(NOW(), INTERVAL 3 DAYS), '**************', 'Mozilla/5.0 (Linux; Android 13; SM-G991B) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Mobile Safari/537.36', 'success', 'normal_login', '浙江省');

-- 用户5的登录记录（高风险用户 - 多IP多地区）
INSERT IGNORE INTO login_logs (user_id, login_time, ip_address, user_agent, status, login_type, location) VALUES
(5, DATE_SUB(NOW(), INTERVAL 30 MINUTES), '**************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36', 'success', 'normal_login', '上海市'),
(5, DATE_SUB(NOW(), INTERVAL 2 HOURS), '***************', 'Mozilla/5.0 (iPhone; CPU iPhone OS 17_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.0 Mobile/15E148 Safari/604.1', 'success', 'quick_login', '广东省'),
(5, DATE_SUB(NOW(), INTERVAL 4 HOURS), '*************', 'Mozilla/5.0 (Linux; Android 13; SM-G991B) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Mobile Safari/537.36', 'success', 'normal_login', '江苏省'),
(5, DATE_SUB(NOW(), INTERVAL 6 HOURS), '**************', 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36', 'success', 'normal_login', '浙江省'),
(5, DATE_SUB(NOW(), INTERVAL 1 DAY), '**************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) Gecko/20100101 Firefox/119.0', 'success', 'normal_login', '北京市'),
(5, DATE_SUB(NOW(), INTERVAL 2 DAYS), '*************', 'Mozilla/5.0 (iPad; CPU OS 17_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.0 Mobile/15E148 Safari/604.1', 'success', 'quick_login', '湖北省'),
(5, DATE_SUB(NOW(), INTERVAL 3 DAYS), '**************', 'Mozilla/5.0 (Linux; Android 13; SM-G991B) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Mobile Safari/537.36', 'success', 'normal_login', '四川省');

-- 3. 验证数据插入
SELECT 
    user_id,
    COUNT(*) as login_count,
    COUNT(DISTINCT ip_address) as unique_ips,
    COUNT(DISTINCT location) as unique_locations,
    MIN(login_time) as first_login,
    MAX(login_time) as last_login
FROM login_logs 
WHERE user_id IN (1,2,3,4,5)
GROUP BY user_id
ORDER BY user_id;

-- 4. 显示各用户的设备类型统计
SELECT 
    user_id,
    CASE 
        WHEN user_agent LIKE '%iPhone%' THEN 'iPhone'
        WHEN user_agent LIKE '%iPad%' THEN 'iPad'
        WHEN user_agent LIKE '%Android%' AND user_agent LIKE '%Mobile%' THEN 'Android手机'
        WHEN user_agent LIKE '%Android%' THEN 'Android平板'
        WHEN user_agent LIKE '%Windows%' THEN 'Windows电脑'
        WHEN user_agent LIKE '%Mac%' THEN 'Mac电脑'
        ELSE '其他设备'
    END as device_type,
    COUNT(*) as count
FROM login_logs 
WHERE user_id IN (1,2,3,4,5)
GROUP BY user_id, device_type
ORDER BY user_id, count DESC;
