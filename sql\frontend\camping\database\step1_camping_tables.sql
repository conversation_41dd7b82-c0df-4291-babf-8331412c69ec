-- 第一步：创建露营活动相关数据库表
-- 请在宝塔数据库中逐步执行以下SQL语句

-- 1. 露营活动表
CREATE TABLE IF NOT EXISTS `camping_activities` (
    `id` INT(11) NOT NULL AUTO_INCREMENT COMMENT '活动ID',
    `title` VARCHAR(200) NOT NULL COMMENT '活动标题',
    `description` TEXT COMMENT '活动描述',
    `organizer_id` INT(11) NOT NULL COMMENT '组局者用户ID',
    `location` VARCHAR(200) NOT NULL COMMENT '活动地点',
    `start_date` DATETIME NOT NULL COMMENT '活动开始时间',
    `end_date` DATETIME NOT NULL COMMENT '活动结束时间',
    `max_participants` INT(11) NOT NULL DEFAULT 20 COMMENT '最大参与人数',
    `current_participants` INT(11) NOT NULL DEFAULT 0 COMMENT '当前参与人数',
    `price` DECIMAL(10,2) NOT NULL DEFAULT 0.00 COMMENT '活动费用',
    `original_price` DECIMAL(10,2) DEFAULT NULL COMMENT '原价（用于显示优惠）',
    `category` ENUM('mountain', 'lake', 'forest', 'beach') NOT NULL DEFAULT 'mountain' COMMENT '活动分类',
    `features` TEXT COMMENT '活动特色标签（逗号分隔）',
    `image_url` VARCHAR(500) COMMENT '活动主图',
    `status` ENUM('draft', 'recruiting', 'full', 'ongoing', 'completed', 'cancelled') NOT NULL DEFAULT 'recruiting' COMMENT '活动状态',
    `rules_accepted` TINYINT(1) NOT NULL DEFAULT 0 COMMENT '是否需要接受活动规则',
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    KEY `idx_organizer` (`organizer_id`),
    KEY `idx_category` (`category`),
    KEY `idx_status` (`status`),
    KEY `idx_start_date` (`start_date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='露营活动表';
