<?php
// 获取智能客服配置API
header('Content-Type: application/json; charset=UTF-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// 处理预检请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

// 引用数据库配置文件
require_once '../../../houtai_backup/db_config.php';

try {
    $pdo = getDbConnection();
    
    // 获取活跃的机器人配置
    $stmt = $pdo->query("
        SELECT * FROM customer_service_bot_config 
        WHERE status = 'active' 
        ORDER BY id DESC 
        LIMIT 1
    ");
    
    $botConfig = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($botConfig) {
        // 返回机器人配置
        echo json_encode([
            'success' => true,
            'data' => [
                'name' => $botConfig['name'],
                'avatar' => $botConfig['avatar'],
                'welcome_message' => $botConfig['welcome_message'],
                'default_reply' => $botConfig['default_reply'],
                'auto_reply_enabled' => (bool)$botConfig['auto_reply_enabled'],
                'transfer_threshold' => (int)$botConfig['transfer_threshold'],
                'working_hours_start' => $botConfig['working_hours_start'],
                'working_hours_end' => $botConfig['working_hours_end']
            ]
        ]);
    } else {
        // 返回默认配置
        echo json_encode([
            'success' => true,
            'data' => [
                'name' => '趣玩小助手',
                'avatar' => null,
                'welcome_message' => '您好！欢迎来到趣玩星球，我是您的专属客服小助手，有什么可以帮助您的吗？',
                'default_reply' => '抱歉，我暂时无法理解您的问题，正在为您转接人工客服，请稍候...',
                'auto_reply_enabled' => true,
                'transfer_threshold' => 3,
                'working_hours_start' => '09:00:00',
                'working_hours_end' => '18:00:00'
            ]
        ]);
    }
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => '获取机器人配置失败',
        'message' => $e->getMessage()
    ]);
}
?>
