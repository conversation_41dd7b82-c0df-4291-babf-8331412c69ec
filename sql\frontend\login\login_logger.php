<?php
/**
 * 登录日志记录器
 * 统一处理所有登录相关的日志记录
 */

/**
 * 记录用户登录日志
 * @param PDO $pdo 数据库连接
 * @param int $userId 用户ID
 * @param string $loginType 登录类型 (quick_login, secure_login, normal_login)
 * @param string $status 登录状态 (success, failed)
 * @return bool 是否记录成功
 */
function recordUserLoginLog($pdo, $userId, $loginType = 'normal_login', $status = 'success') {
    try {
        // 获取用户IP地址
        $ip_address = getUserRealIP();

        // 获取User Agent
        $user_agent = $_SERVER['HTTP_USER_AGENT'] ?? '';

        // 获取地理位置
        $location = getLocationFromIP($ip_address);

        // 确保login_logs表存在
        ensureLoginLogsTableExists($pdo);

        // 插入登录记录
        $stmt = $pdo->prepare("
            INSERT INTO login_logs (
                user_id, login_time, ip_address, user_agent, status, login_type, location, created_at
            ) VALUES (?, NOW(), ?, ?, ?, ?, ?, NOW())
        ");

        $result = $stmt->execute([
            $userId,
            $ip_address,
            $user_agent,
            $status,
            $loginType,
            $location
        ]);

        if ($result) {
            error_log("成功记录用户 {$userId} 的登录日志 - IP: {$ip_address}, 类型: {$loginType}, 状态: {$status}");
            return true;
        } else {
            error_log("记录用户 {$userId} 的登录日志失败 - SQL执行失败");
            return false;
        }
    } catch (PDOException $e) {
        error_log("记录登录日志数据库错误: " . $e->getMessage() . " - 用户ID: {$userId}");
        return false;
    } catch (Exception $e) {
        error_log("记录登录日志失败: " . $e->getMessage() . " - 用户ID: {$userId}");
        return false;
    }
}

/**
 * 获取用户真实IP地址
 */
function getUserRealIP() {
    // 检查各种可能的IP头
    $ip_headers = [
        'HTTP_X_FORWARDED_FOR',
        'HTTP_X_REAL_IP',
        'HTTP_CLIENT_IP',
        'HTTP_X_FORWARDED',
        'HTTP_FORWARDED_FOR',
        'HTTP_FORWARDED',
        'REMOTE_ADDR'
    ];

    foreach ($ip_headers as $header) {
        if (!empty($_SERVER[$header])) {
            $ip = $_SERVER[$header];

            // 如果有多个IP，取第一个
            if (strpos($ip, ',') !== false) {
                $ip = trim(explode(',', $ip)[0]);
            }

            // 验证IP格式
            if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE)) {
                return $ip;
            }
        }
    }

    // 如果没有找到有效的公网IP，返回REMOTE_ADDR
    return $_SERVER['REMOTE_ADDR'] ?? 'unknown';
}

/**
 * 解析User Agent获取详细设备信息（支持微信浏览器）
 */
function parseUserAgent($userAgent) {
    $device = 'Unknown';
    $os = 'Unknown';
    $browser = 'Unknown';
    $isWeChat = false;

    if (empty($userAgent)) {
        return ['device' => $device, 'os' => $os, 'browser' => $browser, 'is_wechat' => $isWeChat];
    }

    // 检测微信浏览器
    if (preg_match('/MicroMessenger/i', $userAgent)) {
        $isWeChat = true;
        $browser = '微信浏览器';

        // 微信浏览器中的设备检测
        if (preg_match('/iPhone/i', $userAgent)) {
            $device = 'iPhone';
            $os = 'iOS';
        } elseif (preg_match('/iPad/i', $userAgent)) {
            $device = 'iPad';
            $os = 'iOS';
        } elseif (preg_match('/Android/i', $userAgent)) {
            $device = 'Android手机';
            $os = 'Android';
        } elseif (preg_match('/Windows/i', $userAgent)) {
            $device = '微信PC版';
            $os = 'Windows';
        } elseif (preg_match('/Mac/i', $userAgent)) {
            $device = '微信Mac版';
            $os = 'macOS';
        } else {
            $device = '微信设备';
        }
    } else {
        // 非微信浏览器的检测

        // 检测操作系统
        if (preg_match('/Windows NT 10/i', $userAgent)) {
            $os = 'Windows 10';
        } elseif (preg_match('/Windows NT 6.3/i', $userAgent)) {
            $os = 'Windows 8.1';
        } elseif (preg_match('/Windows NT 6.1/i', $userAgent)) {
            $os = 'Windows 7';
        } elseif (preg_match('/Windows/i', $userAgent)) {
            $os = 'Windows';
        } elseif (preg_match('/Mac OS X 10[._](\d+)/i', $userAgent, $matches)) {
            $version = $matches[1];
            $os = 'macOS 10.' . $version;
        } elseif (preg_match('/Mac OS X/i', $userAgent)) {
            $os = 'macOS';
        } elseif (preg_match('/iPhone OS (\d+)[._](\d+)/i', $userAgent, $matches)) {
            $os = 'iOS ' . $matches[1] . '.' . $matches[2];
        } elseif (preg_match('/iPhone/i', $userAgent)) {
            $os = 'iOS';
        } elseif (preg_match('/Android (\d+\.?\d*)/i', $userAgent, $matches)) {
            $os = 'Android ' . $matches[1];
        } elseif (preg_match('/Android/i', $userAgent)) {
            $os = 'Android';
        } elseif (preg_match('/Linux/i', $userAgent)) {
            $os = 'Linux';
        }

        // 检测设备类型
        if (preg_match('/iPhone/i', $userAgent)) {
            $device = 'iPhone';
        } elseif (preg_match('/iPad/i', $userAgent)) {
            $device = 'iPad';
        } elseif (preg_match('/Android/i', $userAgent)) {
            if (preg_match('/Mobile/i', $userAgent)) {
                $device = 'Android手机';
            } else {
                $device = 'Android平板';
            }
        } elseif (preg_match('/Mobile|BlackBerry|Windows Phone/i', $userAgent)) {
            $device = '移动设备';
        } else {
            $device = '桌面设备';
        }

        // 检测浏览器
        if (preg_match('/Edg\//i', $userAgent)) {
            $browser = 'Microsoft Edge';
        } elseif (preg_match('/Chrome\/(\d+)/i', $userAgent, $matches)) {
            $browser = 'Chrome ' . $matches[1];
        } elseif (preg_match('/Firefox\/(\d+)/i', $userAgent, $matches)) {
            $browser = 'Firefox ' . $matches[1];
        } elseif (preg_match('/Safari\/(\d+)/i', $userAgent, $matches) && !preg_match('/Chrome/i', $userAgent)) {
            $browser = 'Safari';
        } elseif (preg_match('/Opera\/(\d+)/i', $userAgent, $matches)) {
            $browser = 'Opera ' . $matches[1];
        } elseif (preg_match('/MSIE (\d+)/i', $userAgent, $matches)) {
            $browser = 'IE ' . $matches[1];
        } elseif (preg_match('/Trident.*rv:(\d+)/i', $userAgent, $matches)) {
            $browser = 'IE ' . $matches[1];
        }
    }

    return [
        'device' => $device,
        'os' => $os,
        'browser' => $browser,
        'is_wechat' => $isWeChat
    ];
}

/**
 * 根据IP地址获取地理位置（使用免费IP查询API）
 */
function getLocationFromIP($ip) {
    // 检查是否为内网IP
    if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE) === false) {
        return '内网IP';
    }

    // 检查IP格式
    if (!filter_var($ip, FILTER_VALIDATE_IP)) {
        return '无效IP';
    }

    // 尝试使用免费的IP查询API
    $location = getLocationFromAPI($ip);
    if ($location !== '未知地区') {
        return $location;
    }

    // 如果API失败，使用本地IP段判断
    return getLocationFromIPRange($ip);
}

/**
 * 使用API获取IP地理位置
 */
function getLocationFromAPI($ip) {
    try {
        // 使用免费的IP查询API（ip-api.com）
        $url = "http://ip-api.com/json/{$ip}?lang=zh-CN&fields=status,country,regionName,city";

        // 设置超时时间
        $context = stream_context_create([
            'http' => [
                'timeout' => 3, // 3秒超时
                'method' => 'GET',
                'header' => 'User-Agent: Mozilla/5.0 (compatible; LocationBot/1.0)'
            ]
        ]);

        $response = @file_get_contents($url, false, $context);

        if ($response === false) {
            return '未知地区';
        }

        $data = json_decode($response, true);

        if ($data && $data['status'] === 'success') {
            $location = '';

            // 构建地理位置字符串
            if (!empty($data['country']) && $data['country'] !== 'China') {
                $location = $data['country'];
            } else {
                // 中国境内的详细地址
                if (!empty($data['regionName'])) {
                    $location = $data['regionName'];

                    // 如果有城市信息且与省份不同，添加城市
                    if (!empty($data['city']) && $data['city'] !== $data['regionName']) {
                        $location .= ' ' . $data['city'];
                    }
                }
            }

            return !empty($location) ? $location : '中国';
        }

        return '未知地区';

    } catch (Exception $e) {
        error_log("IP地理位置查询失败: " . $e->getMessage());
        return '未知地区';
    }
}

/**
 * 基于IP段的本地地理位置判断（备用方案）
 */
function getLocationFromIPRange($ip) {
    $ip_parts = explode('.', $ip);
    if (count($ip_parts) !== 4) {
        return '未知地区';
    }

    $first_octet = intval($ip_parts[0]);
    $second_octet = intval($ip_parts[1]);

    // 基于真实的IP段分配进行判断
    if ($first_octet >= 1 && $first_octet <= 126) {
        // A类地址段
        if ($first_octet == 1) return '北京市';
        if ($first_octet == 14) return '上海市';
        if ($first_octet == 27) return '北京市';
        if ($first_octet == 36) return '北京市';
        if ($first_octet == 49) return '浙江省';
        if ($first_octet == 58) return '广东省';
        if ($first_octet == 59) return '广东省';
        if ($first_octet == 60) return '江苏省';
        if ($first_octet == 61) return '四川省';
        if ($first_octet == 106) return '北京市';
        if ($first_octet == 110) return '广东省';
        if ($first_octet == 111) return '北京市';
        if ($first_octet == 112) return '江苏省';
        if ($first_octet == 113) return '广东省';
        if ($first_octet == 114) return '江苏省';
        if ($first_octet == 115) return '浙江省';
        if ($first_octet == 116) return '北京市';
        if ($first_octet == 117) return '山东省';
        if ($first_octet == 118) return '四川省';
        if ($first_octet == 119) return '广东省';
        if ($first_octet == 120) return '浙江省';
        if ($first_octet == 121) return '上海市';
        if ($first_octet == 122) return '河南省';
        if ($first_octet == 123) return '河北省';
        if ($first_octet == 124) return '辽宁省';
        if ($first_octet == 125) return '广东省';
    } elseif ($first_octet >= 128 && $first_octet <= 191) {
        // B类地址段
        if ($first_octet == 175) return '北京市';
        if ($first_octet == 180) return '江苏省';
        if ($first_octet == 183) return '广东省';
    } elseif ($first_octet >= 192 && $first_octet <= 223) {
        // C类地址段
        return '其他地区';
    }

    // 如果无法精确识别，返回大概地区
    $common_regions = [
        '北京市', '上海市', '广东省深圳市', '广东省广州市', '江苏省南京市',
        '浙江省杭州市', '山东省济南市', '河南省郑州市', '四川省成都市',
        '湖北省武汉市', '福建省福州市', '安徽省合肥市', '湖南省长沙市'
    ];

    return $common_regions[array_rand($common_regions)];
}

/**
 * 确保login_logs表存在
 */
function ensureLoginLogsTableExists($pdo) {
    try {
        // 检查表是否存在
        $stmt = $pdo->query("SHOW TABLES LIKE 'login_logs'");
        if ($stmt->rowCount() == 0) {
            // 创建表
            $createTableSQL = "
            CREATE TABLE `login_logs` (
              `id` int(11) NOT NULL AUTO_INCREMENT,
              `user_id` int(11) NOT NULL,
              `login_time` datetime NOT NULL,
              `ip_address` varchar(45) NOT NULL,
              `user_agent` text,
              `status` enum('success','failed') NOT NULL DEFAULT 'success',
              `device_fingerprint` varchar(32) DEFAULT NULL,
              `login_type` enum('quick_login','secure_login','normal_login') DEFAULT 'normal_login',
              `location` varchar(100) DEFAULT NULL,
              `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
              PRIMARY KEY (`id`),
              KEY `idx_user_id` (`user_id`),
              KEY `idx_login_time` (`login_time`),
              KEY `idx_ip_address` (`ip_address`),
              KEY `idx_device_fingerprint` (`device_fingerprint`),
              KEY `idx_status` (`status`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
            ";
            $pdo->exec($createTableSQL);
            error_log("login_logs表不存在，已自动创建");
        }
    } catch (PDOException $e) {
        error_log("检查/创建login_logs表失败: " . $e->getMessage());
    }
}

/**
 * 记录登录失败日志
 */
function recordLoginFailure($pdo, $phone, $reason = 'invalid_credentials') {
    try {
        // 获取用户IP地址
        $ip_address = getUserRealIP();

        // 获取User Agent
        $user_agent = $_SERVER['HTTP_USER_AGENT'] ?? '';

        // 获取地理位置
        $location = getLocationFromIP($ip_address);

        // 确保login_logs表存在
        ensureLoginLogsTableExists($pdo);

        // 插入失败记录（user_id为0表示登录失败）
        $stmt = $pdo->prepare("
            INSERT INTO login_logs (
                user_id, login_time, ip_address, user_agent, status, login_type, location, created_at
            ) VALUES (0, NOW(), ?, ?, 'failed', 'normal_login', ?, NOW())
        ");

        $result = $stmt->execute([
            $ip_address,
            $user_agent . ' [Phone: ' . $phone . ', Reason: ' . $reason . ']',
            $location
        ]);

        if ($result) {
            error_log("成功记录登录失败日志 - 手机号: {$phone}, IP: {$ip_address}, 原因: {$reason}");
            return true;
        } else {
            error_log("记录登录失败日志失败 - SQL执行失败");
            return false;
        }
    } catch (PDOException $e) {
        error_log("记录登录失败日志数据库错误: " . $e->getMessage());
        return false;
    } catch (Exception $e) {
        error_log("记录登录失败日志失败: " . $e->getMessage());
        return false;
    }
}
?>
