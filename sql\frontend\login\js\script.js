// 全局定义Toast提示函数，确保在任何地方都可以访问
function showToast(message, duration = 3000) {
    console.log('显示Toast提示:', message);

    const toast = document.getElementById('toast');
    if (!toast) {
        console.error('未找到toast元素');
        alert(message); // 如果找不到toast元素，使用alert作为备选
        return;
    }

    // 如果已经有toast在显示，先清除之前的定时器
    if (toast.timer) {
        clearTimeout(toast.timer);
    }

    // 先隐藏toast，触发重绘，重置动画
    toast.style.display = 'none';
    void toast.offsetWidth; // 触发重绘

    // 设置消息并显示
    toast.textContent = message;
    toast.style.display = 'block';

    // 设置定时器关闭toast
    toast.timer = setTimeout(() => {
        toast.style.display = 'none';
    }, duration);
}

// 确保window.showToast也可用
window.showToast = showToast;

// 添加一个测试函数，可以直接在控制台调用
window.testToast = function() {
    showToast('这是一个测试提示，验证toast是否正常工作');
};

// 验证码相关变量
let captchaCountdown = 0;
let countdownInterval = null;

// 开始倒计时
function startCaptchaCountdown() {
    const refreshButton = document.getElementById('refresh-captcha-btn');
    if (!refreshButton) return;

    // 设置初始倒计时时间（60秒）
    captchaCountdown = 60;

    // 禁用按钮并显示倒计时
    refreshButton.disabled = true;
    refreshButton.textContent = `${captchaCountdown}秒后重试`;

    // 清除可能存在的旧定时器
    if (countdownInterval) {
        clearInterval(countdownInterval);
    }

    // 设置新的定时器
    countdownInterval = setInterval(function() {
        captchaCountdown--;

        if (captchaCountdown <= 0) {
            // 倒计时结束，恢复按钮
            clearInterval(countdownInterval);
            refreshButton.disabled = false;
            refreshButton.textContent = '获取验证码';
        } else {
            // 更新倒计时显示
            refreshButton.textContent = `${captchaCountdown}秒后重试`;
        }
    }, 1000);
}

// 刷新验证码
window.refreshCaptcha = function() {
    const captchaText = document.getElementById('captcha-text');
    const refreshButton = document.getElementById('refresh-captcha-btn');

    // 如果按钮被禁用（倒计时中），则不执行
    if (refreshButton && refreshButton.disabled) {
        return;
    }

    // 发送AJAX请求获取新验证码
    const xhr = new XMLHttpRequest();
    xhr.open('GET', 'captcha.php?refresh=1&t=' + new Date().getTime(), true);
    xhr.onreadystatechange = function() {
        if (xhr.readyState === 4) {
            if (xhr.status === 200) {
                try {
                    const response = JSON.parse(xhr.responseText);

                    if (response.error) {
                        // 不显示错误信息
                        console.log('验证码获取失败:', response.message);
                    } else {
                        // 显示新验证码
                        if (captchaText) {
                            captchaText.textContent = response.captcha;
                        }

                        // 清空验证码输入框
                        const captchaInput = document.getElementById('captcha');
                        if (captchaInput) {
                            captchaInput.value = '';
                            // 如果验证码输入框可见，则聚焦
                            if (captchaInput.offsetParent !== null) {
                                setTimeout(function() {
                                    captchaInput.focus();
                                }, 100);
                            }
                        }

                        // 开始倒计时
                        startCaptchaCountdown();
                    }
                } catch (e) {
                    console.error('解析验证码响应失败:', e);
                    if (captchaText) {
                        captchaText.textContent = '点击获取';
                    }
                }
            } else {
                console.error('获取验证码失败:', xhr.status);
                if (captchaText) {
                    captchaText.textContent = '点击获取';
                }
            }
        }
    };
    xhr.send();
};

document.addEventListener('DOMContentLoaded', function() {
    // 确保DOM完全加载
    console.log('DOM加载完成，开始初始化表单验证');

    // 初始化验证码文本
    const captchaText = document.getElementById('captcha-text');
    if (captchaText) {
        captchaText.textContent = '点击获取';
    }

    // 检查URL参数，显示成功或失败提示
    const urlParams = new URLSearchParams(window.location.search);
    if (urlParams.has('login_success')) {
        showToast('登录成功！欢迎回来');
    } else if (urlParams.has('register_success')) {
        showToast('注册成功！欢迎加入趣玩星球');
    } else if (urlParams.has('login_failed')) {
        // 如果是登录失败，切换到登录标签
        document.querySelector('.tab[data-tab="login"]').click();
    } else if (urlParams.has('register_failed')) {
        // 如果是注册失败，切换到注册标签
        document.querySelector('.tab[data-tab="register"]').click();
    }

    // 检查会话存储中的成功标志
    if (sessionStorage.getItem('login_success')) {
        showToast('登录成功！欢迎回来');
        sessionStorage.removeItem('login_success');
    } else if (sessionStorage.getItem('register_success')) {
        showToast('注册成功！欢迎加入趣玩星球');
        sessionStorage.removeItem('register_success');
    }

    // 检查PHP会话中的成功或失败标志
    fetch('check_session.php')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showToast(data.message);
                // 存储到会话存储中，以便在页面刷新后仍能显示
                sessionStorage.setItem(data.type + '_success', 'true');
            } else if (data.error) {
                showToast(data.message);
                // 如果是注册错误，切换到注册标签
                if (data.type === 'register') {
                    document.querySelector('.tab[data-tab="register"]').click();
                }
            }
        })
        .catch(error => {
            console.error('检查会话状态失败:', error);
        });

    // 添加滚动事件监听，控制状态栏颜色
    const statusBar = document.querySelector('.status-bar');
    const loginCard = document.querySelector('.login-card');
    const topHeader = document.querySelector('.top-header');

    if (statusBar && loginCard && topHeader) {
        // 获取登录卡片相对于视口的位置
        const updateStatusBarColor = function() {
            const loginCardTop = loginCard.getBoundingClientRect().top;
            const headerBottom = topHeader.getBoundingClientRect().bottom;

            // 当登录卡片顶部接近视口顶部时，改变状态栏颜色
            if (loginCardTop < 50) {
                statusBar.classList.add('scrolled');
            } else if (headerBottom > 24) { // 状态栏高度
                statusBar.classList.remove('scrolled');
            }
        };

        // 初始检查
        updateStatusBarColor();

        // 滚动时检查
        window.addEventListener('scroll', updateStatusBarColor);
    }

    // 绑定登录按钮点击事件
    const loginButton = document.getElementById('login-button');
    if (loginButton) {
        loginButton.addEventListener('click', function(e) {
            validateLoginForm(e);
        });
    }

    // 绑定注册按钮点击事件
    const registerButton = document.getElementById('register-button');
    if (registerButton) {
        registerButton.addEventListener('click', function(e) {
            validateRegisterForm(e);
        });
    }
    // 标签页切换
    const tabs = document.querySelectorAll('.tab');
    const forms = document.querySelectorAll('.form');

    tabs.forEach(tab => {
        tab.addEventListener('click', function() {
            const tabId = this.getAttribute('data-tab');

            // 移除所有标签和表单的active类
            tabs.forEach(t => t.classList.remove('active'));
            forms.forEach(f => f.classList.remove('active'));

            // 添加active类到当前标签和对应表单
            this.classList.add('active');
            document.getElementById(`${tabId}-form`).classList.add('active');
        });
    });

    // 浮动标签效果
    const inputs = document.querySelectorAll('.floating-label input');

    // 初始化时检查是否有值
    inputs.forEach(input => {
        if (input.value.trim() !== '') {
            input.classList.add('has-value');
        }
    });

    // 添加事件监听器
    inputs.forEach(input => {
        // 输入时检查
        input.addEventListener('input', function() {
            if (this.value.trim() !== '') {
                this.classList.add('has-value');
            } else {
                this.classList.remove('has-value');
            }
        });

        // 获取焦点时
        input.addEventListener('focus', function() {
            this.parentElement.classList.add('focused');
        });

        // 失去焦点时
        input.addEventListener('blur', function() {
            this.parentElement.classList.remove('focused');
            if (this.value.trim() === '') {
                this.classList.remove('has-value');
            }
        });
    });

    // 密码可见性切换
    const togglePasswordElements = document.querySelectorAll('.toggle-password');

    togglePasswordElements.forEach(element => {
        element.addEventListener('click', function() {
            const passwordInput = this.parentElement.querySelector('input');
            const eyeOpen = this.querySelector('.eye-open');
            const eyeClosed = this.querySelector('.eye-closed');

            if (passwordInput.type === 'password') {
                passwordInput.type = 'text';
                eyeOpen.style.display = 'none';
                eyeClosed.style.display = 'block';
            } else {
                passwordInput.type = 'password';
                eyeOpen.style.display = 'block';
                eyeClosed.style.display = 'none';
            }
        });
    });

    // 登录表单验证
    const loginForm = document.getElementById('login-form');

    // 验证登录表单的函数
function validateLoginForm(e) {
    // 阻止默认行为
    if (e) e.preventDefault();

    const username = document.getElementById('username').value.trim();
    const password = document.getElementById('password').value.trim();
    const agreementChecked = document.getElementById('login_agreement').checked;

    console.log('验证登录表单:', username ? '用户名已填写' : '用户名为空', password ? '密码已填写' : '密码为空');

    // 验证用户名/手机号/趣玩ID
    if (username === '') {
        showToast('请输入手机号或趣玩ID');
        return false;
    }

    // 验证密码
    if (password === '') {
        showToast('请输入密码');
        return false;
    }

    // 验证协议勾选
    if (!agreementChecked) {
        showToast('请阅读并同意用户协议和隐私政策');
        const agreementCheckbox = document.getElementById('login_agreement').parentNode;
        agreementCheckbox.classList.add('error');

        // 3秒后移除错误样式
        setTimeout(function() {
            agreementCheckbox.classList.remove('error');
        }, 3000);

        return false;
    }

    // 验证通过，提交表单
    const loginForm = document.getElementById('login-form');
    if (loginForm) {
        // 显示提交中的提示
        showToast('正在登录，请稍候...');
        loginForm.submit();
    } else {
        alert('表单验证通过，但未找到表单元素');
    }
}

// 确保全局可用
window.validateLoginForm = validateLoginForm;

// 验证注册表单的函数
function validateRegisterForm(e) {
    // 阻止默认行为
    if (e) e.preventDefault();

    const username = document.getElementById('reg_username').value.trim();
    const phone = document.getElementById('reg_phone').value.trim();
    const email = document.getElementById('reg_email').value.trim();
    const password = document.getElementById('reg_password').value.trim();
    const confirmPassword = document.getElementById('reg_confirm_password').value.trim();
    const captcha = document.getElementById('captcha').value.trim();

    console.log('验证注册表单');

    // 验证用户名
    if (username === '') {
        showToast('请输入用户名');
        return false;
    }

    // 验证手机号
    if (phone === '') {
        showToast('请输入手机号');
        return false;
    }

    // 验证邮箱
    if (email === '') {
        showToast('请输入邮箱');
        return false;
    } else if (!isValidEmail(email)) {
        showToast('请输入有效的邮箱地址');
        return false;
    }

    // 验证密码
    if (password === '') {
        showToast('请输入密码');
        return false;
    } else if (password.length < 8) {
        showToast('密码长度至少为8个字符');
        return false;
    }

    // 验证确认密码
    if (confirmPassword === '') {
        showToast('请确认密码');
        return false;
    } else if (password !== confirmPassword) {
        showToast('两次输入的密码不一致');
        return false;
    }

    // 验证验证码
    if (captcha === '') {
        showToast('请输入验证码');
        return false;
    } else if (captcha.length !== 4) {
        showToast('验证码必须是4位');
        return false;
    }

    // 获取当前显示的验证码
    const displayedCaptcha = document.getElementById('captcha-text').textContent;
    if (displayedCaptcha === '加载中...' || displayedCaptcha === '获取失败' || displayedCaptcha === '点击获取') {
        showToast('请先获取验证码');
        return false;
    }

    // 验证协议勾选
    const agreementChecked = document.getElementById('register_agreement').checked;
    if (!agreementChecked) {
        showToast('请阅读并同意用户协议和隐私政策');
        const agreementCheckbox = document.getElementById('register_agreement').parentNode;
        agreementCheckbox.classList.add('error');

        // 3秒后移除错误样式
        setTimeout(function() {
            agreementCheckbox.classList.remove('error');
        }, 3000);

        return false;
    }

    // 验证通过，提交表单
    const registerForm = document.getElementById('register-form');
    if (registerForm) {
        // 显示提交中的提示
        showToast('正在提交，请稍候...');

        // 直接提交表单，让服务器端处理验证码验证
        registerForm.submit();
    } else {
        alert('表单验证通过，但未找到表单元素');
    }
}

// 确保全局可用
window.validateRegisterForm = validateRegisterForm;

    // 同时保留表单提交事件
    if (loginForm) {
        loginForm.addEventListener('submit', function(e) {
            // 阻止表单默认提交行为，进行自定义验证
            e.preventDefault();

            const username = document.getElementById('username').value.trim();
            const password = document.getElementById('password').value.trim();

            // 验证用户名/手机号/趣玩ID
            if (username === '') {
                showToast('请输入手机号或趣玩ID');
                return false;
            }

            // 验证密码
            if (password === '') {
                showToast('请输入密码');
                return false;
            }

            // 验证通过，提交表单
            this.submit();
        });
    }

    // 注册表单验证
    const registerForm = document.getElementById('register-form');

    // 同时保留表单提交事件
    if (registerForm) {
        registerForm.addEventListener('submit', function(e) {
            // 阻止表单默认提交行为，进行自定义验证
            e.preventDefault();

            const username = document.getElementById('reg_username').value.trim();
            const phone = document.getElementById('reg_phone').value.trim();
            const email = document.getElementById('reg_email').value.trim();
            const password = document.getElementById('reg_password').value.trim();
            const confirmPassword = document.getElementById('reg_confirm_password').value.trim();

            // 验证用户名
            if (username === '') {
                showToast('请输入用户名');
                return false;
            }

            // 验证手机号
            if (phone === '') {
                showToast('请输入手机号');
                return false;
            }

            // 验证邮箱
            if (email === '') {
                showToast('请输入邮箱');
                return false;
            } else if (!isValidEmail(email)) {
                showToast('请输入有效的邮箱地址');
                return false;
            }

            // 验证密码
            if (password === '') {
                showToast('请输入密码');
                return false;
            } else if (password.length < 8) {
                showToast('密码长度至少为8个字符');
                return false;
            }

            // 验证确认密码
            if (confirmPassword === '') {
                showToast('请确认密码');
                return false;
            } else if (password !== confirmPassword) {
                showToast('两次输入的密码不一致');
                return false;
            }

            // 验证通过，提交表单
            this.submit();
        });
    }

    // 验证邮箱格式的函数
    function isValidEmail(email) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
    }

    // 登录和注册表单错误处理已移至PHP处理

    // Toast函数已在全局定义

    // 禁用浏览器原生提示
    document.addEventListener('invalid', (function() {
        return function(e) {
            e.preventDefault();
            const element = e.target;
            if (!element.validationMessage) return;

            showToast(element.validationMessage);
        };
    })(), true);
});
