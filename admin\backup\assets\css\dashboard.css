/**
 * 主菜单/仪表盘专用样式
 * 趣玩星球管理后台 v2.0
 */

/* ===== 欢迎区域 ===== */
.welcome-section {
    text-align: center;
    margin-bottom: var(--spacing-2xl);
    padding: var(--spacing-xl) 0;
}

.welcome-title {
    font-size: 2.5rem;
    font-weight: 700;
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-bottom: var(--spacing-sm);
    line-height: 1.2;
}

.welcome-subtitle {
    font-size: 1.125rem;
    color: var(--gray-600);
    font-weight: 400;
    max-width: 600px;
    margin: 0 auto;
}

/* ===== 统计卡片网格 ===== */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--spacing-lg);
    margin-bottom: var(--spacing-2xl);
}

.stat-card {
    background: white;
    border-radius: var(--radius-2xl);
    padding: var(--spacing-xl);
    box-shadow: var(--shadow-lg);
    border: 1px solid rgba(255, 255, 255, 0.2);
    position: relative;
    overflow: hidden;
    transition: all 0.4s ease;
    cursor: pointer;
}

.stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--gradient-primary);
    transform: scaleX(0);
    transition: transform 0.3s ease;
}

.stat-card:hover::before {
    transform: scaleX(1);
}

.stat-card:hover {
    transform: translateY(-8px);
    box-shadow: var(--shadow-2xl);
}

.stat-card.primary::before { background: var(--gradient-primary); }
.stat-card.secondary::before { background: var(--gradient-secondary); }
.stat-card.success::before { background: var(--gradient-success); }
.stat-card.warning::before { background: var(--gradient-warning); }
.stat-card.error::before { background: var(--gradient-error); }
.stat-card.info::before { background: var(--gradient-info); }

.stat-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: var(--spacing-lg);
}

.stat-icon {
    width: 48px;
    height: 48px;
    border-radius: var(--radius-xl);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.25rem;
    color: white;
    background: var(--gradient-primary);
}

.stat-card.primary .stat-icon { background: var(--gradient-primary); }
.stat-card.secondary .stat-icon { background: var(--gradient-secondary); }
.stat-card.success .stat-icon { background: var(--gradient-success); }
.stat-card.warning .stat-icon { background: var(--gradient-warning); }
.stat-card.error .stat-icon { background: var(--gradient-error); }
.stat-card.info .stat-icon { background: var(--gradient-info); }

.stat-trend {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    font-size: 0.75rem;
    font-weight: 500;
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--radius-md);
    background: rgba(16, 185, 129, 0.1);
    color: var(--success-color);
}

.stat-trend.negative {
    background: rgba(239, 68, 68, 0.1);
    color: var(--error-color);
}

.stat-content {
    text-align: left;
}

.stat-number {
    font-size: 2.25rem;
    font-weight: 700;
    color: var(--gray-800);
    line-height: 1;
    margin-bottom: var(--spacing-xs);
}

.stat-label {
    font-size: 0.875rem;
    color: var(--gray-600);
    font-weight: 500;
    margin-bottom: var(--spacing-sm);
}

.stat-description {
    font-size: 0.75rem;
    color: var(--gray-500);
    line-height: 1.4;
}

/* ===== 功能模块网格 ===== */
.modules-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: var(--spacing-xl);
}

.module-card {
    background: white;
    border-radius: var(--radius-2xl);
    padding: var(--spacing-xl);
    box-shadow: var(--shadow-lg);
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: all 0.4s ease;
    position: relative;
    overflow: hidden;
}

.module-card::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: var(--gradient-primary);
    opacity: 0;
    transition: opacity 0.3s ease;
    z-index: -1;
}

.module-card:hover::after {
    opacity: 0.03;
}

.module-card:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-2xl);
    border-color: var(--primary-color);
}

.module-header {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-lg);
    padding-bottom: var(--spacing-md);
    border-bottom: 2px solid var(--gray-100);
}

.module-icon {
    width: 56px;
    height: 56px;
    border-radius: var(--radius-xl);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: white;
    background: var(--gradient-primary);
    box-shadow: var(--shadow-md);
}

.module-info h3 {
    font-size: 1.25rem;
    font-weight: 700;
    color: var(--gray-800);
    margin-bottom: var(--spacing-xs);
}

.module-info p {
    font-size: 0.875rem;
    color: var(--gray-600);
    line-height: 1.4;
}

.module-functions {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
}

.function-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    padding: var(--spacing-md);
    border-radius: var(--radius-xl);
    background: var(--gray-50);
    text-decoration: none;
    color: var(--gray-700);
    transition: all 0.3s ease;
    border: 2px solid transparent;
    position: relative;
    overflow: hidden;
}

.function-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(64, 224, 208, 0.1), transparent);
    transition: left 0.5s ease;
}

.function-item:hover::before {
    left: 100%;
}

.function-item:hover {
    background: rgba(64, 224, 208, 0.05);
    border-color: var(--primary-color);
    transform: translateX(8px);
    text-decoration: none;
    color: var(--gray-800);
}

.function-icon {
    width: 40px;
    height: 40px;
    border-radius: var(--radius-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1rem;
    color: white;
    background: var(--gradient-secondary);
    flex-shrink: 0;
}

.function-content {
    flex: 1;
}

.function-title {
    font-size: 1rem;
    font-weight: 600;
    color: var(--gray-800);
    margin-bottom: 2px;
}

.function-description {
    font-size: 0.75rem;
    color: var(--gray-500);
    line-height: 1.3;
}

.function-arrow {
    color: var(--gray-400);
    font-size: 0.875rem;
    transition: all 0.3s ease;
}

.function-item:hover .function-arrow {
    color: var(--primary-color);
    transform: translateX(4px);
}

/* ===== 特殊功能项样式 ===== */
.function-item.primary .function-icon { background: var(--gradient-primary); }
.function-item.secondary .function-icon { background: var(--gradient-secondary); }
.function-item.success .function-icon { background: var(--gradient-success); }
.function-item.warning .function-icon { background: var(--gradient-warning); }
.function-item.error .function-icon { background: var(--gradient-error); }
.function-item.info .function-icon { background: var(--gradient-info); }

.function-item.danger {
    border-color: var(--error-color);
    background: rgba(239, 68, 68, 0.05);
}

.function-item.danger:hover {
    background: rgba(239, 68, 68, 0.1);
    border-color: var(--error-color);
}

.function-item.danger .function-title {
    color: var(--error-color);
}

/* ===== 响应式设计 ===== */
@media (max-width: 768px) {
    .welcome-title {
        font-size: 2rem;
    }
    
    .stats-grid {
        grid-template-columns: 1fr;
        gap: var(--spacing-md);
    }
    
    .modules-grid {
        grid-template-columns: 1fr;
        gap: var(--spacing-lg);
    }
    
    .stat-card,
    .module-card {
        padding: var(--spacing-lg);
    }
}

@media (max-width: 480px) {
    .welcome-title {
        font-size: 1.75rem;
    }
    
    .welcome-subtitle {
        font-size: 1rem;
    }
    
    .stat-number {
        font-size: 1.875rem;
    }
    
    .module-header {
        flex-direction: column;
        text-align: center;
        gap: var(--spacing-sm);
    }
}
