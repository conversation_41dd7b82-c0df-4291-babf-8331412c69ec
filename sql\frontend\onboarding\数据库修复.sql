-- 数据库修复SQL脚本 - 解决字段名不一致问题
-- 请在宝塔面板的phpMyAdmin中执行以下SQL语句

-- 1. 检查users表结构
DESCRIBE users;

-- 2. 检查是否存在quwanplanet_id字段
SELECT COLUMN_NAME
FROM INFORMATION_SCHEMA.COLUMNS
WHERE TABLE_SCHEMA = 'quwanplanet'
AND TABLE_NAME = 'users'
AND COLUMN_NAME IN ('quwan_id', 'quwanplanet_id');

-- 3. 统一字段名 - 如果存在quwanplanet_id字段，将其重命名为quwan_id
-- 注意：这个操作会根据实际情况执行

-- 3a. 如果只有quwanplanet_id字段，重命名为quwan_id
-- ALTER TABLE users CHANGE COLUMN quwanplanet_id quwan_id VARCHAR(9) NULL DEFAULT NULL;

-- 3b. 如果只有quwan_id字段，修改其属性
ALTER TABLE users MODIFY COLUMN quwan_id VARCHAR(9) NULL DEFAULT NULL;

-- 3c. 如果两个字段都存在，需要合并数据（请谨慎操作）
-- UPDATE users SET quwan_id = quwanplanet_id WHERE quwan_id IS NULL AND quwanplanet_id IS NOT NULL;
-- ALTER TABLE users DROP COLUMN quwanplanet_id;

-- 3. 确保其他必要字段的设置
ALTER TABLE users MODIFY COLUMN phone VARCHAR(20) NOT NULL;
ALTER TABLE users MODIFY COLUMN username VARCHAR(50) NULL DEFAULT NULL;
ALTER TABLE users MODIFY COLUMN nickname VARCHAR(50) NOT NULL;
ALTER TABLE users MODIFY COLUMN gender ENUM('male', 'female', 'other') NOT NULL;
ALTER TABLE users MODIFY COLUMN birth_date DATE NOT NULL;
ALTER TABLE users MODIFY COLUMN region VARCHAR(100) NOT NULL;
ALTER TABLE users MODIFY COLUMN email VARCHAR(100) NOT NULL UNIQUE;
ALTER TABLE users MODIFY COLUMN password VARCHAR(255) NOT NULL;
ALTER TABLE users MODIFY COLUMN bio TEXT NULL DEFAULT NULL;
ALTER TABLE users MODIFY COLUMN avatar VARCHAR(500) NULL DEFAULT NULL;
ALTER TABLE users MODIFY COLUMN status ENUM('active', 'inactive', 'banned', 'incomplete') DEFAULT 'active';
ALTER TABLE users MODIFY COLUMN created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP;
ALTER TABLE users MODIFY COLUMN updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP;

-- 4. 确保quwan_id字段有唯一索引
ALTER TABLE users ADD UNIQUE INDEX idx_quwan_id (quwan_id);

-- 5. 检查system_messages表是否存在，如果不存在则创建
CREATE TABLE IF NOT EXISTS system_messages (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    title VARCHAR(255) NOT NULL,
    content TEXT NOT NULL,
    type ENUM('welcome', 'notification', 'warning', 'system') DEFAULT 'notification',
    is_read TINYINT(1) DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_user_id (user_id),
    INDEX idx_is_read (is_read),
    INDEX idx_created_at (created_at)
);

-- 6. 检查修复结果
SELECT
    COLUMN_NAME,
    DATA_TYPE,
    IS_NULLABLE,
    COLUMN_DEFAULT,
    EXTRA
FROM INFORMATION_SCHEMA.COLUMNS
WHERE TABLE_SCHEMA = 'quwanplanet'
AND TABLE_NAME = 'users'
ORDER BY ORDINAL_POSITION;
