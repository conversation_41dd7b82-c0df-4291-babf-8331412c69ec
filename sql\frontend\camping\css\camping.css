/* 露营页面样式 */

/* CSS变量定义 */
:root {
    --primary-color: #6F7BF5;
    --primary-light: #A8B2F8;
    --primary-dark: #5A67E8;
    --secondary-color: #8B95F7;
    --accent-color: #FF6B9D;
    --success-color: #06D6A0;
    --warning-color: #FFD166;
    --error-color: #FF6B6B;
    --gradient-primary: linear-gradient(135deg, #6F7BF5, #8B95F7);
    --gradient-secondary: linear-gradient(135deg, #A8B2F8, #6F7BF5);
    --gradient-warm: linear-gradient(135deg, #FF6B9D, #FFD166);
    --text-primary: #333333;
    --text-secondary: #666666;
    --text-light: #999999;
    --bg-white: #FFFFFF;
    --bg-light: #F8F9FA;
    --shadow-sm: 0 2px 8px rgba(0, 0, 0, 0.08);
    --shadow-md: 0 4px 16px rgba(0, 0, 0, 0.12);
    --shadow-lg: 0 8px 32px rgba(0, 0, 0, 0.16);
    --radius-sm: 8px;
    --radius-md: 12px;
    --radius-lg: 16px;
    --radius-xl: 24px;
    --transition-fast: all 0.2s ease;
    --transition-normal: all 0.3s ease;
}

/* 基础样式重置 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background: var(--bg-light);
    color: var(--text-primary);
    line-height: 1.6;
    overflow-x: hidden;
}

/* 页面头部 */
.camping-header {
    position: relative;
    height: 280px;
    overflow: hidden;
    background: var(--gradient-primary);
}

.header-background {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('https://s1.imagehub.cc/images/2025/05/13/586ffb30dfc9e66fc6b5389d69f3b74d.th.jpg') center/cover;
}

.header-background::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: var(--gradient-primary);
    opacity: 0.8;
}

.header-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(180deg, rgba(0,0,0,0.3) 0%, rgba(0,0,0,0.1) 100%);
}

.header-content {
    position: relative;
    z-index: 2;
    height: 100%;
    padding: 20px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}

.header-top {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-top: 20px;
}

.back-btn, .share-btn {
    width: 44px;
    height: 44px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.3);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: var(--transition-normal);
}

.back-btn:hover, .share-btn:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: scale(1.05);
}

.page-title {
    font-size: 1.5rem;
    font-weight: 700;
    color: white;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.header-description {
    text-align: center;
    margin-bottom: 40px;
}

.header-description p {
    color: rgba(255, 255, 255, 0.9);
    font-size: 1rem;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

/* 主要内容区域 */
.camping-main {
    position: relative;
    z-index: 1;
    margin-top: -40px;
    background: var(--bg-light);
    border-radius: var(--radius-xl) var(--radius-xl) 0 0;
    padding: 30px 20px 100px;
    min-height: calc(100vh - 240px);
}

/* 通用区域样式 */
.section-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 20px;
}

.section-header h2 {
    font-size: 1.25rem;
    font-weight: 700;
    color: var(--text-primary);
    display: flex;
    align-items: center;
    gap: 8px;
}

.section-header h2 i {
    color: var(--primary-color);
    font-size: 1.1rem;
}

.more-link {
    color: var(--primary-color);
    text-decoration: none;
    font-size: 0.875rem;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 4px;
    transition: var(--transition-fast);
}

.more-link:hover {
    color: var(--primary-dark);
    transform: translateX(2px);
}

.header-actions {
    display: flex;
    align-items: center;
    gap: 12px;
}

.create-activity-btn {
    background: var(--gradient-primary);
    color: white;
    text-decoration: none;
    padding: 8px 16px;
    border-radius: var(--radius-lg);
    font-size: 0.875rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 6px;
    transition: var(--transition-normal);
    box-shadow: var(--shadow-sm);
}

.create-activity-btn:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
    background: var(--gradient-secondary);
}

.create-activity-btn i {
    font-size: 0.8rem;
}

/* 优惠券区域 */
.coupon-section {
    margin-bottom: 30px;
}

.coupon-container {
    background: white;
    border-radius: var(--radius-lg);
    padding: 20px;
    box-shadow: var(--shadow-md);
}

.coupon-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 16px;
    padding-bottom: 12px;
    border-bottom: 2px solid var(--bg-light);
}

.coupon-header h2 {
    font-size: 1.125rem;
    font-weight: 700;
    color: var(--text-primary);
    display: flex;
    align-items: center;
    gap: 8px;
}

.coupon-header h2 i {
    color: var(--primary-color);
    font-size: 1.1rem;
}

.countdown {
    background: var(--gradient-warm);
    color: white;
    padding: 6px 12px;
    border-radius: var(--radius-lg);
    font-size: 0.875rem;
    font-weight: 600;
    box-shadow: var(--shadow-sm);
}

.coupon-list {
    display: flex;
    gap: 12px;
    overflow-x: auto;
    padding-bottom: 4px;
}

.coupon-item {
    background: var(--gradient-primary);
    border-radius: var(--radius-md);
    padding: 16px;
    min-width: 160px;
    flex-shrink: 0;
    color: white;
    text-align: center;
    position: relative;
    transition: var(--transition-normal);
    cursor: pointer;
}

.coupon-item:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.coupon-item.newbie {
    background: var(--gradient-secondary);
}

.coupon-amount {
    font-size: 1.25rem;
    font-weight: 700;
    margin-bottom: 4px;
}

.coupon-title {
    font-size: 0.875rem;
    font-weight: 600;
    margin-bottom: 8px;
    opacity: 0.95;
}

.coupon-desc {
    font-size: 0.75rem;
    opacity: 0.9;
    margin-bottom: 12px;
    line-height: 1.3;
}

.coupon-validity {
    font-size: 0.7rem;
    opacity: 0.8;
    margin-bottom: 12px;
}

.claim-btn {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    border: 1px solid rgba(255, 255, 255, 0.3);
    padding: 8px 16px;
    border-radius: var(--radius-sm);
    font-size: 0.75rem;
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition-normal);
    width: 100%;
    backdrop-filter: blur(10px);
}

.claim-btn:hover {
    background: rgba(255, 255, 255, 0.3);
    border-color: rgba(255, 255, 255, 0.5);
}

.claim-btn:active {
    transform: scale(0.98);
}

.claim-btn.claimed {
    background: rgba(255, 255, 255, 0.1);
    border-color: rgba(255, 255, 255, 0.2);
    cursor: not-allowed;
    opacity: 0.7;
}

.claim-btn.claimed:hover {
    transform: none;
    background: rgba(255, 255, 255, 0.1);
}

/* 热门露营活动 */
.camping-activities {
    margin-bottom: 40px;
}

.activities-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: 16px;
}

.activity-card {
    background: white;
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-md);
    overflow: hidden;
    transition: var(--transition-normal);
    cursor: pointer;
    position: relative;
    display: flex;
    flex-direction: row;
    height: 140px;
}

.activity-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.activity-image {
    position: relative;
    width: 120px;
    height: 100%;
    overflow: hidden;
    flex-shrink: 0;
}

.activity-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: var(--transition-normal);
}

.activity-card:hover .activity-image img {
    transform: scale(1.05);
}

.activity-badge {
    position: absolute;
    top: 12px;
    left: 12px;
    background: var(--error-color);
    color: white;
    padding: 4px 12px;
    border-radius: var(--radius-lg);
    font-size: 0.75rem;
    font-weight: 600;
}

.activity-badge.new {
    background: var(--success-color);
}

.activity-status {
    position: absolute;
    top: 12px;
    right: 12px;
    background: rgba(0, 0, 0, 0.7);
    color: white;
    padding: 4px 12px;
    border-radius: var(--radius-lg);
    font-size: 0.75rem;
    font-weight: 600;
    backdrop-filter: blur(10px);
}

.activity-info {
    padding: 16px;
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}

.activity-info h3 {
    font-size: 1rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 8px;
    line-height: 1.3;
}

.activity-organizer {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 16px;
    padding: 8px;
    background: var(--bg-light);
    border-radius: var(--radius-md);
}

.organizer-avatar {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    object-fit: cover;
}

.organizer-name {
    font-size: 0.875rem;
    font-weight: 600;
    color: var(--text-primary);
}

.organizer-level {
    background: var(--gradient-primary);
    color: white;
    padding: 2px 8px;
    border-radius: var(--radius-sm);
    font-size: 0.75rem;
    font-weight: 600;
}

.activity-details {
    margin-bottom: 16px;
}

.detail-item {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 8px;
    font-size: 0.875rem;
    color: var(--text-secondary);
}

.detail-item i {
    color: var(--primary-color);
    width: 16px;
}

.activity-features {
    display: flex;
    gap: 8px;
    margin-bottom: 16px;
    flex-wrap: wrap;
}

.feature-tag {
    background: rgba(111, 123, 245, 0.1);
    color: var(--primary-color);
    padding: 4px 8px;
    border-radius: var(--radius-sm);
    font-size: 0.75rem;
    font-weight: 500;
}

.activity-price {
    display: flex;
    align-items: baseline;
    gap: 8px;
    margin-bottom: 16px;
}

.price {
    font-size: 1.25rem;
    font-weight: 700;
    color: var(--primary-color);
}

.unit {
    font-size: 0.875rem;
    color: var(--text-secondary);
}

.original-price {
    font-size: 0.875rem;
    color: var(--text-light);
    text-decoration: line-through;
}

.join-activity-btn {
    width: 100%;
    background: var(--gradient-primary);
    color: white;
    border: none;
    padding: 12px 24px;
    border-radius: var(--radius-md);
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition-normal);
}

.join-activity-btn:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

/* 发起露营活动区域 */
.create-activity-section {
    margin-bottom: 40px;
}

.create-activity-card {
    background: var(--gradient-primary);
    border-radius: var(--radius-lg);
    padding: 24px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    box-shadow: var(--shadow-md);
    transition: var(--transition-normal);
    cursor: pointer;
}

.create-activity-card:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-lg);
}

.create-activity-content {
    display: flex;
    align-items: center;
    gap: 16px;
    flex: 1;
}

.create-icon {
    width: 60px;
    height: 60px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    backdrop-filter: blur(10px);
}

.create-icon i {
    font-size: 1.5rem;
    color: white;
}

.create-text h3 {
    color: white;
    font-size: 1.125rem;
    font-weight: 600;
    margin-bottom: 4px;
}

.create-text p {
    color: rgba(255, 255, 255, 0.9);
    font-size: 0.875rem;
    line-height: 1.4;
}

.create-activity-btn {
    background: white;
    color: var(--primary-color);
    border: none;
    padding: 12px 24px;
    border-radius: var(--radius-md);
    font-size: 0.875rem;
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition-normal);
    box-shadow: var(--shadow-sm);
}

.create-activity-btn:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

/* 活动分类区域 */
.activity-categories-section {
    margin-bottom: 40px;
}

.categories-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 16px;
}

.category-item {
    background: white;
    border-radius: var(--radius-lg);
    padding: 20px;
    text-align: center;
    box-shadow: var(--shadow-md);
    transition: var(--transition-normal);
    cursor: pointer;
}

.category-item:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-lg);
}

.category-icon {
    width: 60px;
    height: 60px;
    background: var(--gradient-primary);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 12px;
}

.category-icon i {
    font-size: 1.5rem;
    color: white;
}

.category-name {
    display: block;
    font-size: 1rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 4px;
}

.category-count {
    font-size: 0.875rem;
    color: var(--text-secondary);
}

/* 露营攻略区域 */
.camping-guides-section {
    margin-bottom: 40px;
}

.guides-list {
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.guide-item {
    background: white;
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-md);
    overflow: hidden;
    display: flex;
    transition: var(--transition-normal);
    cursor: pointer;
}

.guide-item:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.guide-image {
    width: 120px;
    height: 90px;
    flex-shrink: 0;
    overflow: hidden;
}

.guide-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: var(--transition-normal);
}

.guide-item:hover .guide-image img {
    transform: scale(1.05);
}

.guide-content {
    padding: 16px;
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}

.guide-content h4 {
    font-size: 1rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 8px;
    line-height: 1.3;
}

.guide-content p {
    font-size: 0.875rem;
    color: var(--text-secondary);
    line-height: 1.4;
    margin-bottom: 12px;
}

.guide-meta {
    display: flex;
    gap: 16px;
    font-size: 0.75rem;
    color: var(--text-light);
}

.guide-meta span {
    display: flex;
    align-items: center;
    gap: 4px;
}

.guide-meta i {
    color: var(--primary-color);
}

/* 活动区域 */
.activities-section {
    margin-bottom: 40px;
}

.activities-list {
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.activity-card {
    background: white;
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-md);
    padding: 20px;
    display: flex;
    align-items: center;
    gap: 16px;
    transition: var(--transition-normal);
    cursor: pointer;
}

.activity-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.activity-date {
    background: var(--gradient-primary);
    color: white;
    padding: 12px;
    border-radius: var(--radius-md);
    text-align: center;
    min-width: 60px;
}

.month {
    font-size: 0.75rem;
    font-weight: 500;
    opacity: 0.9;
}

.day {
    font-size: 1.25rem;
    font-weight: 700;
}

.activity-info {
    flex: 1;
}

.activity-info h3 {
    font-size: 1rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 4px;
}

.activity-info p {
    font-size: 0.875rem;
    color: var(--text-secondary);
    margin-bottom: 8px;
}

.activity-meta {
    display: flex;
    gap: 16px;
    font-size: 0.75rem;
    color: var(--text-light);
}

.activity-meta span {
    display: flex;
    align-items: center;
    gap: 4px;
}

.join-btn {
    background: var(--gradient-primary);
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: var(--radius-md);
    font-size: 0.875rem;
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition-normal);
}

.join-btn:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

/* 四角星加载动画 */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(8px);
    display: none;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    z-index: 9999;
}

.loading-overlay.active {
    display: flex;
}

.four-star-loader {
    position: relative;
    width: 60px;
    height: 60px;
    margin-bottom: 20px;
}

.star-point {
    position: absolute;
    width: 12px;
    height: 12px;
    background: var(--primary-color);
    border-radius: 2px;
    animation: starRotate 1.5s ease-in-out infinite;
}

.star-point:nth-child(1) {
    top: 0;
    left: 50%;
    transform: translateX(-50%);
    animation-delay: 0s;
    background: var(--primary-color);
}

.star-point:nth-child(2) {
    top: 50%;
    right: 0;
    transform: translateY(-50%);
    animation-delay: 0.375s;
    background: #FF6B9D;
}

.star-point:nth-child(3) {
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    animation-delay: 0.75s;
    background: #FFD166;
}

.star-point:nth-child(4) {
    top: 50%;
    left: 0;
    transform: translateY(-50%);
    animation-delay: 1.125s;
    background: var(--primary-color);
}

@keyframes starRotate {
    0%, 100% {
        transform: scale(1) rotate(0deg);
        opacity: 1;
    }
    25% {
        transform: scale(1.2) rotate(90deg);
        opacity: 0.8;
    }
    50% {
        transform: scale(0.8) rotate(180deg);
        opacity: 0.6;
    }
    75% {
        transform: scale(1.1) rotate(270deg);
        opacity: 0.8;
    }
}

.loading-text {
    color: var(--text-secondary);
    font-size: 0.875rem;
    font-weight: 500;
}

/* 活动规则入口 */
.activity-rules-entrance {
    position: fixed;
    top: 50%;
    right: 0;
    transform: translateY(-50%);
    background: var(--gradient-primary);
    color: white;
    width: 40px;
    height: 120px;
    border-radius: var(--radius-lg) 0 0 var(--radius-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    z-index: 1000;
    box-shadow: var(--shadow-lg);
    transition: var(--transition-normal);
    backdrop-filter: blur(10px);
}

.activity-rules-entrance:hover {
    transform: translateY(-50%) translateX(-4px);
    box-shadow: var(--shadow-lg);
    background: var(--gradient-secondary);
}

.rules-text {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 4px;
    font-size: 0.875rem;
    font-weight: 600;
    letter-spacing: 2px;
}

.rules-text span {
    display: block;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
    transition: var(--transition-fast);
}

.activity-rules-entrance:hover .rules-text span {
    transform: scale(1.1);
}

/* Toast提示 */
.toast {
    position: fixed;
    bottom: 100px;
    left: 50%;
    transform: translateX(-50%);
    background: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 12px 20px;
    border-radius: var(--radius-lg);
    font-size: 0.875rem;
    z-index: 10000;
    display: none;
    backdrop-filter: blur(10px);
}

/* 响应式设计 */
@media (min-width: 768px) {
    .camping-main {
        padding: 30px 40px 100px;
    }

    .spots-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 24px;
    }

    .equipment-grid {
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 20px;
    }
}

@media (max-width: 480px) {
    .camping-header {
        height: 240px;
    }

    .header-content {
        padding: 16px;
    }

    .page-title {
        font-size: 1.25rem;
    }

    .camping-main {
        padding: 20px 16px 100px;
        margin-top: -30px;
    }

    .coupon-left {
        width: 100px;
        padding: 16px;
    }

    .coupon-amount {
        font-size: 1.25rem;
    }

    .coupon-right {
        padding: 16px;
    }
}
