-- 第四步：插入初始数据
-- 请在宝塔数据库中执行以下SQL语句

-- 插入初始优惠券数据（只保留两张优惠券）
INSERT INTO `camping_coupons` (`title`, `description`, `type`, `discount_amount`, `min_amount`, `total_quantity`, `valid_from`, `valid_until`) VALUES
('新人专享券', '首次参加露营活动专享优惠', 'newbie_discount', 20.00, 80.00, 1000, NOW(), DATE_ADD(NOW(), INTERVAL 30 DAY)),
('放肆趣玩券', '参加任意露营活动立减优惠', 'join_discount', 30.00, 100.00, 500, NOW(), DATE_ADD(NOW(), INTERVAL 30 DAY));
