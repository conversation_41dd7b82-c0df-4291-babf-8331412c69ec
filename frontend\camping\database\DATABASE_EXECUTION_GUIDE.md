# 数据库执行指南

## 🚨 重要说明
请按照以下步骤在宝塔数据库管理中**逐步执行**SQL文件，不要一次性执行所有内容。

## 📋 执行步骤

### 第一步：创建露营活动表
**文件**: `step1_camping_tables.sql`
**说明**: 创建露营活动主表
**执行**: 复制文件内容到宝塔数据库SQL执行器中运行

### 第二步：创建参与记录表
**文件**: `step2_participants.sql`
**说明**: 创建活动参与记录表
**执行**: 复制文件内容到宝塔数据库SQL执行器中运行

### 第三步：创建优惠券相关表
**文件**: `step3_coupons.sql`
**说明**: 创建优惠券表和用户优惠券记录表
**执行**: 复制文件内容到宝塔数据库SQL执行器中运行

### 第四步：插入初始数据
**文件**: `step4_initial_data.sql`
**说明**: 插入初始优惠券数据
**执行**: 复制文件内容到宝塔数据库SQL执行器中运行

## ✅ 验证步骤

执行完成后，请在宝塔数据库管理中检查以下表是否创建成功：

1. `camping_activities` - 露营活动表
2. `camping_participants` - 活动参与记录表  
3. `camping_coupons` - 露营活动优惠券表
4. `user_camping_coupons` - 用户优惠券领取记录表

## 🔍 检查数据

执行以下SQL语句检查初始数据是否插入成功：

```sql
-- 检查优惠券数据
SELECT * FROM camping_coupons;

-- 应该看到两条记录：
-- 1. 新人专享券 (20元)
-- 2. 放肆趣玩券 (30元)
```

## ⚠️ 注意事项

1. 如果某个步骤执行失败，请检查错误信息
2. 确保数据库用户有足够的权限创建表
3. 如果表已存在，SQL会自动跳过创建
4. 执行完成后，前端优惠券功能即可正常使用

## 🆘 常见问题

**Q: 执行时提示语法错误？**
A: 请确保复制完整的SQL语句，包括分号结尾

**Q: 提示表已存在？**
A: 这是正常的，SQL使用了 `IF NOT EXISTS` 语法

**Q: 优惠券不显示？**
A: 请检查第四步是否成功执行，确保有初始数据

## 📞 技术支持

如果遇到问题，请提供：
1. 具体的错误信息
2. 执行到哪一步出错
3. 数据库版本信息
