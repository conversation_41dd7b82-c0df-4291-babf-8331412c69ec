<?php
/**
 * 设备检测和IP地理位置测试页面
 */

require_once 'login_logger.php';

// 获取当前用户的信息
$current_ip = getUserRealIP();
$current_user_agent = $_SERVER['HTTP_USER_AGENT'] ?? '';
$device_info = parseUserAgent($current_user_agent);
$location = getLocationFromIP($current_ip);

?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>设备检测测试 - 趣玩星球</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            border-radius: 12px;
            padding: 24px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .title {
            color: #333;
            border-bottom: 2px solid #40E0D0;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }
        .info-grid {
            display: grid;
            grid-template-columns: 1fr 2fr;
            gap: 10px;
            margin-bottom: 15px;
        }
        .label {
            font-weight: 600;
            color: #666;
        }
        .value {
            color: #333;
            background: #f8f9fa;
            padding: 8px 12px;
            border-radius: 6px;
            font-family: monospace;
        }
        .wechat-badge {
            background: #07C160;
            color: white;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            margin-left: 8px;
        }
        .test-section {
            margin-top: 30px;
        }
        .test-button {
            background: #40E0D0;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            margin: 5px;
        }
        .test-button:hover {
            background: #36C5B5;
        }
        .result {
            background: #e8f5e5;
            border: 1px solid #4caf50;
            border-radius: 6px;
            padding: 15px;
            margin-top: 15px;
        }
        .error {
            background: #ffeaea;
            border: 1px solid #f44336;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="title">
            设备检测测试
            <?php if ($device_info['is_wechat']): ?>
                <span class="wechat-badge">微信浏览器</span>
            <?php endif; ?>
        </h1>
        
        <h3>当前设备信息</h3>
        <div class="info-grid">
            <div class="label">设备类型:</div>
            <div class="value"><?php echo htmlspecialchars($device_info['device']); ?></div>
            
            <div class="label">操作系统:</div>
            <div class="value"><?php echo htmlspecialchars($device_info['os']); ?></div>
            
            <div class="label">浏览器:</div>
            <div class="value"><?php echo htmlspecialchars($device_info['browser']); ?></div>
            
            <div class="label">是否微信:</div>
            <div class="value"><?php echo $device_info['is_wechat'] ? '是' : '否'; ?></div>
        </div>
        
        <h3>网络信息</h3>
        <div class="info-grid">
            <div class="label">IP地址:</div>
            <div class="value"><?php echo htmlspecialchars($current_ip); ?></div>
            
            <div class="label">地理位置:</div>
            <div class="value"><?php echo htmlspecialchars($location); ?></div>
        </div>
        
        <h3>User Agent</h3>
        <div class="value" style="word-break: break-all; font-size: 12px;">
            <?php echo htmlspecialchars($current_user_agent); ?>
        </div>
    </div>

    <div class="container">
        <h2 class="title">测试功能</h2>
        
        <div class="test-section">
            <h3>IP地理位置测试</h3>
            <button class="test-button" onclick="testIPLocation()">测试IP地理位置API</button>
            <div id="ipTestResult"></div>
        </div>
        
        <div class="test-section">
            <h3>设备信息测试</h3>
            <button class="test-button" onclick="testDeviceInfo()">测试设备信息解析</button>
            <div id="deviceTestResult"></div>
        </div>
        
        <div class="test-section">
            <h3>登录记录测试</h3>
            <button class="test-button" onclick="testLoginRecord()">模拟登录记录</button>
            <div id="loginTestResult"></div>
        </div>
    </div>

    <div class="container">
        <h2 class="title">常见User Agent示例</h2>
        
        <h4>微信浏览器 (iOS)</h4>
        <div class="value" style="font-size: 11px; margin-bottom: 10px;">
            Mozilla/5.0 (iPhone; CPU iPhone OS 16_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148 MicroMessenger/8.0.29(0x18001d2f) NetType/WIFI Language/zh_CN
        </div>
        
        <h4>微信浏览器 (Android)</h4>
        <div class="value" style="font-size: 11px; margin-bottom: 10px;">
            Mozilla/5.0 (Linux; Android 12; SM-G998B) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/********* Mobile Safari/537.36 MicroMessenger/8.0.29.2240(0x28001D53) Process/tools WeChat/arm64 Weixin NetType/WIFI Language/zh_CN ABI/arm64
        </div>
        
        <h4>Chrome (桌面)</h4>
        <div class="value" style="font-size: 11px; margin-bottom: 10px;">
            Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36
        </div>
        
        <h4>Safari (iPhone)</h4>
        <div class="value" style="font-size: 11px;">
            Mozilla/5.0 (iPhone; CPU iPhone OS 16_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.0 Mobile/15E148 Safari/604.1
        </div>
    </div>

    <script>
        function testIPLocation() {
            const resultDiv = document.getElementById('ipTestResult');
            resultDiv.innerHTML = '<div style="color: #666;">正在测试...</div>';
            
            fetch('test_ip_api.php?ip=<?php echo urlencode($current_ip); ?>')
                .then(response => response.json())
                .then(data => {
                    resultDiv.innerHTML = `
                        <div class="result">
                            <strong>API测试结果:</strong><br>
                            状态: ${data.status || '未知'}<br>
                            国家: ${data.country || '未知'}<br>
                            省份: ${data.regionName || '未知'}<br>
                            城市: ${data.city || '未知'}<br>
                            最终位置: ${data.final_location || '未知'}
                        </div>
                    `;
                })
                .catch(error => {
                    resultDiv.innerHTML = `<div class="result error">API测试失败: ${error.message}</div>`;
                });
        }
        
        function testDeviceInfo() {
            const resultDiv = document.getElementById('deviceTestResult');
            const testUserAgents = [
                'Mozilla/5.0 (iPhone; CPU iPhone OS 16_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148 MicroMessenger/8.0.29',
                'Mozilla/5.0 (Linux; Android 12; SM-G998B) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/********* Mobile Safari/537.36 MicroMessenger/8.0.29',
                'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
            ];
            
            resultDiv.innerHTML = '<div style="color: #666;">正在测试...</div>';
            
            fetch('test_device_api.php', {
                method: 'POST',
                headers: {'Content-Type': 'application/json'},
                body: JSON.stringify({user_agents: testUserAgents})
            })
                .then(response => response.json())
                .then(data => {
                    let html = '<div class="result"><strong>设备信息解析测试:</strong><br>';
                    data.results.forEach((result, index) => {
                        html += `
                            <div style="margin: 10px 0; padding: 10px; background: #f8f9fa; border-radius: 4px;">
                                <strong>测试 ${index + 1}:</strong><br>
                                设备: ${result.device}<br>
                                系统: ${result.os}<br>
                                浏览器: ${result.browser}<br>
                                微信: ${result.is_wechat ? '是' : '否'}
                            </div>
                        `;
                    });
                    html += '</div>';
                    resultDiv.innerHTML = html;
                })
                .catch(error => {
                    resultDiv.innerHTML = `<div class="result error">设备信息测试失败: ${error.message}</div>`;
                });
        }
        
        function testLoginRecord() {
            const resultDiv = document.getElementById('loginTestResult');
            resultDiv.innerHTML = '<div style="color: #666;">正在测试...</div>';
            
            fetch('test_login_record.php', {
                method: 'POST',
                headers: {'Content-Type': 'application/json'},
                body: JSON.stringify({test: true})
            })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        resultDiv.innerHTML = `
                            <div class="result">
                                <strong>登录记录测试成功!</strong><br>
                                记录ID: ${data.record_id}<br>
                                IP: ${data.ip}<br>
                                位置: ${data.location}<br>
                                设备: ${data.device}
                            </div>
                        `;
                    } else {
                        resultDiv.innerHTML = `<div class="result error">登录记录测试失败: ${data.message}</div>`;
                    }
                })
                .catch(error => {
                    resultDiv.innerHTML = `<div class="result error">登录记录测试失败: ${error.message}</div>`;
                });
        }
    </script>
</body>
</html>
