<?php
/**
 * 快速登录token处理
 * 处理基于token的快速登录
 */

// 引入session配置
require_once '../../sql/session_config.php';
initLoginSession();

// 引入数据库配置
require_once '../../sql/db_config.php';

// 设置响应类型为JSON
header('Content-Type: application/json');

// 只允许POST请求
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode(['success' => false, 'message' => '请求方法不允许']);
    exit;
}

// 获取请求数据
$input = json_decode(file_get_contents('php://input'), true);

if (!isset($input['action']) || $input['action'] !== 'quick_login') {
    echo json_encode(['success' => false, 'message' => '无效的请求']);
    exit;
}

// 检查是否有快速登录token
if (!isset($_COOKIE['quick_login_token'])) {
    echo json_encode(['success' => false, 'message' => '未找到快速登录信息']);
    exit;
}

/**
 * 获取用户封号详细信息
 */
function getBanInfo($pdo, $user_id) {
    try {
        // 查询最新的封号记录
        $stmt = $pdo->prepare("
            SELECT reason, admin_name, created_at, end_time
            FROM user_bans
            WHERE user_id = ? AND status = 'active'
            AND (end_time IS NULL OR end_time > NOW())
            ORDER BY created_at DESC
            LIMIT 1
        ");
        $stmt->execute([$user_id]);
        $banRecord = $stmt->fetch(PDO::FETCH_ASSOC);

        if ($banRecord) {
            return [
                'reason' => $banRecord['reason'] ?: '违反平台规定',
                'admin_name' => $banRecord['admin_name'] ?: '系统管理员',
                'ban_time' => $banRecord['created_at'],
                'end_time' => $banRecord['end_time'] ?: null,
                'is_permanent' => empty($banRecord['end_time'])
            ];
        }
    } catch (PDOException $e) {
        // 如果user_bans表不存在，返回默认信息
        error_log("获取封号信息失败: " . $e->getMessage());
    }

    // 默认封号信息
    return [
        'reason' => '违反平台规定',
        'admin_name' => '系统管理员',
        'ban_time' => date('Y-m-d H:i:s'),
        'end_time' => null,
        'is_permanent' => true
    ];
}

try {
    $pdo = getDbConnection();

    // 验证token并获取用户信息
    $stmt = $pdo->prepare("
        SELECT qlt.user_info, qlt.expires_at, u.id, u.username, u.quwan_id, u.status
        FROM quick_login_tokens qlt
        JOIN users u ON qlt.user_id = u.id
        WHERE qlt.token = ? AND qlt.expires_at > NOW()
    ");
    $stmt->execute([$_COOKIE['quick_login_token']]);
    $result = $stmt->fetch(PDO::FETCH_ASSOC);

    if (!$result) {
        // token无效或已过期
        echo json_encode(['success' => false, 'message' => '快速登录已过期，请重新登录']);
        exit;
    }

    // 检查用户状态
    if ($result['status'] === 'banned') {
        // 获取封号详细信息
        $banInfo = getBanInfo($pdo, $result['id']);

        echo json_encode([
            'success' => false,
            'message' => '账户已被封禁',
            'banned' => true,
            'banInfo' => $banInfo
        ]);
        exit;
    } elseif ($result['status'] !== 'active') {
        echo json_encode(['success' => false, 'message' => '账号已被禁用，请联系客服']);
        exit;
    }

    // 设置用户登录状态
    $user = [
        'id' => $result['id'],
        'username' => $result['username'],
        'quwan_id' => $result['quwan_id']
    ];

    setUserLoginSession($user);

    // 更新用户最后登录时间
    $stmt = $pdo->prepare("UPDATE users SET last_login = NOW() WHERE id = ?");
    $stmt->execute([$result['id']]);

    // 更新token的最后使用时间
    $stmt = $pdo->prepare("UPDATE quick_login_tokens SET updated_at = NOW() WHERE token = ?");
    $stmt->execute([$_COOKIE['quick_login_token']]);

    // 记录登录日志
    require_once 'login_logger.php';
    recordUserLoginLog($pdo, $result['id'], 'quick_login', 'success');

    echo json_encode([
        'success' => true,
        'message' => '快速登录成功',
        'user' => [
            'id' => $result['id'],
            'username' => $result['username'],
            'quwan_id' => $result['quwan_id']
        ]
    ]);

} catch (Exception $e) {
    error_log('快速登录错误: ' . $e->getMessage());
    echo json_encode(['success' => false, 'message' => '系统错误，请重试']);
}
?>
