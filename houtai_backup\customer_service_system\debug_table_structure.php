<?php
// 调试表结构
header('Content-Type: text/html; charset=UTF-8');
ini_set('default_charset', 'UTF-8');

session_start();

// 检查登录状态
if (!isset($_SESSION['cs_logged_in']) || !$_SESSION['cs_logged_in']) {
    echo '<h1>请先登录客服系统</h1>';
    exit;
}

require_once '../db_config.php';

echo '<h1>详细表结构调试</h1>';

try {
    $pdo = getDbConnection();
    
    // 检查 customer_service_messages 表的完整结构
    echo '<h2>customer_service_messages 表完整结构</h2>';
    $stmt = $pdo->query("DESCRIBE customer_service_messages");
    $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo '<table border="1" style="border-collapse: collapse; width: 100%;">';
    echo '<tr><th>字段名</th><th>类型</th><th>是否为空</th><th>键</th><th>默认值</th><th>额外</th></tr>';
    
    foreach ($columns as $column) {
        $rowStyle = '';
        if ($column['Null'] === 'NO' && $column['Default'] === null && $column['Extra'] !== 'auto_increment') {
            $rowStyle = 'style="background-color: #ffcccc;"'; // 标记有问题的字段
        }
        
        echo '<tr ' . $rowStyle . '>';
        echo '<td><strong>' . htmlspecialchars($column['Field']) . '</strong></td>';
        echo '<td>' . htmlspecialchars($column['Type']) . '</td>';
        echo '<td>' . htmlspecialchars($column['Null']) . '</td>';
        echo '<td>' . htmlspecialchars($column['Key']) . '</td>';
        echo '<td>' . htmlspecialchars($column['Default'] ?? 'NULL') . '</td>';
        echo '<td>' . htmlspecialchars($column['Extra']) . '</td>';
        echo '</tr>';
    }
    echo '</table>';
    
    echo '<p><strong>红色背景的字段</strong>：不允许NULL且没有默认值，可能导致插入失败</p>';
    
    // 检查表的创建语句
    echo '<h2>表创建语句</h2>';
    $stmt = $pdo->query("SHOW CREATE TABLE customer_service_messages");
    $createTable = $stmt->fetch();
    echo '<pre>' . htmlspecialchars($createTable['Create Table']) . '</pre>';
    
    // 测试插入语句
    echo '<h2>测试插入语句</h2>';
    
    if ($_POST['test_insert'] ?? false) {
        try {
            $testSessionId = 'test_debug_' . time();
            $testMessage = '测试消息';
            
            echo '<p>尝试插入测试数据...</p>';
            
            $stmt = $pdo->prepare("
                INSERT INTO customer_service_messages
                (session_id, sender_type, sender_id, sender_name, message_type, content, created_at)
                VALUES (?, 'system', NULL, '系统', 'system', ?, NOW())
            ");
            
            $stmt->execute([$testSessionId, $testMessage]);
            
            echo '<p style="color: green;">✓ 插入成功！</p>';
            
            // 删除测试数据
            $stmt = $pdo->prepare("DELETE FROM customer_service_messages WHERE session_id = ?");
            $stmt->execute([$testSessionId]);
            echo '<p>测试数据已清理</p>';
            
        } catch (Exception $e) {
            echo '<p style="color: red;">✗ 插入失败：' . $e->getMessage() . '</p>';
            
            // 尝试不同的插入方式
            echo '<h3>尝试指定所有字段的插入</h3>';
            try {
                $stmt = $pdo->prepare("
                    INSERT INTO customer_service_messages
                    (session_id, sender_type, sender_id, sender_name, message_type, content, attachment_url, attachment_type, attachment_size, is_read, created_at, updated_at)
                    VALUES (?, 'system', NULL, '系统', 'system', ?, NULL, NULL, NULL, 0, NOW(), NOW())
                ");
                
                $stmt->execute([$testSessionId, $testMessage]);
                echo '<p style="color: green;">✓ 完整字段插入成功！</p>';
                
                // 删除测试数据
                $stmt = $pdo->prepare("DELETE FROM customer_service_messages WHERE session_id = ?");
                $stmt->execute([$testSessionId]);
                echo '<p>测试数据已清理</p>';
                
            } catch (Exception $e2) {
                echo '<p style="color: red;">✗ 完整字段插入也失败：' . $e2->getMessage() . '</p>';
            }
        }
    }
    
    // 检查MySQL模式
    echo '<h2>MySQL SQL模式</h2>';
    $stmt = $pdo->query("SELECT @@sql_mode");
    $sqlMode = $stmt->fetch();
    echo '<p>当前SQL模式：<code>' . htmlspecialchars($sqlMode['@@sql_mode']) . '</code></p>';
    
    if (strpos($sqlMode['@@sql_mode'], 'STRICT_TRANS_TABLES') !== false) {
        echo '<p style="color: orange;">⚠️ 检测到严格模式，这可能导致插入问题</p>';
    }
    
} catch (Exception $e) {
    echo '<p style="color: red;">错误：' . $e->getMessage() . '</p>';
}
?>

<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>表结构调试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        table { margin: 10px 0; }
        th, td { padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
        pre { background: #f5f5f5; padding: 10px; border-radius: 5px; overflow-x: auto; }
        button { padding: 8px 16px; background: #007cba; color: white; border: none; border-radius: 3px; cursor: pointer; }
    </style>
</head>
<body>
    <form method="POST">
        <button type="submit" name="test_insert" value="1">测试插入语句</button>
    </form>
    
    <hr>
    <p><a href="simple_test.php">返回简化测试</a></p>
</body>
</html>
