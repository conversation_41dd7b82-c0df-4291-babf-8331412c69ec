<?php
/**
 * 申诉提交处理
 */

// 设置响应头
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST');
header('Access-Control-Allow-Headers: Content-Type');

// 引入数据库配置
require_once '../../sql/db_config.php';

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode(['success' => false, 'message' => '请求方法不允许']);
    exit;
}

try {
    $pdo = getDbConnection();

    if (!$pdo) {
        error_log("申诉提交：数据库连接失败");
        echo json_encode(['success' => false, 'message' => '数据库连接失败，请稍后重试']);
        exit;
    }

    // 获取表单数据
    $phone = trim($_POST['phone'] ?? '');
    $appeal_type = trim($_POST['appeal_type'] ?? '');
    $reason = trim($_POST['reason'] ?? '');
    $email = trim($_POST['email'] ?? '');

    // 记录调试信息
    error_log("申诉提交数据: phone=$phone, appeal_type=$appeal_type, email=$email");

    // 验证必填字段
    if (empty($phone) || empty($appeal_type) || empty($reason) || empty($email)) {
        echo json_encode(['success' => false, 'message' => '请填写完整信息']);
        exit;
    }

    // 验证手机号格式
    if (!preg_match('/^1[3-9]\d{9}$/', $phone)) {
        echo json_encode(['success' => false, 'message' => '手机号格式不正确']);
        exit;
    }

    // 验证邮箱格式
    if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
        echo json_encode(['success' => false, 'message' => '邮箱格式不正确']);
        exit;
    }

    // 简化的重复申诉检查
    try {
        $stmt = $pdo->prepare("
            SELECT id FROM user_appeals
            WHERE phone = ? AND status IN ('pending', 'processing')
            ORDER BY created_at DESC
            LIMIT 1
        ");
        $stmt->execute([$phone]);
        if ($stmt->fetch()) {
            echo json_encode(['success' => false, 'message' => '您已有申诉正在处理中，请勿重复提交']);
            exit;
        }
    } catch (PDOException $e) {
        error_log("检查重复申诉失败: " . $e->getMessage());
        // 如果检查失败，继续执行
    }

    // 检查申诉表是否存在，如果不存在则创建
    try {
        $pdo->exec("
            CREATE TABLE IF NOT EXISTS user_appeals (
                id INT AUTO_INCREMENT PRIMARY KEY,
                phone VARCHAR(20) NOT NULL,
                appeal_type VARCHAR(50) NOT NULL,
                reason TEXT NOT NULL,
                email VARCHAR(255) NOT NULL,
                evidence_files JSON NULL,
                status VARCHAR(20) DEFAULT 'pending',
                admin_reply TEXT NULL,
                admin_id INT NULL,
                admin_name VARCHAR(100) NULL,
                processed_at TIMESTAMP NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                INDEX idx_phone (phone),
                INDEX idx_status (status),
                INDEX idx_created_at (created_at)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
        ");
        error_log("申诉表创建/检查成功");
    } catch (Exception $e) {
        error_log("创建申诉表失败: " . $e->getMessage());
        throw $e;
    }

    // 处理文件上传
    $evidence_files = [];
    $upload_dir = '../../uploads/appeals/';

    // 创建上传目录
    if (!is_dir($upload_dir)) {
        mkdir($upload_dir, 0755, true);
    }

    // 处理上传的文件
    for ($i = 0; $i < 3; $i++) {
        $file_key = "evidence_$i";
        if (isset($_FILES[$file_key]) && $_FILES[$file_key]['error'] === UPLOAD_ERR_OK) {
            $file = $_FILES[$file_key];

            // 验证文件类型
            $allowed_types = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
            if (!in_array($file['type'], $allowed_types)) {
                continue;
            }

            // 验证文件大小（最大5MB）
            if ($file['size'] > 5 * 1024 * 1024) {
                continue;
            }

            // 生成唯一文件名
            $extension = pathinfo($file['name'], PATHINFO_EXTENSION);
            $filename = uniqid() . '_' . time() . '.' . $extension;
            $filepath = $upload_dir . $filename;

            // 移动文件
            if (move_uploaded_file($file['tmp_name'], $filepath)) {
                $evidence_files[] = [
                    'original_name' => $file['name'],
                    'filename' => $filename,
                    'filepath' => $filepath,
                    'size' => $file['size']
                ];
            }
        }
    }

    // 插入申诉记录
    try {
        error_log("准备插入申诉记录: phone=$phone, appeal_type=$appeal_type");

        $stmt = $pdo->prepare("
            INSERT INTO user_appeals (phone, appeal_type, reason, email, evidence_files, status)
            VALUES (?, ?, ?, ?, ?, 'pending')
        ");

        $evidence_json = empty($evidence_files) ? null : json_encode($evidence_files);
        $result = $stmt->execute([$phone, $appeal_type, $reason, $email, $evidence_json]);

        if (!$result) {
            error_log("申诉记录插入失败");
            throw new Exception("申诉记录插入失败");
        }

        $appeal_id = $pdo->lastInsertId();
        error_log("申诉记录插入成功，ID: $appeal_id");
    } catch (PDOException $e) {
        error_log("插入申诉记录数据库错误: " . $e->getMessage());
        throw $e;
    }

    // 记录申诉日志
    try {
        // 先尝试创建日志表
        $pdo->exec("
            CREATE TABLE IF NOT EXISTS appeal_logs (
                id INT AUTO_INCREMENT PRIMARY KEY,
                appeal_id INT NOT NULL,
                action VARCHAR(100) NOT NULL,
                description TEXT,
                admin_id INT NULL,
                admin_name VARCHAR(100) NULL,
                ip_address VARCHAR(45),
                user_agent TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                INDEX idx_appeal_id (appeal_id)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ");

        // 记录提交日志
        $stmt = $pdo->prepare("
            INSERT INTO appeal_logs (appeal_id, action, description, ip_address, user_agent)
            VALUES (?, 'submit', '用户提交申诉', ?, ?)
        ");

        $ip_address = $_SERVER['HTTP_X_FORWARDED_FOR'] ?? $_SERVER['REMOTE_ADDR'] ?? '';
        $user_agent = $_SERVER['HTTP_USER_AGENT'] ?? '';

        $stmt->execute([$appeal_id, $ip_address, $user_agent]);
    } catch (Exception $e) {
        // 日志记录失败不影响主流程
        error_log("申诉日志记录失败: " . $e->getMessage());
    }

    echo json_encode([
        'success' => true,
        'message' => '申诉提交成功',
        'appeal_id' => $appeal_id
    ]);

} catch (PDOException $e) {
    error_log("申诉提交数据库错误: " . $e->getMessage());
    echo json_encode(['success' => false, 'message' => '系统错误，请稍后重试']);
} catch (Exception $e) {
    error_log("申诉提交错误: " . $e->getMessage());
    echo json_encode(['success' => false, 'message' => '提交失败，请重试']);
}
?>
