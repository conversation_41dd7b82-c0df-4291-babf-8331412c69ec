<?php
session_start();
header('Content-Type: application/json; charset=utf-8');

require_once __DIR__ . '/../../../config.php'; // 引入数据库配置文件

$response = ['success' => false, 'message' => '未知错误。'];

// 检查用户是否登录
if (!isset($_SESSION['user_id'])) {
    $response['message'] = '用户未登录，请先登录。';
    echo json_encode($response);
    exit;
}

$user_id = $_SESSION['user_id'];

// 仅处理POST请求
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    $response['message'] = '无效的请求方法。';
    echo json_encode($response);
    exit;
}

// --- 数据接收和初步清理 ---
$real_name = trim($_POST['real_name'] ?? '');
$id_number = trim($_POST['id_number'] ?? '');
$contact_info = trim($_POST['contact_info'] ?? '');
$game_interests = trim($_POST['game_interests'] ?? '');
$self_description = trim($_POST['self_description'] ?? '');
$agree_terms = isset($_POST['agree_terms']);

// --- 服务端验证 ---
$errors = [];
if (empty($real_name)) {
    $errors['real_name'] = '真实姓名不能为空。';
}
if (!preg_match('/^\d{17}(\d|X|x)$/', $id_number)) {
    $errors['id_number'] = '身份证号码格式不正确。';
}
if (empty($self_description)) {
    $errors['self_description'] = '自我介绍不能为空。';
}
if (mb_strlen($self_description, 'UTF-8') > 500) { // 假设最大长度500字
    $errors['self_description'] = '自我介绍过长，请保持在500字以内。';
}
if (!$agree_terms) {
    $errors['agree_terms'] = '您必须同意服务协议。';
}

// 文件验证
$allowed_image_types = ['image/jpeg', 'image/png'];
$allowed_audio_types = ['audio/mpeg', 'audio/wav', 'audio/mp3']; // audio/mpeg for mp3
$max_file_size = 2 * 1024 * 1024; // 2MB

$upload_paths = [];
$upload_dir_base = __DIR__ . '/../../../uploads/companion_applications/user_' . $user_id . '/';
if (!is_dir($upload_dir_base) && !mkdir($upload_dir_base, 0755, true)) {
    $response['message'] = '创建上传目录失败，请联系管理员。(' . $upload_dir_base . ')';
    echo json_encode($response);
    exit;
}

// 身份证正面
if (isset($_FILES['id_card_front_url']) && $_FILES['id_card_front_url']['error'] === UPLOAD_ERR_OK) {
    if ($_FILES['id_card_front_url']['size'] > $max_file_size) {
        $errors['id_card_front_url'] = '身份证正面照片大小不能超过2MB。';
    } elseif (!in_array($_FILES['id_card_front_url']['type'], $allowed_image_types)) {
        $errors['id_card_front_url'] = '身份证正面照片格式无效，请上传JPG或PNG格式。';
    } else {
        $file_ext_front = strtolower(pathinfo($_FILES['id_card_front_url']['name'], PATHINFO_EXTENSION));
        $filename_front = uniqid('id_front_', true) . '.' . $file_ext_front;
        if (move_uploaded_file($_FILES['id_card_front_url']['tmp_name'], $upload_dir_base . $filename_front)) {
            $upload_paths['id_card_front_url'] = 'uploads/companion_applications/user_' . $user_id . '/' . $filename_front;
        } else {
            $errors['id_card_front_url'] = '上传身份证正面照片失败。';
        }
    }
} else {
    $errors['id_card_front_url'] = '请上传身份证正面照片。';
}

// 身份证背面
if (isset($_FILES['id_card_back_url']) && $_FILES['id_card_back_url']['error'] === UPLOAD_ERR_OK) {
    if ($_FILES['id_card_back_url']['size'] > $max_file_size) {
        $errors['id_card_back_url'] = '身份证背面照片大小不能超过2MB。';
    } elseif (!in_array($_FILES['id_card_back_url']['type'], $allowed_image_types)) {
        $errors['id_card_back_url'] = '身份证背面照片格式无效，请上传JPG或PNG格式。';
    } else {
        $file_ext_back = strtolower(pathinfo($_FILES['id_card_back_url']['name'], PATHINFO_EXTENSION));
        $filename_back = uniqid('id_back_', true) . '.' . $file_ext_back;
        if (move_uploaded_file($_FILES['id_card_back_url']['tmp_name'], $upload_dir_base . $filename_back)) {
            $upload_paths['id_card_back_url'] = 'uploads/companion_applications/user_' . $user_id . '/' . $filename_back;
        } else {
            $errors['id_card_back_url'] = '上传身份证背面照片失败。';
        }
    }
} else {
    $errors['id_card_back_url'] = '请上传身份证背面照片。';
}

// 语音介绍 (选填)
if (isset($_FILES['voice_sample_url']) && $_FILES['voice_sample_url']['error'] === UPLOAD_ERR_OK && $_FILES['voice_sample_url']['size'] > 0) {
    if ($_FILES['voice_sample_url']['size'] > $max_file_size) {
        $errors['voice_sample_url'] = '语音介绍文件大小不能超过2MB。';
    } elseif (!in_array($_FILES['voice_sample_url']['type'], $allowed_audio_types)) {
        $errors['voice_sample_url'] = '语音介绍文件格式无效，请上传MP3或WAV格式。 (Type: ' . $_FILES['voice_sample_url']['type'] . ')';
    } else {
        $file_ext_voice = strtolower(pathinfo($_FILES['voice_sample_url']['name'], PATHINFO_EXTENSION));
        $filename_voice = uniqid('voice_', true) . '.' . $file_ext_voice;
        if (move_uploaded_file($_FILES['voice_sample_url']['tmp_name'], $upload_dir_base . $filename_voice)) {
            $upload_paths['voice_sample_url'] = 'uploads/companion_applications/user_' . $user_id . '/' . $filename_voice;
        } else {
            $errors['voice_sample_url'] = '上传语音介绍失败。';
        }
    }
} else {
    $upload_paths['voice_sample_url'] = null; // 明确设为null如果未上传或上传失败（非必需错误）
}

if (!empty($errors)) {
    $response['message'] = '提交的信息有误，请检查。';
    $response['errors'] = $errors;
    echo json_encode($response);
    exit;
}

// --- 数据库操作 ---
try {
    $dsn = "mysql:host={$db_config['host']};dbname={$db_config['dbname']};charset={$db_config['charset']};port={$db_config['port']}";
    $options = [
        PDO::ATTR_ERRMODE            => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
        PDO::ATTR_EMULATE_PREPARES   => false,
    ];
    $pdo = new PDO($dsn, $db_config['username'], $db_config['password'], $options);

    $pdo->beginTransaction();

    // 检查是否已有待处理或已通过的申请，避免重复提交
    $stmt_check_existing = $pdo->prepare("SELECT id FROM companion_applications WHERE user_id = :user_id AND status IN ('pending', 'approved')");
    $stmt_check_existing->execute([':user_id' => $user_id]);
    if ($stmt_check_existing->fetch()) {
        $response['message'] = '您已有正在审核或已通过的申请，请勿重复提交。';
        $pdo->rollBack(); // 回滚事务
        echo json_encode($response);
        exit;
    }

    // 1. 插入到 companion_applications 表
    $stmt_app = $pdo->prepare("\n        INSERT INTO companion_applications \n        (user_id, real_name, id_number, game_interests, self_description, voice_sample_url, id_card_front_url, id_card_back_url, contact_info, status, application_date)\n        VALUES \n        (:user_id, :real_name, :id_number, :game_interests, :self_description, :voice_sample_url, :id_card_front_url, :id_card_back_url, :contact_info, 'pending', NOW())\n    ");
    $stmt_app->execute([
        ':user_id' => $user_id,
        ':real_name' => $real_name,
        ':id_number' => $id_number,
        ':game_interests' => $game_interests,
        ':self_description' => $self_description,
        ':voice_sample_url' => $upload_paths['voice_sample_url'],
        ':id_card_front_url' => $upload_paths['id_card_front_url'],
        ':id_card_back_url' => $upload_paths['id_card_back_url'],
        ':contact_info' => $contact_info
    ]);
    $application_id = $pdo->lastInsertId();

    // 2. 更新或插入 companion_verification 表
    $stmt_cv_check = $pdo->prepare("SELECT id FROM companion_verification WHERE user_id = :user_id");
    $stmt_cv_check->execute([':user_id' => $user_id]);
    if ($stmt_cv_check->fetch()) {
        $stmt_cv_update = $pdo->prepare("UPDATE companion_verification SET verification_status = 'pending', application_id = :application_id, last_updated = NOW() WHERE user_id = :user_id");
        $stmt_cv_update->execute([':user_id' => $user_id, ':application_id' => $application_id]);
    } else {
        $stmt_cv_insert = $pdo->prepare("INSERT INTO companion_verification (user_id, verification_status, application_id, last_updated) VALUES (:user_id, 'pending', :application_id, NOW())");
        $stmt_cv_insert->execute([':user_id' => $user_id, ':application_id' => $application_id]);
    }

    $pdo->commit();

    $response['success'] = true;
    $response['message'] = '您的陪玩申请已成功提交，我们将在1-3个工作日内完成审核。';

} catch (PDOException $e) {
    if ($pdo->inTransaction()) {
        $pdo->rollBack();
    }
    error_log("陪玩申请提交API数据库错误 (User: {$user_id}): " . $e->getMessage());
    $response['message'] = '数据库操作失败，请稍后再试或联系客服。';
    // $response['db_error'] = $e->getMessage(); // 开发时可取消注释
} catch (Exception $e) {
    error_log("陪玩申请提交API一般错误 (User: {$user_id}): " . $e->getMessage());
    $response['message'] = '发生意外错误，请稍后再试。';
}

echo json_encode($response);
?>
