<?php
/**
 * 获取用户IP记录
 * 趣玩星球管理后台
 */

session_start();

// 检查登录状态
if (!isset($_SESSION['admin_id'])) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => '未登录']);
    exit;
}

require_once '../db_config.php';

// 检查login_logger.php是否存在
$login_logger_path = '../../frontend/login/login_logger.php';
if (file_exists($login_logger_path)) {
    require_once $login_logger_path;
} else {
    // 如果文件不存在，定义基本函数
    function parseUserAgent($userAgent) {
        $device = '未知设备';
        $os = '未知系统';
        $browser = '未知浏览器';
        $is_wechat = false;

        if (empty($userAgent)) {
            return compact('device', 'os', 'browser', 'is_wechat');
        }

        // 检测微信
        if (strpos($userAgent, 'MicroMessenger') !== false) {
            $is_wechat = true;
            $browser = '微信浏览器';
        }

        // 检测设备
        if (preg_match('/iPhone/i', $userAgent)) {
            $device = 'iPhone';
        } elseif (preg_match('/iPad/i', $userAgent)) {
            $device = 'iPad';
        } elseif (preg_match('/Android/i', $userAgent)) {
            $device = preg_match('/Mobile/i', $userAgent) ? 'Android手机' : 'Android平板';
        } elseif (preg_match('/Windows/i', $userAgent)) {
            $device = 'Windows电脑';
        } elseif (preg_match('/Mac/i', $userAgent)) {
            $device = 'Mac电脑';
        }

        // 检测操作系统
        if (preg_match('/Windows NT 10/i', $userAgent)) {
            $os = 'Windows 10';
        } elseif (preg_match('/Windows NT 6.3/i', $userAgent)) {
            $os = 'Windows 8.1';
        } elseif (preg_match('/Windows NT 6.1/i', $userAgent)) {
            $os = 'Windows 7';
        } elseif (preg_match('/Mac OS X ([\d_]+)/i', $userAgent, $matches)) {
            $os = 'macOS ' . str_replace('_', '.', $matches[1]);
        } elseif (preg_match('/Android ([\d.]+)/i', $userAgent, $matches)) {
            $os = 'Android ' . $matches[1];
        } elseif (preg_match('/iPhone OS ([\d_]+)/i', $userAgent, $matches)) {
            $os = 'iOS ' . str_replace('_', '.', $matches[1]);
        }

        // 检测浏览器
        if (!$is_wechat) {
            if (preg_match('/Chrome\/([\d.]+)/i', $userAgent, $matches)) {
                $browser = 'Chrome ' . $matches[1];
            } elseif (preg_match('/Firefox\/([\d.]+)/i', $userAgent, $matches)) {
                $browser = 'Firefox ' . $matches[1];
            } elseif (preg_match('/Safari\/([\d.]+)/i', $userAgent, $matches)) {
                $browser = 'Safari';
            } elseif (preg_match('/Edge\/([\d.]+)/i', $userAgent, $matches)) {
                $browser = 'Edge ' . $matches[1];
            }
        }

        return compact('device', 'os', 'browser', 'is_wechat');
    }

    function getLocationFromIP($ip) {
        if (empty($ip) || $ip === '127.0.0.1' || $ip === '::1') {
            return '本地';
        }

        // 简单的IP地理位置判断
        if (preg_match('/^192\.168\./', $ip) || preg_match('/^10\./', $ip) || preg_match('/^172\.(1[6-9]|2[0-9]|3[01])\./', $ip)) {
            return '内网IP';
        }

        return '未知地区';
    }
}

$response = ['success' => false, 'message' => '', 'records' => []];

$user_id = intval($_GET['user_id'] ?? 0);

if (!$user_id) {
    $response['message'] = '用户ID无效';
    echo json_encode($response);
    exit;
}

try {
    $pdo = getDbConnection();

    // 调试信息：检查用户ID
    error_log("查询用户 {$user_id} 的登录记录");

    // 获取用户登录记录（增加更多记录，确保实时性）
    $stmt = $pdo->prepare("
        SELECT
            ip_address,
            MAX(login_time) as login_time,
            user_agent,
            location,
            COUNT(*) as login_count
        FROM login_logs
        WHERE user_id = ? AND status = 'success'
        GROUP BY ip_address, user_agent
        ORDER BY MAX(login_time) DESC
        LIMIT 50
    ");
    $stmt->execute([$user_id]);
    $logs = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // 调试信息：记录查询结果
    error_log("用户 {$user_id} 查询到 " . count($logs) . " 条登录记录");

    // 如果没有记录，检查数据库中是否有任何登录记录
    if (empty($logs)) {
        // 检查表是否存在
        $tableStmt = $pdo->query("SHOW TABLES LIKE 'login_logs'");
        if ($tableStmt->rowCount() == 0) {
            error_log("login_logs表不存在");
            $response['message'] = 'login_logs表不存在，请先执行数据库创建脚本';
            echo json_encode($response);
            exit;
        }

        $totalStmt = $pdo->query("SELECT COUNT(*) as total FROM login_logs");
        $total = $totalStmt->fetch()['total'];
        error_log("数据库中总共有 {$total} 条登录记录");

        // 检查该用户是否存在
        $userStmt = $pdo->prepare("SELECT id, username, quwan_id FROM users WHERE id = ?");
        $userStmt->execute([$user_id]);
        $userExists = $userStmt->fetch();
        if ($userExists) {
            error_log("用户 {$user_id} 存在: {$userExists['username']} ({$userExists['quwan_id']})");

            // 如果用户存在但没有登录记录，添加一条测试记录
            if ($total == 0) {
                error_log("数据库为空，为用户 {$user_id} 添加测试登录记录");
                $testStmt = $pdo->prepare("
                    INSERT INTO login_logs (user_id, login_time, ip_address, user_agent, status, login_type, location)
                    VALUES (?, NOW(), ?, ?, 'success', 'normal_login', ?)
                ");
                $testStmt->execute([
                    $user_id,
                    '*************',
                    'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
                    '北京市'
                ]);

                // 重新查询
                $stmt->execute([$user_id]);
                $logs = $stmt->fetchAll(PDO::FETCH_ASSOC);
                error_log("添加测试记录后，用户 {$user_id} 现在有 " . count($logs) . " 条记录");
            }
        } else {
            error_log("用户 {$user_id} 不存在");
        }
    }

    $records = [];
    foreach ($logs as $log) {
        // 解析User Agent
        $device_info = parseUserAgent($log['user_agent'] ?? '');
        $device_icon = getDeviceIcon($log['user_agent'] ?? '');

        // 优先使用数据库中的location，如果没有则重新获取
        $location = !empty($log['location']) ? $log['location'] : getLocationFromIP($log['ip_address']);

        $records[] = [
            'ip_address' => $log['ip_address'],
            'login_time' => date('Y-m-d H:i:s', strtotime($log['login_time'])),
            'login_count' => intval($log['login_count']),
            'location' => $location,
            'device' => $device_info['device'],
            'device_icon' => $device_icon,
            'os' => $device_info['os'],
            'browser' => $device_info['browser']
        ];
    }

    $response['success'] = true;
    $response['records'] = $records;

} catch (PDOException $e) {
    $response['message'] = '数据库错误：' . $e->getMessage();
}

// 设备图标函数
function getDeviceIcon($userAgent) {
    if (empty($userAgent)) {
        return 'question';
    }

    if (preg_match('/iPhone/i', $userAgent)) {
        return 'mobile-alt';
    } elseif (preg_match('/iPad/i', $userAgent)) {
        return 'tablet-alt';
    } elseif (preg_match('/Android/i', $userAgent)) {
        if (preg_match('/Mobile/i', $userAgent)) {
            return 'mobile-alt';
        } else {
            return 'tablet-alt';
        }
    } elseif (preg_match('/Mobile|BlackBerry|Windows Phone/i', $userAgent)) {
        return 'mobile-alt';
    } elseif (preg_match('/Mac/i', $userAgent)) {
        return 'laptop';
    } else {
        return 'desktop';
    }
}

// 使用统一的地理位置识别函数（已在login_logger.php中定义）
// 这里不需要重复定义getLocationFromIP函数

// 设置防缓存头部，确保数据实时性
header('Content-Type: application/json');
header('Cache-Control: no-cache, no-store, must-revalidate');
header('Pragma: no-cache');
header('Expires: 0');

echo json_encode($response);
?>
