<?php
/**
 * 检查管理员登录功能
 */

require_once 'db_config.php';

echo "<h2>管理员登录功能检查</h2>";

try {
    $pdo = getDbConnection();
    echo "✅ 数据库连接成功<br><br>";
    
    // 检查admin_users表是否存在
    $stmt = $pdo->query("SHOW TABLES LIKE 'admin_users'");
    if ($stmt->rowCount() > 0) {
        echo "✅ admin_users表存在<br>";
        
        // 检查表结构
        $stmt = $pdo->query("DESCRIBE admin_users");
        $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
        echo "<h3>表结构:</h3>";
        echo "<table border='1' style='border-collapse: collapse;'>";
        echo "<tr><th>字段名</th><th>类型</th><th>是否为空</th><th>键</th><th>默认值</th></tr>";
        foreach ($columns as $column) {
            echo "<tr>";
            echo "<td>" . $column['Field'] . "</td>";
            echo "<td>" . $column['Type'] . "</td>";
            echo "<td>" . $column['Null'] . "</td>";
            echo "<td>" . $column['Key'] . "</td>";
            echo "<td>" . $column['Default'] . "</td>";
            echo "</tr>";
        }
        echo "</table><br>";
        
        // 检查管理员账户
        $stmt = $pdo->query("SELECT id, employee_id, email, name, nickname, username, status FROM admin_users");
        $admins = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo "<h3>现有管理员账户:</h3>";
        if (count($admins) > 0) {
            echo "<table border='1' style='border-collapse: collapse;'>";
            echo "<tr><th>ID</th><th>工号</th><th>邮箱</th><th>姓名</th><th>昵称</th><th>用户名</th><th>状态</th></tr>";
            foreach ($admins as $admin) {
                echo "<tr>";
                echo "<td>" . $admin['id'] . "</td>";
                echo "<td>" . $admin['employee_id'] . "</td>";
                echo "<td>" . $admin['email'] . "</td>";
                echo "<td>" . $admin['name'] . "</td>";
                echo "<td>" . $admin['nickname'] . "</td>";
                echo "<td>" . $admin['username'] . "</td>";
                echo "<td>" . ($admin['status'] ? '启用' : '禁用') . "</td>";
                echo "</tr>";
            }
            echo "</table><br>";
        } else {
            echo "❌ 没有管理员账户<br>";
            echo "<h3>创建测试管理员账户:</h3>";
            
            // 创建测试管理员账户
            $test_password = password_hash('admin123', PASSWORD_DEFAULT);
            $stmt = $pdo->prepare("
                INSERT INTO admin_users (employee_id, email, name, nickname, username, password, status, role_name, department, created_at) 
                VALUES (?, ?, ?, ?, ?, ?, 1, '超级管理员', '总经办', NOW())
            ");
            
            $result = $stmt->execute([
                'ADMIN001',
                '<EMAIL>',
                '系统管理员',
                '管理员',
                'admin',
                $test_password
            ]);
            
            if ($result) {
                echo "✅ 测试管理员账户创建成功<br>";
                echo "工号: ADMIN001<br>";
                echo "邮箱: <EMAIL><br>";
                echo "密码: admin123<br>";
            } else {
                echo "❌ 创建测试管理员账户失败<br>";
            }
        }
        
    } else {
        echo "❌ admin_users表不存在<br>";
        echo "<h3>创建admin_users表:</h3>";
        
        // 创建admin_users表
        $create_table_sql = "
        CREATE TABLE `admin_users` (
          `id` int(11) NOT NULL AUTO_INCREMENT,
          `employee_id` varchar(50) NOT NULL COMMENT '工号',
          `email` varchar(100) DEFAULT NULL COMMENT '邮箱',
          `name` varchar(50) NOT NULL COMMENT '真实姓名',
          `nickname` varchar(50) DEFAULT NULL COMMENT '昵称',
          `username` varchar(50) DEFAULT NULL COMMENT '用户名',
          `password` varchar(255) NOT NULL COMMENT '密码',
          `status` tinyint(1) NOT NULL DEFAULT 1 COMMENT '状态 1启用 0禁用',
          `role_name` varchar(50) DEFAULT '审核员' COMMENT '角色名称',
          `department` varchar(50) DEFAULT '总经办' COMMENT '部门',
          `last_login` datetime DEFAULT NULL COMMENT '最后登录时间',
          `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
          `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
          PRIMARY KEY (`id`),
          UNIQUE KEY `employee_id` (`employee_id`),
          UNIQUE KEY `email` (`email`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='管理员用户表';
        ";
        
        $pdo->exec($create_table_sql);
        echo "✅ admin_users表创建成功<br>";
        
        // 创建测试管理员账户
        $test_password = password_hash('admin123', PASSWORD_DEFAULT);
        $stmt = $pdo->prepare("
            INSERT INTO admin_users (employee_id, email, name, nickname, username, password, status, role_name, department, created_at) 
            VALUES (?, ?, ?, ?, ?, ?, 1, '超级管理员', '总经办', NOW())
        ");
        
        $result = $stmt->execute([
            'ADMIN001',
            '<EMAIL>',
            '系统管理员',
            '管理员',
            'admin',
            $test_password
        ]);
        
        if ($result) {
            echo "✅ 测试管理员账户创建成功<br>";
            echo "<strong>登录信息:</strong><br>";
            echo "工号: ADMIN001<br>";
            echo "邮箱: <EMAIL><br>";
            echo "密码: admin123<br>";
        } else {
            echo "❌ 创建测试管理员账户失败<br>";
        }
    }
    
} catch (PDOException $e) {
    echo "❌ 数据库错误: " . $e->getMessage() . "<br>";
}

echo "<br><h3>测试登录:</h3>";
echo "<a href='login.php' target='_blank'>点击这里测试登录</a><br>";
echo "<br><strong>如果登录成功，请删除此文件以确保安全！</strong>";

?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; }
h2, h3 { color: #333; }
table { margin: 10px 0; }
th, td { padding: 8px; text-align: left; border: 1px solid #ddd; }
th { background-color: #f2f2f2; }
a { color: #007cba; text-decoration: none; }
a:hover { text-decoration: underline; }
</style>
