<?php
session_start();
require_once 'db_config.php';

// 检查登录状态
if (!isset($_SESSION['admin_id'])) {
    header('Location: login.php');
    exit;
}

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        $pdo = getDbConnection();

        $user_id = (int)$_POST['user_id'];
        $action = trim($_POST['action']);
        $reason = trim($_POST['reason']);
        $duration = $_POST['duration'] ?? '';

        // 验证输入
        if (empty($user_id) || empty($action) || empty($reason)) {
            throw new Exception('请填写完整信息');
        }

        // 计算过期时间
        $expire_at = null;
        if ($duration && $duration !== 'permanent') {
            $duration_map = [
                '1h' => '+1 hour',
                '6h' => '+6 hours',
                '12h' => '+12 hours',
                '1d' => '+1 day',
                '3d' => '+3 days',
                '7d' => '+7 days',
                '30d' => '+30 days'
            ];

            if (isset($duration_map[$duration])) {
                $expire_at = date('Y-m-d H:i:s', strtotime($duration_map[$duration]));
            }
        }

        // 获取当前管理员信息
        $admin_id = $_SESSION['admin_id'];
        $admin_name = $_SESSION['admin_name'] ?? '未知管理员';
        $employee_id = $_SESSION['admin_employee_id'] ?? '';
        $department = $_SESSION['admin_department'] ?? '';

        // 获取用户信息
        $stmt = $pdo->prepare("SELECT * FROM users WHERE id = ?");
        $stmt->execute([$user_id]);
        $user = $stmt->fetch(PDO::FETCH_ASSOC);

        if (!$user) {
            throw new Exception('用户不存在');
        }

        $log_action = '';

        // 执行相应操作
        switch ($action) {
            case 'ban_user':
                // 更新用户状态
                $stmt = $pdo->prepare("UPDATE users SET status = 'banned' WHERE id = ?");
                $stmt->execute([$user_id]);

                // 创建user_bans表（如果不存在）
                try {
                    $pdo->exec("
                        CREATE TABLE IF NOT EXISTS user_bans (
                            id INT AUTO_INCREMENT PRIMARY KEY,
                            user_id INT NOT NULL,
                            admin_id INT NOT NULL,
                            reason TEXT NOT NULL,
                            expire_at TIMESTAMP NULL,
                            status ENUM('active', 'expired') DEFAULT 'active',
                            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                            INDEX idx_user_id (user_id),
                            INDEX idx_status (status),
                            INDEX idx_expire_at (expire_at)
                        )
                    ");
                } catch (PDOException $e) {
                    // 表可能已存在，继续执行
                }

                // 添加封号记录
                $stmt = $pdo->prepare("
                    INSERT INTO user_bans (user_id, admin_id, reason, expire_at, status, created_at)
                    VALUES (?, ?, ?, ?, 'active', NOW())
                ");
                $stmt->execute([$user_id, $admin_id, $reason, $expire_at]);

                $log_action = '封号';
                break;

            case 'unban_user':
                // 更新用户状态
                $stmt = $pdo->prepare("UPDATE users SET status = 'active' WHERE id = ?");
                $stmt->execute([$user_id]);

                // 将所有活跃的封号记录设为过期（如果表存在）
                try {
                    $stmt = $pdo->prepare("
                        UPDATE user_bans SET status = 'expired', updated_at = NOW()
                        WHERE user_id = ? AND status = 'active'
                    ");
                    $stmt->execute([$user_id]);
                } catch (PDOException $e) {
                    // user_bans表不存在，只更新用户状态即可
                    error_log("user_bans表不存在，跳过封号记录更新: " . $e->getMessage());
                }

                $log_action = '解封';
                break;

            case 'mute_user':
                // 创建禁言记录表（如果不存在）
                try {
                    $pdo->exec("
                        CREATE TABLE IF NOT EXISTS user_mutes (
                            id INT AUTO_INCREMENT PRIMARY KEY,
                            user_id INT NOT NULL,
                            admin_id INT NOT NULL,
                            admin_name VARCHAR(100) NOT NULL,
                            reason TEXT NOT NULL,
                            start_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                            end_time TIMESTAMP NULL,
                            status ENUM('active', 'expired', 'cancelled') DEFAULT 'active',
                            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                            INDEX idx_user_id (user_id),
                            INDEX idx_status (status)
                        )
                    ");
                } catch (PDOException $e) {
                    // 表可能已存在，继续执行
                }

                // 添加禁言记录
                $stmt = $pdo->prepare("
                    INSERT INTO user_mutes (user_id, admin_id, admin_name, reason, end_time, expire_at, status)
                    VALUES (?, ?, ?, ?, ?, ?, 'active')
                ");
                $stmt->execute([$user_id, $admin_id, $admin_name, $reason, $expire_at, $expire_at]);
                $log_action = '禁言';
                break;

            case 'ban_ip':
                // 获取用户最近的IP地址
                $stmt = $pdo->prepare("
                    SELECT ip_address FROM login_logs
                    WHERE user_id = ?
                    ORDER BY login_time DESC
                    LIMIT 1
                ");
                $stmt->execute([$user_id]);
                $latest_ip = $stmt->fetchColumn();

                if ($latest_ip) {
                    // 创建IP封禁表（如果不存在）
                    try {
                        $pdo->exec("
                            CREATE TABLE IF NOT EXISTS ip_bans (
                                id INT AUTO_INCREMENT PRIMARY KEY,
                                ip_address VARCHAR(45) NOT NULL,
                                admin_id INT NOT NULL,
                                admin_name VARCHAR(100) NOT NULL,
                                reason TEXT NOT NULL,
                                status ENUM('active', 'expired', 'cancelled') DEFAULT 'active',
                                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                                INDEX idx_ip_address (ip_address),
                                INDEX idx_status (status)
                            )
                        ");
                    } catch (PDOException $e) {
                        // 表可能已存在，继续执行
                    }

                    // 添加IP封禁记录
                    $stmt = $pdo->prepare("
                        INSERT INTO banned_ips (ip_address, admin_id, reason, expire_at, status, banned_at)
                        VALUES (?, ?, ?, ?, 'active', NOW())
                    ");
                    $stmt->execute([$latest_ip, $admin_id, $reason, $expire_at]);
                    $log_action = '封IP';
                } else {
                    throw new Exception('未找到用户IP地址');
                }
                break;

            default:
                throw new Exception('无效的操作类型');
        }

        // 记录管理日志
        $stmt = $pdo->prepare("
            INSERT INTO admin_logs (admin_id, admin_name, target_user_id, action, reason, created_at)
            VALUES (?, ?, ?, ?, ?, NOW())
        ");
        $stmt->execute([$admin_id, $admin_name, $user_id, $log_action, $reason]);

        // 记录到用户日志
        try {
            $stmt = $pdo->prepare("
                INSERT INTO user_logs (user_id, operator_name, employee_id, department, type, content)
                VALUES (?, ?, ?, ?, ?, ?)
            ");
            $stmt->execute([
                $user_id,
                $admin_name,
                $employee_id,
                $department,
                $log_action,
                $reason
            ]);
        } catch (PDOException $e) {
            // 如果user_logs表不存在，忽略错误
        }

        // 重定向回用户详情页
        header('Location: user_detail.php?id=' . $user_id . '&action_success=' . urlencode($log_action));
        exit;

    } catch (Exception $e) {
        $error = $e->getMessage();
        header('Location: user_detail.php?id=' . ($_POST['user_id'] ?? 1) . '&error=' . urlencode($error));
        exit;
    }
} else {
    header('Location: user_management.php');
    exit;
}
?>
