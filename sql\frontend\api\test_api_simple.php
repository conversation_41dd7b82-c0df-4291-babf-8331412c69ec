<?php
/**
 * 前台API测试文件
 * 用于验证前台API路径是否正常
 */

// 禁用错误输出
error_reporting(0);
ini_set('display_errors', 0);

header('Content-Type: application/json; charset=utf-8');

$response = [
    'success' => true,
    'message' => '前台API测试成功',
    'timestamp' => date('Y-m-d H:i:s'),
    'location' => 'frontend/api/',
    'server_info' => [
        'php_version' => PHP_VERSION,
        'document_root' => $_SERVER['DOCUMENT_ROOT'] ?? 'Unknown',
        'script_name' => $_SERVER['SCRIPT_NAME'] ?? 'Unknown',
        'request_uri' => $_SERVER['REQUEST_URI'] ?? 'Unknown'
    ]
];

// 测试数据库连接
try {
    $pdo = new PDO(
        "mysql:host=localhost;dbname=quwanplanet;charset=utf8mb4",
        "quwanplanet",
        "nJmJm23FB4Xn6Fc3",
        [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC
        ]
    );
    
    $response['database'] = [
        'status' => 'connected',
        'message' => '数据库连接成功'
    ];
    
} catch (Exception $e) {
    $response['database'] = [
        'status' => 'error',
        'message' => '数据库连接失败: ' . $e->getMessage()
    ];
}

echo json_encode($response, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);
?>
