<?php
session_start();
header('Content-Type: application/json');

// 检查用户是否登录
if (!isset($_SESSION['user_id'])) {
    echo json_encode([
        'success' => false,
        'message' => '用户未登录'
    ]);
    exit;
}

// 检查请求方法
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode([
        'success' => false,
        'message' => '请求方法错误'
    ]);
    exit;
}

// 数据库连接
require_once '../../sql/db_config.php';

// 获取数据库连接
$pdo = getDbConnection();
if (!$pdo) {
    echo json_encode([
        'success' => false,
        'message' => '数据库连接失败'
    ]);
    exit;
}

try {
    $user_id = $_SESSION['user_id'];
    
    // 验证必填字段
    $title = trim($_POST['title'] ?? '');
    $activity_type = trim($_POST['activity_type'] ?? '');
    $description = trim($_POST['description'] ?? '');
    $start_time = trim($_POST['start_time'] ?? '');
    $location = trim($_POST['location'] ?? '');
    
    if (empty($title)) {
        echo json_encode([
            'success' => false,
            'message' => '请输入活动标题'
        ]);
        exit;
    }
    
    if (empty($activity_type)) {
        echo json_encode([
            'success' => false,
            'message' => '请选择活动类型'
        ]);
        exit;
    }
    
    if (empty($description)) {
        echo json_encode([
            'success' => false,
            'message' => '请输入活动描述'
        ]);
        exit;
    }
    
    if (empty($start_time)) {
        echo json_encode([
            'success' => false,
            'message' => '请选择开始时间'
        ]);
        exit;
    }
    
    if (empty($location)) {
        echo json_encode([
            'success' => false,
            'message' => '请输入活动地点'
        ]);
        exit;
    }
    
    // 验证开始时间不能是过去时间
    $startDateTime = new DateTime($start_time);
    $now = new DateTime();
    if ($startDateTime <= $now) {
        echo json_encode([
            'success' => false,
            'message' => '开始时间不能是过去时间'
        ]);
        exit;
    }
    
    // 处理图片上传（可选）
    $imageUrls = [];
    if (isset($_FILES['images']) && !empty($_FILES['images']['name'][0])) {
        $uploadDir = '../../uploads/activities/';
        if (!is_dir($uploadDir)) {
            mkdir($uploadDir, 0755, true);
        }
        
        $allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
        $maxFileSize = 10 * 1024 * 1024; // 10MB
        
        foreach ($_FILES['images']['tmp_name'] as $index => $tmpName) {
            if (empty($tmpName)) continue;
            
            $fileName = $_FILES['images']['name'][$index];
            $fileSize = $_FILES['images']['size'][$index];
            $fileType = $_FILES['images']['type'][$index];
            
            // 验证文件类型
            if (!in_array($fileType, $allowedTypes)) {
                echo json_encode([
                    'success' => false,
                    'message' => '不支持的图片格式'
                ]);
                exit;
            }
            
            // 验证文件大小
            if ($fileSize > $maxFileSize) {
                echo json_encode([
                    'success' => false,
                    'message' => '图片大小不能超过10MB'
                ]);
                exit;
            }
            
            // 生成唯一文件名
            $extension = pathinfo($fileName, PATHINFO_EXTENSION);
            $newFileName = uniqid() . '_' . time() . '.' . $extension;
            $filePath = $uploadDir . $newFileName;
            
            // 移动文件
            if (move_uploaded_file($tmpName, $filePath)) {
                $imageUrls[] = 'uploads/activities/' . $newFileName;
            }
        }
    }
    
    // 准备数据
    $end_time = !empty($_POST['end_time']) ? $_POST['end_time'] : null;
    $max_participants = intval($_POST['max_participants'] ?? 5);
    $fee = floatval($_POST['fee'] ?? 0);
    $contact_info = trim($_POST['contact_info'] ?? '');
    $requirements = trim($_POST['requirements'] ?? '');
    $latitude = !empty($_POST['latitude']) ? floatval($_POST['latitude']) : null;
    $longitude = !empty($_POST['longitude']) ? floatval($_POST['longitude']) : null;
    $tags = !empty($_POST['tags']) ? $_POST['tags'] : '[]';
    
    // 检查是否存在user_activities表，如果不存在则创建简化版本
    try {
        $stmt = $pdo->prepare("SELECT 1 FROM user_activities LIMIT 1");
        $stmt->execute();
    } catch (PDOException $e) {
        // 表不存在，创建简化版本
        $createTableSQL = "
            CREATE TABLE IF NOT EXISTS user_activities (
                id int(11) NOT NULL AUTO_INCREMENT,
                user_id int(11) NOT NULL,
                title varchar(200) NOT NULL,
                description text DEFAULT NULL,
                activity_type varchar(50) NOT NULL,
                location varchar(200) NOT NULL,
                latitude decimal(10,8) DEFAULT NULL,
                longitude decimal(11,8) DEFAULT NULL,
                start_time datetime NOT NULL,
                end_time datetime DEFAULT NULL,
                max_participants int(11) DEFAULT NULL,
                current_participants int(11) DEFAULT 1,
                fee decimal(10,2) DEFAULT 0.00,
                contact_info varchar(200) DEFAULT NULL,
                requirements text DEFAULT NULL,
                images json DEFAULT NULL,
                tags json DEFAULT NULL,
                status enum('draft','published','ongoing','completed','cancelled','deleted') DEFAULT 'published',
                view_count int(11) DEFAULT 0,
                like_count int(11) DEFAULT 0,
                comment_count int(11) DEFAULT 0,
                share_count int(11) DEFAULT 0,
                is_featured tinyint(1) DEFAULT 0,
                created_at timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
                updated_at timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                PRIMARY KEY (id),
                KEY user_id (user_id),
                KEY activity_type (activity_type),
                KEY location (location),
                KEY start_time (start_time),
                KEY status (status),
                KEY created_at (created_at)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci
        ";
        $pdo->exec($createTableSQL);
    }
    
    // 插入活动数据
    $stmt = $pdo->prepare("
        INSERT INTO user_activities (
            user_id, title, description, activity_type, location, latitude, longitude,
            start_time, end_time, max_participants, fee, contact_info, requirements,
            images, tags, status, created_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 'published', NOW())
    ");
    
    $result = $stmt->execute([
        $user_id,
        $title,
        $description,
        $activity_type,
        $location,
        $latitude,
        $longitude,
        $start_time,
        $end_time,
        $max_participants,
        $fee,
        $contact_info,
        $requirements,
        json_encode($imageUrls),
        $tags
    ]);
    
    if ($result) {
        echo json_encode([
            'success' => true,
            'message' => '组局发布成功',
            'activity_id' => $pdo->lastInsertId()
        ]);
    } else {
        echo json_encode([
            'success' => false,
            'message' => '组局发布失败'
        ]);
    }
    
} catch (PDOException $e) {
    error_log("Database error in publish_activity.php: " . $e->getMessage());
    echo json_encode([
        'success' => false,
        'message' => '数据库错误'
    ]);
} catch (Exception $e) {
    error_log("Error in publish_activity.php: " . $e->getMessage());
    echo json_encode([
        'success' => false,
        'message' => '系统错误'
    ]);
}
?>
