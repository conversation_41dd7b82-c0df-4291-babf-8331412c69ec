# 🔧 分步骤数据库设置（兼容版）

## ⚡ 按顺序执行以下3个文件

### 第1步：创建数据库表
在phpMyAdmin中执行：`COMPATIBLE_DATABASE.sql`

### 第2步：扩展admin_users表
在phpMyAdmin中执行：`EXTEND_ADMIN_USERS.sql`

### 第3步：插入初始数据
在phpMyAdmin中执行：`INSERT_INITIAL_DATA.sql`

## 📋 详细执行步骤

### 1. 登录phpMyAdmin
- 访问您的phpMyAdmin
- 选择数据库 `quwanplanet`

### 2. 执行第一个文件
- 点击"SQL"选项卡
- 复制 `COMPATIBLE_DATABASE.sql` 的内容并粘贴
- 点击"执行"
- 应该看到创建了7个新表

### 3. 执行第二个文件
- 在同一个SQL选项卡中
- 复制 `EXTEND_ADMIN_USERS.sql` 的内容并粘贴
- 点击"执行"
- 应该看到admin_users表添加了新列

### 4. 执行第三个文件
- 在同一个SQL选项卡中
- 复制 `INSERT_INITIAL_DATA.sql` 的内容并粘贴
- 点击"执行"
- 应该看到插入了大量初始数据

## ✅ 验证安装

执行以下查询验证：

```sql
-- 检查表是否创建
SHOW TABLES LIKE '%department%';
SHOW TABLES LIKE '%role%';
SHOW TABLES LIKE '%permission%';

-- 检查数据是否插入
SELECT COUNT(*) FROM departments;  -- 应该是15
SELECT COUNT(*) FROM roles;        -- 应该是10
SELECT COUNT(*) FROM permissions;  -- 应该是27
```

## 🎯 测试权限申请页面

完成后立即测试：
```
https://vansmrz.vancrest.xyz/houtai_backup/permission/index.php
```

## ⚠️ 如果遇到错误

### 外键错误
如果遇到外键约束错误，先执行：
```sql
SET FOREIGN_KEY_CHECKS = 0;
```

### 权限错误
确保MySQL用户有以下权限：
- CREATE
- INSERT
- ALTER
- INDEX

### 字符集错误
确保数据库字符集为 `utf8mb4`

---

**按步骤执行完成后，权限管理系统将完全可用！** 🎉
