<?php
session_start();

// 检查用户是否已登录
if (!isset($_SESSION['user_id'])) {
    header('Location: ../login/index.php');
    exit;
}

// 引入数据库配置
require_once '../../includes/db_config.php';

// 初始化变量
$categories = [];
$subcategories = [];
$posts = [];
$current_category = 0;
$current_subcategory = 0;
$db_error = false;

// 获取分类数据
try {
    // 检查数据库配置是否存在
    if (!isset($db_config) || !is_array($db_config)) {
        throw new Exception("数据库配置不存在或格式不正确");
    }

    // 检查必要的配置项
    $required_configs = ['host', 'dbname', 'username', 'password', 'charset', 'port'];
    foreach ($required_configs as $config) {
        if (!isset($db_config[$config])) {
            throw new Exception("数据库配置缺少 {$config} 项");
        }
    }

    // 连接数据库
    $dsn = "mysql:host={$db_config['host']};dbname={$db_config['dbname']};charset={$db_config['charset']};port={$db_config['port']}";
    $pdo = new PDO($dsn, $db_config['username'], $db_config['password']);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

    // 检查表是否存在
    $tables_to_check = ['universe_categories', 'universe_subcategories', 'universe_posts', 'users'];
    foreach ($tables_to_check as $table) {
        $stmt = $pdo->query("SHOW TABLES LIKE '{$table}'");
        if ($stmt->rowCount() === 0) {
            throw new Exception("表 {$table} 不存在，请先创建数据库表");
        }
    }

    // 获取所有主分类
    $stmt = $pdo->query("SELECT * FROM universe_categories WHERE status = 1 ORDER BY sort_order ASC");
    $categories = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // 获取当前选中的分类
    $current_category = isset($_GET['category']) ? intval($_GET['category']) : 0;
    $current_subcategory = isset($_GET['subcategory']) ? intval($_GET['subcategory']) : 0;

    // 如果有选中的分类，获取其子分类
    if ($current_category > 0) {
        $stmt = $pdo->prepare("SELECT * FROM universe_subcategories WHERE category_id = :category_id AND status = 1 ORDER BY sort_order ASC");
        $stmt->execute(['category_id' => $current_category]);
        $subcategories = $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    // 检查是否有内容
    $check_posts = $pdo->query("SELECT COUNT(*) FROM universe_posts");
    $has_posts = $check_posts->fetchColumn() > 0;

    if ($has_posts) {
        // 构建查询条件
        $where_conditions = ["p.status = 1"];
        $params = [];

        if ($current_category > 0) {
            $where_conditions[] = "p.category_id = :category_id";
            $params['category_id'] = $current_category;
        }

        if ($current_subcategory > 0) {
            $where_conditions[] = "p.subcategory_id = :subcategory_id";
            $params['subcategory_id'] = $current_subcategory;
        }

        $where_clause = implode(" AND ", $where_conditions);

        // 获取内容列表
        $stmt = $pdo->prepare("
            SELECT p.*, u.username, u.avatar
            FROM universe_posts p
            JOIN users u ON p.user_id = u.id
            WHERE {$where_clause}
            ORDER BY p.created_at DESC
            LIMIT 20
        ");
        $stmt->execute($params);
        $posts = $stmt->fetchAll(PDO::FETCH_ASSOC);

        // 获取每个内容的分类名称
        foreach ($posts as &$post) {
            $stmt = $pdo->prepare("SELECT name FROM universe_categories WHERE id = :id");
            $stmt->execute(['id' => $post['category_id']]);
            $category = $stmt->fetch(PDO::FETCH_ASSOC);
            $post['category_name'] = $category ? $category['name'] : '';

            $stmt = $pdo->prepare("SELECT name FROM universe_subcategories WHERE id = :id");
            $stmt->execute(['id' => $post['subcategory_id']]);
            $subcategory = $stmt->fetch(PDO::FETCH_ASSOC);
            $post['subcategory_name'] = $subcategory ? $subcategory['name'] : '';
        }
    }
} catch (PDOException $e) {
    error_log("趣玩宇宙页面PDO错误: " . $e->getMessage());
    $db_error = true;
    $error_message = $e->getMessage();
} catch (Exception $e) {
    error_log("趣玩宇宙页面错误: " . $e->getMessage());
    $db_error = true;
    $error_message = $e->getMessage();
}
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <meta name="theme-color" content="#FFFFFF">
    <title>宇宙 - 趣玩星球</title>
    <link rel="stylesheet" href="css/style.css">
    <!-- 引入字体图标库 -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <!-- Toast提示 -->
    <div class="toast" id="toast"></div>

    <!-- 顶部导航栏 -->
    <div class="header-bar">
        <a href="../home/<USER>" class="back-button">
            <i class="fas fa-arrow-left"></i>
        </a>
        <div class="header-title">趣玩宇宙</div>
        <a href="publish/index.php" class="publish-icon">
            <i class="fas fa-plus"></i>
        </a>
    </div>

    <!-- 内容区域 -->
    <div class="content-container">
        <!-- 搜索栏 -->
        <div class="search-bar">
            <i class="fas fa-search"></i>
            <input type="text" placeholder="搜索宇宙内容...">
        </div>

        <!-- 分类筛选 -->
        <div class="filter-section">
            <?php if (!$db_error): ?>
            <div class="category-filter">
                <a href="index.php" class="filter-item <?php echo $current_category === 0 ? 'active' : ''; ?>">全部</a>
                <?php if (is_array($categories) && !empty($categories)): ?>
                    <?php foreach ($categories as $category): ?>
                    <a href="index.php?category=<?php echo $category['id']; ?>" class="filter-item <?php echo $current_category === intval($category['id']) ? 'active' : ''; ?>">
                        <?php echo $category['name']; ?>
                    </a>
                    <?php endforeach; ?>
                <?php endif; ?>
            </div>

            <?php if ($current_category > 0 && is_array($subcategories) && !empty($subcategories)): ?>
            <div class="subcategory-filter">
                <a href="index.php?category=<?php echo $current_category; ?>" class="filter-item <?php echo $current_subcategory === 0 ? 'active' : ''; ?>">全部</a>
                <?php foreach ($subcategories as $subcategory): ?>
                <a href="index.php?category=<?php echo $current_category; ?>&subcategory=<?php echo $subcategory['id']; ?>" class="filter-item <?php echo $current_subcategory === intval($subcategory['id']) ? 'active' : ''; ?>">
                    <?php echo $subcategory['name']; ?>
                </a>
                <?php endforeach; ?>
            </div>
            <?php endif; ?>
            <?php endif; ?>
        </div>

        <!-- 内容列表 -->
        <div class="content-list">
            <?php if ($db_error): ?>
            <div class="empty-state">
                <i class="fas fa-exclamation-triangle"></i>
                <p>数据加载失败，请稍后再试</p>
                <?php if (isset($error_message)): ?>
                <div class="error-details">
                    错误信息: <?php echo htmlspecialchars($error_message); ?>
                </div>
                <?php endif; ?>
                <div class="error-actions">
                    <a href="index.php" class="btn">刷新页面</a>
                    <a href="../home/<USER>" class="btn btn-secondary">返回首页</a>
                </div>
            </div>
            <?php elseif (empty($posts)): ?>
            <div class="empty-state">
                <i class="fas fa-rocket"></i>
                <p>暂无内容，快来发布第一篇吧！</p>
                <a href="publish/index.php" class="btn">立即发布</a>
            </div>
            <?php else: ?>
            <?php foreach ($posts as $post): ?>
            <div class="content-card" onclick="window.location.href='detail.php?id=<?php echo $post['id']; ?>'">
                <div class="card-header">
                    <div class="user-info">
                        <div class="avatar">
                            <img src="<?php echo !empty($post['avatar']) ? $post['avatar'] : 'https://s1.imagehub.cc/images/2025/04/26/f5c2f26c4564aabcbb56c5fd30f1d652.jpg'; ?>" alt="用户头像">
                        </div>
                        <div class="user-meta">
                            <div class="username"><?php echo $post['username']; ?></div>
                            <div class="post-meta">
                                <span class="category"><?php echo $post['category_name']; ?> · <?php echo $post['subcategory_name']; ?></span>
                                <span class="time"><?php echo date('m-d H:i', strtotime($post['created_at'])); ?></span>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="card-content">
                    <h3 class="post-title"><?php echo $post['title']; ?></h3>

                    <?php if (!empty($post['cover_image'])): ?>
                    <div class="post-cover">
                        <img src="<?php echo $post['cover_image']; ?>" alt="封面图片">
                    </div>
                    <?php endif; ?>

                    <div class="post-excerpt">
                        <?php
                        // 移除HTML标签，获取纯文本
                        $excerpt = strip_tags($post['content']);
                        // 截取前100个字符
                        $excerpt = mb_substr($excerpt, 0, 100, 'UTF-8');
                        echo $excerpt . (mb_strlen($excerpt, 'UTF-8') >= 100 ? '...' : '');
                        ?>
                    </div>
                </div>

                <div class="card-footer">
                    <div class="action-buttons">
                        <div class="action-button" onclick="event.stopPropagation()">
                            <i class="far fa-eye"></i>
                            <span><?php echo $post['views']; ?></span>
                        </div>
                        <div class="action-button" onclick="event.stopPropagation()">
                            <i class="far fa-comment"></i>
                            <span><?php echo $post['comments']; ?></span>
                        </div>
                        <div class="action-button like-button" data-id="<?php echo $post['id']; ?>" onclick="likePost(event, this)">
                            <i class="far fa-heart"></i>
                            <span><?php echo $post['likes']; ?></span>
                        </div>
                    </div>
                </div>
            </div>
            <?php endforeach; ?>
            <?php endif; ?>
        </div>
    </div>

    <!-- 悬浮发布按钮 -->
    <a href="publish/index.php" class="floating-publish-btn">
        <i class="fas fa-plus"></i>
    </a>

    <!-- 底部导航栏 -->
    <div class="bottom-nav">
        <a href="../home/<USER>" class="nav-item">
            <i class="fas fa-home"></i>
            <span>首页</span>
        </a>
        <a href="../activity/index.php" class="nav-item">
            <i class="fas fa-calendar-alt"></i>
            <span>组局</span>
        </a>
        <a href="index.php" class="nav-item active">
            <i class="fas fa-book-open"></i>
            <span>攻略</span>
        </a>
        <a href="../message/index.php" class="nav-item">
            <i class="fas fa-comment"></i>
            <span>消息</span>
        </a>
        <a href="../profile/index.php" class="nav-item">
            <i class="fas fa-user"></i>
            <span>我的</span>
        </a>
    </div>

    <script src="js/script.js"></script>
</body>
</html>
