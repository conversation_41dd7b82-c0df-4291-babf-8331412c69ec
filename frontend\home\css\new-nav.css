/* 底部导航栏样式 */
:root {
    --theme-color: #6F7BF5; /* 主题色更新为新主题色 */
    --inactive-color: #999999; /* 未选中颜色 */
}

/* 登录提醒条样式 */
.login-reminder {
    position: fixed;
    bottom: 56px; /* 底部导航栏的高度 */
    left: 0;
    right: 0;
    background-color: rgba(255, 255, 255, 0.95);
    padding: 10px 15px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-shadow: 0 -1px 5px rgba(0, 0, 0, 0.1);
    z-index: 99;
}

.login-reminder span {
    color: #333;
    font-size: 14px;
}

.login-btn {
    background-color: var(--theme-color);
    color: white;
    padding: 5px 12px;
    border-radius: 15px;
    font-size: 13px;
    text-decoration: none;
    font-weight: 500;
}

.bottom-nav {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background-color: white;
    display: flex;
    justify-content: space-around;
    align-items: center;
    padding: 8px 0;
    box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.05);
    z-index: 100;
    height: 60px; /* 增加高度 */
}

.nav-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 5px 0;
    width: 20%;
    text-decoration: none;
    position: relative;
    transition: all 0.3s ease;
}

.nav-item span {
    font-size: 16px; /* 增加字体大小 */
    color: var(--inactive-color);
    transition: color 0.3s ease;
    font-weight: 400;
}

.nav-item.active span {
    color: var(--theme-color);
    font-weight: 600;
}

/* 中间的加号按钮 */
.nav-add-button {
    width: 44px;
    height: 44px;
    background-color: var(--theme-color);
    border-radius: 10px; /* 正方形带圆角 */
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 4px 12px rgba(111, 123, 245, 0.3);
    position: relative;
    top: -12px; /* 稍微向上偏移 */
    transition: all 0.2s ease;
    cursor: pointer;
}

.nav-add-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 16px rgba(111, 123, 245, 0.4);
}

.nav-add-button:active {
    transform: translateY(-2px) scale(0.95);
}

.nav-add-button::before,
.nav-add-button::after {
    content: '';
    position: absolute;
    background-color: white;
    border-radius: 1px;
}

.nav-add-button::before {
    width: 18px;
    height: 2.5px;
}

.nav-add-button::after {
    width: 2.5px;
    height: 18px;
}

/* 发布菜单弹窗 */
.publish-menu {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 1000;
    display: none;
}

.publish-menu.show {
    display: block;
}

.publish-menu-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(5px);
}

.publish-menu-content {
    position: absolute;
    bottom: 80px; /* 在导航栏上方 */
    left: 50%;
    transform: translateX(-50%);
    background-color: white;
    border-radius: 16px;
    padding: 20px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15);
    min-width: 200px;
    animation: slideUp 0.3s ease-out;
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateX(-50%) translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateX(-50%) translateY(0);
    }
}

.publish-menu-item {
    display: flex;
    align-items: center;
    padding: 15px;
    border-radius: 12px;
    cursor: pointer;
    transition: background-color 0.2s ease;
    margin-bottom: 8px;
}

.publish-menu-item:last-child {
    margin-bottom: 0;
}

.publish-menu-item:hover {
    background-color: #f8f9fa;
}

.publish-menu-item i {
    font-size: 20px;
    color: var(--theme-color);
    margin-right: 12px;
    width: 24px;
    text-align: center;
}

.publish-menu-item span {
    font-size: 16px;
    font-weight: 500;
    color: #333;
    margin-right: 8px;
}

.publish-menu-item small {
    font-size: 12px;
    color: #666;
    margin-left: auto;
}

/* 自定义弹窗样式 */
.custom-dialog {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 2000;
    display: none;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.custom-dialog.show {
    display: flex;
    opacity: 1;
}

.custom-dialog-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.6);
    backdrop-filter: blur(8px);
}

.custom-dialog-content {
    position: relative;
    margin: auto;
    background: white;
    border-radius: 20px;
    padding: 30px 24px 24px;
    max-width: 320px;
    width: 90%;
    text-align: center;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
    transform: scale(0.9);
    transition: transform 0.3s ease;
}

.custom-dialog.show .custom-dialog-content {
    transform: scale(1);
}

.dialog-icon {
    font-size: 48px;
    margin-bottom: 16px;
    line-height: 1;
}

.dialog-title {
    font-size: 20px;
    font-weight: 600;
    color: #333;
    margin: 0 0 12px 0;
    line-height: 1.3;
}

.dialog-message {
    font-size: 16px;
    color: #555;
    margin: 0 0 8px 0;
    line-height: 1.4;
}

.dialog-description {
    font-size: 14px;
    color: #777;
    margin: 0 0 24px 0;
    line-height: 1.4;
}

.dialog-buttons {
    display: flex;
    gap: 12px;
    margin-top: 24px;
}

.dialog-btn {
    flex: 1;
    padding: 12px 20px;
    border: none;
    border-radius: 12px;
    font-size: 16px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    min-height: 48px;
}

.dialog-btn-cancel {
    background: #f5f5f5;
    color: #666;
}

.dialog-btn-cancel:hover {
    background: #e8e8e8;
    transform: translateY(-1px);
}

.dialog-btn-confirm {
    background: linear-gradient(135deg, #6F7BF5, #8B95F7);
    color: white;
    box-shadow: 0 4px 12px rgba(111, 123, 245, 0.3);
}

.dialog-btn-confirm:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(111, 123, 245, 0.4);
}

.dialog-btn:active {
    transform: translateY(0);
}

/* 底部导航栏下方的安全区域（适配全面屏手机） */
.bottom-safe-area {
    height: env(safe-area-inset-bottom, 0);
    background-color: white;
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    z-index: 99;
}

/* 适配不同设备 */
@media (max-width: 320px) {
    .nav-item span {
        font-size: 11px;
    }

    .nav-add-button {
        width: 36px;
        height: 36px;
    }
}
