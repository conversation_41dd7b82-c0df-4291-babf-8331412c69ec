<?php
/**
 * 现代化验证码页面
 * 保持所有核心功能不变，只优化UI设计
 */
session_start();

// 获取手机号
$phone = $_GET['phone'] ?? '';
if (empty($phone)) {
    header('Location: index.php');
    exit;
}

// 生成验证码（保持原有逻辑）
$verification_code = sprintf('%06d', mt_rand(0, 999999));
$_SESSION['verification_code'] = $verification_code;
$_SESSION['verification_phone'] = $phone;
$_SESSION['verification_time'] = time();

// 模拟发送短信
error_log("验证码发送到 {$phone}: {$verification_code}");
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0, user-scalable=no, shrink-to-fit=no">
    <meta name="theme-color" content="#f8fafc">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="default">
    
    <title>验证手机号 - 趣玩星球</title>
    
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <style>
        :root {
            --primary-color: #40E0D0;
            --primary-dark: #36C5B5;
            --primary-light: #5EEADB;
            --bg-primary: #f8fafc;
            --bg-white: #ffffff;
            --text-primary: #1a202c;
            --text-secondary: #718096;
            --text-muted: #a0aec0;
            --border-color: #e2e8f0;
            --success-color: #48bb78;
            --error-color: #f56565;
            --shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.1);
            --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.1);
            --shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.1);
            --radius-sm: 6px;
            --radius-md: 12px;
            --radius-lg: 16px;
            --transition-fast: 0.15s ease;
            --transition-normal: 0.3s ease;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
            color: var(--text-primary);
            line-height: 1.6;
        }

        .verify-container {
            width: 100%;
            max-width: 400px;
            background: var(--bg-white);
            border-radius: var(--radius-lg);
            box-shadow: var(--shadow-lg);
            overflow: hidden;
            position: relative;
        }

        .verify-header {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
            color: white;
            padding: 24px;
            text-align: center;
            position: relative;
        }

        .back-btn {
            position: absolute;
            top: 16px;
            left: 16px;
            background: rgba(255, 255, 255, 0.2);
            color: white;
            border: none;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition-normal);
        }

        .back-btn:hover {
            background: rgba(255, 255, 255, 0.3);
        }

        .verify-icon {
            width: 60px;
            height: 60px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 16px;
            font-size: 24px;
        }

        .verify-title {
            font-size: 20px;
            font-weight: 600;
            margin-bottom: 8px;
        }

        .verify-subtitle {
            font-size: 14px;
            opacity: 0.9;
        }

        .verify-content {
            padding: 32px 24px;
        }

        .phone-display {
            text-align: center;
            margin-bottom: 32px;
        }

        .phone-text {
            font-size: 14px;
            color: var(--text-secondary);
            margin-bottom: 4px;
        }

        .phone-number {
            font-size: 18px;
            font-weight: 600;
            color: var(--text-primary);
        }

        .code-input-area {
            margin-bottom: 32px;
        }

        .code-inputs {
            display: flex;
            gap: 12px;
            justify-content: center;
            margin-bottom: 24px;
        }

        .code-input {
            width: 48px;
            height: 56px;
            text-align: center;
            font-size: 20px;
            font-weight: 600;
            border: 2px solid var(--border-color);
            border-radius: var(--radius-md);
            transition: var(--transition-normal);
            background: var(--bg-white);
            /* 强制数字键盘 */
            -webkit-appearance: none;
            -moz-appearance: textfield;
        }

        .code-input:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(64, 224, 208, 0.1);
        }

        .code-input.filled {
            border-color: var(--primary-color);
            background: rgba(64, 224, 208, 0.05);
            color: var(--primary-color);
        }

        /* 隐藏数字输入框的上下箭头 */
        .code-input::-webkit-outer-spin-button,
        .code-input::-webkit-inner-spin-button {
            -webkit-appearance: none;
            margin: 0;
        }

        .resend-area {
            text-align: center;
            margin-bottom: 24px;
        }

        .countdown {
            font-size: 14px;
            color: var(--text-secondary);
            margin-bottom: 12px;
        }

        .action-buttons {
            display: flex;
            gap: 12px;
        }

        .btn {
            flex: 1;
            padding: 12px 16px;
            border: none;
            border-radius: var(--radius-md);
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            transition: var(--transition-normal);
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 6px;
        }

        .btn-primary {
            background: var(--primary-color);
            color: white;
        }

        .btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-1px);
        }

        .btn-primary:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .btn-secondary {
            background: var(--bg-primary);
            color: var(--text-secondary);
            border: 1px solid var(--border-color);
        }

        .btn-secondary:hover {
            background: var(--border-color);
        }

        .continue-btn {
            width: 100%;
            padding: 16px;
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
            color: white;
            border: none;
            border-radius: var(--radius-md);
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: var(--transition-normal);
            margin-top: 16px;
        }

        .continue-btn:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-md);
        }

        .continue-btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        /* 短信弹窗样式 */
        .sms-modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.8);
            display: none;
            justify-content: center;
            align-items: center;
            z-index: 10000;
        }

        .sms-content {
            background: var(--bg-white);
            border-radius: var(--radius-lg);
            padding: 24px;
            margin: 20px;
            max-width: 400px;
            width: 90%;
            box-shadow: var(--shadow-lg);
        }

        .sms-header {
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 16px;
            font-size: 16px;
        }

        .sms-body {
            color: var(--text-secondary);
            line-height: 1.6;
            margin-bottom: 24px;
            font-size: 14px;
        }

        .verification-code-display {
            background: var(--primary-color);
            color: white;
            padding: 12px 16px;
            border-radius: var(--radius-md);
            font-size: 24px;
            font-weight: 700;
            text-align: center;
            margin: 16px 0;
            letter-spacing: 4px;
        }

        .modal-buttons {
            display: flex;
            gap: 12px;
        }

        /* 响应式设计 */
        @media (max-width: 480px) {
            .verify-container {
                margin: 10px;
                border-radius: var(--radius-md);
            }
            
            .verify-content {
                padding: 24px 20px;
            }

            .code-inputs {
                gap: 8px;
            }

            .code-input {
                width: 42px;
                height: 50px;
                font-size: 18px;
            }
        }
    </style>
</head>
<body>
    <div class="verify-container">
        <!-- 头部 -->
        <div class="verify-header">
            <button class="back-btn" onclick="goBack()">
                <i class="fas fa-arrow-left"></i>
            </button>
            
            <div class="verify-icon">
                <i class="fas fa-mobile-alt"></i>
            </div>
            
            <div class="verify-title">验证手机号</div>
            <div class="verify-subtitle">请输入收到的验证码</div>
        </div>

        <!-- 内容区域 -->
        <div class="verify-content">
            <!-- 手机号显示 -->
            <div class="phone-display">
                <div class="phone-text">验证码已发送至</div>
                <div class="phone-number" id="phoneDisplay"><?php echo substr($phone, 0, 3) . '****' . substr($phone, -4); ?></div>
            </div>

            <!-- 验证码输入 -->
            <div class="code-input-area">
                <div class="code-inputs">
                    <input type="tel" class="code-input" maxlength="1" data-index="0" inputmode="numeric" pattern="[0-9]" autocomplete="off">
                    <input type="tel" class="code-input" maxlength="1" data-index="1" inputmode="numeric" pattern="[0-9]" autocomplete="off">
                    <input type="tel" class="code-input" maxlength="1" data-index="2" inputmode="numeric" pattern="[0-9]" autocomplete="off">
                    <input type="tel" class="code-input" maxlength="1" data-index="3" inputmode="numeric" pattern="[0-9]" autocomplete="off">
                    <input type="tel" class="code-input" maxlength="1" data-index="4" inputmode="numeric" pattern="[0-9]" autocomplete="off">
                    <input type="tel" class="code-input" maxlength="1" data-index="5" inputmode="numeric" pattern="[0-9]" autocomplete="off">
                </div>
            </div>

            <!-- 重发验证码区域 -->
            <div class="resend-area">
                <div class="countdown" id="countdown">60秒后可重新发送</div>
                
                <div class="action-buttons">
                    <button type="button" class="btn btn-primary" id="resendBtn" disabled>
                        <i class="fas fa-paper-plane"></i>
                        重新发送
                    </button>
                    <button type="button" class="btn btn-secondary" onclick="showSmsModal()">
                        <i class="fas fa-eye"></i>
                        查看短信
                    </button>
                </div>
            </div>

            <!-- 继续按钮 -->
            <button type="button" class="continue-btn" id="continueBtn" disabled>
                <i class="fas fa-arrow-right"></i>
                继续
            </button>
        </div>
    </div>

    <!-- 短信弹窗 -->
    <div class="sms-modal" id="smsModal">
        <div class="sms-content">
            <div class="sms-header">【趣玩星球】</div>
            <div class="sms-body">
                Hi~ 欢迎来到趣玩星球！您的验证码：
                <div class="verification-code-display" id="verificationCodeDisplay"><?php echo $verification_code; ?></div>
                验证码有效期为5分钟，请勿将验证码泄露给他人，以免造成不必要的损失。如非本人操作，请忽略此短信。
            </div>
            <div class="modal-buttons">
                <button type="button" class="btn btn-primary" onclick="copyAndFillCode()">
                    <i class="fas fa-copy"></i>
                    复制并填入
                </button>
                <button type="button" class="btn btn-secondary" onclick="closeSmsModal()">
                    关闭
                </button>
            </div>
        </div>
    </div>

    <script>
        // 保持所有原有的核心功能
        const codeInputs = document.querySelectorAll('.code-input');
        const continueBtn = document.getElementById('continueBtn');
        const resendBtn = document.getElementById('resendBtn');
        const countdownEl = document.getElementById('countdown');
        
        let countdownTimer = null;
        let countdownSeconds = 60;
        let currentVerificationCode = '<?php echo $verification_code; ?>';

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            initializeCodeInputs();
            startCountdown();
            
            // 3秒后自动显示短信弹窗
            setTimeout(() => {
                showSmsModal();
            }, 3000);
        });

        // 初始化验证码输入框（保持原有逻辑）
        function initializeCodeInputs() {
            codeInputs.forEach((input, index) => {
                // 输入事件处理
                input.addEventListener('input', (e) => {
                    let value = e.target.value;
                    
                    // 只允许数字，移除非数字字符
                    value = value.replace(/[^0-9]/g, '');
                    
                    // 如果输入了多个字符，只取第一个
                    if (value.length > 1) {
                        value = value.charAt(0);
                    }
                    
                    e.target.value = value;

                    // 添加填充样式
                    if (value) {
                        e.target.classList.add('filled');
                        // 自动跳转到下一个输入框
                        if (index < codeInputs.length - 1) {
                            setTimeout(() => {
                                codeInputs[index + 1].focus();
                            }, 50);
                        }
                    } else {
                        e.target.classList.remove('filled');
                    }

                    checkAllFilled();
                });

                // 键盘事件处理
                input.addEventListener('keydown', (e) => {
                    // 退格键处理
                    if (e.key === 'Backspace') {
                        if (!e.target.value && index > 0) {
                            setTimeout(() => {
                                codeInputs[index - 1].focus();
                                codeInputs[index - 1].value = '';
                                codeInputs[index - 1].classList.remove('filled');
                                checkAllFilled();
                            }, 50);
                        }
                    }
                    
                    // 左右箭头键导航
                    if (e.key === 'ArrowLeft' && index > 0) {
                        codeInputs[index - 1].focus();
                    }
                    if (e.key === 'ArrowRight' && index < codeInputs.length - 1) {
                        codeInputs[index + 1].focus();
                    }
                });

                // 粘贴事件处理
                input.addEventListener('paste', (e) => {
                    e.preventDefault();
                    const paste = e.clipboardData.getData('text').replace(/[^0-9]/g, '');
                    
                    if (paste.length >= 6) {
                        // 粘贴6位或更多数字
                        codeInputs.forEach((inp, i) => {
                            if (i < 6 && paste[i]) {
                                inp.value = paste[i];
                                inp.classList.add('filled');
                            } else {
                                inp.value = '';
                                inp.classList.remove('filled');
                            }
                        });
                        codeInputs[5].focus();
                    } else if (paste.length > 0) {
                        // 粘贴少于6位数字，从当前位置开始填充
                        for (let i = 0; i < paste.length && (index + i) < codeInputs.length; i++) {
                            codeInputs[index + i].value = paste[i];
                            codeInputs[index + i].classList.add('filled');
                        }
                        const nextIndex = Math.min(index + paste.length, codeInputs.length - 1);
                        codeInputs[nextIndex].focus();
                    }
                    
                    checkAllFilled();
                });

                // 聚焦事件处理
                input.addEventListener('focus', (e) => {
                    setTimeout(() => {
                        e.target.select();
                    }, 50);
                });
            });
        }

        // 检查是否全部填写完成
        function checkAllFilled() {
            const allFilled = Array.from(codeInputs).every(input => input.value);
            continueBtn.disabled = !allFilled;
            
            if (allFilled) {
                // 自动验证
                setTimeout(() => {
                    verifyCode();
                }, 500);
            }
        }

        // 验证验证码（保持原有逻辑）
        function verifyCode() {
            const enteredCode = Array.from(codeInputs).map(input => input.value).join('');
            const correctCode = currentVerificationCode;

            if (enteredCode.length !== 6) {
                alert('请输入完整的6位验证码');
                return;
            }

            if (enteredCode === correctCode) {
                // 验证成功，跳转到引导页（保持原有跳转逻辑）
                handleVerificationSuccess();
            } else {
                alert('验证码错误，请重新输入');
                // 清空输入框
                codeInputs.forEach(input => {
                    input.value = '';
                    input.classList.remove('filled');
                });
                codeInputs[0].focus();
                continueBtn.disabled = true;
            }
        }

        // 处理验证成功（保持原有逻辑）
        async function handleVerificationSuccess() {
            try {
                const phone = '<?php echo $phone; ?>';
                
                // 设置验证成功的session（保持原有逻辑）
                const response = await fetch('../register/set_verification_session.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ phone: phone })
                });

                const data = await response.json();

                if (data.success) {
                    // 设置验证完成标记
                    sessionStorage.setItem('phone_verified', 'true');
                    sessionStorage.setItem('register_phone', phone);

                    // 跳转到引导页面（保持原有跳转逻辑）
                    window.location.href = '../onboarding/index.php';
                } else {
                    alert('验证失败，请重试');
                    window.location.href = 'index.php';
                }
            } catch (error) {
                console.error('验证失败:', error);
                alert('验证失败，请重试');
                window.location.href = 'index.php';
            }
        }

        // 倒计时功能
        function startCountdown() {
            countdownTimer = setInterval(() => {
                countdownSeconds--;
                countdownEl.textContent = `${countdownSeconds}秒后可重新发送`;
                
                if (countdownSeconds <= 0) {
                    clearInterval(countdownTimer);
                    countdownEl.textContent = '可以重新发送验证码';
                    resendBtn.disabled = false;
                }
            }, 1000);
        }

        // 短信弹窗功能
        function showSmsModal() {
            document.getElementById('smsModal').style.display = 'flex';
        }

        function closeSmsModal() {
            document.getElementById('smsModal').style.display = 'none';
        }

        function copyAndFillCode() {
            const code = currentVerificationCode;
            
            // 填入验证码
            code.split('').forEach((digit, index) => {
                if (codeInputs[index]) {
                    codeInputs[index].value = digit;
                    codeInputs[index].classList.add('filled');
                }
            });

            checkAllFilled();
            closeSmsModal();
        }

        // 返回功能
        function goBack() {
            window.history.back();
        }

        // 继续按钮点击事件
        continueBtn.addEventListener('click', verifyCode);
    </script>
</body>
</html>
