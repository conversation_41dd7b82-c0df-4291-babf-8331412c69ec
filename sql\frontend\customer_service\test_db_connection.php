<?php
// 测试数据库连接和基本操作
header('Content-Type: application/json; charset=UTF-8');

try {
    // 引用数据库配置
    require_once '../../houtai_backup/db_config.php';

    echo json_encode([
        'step' => 1,
        'message' => '✅ 数据库配置文件加载成功',
        'success' => true
    ]) . "\n";

    // 测试数据库连接
    $pdo = getDbConnection();

    echo json_encode([
        'step' => 2,
        'message' => '✅ 数据库连接成功',
        'success' => true
    ]) . "\n";

    // 测试查询会话表
    $stmt = $pdo->prepare("SELECT COUNT(*) as count FROM customer_service_sessions");
    $stmt->execute();
    $sessionCount = $stmt->fetchColumn();

    echo json_encode([
        'step' => 3,
        'message' => "✅ 会话表查询成功，共 {$sessionCount} 条记录",
        'success' => true,
        'session_count' => $sessionCount
    ]) . "\n";

    // 测试查询消息表
    $stmt = $pdo->prepare("SELECT COUNT(*) as count FROM customer_service_messages");
    $stmt->execute();
    $messageCount = $stmt->fetchColumn();

    echo json_encode([
        'step' => 4,
        'message' => "✅ 消息表查询成功，共 {$messageCount} 条记录",
        'success' => true,
        'message_count' => $messageCount
    ]) . "\n";

    // 测试插入消息（使用现有会话ID）
    // 先获取一个现有的会话ID
    $stmt = $pdo->prepare("SELECT session_id FROM customer_service_sessions LIMIT 1");
    $stmt->execute();
    $existingSession = $stmt->fetchColumn();

    if ($existingSession) {
        $testMessage = '数据库连接测试消息 - ' . date('Y-m-d H:i:s');

        $stmt = $pdo->prepare("
            INSERT INTO customer_service_messages
            (session_id, sender_type, sender_name, content, created_at)
            VALUES (?, 'system', '系统测试', ?, NOW())
        ");
        $result = $stmt->execute([$existingSession, $testMessage]);
        $insertId = $pdo->lastInsertId();

        echo json_encode([
            'step' => 5,
            'message' => "✅ 消息插入测试成功，ID: {$insertId}",
            'success' => true,
            'insert_id' => $insertId,
            'session_id' => $existingSession
        ]) . "\n";

        // 删除测试消息
        $stmt = $pdo->prepare("DELETE FROM customer_service_messages WHERE id = ?");
        $stmt->execute([$insertId]);

        echo json_encode([
            'step' => 6,
            'message' => "✅ 测试消息清理完成",
            'success' => true
        ]) . "\n";
    } else {
        echo json_encode([
            'step' => 5,
            'message' => "⚠️ 没有现有会话，跳过消息插入测试",
            'success' => true,
            'note' => '需要先创建会话才能插入消息'
        ]) . "\n";
    }

    // 测试会话查询
    $stmt = $pdo->prepare("
        SELECT session_id, user_name, status, started_at
        FROM customer_service_sessions
        ORDER BY started_at DESC
        LIMIT 3
    ");
    $stmt->execute();
    $sessions = $stmt->fetchAll(PDO::FETCH_ASSOC);

    echo json_encode([
        'step' => 7,
        'message' => "✅ 最近会话查询成功",
        'success' => true,
        'recent_sessions' => $sessions
    ]) . "\n";

    echo json_encode([
        'step' => 'FINAL',
        'message' => '🎉 所有数据库测试通过！数据库工作正常！',
        'success' => true,
        'summary' => [
            'sessions' => $sessionCount,
            'messages' => $messageCount,
            'connection' => 'OK',
            'insert_test' => 'OK',
            'query_test' => 'OK'
        ]
    ]) . "\n";

} catch (Exception $e) {
    echo json_encode([
        'step' => 'ERROR',
        'message' => '❌ 数据库测试失败: ' . $e->getMessage(),
        'success' => false,
        'error' => $e->getMessage(),
        'file' => $e->getFile(),
        'line' => $e->getLine()
    ]) . "\n";
}
?>
