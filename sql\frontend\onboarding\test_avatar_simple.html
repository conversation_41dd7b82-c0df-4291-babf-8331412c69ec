<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>简单头像上传测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 600px;
            margin: 50px auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .upload-area {
            border: 2px dashed #40E0D0;
            border-radius: 10px;
            padding: 40px;
            text-align: center;
            margin: 20px 0;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        .upload-area:hover {
            background: rgba(64, 224, 208, 0.05);
        }
        .upload-btn {
            background: #40E0D0;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
        }
        .upload-btn:hover {
            background: #20B2AA;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 5px;
            white-space: pre-wrap;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .preview {
            max-width: 200px;
            max-height: 200px;
            margin: 10px auto;
            border-radius: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 简单头像上传测试</h1>
        
        <div class="upload-area" onclick="document.getElementById('avatar').click()">
            <p>📸 点击选择头像文件</p>
            <p style="color: #666; font-size: 14px;">支持 JPG、PNG、GIF、WebP 格式，最大 5MB</p>
            <input type="file" id="avatar" accept="image/*" style="display: none;">
        </div>
        
        <button class="upload-btn" onclick="testUpload()">🚀 测试上传</button>
        <button class="upload-btn" onclick="testCloudinary()" style="margin-left: 10px;">🔧 测试Cloudinary连接</button>
        
        <div id="result" class="result info">等待选择文件...</div>
        
        <div id="preview-container" style="text-align: center; margin-top: 20px;"></div>
    </div>

    <script>
        // 文件选择事件
        document.getElementById('avatar').addEventListener('change', function() {
            const file = this.files[0];
            const result = document.getElementById('result');
            
            if (file) {
                result.className = 'result info';
                result.textContent = `已选择文件：${file.name}\n大小：${(file.size / 1024 / 1024).toFixed(2)} MB\n类型：${file.type}`;
                
                // 显示预览
                const reader = new FileReader();
                reader.onload = function(e) {
                    const preview = document.getElementById('preview-container');
                    preview.innerHTML = `<img src="${e.target.result}" class="preview" alt="预览">`;
                };
                reader.readAsDataURL(file);
            } else {
                result.className = 'result info';
                result.textContent = '等待选择文件...';
                document.getElementById('preview-container').innerHTML = '';
            }
        });

        // 测试上传
        async function testUpload() {
            const fileInput = document.getElementById('avatar');
            const result = document.getElementById('result');
            
            if (!fileInput.files[0]) {
                result.className = 'result error';
                result.textContent = '❌ 请先选择文件';
                return;
            }
            
            const formData = new FormData();
            formData.append('avatar', fileInput.files[0]);
            
            try {
                result.className = 'result info';
                result.textContent = '⏳ 上传中，请稍候...';
                
                const response = await fetch('test_simple_upload.php', {
                    method: 'POST',
                    body: formData
                });
                
                const data = await response.json();
                
                if (data.success) {
                    result.className = 'result success';
                    result.textContent = `✅ 上传成功！
                    
头像URL: ${data.avatar_url}
审核结果: ${data.moderation.approved ? '通过' : '未通过'}
审核原因: ${data.moderation.reason}

调试信息:
${JSON.stringify(data.debug, null, 2)}`;
                    
                    // 显示上传后的图片
                    const preview = document.getElementById('preview-container');
                    preview.innerHTML = `
                        <h3>上传成功的图片：</h3>
                        <img src="${data.avatar_url}" class="preview" alt="上传成功">
                    `;
                } else {
                    result.className = 'result error';
                    result.textContent = `❌ 上传失败: ${data.error}

调试信息:
${JSON.stringify(data.debug || {}, null, 2)}`;
                }
            } catch (error) {
                result.className = 'result error';
                result.textContent = `❌ 请求失败: ${error.message}`;
            }
        }

        // 测试Cloudinary连接
        async function testCloudinary() {
            const result = document.getElementById('result');
            
            try {
                result.className = 'result info';
                result.textContent = '🔧 测试Cloudinary连接...';
                
                // 创建一个1x1像素的透明PNG图片进行测试
                const canvas = document.createElement('canvas');
                canvas.width = 1;
                canvas.height = 1;
                const ctx = canvas.getContext('2d');
                ctx.fillStyle = 'rgba(0,0,0,0)';
                ctx.fillRect(0, 0, 1, 1);
                
                canvas.toBlob(async function(blob) {
                    const formData = new FormData();
                    formData.append('avatar', blob, 'test.png');
                    
                    const response = await fetch('test_simple_upload.php', {
                        method: 'POST',
                        body: formData
                    });
                    
                    const data = await response.json();
                    
                    if (data.success) {
                        result.className = 'result success';
                        result.textContent = `✅ Cloudinary连接正常！
                        
测试图片URL: ${data.avatar_url}

连接信息:
${JSON.stringify(data.debug, null, 2)}`;
                    } else {
                        result.className = 'result error';
                        result.textContent = `❌ Cloudinary连接失败: ${data.error}

错误信息:
${JSON.stringify(data.debug || {}, null, 2)}`;
                    }
                }, 'image/png');
                
            } catch (error) {
                result.className = 'result error';
                result.textContent = `❌ 连接测试失败: ${error.message}`;
            }
        }
    </script>
</body>
</html>
