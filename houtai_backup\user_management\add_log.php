<?php
/**
 * 添加用户日志
 * 趣玩星球管理后台
 */

session_start();

// 检查登录状态
if (!isset($_SESSION['admin_id'])) {
    header('Location: ../login.php');
    exit;
}

require_once '../db_config.php';

$response = ['success' => false, 'message' => ''];

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $user_id = intval($_POST['user_id'] ?? 0);
    $type = trim($_POST['type'] ?? '');
    $content = trim($_POST['content'] ?? '');

    if (!$user_id || !$type || !$content) {
        $response['message'] = '参数不完整';
        echo json_encode($response);
        exit;
    }

    try {
        $pdo = getDbConnection();

        // 获取管理员信息
        $admin_name = $_SESSION['admin_name'] ?? '管理员';
        $admin_employee_id = $_SESSION['admin_employee_id'] ?? '';
        $admin_department = $_SESSION['admin_department'] ?? '';

        // 插入日志记录
        $stmt = $pdo->prepare("
            INSERT INTO user_logs (user_id, operator_name, employee_id, department, type, content, created_at)
            VALUES (?, ?, ?, ?, ?, ?, NOW())
        ");
        
        $stmt->execute([
            $user_id,
            $admin_name,
            $admin_employee_id,
            $admin_department,
            $type,
            $content
        ]);

        $response['success'] = true;
        $response['message'] = '日志添加成功';

        // 如果是通过表单提交，重定向回详情页
        if (!isset($_POST['ajax'])) {
            $_SESSION['success_message'] = '日志添加成功！';
            header('Location: detail.php?id=' . $user_id);
            exit;
        }

    } catch (PDOException $e) {
        $response['message'] = '数据库错误：' . $e->getMessage();
    }
}

// 如果是AJAX请求，返回JSON
if (isset($_POST['ajax'])) {
    header('Content-Type: application/json');
    echo json_encode($response);
} else {
    // 非AJAX请求且失败，重定向回详情页
    if (isset($_POST['user_id'])) {
        $_SESSION['error_message'] = $response['message'];
        header('Location: detail.php?id=' . intval($_POST['user_id']));
    } else {
        header('Location: index.php');
    }
}
?>
