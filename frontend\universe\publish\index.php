<?php
session_start();

// 检查用户是否已登录
if (!isset($_SESSION['user_id'])) {
    header('Location: ../../login/index.php');
    exit;
}

// 引入数据库配置
require_once '../../../includes/db_config.php';

// 初始化变量
$categories = [];
$subcategories = [];
$subcategories_by_category = [];
$db_error = false;

// 获取分类数据
try {
    $dsn = "mysql:host={$db_config['host']};dbname={$db_config['dbname']};charset={$db_config['charset']};port={$db_config['port']}";
    $pdo = new PDO($dsn, $db_config['username'], $db_config['password']);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

    // 获取所有主分类
    $stmt = $pdo->query("SELECT * FROM universe_categories WHERE status = 1 ORDER BY sort_order ASC");
    $categories = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // 获取所有子分类
    $stmt = $pdo->query("SELECT * FROM universe_subcategories WHERE status = 1 ORDER BY category_id ASC, sort_order ASC");
    $subcategories = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // 按主分类ID组织子分类
    $subcategories_by_category = [];
    if (is_array($subcategories) && !empty($subcategories)) {
        foreach ($subcategories as $subcategory) {
            $subcategories_by_category[$subcategory['category_id']][] = $subcategory;
        }
    }
} catch (PDOException $e) {
    error_log("趣玩宇宙发布页面错误: " . $e->getMessage());
    $db_error = true;
}
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <meta name="theme-color" content="#1E90FF">
    <title>发布内容 - 趣玩宇宙</title>
    <link rel="stylesheet" href="../css/style.css">
    <link rel="stylesheet" href="css/publish.css">
    <!-- 引入字体图标库 -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <!-- 引入富文本编辑器 -->
    <link href="https://cdn.quilljs.com/1.3.6/quill.snow.css" rel="stylesheet">
    <!-- 引入表情选择器 -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/emoji-picker-element@1.15.0/css/emoji-picker.css">
</head>
<body>
    <!-- Toast提示 -->
    <div class="toast" id="toast"></div>

    <!-- 顶部导航栏 -->
    <div class="header-bar">
        <a href="../index.php" class="back-button">
            <i class="fas fa-arrow-left"></i>
        </a>
        <div class="header-title">发布内容</div>
        <button class="publish-button" id="publish-button">发布</button>
    </div>

    <!-- 内容区域 -->
    <div class="content-container">
        <form id="publish-form" class="publish-form">
            <!-- 标题输入 -->
            <div class="form-group">
                <input type="text" id="title" name="title" placeholder="请输入标题（必填）" required>
            </div>

            <!-- 分类选择 -->
            <div class="form-group">
                <div class="category-selector">
                    <select id="category" name="category_id" required>
                        <option value="">请选择主分类</option>
                        <?php if (!$db_error && is_array($categories) && !empty($categories)): ?>
                            <?php foreach ($categories as $category): ?>
                            <option value="<?php echo $category['id']; ?>"><?php echo $category['name']; ?></option>
                            <?php endforeach; ?>
                        <?php endif; ?>
                    </select>

                    <select id="subcategory" name="subcategory_id" required disabled>
                        <option value="">请先选择主分类</option>
                    </select>
                </div>
                <?php if ($db_error): ?>
                <div class="error-message">加载分类失败，请刷新页面重试</div>
                <?php endif; ?>
            </div>

            <!-- 富文本编辑器 -->
            <div class="form-group">
                <div id="editor-container">
                    <div id="editor"></div>
                </div>
                <input type="hidden" id="content" name="content">
            </div>

            <!-- 媒体上传区域 -->
            <div class="form-group">
                <div class="media-upload-section">
                    <div class="upload-buttons">
                        <button type="button" class="upload-button" id="image-upload-button">
                            <i class="fas fa-image"></i> 图片
                        </button>
                        <button type="button" class="upload-button" id="video-upload-button">
                            <i class="fas fa-video"></i> 视频
                        </button>
                        <button type="button" class="upload-button" id="audio-upload-button">
                            <i class="fas fa-music"></i> 音乐
                        </button>
                        <button type="button" class="upload-button" id="emoji-button">
                            <i class="fas fa-smile"></i> 表情
                        </button>
                    </div>

                    <div id="emoji-picker-container" class="emoji-picker-container">
                        <!-- 表情选择器将在这里动态加载 -->
                    </div>

                    <div id="media-preview" class="media-preview">
                        <!-- 上传的媒体预览将在这里显示 -->
                    </div>
                </div>
            </div>

            <!-- 封面图片上传 -->
            <div class="form-group">
                <div class="cover-upload">
                    <label>封面图片（可选）</label>
                    <div class="cover-preview" id="cover-preview">
                        <div class="upload-placeholder">
                            <i class="fas fa-plus"></i>
                            <span>上传封面</span>
                        </div>
                        <img id="cover-image-preview" style="display: none;">
                    </div>
                    <input type="file" id="cover-upload" name="cover" accept="image/*" style="display: none;">
                    <input type="hidden" id="cover_image_url" name="cover_image_url">
                </div>
            </div>
        </form>
    </div>

    <!-- 隐藏的文件上传输入 -->
    <input type="file" id="image-upload" accept="image/*" style="display: none;" multiple>
    <input type="file" id="video-upload" accept="video/*" style="display: none;">
    <input type="file" id="audio-upload" accept="audio/*" style="display: none;">

    <!-- 引入脚本 -->
    <script src="https://cdn.quilljs.com/1.3.6/quill.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/emoji-picker-element@1.15.0/index.js" type="module"></script>
    <script src="js/publish.js"></script>
</body>
</html>
