// 实名认证审核页面JavaScript

// 通过审核
function approveVerification(verificationId) {
    document.getElementById('approveVerificationId').value = verificationId;
    showModal('approveModal');
}

// 拒绝审核
function rejectVerification(verificationId) {
    document.getElementById('rejectVerificationId').value = verificationId;
    document.getElementById('rejectReason').value = '';
    showModal('rejectModal');
}

// 显示模态框
function showModal(modalId) {
    const modal = document.getElementById(modalId);
    modal.classList.add('show');
    modal.style.display = 'flex';
    
    // 阻止背景滚动
    document.body.style.overflow = 'hidden';
}

// 关闭模态框
function closeModal(modalId) {
    const modal = document.getElementById(modalId);
    modal.classList.remove('show');
    modal.style.display = 'none';
    
    // 恢复背景滚动
    document.body.style.overflow = 'auto';
}

// 提交通过审核
function submitApprove() {
    const form = document.getElementById('approveForm');
    const submitBtn = event.target;
    
    // 显示加载状态
    submitBtn.disabled = true;
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 处理中...';
    
    form.submit();
}

// 提交拒绝审核
function submitReject() {
    const reason = document.getElementById('rejectReason').value.trim();
    
    if (!reason) {
        alert('请填写拒绝原因');
        return;
    }
    
    const form = document.getElementById('rejectForm');
    const submitBtn = event.target;
    
    // 显示加载状态
    submitBtn.disabled = true;
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 处理中...';
    
    form.submit();
}

// 点击模态框背景关闭
document.addEventListener('DOMContentLoaded', function() {
    const modals = document.querySelectorAll('.modal');
    
    modals.forEach(modal => {
        modal.addEventListener('click', function(e) {
            if (e.target === modal) {
                closeModal(modal.id);
            }
        });
    });
    
    // ESC键关闭模态框
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape') {
            const openModal = document.querySelector('.modal.show');
            if (openModal) {
                closeModal(openModal.id);
            }
        }
    });
    
    // 自动隐藏成功/错误消息
    const alerts = document.querySelectorAll('.alert');
    alerts.forEach(alert => {
        setTimeout(() => {
            alert.style.opacity = '0';
            setTimeout(() => {
                alert.remove();
            }, 300);
        }, 5000);
    });
});

// 批量操作功能（可选）
function selectAll() {
    const checkboxes = document.querySelectorAll('.verification-checkbox');
    const selectAllCheckbox = document.getElementById('selectAll');
    
    checkboxes.forEach(checkbox => {
        checkbox.checked = selectAllCheckbox.checked;
    });
    
    updateBatchActions();
}

function updateBatchActions() {
    const checkedBoxes = document.querySelectorAll('.verification-checkbox:checked');
    const batchActions = document.getElementById('batchActions');
    
    if (checkedBoxes.length > 0) {
        batchActions.style.display = 'block';
        document.getElementById('selectedCount').textContent = checkedBoxes.length;
    } else {
        batchActions.style.display = 'none';
    }
}

// 搜索功能
function searchVerifications() {
    const searchTerm = document.getElementById('searchInput').value.toLowerCase();
    const cards = document.querySelectorAll('.verification-card');
    
    cards.forEach(card => {
        const name = card.querySelector('.user-info h3').textContent.toLowerCase();
        const username = card.querySelector('.user-info p').textContent.toLowerCase();
        
        if (name.includes(searchTerm) || username.includes(searchTerm)) {
            card.style.display = 'block';
        } else {
            card.style.display = 'none';
        }
    });
}

// 实时搜索
document.addEventListener('DOMContentLoaded', function() {
    const searchInput = document.getElementById('searchInput');
    if (searchInput) {
        searchInput.addEventListener('input', searchVerifications);
    }
});

// 导出功能
function exportVerifications() {
    const status = new URLSearchParams(window.location.search).get('status') || 'pending';
    window.location.href = `export.php?type=verifications&status=${status}`;
}

// 刷新页面
function refreshPage() {
    window.location.reload();
}

// 快捷键支持
document.addEventListener('keydown', function(e) {
    // Ctrl+R 刷新
    if (e.ctrlKey && e.key === 'r') {
        e.preventDefault();
        refreshPage();
    }
    
    // Ctrl+F 搜索
    if (e.ctrlKey && e.key === 'f') {
        e.preventDefault();
        const searchInput = document.getElementById('searchInput');
        if (searchInput) {
            searchInput.focus();
        }
    }
});

// 工具提示
function showTooltip(element, message) {
    const tooltip = document.createElement('div');
    tooltip.className = 'tooltip';
    tooltip.textContent = message;
    
    document.body.appendChild(tooltip);
    
    const rect = element.getBoundingClientRect();
    tooltip.style.left = rect.left + (rect.width / 2) - (tooltip.offsetWidth / 2) + 'px';
    tooltip.style.top = rect.top - tooltip.offsetHeight - 10 + 'px';
    
    setTimeout(() => {
        tooltip.remove();
    }, 3000);
}

// 复制身份证号
function copyIdCard(idCard) {
    navigator.clipboard.writeText(idCard).then(() => {
        showTooltip(event.target, '已复制到剪贴板');
    }).catch(() => {
        // 降级方案
        const textArea = document.createElement('textarea');
        textArea.value = idCard;
        document.body.appendChild(textArea);
        textArea.select();
        document.execCommand('copy');
        document.body.removeChild(textArea);
        showTooltip(event.target, '已复制到剪贴板');
    });
}

// 页面加载完成后的初始化
document.addEventListener('DOMContentLoaded', function() {
    console.log('实名认证审核页面已加载');
    
    // 添加复制功能到身份证号
    const idCardElements = document.querySelectorAll('.id-card-number');
    idCardElements.forEach(element => {
        element.style.cursor = 'pointer';
        element.title = '点击复制完整身份证号';
        element.addEventListener('click', function() {
            const fullIdCard = this.dataset.fullId;
            if (fullIdCard) {
                copyIdCard(fullIdCard);
            }
        });
    });
});
