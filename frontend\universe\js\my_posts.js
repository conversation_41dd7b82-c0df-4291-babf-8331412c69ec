document.addEventListener('DOMContentLoaded', function() {
    // 获取删除按钮和弹窗
    const deleteButtons = document.querySelectorAll('.delete-button');
    const deleteModal = document.getElementById('delete-modal');
    const cancelButton = document.querySelector('.cancel-button');
    const confirmButton = document.querySelector('.confirm-button');
    
    let currentPostId = null;
    
    // 点击删除按钮显示弹窗
    deleteButtons.forEach(button => {
        button.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();
            
            // 获取作品ID
            currentPostId = this.dataset.id;
            
            // 显示弹窗
            deleteModal.style.display = 'flex';
        });
    });
    
    // 点击取消按钮关闭弹窗
    cancelButton.addEventListener('click', function() {
        deleteModal.style.display = 'none';
        currentPostId = null;
    });
    
    // 点击弹窗外部关闭弹窗
    deleteModal.addEventListener('click', function(e) {
        if (e.target === this) {
            deleteModal.style.display = 'none';
            currentPostId = null;
        }
    });
    
    // 点击确认按钮删除作品
    confirmButton.addEventListener('click', function() {
        if (!currentPostId) return;
        
        // 发送删除请求
        fetch('../api/universe/delete_post.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: `post_id=${currentPostId}`
        })
        .then(response => response.json())
        .then(data => {
            // 关闭弹窗
            deleteModal.style.display = 'none';
            
            if (data.success) {
                // 显示成功提示
                showToast('删除成功');
                
                // 移除对应的作品卡片
                const postCard = document.querySelector(`.delete-button[data-id="${currentPostId}"]`).closest('.content-card');
                postCard.style.animation = 'fadeOut 0.3s forwards';
                setTimeout(() => {
                    postCard.remove();
                    
                    // 如果没有作品了，显示空状态
                    if (document.querySelectorAll('.content-card').length === 0) {
                        const emptyState = document.createElement('div');
                        emptyState.className = 'empty-state';
                        emptyState.innerHTML = `
                            <i class="fas fa-rocket"></i>
                            <p>暂无作品，快来发布第一篇吧！</p>
                            <a href="publish/index.php" class="btn">立即发布</a>
                        `;
                        document.querySelector('.content-list').appendChild(emptyState);
                    }
                }, 300);
            } else {
                // 显示错误提示
                showToast(data.message || '删除失败，请重试');
            }
            
            // 重置当前作品ID
            currentPostId = null;
        })
        .catch(error => {
            console.error('Error:', error);
            showToast('网络错误，请稍后再试');
            
            // 关闭弹窗
            deleteModal.style.display = 'none';
            currentPostId = null;
        });
    });
    
    // Toast提示函数
    function showToast(message) {
        const toast = document.getElementById('toast');
        toast.textContent = message;
        toast.style.display = 'block';
        
        // 添加动画
        toast.style.animation = 'fadeIn 0.3s forwards';
        
        // 3秒后隐藏
        setTimeout(() => {
            toast.style.animation = 'fadeOut 0.3s forwards';
            setTimeout(() => {
                toast.style.display = 'none';
            }, 300);
        }, 3000);
    }
    
    // 确保window.showToast也可用
    window.showToast = showToast;
});
