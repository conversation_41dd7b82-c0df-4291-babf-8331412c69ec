<?php
/**
 * 客服对话记录查看页面
 */

session_start();

// 检查管理员登录状态
if (!isset($_SESSION['admin_id'])) {
    header('Location: ../login.php');
    exit;
}

$admin_id = $_SESSION['admin_id'];
$admin_username = $_SESSION['admin_username'] ?? '管理员';

// 获取对话记录
try {
    $pdo = new PDO(
        "mysql:host=localhost;dbname=quwanplanet;charset=utf8mb4",
        "quwanplanet",
        "nJmJm23FB4Xn6Fc3",
        [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC
        ]
    );
    
    // 获取分页参数
    $page = intval($_GET['page'] ?? 1);
    $limit = 20;
    $offset = ($page - 1) * $limit;
    
    // 获取搜索参数
    $search = trim($_GET['search'] ?? '');
    $date_from = trim($_GET['date_from'] ?? '');
    $date_to = trim($_GET['date_to'] ?? '');
    
    // 构建查询条件
    $where_conditions = [];
    $params = [];
    
    if (!empty($search)) {
        $where_conditions[] = "(user_message LIKE ? OR bot_reply LIKE ? OR admin_reply LIKE ?)";
        $params[] = "%$search%";
        $params[] = "%$search%";
        $params[] = "%$search%";
    }
    
    if (!empty($date_from)) {
        $where_conditions[] = "DATE(created_at) >= ?";
        $params[] = $date_from;
    }
    
    if (!empty($date_to)) {
        $where_conditions[] = "DATE(created_at) <= ?";
        $params[] = $date_to;
    }
    
    $where_clause = !empty($where_conditions) ? 'WHERE ' . implode(' AND ', $where_conditions) : '';
    
    // 获取总数
    $count_sql = "SELECT COUNT(*) FROM customer_service_conversations $where_clause";
    $stmt = $pdo->prepare($count_sql);
    $stmt->execute($params);
    $total_count = $stmt->fetchColumn();
    
    // 获取对话记录
    $sql = "
        SELECT c.*, u.username, u.phone
        FROM customer_service_conversations c
        LEFT JOIN users u ON c.user_id = u.id
        $where_clause
        ORDER BY c.created_at DESC 
        LIMIT $limit OFFSET $offset
    ";
    $stmt = $pdo->prepare($sql);
    $stmt->execute($params);
    $conversations = $stmt->fetchAll();
    
    $total_pages = ceil($total_count / $limit);
    
    // 获取统计数据
    $stats = [];
    
    // 今日对话数
    $stmt = $pdo->query("SELECT COUNT(*) FROM customer_service_conversations WHERE DATE(created_at) = CURDATE()");
    $stats['today'] = $stmt->fetchColumn();
    
    // 机器人回复数
    $stmt = $pdo->query("SELECT COUNT(*) FROM customer_service_conversations WHERE bot_reply IS NOT NULL");
    $stats['bot_replies'] = $stmt->fetchColumn();
    
    // 人工回复数
    $stmt = $pdo->query("SELECT COUNT(*) FROM customer_service_conversations WHERE admin_reply IS NOT NULL");
    $stats['human_replies'] = $stmt->fetchColumn();
    
    // 转人工数
    $stmt = $pdo->query("SELECT COUNT(*) FROM customer_service_conversations WHERE is_human_service = 1");
    $stats['transferred'] = $stmt->fetchColumn();
    
} catch (Exception $e) {
    $error_message = '数据库连接失败：' . $e->getMessage();
    $conversations = [];
    $total_count = 0;
    $total_pages = 0;
    $stats = ['today' => 0, 'bot_replies' => 0, 'human_replies' => 0, 'transferred' => 0];
}
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>对话记录 - 趣玩星球管理后台</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        .main-content {
            margin-left: 250px;
            padding: 20px;
        }
        .conversation-card {
            background: white;
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            border-left: 4px solid #667eea;
        }
        .conversation-card.human-service {
            border-left-color: #f59e0b;
        }
        .message-bubble {
            background: #f8f9fa;
            padding: 12px 16px;
            border-radius: 12px;
            margin: 8px 0;
            max-width: 80%;
        }
        .message-bubble.user {
            background: #e3f2fd;
            margin-left: auto;
            text-align: right;
        }
        .message-bubble.bot {
            background: #e8f5e8;
        }
        .message-bubble.admin {
            background: #fff3cd;
        }
        .search-box {
            background: white;
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .stat-card {
            background: white;
            border-radius: 12px;
            padding: 20px;
            text-align: center;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .stat-number {
            font-size: 2rem;
            font-weight: bold;
            color: #667eea;
        }
    </style>
</head>
<body>
    <?php include '../includes/sidebar.php'; ?>
    
    <div class="main-content">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <div>
                <h2><i class="bi bi-chat-square-text"></i> 对话记录</h2>
                <p class="text-muted mb-0">查看和管理客服对话记录</p>
            </div>
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="../dashboard.php">首页</a></li>
                    <li class="breadcrumb-item"><a href="index.php">智能客服管理</a></li>
                    <li class="breadcrumb-item active">对话记录</li>
                </ol>
            </nav>
        </div>

        <?php if (isset($error_message)): ?>
            <div class="alert alert-danger">
                <i class="bi bi-exclamation-triangle"></i> <?= htmlspecialchars($error_message) ?>
            </div>
        <?php endif; ?>

        <!-- 统计数据 -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="stat-card">
                    <div class="stat-number"><?= number_format($stats['today']) ?></div>
                    <div class="text-muted">今日对话</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stat-card">
                    <div class="stat-number"><?= number_format($stats['bot_replies']) ?></div>
                    <div class="text-muted">机器人回复</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stat-card">
                    <div class="stat-number"><?= number_format($stats['human_replies']) ?></div>
                    <div class="text-muted">人工回复</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stat-card">
                    <div class="stat-number"><?= number_format($stats['transferred']) ?></div>
                    <div class="text-muted">转人工</div>
                </div>
            </div>
        </div>

        <!-- 搜索和筛选 -->
        <div class="search-box">
            <form method="GET" class="row g-3">
                <div class="col-md-4">
                    <label class="form-label">搜索对话</label>
                    <input type="text" class="form-control" name="search" 
                           value="<?= htmlspecialchars($search) ?>" 
                           placeholder="搜索对话内容">
                </div>
                <div class="col-md-3">
                    <label class="form-label">开始日期</label>
                    <input type="date" class="form-control" name="date_from" 
                           value="<?= htmlspecialchars($date_from) ?>">
                </div>
                <div class="col-md-3">
                    <label class="form-label">结束日期</label>
                    <input type="date" class="form-control" name="date_to" 
                           value="<?= htmlspecialchars($date_to) ?>">
                </div>
                <div class="col-md-2 d-flex align-items-end">
                    <button type="submit" class="btn btn-primary me-2">
                        <i class="bi bi-search"></i> 搜索
                    </button>
                    <a href="?" class="btn btn-outline-secondary">
                        <i class="bi bi-arrow-clockwise"></i> 重置
                    </a>
                </div>
            </form>
        </div>

        <!-- 对话列表 -->
        <div class="conversations-container">
            <?php if (!empty($conversations)): ?>
                <?php foreach ($conversations as $conv): ?>
                    <div class="conversation-card <?= $conv['is_human_service'] ? 'human-service' : '' ?>">
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <div>
                                <strong>
                                    <?php if ($conv['username']): ?>
                                        <?= htmlspecialchars($conv['username']) ?>
                                        <?php if ($conv['phone']): ?>
                                            (<?= htmlspecialchars($conv['phone']) ?>)
                                        <?php endif; ?>
                                    <?php else: ?>
                                        访客 (<?= substr($conv['session_id'], -8) ?>)
                                    <?php endif; ?>
                                </strong>
                                <?php if ($conv['is_human_service']): ?>
                                    <span class="badge bg-warning">人工客服</span>
                                <?php endif; ?>
                            </div>
                            <small class="text-muted">
                                <?= date('Y-m-d H:i:s', strtotime($conv['created_at'])) ?>
                            </small>
                        </div>

                        <!-- 用户消息 -->
                        <div class="message-bubble user">
                            <strong>用户：</strong><?= htmlspecialchars($conv['user_message']) ?>
                        </div>

                        <!-- 机器人回复 -->
                        <?php if ($conv['bot_reply']): ?>
                            <div class="message-bubble bot">
                                <strong>机器人：</strong><?= nl2br(htmlspecialchars($conv['bot_reply'])) ?>
                            </div>
                        <?php endif; ?>

                        <!-- 人工回复 -->
                        <?php if ($conv['admin_reply']): ?>
                            <div class="message-bubble admin">
                                <strong>客服：</strong><?= nl2br(htmlspecialchars($conv['admin_reply'])) ?>
                            </div>
                        <?php endif; ?>
                    </div>
                <?php endforeach; ?>
            <?php else: ?>
                <div class="text-center py-5">
                    <i class="bi bi-chat-square-text" style="font-size: 3rem; color: #ccc;"></i>
                    <h5 class="mt-3 text-muted">暂无对话记录</h5>
                    <p class="text-muted">当用户开始使用客服功能时，对话记录会显示在这里</p>
                </div>
            <?php endif; ?>
        </div>

        <!-- 分页 -->
        <?php if ($total_pages > 1): ?>
            <nav aria-label="对话分页">
                <ul class="pagination justify-content-center">
                    <?php if ($page > 1): ?>
                        <li class="page-item">
                            <a class="page-link" href="?page=<?= $page - 1 ?>&search=<?= urlencode($search) ?>&date_from=<?= urlencode($date_from) ?>&date_to=<?= urlencode($date_to) ?>">
                                <i class="bi bi-chevron-left"></i>
                            </a>
                        </li>
                    <?php endif; ?>
                    
                    <?php for ($i = max(1, $page - 2); $i <= min($total_pages, $page + 2); $i++): ?>
                        <li class="page-item <?= $i === $page ? 'active' : '' ?>">
                            <a class="page-link" href="?page=<?= $i ?>&search=<?= urlencode($search) ?>&date_from=<?= urlencode($date_from) ?>&date_to=<?= urlencode($date_to) ?>">
                                <?= $i ?>
                            </a>
                        </li>
                    <?php endfor; ?>
                    
                    <?php if ($page < $total_pages): ?>
                        <li class="page-item">
                            <a class="page-link" href="?page=<?= $page + 1 ?>&search=<?= urlencode($search) ?>&date_from=<?= urlencode($date_from) ?>&date_to=<?= urlencode($date_to) ?>">
                                <i class="bi bi-chevron-right"></i>
                            </a>
                        </li>
                    <?php endif; ?>
                </ul>
            </nav>
        <?php endif; ?>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
