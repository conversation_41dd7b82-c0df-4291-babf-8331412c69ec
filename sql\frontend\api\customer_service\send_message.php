<?php
/**
 * Vue客服系统 - 发送消息接口
 * 处理用户和客服发送的消息
 */

header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// 处理预检请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

// 只允许POST请求
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => '只允许POST请求']);
    exit;
}

// 引入数据库配置
require_once '../../config/database.php';

// 获取POST数据
$input = json_decode(file_get_contents('php://input'), true);

// 验证必要参数
$session_id = $input['session_id'] ?? '';
$sender_type = $input['sender_type'] ?? ''; // user, customer_service, bot
$sender_id = $input['sender_id'] ?? 0;
$sender_name = $input['sender_name'] ?? '';
$message_type = $input['message_type'] ?? 'text'; // text, image, file
$content = $input['content'] ?? '';
$file_url = $input['file_url'] ?? null;
$file_name = $input['file_name'] ?? null;

// 参数验证
if (empty($session_id) || empty($sender_type) || empty($content)) {
    echo json_encode([
        'success' => false,
        'message' => '缺少必要参数'
    ]);
    exit;
}

try {
    // 开始事务
    $pdo->beginTransaction();

    // 1. 插入消息记录
    $stmt = $pdo->prepare("
        INSERT INTO customer_service_messages (
            session_id,
            sender_type,
            sender_id,
            sender_name,
            message_type,
            content,
            file_url,
            file_name,
            created_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, NOW())
    ");
    
    $stmt->execute([
        $session_id,
        $sender_type,
        $sender_id,
        $sender_name,
        $message_type,
        $content,
        $file_url,
        $file_name
    ]);

    $message_id = $pdo->lastInsertId();

    // 2. 更新会话信息
    $stmt = $pdo->prepare("
        UPDATE customer_service_sessions 
        SET 
            message_count = message_count + 1,
            updated_at = NOW()
        WHERE session_id = ?
    ");
    $stmt->execute([$session_id]);

    // 3. 如果是用户发送的消息，检查是否需要机器人自动回复
    if ($sender_type === 'user') {
        // 获取机器人配置
        $stmt = $pdo->prepare("
            SELECT * FROM customer_service_bot_config 
            WHERE status = 'active' AND auto_reply_enabled = 1 
            LIMIT 1
        ");
        $stmt->execute();
        $bot_config = $stmt->fetch(PDO::FETCH_ASSOC);

        if ($bot_config) {
            // 检查关键词匹配
            $stmt = $pdo->prepare("
                SELECT reply_content, priority 
                FROM customer_service_replies 
                WHERE is_enabled = 1 
                ORDER BY priority DESC
            ");
            $stmt->execute();
            $reply_rules = $stmt->fetchAll(PDO::FETCH_ASSOC);

            $bot_reply = null;
            foreach ($reply_rules as $rule) {
                $keywords = json_decode($rule['keywords'], true);
                if ($keywords) {
                    foreach ($keywords as $keyword) {
                        if (stripos($content, $keyword) !== false) {
                            $bot_reply = $rule['reply_content'];
                            break 2;
                        }
                    }
                }
            }

            // 如果没有匹配的关键词，使用默认回复
            if (!$bot_reply) {
                $bot_reply = $bot_config['default_reply'];
            }

            // 发送机器人回复
            if ($bot_reply) {
                $stmt = $pdo->prepare("
                    INSERT INTO customer_service_messages (
                        session_id,
                        sender_type,
                        sender_id,
                        sender_name,
                        message_type,
                        content,
                        created_at
                    ) VALUES (?, 'bot', 0, ?, 'text', ?, NOW())
                ");
                $stmt->execute([
                    $session_id,
                    $bot_config['name'],
                    $bot_reply
                ]);

                // 更新消息计数
                $stmt = $pdo->prepare("
                    UPDATE customer_service_sessions 
                    SET message_count = message_count + 1 
                    WHERE session_id = ?
                ");
                $stmt->execute([$session_id]);
            }
        }
    }

    // 4. 创建实时通知
    $notification_type = ($sender_type === 'user') ? 'new_message' : 'new_message';
    $stmt = $pdo->prepare("
        INSERT INTO vue_customer_service_notifications (
            session_id,
            user_id,
            customer_service_id,
            notification_type,
            message,
            created_at
        ) VALUES (?, ?, ?, ?, ?, NOW())
    ");
    
    $user_id = ($sender_type === 'user') ? $sender_id : null;
    $cs_id = ($sender_type === 'customer_service') ? $sender_id : null;
    $notification_message = "收到新消息: " . mb_substr($content, 0, 50);
    
    $stmt->execute([
        $session_id,
        $user_id,
        $cs_id,
        $notification_type,
        $notification_message
    ]);

    // 提交事务
    $pdo->commit();

    // 返回成功响应
    echo json_encode([
        'success' => true,
        'message' => '消息发送成功',
        'data' => [
            'message_id' => $message_id,
            'session_id' => $session_id,
            'timestamp' => time()
        ]
    ]);

} catch (Exception $e) {
    // 回滚事务
    $pdo->rollBack();
    
    error_log("发送消息失败: " . $e->getMessage());
    
    echo json_encode([
        'success' => false,
        'message' => '发送消息失败',
        'error' => $e->getMessage()
    ]);
}
?>
