<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>页面链接测试 - 趣玩星球管理后台</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 40px;
            background: #f8fafc;
            color: #1f2937;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 16px;
            padding: 32px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
        }

        h1 {
            color: #40E0D0;
            text-align: center;
            margin-bottom: 32px;
            font-size: 2rem;
        }

        .section {
            margin-bottom: 32px;
        }

        .section h2 {
            color: #374151;
            border-bottom: 2px solid #e5e7eb;
            padding-bottom: 8px;
            margin-bottom: 16px;
        }

        .link-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 16px;
        }

        .link-card {
            background: #f9fafb;
            border: 2px solid #e5e7eb;
            border-radius: 12px;
            padding: 16px;
            transition: all 0.3s ease;
            text-decoration: none;
            color: inherit;
        }

        .link-card:hover {
            border-color: #40E0D0;
            background: #f0fdfa;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(64, 224, 208, 0.2);
        }

        .link-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: #1f2937;
        }

        .link-desc {
            font-size: 0.875rem;
            color: #6b7280;
            margin-bottom: 8px;
        }

        .link-url {
            font-size: 0.75rem;
            color: #9ca3af;
            font-family: monospace;
            background: #f3f4f6;
            padding: 4px 8px;
            border-radius: 6px;
        }

        .status {
            display: inline-block;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 0.75rem;
            font-weight: 600;
            margin-left: 8px;
        }

        .status-working {
            background: rgba(16, 185, 129, 0.1);
            color: #059669;
        }

        .status-new {
            background: rgba(64, 224, 208, 0.1);
            color: #0d9488;
        }

        .note {
            background: #fef3c7;
            border: 1px solid #f59e0b;
            border-radius: 8px;
            padding: 16px;
            margin-top: 24px;
        }

        .note h3 {
            color: #92400e;
            margin-top: 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔗 页面链接测试</h1>

        <div class="section">
            <h2>🔐 权限管理模块</h2>
            <div class="link-grid">
                <a href="/houtai_backup/permission/test.php" class="link-card">
                    <div class="link-title">权限管理测试 <span class="status status-new">TEST</span></div>
                    <div class="link-desc">测试权限管理模块所有功能</div>
                    <div class="link-url">/houtai_backup/permission/test.php</div>
                </a>

                <a href="/houtai_backup/permission/index.php" class="link-card">
                    <div class="link-title">权限申请 <span class="status status-new">FIXED</span></div>
                    <div class="link-desc">员工申请新的系统权限</div>
                    <div class="link-url">/houtai_backup/permission/index.php</div>
                </a>

                <a href="/houtai_backup/permission/approval.php" class="link-card">
                    <div class="link-title">权限审批 <span class="status status-new">FIXED</span></div>
                    <div class="link-desc">管理员审批员工权限申请</div>
                    <div class="link-url">/houtai_backup/permission/approval.php</div>
                </a>

                <a href="/houtai_backup/permission/roles.php" class="link-card">
                    <div class="link-title">角色管理 <span class="status status-new">FIXED</span></div>
                    <div class="link-desc">创建、编辑、删除系统角色</div>
                    <div class="link-url">/houtai_backup/permission/roles.php</div>
                </a>

                <a href="/houtai_backup/permission/config.php" class="link-card">
                    <div class="link-title">权限配置 <span class="status status-new">FIXED</span></div>
                    <div class="link-desc">管理系统权限和功能模块</div>
                    <div class="link-url">/houtai_backup/permission/config.php</div>
                </a>
            </div>
        </div>

        <div class="section">
            <h2>🏢 部门管理模块</h2>
            <div class="link-grid">
                <a href="/houtai_backup/department/test.php" class="link-card">
                    <div class="link-title">部门管理测试 <span class="status status-new">TEST</span></div>
                    <div class="link-desc">测试部门管理模块所有功能</div>
                    <div class="link-url">/houtai_backup/department/test.php</div>
                </a>

                <a href="/houtai_backup/department/index.php" class="link-card">
                    <div class="link-title">部门总览 <span class="status status-new">FIXED</span></div>
                    <div class="link-desc">超级管理员管理所有部门</div>
                    <div class="link-url">/houtai_backup/department/index.php</div>
                </a>

                <a href="/houtai_backup/department/my_department.php" class="link-card">
                    <div class="link-title">我的部门 <span class="status status-new">FIXED</span></div>
                    <div class="link-desc">部门管理员管理自己的部门</div>
                    <div class="link-url">/houtai_backup/department/my_department.php</div>
                </a>
            </div>
        </div>

        <div class="section">
            <h2>📊 现有页面</h2>
            <div class="link-grid">
                <a href="/houtai_backup/home.php" class="link-card">
                    <div class="link-title">首页 <span class="status status-working">WORKING</span></div>
                    <div class="link-desc">管理后台首页，已增强审核功能</div>
                    <div class="link-url">/houtai_backup/home.php</div>
                </a>

                <a href="/houtai_backup/dashboard.php" class="link-card">
                    <div class="link-title">数据统计 <span class="status status-working">WORKING</span></div>
                    <div class="link-desc">数据统计仪表盘</div>
                    <div class="link-url">/houtai_backup/dashboard.php</div>
                </a>

                <a href="/houtai_backup/user_management/index.php" class="link-card">
                    <div class="link-title">用户管理 <span class="status status-working">WORKING</span></div>
                    <div class="link-desc">平台用户管理</div>
                    <div class="link-url">/houtai_backup/user_management/index.php</div>
                </a>

                <a href="/houtai_backup/verification/index.php" class="link-card">
                    <div class="link-title">实名认证审核 <span class="status status-working">WORKING</span></div>
                    <div class="link-desc">用户实名认证审核</div>
                    <div class="link-url">/houtai_backup/verification/index.php</div>
                </a>
            </div>
        </div>

        <div class="note">
            <h3>📝 修复说明</h3>
            <p><strong>问题原因：</strong>之前使用了绝对服务器路径导致include文件找不到。</p>
            <p><strong>解决方案：</strong>已将所有路径改为相对路径：</p>
            <ul>
                <li>菜单链接：使用绝对URL路径 <code>/houtai_backup/xxx.php</code></li>
                <li>CSS/JS文件：使用相对路径 <code>../assets/xxx</code></li>
                <li>Include文件：使用相对路径 <code>../includes/xxx.php</code></li>
            </ul>
            <p><strong>测试方法：</strong>点击上方链接测试每个页面是否正常访问。</p>
        </div>

        <div style="text-align: center; margin-top: 32px; color: #6b7280;">
            <p>🚀 所有页面都采用年轻化、UI精美、布局美观、交互性强的设计！</p>
        </div>
    </div>
</body>
</html>
