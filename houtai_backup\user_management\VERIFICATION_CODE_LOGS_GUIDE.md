# 🔑 验证码发送日志系统使用指南

## 🎯 功能概述

现在后台管理员发送验证码时会自动记录详细的操作日志，就像查看用户敏感信息一样，所有操作都有完整的审计记录！

## ✅ 已完成的功能

### 1. 数据库结构优化
- ✅ 扩展了 `admin_logs` 表，增加员工ID、部门、目标用户等字段
- ✅ 创建了 `user_logs` 表，专门记录用户相关的操作日志
- ✅ 添加了索引优化查询性能
- ✅ 创建了存储过程简化日志记录

### 2. 自动日志记录
- ✅ 发送验证码时自动记录管理员操作日志
- ✅ 记录用户操作日志，包含详细的操作信息
- ✅ 记录操作员信息（姓名、工号、部门）
- ✅ 记录客户端信息（IP地址、用户代理）

### 3. 日志查看界面
- ✅ 专门的验证码日志查看页面
- ✅ 支持多条件搜索（用户、管理员、日期范围）
- ✅ 分页显示，性能优化
- ✅ 详细的日志信息展示

### 4. 用户详情页集成
- ✅ 在用户详情页添加"验证码日志"按钮
- ✅ 点击可直接查看该用户的验证码发送记录
- ✅ 在侧边栏菜单中添加验证码日志入口

## 🚀 使用方法

### 第一步：执行数据库脚本

**请在数据库管理工具中执行以下SQL脚本：**

```sql
-- 文件位置：houtai_backup/user_management/fix_verification_logs_database.sql
```

这个脚本会：
- 修复 `admin_logs` 表结构
- 创建 `user_logs` 表
- 添加必要的索引
- 创建存储过程
- 创建查询视图

### 第二步：发送验证码

1. **访问用户详情页面**：
   ```
   https://vansmrz.vancrest.xyz/houtai_backup/user_management/detail.php?id=用户ID
   ```

2. **点击"发送验证码"按钮**

3. **填写验证码信息并发送**

4. **系统会自动记录以下日志**：
   - 管理员操作日志（`admin_logs` 表）
   - 用户操作日志（`user_logs` 表）
   - 操作员信息（姓名、工号、部门）
   - 操作详情（验证码、手机号、类型、备注）
   - 客户端信息（IP地址、浏览器信息）

### 第三步：查看日志记录

#### 方法1：通过用户详情页
1. 在用户详情页点击"验证码日志"按钮
2. 会在新窗口中打开该用户的验证码发送记录

#### 方法2：通过侧边栏菜单
1. 点击左侧菜单"用户管理" → "验证码日志"
2. 可以查看所有管理员的验证码发送记录
3. 支持搜索和筛选

## 📊 日志记录内容

### 管理员日志（admin_logs表）
- **操作员信息**：管理员ID、姓名、工号、部门
- **目标用户**：用户ID
- **操作类型**：发送验证码
- **操作详情**：完整的操作描述
- **操作时间**：精确到秒

### 用户日志（user_logs表）
- **用户信息**：用户ID
- **操作员信息**：管理员姓名、工号、部门
- **日志类型**：发送验证码
- **日志内容**：详细的操作描述
- **客户端信息**：IP地址、用户代理
- **操作时间**：精确到秒

### 日志内容示例
```
向用户 张三（趣玩ID：1234567）发送验证码到手机号 13800138000，类型：admin_send，备注：测试发送，验证码：123456，有效期：5分钟
```

## 🔍 日志查看功能

### 1. 搜索功能
- **用户搜索**：支持用户名、趣玩ID、手机号搜索
- **管理员搜索**：支持管理员姓名、工号搜索
- **日期范围**：支持按日期范围筛选
- **组合搜索**：支持多条件组合搜索

### 2. 显示信息
- **用户信息**：用户名、趣玩ID
- **操作员信息**：管理员姓名、工号、部门
- **验证码信息**：验证码、手机号、类型、状态
- **操作详情**：完整的操作描述
- **时间信息**：操作时间
- **客户端信息**：IP地址

### 3. 分页功能
- 每页显示20条记录
- 支持页面跳转
- 显示总记录数和页数

## 🛡️ 安全特性

### 1. 权限控制
- 只有登录的管理员才能查看日志
- 基于会话验证身份

### 2. 数据完整性
- 使用数据库事务确保日志记录完整
- 即使主要操作失败，也会尝试记录日志

### 3. 审计追踪
- 记录操作员的完整信息
- 记录客户端信息便于追踪
- 所有操作都有时间戳

## 📁 相关文件

### 数据库脚本
- `fix_verification_logs_database.sql` - 数据库修复脚本

### 后台文件
- `send_verification_code_debug.php` - 发送验证码API（已添加日志记录）
- `verification_code_logs.php` - 验证码日志查看页面
- `detail.php` - 用户详情页面（已添加日志按钮）

### 配置文件
- `includes/sidebar.php` - 侧边栏菜单（已添加日志入口）

## 🔧 故障排除

### 问题1：日志记录失败
**检查步骤**：
1. 确认数据库脚本已执行
2. 检查 `admin_logs` 和 `user_logs` 表是否存在
3. 查看PHP错误日志

### 问题2：日志页面无法访问
**检查步骤**：
1. 确认管理员已登录
2. 检查文件权限
3. 确认数据库连接正常

### 问题3：搜索功能异常
**检查步骤**：
1. 确认数据库索引已创建
2. 检查搜索参数格式
3. 查看数据库查询日志

## 🎉 使用效果

当一切正常工作时，您会看到：

1. ✅ 发送验证码后，后台提示包含日志记录信息
2. ✅ 在验证码日志页面可以看到详细的发送记录
3. ✅ 每条记录包含完整的操作信息和时间戳
4. ✅ 支持多种方式搜索和筛选日志
5. ✅ 用户详情页可以直接查看该用户的验证码记录

---

**现在您的验证码发送功能已经具备完整的审计日志功能，就像查看用户敏感信息一样安全可靠！** 🔒
