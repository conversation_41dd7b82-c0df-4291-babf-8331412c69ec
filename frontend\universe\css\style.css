/* 全局样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    font-family: 'PingFang SC', 'Helvetica Neue', Arial, sans-serif;
}

body {
    background-color: #f8f9fa;
    color: #333333;
    line-height: 1.6;
    min-height: 100vh;
    position: relative;
    padding-bottom: 60px; /* 为底部导航栏留出空间 */
}

a {
    text-decoration: none;
    color: inherit;
}

/* Toast提示样式 */
.toast {
    position: fixed;
    top: 20px;
    left: 50%;
    transform: translateX(-50%);
    background-color: rgba(255, 255, 255, 0.95);
    color: #333333;
    padding: 14px 24px;
    border-radius: 12px;
    box-shadow: 0 6px 16px rgba(0, 0, 0, 0.12);
    z-index: 9999;
    display: none;
    border-left: 4px solid #1E90FF;
    font-size: 15px;
    font-weight: 500;
    max-width: 90%;
    text-align: center;
    pointer-events: none;
}

/* 顶部导航栏 */
.header-bar {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    height: 50px;
    background-color: #1E90FF;
    color: white;
    display: flex;
    align-items: center;
    padding: 0 15px;
    z-index: 100;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.back-button {
    font-size: 18px;
    color: white;
    margin-right: 15px;
}

.header-title {
    font-size: 18px;
    font-weight: 500;
    flex: 1;
    text-align: center;
    margin-right: 30px;
}

.publish-icon {
    font-size: 16px;
    color: #1E90FF;
    background-color: white;
    width: 36px;
    height: 36px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease;
}

.publish-icon:active {
    transform: scale(0.95);
}

/* 内容容器 */
.content-container {
    padding: 60px 0 20px;
}

/* 搜索栏 */
.search-bar {
    background-color: white;
    border-radius: 8px;
    padding: 12px 15px;
    margin: 0 15px 15px;
    display: flex;
    align-items: center;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.search-bar i {
    color: #999;
    margin-right: 10px;
    font-size: 16px;
}

.search-bar input {
    border: none;
    outline: none;
    flex: 1;
    font-size: 15px;
    color: #333;
}

/* 分类筛选 */
.filter-section {
    margin-bottom: 15px;
}

.category-filter, .subcategory-filter {
    display: flex;
    overflow-x: auto;
    padding: 0 15px 10px;
    -webkit-overflow-scrolling: touch;
    scrollbar-width: none; /* Firefox */
}

.category-filter::-webkit-scrollbar, .subcategory-filter::-webkit-scrollbar {
    display: none; /* Chrome, Safari, Edge */
}

.filter-item {
    flex-shrink: 0;
    padding: 8px 15px;
    margin-right: 10px;
    background-color: white;
    border-radius: 20px;
    font-size: 14px;
    color: #666;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
    white-space: nowrap;
}

.filter-item.active {
    background-color: #1E90FF;
    color: white;
}

/* 内容列表 */
.content-list {
    padding: 0 15px;
}

.content-card {
    background-color: white;
    border-radius: 12px;
    margin-bottom: 15px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    overflow: hidden;
}

.card-header {
    padding: 15px;
    border-bottom: 1px solid #f0f0f0;
}

.user-info {
    display: flex;
    align-items: center;
}

.avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    overflow: hidden;
    margin-right: 10px;
}

.avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.user-meta {
    flex: 1;
}

.username {
    font-size: 15px;
    font-weight: 500;
    color: #333;
    margin-bottom: 3px;
}

.post-meta {
    display: flex;
    justify-content: space-between;
    font-size: 12px;
    color: #999;
}

.card-content {
    padding: 15px;
    display: block;
}

.post-title {
    font-size: 17px;
    font-weight: 600;
    margin-bottom: 10px;
    color: #333;
    line-height: 1.4;
}

.post-cover {
    margin-bottom: 10px;
    border-radius: 8px;
    overflow: hidden;
    height: 180px;
}

.post-cover img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.post-excerpt {
    font-size: 14px;
    color: #666;
    line-height: 1.6;
    margin-bottom: 10px;
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
}

.card-footer {
    padding: 10px 15px;
    border-top: 1px solid #f0f0f0;
}

.action-buttons {
    display: flex;
    justify-content: space-around;
}

.action-button {
    display: flex;
    align-items: center;
    color: #999;
    font-size: 14px;
}

.action-button i {
    margin-right: 5px;
}

/* 空状态 */
.empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 50px 20px;
    text-align: center;
}

.empty-state i {
    font-size: 48px;
    color: #ccc;
    margin-bottom: 15px;
}

.empty-state i.fa-exclamation-triangle {
    color: #ff9800;
}

.empty-state p {
    font-size: 16px;
    color: #999;
    margin-bottom: 20px;
}

.error-details {
    background-color: #f8f8f8;
    border-radius: 8px;
    padding: 12px;
    margin-bottom: 20px;
    font-size: 14px;
    color: #666;
    max-width: 100%;
    overflow-x: auto;
    text-align: left;
    border-left: 3px solid #ff3b30;
}

.error-actions {
    display: flex;
    gap: 10px;
    justify-content: center;
}

.btn {
    display: inline-block;
    padding: 10px 20px;
    background-color: #1E90FF;
    color: white;
    border-radius: 5px;
    font-weight: 500;
    transition: background-color 0.3s ease;
}

.btn:active {
    background-color: #187bcd;
}

.btn-secondary {
    background-color: #f0f0f0;
    color: #666;
}

.btn-secondary:active {
    background-color: #e0e0e0;
}

/* 底部导航栏样式 */
.bottom-nav {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background-color: white;
    display: flex;
    justify-content: space-around;
    padding: 8px 0;
    box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.05);
    z-index: 100;
}

.nav-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 5px 0;
    width: 20%;
}

.nav-item i {
    font-size: 24px;
    margin-bottom: 3px;
    color: #999;
}

.nav-item span {
    font-size: 12px;
    color: #999;
}

.nav-item.active i,
.nav-item.active span {
    color: #1E90FF;
}

/* 悬浮发布按钮 */
.floating-publish-btn {
    position: fixed;
    right: 20px;
    bottom: 80px;
    width: 56px;
    height: 56px;
    background: linear-gradient(135deg, #1E90FF, #40E0D0);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 24px;
    box-shadow: 0 4px 12px rgba(30, 144, 255, 0.4);
    z-index: 99;
    transition: all 0.3s ease;
}

.floating-publish-btn:active {
    transform: scale(0.95);
    box-shadow: 0 2px 8px rgba(30, 144, 255, 0.4);
}

/* 内容卡片交互效果 */
.content-card {
    transition: transform 0.2s ease, box-shadow 0.2s ease;
    cursor: pointer;
}

.content-card:active {
    transform: scale(0.98);
}

/* 点赞动画 */
.like-button {
    position: relative;
    overflow: visible;
}

.floating-heart {
    position: absolute;
    top: -20px;
    left: 50%;
    transform: translateX(-50%);
    color: #ff6b6b;
    font-size: 16px;
    animation: float-up 1s ease-out forwards;
    pointer-events: none;
}

@keyframes float-up {
    0% {
        opacity: 1;
        transform: translateX(-50%) translateY(0) scale(1);
    }
    100% {
        opacity: 0;
        transform: translateX(-50%) translateY(-30px) scale(1.5);
    }
}

/* 悬浮按钮动画 */
.floating-publish-btn {
    transform: scale(0);
    opacity: 0;
    transition: transform 0.3s ease, opacity 0.3s ease, box-shadow 0.3s ease;
}

.floating-publish-btn.show {
    transform: scale(1);
    opacity: 1;
}
