<?php
session_start();

// 生成验证码
function generateCaptcha() {
    $characters = 'ABCDEFGHIJKLMNPQRSTUVWXYZ123456789'; // 去除容易混淆的字符
    $captcha = '';
    for ($i = 0; $i < 4; $i++) {
        $captcha .= $characters[rand(0, strlen($characters) - 1)];
    }
    return $captcha;
}

// 生成验证码
$captcha = generateCaptcha();
$_SESSION['captcha'] = $captcha;

// 创建图像
$width = 120;
$height = 40;
$image = imagecreate($width, $height);

// 定义颜色
$bg_color = imagecolorallocate($image, 240, 240, 240);
$text_color = imagecolorallocate($image, 50, 50, 50);
$line_color = imagecolorallocate($image, 200, 200, 200);
$noise_color = imagecolorallocate($image, 180, 180, 180);

// 填充背景
imagefill($image, 0, 0, $bg_color);

// 添加干扰线
for ($i = 0; $i < 5; $i++) {
    imageline($image, 
        rand(0, $width), rand(0, $height),
        rand(0, $width), rand(0, $height),
        $line_color
    );
}

// 添加噪点
for ($i = 0; $i < 50; $i++) {
    imagesetpixel($image, rand(0, $width), rand(0, $height), $noise_color);
}

// 添加验证码文字
$font_size = 16;
$angle = 0;
$x = 15;
$y = 25;

for ($i = 0; $i < strlen($captcha); $i++) {
    $char = $captcha[$i];
    $char_angle = rand(-15, 15);
    $char_color = imagecolorallocate($image, rand(30, 100), rand(30, 100), rand(30, 100));
    
    // 使用内置字体
    imagestring($image, 5, $x + ($i * 22), $y + rand(-5, 5), $char, $char_color);
}

// 输出图像
header('Content-Type: image/png');
header('Cache-Control: no-cache, no-store, must-revalidate');
header('Pragma: no-cache');
header('Expires: 0');

imagepng($image);
imagedestroy($image);
?>
