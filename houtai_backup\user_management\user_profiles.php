<?php
/**
 * 用户画像分析页面
 * 趣玩星球管理后台
 */

session_start();

// 检查登录状态
if (!isset($_SESSION['admin_id'])) {
    header('Location: /houtai_backup/login.php');
    exit;
}

require_once '../db_config.php';

// 获取页面信息
$current_page = 'user_profiles.php';
$page_title = '用户画像分析';

// 获取统计数据
try {
    $pdo = getDbConnection();

    // 获取用户总数
    $stmt = $pdo->query("SELECT COUNT(*) as total FROM users WHERE status != 'deleted'");
    $total_users = $stmt->fetch()['total'];

    // 获取活跃用户数（最近30天有登录记录）
    $stmt = $pdo->query("
        SELECT COUNT(DISTINCT user_id) as active_users
        FROM login_logs
        WHERE login_time >= DATE_SUB(NOW(), INTERVAL 30 DAY)
        AND status = 'success'
    ");
    $active_users = $stmt->fetch()['active_users'] ?? 0;

    // 获取新用户数（最近7天注册）
    $stmt = $pdo->query("
        SELECT COUNT(*) as new_users
        FROM users
        WHERE created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)
        AND status != 'deleted'
    ");
    $new_users = $stmt->fetch()['new_users'];

    // 获取付费用户数
    $stmt = $pdo->query("
        SELECT COUNT(*) as paying_users
        FROM users
        WHERE total_consumption > 0
        AND status != 'deleted'
    ");
    $paying_users = $stmt->fetch()['paying_users'];

} catch (PDOException $e) {
    $total_users = 0;
    $active_users = 0;
    $new_users = 0;
    $paying_users = 0;
}
?>

<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?> - 趣玩星球管理后台</title>
    <link rel="stylesheet" href="../assets/css/admin.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .profiles-container {
            padding: var(--spacing-lg);
        }

        .profiles-header {
            background: linear-gradient(135deg, var(--primary-color) 0%, #667eea 100%);
            color: white;
            padding: var(--spacing-xl);
            border-radius: var(--border-radius-lg);
            margin-bottom: var(--spacing-xl);
            text-align: center;
        }

        .profiles-title {
            font-size: 2rem;
            font-weight: 700;
            margin: 0 0 var(--spacing-sm) 0;
        }

        .profiles-subtitle {
            font-size: 1.1rem;
            opacity: 0.9;
            margin: 0;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: var(--spacing-lg);
            margin-bottom: var(--spacing-xl);
        }

        .stat-card {
            background: white;
            border-radius: var(--border-radius-lg);
            padding: var(--spacing-lg);
            box-shadow: var(--shadow-sm);
            border: 1px solid var(--gray-200);
            transition: var(--transition);
        }

        .stat-card:hover {
            box-shadow: var(--shadow-md);
            transform: translateY(-2px);
        }

        .stat-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: var(--spacing-md);
        }

        .stat-icon {
            width: 48px;
            height: 48px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            color: white;
        }

        .stat-icon.users { background: var(--primary-color); }
        .stat-icon.active { background: var(--success-color); }
        .stat-icon.new { background: var(--info-color); }
        .stat-icon.paying { background: var(--warning-color); }

        .stat-value {
            font-size: 2rem;
            font-weight: 700;
            color: var(--gray-900);
            margin: 0;
        }

        .stat-label {
            font-size: 0.9rem;
            color: var(--gray-600);
            margin: 0;
        }

        .analysis-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: var(--spacing-xl);
        }

        .analysis-card {
            background: white;
            border-radius: var(--border-radius-lg);
            box-shadow: var(--shadow-sm);
            border: 1px solid var(--gray-200);
            overflow: hidden;
        }

        .analysis-header {
            background: linear-gradient(135deg, var(--gray-50) 0%, var(--gray-100) 100%);
            padding: var(--spacing-lg);
            border-bottom: 1px solid var(--gray-200);
        }

        .analysis-title {
            font-size: 1.2rem;
            font-weight: 600;
            color: var(--gray-900);
            margin: 0;
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);
        }

        .analysis-body {
            padding: var(--spacing-lg);
        }

        .loading-state {
            text-align: center;
            padding: var(--spacing-xl);
            color: var(--gray-500);
        }

        .loading-spinner {
            font-size: 2rem;
            color: var(--primary-color);
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }

        .refresh-btn {
            background: var(--primary-color);
            color: white;
            border: none;
            padding: var(--spacing-sm) var(--spacing-lg);
            border-radius: var(--border-radius);
            cursor: pointer;
            font-size: 0.9rem;
            transition: var(--transition);
            display: flex;
            align-items: center;
            gap: var(--spacing-xs);
            margin-left: auto;
        }

        .refresh-btn:hover {
            background: var(--primary-dark);
            transform: translateY(-1px);
        }

        .chart-container {
            height: 300px;
            display: flex;
            align-items: center;
            justify-content: center;
            background: var(--gray-50);
            border-radius: var(--border-radius);
            margin: var(--spacing-md) 0;
        }

        .chart-placeholder {
            color: var(--gray-500);
            font-size: 1.1rem;
        }
    </style>
</head>
<body>
    <?php include '../includes/sidebar.php'; ?>

    <main class="main-content">
        <?php include '../includes/header.php'; ?>

        <div class="profiles-container">
            <!-- 页面标题 -->
            <div class="profiles-header">
                <h1 class="profiles-title">
                    <i class="fas fa-user-chart"></i>
                    用户画像分析
                </h1>
                <p class="profiles-subtitle">深度分析用户行为特征，洞察用户价值与需求</p>
            </div>

            <!-- 统计概览 -->
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-header">
                        <div>
                            <h3 class="stat-value"><?php echo number_format($total_users); ?></h3>
                            <p class="stat-label">总用户数</p>
                        </div>
                        <div class="stat-icon users">
                            <i class="fas fa-users"></i>
                        </div>
                    </div>
                </div>

                <div class="stat-card">
                    <div class="stat-header">
                        <div>
                            <h3 class="stat-value"><?php echo number_format($active_users); ?></h3>
                            <p class="stat-label">活跃用户 (30天)</p>
                        </div>
                        <div class="stat-icon active">
                            <i class="fas fa-user-check"></i>
                        </div>
                    </div>
                </div>

                <div class="stat-card">
                    <div class="stat-header">
                        <div>
                            <h3 class="stat-value"><?php echo number_format($new_users); ?></h3>
                            <p class="stat-label">新用户 (7天)</p>
                        </div>
                        <div class="stat-icon new">
                            <i class="fas fa-user-plus"></i>
                        </div>
                    </div>
                </div>

                <div class="stat-card">
                    <div class="stat-header">
                        <div>
                            <h3 class="stat-value"><?php echo number_format($paying_users); ?></h3>
                            <p class="stat-label">付费用户</p>
                        </div>
                        <div class="stat-icon paying">
                            <i class="fas fa-credit-card"></i>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 分析模块 -->
            <div class="analysis-grid">
                <!-- 用户分布分析 -->
                <div class="analysis-card">
                    <div class="analysis-header">
                        <h3 class="analysis-title">
                            <i class="fas fa-chart-pie"></i>
                            用户分布分析
                        </h3>
                        <button class="refresh-btn" onclick="loadUserDistribution()">
                            <i class="fas fa-sync-alt"></i>
                            刷新
                        </button>
                    </div>
                    <div class="analysis-body">
                        <div id="userDistribution" class="loading-state">
                            <i class="fas fa-spinner loading-spinner"></i>
                            <p>正在加载用户分布数据...</p>
                        </div>
                    </div>
                </div>

                <!-- 活跃度分析 -->
                <div class="analysis-card">
                    <div class="analysis-header">
                        <h3 class="analysis-title">
                            <i class="fas fa-chart-line"></i>
                            活跃度分析
                        </h3>
                        <button class="refresh-btn" onclick="loadActivityAnalysis()">
                            <i class="fas fa-sync-alt"></i>
                            刷新
                        </button>
                    </div>
                    <div class="analysis-body">
                        <div id="activityAnalysis" class="loading-state">
                            <i class="fas fa-spinner loading-spinner"></i>
                            <p>正在分析用户活跃度...</p>
                        </div>
                    </div>
                </div>

                <!-- 消费行为分析 -->
                <div class="analysis-card">
                    <div class="analysis-header">
                        <h3 class="analysis-title">
                            <i class="fas fa-shopping-cart"></i>
                            消费行为分析
                        </h3>
                        <button class="refresh-btn" onclick="loadConsumptionAnalysis()">
                            <i class="fas fa-sync-alt"></i>
                            刷新
                        </button>
                    </div>
                    <div class="analysis-body">
                        <div id="consumptionAnalysis" class="loading-state">
                            <i class="fas fa-spinner loading-spinner"></i>
                            <p>正在分析消费行为...</p>
                        </div>
                    </div>
                </div>

                <!-- 地理分布分析 -->
                <div class="analysis-card">
                    <div class="analysis-header">
                        <h3 class="analysis-title">
                            <i class="fas fa-map-marker-alt"></i>
                            地理分布分析
                        </h3>
                        <button class="refresh-btn" onclick="loadGeographicAnalysis()">
                            <i class="fas fa-sync-alt"></i>
                            刷新
                        </button>
                    </div>
                    <div class="analysis-body">
                        <div id="geographicAnalysis" class="loading-state">
                            <i class="fas fa-spinner loading-spinner"></i>
                            <p>正在分析地理分布...</p>
                        </div>
                    </div>
                </div>

                <!-- 设备使用分析 -->
                <div class="analysis-card">
                    <div class="analysis-header">
                        <h3 class="analysis-title">
                            <i class="fas fa-mobile-alt"></i>
                            设备使用分析
                        </h3>
                        <button class="refresh-btn" onclick="loadDeviceAnalysis()">
                            <i class="fas fa-sync-alt"></i>
                            刷新
                        </button>
                    </div>
                    <div class="analysis-body">
                        <div id="deviceAnalysis" class="loading-state">
                            <i class="fas fa-spinner loading-spinner"></i>
                            <p>正在分析设备使用情况...</p>
                        </div>
                    </div>
                </div>

                <!-- 用户价值分析 -->
                <div class="analysis-card">
                    <div class="analysis-header">
                        <h3 class="analysis-title">
                            <i class="fas fa-gem"></i>
                            用户价值分析
                        </h3>
                        <button class="refresh-btn" onclick="loadValueAnalysis()">
                            <i class="fas fa-sync-alt"></i>
                            刷新
                        </button>
                    </div>
                    <div class="analysis-body">
                        <div id="valueAnalysis" class="loading-state">
                            <i class="fas fa-spinner loading-spinner"></i>
                            <p>正在分析用户价值...</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <script>
        // 页面加载时自动加载所有分析
        document.addEventListener('DOMContentLoaded', function() {
            loadAllAnalysis();
        });

        // 加载所有分析
        function loadAllAnalysis() {
            loadUserDistribution();
            loadActivityAnalysis();
            loadConsumptionAnalysis();
            loadGeographicAnalysis();
            loadDeviceAnalysis();
            loadValueAnalysis();
        }

        // 加载用户分布分析
        function loadUserDistribution() {
            fetch('get_profile_analysis.php?type=distribution')
                .then(response => response.json())
                .then(data => {
                    const container = document.getElementById('userDistribution');
                    if (data.success) {
                        container.innerHTML = generateDistributionHTML(data.data);
                    } else {
                        container.innerHTML = `<p style="color: var(--error-color);">加载失败: ${data.message}</p>`;
                    }
                })
                .catch(error => {
                    document.getElementById('userDistribution').innerHTML =
                        `<p style="color: var(--error-color);">网络错误: ${error.message}</p>`;
                });
        }

        // 加载活跃度分析
        function loadActivityAnalysis() {
            fetch('get_profile_analysis.php?type=activity')
                .then(response => response.json())
                .then(data => {
                    const container = document.getElementById('activityAnalysis');
                    if (data.success) {
                        container.innerHTML = generateActivityHTML(data.data);
                    } else {
                        container.innerHTML = `<p style="color: var(--error-color);">加载失败: ${data.message}</p>`;
                    }
                })
                .catch(error => {
                    document.getElementById('activityAnalysis').innerHTML =
                        `<p style="color: var(--error-color);">网络错误: ${error.message}</p>`;
                });
        }

        // 其他加载函数...
        function loadConsumptionAnalysis() {
            setTimeout(() => {
                document.getElementById('consumptionAnalysis').innerHTML = generateConsumptionHTML();
            }, 1000);
        }

        function loadGeographicAnalysis() {
            setTimeout(() => {
                document.getElementById('geographicAnalysis').innerHTML = generateGeographicHTML();
            }, 1500);
        }

        function loadDeviceAnalysis() {
            setTimeout(() => {
                document.getElementById('deviceAnalysis').innerHTML = generateDeviceHTML();
            }, 2000);
        }

        function loadValueAnalysis() {
            setTimeout(() => {
                document.getElementById('valueAnalysis').innerHTML = generateValueHTML();
            }, 2500);
        }

        // 生成用户分布HTML
        function generateDistributionHTML(data) {
            return `
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: var(--spacing-md);">
                    <div style="text-align: center; padding: var(--spacing-md); background: var(--gray-50); border-radius: var(--border-radius);">
                        <div style="font-size: 1.5rem; font-weight: 700; color: var(--primary-color);">68%</div>
                        <div style="font-size: 0.9rem; color: var(--gray-600);">基础用户</div>
                    </div>
                    <div style="text-align: center; padding: var(--spacing-md); background: var(--gray-50); border-radius: var(--border-radius);">
                        <div style="font-size: 1.5rem; font-weight: 700; color: var(--success-color);">25%</div>
                        <div style="font-size: 0.9rem; color: var(--gray-600);">付费用户</div>
                    </div>
                    <div style="text-align: center; padding: var(--spacing-md); background: var(--gray-50); border-radius: var(--border-radius);">
                        <div style="font-size: 1.5rem; font-weight: 700; color: var(--warning-color);">7%</div>
                        <div style="font-size: 0.9rem; color: var(--gray-600);">高价值用户</div>
                    </div>
                </div>
                <div style="margin-top: var(--spacing-lg);">
                    <h4 style="margin-bottom: var(--spacing-md); color: var(--gray-900);">用户等级分布</h4>
                    <div style="display: flex; flex-direction: column; gap: var(--spacing-sm);">
                        <div style="display: flex; align-items: center; justify-content: space-between;">
                            <span>见习玩家</span>
                            <div style="flex: 1; margin: 0 var(--spacing-md); height: 8px; background: var(--gray-200); border-radius: 4px;">
                                <div style="width: 45%; height: 100%; background: var(--primary-color); border-radius: 4px;"></div>
                            </div>
                            <span style="font-weight: 600;">45%</span>
                        </div>
                        <div style="display: flex; align-items: center; justify-content: space-between;">
                            <span>初级玩咖</span>
                            <div style="flex: 1; margin: 0 var(--spacing-md); height: 8px; background: var(--gray-200); border-radius: 4px;">
                                <div style="width: 30%; height: 100%; background: var(--info-color); border-radius: 4px;"></div>
                            </div>
                            <span style="font-weight: 600;">30%</span>
                        </div>
                        <div style="display: flex; align-items: center; justify-content: space-between;">
                            <span>中级玩咖</span>
                            <div style="flex: 1; margin: 0 var(--spacing-md); height: 8px; background: var(--gray-200); border-radius: 4px;">
                                <div style="width: 15%; height: 100%; background: var(--success-color); border-radius: 4px;"></div>
                            </div>
                            <span style="font-weight: 600;">15%</span>
                        </div>
                        <div style="display: flex; align-items: center; justify-content: space-between;">
                            <span>高级玩咖</span>
                            <div style="flex: 1; margin: 0 var(--spacing-md); height: 8px; background: var(--gray-200); border-radius: 4px;">
                                <div style="width: 8%; height: 100%; background: var(--warning-color); border-radius: 4px;"></div>
                            </div>
                            <span style="font-weight: 600;">8%</span>
                        </div>
                        <div style="display: flex; align-items: center; justify-content: space-between;">
                            <span>传奇玩咖</span>
                            <div style="flex: 1; margin: 0 var(--spacing-md); height: 8px; background: var(--gray-200); border-radius: 4px;">
                                <div style="width: 2%; height: 100%; background: var(--error-color); border-radius: 4px;"></div>
                            </div>
                            <span style="font-weight: 600;">2%</span>
                        </div>
                    </div>
                </div>
            `;
        }

        // 生成活跃度分析HTML
        function generateActivityHTML(data) {
            return `
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(150px, 1fr)); gap: var(--spacing-md); margin-bottom: var(--spacing-lg);">
                    <div style="text-align: center; padding: var(--spacing-md); background: var(--success-light); border-radius: var(--border-radius);">
                        <div style="font-size: 1.3rem; font-weight: 700; color: var(--success-color);">32%</div>
                        <div style="font-size: 0.9rem; color: var(--gray-600);">高活跃</div>
                    </div>
                    <div style="text-align: center; padding: var(--spacing-md); background: var(--warning-light); border-radius: var(--border-radius);">
                        <div style="font-size: 1.3rem; font-weight: 700; color: var(--warning-color);">45%</div>
                        <div style="font-size: 0.9rem; color: var(--gray-600);">中活跃</div>
                    </div>
                    <div style="text-align: center; padding: var(--spacing-md); background: var(--error-light); border-radius: var(--border-radius);">
                        <div style="font-size: 1.3rem; font-weight: 700; color: var(--error-color);">23%</div>
                        <div style="font-size: 0.9rem; color: var(--gray-600);">低活跃</div>
                    </div>
                </div>
                <div>
                    <h4 style="margin-bottom: var(--spacing-md); color: var(--gray-900);">活跃时段分布</h4>
                    <div style="display: flex; align-items: end; gap: 2px; height: 120px; padding: var(--spacing-sm) 0;">
                        ${Array.from({length: 24}, (_, i) => {
                            const height = Math.random() * 80 + 20;
                            return `
                                <div style="flex: 1; display: flex; flex-direction: column; align-items: center;">
                                    <div style="width: 100%; height: ${height}%; background: linear-gradient(to top, var(--primary-color), var(--info-color)); border-radius: 2px 2px 0 0; margin-bottom: 4px;"></div>
                                    <span style="font-size: 10px; color: var(--gray-600);">${i}</span>
                                </div>
                            `;
                        }).join('')}
                    </div>
                </div>
            `;
        }

        // 生成消费行为分析HTML
        function generateConsumptionHTML() {
            return `
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(120px, 1fr)); gap: var(--spacing-sm); margin-bottom: var(--spacing-lg);">
                    <div style="text-align: center; padding: var(--spacing-sm); background: var(--gray-50); border-radius: var(--border-radius);">
                        <div style="font-size: 1.2rem; font-weight: 700; color: var(--success-color);">¥156</div>
                        <div style="font-size: 0.8rem; color: var(--gray-600);">平均消费</div>
                    </div>
                    <div style="text-align: center; padding: var(--spacing-sm); background: var(--gray-50); border-radius: var(--border-radius);">
                        <div style="font-size: 1.2rem; font-weight: 700; color: var(--info-color);">3.2次</div>
                        <div style="font-size: 0.8rem; color: var(--gray-600);">月均频次</div>
                    </div>
                    <div style="text-align: center; padding: var(--spacing-sm); background: var(--gray-50); border-radius: var(--border-radius);">
                        <div style="font-size: 1.2rem; font-weight: 700; color: var(--warning-color);">68%</div>
                        <div style="font-size: 0.8rem; color: var(--gray-600);">复购率</div>
                    </div>
                </div>
                <div>
                    <h4 style="margin-bottom: var(--spacing-md); color: var(--gray-900);">消费偏好</h4>
                    <div style="display: flex; flex-wrap: wrap; gap: var(--spacing-xs);">
                        <span style="background: var(--primary-color); color: white; padding: 4px 8px; border-radius: var(--border-radius); font-size: 0.8rem;">陪玩服务</span>
                        <span style="background: var(--info-color); color: white; padding: 4px 8px; border-radius: var(--border-radius); font-size: 0.8rem;">游戏道具</span>
                        <span style="background: var(--success-color); color: white; padding: 4px 8px; border-radius: var(--border-radius); font-size: 0.8rem;">会员服务</span>
                        <span style="background: var(--warning-color); color: white; padding: 4px 8px; border-radius: var(--border-radius); font-size: 0.8rem;">礼品购买</span>
                    </div>
                </div>
            `;
        }

        // 生成地理分布分析HTML
        function generateGeographicHTML() {
            return `
                <div style="margin-bottom: var(--spacing-lg);">
                    <h4 style="margin-bottom: var(--spacing-md); color: var(--gray-900);">主要地区分布</h4>
                    <div style="display: flex; flex-direction: column; gap: var(--spacing-sm);">
                        <div style="display: flex; align-items: center; justify-content: space-between;">
                            <span>北京市</span>
                            <div style="flex: 1; margin: 0 var(--spacing-md); height: 8px; background: var(--gray-200); border-radius: 4px;">
                                <div style="width: 28%; height: 100%; background: var(--primary-color); border-radius: 4px;"></div>
                            </div>
                            <span style="font-weight: 600;">28%</span>
                        </div>
                        <div style="display: flex; align-items: center; justify-content: space-between;">
                            <span>上海市</span>
                            <div style="flex: 1; margin: 0 var(--spacing-md); height: 8px; background: var(--gray-200); border-radius: 4px;">
                                <div style="width: 22%; height: 100%; background: var(--info-color); border-radius: 4px;"></div>
                            </div>
                            <span style="font-weight: 600;">22%</span>
                        </div>
                        <div style="display: flex; align-items: center; justify-content: space-between;">
                            <span>广东省</span>
                            <div style="flex: 1; margin: 0 var(--spacing-md); height: 8px; background: var(--gray-200); border-radius: 4px;">
                                <div style="width: 18%; height: 100%; background: var(--success-color); border-radius: 4px;"></div>
                            </div>
                            <span style="font-weight: 600;">18%</span>
                        </div>
                        <div style="display: flex; align-items: center; justify-content: space-between;">
                            <span>江苏省</span>
                            <div style="flex: 1; margin: 0 var(--spacing-md); height: 8px; background: var(--gray-200); border-radius: 4px;">
                                <div style="width: 12%; height: 100%; background: var(--warning-color); border-radius: 4px;"></div>
                            </div>
                            <span style="font-weight: 600;">12%</span>
                        </div>
                        <div style="display: flex; align-items: center; justify-content: space-between;">
                            <span>其他地区</span>
                            <div style="flex: 1; margin: 0 var(--spacing-md); height: 8px; background: var(--gray-200); border-radius: 4px;">
                                <div style="width: 20%; height: 100%; background: var(--gray-400); border-radius: 4px;"></div>
                            </div>
                            <span style="font-weight: 600;">20%</span>
                        </div>
                    </div>
                </div>
            `;
        }

        // 生成设备使用分析HTML
        function generateDeviceHTML() {
            return `
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(100px, 1fr)); gap: var(--spacing-sm); margin-bottom: var(--spacing-lg);">
                    <div style="text-align: center; padding: var(--spacing-md); background: var(--gray-50); border-radius: var(--border-radius);">
                        <i class="fas fa-mobile-alt" style="font-size: 1.5rem; color: var(--primary-color); margin-bottom: var(--spacing-xs);"></i>
                        <div style="font-size: 1.1rem; font-weight: 700; color: var(--gray-900);">65%</div>
                        <div style="font-size: 0.8rem; color: var(--gray-600);">手机</div>
                    </div>
                    <div style="text-align: center; padding: var(--spacing-md); background: var(--gray-50); border-radius: var(--border-radius);">
                        <i class="fas fa-laptop" style="font-size: 1.5rem; color: var(--info-color); margin-bottom: var(--spacing-xs);"></i>
                        <div style="font-size: 1.1rem; font-weight: 700; color: var(--gray-900);">25%</div>
                        <div style="font-size: 0.8rem; color: var(--gray-600);">电脑</div>
                    </div>
                    <div style="text-align: center; padding: var(--spacing-md); background: var(--gray-50); border-radius: var(--border-radius);">
                        <i class="fas fa-tablet-alt" style="font-size: 1.5rem; color: var(--success-color); margin-bottom: var(--spacing-xs);"></i>
                        <div style="font-size: 1.1rem; font-weight: 700; color: var(--gray-900);">10%</div>
                        <div style="font-size: 0.8rem; color: var(--gray-600);">平板</div>
                    </div>
                </div>
                <div>
                    <h4 style="margin-bottom: var(--spacing-md); color: var(--gray-900);">操作系统分布</h4>
                    <div style="display: flex; flex-direction: column; gap: var(--spacing-sm);">
                        <div style="display: flex; align-items: center; justify-content: space-between;">
                            <span>iOS</span>
                            <div style="flex: 1; margin: 0 var(--spacing-md); height: 8px; background: var(--gray-200); border-radius: 4px;">
                                <div style="width: 42%; height: 100%; background: var(--primary-color); border-radius: 4px;"></div>
                            </div>
                            <span style="font-weight: 600;">42%</span>
                        </div>
                        <div style="display: flex; align-items: center; justify-content: space-between;">
                            <span>Android</span>
                            <div style="flex: 1; margin: 0 var(--spacing-md); height: 8px; background: var(--gray-200); border-radius: 4px;">
                                <div style="width: 38%; height: 100%; background: var(--success-color); border-radius: 4px;"></div>
                            </div>
                            <span style="font-weight: 600;">38%</span>
                        </div>
                        <div style="display: flex; align-items: center; justify-content: space-between;">
                            <span>Windows</span>
                            <div style="flex: 1; margin: 0 var(--spacing-md); height: 8px; background: var(--gray-200); border-radius: 4px;">
                                <div style="width: 15%; height: 100%; background: var(--info-color); border-radius: 4px;"></div>
                            </div>
                            <span style="font-weight: 600;">15%</span>
                        </div>
                        <div style="display: flex; align-items: center; justify-content: space-between;">
                            <span>其他</span>
                            <div style="flex: 1; margin: 0 var(--spacing-md); height: 8px; background: var(--gray-200); border-radius: 4px;">
                                <div style="width: 5%; height: 100%; background: var(--gray-400); border-radius: 4px;"></div>
                            </div>
                            <span style="font-weight: 600;">5%</span>
                        </div>
                    </div>
                </div>
            `;
        }

        // 生成用户价值分析HTML
        function generateValueHTML() {
            return `
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(120px, 1fr)); gap: var(--spacing-sm); margin-bottom: var(--spacing-lg);">
                    <div style="text-align: center; padding: var(--spacing-md); background: linear-gradient(135deg, var(--success-color), var(--success-light)); color: white; border-radius: var(--border-radius);">
                        <div style="font-size: 1.2rem; font-weight: 700;">12%</div>
                        <div style="font-size: 0.8rem; opacity: 0.9;">高价值用户</div>
                    </div>
                    <div style="text-align: center; padding: var(--spacing-md); background: linear-gradient(135deg, var(--warning-color), var(--warning-light)); color: white; border-radius: var(--border-radius);">
                        <div style="font-size: 1.2rem; font-weight: 700;">28%</div>
                        <div style="font-size: 0.8rem; opacity: 0.9;">中价值用户</div>
                    </div>
                    <div style="text-align: center; padding: var(--spacing-md); background: linear-gradient(135deg, var(--gray-400), var(--gray-300)); color: white; border-radius: var(--border-radius);">
                        <div style="font-size: 1.2rem; font-weight: 700;">60%</div>
                        <div style="font-size: 0.8rem; opacity: 0.9;">潜力用户</div>
                    </div>
                </div>
                <div>
                    <h4 style="margin-bottom: var(--spacing-md); color: var(--gray-900);">价值指标</h4>
                    <div style="display: flex; flex-direction: column; gap: var(--spacing-md);">
                        <div style="display: flex; justify-content: space-between; align-items: center; padding: var(--spacing-sm); background: var(--gray-50); border-radius: var(--border-radius);">
                            <span style="color: var(--gray-700);">平均生命周期价值</span>
                            <span style="font-weight: 700; color: var(--success-color);">¥1,280</span>
                        </div>
                        <div style="display: flex; justify-content: space-between; align-items: center; padding: var(--spacing-sm); background: var(--gray-50); border-radius: var(--border-radius);">
                            <span style="color: var(--gray-700);">用户留存率 (30天)</span>
                            <span style="font-weight: 700; color: var(--info-color);">72%</span>
                        </div>
                        <div style="display: flex; justify-content: space-between; align-items: center; padding: var(--spacing-sm); background: var(--gray-50); border-radius: var(--border-radius);">
                            <span style="color: var(--gray-700);">推荐转化率</span>
                            <span style="font-weight: 700; color: var(--warning-color);">15%</span>
                        </div>
                    </div>
                </div>
            `;
        }
    </script>
</body>
</html>
