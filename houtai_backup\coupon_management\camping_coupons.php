<?php
/**
 * 露营页优惠券管理
 * 趣玩星球管理后台
 */

session_start();

// 检查管理员登录状态
if (!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
    header('Location: /houtai_backup/login.php');
    exit;
}

require_once '../db_config.php';

try {
    $pdo = getDbConnection();
} catch (Exception $e) {
    die('数据库连接失败: ' . $e->getMessage());
}

// 处理AJAX请求
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    header('Content-Type: application/json');
    
    switch ($_POST['action']) {
        case 'add_coupon':
            $result = addCoupon($pdo, $_POST);
            echo json_encode($result);
            exit;
            
        case 'edit_coupon':
            $result = editCoupon($pdo, $_POST);
            echo json_encode($result);
            exit;
            
        case 'delete_coupon':
            $result = deleteCoupon($pdo, $_POST['id']);
            echo json_encode($result);
            exit;
            
        case 'toggle_status':
            $result = toggleCouponStatus($pdo, $_POST['id']);
            echo json_encode($result);
            exit;
    }
}

// 获取优惠券列表
function getCoupons($pdo) {
    try {
        $sql = "SELECT * FROM camping_coupons ORDER BY created_at DESC";
        $stmt = $pdo->prepare($sql);
        $stmt->execute();
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    } catch (Exception $e) {
        return [];
    }
}

// 添加优惠券
function addCoupon($pdo, $data) {
    try {
        $sql = "INSERT INTO camping_coupons (title, description, type, discount_amount, min_amount, total_quantity, valid_from, valid_until, status) 
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, 'active')";
        $stmt = $pdo->prepare($sql);
        $stmt->execute([
            $data['title'],
            $data['description'],
            $data['type'],
            $data['discount_amount'],
            $data['min_amount'],
            $data['total_quantity'],
            $data['valid_from'],
            $data['valid_until']
        ]);
        
        return ['success' => true, 'message' => '优惠券添加成功'];
    } catch (Exception $e) {
        return ['success' => false, 'message' => '添加失败: ' . $e->getMessage()];
    }
}

// 编辑优惠券
function editCoupon($pdo, $data) {
    try {
        $sql = "UPDATE camping_coupons SET title=?, description=?, type=?, discount_amount=?, min_amount=?, total_quantity=?, valid_from=?, valid_until=? WHERE id=?";
        $stmt = $pdo->prepare($sql);
        $stmt->execute([
            $data['title'],
            $data['description'],
            $data['type'],
            $data['discount_amount'],
            $data['min_amount'],
            $data['total_quantity'],
            $data['valid_from'],
            $data['valid_until'],
            $data['id']
        ]);
        
        return ['success' => true, 'message' => '优惠券更新成功'];
    } catch (Exception $e) {
        return ['success' => false, 'message' => '更新失败: ' . $e->getMessage()];
    }
}

// 删除优惠券
function deleteCoupon($pdo, $id) {
    try {
        $sql = "DELETE FROM camping_coupons WHERE id = ?";
        $stmt = $pdo->prepare($sql);
        $stmt->execute([$id]);
        
        return ['success' => true, 'message' => '优惠券删除成功'];
    } catch (Exception $e) {
        return ['success' => false, 'message' => '删除失败: ' . $e->getMessage()];
    }
}

// 切换优惠券状态
function toggleCouponStatus($pdo, $id) {
    try {
        $sql = "UPDATE camping_coupons SET status = CASE WHEN status = 'active' THEN 'inactive' ELSE 'active' END WHERE id = ?";
        $stmt = $pdo->prepare($sql);
        $stmt->execute([$id]);
        
        return ['success' => true, 'message' => '状态更新成功'];
    } catch (Exception $e) {
        return ['success' => false, 'message' => '状态更新失败: ' . $e->getMessage()];
    }
}

$coupons = getCoupons($pdo);
?>

<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>露营页优惠券管理 - 趣玩星球管理后台</title>
    <link rel="stylesheet" href="../assets/css/admin-layout.css">
    <link rel="stylesheet" href="../assets/css/modern-admin.css">
    <link rel="stylesheet" href="css/coupon-management.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <?php include '../includes/topbar.php'; ?>
    
    <div class="admin-container">
        <?php include '../includes/sidebar.php'; ?>
        
        <main class="main-content">
            <div class="content-header">
                <div class="header-left">
                    <h1><i class="fas fa-campground"></i> 露营页优惠券管理</h1>
                    <p>管理露营页面显示的优惠券，支持增删改查操作</p>
                </div>
                <div class="header-right">
                    <button class="btn btn-primary" onclick="showAddModal()">
                        <i class="fas fa-plus"></i> 添加优惠券
                    </button>
                </div>
            </div>

            <div class="content-body">
                <div class="card">
                    <div class="card-header">
                        <h3>优惠券列表</h3>
                        <div class="card-tools">
                            <div class="search-box">
                                <input type="text" id="searchInput" placeholder="搜索优惠券..." onkeyup="filterCoupons()">
                                <i class="fas fa-search"></i>
                            </div>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table" id="couponsTable">
                                <thead>
                                    <tr>
                                        <th>ID</th>
                                        <th>标题</th>
                                        <th>类型</th>
                                        <th>优惠金额</th>
                                        <th>最低消费</th>
                                        <th>总数量</th>
                                        <th>已领取</th>
                                        <th>有效期</th>
                                        <th>状态</th>
                                        <th>操作</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($coupons as $coupon): ?>
                                    <tr>
                                        <td><?php echo $coupon['id']; ?></td>
                                        <td><?php echo htmlspecialchars($coupon['title']); ?></td>
                                        <td>
                                            <?php
                                            $types = [
                                                'join_discount' => '参加活动',
                                                'organize_discount' => '组局活动',
                                                'newbie_discount' => '新人专享'
                                            ];
                                            echo $types[$coupon['type']] ?? $coupon['type'];
                                            ?>
                                        </td>
                                        <td>¥<?php echo $coupon['discount_amount']; ?></td>
                                        <td>¥<?php echo $coupon['min_amount']; ?></td>
                                        <td><?php echo $coupon['total_quantity']; ?></td>
                                        <td><?php echo $coupon['claimed_quantity']; ?></td>
                                        <td>
                                            <?php echo date('Y-m-d', strtotime($coupon['valid_from'])); ?> 至<br>
                                            <?php echo date('Y-m-d', strtotime($coupon['valid_until'])); ?>
                                        </td>
                                        <td>
                                            <span class="status-badge status-<?php echo $coupon['status']; ?>">
                                                <?php echo $coupon['status'] === 'active' ? '启用' : '禁用'; ?>
                                            </span>
                                        </td>
                                        <td>
                                            <div class="action-buttons">
                                                <button class="btn btn-sm btn-info" onclick="editCoupon(<?php echo $coupon['id']; ?>)" title="编辑">
                                                    <i class="fas fa-edit"></i>
                                                </button>
                                                <button class="btn btn-sm btn-warning" onclick="toggleStatus(<?php echo $coupon['id']; ?>)" title="切换状态">
                                                    <i class="fas fa-toggle-on"></i>
                                                </button>
                                                <button class="btn btn-sm btn-danger" onclick="deleteCoupon(<?php echo $coupon['id']; ?>)" title="删除">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <!-- 添加/编辑优惠券模态框 -->
    <div class="modal" id="couponModal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 id="modalTitle">添加优惠券</h3>
                <button class="modal-close" onclick="closeModal()">&times;</button>
            </div>
            <div class="modal-body">
                <form id="couponForm">
                    <input type="hidden" id="couponId" name="id">
                    <div class="form-row">
                        <div class="form-group">
                            <label for="title">优惠券标题 *</label>
                            <input type="text" id="title" name="title" required>
                        </div>
                        <div class="form-group">
                            <label for="type">优惠券类型 *</label>
                            <select id="type" name="type" required>
                                <option value="newbie_discount">新人专享</option>
                                <option value="join_discount">参加活动</option>
                                <option value="organize_discount">组局活动</option>
                            </select>
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="description">描述</label>
                        <textarea id="description" name="description" rows="3"></textarea>
                    </div>
                    <div class="form-row">
                        <div class="form-group">
                            <label for="discount_amount">优惠金额 *</label>
                            <input type="number" id="discount_amount" name="discount_amount" step="0.01" required>
                        </div>
                        <div class="form-group">
                            <label for="min_amount">最低消费金额</label>
                            <input type="number" id="min_amount" name="min_amount" step="0.01" value="0">
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="total_quantity">发放总数量 *</label>
                        <input type="number" id="total_quantity" name="total_quantity" required>
                    </div>
                    <div class="form-row">
                        <div class="form-group">
                            <label for="valid_from">有效期开始 *</label>
                            <input type="datetime-local" id="valid_from" name="valid_from" required>
                        </div>
                        <div class="form-group">
                            <label for="valid_until">有效期结束 *</label>
                            <input type="datetime-local" id="valid_until" name="valid_until" required>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button class="btn btn-secondary" onclick="closeModal()">取消</button>
                <button class="btn btn-primary" onclick="saveCoupon()">保存</button>
            </div>
        </div>
    </div>

    <script src="../assets/js/admin-layout.js"></script>
    <script src="js/coupon-management.js"></script>
</body>
</html>
