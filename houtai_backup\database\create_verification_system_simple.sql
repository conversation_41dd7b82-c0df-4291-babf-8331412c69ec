-- 创建验证码系统相关表和字段（简化版本）
-- 用于前后台联动发送验证码功能
-- 兼容MySQL 5.5+版本

-- 1. 创建验证码记录表
DROP TABLE IF EXISTS `verification_codes`;
CREATE TABLE `verification_codes` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `phone` varchar(20) NOT NULL,
  `code` varchar(10) NOT NULL,
  `type` varchar(20) NOT NULL DEFAULT 'admin_send',
  `status` varchar(10) NOT NULL DEFAULT 'pending',
  `sent_by_admin` int(11) DEFAULT NULL,
  `admin_note` varchar(255) DEFAULT NULL,
  `expires_at` datetime NOT NULL,
  `used_at` datetime DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_phone` (`phone`),
  KEY `idx_type_status` (`type`, `status`),
  KEY `idx_expires_at` (`expires_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- 2. 创建实时通知表
DROP TABLE IF EXISTS `realtime_notifications`;
CREATE TABLE `realtime_notifications` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `type` varchar(20) NOT NULL DEFAULT 'verification_code',
  `title` varchar(100) NOT NULL,
  `content` text NOT NULL,
  `data` text DEFAULT NULL,
  `status` varchar(10) NOT NULL DEFAULT 'pending',
  `priority` tinyint(1) NOT NULL DEFAULT 1,
  `expires_at` datetime DEFAULT NULL,
  `delivered_at` datetime DEFAULT NULL,
  `read_at` datetime DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_user_id_status` (`user_id`, `status`),
  KEY `idx_type` (`type`),
  KEY `idx_expires_at` (`expires_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- 3. 创建管理员操作日志表
DROP TABLE IF EXISTS `admin_operation_logs`;
CREATE TABLE `admin_operation_logs` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `admin_id` int(11) NOT NULL,
  `operation_type` varchar(50) NOT NULL,
  `target_type` varchar(50) DEFAULT NULL,
  `target_id` int(11) DEFAULT NULL,
  `description` varchar(255) NOT NULL,
  `details` text DEFAULT NULL,
  `ip_address` varchar(45) DEFAULT NULL,
  `user_agent` text DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_admin_id` (`admin_id`),
  KEY `idx_operation_type` (`operation_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- 4. 为users表添加新字段（如果users表存在）
-- 先检查users表是否存在
SELECT COUNT(*) as users_table_exists FROM information_schema.tables WHERE table_schema = DATABASE() AND table_name = 'users';

-- 添加字段（忽略错误）
ALTER TABLE `users` ADD COLUMN `last_notification_check` datetime DEFAULT NULL;
ALTER TABLE `users` ADD COLUMN `notification_token` varchar(64) DEFAULT NULL;
ALTER TABLE `users` ADD COLUMN `online_status` varchar(10) DEFAULT 'offline';
ALTER TABLE `users` ADD COLUMN `last_activity` datetime DEFAULT NULL;

-- 5. 插入测试数据
INSERT INTO `verification_codes` (`user_id`, `phone`, `code`, `type`, `sent_by_admin`, `admin_note`, `expires_at`) VALUES
(1, '13800138000', '123456', 'admin_send', 1, '测试发送验证码', DATE_ADD(NOW(), INTERVAL 5 MINUTE)),
(2, '13900139000', '654321', 'admin_send', 1, '安全验证', DATE_ADD(NOW(), INTERVAL 5 MINUTE));

-- 6. 创建索引
CREATE INDEX `idx_user_notification_status` ON `realtime_notifications` (`user_id`, `status`, `created_at`);
CREATE INDEX `idx_verification_user_type` ON `verification_codes` (`user_id`, `type`, `status`);

-- 7. 显示创建结果
SELECT 'verification_codes表创建完成！' as message;
SELECT COUNT(*) as verification_records FROM `verification_codes`;

SELECT 'realtime_notifications表创建完成！' as message;
SELECT COUNT(*) as notification_records FROM `realtime_notifications`;

SELECT 'admin_operation_logs表创建完成！' as message;
SELECT COUNT(*) as operation_records FROM `admin_operation_logs`;

SELECT '✅ 验证码系统数据库结构创建完成！' as status;
SELECT '包含验证码记录、实时通知、操作日志等功能' as description;
