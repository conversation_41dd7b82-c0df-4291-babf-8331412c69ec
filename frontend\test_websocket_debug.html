<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WebSocket调试工具</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .section h3 {
            margin: 0 0 15px 0;
            color: #333;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        .output {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 10px;
            margin: 10px 0;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
        }
        .status {
            padding: 5px 10px;
            border-radius: 4px;
            margin: 5px 0;
        }
        .status.success { background: #d4edda; color: #155724; }
        .status.error { background: #f8d7da; color: #721c24; }
        .status.info { background: #d1ecf1; color: #0c5460; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 WebSocket调试工具</h1>
        
        <div class="section">
            <h3>1. 手动轮询测试</h3>
            <label>用户ID: <input type="number" id="userId" value="4"></label>
            <button onclick="testPoll()">测试轮询</button>
            <button onclick="testHeartbeat()">测试心跳</button>
            <div id="pollStatus" class="status info">等待测试</div>
            <div id="pollOutput" class="output"></div>
        </div>
        
        <div class="section">
            <h3>2. 查看数据库状态</h3>
            <button onclick="checkDatabase()">查看通知记录</button>
            <button onclick="resetNotifications()">重置通知状态</button>
            <div id="dbStatus" class="status info">等待查询</div>
            <div id="dbOutput" class="output"></div>
        </div>
        
        <div class="section">
            <h3>3. 模拟WebSocket连接</h3>
            <button onclick="startMockWebSocket()">开始模拟连接</button>
            <button onclick="stopMockWebSocket()">停止连接</button>
            <div id="wsStatus" class="status info">未连接</div>
            <div id="wsOutput" class="output"></div>
        </div>
        
        <div class="section">
            <h3>4. 发送测试验证码</h3>
            <button onclick="sendTestCode()">发送测试验证码</button>
            <div id="sendStatus" class="status info">等待发送</div>
            <div id="sendOutput" class="output"></div>
        </div>
    </div>

    <script>
        let mockInterval = null;
        
        function log(elementId, message) {
            const element = document.getElementById(elementId);
            const time = new Date().toLocaleTimeString();
            element.textContent += `[${time}] ${message}\n`;
            element.scrollTop = element.scrollHeight;
        }
        
        function setStatus(elementId, message, type) {
            const element = document.getElementById(elementId);
            element.textContent = message;
            element.className = `status ${type}`;
        }
        
        async function testPoll() {
            const userId = document.getElementById('userId').value;
            log('pollOutput', `开始轮询用户 ${userId} 的通知...`);
            setStatus('pollStatus', '轮询中...', 'info');
            
            try {
                const response = await fetch(`api/websocket_server.php?action=poll&user_id=${userId}`);
                const data = await response.json();
                
                log('pollOutput', `轮询结果: ${JSON.stringify(data, null, 2)}`);
                
                if (data.success) {
                    setStatus('pollStatus', `找到 ${data.data.count} 条通知`, 'success');
                    
                    if (data.data.notifications && data.data.notifications.length > 0) {
                        data.data.notifications.forEach(notification => {
                            log('pollOutput', `通知: ${notification.title} - ${notification.content}`);
                        });
                    }
                } else {
                    setStatus('pollStatus', '轮询失败', 'error');
                }
            } catch (error) {
                log('pollOutput', `轮询错误: ${error.message}`);
                setStatus('pollStatus', '请求失败', 'error');
            }
        }
        
        async function testHeartbeat() {
            const userId = document.getElementById('userId').value;
            log('pollOutput', `发送心跳...`);
            
            try {
                const response = await fetch(`api/websocket_server.php?action=heartbeat&user_id=${userId}`);
                const data = await response.json();
                
                log('pollOutput', `心跳结果: ${JSON.stringify(data, null, 2)}`);
            } catch (error) {
                log('pollOutput', `心跳错误: ${error.message}`);
            }
        }
        
        async function checkDatabase() {
            log('dbOutput', '查询数据库通知记录...');
            setStatus('dbStatus', '查询中...', 'info');
            
            try {
                const response = await fetch('api/check_notifications_proxy.php?user_id=4');
                const data = await response.json();
                
                log('dbOutput', `数据库查询结果: ${JSON.stringify(data, null, 2)}`);
                
                if (data.success) {
                    setStatus('dbStatus', '查询成功', 'success');
                    
                    if (data.data.realtime_notifications) {
                        log('dbOutput', '=== 通知记录 ===');
                        data.data.realtime_notifications.forEach(notification => {
                            log('dbOutput', `ID:${notification.id} | 状态:${notification.status} | 送达:${notification.delivered_at || '未送达'} | 标题:${notification.title}`);
                        });
                    }
                } else {
                    setStatus('dbStatus', '查询失败', 'error');
                }
            } catch (error) {
                log('dbOutput', `查询错误: ${error.message}`);
                setStatus('dbStatus', '请求失败', 'error');
            }
        }
        
        async function resetNotifications() {
            log('dbOutput', '重置通知状态...');
            
            try {
                // 这里需要创建一个重置API
                const response = await fetch('api/reset_notifications.php?user_id=4', {
                    method: 'POST'
                });
                const data = await response.json();
                
                log('dbOutput', `重置结果: ${JSON.stringify(data, null, 2)}`);
                
                if (data.success) {
                    setStatus('dbStatus', '重置成功', 'success');
                } else {
                    setStatus('dbStatus', '重置失败', 'error');
                }
            } catch (error) {
                log('dbOutput', `重置错误: ${error.message}`);
                setStatus('dbStatus', '重置失败', 'error');
            }
        }
        
        function startMockWebSocket() {
            if (mockInterval) {
                clearInterval(mockInterval);
            }
            
            const userId = document.getElementById('userId').value;
            log('wsOutput', '开始模拟WebSocket连接...');
            setStatus('wsStatus', '连接中...', 'info');
            
            mockInterval = setInterval(async () => {
                try {
                    const response = await fetch(`api/websocket_server.php?action=poll&user_id=${userId}`);
                    const data = await response.json();
                    
                    if (data.success && data.data.count > 0) {
                        log('wsOutput', `收到 ${data.data.count} 条新通知`);
                        data.data.notifications.forEach(notification => {
                            log('wsOutput', `通知: ${notification.title}`);
                            // 这里应该显示弹窗
                            showNotificationModal(notification);
                        });
                    }
                } catch (error) {
                    log('wsOutput', `轮询错误: ${error.message}`);
                }
            }, 2000);
            
            setStatus('wsStatus', '已连接 (2秒轮询)', 'success');
        }
        
        function stopMockWebSocket() {
            if (mockInterval) {
                clearInterval(mockInterval);
                mockInterval = null;
            }
            
            log('wsOutput', 'WebSocket连接已断开');
            setStatus('wsStatus', '已断开', 'info');
        }
        
        async function sendTestCode() {
            log('sendOutput', '发送测试验证码...');
            setStatus('sendStatus', '发送中...', 'info');
            
            const testData = {
                user_id: 4,
                phone: '13800138000',
                type: 'admin_send',
                note: 'WebSocket调试测试',
                expiry: 5
            };
            
            try {
                const response = await fetch('api/send_verification_code_proxy.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(testData)
                });
                
                const data = await response.json();
                
                log('sendOutput', `发送结果: ${JSON.stringify(data, null, 2)}`);
                
                if (data.success) {
                    setStatus('sendStatus', '发送成功', 'success');
                    log('sendOutput', `验证码: ${data.data.code}`);
                } else {
                    setStatus('sendStatus', '发送失败', 'error');
                }
            } catch (error) {
                log('sendOutput', `发送错误: ${error.message}`);
                setStatus('sendStatus', '请求失败', 'error');
            }
        }
        
        function showNotificationModal(notification) {
            alert(`收到通知: ${notification.title}\n内容: ${notification.content}`);
        }
        
        // 页面加载完成
        document.addEventListener('DOMContentLoaded', function() {
            log('pollOutput', 'WebSocket调试工具加载完成');
            log('dbOutput', '可以开始调试了');
            log('wsOutput', '准备模拟WebSocket连接');
            log('sendOutput', '准备发送测试验证码');
        });
    </script>
</body>
</html>
