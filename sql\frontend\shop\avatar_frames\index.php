<?php
session_start();

// 检查用户是否已登录
if (!isset($_SESSION['user_id'])) {
    header('Location: ../../login/index.php');
    exit;
}
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <meta name="theme-color" content="#7B68EE">
    <title>头像框 - 趣玩商城</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'PingFang SC', 'Helvetica Neue', Arial, sans-serif;
        }
        
        body {
            background-color: #f8f9fa;
            color: #333333;
            line-height: 1.6;
            min-height: 100vh;
        }
        
        .header-bar {
            display: flex;
            align-items: center;
            padding: 15px;
            background-color: #7B68EE;
            color: white;
            position: sticky;
            top: 0;
            z-index: 100;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }
        
        .back-button {
            margin-right: 15px;
            font-size: 18px;
            color: white;
            text-decoration: none;
        }
        
        .header-title {
            font-size: 18px;
            font-weight: 600;
            flex-grow: 1;
        }
        
        .search-bar {
            display: flex;
            align-items: center;
            background-color: white;
            border-radius: 20px;
            margin: 15px;
            padding: 10px 15px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
        }
        
        .search-bar i {
            color: #999;
            margin-right: 10px;
        }
        
        .search-bar input {
            border: none;
            outline: none;
            flex-grow: 1;
            font-size: 15px;
        }
        
        .filter-tabs {
            display: flex;
            padding: 0 15px;
            margin-bottom: 15px;
            overflow-x: auto;
            scrollbar-width: none; /* Firefox */
            -ms-overflow-style: none; /* IE and Edge */
        }
        
        .filter-tabs::-webkit-scrollbar {
            display: none; /* Chrome, Safari, Opera */
        }
        
        .filter-tab {
            padding: 8px 15px;
            margin-right: 10px;
            background-color: white;
            border-radius: 20px;
            font-size: 14px;
            color: #666;
            white-space: nowrap;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
        }
        
        .filter-tab.active {
            background-color: #7B68EE;
            color: white;
        }
        
        .avatar-frame-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 15px;
            padding: 0 15px;
            margin-bottom: 20px;
        }
        
        .avatar-frame-card {
            background-color: white;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
            transition: transform 0.3s ease;
        }
        
        .avatar-frame-card:active {
            transform: scale(0.98);
        }
        
        .frame-preview {
            position: relative;
            width: 100%;
            padding-top: 100%; /* 1:1 Aspect Ratio */
            background-color: #f5f5f5;
        }
        
        .frame-preview img {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            object-fit: contain;
        }
        
        .frame-info {
            padding: 12px;
        }
        
        .frame-title {
            font-size: 15px;
            font-weight: 600;
            margin-bottom: 5px;
            color: #333;
        }
        
        .frame-price {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .price {
            color: #FF6B00;
            font-size: 16px;
            font-weight: bold;
        }
        
        .price small {
            font-size: 12px;
            font-weight: normal;
        }
        
        .sold {
            font-size: 12px;
            color: #999;
        }
        
        .frame-tag {
            position: absolute;
            top: 10px;
            right: 10px;
            background-color: #FF6B00;
            color: white;
            font-size: 12px;
            padding: 3px 8px;
            border-radius: 10px;
            z-index: 1;
        }
        
        .empty-notice {
            text-align: center;
            padding: 30px 15px;
            color: #999;
            font-size: 15px;
        }
    </style>
    <!-- 引入字体图标库 -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <!-- 顶部导航栏 -->
    <div class="header-bar">
        <a href="../index.php" class="back-button">
            <i class="fas fa-arrow-left"></i>
        </a>
        <div class="header-title">头像框</div>
    </div>
    
    <!-- 搜索栏 -->
    <div class="search-bar">
        <i class="fas fa-search"></i>
        <input type="text" placeholder="搜索头像框">
    </div>
    
    <!-- 筛选标签 -->
    <div class="filter-tabs">
        <div class="filter-tab active">全部</div>
        <div class="filter-tab">限时</div>
        <div class="filter-tab">会员专属</div>
        <div class="filter-tab">活动奖励</div>
        <div class="filter-tab">新品</div>
    </div>
    
    <!-- 头像框列表 -->
    <div class="avatar-frame-grid">
        <div class="avatar-frame-card">
            <div class="frame-preview">
                <img src="https://s1.imagehub.cc/images/2025/05/16/avatar_frame_1.png" alt="头像框预览">
                <div class="frame-tag">限时</div>
            </div>
            <div class="frame-info">
                <div class="frame-title">星光闪耀头像框</div>
                <div class="frame-price">
                    <div class="price"><small>¥</small>68</div>
                    <div class="sold">已售 128</div>
                </div>
            </div>
        </div>
        
        <div class="avatar-frame-card">
            <div class="frame-preview">
                <img src="https://s1.imagehub.cc/images/2025/05/16/avatar_frame_2.png" alt="头像框预览">
                <div class="frame-tag">新品</div>
            </div>
            <div class="frame-info">
                <div class="frame-title">梦幻彩虹头像框</div>
                <div class="frame-price">
                    <div class="price"><small>¥</small>88</div>
                    <div class="sold">已售 96</div>
                </div>
            </div>
        </div>
        
        <div class="avatar-frame-card">
            <div class="frame-preview">
                <img src="https://s1.imagehub.cc/images/2025/05/16/avatar_frame_3.png" alt="头像框预览">
                <div class="frame-tag">会员专属</div>
            </div>
            <div class="frame-info">
                <div class="frame-title">皇冠尊贵头像框</div>
                <div class="frame-price">
                    <div class="price"><small>¥</small>128</div>
                    <div class="sold">已售 75</div>
                </div>
            </div>
        </div>
        
        <div class="avatar-frame-card">
            <div class="frame-preview">
                <img src="https://s1.imagehub.cc/images/2025/05/16/avatar_frame_4.png" alt="头像框预览">
            </div>
            <div class="frame-info">
                <div class="frame-title">简约几何头像框</div>
                <div class="frame-price">
                    <div class="price"><small>¥</small>48</div>
                    <div class="sold">已售 215</div>
                </div>
            </div>
        </div>
    </div>
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 筛选标签点击效果
            const filterTabs = document.querySelectorAll('.filter-tab');
            filterTabs.forEach(tab => {
                tab.addEventListener('click', function() {
                    // 移除所有标签的active类
                    filterTabs.forEach(t => t.classList.remove('active'));
                    
                    // 添加active类到当前标签
                    this.classList.add('active');
                    
                    // 这里可以添加筛选逻辑
                    const filter = this.textContent.trim();
                    // 示例：如果选择了"会员专属"，只显示会员专属的头像框
                    if (filter !== '全部') {
                        alert(`已选择筛选条件：${filter}\n筛选功能即将上线，敬请期待`);
                    }
                });
            });
            
            // 头像框卡片点击效果
            const frameCards = document.querySelectorAll('.avatar-frame-card');
            frameCards.forEach(card => {
                card.addEventListener('click', function() {
                    const frameTitle = this.querySelector('.frame-title').textContent;
                    alert(`头像框详情页即将上线，敬请期待\n${frameTitle}`);
                });
            });
        });
    </script>
</body>
</html>
