<?php
session_start();
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="theme-color" content="#0a0a2e">
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">
    <meta name="msapplication-navbutton-color" content="#0a0a2e">
    <title>手机号注册 - 趣玩星球</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        /* CSS变量定义 - 主题色系统 */
        :root {
            --primary-color: #40E0D0;
            --primary-light: #AFFBF2;
            --primary-dark: #20B2AA;
            --secondary-color: #06D6A0;
            --accent-color: #FF6B9D;
            --gradient-primary: linear-gradient(135deg, #40E0D0, #06D6A0);
            --gradient-secondary: linear-gradient(135deg, #AFFBF2, #40E0D0);
            --gradient-accent: linear-gradient(135deg, #FF6B9D, #FFB6C1);
            --text-primary: #333333;
            --text-secondary: #666666;
            --text-light: #999999;
            --bg-white: #FFFFFF;
            --bg-light: #F8F9FA;
            --shadow-sm: 0 2px 8px rgba(64, 224, 208, 0.08);
            --shadow-md: 0 4px 16px rgba(64, 224, 208, 0.12);
            --shadow-lg: 0 8px 32px rgba(64, 224, 208, 0.16);
            --radius-sm: 8px;
            --radius-md: 12px;
            --radius-lg: 16px;
            --radius-xl: 24px;
            --transition-fast: 0.2s ease;
            --transition-normal: 0.3s ease;
            --transition-slow: 0.4s ease;
        }

        /* 防止内容被选择 */
        * {
            -webkit-touch-callout: none;
            -webkit-user-select: none;
            -khtml-user-select: none;
            -moz-user-select: none;
            -ms-user-select: none;
            user-select: none;
        }

        html {
            height: 100%;
        }

        body {
            background: linear-gradient(135deg, #0a0a2e 0%, #16213e 25%, #1a1a3a 50%, #0f3460 75%, #0a0a2e 100%);
            position: relative;
            margin: 0;
            padding: 0;
            min-height: 100vh;
            overflow-x: hidden;
            font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;
        }

        /* 星空背景动画 */
        .space-background {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: 1;
            overflow: hidden;
        }

        /* 主星球和轨道系统 */
        .planet-system {
            position: absolute;
            top: 80px;
            left: 50%;
            transform: translateX(-50%);
            width: 300px;
            height: 300px;
        }

        /* 轨道 */
        .orbit {
            position: absolute;
            border: 2px solid rgba(64, 224, 208, 0.2);
            border-radius: 50%;
            animation: orbitRotate 30s linear infinite;
        }

        .orbit-1 {
            width: 160px;
            height: 160px;
            top: 70px;
            left: 70px;
        }

        .orbit-2 {
            width: 200px;
            height: 200px;
            top: 50px;
            left: 50px;
            animation-duration: 45s;
            animation-direction: reverse;
        }

        .orbit-3 {
            width: 250px;
            height: 250px;
            top: 25px;
            left: 25px;
            animation-duration: 60s;
        }

        /* 中心星球 */
        .central-planet {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 60px;
            height: 60px;
            background: radial-gradient(circle at 30% 30%, var(--primary-light), var(--primary-color), var(--primary-dark));
            border-radius: 50%;
            box-shadow: 
                inset -10px -10px 20px rgba(32, 178, 170, 0.4),
                0 0 30px rgba(64, 224, 208, 0.3),
                0 0 60px rgba(64, 224, 208, 0.1);
            animation: planetPulse 4s ease-in-out infinite;
        }

        /* 轨道上的圆角星星 */
        .orbit-star {
            position: absolute;
            width: 6px;
            height: 6px;
            background: #FFD700;
            border-radius: 2px;
            box-shadow: 0 0 8px #FFD700, 0 0 16px #FFD700;
            animation: starTwinkle 2s ease-in-out infinite;
        }

        .orbit-1 .orbit-star {
            top: -3px;
            left: 50%;
            transform: translateX(-50%);
            animation-delay: 0s;
        }

        .orbit-2 .orbit-star {
            top: 50%;
            right: -3px;
            transform: translateY(-50%);
            animation-delay: 1s;
        }

        .orbit-3 .orbit-star {
            bottom: -3px;
            left: 30%;
            transform: translateX(-50%);
            animation-delay: 2s;
        }

        /* 容器 */
        .container {
            position: relative;
            z-index: 10;
            margin: 0;
            padding: 420px 0 60px 0;
            min-height: 100vh;
            width: 100vw;
            display: flex;
            align-items: flex-start;
            justify-content: center;
        }

        /* 注册卡片样式 */
        .register-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 0;
            padding: 40px 20px;
            box-shadow: none;
            border: none;
            width: 100%;
            max-width: none;
            min-height: auto;
        }

        /* Logo样式 */
        .logo {
            text-align: center;
            margin-bottom: 40px;
        }

        .logo h1 {
            background: var(--gradient-primary);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            font-size: 2rem;
            font-weight: 700;
            margin: 0 0 8px 0;
            position: relative;
        }

        .logo h1::before {
            content: '🚀';
            font-size: 1.5rem;
            margin-right: 8px;
            -webkit-text-fill-color: initial;
        }

        .logo p {
            color: var(--text-secondary);
            font-size: 1rem;
            margin: 0;
        }

        /* 步骤指示器 */
        .step-indicator {
            display: flex;
            justify-content: center;
            margin-bottom: 40px;
            gap: 20px;
        }

        .step {
            display: flex;
            align-items: center;
            color: var(--text-light);
            font-size: 0.9rem;
        }

        .step.active {
            color: var(--primary-color);
            font-weight: 600;
        }

        .step-number {
            width: 24px;
            height: 24px;
            border-radius: 50%;
            background: var(--text-light);
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 0.8rem;
            margin-right: 8px;
        }

        .step.active .step-number {
            background: var(--gradient-primary);
        }

        /* 输入框组 */
        .input-group {
            margin-bottom: 32px;
            position: relative;
        }

        .input-group input {
            width: 100%;
            padding: 20px 16px 8px 16px;
            border: 2px solid rgba(64, 224, 208, 0.2);
            border-radius: var(--radius-md);
            font-size: 1.1rem;
            background: var(--bg-white);
            color: var(--text-primary);
            transition: var(--transition-normal);
            box-sizing: border-box;
        }

        .input-group input:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 4px rgba(64, 224, 208, 0.1);
        }

        .input-group input:focus + label,
        .input-group input:not(:placeholder-shown) + label {
            top: 0;
            transform: translateY(-50%);
            font-size: 0.8rem;
            color: var(--primary-color);
            font-weight: 600;
        }

        .input-group label {
            position: absolute;
            left: 16px;
            top: 50%;
            transform: translateY(-50%);
            color: var(--text-secondary);
            font-size: 1rem;
            font-weight: 400;
            pointer-events: none;
            transition: var(--transition-normal);
            background: var(--bg-white);
            padding: 0 4px;
        }

        /* 按钮样式 */
        .btn {
            width: 100%;
            padding: 16px;
            border: none;
            border-radius: var(--radius-md);
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: var(--transition-normal);
            position: relative;
            overflow: hidden;
        }

        .btn-primary {
            background: var(--gradient-primary);
            color: white;
            box-shadow: var(--shadow-md);
        }

        .btn-primary:hover {
            transform: translateY(-3px);
            box-shadow: var(--shadow-lg);
        }

        .btn-primary:active {
            transform: translateY(-1px);
        }

        /* 返回链接 */
        .back-link {
            text-align: center;
            margin-top: 24px;
        }

        .back-link a {
            color: var(--primary-color);
            text-decoration: none;
            font-size: 0.9rem;
            transition: var(--transition-normal);
        }

        .back-link a:hover {
            color: var(--primary-dark);
            text-decoration: underline;
        }

        /* 允许输入框内的文本选择 */
        input[type="tel"] {
            -webkit-user-select: text;
            -khtml-user-select: text;
            -moz-user-select: text;
            -ms-user-select: text;
            user-select: text;
        }

        /* 动画关键帧 */
        @keyframes orbitRotate {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        @keyframes planetPulse {
            0%, 100% { transform: translate(-50%, -50%) scale(1); }
            50% { transform: translate(-50%, -50%) scale(1.05); }
        }

        @keyframes starTwinkle {
            0%, 100% { opacity: 0.3; transform: scale(0.8); }
            50% { opacity: 1; transform: scale(1.2); }
        }

        /* 响应式调整 */
        @media (max-width: 768px) {
            .planet-system {
                width: 250px;
                height: 250px;
                top: 60px;
            }

            .container {
                padding: 340px 0 60px 0;
            }

            .register-card {
                padding: 30px 15px;
            }
        }

        @media (max-width: 480px) {
            .planet-system {
                width: 200px;
                height: 200px;
                top: 40px;
            }

            .container {
                padding: 280px 0 60px 0;
            }

            .register-card {
                padding: 25px 10px;
            }
        }
    </style>
</head>
<body>
    <!-- 星空背景动画 -->
    <div class="space-background">
        <div class="planet-system">
            <!-- 轨道 -->
            <div class="orbit orbit-1">
                <div class="orbit-star"></div>
            </div>
            <div class="orbit orbit-2">
                <div class="orbit-star"></div>
            </div>
            <div class="orbit orbit-3">
                <div class="orbit-star"></div>
            </div>
            
            <!-- 中心星球 -->
            <div class="central-planet"></div>
        </div>
    </div>

    <div class="container">
        <div class="register-card">
            <!-- Logo -->
            <div class="logo">
                <h1>趣玩星球</h1>
                <p>探索有趣的生活</p>
            </div>

            <!-- 步骤指示器 -->
            <div class="step-indicator">
                <div class="step active">
                    <div class="step-number">1</div>
                    <span>手机号</span>
                </div>
                <div class="step">
                    <div class="step-number">2</div>
                    <span>验证码</span>
                </div>
                <div class="step">
                    <div class="step-number">3</div>
                    <span>完成</span>
                </div>
            </div>

            <!-- 表单 -->
            <form id="phone-form">
                <div class="input-group">
                    <input type="tel" name="phone" id="phone" placeholder=" " required>
                    <label for="phone">手机号</label>
                </div>

                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-arrow-right"></i>
                    下一步
                </button>
            </form>

            <!-- 返回链接 -->
            <div class="back-link">
                <a href="../login/index.php">
                    <i class="fas fa-arrow-left"></i>
                    返回登录
                </a>
            </div>
        </div>
    </div>

    <script>
        // 手机号格式验证
        function validatePhoneNumber(phone) {
            const phoneRegex = /^1[3-9]\d{9}$/;
            return phoneRegex.test(phone);
        }

        // 表单提交处理
        document.getElementById('phone-form').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const phoneInput = document.getElementById('phone');
            const phone = phoneInput.value.trim();
            
            if (!phone) {
                alert('请输入手机号');
                phoneInput.focus();
                return;
            }
            
            if (!validatePhoneNumber(phone)) {
                alert('请输入正确的手机号格式');
                phoneInput.focus();
                return;
            }
            
            // 保存手机号到sessionStorage
            sessionStorage.setItem('register_phone', phone);
            
            // 跳转到验证码页面
            window.location.href = 'verify.php';
        });

        // 防止图片被保存或复制
        document.addEventListener('DOMContentLoaded', function() {
            // 禁止右键菜单
            document.addEventListener('contextmenu', function(e) {
                e.preventDefault();
                return false;
            });

            // 禁止拖拽
            document.addEventListener('dragstart', function(e) {
                e.preventDefault();
                return false;
            });

            // 禁止长按选择
            document.addEventListener('selectstart', function(e) {
                if (e.target.tagName === 'INPUT' || e.target.tagName === 'TEXTAREA') {
                    return true;
                }
                e.preventDefault();
                return false;
            });
        });
    </script>
</body>
</html>
