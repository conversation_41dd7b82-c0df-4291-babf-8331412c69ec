<?php
// 标记通知已读API
header('Content-Type: application/json; charset=UTF-8');
ini_set('default_charset', 'UTF-8');

session_start();

// 检查登录状态
if (!isset($_SESSION['cs_logged_in']) || !$_SESSION['cs_logged_in']) {
    http_response_code(401);
    echo json_encode(['error' => '未登录']);
    exit;
}

// 只接受POST请求
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['error' => '方法不允许']);
    exit;
}

// 引用数据库配置文件
require_once '../../db_config.php';

try {
    $pdo = getDbConnection();
    
    // 这里可以实现具体的标记已读逻辑
    // 目前简单返回成功，实际项目中可以添加通知表来记录已读状态
    
    echo json_encode([
        'success' => true,
        'message' => '通知已标记为已读',
        'timestamp' => time()
    ]);
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => '标记通知已读失败',
        'message' => $e->getMessage()
    ]);
}
?>
