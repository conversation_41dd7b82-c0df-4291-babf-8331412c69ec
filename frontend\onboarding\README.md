# 引导页面功能说明

## 🗄️ 数据库设置

### 必需的数据库表

在使用引导页面功能之前，请确保数据库中存在以下表和字段：

#### 1. users表字段检查
请确保 `users` 表包含以下字段：
- `gender` ENUM('male','female','other') - 性别字段
- `birth_date` DATE - 出生日期字段

如果缺少这些字段，请执行以下SQL：

```sql
-- 添加性别字段（如果不存在）
ALTER TABLE users ADD COLUMN gender ENUM('male','female','other') DEFAULT NULL COMMENT '性别' AFTER nickname;

-- 添加出生日期字段（如果不存在）
ALTER TABLE users ADD COLUMN birth_date DATE DEFAULT NULL COMMENT '出生日期' AFTER gender;
```

#### 2. 创建系统消息表
**重要：** 请在数据库中执行以下SQL文件来创建系统消息表：

```sql
-- 执行系统消息表创建脚本
SOURCE /path/to/sql/create_system_messages_table.sql;
```

或者直接执行以下SQL：

```sql
USE quwanplanet;

CREATE TABLE IF NOT EXISTS `system_messages` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL COMMENT '接收用户ID',
  `title` varchar(255) NOT NULL COMMENT '消息标题',
  `content` text NOT NULL COMMENT '消息内容',
  `type` enum('welcome','notice','warning','promotion','system') NOT NULL DEFAULT 'system' COMMENT '消息类型',
  `is_read` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否已读：0-未读，1-已读',
  `read_at` timestamp NULL DEFAULT NULL COMMENT '阅读时间',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_type` (`type`),
  KEY `idx_is_read` (`is_read`),
  KEY `idx_created_at` (`created_at`),
  CONSTRAINT `fk_system_messages_user_id` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='系统消息表';

CREATE INDEX `idx_user_read_status` ON `system_messages` (`user_id`, `is_read`);
CREATE INDEX `idx_user_type` ON `system_messages` (`user_id`, `type`);
```

## 🎨 功能特性

### 1. 星球动画主题
- ✅ 使用主题色 #40E0D0（绿松石色）
- ✅ 星球轨道旋转动画
- ✅ 星星闪烁效果
- ✅ 响应式设计

### 2. 头像上传和审核
- ✅ Cloudinary云存储集成
- ✅ AI自动审核功能
- ✅ 审核动画提示
- ✅ 支持拖拽和点击上传

### 3. 地区定位选择
- ✅ 高德地图API定位
- ✅ 全球城市搜索
- ✅ 热门城市快选
- ✅ 定位动画效果

### 4. 必填字段验证
- ✅ 昵称（必填）
- ✅ 性别（必填）
- ✅ 出生日期（必填，需年满18岁）
- ✅ 地区（必填）
- ✅ 邮箱（必填）
- ✅ 密码（必填，最少6位）

### 5. 系统消息功能
- ✅ 注册成功自动发送欢迎消息
- ✅ 专业化消息内容
- ✅ 消息类型分类
- ✅ 已读/未读状态管理

## 🔧 技术实现

### 前端技术
- HTML5 + CSS3 + JavaScript
- CSS动画和过渡效果
- 响应式布局
- 高德地图API集成

### 后端技术
- PHP 7.4+
- MySQL 5.7+
- Cloudinary云存储
- PDO数据库操作

### 安全特性
- 头像内容审核
- 年龄验证（18岁以上）
- 邮箱格式验证
- 密码强度要求
- SQL注入防护

## 📱 使用流程

1. **手机验证** → 输入手机号并验证验证码
2. **信息填写** → 填写个人基本信息和必填字段
3. **头像上传** → 可选择上传头像（自动审核）
4. **地区选择** → 定位或搜索选择所在城市
5. **注册完成** → 自动发送欢迎系统消息

## ⚠️ 注意事项

1. 确保数据库表结构完整
2. 配置Cloudinary云存储服务
3. 设置高德地图API密钥
4. 检查PHP扩展支持（curl, gd等）
5. 确保服务器支持HTTPS（Cloudinary要求）
