-- 创建消息功能测试数据

-- 插入测试用户（Vancrest技术部成员）
INSERT INTO `users` (`quwanplanet_id`, `username`, `password`, `email`, `phone`, `avatar`, `gender`, `region`, `created_at`) VALUES
('1000001', '张伟', '$2y$10$8MNjTMxMoJQsEHFLzIaKxeOgwzKEAcHYV9JYjYrCaW4pEkOPPY7Hy', '<EMAIL>', '13800000001', 'https://s1.imagehub.cc/images/2025/04/26/f5c2f26c4564aabcbb56c5fd30f1d652.jpg', 'male', '北京', NOW()),
('1000002', '李娜', '$2y$10$8MNjTMxMoJQsEHFLzIaKxeOgwzKEAcHYV9JYjYrCaW4pEkOPPY7Hy', '<EMAIL>', '13800000002', 'https://s1.imagehub.cc/images/2025/04/26/f5c2f26c4564aabcbb56c5fd30f1d652.jpg', 'female', '上海', NOW()),
('1000003', '王强', '$2y$10$8MNjTMxMoJQsEHFLzIaKxeOgwzKEAcHYV9JYjYrCaW4pEkOPPY7Hy', '<EMAIL>', '13800000003', 'https://s1.imagehub.cc/images/2025/04/26/f5c2f26c4564aabcbb56c5fd30f1d652.jpg', 'male', '深圳', NOW()),
('1000004', '刘芳', '$2y$10$8MNjTMxMoJQsEHFLzIaKxeOgwzKEAcHYV9JYjYrCaW4pEkOPPY7Hy', '<EMAIL>', '13800000004', 'https://s1.imagehub.cc/images/2025/04/26/f5c2f26c4564aabcbb56c5fd30f1d652.jpg', 'female', '广州', NOW()),
('1000005', '陈明', '$2y$10$8MNjTMxMoJQsEHFLzIaKxeOgwzKEAcHYV9JYrCaW4pEkOPPY7Hy', '<EMAIL>', '13800000005', 'https://s1.imagehub.cc/images/2025/04/26/f5c2f26c4564aabcbb56c5fd30f1d652.jpg', 'male', '杭州', NOW()),
('1000006', '赵丽', '$2y$10$8MNjTMxMoJQsEHFLzIaKxeOgwzKEAcHYV9JYjYrCaW4pEkOPPY7Hy', '<EMAIL>', '13800000006', 'https://s1.imagehub.cc/images/2025/04/26/f5c2f26c4564aabcbb56c5fd30f1d652.jpg', 'female', '成都', NOW()),
('1000007', '孙涛', '$2y$10$8MNjTMxMoJQsEHFLzIaKxeOgwzKEAcHYV9JYjYrCaW4pEkOPPY7Hy', '<EMAIL>', '13800000007', 'https://s1.imagehub.cc/images/2025/04/26/f5c2f26c4564aabcbb56c5fd30f1d652.jpg', 'male', '武汉', NOW()),
('1000008', '周敏', '$2y$10$8MNjTMxMoJQsEHFLzIaKxeOgwzKEAcHYV9JYjYrCaW4pEkOPPY7Hy', '<EMAIL>', '13800000008', 'https://s1.imagehub.cc/images/2025/04/26/f5c2f26c4564aabcbb56c5fd30f1d652.jpg', 'female', '西安', NOW()),
('1000009', '吴刚', '$2y$10$8MNjTMxMoJQsEHFLzIaKxeOgwzKEAcHYV9JYjYrCaW4pEkOPPY7Hy', '<EMAIL>', '13800000009', 'https://s1.imagehub.cc/images/2025/04/26/f5c2f26c4564aabcbb56c5fd30f1d652.jpg', 'male', '南京', NOW()),
('1000010', '郑雪', '$2y$10$8MNjTMxMoJQsEHFLzIaKxeOgwzKEAcHYV9JYjYrCaW4pEkOPPY7Hy', '<EMAIL>', '13800000010', 'https://s1.imagehub.cc/images/2025/04/26/f5c2f26c4564aabcbb56c5fd30f1d652.jpg', 'female', '青岛', NOW()),
('1000011', '何亮', '$2y$10$8MNjTMxMoJQsEHFLzIaKxeOgwzKEAcHYV9JYjYrCaW4pEkOPPY7Hy', '<EMAIL>', '13800000011', 'https://s1.imagehub.cc/images/2025/04/26/f5c2f26c4564aabcbb56c5fd30f1d652.jpg', 'male', '大连', NOW()),
('1000012', '朱琳', '$2y$10$8MNjTMxMoJQsEHFLzIaKxeOgwzKEAcHYV9JYjYrCaW4pEkOPPY7Hy', '<EMAIL>', '13800000012', 'https://s1.imagehub.cc/images/2025/04/26/f5c2f26c4564aabcbb56c5fd30f1d652.jpg', 'female', '厦门', NOW()),
('1000013', '马超', '$2y$10$8MNjTMxMoJQsEHFLzIaKxeOgwzKEAcHYV9JYjYrCaW4pEkOPPY7Hy', '<EMAIL>', '13800000013', 'https://s1.imagehub.cc/images/2025/04/26/f5c2f26c4564aabcbb56c5fd30f1d652.jpg', 'male', '苏州', NOW());

-- 创建Vancrest技术部群聊
INSERT INTO `groups` (`name`, `description`, `avatar`, `owner_id`, `member_count`, `created_at`) VALUES
('Vancrest技术部', 'Vancrest公司技术部门内部交流群', 'https://s1.imagehub.cc/images/2025/04/26/group_avatar.jpg', 1, 13, NOW());

-- 添加群成员（假设群ID为1，用户ID从1开始）
INSERT INTO `group_members` (`group_id`, `user_id`, `role`, `joined_at`) VALUES
(1, 1, 'owner', NOW()),
(1, 2, 'admin', NOW()),
(1, 3, 'member', NOW()),
(1, 4, 'member', NOW()),
(1, 5, 'member', NOW()),
(1, 6, 'member', NOW()),
(1, 7, 'member', NOW()),
(1, 8, 'member', NOW()),
(1, 9, 'member', NOW()),
(1, 10, 'member', NOW()),
(1, 11, 'member', NOW()),
(1, 12, 'member', NOW()),
(1, 13, 'member', NOW());

-- 创建一些测试消息
INSERT INTO `messages` (`sender_id`, `chat_type`, `chat_id`, `content`, `message_type`, `created_at`) VALUES
(1, 'group', 1, '大家好，欢迎加入Vancrest技术部群聊！', 'text', DATE_SUB(NOW(), INTERVAL 2 DAY)),
(2, 'group', 1, '谢谢群主！很高兴加入团队', 'text', DATE_SUB(NOW(), INTERVAL 2 DAY)),
(3, 'group', 1, '新人报到，请多多指教', 'text', DATE_SUB(NOW(), INTERVAL 1 DAY)),
(4, 'group', 1, '欢迎欢迎！', 'text', DATE_SUB(NOW(), INTERVAL 1 DAY)),
(5, 'group', 1, '今天的项目进度如何？', 'text', DATE_SUB(NOW(), INTERVAL 12 HOUR)),
(6, 'group', 1, '我这边已经完成了前端页面设计', 'text', DATE_SUB(NOW(), INTERVAL 11 HOUR)),
(7, 'group', 1, '后端API也基本完成了', 'text', DATE_SUB(NOW(), INTERVAL 10 HOUR)),
(8, 'group', 1, '测试这边准备开始', 'text', DATE_SUB(NOW(), INTERVAL 9 HOUR)),
(9, 'group', 1, '数据库优化还需要一点时间', 'text', DATE_SUB(NOW(), INTERVAL 8 HOUR)),
(10, 'group', 1, '大家辛苦了！', 'text', DATE_SUB(NOW(), INTERVAL 7 HOUR));
