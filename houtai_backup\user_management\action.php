<?php
session_start();
require_once '../db_config.php';

// 设置JSON响应头
header('Content-Type: application/json; charset=utf-8');

// 检查登录状态
if (!isset($_SESSION['admin_id'])) {
    echo json_encode(['success' => false, 'message' => '未登录']);
    exit;
}

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        $pdo = getDbConnection();

        $user_id = (int)$_POST['user_id'];
        $action = trim($_POST['action']);
        $reason = trim($_POST['reason'] ?? '');
        $duration = $_POST['duration'] ?? '';

        // 验证输入
        if (empty($user_id) || empty($action)) {
            throw new Exception('请填写完整信息');
        }

        // 对于某些操作，reason不是必需的
        if (in_array($action, ['ban_user', 'mute_user', 'ban_ip']) && empty($reason)) {
            throw new Exception('请填写操作原因');
        }

        // 计算过期时间
        $expire_at = null;
        if ($duration && $duration !== 'permanent') {
            // 处理不同的时间单位
            if (is_numeric($duration)) {
                // 数字表示天数
                $expire_at = date('Y-m-d H:i:s', strtotime("+{$duration} days"));
            } else {
                // 处理小时单位（禁言用）
                $duration_map = [
                    '1' => '+1 hour',
                    '6' => '+6 hours',
                    '24' => '+1 day',
                    '72' => '+3 days',
                    '168' => '+7 days',
                    '720' => '+30 days'
                ];

                if (isset($duration_map[$duration])) {
                    $expire_at = date('Y-m-d H:i:s', strtotime($duration_map[$duration]));
                }
            }
        }

        // 获取当前管理员信息
        $admin_id = $_SESSION['admin_id'];
        $admin_name = $_SESSION['admin_name'] ?? '未知管理员';
        $employee_id = $_SESSION['admin_employee_id'] ?? '';
        $department = $_SESSION['admin_department'] ?? '';

        // 获取用户信息
        $stmt = $pdo->prepare("SELECT * FROM users WHERE id = ?");
        $stmt->execute([$user_id]);
        $user = $stmt->fetch(PDO::FETCH_ASSOC);

        if (!$user) {
            throw new Exception('用户不存在');
        }

        $log_action = '';

        // 执行相应操作
        switch ($action) {
            case 'edit_user':
                // 编辑用户信息
                $update_fields = [];
                $update_params = [];

                if (isset($_POST['username']) && !empty(trim($_POST['username']))) {
                    $update_fields[] = "username = ?";
                    $update_params[] = trim($_POST['username']);
                }

                if (isset($_POST['quwan_id']) && !empty(trim($_POST['quwan_id']))) {
                    // 检查字段名是quwan_id还是quwanplanet_id
                    try {
                        $stmt = $pdo->query("SHOW COLUMNS FROM users LIKE 'quwan_id'");
                        if ($stmt->rowCount() > 0) {
                            $update_fields[] = "quwan_id = ?";
                            $update_params[] = trim($_POST['quwan_id']);
                        } else {
                            // 尝试quwanplanet_id字段
                            $stmt = $pdo->query("SHOW COLUMNS FROM users LIKE 'quwanplanet_id'");
                            if ($stmt->rowCount() > 0) {
                                $update_fields[] = "quwanplanet_id = ?";
                                $update_params[] = trim($_POST['quwan_id']);
                            }
                        }
                    } catch (Exception $e) {
                        // 如果检查失败，尝试两个字段名
                        $update_fields[] = "quwan_id = ?";
                        $update_params[] = trim($_POST['quwan_id']);
                    }
                }

                if (isset($_POST['phone'])) {
                    $update_fields[] = "phone = ?";
                    $update_params[] = trim($_POST['phone']) ?: null;
                }

                if (isset($_POST['email'])) {
                    $update_fields[] = "email = ?";
                    $update_params[] = trim($_POST['email']) ?: null;
                }

                if (isset($_POST['gender'])) {
                    $update_fields[] = "gender = ?";
                    $update_params[] = trim($_POST['gender']) ?: null;
                }

                if (isset($_POST['region'])) {
                    $update_fields[] = "region = ?";
                    $update_params[] = trim($_POST['region']) ?: null;
                }

                if (isset($_POST['bio'])) {
                    $update_fields[] = "bio = ?";
                    $update_params[] = trim($_POST['bio']) ?: null;
                }

                if (!empty($update_fields)) {
                    $update_params[] = $user_id;
                    $sql = "UPDATE users SET " . implode(", ", $update_fields) . " WHERE id = ?";
                    $stmt = $pdo->prepare($sql);
                    $result = $stmt->execute($update_params);

                    // 调试信息
                    error_log("SQL: " . $sql);
                    error_log("Params: " . print_r($update_params, true));
                    error_log("Result: " . ($result ? 'true' : 'false'));
                    error_log("Affected rows: " . $stmt->rowCount());

                    $log_action = '编辑用户信息';
                    $reason = '管理员修改了用户信息: ' . implode(', ', $update_fields);
                } else {
                    throw new Exception('没有需要更新的字段');
                }
                break;

            case 'add_log':
                // 添加日志记录
                $log_type = trim($_POST['log_type'] ?? '');
                $log_content = trim($_POST['log_content'] ?? '');
                $log_reason = trim($_POST['log_reason'] ?? '');

                if (empty($log_type) || empty($log_content)) {
                    throw new Exception('请填写完整的日志信息');
                }

                $log_action = $log_type;
                $reason = $log_content . ($log_reason ? ' - ' . $log_reason : '');
                break;

            case 'view_sensitive':
                // 查看敏感信息日志
                $info_type = $_POST['info_type'] ?? '';
                $log_action = '查看敏感信息';
                $reason = "查看了用户的{$info_type}信息";
                break;

            case 'ban_user':
                // 开始事务确保数据一致性
                $pdo->beginTransaction();

                try {
                    // 1. 更新用户状态
                    $stmt = $pdo->prepare("UPDATE users SET status = 'banned' WHERE id = ?");
                    $stmt->execute([$user_id]);

                    // 2. 尝试添加封号记录，使用最简化的方式
                    try {
                        // 检查表是否存在
                        $check_table = $pdo->query("SHOW TABLES LIKE 'user_bans'");
                        if ($check_table->rowCount() == 0) {
                            // 表不存在，创建最简化的表结构
                            $pdo->exec("
                                CREATE TABLE user_bans (
                                    id INT AUTO_INCREMENT PRIMARY KEY,
                                    user_id INT NOT NULL,
                                    reason TEXT NOT NULL,
                                    status VARCHAR(20) DEFAULT 'active',
                                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                                    INDEX idx_user_id (user_id),
                                    INDEX idx_status (status)
                                )
                            ");
                        }

                        // 尝试最简化的插入（只包含必要字段）
                        $stmt = $pdo->prepare("
                            INSERT INTO user_bans (user_id, reason, status, created_at)
                            VALUES (?, ?, 'active', NOW())
                        ");
                        $stmt->execute([$user_id, $reason]);

                    } catch (PDOException $e) {
                        // 如果还是失败，尝试检查现有表结构并适配
                        try {
                            // 获取表结构
                            $columns = $pdo->query("SHOW COLUMNS FROM user_bans")->fetchAll(PDO::FETCH_COLUMN);

                            // 构建动态插入语句
                            $insert_fields = ['user_id', 'reason'];
                            $insert_values = [$user_id, $reason];
                            $placeholders = ['?', '?'];

                            // 检查可选字段
                            if (in_array('admin_id', $columns)) {
                                $insert_fields[] = 'admin_id';
                                $insert_values[] = $admin_id;
                                $placeholders[] = '?';
                            }
                            if (in_array('admin_name', $columns)) {
                                $insert_fields[] = 'admin_name';
                                $insert_values[] = $admin_name;
                                $placeholders[] = '?';
                            }
                            if (in_array('end_time', $columns)) {
                                $insert_fields[] = 'end_time';
                                $insert_values[] = $expire_at;
                                $placeholders[] = '?';
                            }
                            if (in_array('status', $columns)) {
                                $insert_fields[] = 'status';
                                $insert_values[] = 'active';
                                $placeholders[] = '?';
                            }

                            $sql = "INSERT INTO user_bans (" . implode(', ', $insert_fields) . ") VALUES (" . implode(', ', $placeholders) . ")";
                            $stmt = $pdo->prepare($sql);
                            $stmt->execute($insert_values);

                        } catch (PDOException $e2) {
                            // 如果还是失败，只更新用户状态，不记录封号详情
                            error_log("封号记录插入失败，只更新用户状态: " . $e2->getMessage());
                        }
                    }

                    // 提交事务
                    $pdo->commit();

                } catch (Exception $e) {
                    // 回滚事务
                    $pdo->rollback();
                    throw new Exception("封号操作失败: " . $e->getMessage());
                }

                $log_action = '封号';
                break;

            case 'unban_user':
                // 开始事务确保数据一致性
                $pdo->beginTransaction();

                try {
                    // 1. 更新用户状态为active
                    $stmt = $pdo->prepare("UPDATE users SET status = 'active' WHERE id = ?");
                    $stmt->execute([$user_id]);

                    // 2. 处理封号记录 - 使用多种方式确保成功
                    try {
                        // 首先尝试直接删除封号记录（最简单有效）
                        $stmt = $pdo->prepare("DELETE FROM user_bans WHERE user_id = ? AND status = 'active'");
                        $stmt->execute([$user_id]);
                        $affected_bans = $stmt->rowCount();

                    } catch (PDOException $e) {
                        // 如果删除失败，尝试更新状态
                        try {
                            // 检查表中status字段的可能值
                            $enum_query = $pdo->query("SHOW COLUMNS FROM user_bans LIKE 'status'");
                            $enum_info = $enum_query->fetch(PDO::FETCH_ASSOC);

                            // 尝试不同的状态值
                            $possible_statuses = ['inactive', 'expired', 'disabled', 'cancelled'];
                            $success = false;

                            foreach ($possible_statuses as $status_value) {
                                try {
                                    $stmt = $pdo->prepare("UPDATE user_bans SET status = ? WHERE user_id = ? AND status = 'active'");
                                    $stmt->execute([$status_value, $user_id]);
                                    $affected_bans = $stmt->rowCount();
                                    $success = true;
                                    break;
                                } catch (PDOException $e2) {
                                    // 继续尝试下一个状态值
                                    continue;
                                }
                            }

                            if (!$success) {
                                // 如果所有状态值都失败，尝试删除
                                $stmt = $pdo->prepare("DELETE FROM user_bans WHERE user_id = ?");
                                $stmt->execute([$user_id]);
                                $affected_bans = $stmt->rowCount();
                            }

                        } catch (PDOException $e2) {
                            // 如果user_bans表操作都失败，只要用户状态更新成功就算解封成功
                            error_log("封号记录处理失败，但用户状态已更新: " . $e2->getMessage());
                            $affected_bans = 0;
                        }
                    }

                    // 3. 验证结果
                    $verify_stmt = $pdo->prepare("SELECT status FROM users WHERE id = ?");
                    $verify_stmt->execute([$user_id]);
                    $new_status = $verify_stmt->fetchColumn();

                    // 检查是否还有活跃的封号记录
                    try {
                        $verify_ban_stmt = $pdo->prepare("SELECT COUNT(*) FROM user_bans WHERE user_id = ? AND status = 'active'");
                        $verify_ban_stmt->execute([$user_id]);
                        $remaining_bans = $verify_ban_stmt->fetchColumn();
                    } catch (PDOException $e) {
                        // 如果查询失败，假设没有剩余封号
                        $remaining_bans = 0;
                    }

                    // 提交事务
                    $pdo->commit();

                    // 只要用户状态是active就算成功
                    if ($new_status !== 'active') {
                        throw new Exception("用户状态更新失败");
                    }

                } catch (Exception $e) {
                    // 回滚事务
                    $pdo->rollback();
                    throw new Exception("解封操作失败: " . $e->getMessage());
                }

                $log_action = '解封';
                break;

            case 'mute_user':
                // 创建禁言记录表（如果不存在）
                try {
                    $pdo->exec("
                        CREATE TABLE IF NOT EXISTS user_mutes (
                            id INT AUTO_INCREMENT PRIMARY KEY,
                            user_id INT NOT NULL,
                            admin_id INT NOT NULL,
                            admin_name VARCHAR(100) NOT NULL,
                            reason TEXT NOT NULL,
                            start_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                            end_time TIMESTAMP NULL,
                            status ENUM('active', 'expired', 'cancelled') DEFAULT 'active',
                            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                            INDEX idx_user_id (user_id),
                            INDEX idx_status (status)
                        )
                    ");
                } catch (PDOException $e) {
                    // 表可能已存在，继续执行
                }

                // 添加禁言记录
                $stmt = $pdo->prepare("
                    INSERT INTO user_mutes (user_id, admin_id, admin_name, reason, end_time, status)
                    VALUES (?, ?, ?, ?, ?, 'active')
                ");
                $stmt->execute([$user_id, $admin_id, $admin_name, $reason, $expire_at]);
                $log_action = '禁言';
                break;

            case 'unmute_user':
                // 将所有活跃的禁言记录设为取消
                try {
                    $stmt = $pdo->prepare("
                        UPDATE user_mutes SET status = 'cancelled', updated_at = NOW()
                        WHERE user_id = ? AND status = 'active'
                    ");
                    $stmt->execute([$user_id]);
                } catch (PDOException $e) {
                    // user_mutes表不存在，忽略
                    error_log("user_mutes表不存在，跳过禁言记录更新: " . $e->getMessage());
                }

                $log_action = '解禁言';
                break;

            case 'ban_ip':
                // 获取用户最近的IP地址
                $stmt = $pdo->prepare("
                    SELECT ip_address FROM login_logs
                    WHERE user_id = ?
                    ORDER BY login_time DESC
                    LIMIT 1
                ");
                $stmt->execute([$user_id]);
                $latest_ip = $stmt->fetchColumn();

                if ($latest_ip) {
                    // 创建IP封禁表（如果不存在）
                    try {
                        $pdo->exec("
                            CREATE TABLE IF NOT EXISTS ip_bans (
                                id INT AUTO_INCREMENT PRIMARY KEY,
                                ip_address VARCHAR(45) NOT NULL,
                                admin_id INT NOT NULL,
                                admin_name VARCHAR(100) NOT NULL,
                                reason TEXT NOT NULL,
                                status ENUM('active', 'expired', 'cancelled') DEFAULT 'active',
                                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                                INDEX idx_ip_address (ip_address),
                                INDEX idx_status (status)
                            )
                        ");
                    } catch (PDOException $e) {
                        // 表可能已存在，继续执行
                    }

                    // 添加IP封禁记录
                    $stmt = $pdo->prepare("
                        INSERT INTO ip_bans (ip_address, admin_id, admin_name, reason, status, created_at)
                        VALUES (?, ?, ?, ?, 'active', NOW())
                    ");
                    $stmt->execute([$latest_ip, $admin_id, $admin_name, $reason]);
                    $log_action = '封IP';
                } else {
                    throw new Exception('未找到用户IP地址');
                }
                break;

            case 'unban_ip':
                // 获取用户最近的IP地址
                $stmt = $pdo->prepare("
                    SELECT ip_address FROM login_logs
                    WHERE user_id = ?
                    ORDER BY login_time DESC
                    LIMIT 1
                ");
                $stmt->execute([$user_id]);
                $latest_ip = $stmt->fetchColumn();

                if ($latest_ip) {
                    // 将IP封禁记录设为取消
                    try {
                        $stmt = $pdo->prepare("
                            UPDATE ip_bans SET status = 'cancelled', updated_at = NOW()
                            WHERE ip_address = ? AND status = 'active'
                        ");
                        $stmt->execute([$latest_ip]);
                    } catch (PDOException $e) {
                        // ip_bans表不存在，忽略
                        error_log("ip_bans表不存在，跳过IP封禁记录更新: " . $e->getMessage());
                    }
                    $log_action = '解封IP';
                } else {
                    throw new Exception('未找到用户IP地址');
                }
                break;

            case 'check_ban_status':
                $type = $_POST['type'] ?? '';
                $banInfo = null;
                $isBanned = false;

                switch($type) {
                    case 'ban':
                        // 检查用户封号状态
                        try {
                            $stmt = $pdo->prepare("
                                SELECT * FROM user_bans
                                WHERE user_id = ? AND status = 'active'
                                AND (end_time IS NULL OR end_time > NOW())
                                ORDER BY created_at DESC LIMIT 1
                            ");
                            $stmt->execute([$user_id]);
                            $banRecord = $stmt->fetch(PDO::FETCH_ASSOC);

                            if ($banRecord) {
                                $isBanned = true;
                                $endTime = $banRecord['end_time'];
                                $isPermanent = empty($endTime) || $endTime === '0000-00-00 00:00:00';

                                $banInfo = [
                                    'start_time' => $banRecord['created_at'],
                                    'end_time' => $isPermanent ? '永久' : $endTime,
                                    'admin_name' => $banRecord['admin_name'] ?: '系统管理员',
                                    'reason' => $banRecord['reason'] ?: '违反平台规定'
                                ];
                            }
                        } catch (PDOException $e) {
                            // 表不存在，用户未被封号
                        }
                        break;

                    case 'mute':
                        // 检查用户禁言状态
                        try {
                            $stmt = $pdo->prepare("
                                SELECT * FROM user_mutes
                                WHERE user_id = ? AND status = 'active'
                                AND (end_time IS NULL OR end_time > NOW())
                                ORDER BY created_at DESC LIMIT 1
                            ");
                            $stmt->execute([$user_id]);
                            $muteRecord = $stmt->fetch(PDO::FETCH_ASSOC);

                            if ($muteRecord) {
                                $isBanned = true;
                                $banInfo = [
                                    'start_time' => $muteRecord['created_at'],
                                    'end_time' => $muteRecord['end_time'] ?: '永久',
                                    'admin_name' => $muteRecord['admin_name'],
                                    'reason' => $muteRecord['reason']
                                ];
                            }
                        } catch (PDOException $e) {
                            // 表不存在，用户未被禁言
                        }
                        break;

                    case 'ip':
                        // 检查用户IP封禁状态
                        try {
                            // 获取用户最近的IP地址
                            $stmt = $pdo->prepare("
                                SELECT ip_address FROM login_logs
                                WHERE user_id = ?
                                ORDER BY login_time DESC
                                LIMIT 1
                            ");
                            $stmt->execute([$user_id]);
                            $latest_ip = $stmt->fetchColumn();

                            if ($latest_ip) {
                                $stmt = $pdo->prepare("
                                    SELECT * FROM ip_bans
                                    WHERE ip_address = ? AND status = 'active'
                                    ORDER BY created_at DESC LIMIT 1
                                ");
                                $stmt->execute([$latest_ip]);
                                $ipBanRecord = $stmt->fetch(PDO::FETCH_ASSOC);

                                if ($ipBanRecord) {
                                    $isBanned = true;
                                    $banInfo = [
                                        'start_time' => $ipBanRecord['created_at'],
                                        'end_time' => $ipBanRecord['end_time'] ?: '永久',
                                        'admin_name' => $ipBanRecord['admin_name'],
                                        'reason' => $ipBanRecord['reason'],
                                        'ip_address' => $latest_ip
                                    ];
                                }
                            }
                        } catch (PDOException $e) {
                            // 表不存在或其他错误
                        }
                        break;
                }

                echo json_encode([
                    'success' => true,
                    'banned' => $isBanned,
                    'banInfo' => $banInfo
                ]);
                exit;

            default:
                throw new Exception('无效的操作类型');
        }

        // 记录管理日志到admin_operation_logs表
        try {
            $stmt = $pdo->prepare("
                INSERT INTO admin_operation_logs (user_id, admin_id, admin_name, employee_id, department, operation_type, operation_content, operation_reason, ip_address, user_agent, created_at)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW())
            ");
            $stmt->execute([
                $user_id,
                $admin_id,
                $admin_name,
                $employee_id,
                $department,
                $log_action,
                $log_action . '操作成功',
                $reason,
                $_SERVER['REMOTE_ADDR'] ?? '',
                $_SERVER['HTTP_USER_AGENT'] ?? ''
            ]);
        } catch (PDOException $e) {
            // 如果admin_operation_logs表不存在，尝试其他日志表
            try {
                $stmt = $pdo->prepare("
                    INSERT INTO admin_logs (admin_id, admin_name, target_user_id, action, reason, created_at)
                    VALUES (?, ?, ?, ?, ?, NOW())
                ");
                $stmt->execute([$admin_id, $admin_name, $user_id, $log_action, $reason]);
            } catch (PDOException $e2) {
                // 忽略日志记录错误
            }
        }

        // 记录到用户日志
        try {
            $stmt = $pdo->prepare("
                INSERT INTO user_logs (user_id, operator_name, employee_id, department, type, content)
                VALUES (?, ?, ?, ?, ?, ?)
            ");
            $stmt->execute([
                $user_id,
                $admin_name,
                $employee_id,
                $department,
                $log_action,
                $reason
            ]);
        } catch (PDOException $e) {
            // 如果user_logs表不存在，忽略错误
        }

        // 返回JSON响应
        echo json_encode([
            'success' => true,
            'message' => $log_action . '操作成功',
            'action' => $log_action
        ]);
        exit;

    } catch (Exception $e) {
        http_response_code(400);
        echo json_encode([
            'success' => false,
            'message' => $e->getMessage()
        ]);
        exit;
    }
} else {
    header('Location: index.php');
    exit;
}
?>
