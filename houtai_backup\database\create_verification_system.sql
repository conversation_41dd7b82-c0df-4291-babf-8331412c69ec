-- 创建验证码系统相关表和字段
-- 用于前后台联动发送验证码功能

-- 1. 创建验证码记录表
CREATE TABLE IF NOT EXISTS `verification_codes` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `phone` varchar(20) NOT NULL COMMENT '手机号',
  `code` varchar(10) NOT NULL COMMENT '验证码',
  `type` enum('admin_send','login','register','reset_password','security_verify') NOT NULL DEFAULT 'admin_send' COMMENT '验证码类型',
  `status` enum('pending','used','expired') NOT NULL DEFAULT 'pending' COMMENT '状态',
  `sent_by_admin` int(11) DEFAULT NULL COMMENT '发送的管理员ID',
  `admin_note` varchar(255) DEFAULT NULL COMMENT '管理员备注',
  `expires_at` datetime NOT NULL COMMENT '过期时间',
  `used_at` datetime DEFAULT NULL COMMENT '使用时间',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_phone` (`phone`),
  KEY `idx_type_status` (`type`, `status`),
  KEY `idx_expires_at` (`expires_at`),
  KEY `idx_sent_by_admin` (`sent_by_admin`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='验证码记录表';

-- 2. 创建实时通知表
CREATE TABLE IF NOT EXISTS `realtime_notifications` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL COMMENT '目标用户ID',
  `type` enum('verification_code','system_message','admin_notice') NOT NULL DEFAULT 'verification_code' COMMENT '通知类型',
  `title` varchar(100) NOT NULL COMMENT '通知标题',
  `content` text NOT NULL COMMENT '通知内容',
  `data` json DEFAULT NULL COMMENT '附加数据',
  `status` enum('pending','delivered','read') NOT NULL DEFAULT 'pending' COMMENT '状态',
  `priority` tinyint(1) NOT NULL DEFAULT 1 COMMENT '优先级 1-5',
  `expires_at` datetime DEFAULT NULL COMMENT '过期时间',
  `delivered_at` datetime DEFAULT NULL COMMENT '送达时间',
  `read_at` datetime DEFAULT NULL COMMENT '阅读时间',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_user_id_status` (`user_id`, `status`),
  KEY `idx_type` (`type`),
  KEY `idx_expires_at` (`expires_at`),
  KEY `idx_priority` (`priority`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='实时通知表';

-- 3. 为users表添加实时通知相关字段（如果不存在）
ALTER TABLE `users` 
ADD COLUMN IF NOT EXISTS `last_notification_check` datetime DEFAULT NULL COMMENT '最后检查通知时间',
ADD COLUMN IF NOT EXISTS `notification_token` varchar(64) DEFAULT NULL COMMENT '通知令牌',
ADD COLUMN IF NOT EXISTS `online_status` enum('online','offline','away') DEFAULT 'offline' COMMENT '在线状态',
ADD COLUMN IF NOT EXISTS `last_activity` datetime DEFAULT NULL COMMENT '最后活动时间';

-- 4. 创建管理员操作日志表（如果不存在）
CREATE TABLE IF NOT EXISTS `admin_operation_logs` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `admin_id` int(11) NOT NULL COMMENT '管理员ID',
  `operation_type` varchar(50) NOT NULL COMMENT '操作类型',
  `target_type` varchar(50) DEFAULT NULL COMMENT '目标类型',
  `target_id` int(11) DEFAULT NULL COMMENT '目标ID',
  `description` varchar(255) NOT NULL COMMENT '操作描述',
  `details` json DEFAULT NULL COMMENT '操作详情',
  `ip_address` varchar(45) DEFAULT NULL COMMENT 'IP地址',
  `user_agent` text DEFAULT NULL COMMENT '用户代理',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_admin_id` (`admin_id`),
  KEY `idx_operation_type` (`operation_type`),
  KEY `idx_target` (`target_type`, `target_id`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='管理员操作日志表';

-- 5. 插入一些测试数据
INSERT IGNORE INTO `verification_codes` (`user_id`, `phone`, `code`, `type`, `sent_by_admin`, `admin_note`, `expires_at`) VALUES
(1, '13800138000', '123456', 'admin_send', 1, '测试发送验证码', DATE_ADD(NOW(), INTERVAL 5 MINUTE)),
(2, '13900139000', '654321', 'admin_send', 1, '安全验证', DATE_ADD(NOW(), INTERVAL 5 MINUTE));

-- 6. 创建索引优化查询性能
CREATE INDEX IF NOT EXISTS `idx_user_notification_status` ON `realtime_notifications` (`user_id`, `status`, `created_at`);
CREATE INDEX IF NOT EXISTS `idx_verification_user_type` ON `verification_codes` (`user_id`, `type`, `status`);

-- 7. 创建清理过期数据的存储过程
DELIMITER //
CREATE PROCEDURE IF NOT EXISTS `CleanExpiredVerificationCodes`()
BEGIN
    -- 清理过期的验证码
    UPDATE `verification_codes` 
    SET `status` = 'expired' 
    WHERE `status` = 'pending' 
    AND `expires_at` < NOW();
    
    -- 清理过期的通知
    DELETE FROM `realtime_notifications` 
    WHERE `expires_at` IS NOT NULL 
    AND `expires_at` < NOW() 
    AND `status` != 'read';
    
    -- 清理30天前的操作日志
    DELETE FROM `admin_operation_logs` 
    WHERE `created_at` < DATE_SUB(NOW(), INTERVAL 30 DAY);
END //
DELIMITER ;

-- 8. 创建触发器自动更新用户活动时间
DELIMITER //
CREATE TRIGGER IF NOT EXISTS `update_user_activity` 
AFTER INSERT ON `login_logs` 
FOR EACH ROW 
BEGIN
    IF NEW.status = 'success' AND NEW.user_id > 0 THEN
        UPDATE `users` 
        SET `last_activity` = NEW.login_time,
            `online_status` = 'online'
        WHERE `id` = NEW.user_id;
    END IF;
END //
DELIMITER ;

-- 9. 显示创建结果
SELECT 'verification_codes表创建完成！' as message;
SELECT COUNT(*) as verification_records FROM `verification_codes`;

SELECT 'realtime_notifications表创建完成！' as message;
SELECT COUNT(*) as notification_records FROM `realtime_notifications`;

SELECT 'admin_operation_logs表创建完成！' as message;
SELECT COUNT(*) as operation_records FROM `admin_operation_logs`;

-- 10. 检查users表新增字段
DESCRIBE `users`;

-- 显示完成信息
SELECT '✅ 验证码系统数据库结构创建完成！' as status,
       '包含验证码记录、实时通知、操作日志等功能' as description;
