<?php
/**
 * 搜索用户API
 * 用于后台发送验证码时搜索用户
 */

session_start();

// 检查管理员权限
if (!isset($_SESSION['admin_id'])) {
    header('Content-Type: application/json; charset=utf-8');
    echo json_encode(['success' => false, 'message' => '未授权访问']);
    exit;
}

header('Content-Type: application/json; charset=utf-8');

try {
    $pdo = new PDO(
        "mysql:host=localhost;dbname=quwanplanet;charset=utf8mb4",
        "quwanplanet",
        "nJmJm23FB4Xn6Fc3",
        [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC
        ]
    );
    
    $input = json_decode(file_get_contents('php://input'), true);
    $search = trim($input['search'] ?? '');
    
    if (empty($search)) {
        echo json_encode(['success' => false, 'message' => '请输入搜索关键词']);
        exit;
    }
    
    // 搜索用户
    $stmt = $pdo->prepare("
        SELECT id, username, phone, email, created_at, last_activity, online_status
        FROM users 
        WHERE username LIKE ? OR phone LIKE ? OR email LIKE ? OR id = ?
        ORDER BY 
            CASE 
                WHEN id = ? THEN 1
                WHEN username = ? THEN 2
                WHEN phone = ? THEN 3
                WHEN email = ? THEN 4
                ELSE 5
            END,
            last_activity DESC
        LIMIT 20
    ");
    
    $searchTerm = "%{$search}%";
    $exactSearch = $search;
    $idSearch = is_numeric($search) ? intval($search) : 0;
    
    $stmt->execute([
        $searchTerm, $searchTerm, $searchTerm, $idSearch,
        $idSearch, $exactSearch, $exactSearch, $exactSearch
    ]);
    
    $users = $stmt->fetchAll();
    
    // 格式化用户数据
    foreach ($users as &$user) {
        $user['created_at'] = date('Y-m-d', strtotime($user['created_at']));
        $user['last_activity'] = $user['last_activity'] ? date('Y-m-d H:i', strtotime($user['last_activity'])) : '从未活动';
        $user['online_status'] = $user['online_status'] ?? 'offline';
    }
    
    echo json_encode([
        'success' => true,
        'users' => $users,
        'count' => count($users)
    ]);
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => '搜索失败：' . $e->getMessage()
    ]);
}
?>
