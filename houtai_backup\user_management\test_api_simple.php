<?php
/**
 * 简单的API测试文件
 * 用于验证路径和功能是否正常
 */

// 禁用错误输出
error_reporting(0);
ini_set('display_errors', 0);

header('Content-Type: application/json; charset=utf-8');

$response = [
    'success' => true,
    'message' => 'API测试成功',
    'timestamp' => date('Y-m-d H:i:s'),
    'server_info' => [
        'php_version' => PHP_VERSION,
        'server_software' => $_SERVER['SERVER_SOFTWARE'] ?? 'Unknown',
        'document_root' => $_SERVER['DOCUMENT_ROOT'] ?? 'Unknown',
        'script_name' => $_SERVER['SCRIPT_NAME'] ?? 'Unknown',
        'request_uri' => $_SERVER['REQUEST_URI'] ?? 'Unknown'
    ]
];

// 测试数据库连接
try {
    $pdo = new PDO(
        "mysql:host=localhost;dbname=quwanplanet;charset=utf8mb4",
        "quwanplanet",
        "nJmJm23FB4Xn6Fc3",
        [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC
        ]
    );
    
    $response['database'] = [
        'status' => 'connected',
        'message' => '数据库连接成功'
    ];
    
    // 检查表是否存在
    $tables = ['verification_codes', 'realtime_notifications', 'users'];
    foreach ($tables as $table) {
        $stmt = $pdo->query("SHOW TABLES LIKE '{$table}'");
        $exists = $stmt->rowCount() > 0;
        $response['database']['tables'][$table] = $exists ? '存在' : '不存在';
    }
    
    // 查询一些基本信息
    if (isset($response['database']['tables']['users']) && $response['database']['tables']['users'] === '存在') {
        $stmt = $pdo->query("SELECT COUNT(*) as count FROM users");
        $userCount = $stmt->fetch()['count'];
        $response['database']['user_count'] = $userCount;
    }
    
} catch (Exception $e) {
    $response['database'] = [
        'status' => 'error',
        'message' => '数据库连接失败: ' . $e->getMessage()
    ];
}

// 测试会话
session_start();
$response['session'] = [
    'session_id' => session_id(),
    'admin_logged_in' => isset($_SESSION['admin_id']),
    'admin_id' => $_SESSION['admin_id'] ?? null,
    'admin_username' => $_SESSION['admin_username'] ?? null
];

echo json_encode($response, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);
?>
