<?php
/**
 * Cloudinary上传助手类
 * 用于将媒体文件上传到Cloudinary并返回URL
 */
class CloudinaryHelper {
    // Cloudinary配置
    private $cloud_name = 'dwcauq0wy';
    private $api_key = '965165511998959';
    private $api_secret = 'JYnkxTIAAC3GLuf3u6iiQpfqfMA';

    /**
     * 构造函数
     */
    public function __construct() {
        // 可以在这里添加初始化代码
    }

    /**
     * 上传图片到Cloudinary
     *
     * @param string $file_path 本地文件路径
     * @param array $options 上传选项
     * @return array 包含上传结果的数组
     */
    public function uploadImage($file_path, $options = []) {
        return $this->uploadMedia($file_path, 'image', $options);
    }

    /**
     * 上传视频到Cloudinary
     *
     * @param string $file_path 本地文件路径
     * @param array $options 上传选项
     * @return array 包含上传结果的数组
     */
    public function uploadVideo($file_path, $options = []) {
        return $this->uploadMedia($file_path, 'video', $options);
    }

    /**
     * 上传音频到Cloudinary
     *
     * @param string $file_path 本地文件路径
     * @param array $options 上传选项
     * @return array 包含上传结果的数组
     */
    public function uploadAudio($file_path, $options = []) {
        return $this->uploadMedia($file_path, 'raw', $options);
    }

    /**
     * 上传媒体文件到Cloudinary
     *
     * @param string $file_path 本地文件路径
     * @param string $resource_type 资源类型 (image, video, raw)
     * @param array $options 上传选项
     * @return array 包含上传结果的数组
     */
    private function uploadMedia($file_path, $resource_type = 'image', $options = []) {
        // 构建上传URL
        $url = "https://api.cloudinary.com/v1_1/{$this->cloud_name}/{$resource_type}/upload";

        // 准备签名
        $timestamp = time();
        $signature_data = [];

        // 添加选项到签名数据
        foreach ($options as $key => $value) {
            $signature_data[$key] = $value;
        }

        $signature_data['timestamp'] = $timestamp;

        // 按字母顺序排序参数
        ksort($signature_data);

        // 构建签名字符串
        $signature_string = '';
        foreach ($signature_data as $key => $value) {
            $signature_string .= $key . '=' . $value . '&';
        }
        $signature_string = rtrim($signature_string, '&');

        // 添加API密钥
        $signature_string .= $this->api_secret;

        // 计算签名
        $signature = hash('sha1', $signature_string);

        // 准备表单数据
        $post_data = [
            'file' => new CURLFile($file_path),
            'api_key' => $this->api_key,
            'timestamp' => $timestamp,
            'signature' => $signature
        ];

        // 添加选项到表单数据
        foreach ($options as $key => $value) {
            $post_data[$key] = $value;
        }

        // 初始化cURL
        $curl = curl_init();

        // 设置cURL选项
        curl_setopt_array($curl, [
            CURLOPT_URL => $url,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_POST => true,
            CURLOPT_POSTFIELDS => $post_data
        ]);

        // 执行请求
        $response = curl_exec($curl);

        // 检查错误
        if (curl_errno($curl)) {
            $error = curl_error($curl);
            curl_close($curl);
            return [
                'success' => false,
                'message' => $error
            ];
        }

        // 关闭cURL
        curl_close($curl);

        // 解析响应
        $result = json_decode($response, true);

        // 返回结果
        if (isset($result['secure_url'])) {
            return [
                'success' => true,
                'data' => $result
            ];
        } else {
            return [
                'success' => false,
                'message' => isset($result['error']['message']) ? $result['error']['message'] : '上传失败',
                'data' => $result
            ];
        }
    }

    /**
     * 从Cloudinary URL中提取公共ID
     *
     * @param string $url Cloudinary URL
     * @return string 公共ID
     */
    public function extractPublicIdFromUrl($url) {
        // 示例URL: https://res.cloudinary.com/dwcauq0wy/image/upload/v1234567890/avatars/abcdef123456
        $pattern = '/\/v\d+\/(.+?)$/';
        if (preg_match($pattern, $url, $matches)) {
            return $matches[1];
        }
        return '';
    }
}

// 保留原来的函数，以便向后兼容
function uploadToCloudinary($file_path, $public_id = null, $folder = 'avatars') {
    $cloudinary = new CloudinaryHelper();
    $options = [];

    if ($public_id) {
        $options['public_id'] = $public_id;
    }

    if ($folder) {
        $options['folder'] = $folder;
    }

    $result = $cloudinary->uploadImage($file_path, $options);

    if ($result['success']) {
        return $result['data'];
    } else {
        return ['error' => true, 'message' => $result['message']];
    }
}

function extractPublicIdFromUrl($url) {
    $cloudinary = new CloudinaryHelper();
    return $cloudinary->extractPublicIdFromUrl($url);
}
