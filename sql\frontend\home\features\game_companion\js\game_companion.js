/**
 * 游戏玩伴页面交互脚本
 * 实现了现代化、年轻化的UI交互效果
 */

document.addEventListener('DOMContentLoaded', function() {
    console.log("游戏玩伴页面脚本已加载");

    // 初始化所有交互功能
    initializeViewToggle();
    initializeQuickFilters();
    initializeAdvancedFilters();
    initializePriceSlider();
    initializeCardInteractions();
    initializeLoadMore();
    initializeFloatingChat();
    initializeAnimations();

    // 如果在主页中加载，则处理内容区域
    const gameCompanionContentArea = document.getElementById('game-companion-content-area');
    if (gameCompanionContentArea && gameCompanionContentArea.innerHTML.trim() === '') {
        // 如果内容区域为空，则加载游戏玩伴页面
        loadGameCompanionPage(gameCompanionContentArea);
    }
});

/**
 * 加载游戏玩伴页面内容
 * @param {HTMLElement} container - 内容容器元素
 */
function loadGameCompanionPage(container) {
    // 这里可以通过AJAX加载页面内容，或者直接插入HTML
    // 为了简化，这里直接使用fetch加载HTML
    fetch('features/game_companion/game_companion_page.html')
        .then(response => response.text())
        .then(html => {
            container.innerHTML = html;

            // 页面加载后重新初始化所有交互
            initializeViewToggle();
            initializeQuickFilters();
            initializeAdvancedFilters();
            initializePriceSlider();
            initializeCardInteractions();
            initializeLoadMore();
            initializeFloatingChat();
            initializeAnimations();
        })
        .catch(error => {
            console.error('加载游戏玩伴页面失败:', error);
            container.innerHTML = '<p class="error-message">内容加载失败，请稍后重试。</p>';
        });
}

/**
 * 初始化视图切换功能（网格/列表视图）
 */
function initializeViewToggle() {
    const viewOptions = document.querySelectorAll('.view-option');
    const companionList = document.querySelector('.companion-list');

    if (!viewOptions.length || !companionList) return;

    viewOptions.forEach(option => {
        option.addEventListener('click', function() {
            // 移除所有选项的active类
            viewOptions.forEach(opt => opt.classList.remove('active'));

            // 为当前选项添加active类
            this.classList.add('active');

            // 获取视图类型
            const viewType = this.getAttribute('data-view');

            // 移除所有视图类
            companionList.classList.remove('grid-view', 'list-view');

            // 添加当前视图类
            companionList.classList.add(`${viewType}-view`);

            // 添加过渡动画
            companionList.style.opacity = '0';
            setTimeout(() => {
                companionList.style.opacity = '1';
            }, 50);
        });
    });
}

/**
 * 初始化快速筛选标签
 */
function initializeQuickFilters() {
    const quickFilterBtns = document.querySelectorAll('.quick-filter-btn');

    if (!quickFilterBtns.length) return;

    quickFilterBtns.forEach(btn => {
        btn.addEventListener('click', function() {
            // 移除所有按钮的active类
            quickFilterBtns.forEach(b => b.classList.remove('active'));

            // 为当前按钮添加active类
            this.classList.add('active');

            // 这里可以添加实际的筛选逻辑
            const filterValue = this.textContent.trim();
            console.log(`应用快速筛选: ${filterValue}`);

            // 模拟筛选效果
            simulateFiltering();
        });
    });
}

/**
 * 初始化高级筛选面板
 */
function initializeAdvancedFilters() {
    const advancedFilterBtn = document.querySelector('.advanced-filter-btn');
    const advancedFilters = document.querySelector('.advanced-filters');
    const closeFiltersBtn = document.querySelector('.close-filters-btn');
    const applyFiltersBtn = document.querySelector('#apply-filters-btn');
    const resetFiltersBtn = document.querySelector('#reset-filters-btn');

    if (!advancedFilterBtn || !advancedFilters) return;

    // 显示高级筛选面板
    advancedFilterBtn.addEventListener('click', function() {
        advancedFilters.style.display = 'block';

        // 添加淡入效果
        advancedFilters.style.opacity = '0';
        setTimeout(() => {
            advancedFilters.style.opacity = '1';
        }, 10);
    });

    // 关闭高级筛选面板
    if (closeFiltersBtn) {
        closeFiltersBtn.addEventListener('click', function() {
            // 添加淡出效果
            advancedFilters.style.opacity = '0';
            setTimeout(() => {
                advancedFilters.style.display = 'none';
            }, 300);
        });
    }

    // 应用筛选
    if (applyFiltersBtn) {
        applyFiltersBtn.addEventListener('click', function() {
            // 收集所有筛选条件
            const gameSelect = document.querySelector('#game-select').value;
            const skillLevel = document.querySelector('#skill-level').value;
            const genderSelect = document.querySelector('#gender-select').value;
            const onlineStatus = document.querySelector('#online-status').value;
            const voiceChat = document.querySelector('#voice-chat').value;
            const priceRange = document.querySelector('#price-range').value;

            console.log('应用高级筛选:', {
                game: gameSelect,
                skill: skillLevel,
                gender: genderSelect,
                online: onlineStatus,
                voice: voiceChat,
                price: priceRange
            });

            // 关闭筛选面板
            advancedFilters.style.opacity = '0';
            setTimeout(() => {
                advancedFilters.style.display = 'none';
            }, 300);

            // 模拟筛选效果
            simulateFiltering();
        });
    }

    // 重置筛选
    if (resetFiltersBtn) {
        resetFiltersBtn.addEventListener('click', function() {
            // 重置所有选择框
            const selects = advancedFilters.querySelectorAll('select');
            selects.forEach(select => {
                select.selectedIndex = 0;
            });

            // 重置价格滑块
            const priceRange = document.querySelector('#price-range');
            if (priceRange) {
                priceRange.value = 100;
                updatePriceValue(100);
            }

            console.log('已重置所有筛选条件');
        });
    }
}

/**
 * 初始化价格滑块
 */
function initializePriceSlider() {
    const priceRange = document.querySelector('#price-range');
    const priceValue = document.querySelector('#price-value');

    if (!priceRange || !priceValue) return;

    // 初始化价格显示
    updatePriceValue(priceRange.value);

    // 监听滑块变化
    priceRange.addEventListener('input', function() {
        updatePriceValue(this.value);
    });
}

/**
 * 更新价格显示
 * @param {string} value - 价格值
 */
function updatePriceValue(value) {
    const priceValue = document.querySelector('#price-value');
    if (priceValue) {
        priceValue.textContent = `${value}元`;
    }
}

/**
 * 初始化卡片交互效果
 */
function initializeCardInteractions() {
    // 玩伴卡片
    const companionCards = document.querySelectorAll('.companion-card');

    companionCards.forEach(card => {
        // 鼠标悬停效果已在CSS中实现

        // 添加点击事件
        card.addEventListener('click', function(e) {
            // 如果点击的是按钮，则不触发卡片点击事件
            if (e.target.tagName === 'BUTTON' || e.target.closest('button')) {
                return;
            }

            // 卡片点击效果 - 可以显示详情弹窗
            console.log('查看玩伴详情:', card.querySelector('h4').textContent);
            // 这里可以添加显示详情弹窗的代码
        });
    });

    // 聊天按钮
    const chatBtns = document.querySelectorAll('.chat-btn');
    chatBtns.forEach(btn => {
        btn.addEventListener('click', function() {
            const cardName = this.closest('.companion-card').querySelector('h4').textContent;
            console.log(`与 ${cardName} 开始聊天`);
            // 这里可以添加打开聊天窗口的代码
        });
    });

    // 邀请按钮
    const inviteBtns = document.querySelectorAll('.invite-btn:not([disabled])');
    inviteBtns.forEach(btn => {
        btn.addEventListener('click', function() {
            const cardName = this.closest('.companion-card').querySelector('h4').textContent;
            console.log(`邀请 ${cardName} 一起游戏`);

            // 添加点击反馈
            this.classList.add('clicked');
            setTimeout(() => {
                this.classList.remove('clicked');
            }, 300);

            // 这里可以添加邀请逻辑
        });
    });

    // 游戏分类卡片
    const gameCards = document.querySelectorAll('.game-category-card');
    gameCards.forEach(card => {
        const btn = card.querySelector('.find-companions-btn');
        if (btn && !btn.hasAttribute('disabled')) {
            btn.addEventListener('click', function() {
                const gameName = card.querySelector('h4').textContent;
                console.log(`查找 ${gameName} 的玩伴`);
                // 这里可以添加跳转或筛选逻辑
            });
        }
    });

    // 组合预约按钮
    const comboBtns = document.querySelectorAll('.book-combo-btn');
    comboBtns.forEach(btn => {
        btn.addEventListener('click', function() {
            const comboName = this.closest('.combo-card').querySelector('h3').textContent;
            console.log(`预约组合: ${comboName}`);
            // 这里可以添加预约逻辑
        });
    });
}

/**
 * 初始化加载更多功能
 */
function initializeLoadMore() {
    const loadMoreBtn = document.querySelector('.load-more-btn');

    if (!loadMoreBtn) return;

    loadMoreBtn.addEventListener('click', function() {
        // 显示加载中状态
        this.innerHTML = '加载中... <i class="fas fa-spinner fa-spin"></i>';

        // 模拟加载延迟
        setTimeout(() => {
            // 这里可以添加实际的加载逻辑
            console.log('加载更多玩伴');

            // 恢复按钮状态
            this.innerHTML = '加载更多 <i class="fas fa-sync"></i>';

            // 模拟没有更多数据
            this.disabled = true;
            this.innerHTML = '没有更多了';
            this.style.opacity = '0.7';
        }, 1500);
    });
}

/**
 * 初始化浮动聊天按钮
 */
function initializeFloatingChat() {
    const chatBtn = document.querySelector('.floating-chat-btn');

    if (!chatBtn) return;

    chatBtn.addEventListener('click', function() {
        console.log('打开聊天窗口');
        // 这里可以添加打开聊天窗口的代码

        // 清除通知
        const badge = this.querySelector('.notification-badge');
        if (badge) {
            badge.style.display = 'none';
        }
    });
}

/**
 * 初始化页面动画效果
 */
function initializeAnimations() {
    // 添加页面元素的进入动画
    const sections = document.querySelectorAll('section');

    sections.forEach((section, index) => {
        // 设置初始状态
        section.style.opacity = '0';
        section.style.transform = 'translateY(20px)';
        section.style.transition = 'opacity 0.5s ease, transform 0.5s ease';

        // 延迟显示各个部分
        setTimeout(() => {
            section.style.opacity = '1';
            section.style.transform = 'translateY(0)';
        }, 100 * (index + 1));
    });
}

/**
 * 模拟筛选效果
 */
function simulateFiltering() {
    const companionList = document.querySelector('.companion-list');

    if (!companionList) return;

    // 添加加载效果
    companionList.style.opacity = '0.5';

    // 模拟加载延迟
    setTimeout(() => {
        // 恢复显示
        companionList.style.opacity = '1';

        // 这里可以添加实际的筛选逻辑
        console.log('筛选完成');
    }, 800);
}