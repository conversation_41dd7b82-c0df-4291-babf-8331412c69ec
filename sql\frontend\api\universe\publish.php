<?php
// 设置响应头
header('Content-Type: application/json');

// 启动会话
session_start();

// 检查用户是否已登录
if (!isset($_SESSION['user_id'])) {
    echo json_encode([
        'success' => false,
        'message' => '请先登录'
    ]);
    exit;
}

// 获取用户ID
$user_id = $_SESSION['user_id'];

// 检查必填字段
$required_fields = ['title', 'category_id', 'subcategory_id', 'content'];
foreach ($required_fields as $field) {
    if (!isset($_POST[$field]) || empty($_POST[$field])) {
        echo json_encode([
            'success' => false,
            'message' => '请填写所有必填字段'
        ]);
        exit;
    }
}

// 获取表单数据
$title = trim($_POST['title']);
$category_id = intval($_POST['category_id']);
$subcategory_id = intval($_POST['subcategory_id']);
$content = $_POST['content'];
$cover_image_url = isset($_POST['cover_image_url']) ? $_POST['cover_image_url'] : null;

// 检查内容中是否包含图片、视频或音频
$has_images = preg_match('/<img[^>]+>/i', $content) ? 1 : 0;
$has_video = preg_match('/\[视频\]/i', $content) ? 1 : 0;
$has_audio = preg_match('/\[音频\]/i', $content) ? 1 : 0;

// 引入数据库配置
require_once '../../../includes/db_config.php';

try {
    // 连接数据库
    $dsn = "mysql:host={$db_config['host']};dbname={$db_config['dbname']};charset={$db_config['charset']};port={$db_config['port']}";
    $pdo = new PDO($dsn, $db_config['username'], $db_config['password']);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

    // 开始事务
    $pdo->beginTransaction();

    // 插入内容
    $stmt = $pdo->prepare("
        INSERT INTO universe_posts (
            user_id, title, content, category_id, subcategory_id,
            cover_image, has_images, has_video, has_audio, status
        ) VALUES (
            :user_id, :title, :content, :category_id, :subcategory_id,
            :cover_image, :has_images, :has_video, :has_audio, 1
        )
    ");

    $stmt->execute([
        'user_id' => $user_id,
        'title' => $title,
        'content' => $content,
        'category_id' => $category_id,
        'subcategory_id' => $subcategory_id,
        'cover_image' => $cover_image_url,
        'has_images' => $has_images,
        'has_video' => $has_video,
        'has_audio' => $has_audio
    ]);

    // 获取新插入的内容ID
    $post_id = $pdo->lastInsertId();

    // 提交事务
    $pdo->commit();

    // 返回成功信息
    echo json_encode([
        'success' => true,
        'message' => '发布成功',
        'post_id' => $post_id
    ]);
} catch (PDOException $e) {
    // 回滚事务
    if ($pdo->inTransaction()) {
        $pdo->rollBack();
    }

    // 记录错误
    error_log("发布内容错误: " . $e->getMessage());

    // 返回错误信息
    echo json_encode([
        'success' => false,
        'message' => '发布失败，请稍后再试'
    ]);
}
