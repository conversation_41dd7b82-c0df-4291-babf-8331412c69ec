<?php
// 基于现有实时通知系统的客服聊天测试
header('Content-Type: text/html; charset=UTF-8');
ini_set('default_charset', 'UTF-8');

session_start();

// 模拟用户登录
if (!isset($_SESSION['user_id'])) {
    $_SESSION['user_id'] = 4;
    $_SESSION['user_name'] = '测试用户';
    $_SESSION['nickname'] = '测试用户';
}

require_once '../../houtai_backup/db_config.php';

echo '<h1>🚀 基于现有实时通知系统的客服聊天测试</h1>';

try {
    $pdo = getDbConnection();
    
    // 创建测试会话
    $testSessionId = 'realtime_chat_' . time() . '_' . rand(1000, 9999);
    
    $stmt = $pdo->prepare("
        INSERT INTO customer_service_sessions
        (session_id, user_id, user_name, status, priority, source, started_at)
        VALUES (?, ?, ?, 'active', 'normal', 'web', NOW())
    ");
    $stmt->execute([
        $testSessionId,
        $_SESSION['user_id'],
        $_SESSION['user_name']
    ]);
    
    echo '<div style="background: #d4edda; padding: 15px; border-radius: 8px; margin: 10px 0;">';
    echo '<h3>✅ 实时聊天测试会话创建成功</h3>';
    echo '<p><strong>会话ID:</strong> ' . htmlspecialchars($testSessionId) . '</p>';
    echo '<p><strong>用户ID:</strong> ' . $_SESSION['user_id'] . '</p>';
    echo '<p><strong>状态:</strong> active（已激活）</p>';
    echo '</div>';
    
    // 获取现有会话
    $stmt = $pdo->prepare("
        SELECT session_id, status, customer_service_id, started_at, message_count
        FROM customer_service_sessions 
        WHERE user_id = ? 
        ORDER BY started_at DESC 
        LIMIT 5
    ");
    $stmt->execute([$_SESSION['user_id']]);
    $sessions = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (!empty($sessions)) {
        echo '<h2>📋 您的客服会话</h2>';
        
        foreach ($sessions as $session) {
            $statusColor = $session['status'] === 'active' ? 'green' : ($session['status'] === 'waiting' ? 'orange' : 'gray');
            
            echo '<div style="border: 1px solid #ddd; padding: 15px; margin: 10px 0; border-radius: 8px; background: #f9f9f9;">';
            echo '<h4>会话: ' . htmlspecialchars($session['session_id']) . '</h4>';
            echo '<p><strong>状态:</strong> <span style="color: ' . $statusColor . ';">' . htmlspecialchars($session['status']) . '</span></p>';
            echo '<p><strong>客服ID:</strong> ' . htmlspecialchars($session['customer_service_id'] ?? '未分配') . '</p>';
            echo '<p><strong>消息数量:</strong> ' . htmlspecialchars($session['message_count']) . '</p>';
            echo '<p><strong>开始时间:</strong> ' . htmlspecialchars($session['started_at']) . '</p>';
            
            echo '<div style="margin-top: 15px;">';
            echo '<a href="chat_with_realtime.php?session_id=' . urlencode($session['session_id']) . '" style="background: #6F7BF5; color: white; padding: 8px 16px; text-decoration: none; border-radius: 5px; margin-right: 10px;">🚀 实时聊天</a>';
            echo '<a href="../demo_realtime_notifications.html" target="_blank" style="background: #28a745; color: white; padding: 8px 16px; text-decoration: none; border-radius: 5px; margin-right: 10px;">🔔 通知演示</a>';
            echo '<button onclick="testRealtimeAPI(\'' . $session['session_id'] . '\')" style="background: #17a2b8; color: white; padding: 8px 16px; border: none; border-radius: 5px; cursor: pointer;">🧪 测试通知</button>';
            echo '</div>';
            
            echo '</div>';
        }
    }
    
} catch (Exception $e) {
    echo '<div style="background: #f8d7da; padding: 15px; border-radius: 8px; margin: 10px 0; color: #721c24;">';
    echo '<h3>❌ 错误</h3>';
    echo '<p>' . htmlspecialchars($e->getMessage()) . '</p>';
    echo '</div>';
}
?>

<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>实时聊天测试</title>
    <style>
        body { 
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; 
            margin: 20px; 
            background: #f5f5f5; 
        }
        .container { 
            max-width: 1000px; 
            margin: 0 auto; 
            background: white; 
            padding: 20px; 
            border-radius: 10px; 
            box-shadow: 0 2px 10px rgba(0,0,0,0.1); 
        }
        .feature-box {
            background: #e7f3ff;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
            border-left: 4px solid #6F7BF5;
        }
        .step-box {
            background: #fff3cd;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
            border-left: 4px solid #ffc107;
        }
        .result {
            margin: 10px 0;
            padding: 15px;
            border-radius: 8px;
        }
        .success { background: #d4edda; color: #155724; border-left: 4px solid #28a745; }
        .error { background: #f8d7da; color: #721c24; border-left: 4px solid #dc3545; }
        .info { background: #d1ecf1; color: #0c5460; border-left: 4px solid #17a2b8; }
        pre { 
            background: #f8f9fa; 
            padding: 10px; 
            border-radius: 5px; 
            overflow-x: auto; 
            font-size: 12px; 
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="feature-box">
            <h3>🎯 实时通知系统集成</h3>
            <p>我们发现您已经有一个完整的实时通知系统！现在我们将客服聊天功能集成到这个系统中：</p>
            <ul>
                <li><strong>现有系统</strong>：SSE实时通知，用于验证码推送</li>
                <li><strong>新功能</strong>：客服消息也通过同一个通知系统推送</li>
                <li><strong>优势</strong>：复用成熟的实时通信基础设施</li>
                <li><strong>稳定性</strong>：基于已验证可用的通知系统</li>
            </ul>
        </div>
        
        <div class="step-box">
            <h3>📝 测试步骤</h3>
            <ol>
                <li><strong>打开实时聊天</strong>：点击上面的"🚀 实时聊天"按钮</li>
                <li><strong>观察连接状态</strong>：页面应显示"已连接"状态</li>
                <li><strong>打开客服后台</strong>：<a href="../../houtai_backup/customer_service_system/quick_message_test.php" target="_blank">客服发送消息</a></li>
                <li><strong>发送测试消息</strong>：在客服后台发送消息给用户</li>
                <li><strong>验证实时接收</strong>：前台应立即收到消息（通过实时通知系统）</li>
                <li><strong>测试通知演示</strong>：点击"🔔 通知演示"查看现有通知系统</li>
            </ol>
        </div>
        
        <div id="test-result"></div>
        
        <div class="info">
            <h3>🔧 技术实现</h3>
            <ul>
                <li><strong>复用现有SSE服务</strong>：`frontend/api/realtime_notifications.php`</li>
                <li><strong>扩展通知类型</strong>：添加 `customer_service_message` 类型</li>
                <li><strong>统一通知处理</strong>：验证码和客服消息使用同一套系统</li>
                <li><strong>降级机制</strong>：如果SSE失败，自动切换到轮询模式</li>
            </ul>
        </div>
        
        <div class="feature-box">
            <h3>💡 系统优势</h3>
            <ul>
                <li><strong>已验证可用</strong>：验证码推送功能已经在正常工作</li>
                <li><strong>技术成熟</strong>：基于现有的SSE实时通信基础设施</li>
                <li><strong>维护简单</strong>：不需要额外的WebSocket服务器</li>
                <li><strong>兼容性好</strong>：支持所有现代浏览器</li>
            </ul>
        </div>
        
        <p style="margin-top: 30px;">
            <a href="index.php">返回客服首页</a> | 
            <a href="../../houtai_backup/customer_service_system/sessions.php">客服后台</a> | 
            <a href="../demo_realtime_notifications.html">通知演示页面</a>
        </p>
    </div>
    
    <script>
        async function testRealtimeAPI(sessionId) {
            const resultDiv = document.getElementById('test-result');
            resultDiv.innerHTML = '<div class="result info">🔄 测试实时通知API...</div>';
            
            try {
                // 模拟发送客服消息通知
                const notificationData = {
                    user_id: <?php echo $_SESSION['user_id']; ?>,
                    type: 'customer_service_message',
                    title: '客服回复',
                    content: '这是一条测试消息',
                    data: JSON.stringify({
                        type: 'customer_service_message',
                        session_id: sessionId,
                        content: '这是一条测试消息',
                        sender_name: '测试客服',
                        timestamp: Date.now()
                    })
                };
                
                // 这里可以调用实际的通知API
                console.log('模拟发送通知:', notificationData);
                
                resultDiv.innerHTML = `
                    <div class="result success">
                        <h3>✅ 通知测试完成</h3>
                        <p><strong>会话ID：</strong>${sessionId}</p>
                        <p><strong>用户ID：</strong><?php echo $_SESSION['user_id']; ?></p>
                        <p><strong>通知类型：</strong>customer_service_message</p>
                        <pre>${JSON.stringify(notificationData, null, 2)}</pre>
                        <div style="margin-top: 15px;">
                            <a href="chat_with_realtime.php?session_id=${sessionId}" style="background: #6F7BF5; color: white; padding: 8px 16px; text-decoration: none; border-radius: 5px; margin-right: 10px;">🚀 打开实时聊天</a>
                            <a href="../../houtai_backup/customer_service_system/quick_message_test.php" target="_blank" style="background: #dc3545; color: white; padding: 8px 16px; text-decoration: none; border-radius: 5px;">📤 发送真实消息</a>
                        </div>
                    </div>
                `;
                
            } catch (error) {
                console.error('测试失败:', error);
                resultDiv.innerHTML = `
                    <div class="result error">
                        <h3>❌ 测试失败</h3>
                        <p>${error.message}</p>
                    </div>
                `;
            }
        }
    </script>
</body>
</html>
