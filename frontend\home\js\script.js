// ---- 诊断版本 script.js 开始 ----

// 全局 Toast DOM 元素引用
let toastContainer = null;
let toastTimeout = null;

function showToast(message, duration = 3000) {
    // console.log(`【诊断】showToast 被调用, 消息: ${message}`); // 保留 console.log 用于调试

    if (!toastContainer) {
        toastContainer = document.createElement('div');
        toastContainer.className = 'toast-container';
        // 美化样式
        toastContainer.style.position = 'fixed';
        toastContainer.style.bottom = '20px';
        toastContainer.style.left = '50%';
        toastContainer.style.transform = 'translateX(-50%)';
        toastContainer.style.backgroundColor = 'rgba(0,0,0,0.8)';
        toastContainer.style.color = 'white';
        toastContainer.style.padding = '10px 20px';
        toastContainer.style.borderRadius = '5px';
        toastContainer.style.zIndex = '10000'; //确保在最上层
        toastContainer.style.fontSize = '14px'; // 调整字体大小
        toastContainer.style.opacity = '0'; // 初始透明
        toastContainer.style.transition = 'opacity 0.5s ease'; // 平滑过渡
        document.body.appendChild(toastContainer);
    }

    toastContainer.textContent = message;
    // console.log(`【诊断】Toast DOM 操作完成，尝试显示: ${message}`);

    // 显示 Toast
    toastContainer.style.opacity = '1';

    if (toastTimeout) {
        clearTimeout(toastTimeout);
    }

    toastTimeout = setTimeout(() => {
        toastContainer.style.opacity = '0';
        // console.log(`【诊断】Toast 隐藏: ${message}`);
    }, duration);
}

// 全局变量存储当前城市
let currentCity = '北京市'; // 默认城市
let initialLocationAttempted = false; // 防止重复调用
let amapApiLoadedToastShown = false; // 新增标志：控制API加载提示是否已显示

const CITY_EXPIRATION_MS = 6 * 60 * 60 * 1000; // 城市信息过期时间，例如6小时

// 高德API加载完成后的回调函数
function onAMapLoaded() {
    console.log("onAMapLoaded: Callback received. AMap object should be available.");

    // 只有在首次API加载，或者需要通过API进行定位设置时才提示API已加载
    if (!amapApiLoadedToastShown && !initialLocationAttempted) {
        // showToast("高德API已加载", 1500); // 注释掉
        amapApiLoadedToastShown = true;
    } else if (!amapApiLoadedToastShown && initialLocationAttempted && !localStorage.getItem('userCity')){
        // 如果之前从缓存加载了城市，但API现在才加载完成，也提示一次（主要用于首次访问，缓存城市后API才加载的场景）
        // showToast("高德API已加载", 1500); // 注释掉
        amapApiLoadedToastShown = true;
    }

    if (typeof initLocation === 'function') {
        if (!initialLocationAttempted) {
            console.log("onAMapLoaded: Calling initLocation for the first time.");
            initLocation();
            initialLocationAttempted = true;
        } else {
            console.log("onAMapLoaded: initLocation already attempted, not calling again automatically.");
        }
    } else {
        showToast("错误: initLocation 函数未定义", 5000);
        console.error("onAMapLoaded: initLocation 函数未定义。");
    }
}

// DOMContentLoaded 事件触发后，如果高德API还未加载，则不直接调用initLocation
// initLocation 的调用移至 onAMapLoaded 回调中
document.addEventListener('DOMContentLoaded', function() {
    console.log("DOMContentLoaded: Script starting.");

    // --- START: Centralized Variable Declarations ---
    // Common Elements
    const overlay = document.getElementById('overlay'); // Was line 86
    const searchInput = document.getElementById('main-search-input'); // Was line 87
    const cityNameSpanHeader = document.getElementById('current-city-minimal'); // Was line 88

    // Feature Navigation / Tabs
    const featureNav = document.querySelector('.feature-nav'); // Was line 91
    const activeTabIndicatorContainer = featureNav ? featureNav.querySelector('.active-tab-indicator-container') : null; // Was line 92
    const featureItems = featureNav ? Array.from(featureNav.querySelectorAll('.feature-item')) : []; // Was line 93
    const subOptionsLists = document.querySelectorAll('.sub-options-list'); // Was line 94
    const subOptionsWrapper = document.querySelector('.sub-options-wrapper'); // 添加缺少的变量

    // City Selector Elements
    const locationElement = document.getElementById('location'); // Was line 204
    const citySelectorModal = document.getElementById('city-selector'); // Was line 205 (standardized from citySelector)
    const closeCitySelectorBtn = document.getElementById('close-city-selector'); // Was line 206
    const cityItemsInModal = citySelectorModal ? citySelectorModal.querySelectorAll('.city-selector .city-item') : []; // Was line 207
    const autoLocateBtn = document.getElementById('auto-locate-btn'); // Was line 208
    const selectedCityDisplayElement = document.getElementById('selected-city-display'); // Was line 209
    const citySearchInput = document.getElementById('city-search-input'); // Was line 210
    const cityListContainer = citySelectorModal ? citySelectorModal.querySelector('.city-list') : null; // Was line 211
    const allCityItems = citySelectorModal ? Array.from(citySelectorModal.querySelectorAll('.city-selector .city-item')) : []; // Corrected: Select all .city-item elements for search

    // Date Selector Elements
    const selectedDateDisplayElement = document.getElementById('selected-date-display'); // Was line 219
    const currentSelectedDateText = document.getElementById('current-selected-date-text'); // Was line 220
    const dateSelectorModal = document.getElementById('date-selector-modal'); // Was line 221
    const closeDateSelectorBtn = document.getElementById('close-date-selector'); // Was line 222
    const prevMonthBtn = document.getElementById('prev-month-btn'); // Was line 223
    const nextMonthBtn = document.getElementById('next-month-btn'); // Was line 224
    const currentMonthYearDisplay = document.getElementById('current-month-year-display'); // Was line 225
    const calendarDaysGrid = document.getElementById('calendar-days-grid'); // Was line 226
    const confirmDateBtn = document.getElementById('confirm-date-btn'); // Was line 227

    // New Dynamic Search Button Element
    const dynamicSearchBtn = document.getElementById('dynamic-search-btn'); // ADDED

    // Calendar State Variables
    let calendarCurrentDate = new Date(); // Was line 273 / 322 / 640
    let today = new Date(); // Standardized 'today' variable from lines 274, 323, 641
    today.setHours(0, 0, 0, 0);
    let calendarSelectedDate = null; // Was line 276 / 325 / 643

    // Search Overlay Page Elements (NEW)
    const searchOverlayPage = document.getElementById('search-overlay-page');
    const searchOverlayBackBtn = document.getElementById('search-overlay-back-btn');
    const searchOverlayCancelBtn = document.getElementById('search-overlay-cancel-btn');
    const searchOverlayInput = document.getElementById('search-overlay-input');
    const gameSpecificFiltersContainer = document.getElementById('game-specific-filters'); // ADDED FOR GAME FILTERS
    // const gameCompanionContentArea = document.getElementById('game-companion-content-area'); // Old game content area, will be replaced by new sections

    // NEW: Refactored content section elements
    const refactoredContentArea = document.getElementById('refactored-content-area');
    const gameCompanionSection = document.getElementById('game-companion-section');
    const cityCompanionSection = document.getElementById('city-companion-section');
    const attractionTicketsSection = document.getElementById('attraction-tickets-section');
    const groupUpSection = document.getElementById('group-up-section');
    const emptyStatePlaceholder = document.getElementById('empty-state-placeholder');

    // Map feature keys to their respective content sections
    const contentSectionsMap = {
        game: gameCompanionSection,
        city: cityCompanionSection,
        ticket: attractionTicketsSection,
        group: groupUpSection
    };
    // --- END: Centralized Variable Declarations ---

    function updateCityDisplay(newCity) {
        if (typeof newCity !== 'string' || newCity.trim() === "") {
            console.warn("updateCityDisplay: 无效的城市名称或城市为空，已中止更新。", newCity);
            return;
        }

        // 去掉城市名后的"市"字
        let displayCity = newCity;
        if (displayCity.endsWith('市')) {
            displayCity = displayCity.slice(0, -1);
        }

        currentCity = newCity; // 保存完整的城市名用于其他用途

        // 更新页眉中的最小化城市显示 (例如, 顶部栏)
        if (cityNameSpanHeader) {
            cityNameSpanHeader.textContent = displayCity;
            console.log(`【诊断】页眉城市 (current-city-minimal) 更新为: ${displayCity}`);
        } else {
            console.warn("updateCityDisplay: cityNameSpanHeader (current-city-minimal) DOM元素未找到。");
        }

        // 更新可点击的 "selected-city-display" 元素内的城市显示
        const currentSelectedCityText = document.getElementById('current-selected-city-text');
        if (currentSelectedCityText) {
            currentSelectedCityText.textContent = displayCity;
            console.log(`【诊断】选择城市区域 (current-selected-city-text) 更新为: ${displayCity}`);
        } else {
            console.warn("updateCityDisplay: current-selected-city-text DOM元素未找到。主选择框内的城市显示可能不会更新。");
        }
        console.log(`【诊断】updateCityDisplay: 全局 currentCity 设置为 ${currentCity}，显示为 ${displayCity}。UI更新尝试已完成。`);
    }

    // --- DOM Element Retrieval (Common Elements First) ---
    // const overlay = document.getElementById('overlay'); // Declare overlay once here // REMOVED (declared above)
    // const searchInput = document.getElementById('main-search-input'); // REMOVED (declared above)
    // const cityNameSpanHeader = document.getElementById('current-city-minimal'); // Used by city logic // REMOVED (declared above)

    // --- Feature Navigation / Tabs ---
    // const featureNav = document.querySelector('.feature-nav'); // REMOVED (declared above)
    // const activeTabIndicatorContainer = featureNav ? featureNav.querySelector('.active-tab-indicator-container') : null; // REMOVED (declared above)
    // const featureItems = featureNav ? Array.from(featureNav.querySelectorAll('.feature-item')) : []; // REMOVED (declared above)
    // const subOptionsLists = document.querySelectorAll('.sub-options-list'); // REMOVED (declared above)

    // --- Main Feature Navigation Logic ---

    // Function to set the active sub-option tab
    function setActiveSubOption(selectedSubOptionTab, parentSubOptionsList) {
        if (!selectedSubOptionTab || !parentSubOptionsList) {
            console.error("setActiveSubOption: Invalid arguments. Tab or parent list is missing.", { tab: selectedSubOptionTab, list: parentSubOptionsList });
            return;
        }
        // console.log("setActiveSubOption called for:", selectedSubOptionTab.textContent, "in list:", parentSubOptionsList.id);

        // Remove active class from all tabs in this list
        parentSubOptionsList.querySelectorAll('.sub-option-tab').forEach(siblingTab => {
            siblingTab.classList.remove('active');
        });

        // Add active class to the selected tab
        selectedSubOptionTab.classList.add('active');

        // Placeholder for any additional logic needed when a sub-option becomes active
        // e.g., loading content, filtering results, etc.
        const subOptionType = selectedSubOptionTab.getAttribute('data-type');
        // console.log(`Sub-option type "${subOptionType}" activated.`);
        // Add specific actions based on subOptionType if needed in the future
    }


    window.setActiveFeature = function setActiveFeature(selectedFeatureItem) {
        if (!selectedFeatureItem) {
            console.error("setActiveFeature: selectedFeatureItem is null or undefined.");
            return;
        }
        if (!featureNav) {
            console.error("setActiveFeature: featureNav element is not available.");
            return;
        }
        if (!activeTabIndicatorContainer) {
            console.error("setActiveFeature: activeTabIndicatorContainer is not available.");
            return;
        }

        // Update active class for feature items
        featureItems.forEach(item => item.classList.remove('active'));
        selectedFeatureItem.classList.add('active');

        const featureKey = selectedFeatureItem.getAttribute('data-feature');
        console.log("Selected feature key:", featureKey);

        // Hide all new content sections first
        Object.values(contentSectionsMap).forEach(section => {
            if (section) section.classList.remove('active');
        });

        // Show the target new content section
        const targetContentSection = contentSectionsMap[featureKey];
        if (targetContentSection) {
            targetContentSection.classList.add('active');
            if (emptyStatePlaceholder) emptyStatePlaceholder.style.display = 'none';
        } else {
            // If no specific content section, show empty state or hide all
            console.warn(`No content section defined for feature: ${featureKey}`);
            if (emptyStatePlaceholder) emptyStatePlaceholder.style.display = 'block';
        }

        // Manage visibility of sub-options and city/date selectors
        if (featureKey === 'game') {
            // For "游戏玩伴", hide sub-options wrapper and city/date selectors
            if (subOptionsWrapper) subOptionsWrapper.style.display = 'none';
            if (selectedCityDisplayElement) selectedCityDisplayElement.style.display = 'none';
            if (selectedDateDisplayElement) selectedDateDisplayElement.style.display = 'none';
            // The gameCompanionSection (which is targetContentSection for 'game') is already handled above
        } else {
            // For other features, show sub-options wrapper and city/date selectors
            if (subOptionsWrapper) subOptionsWrapper.style.display = 'flex';
            if (selectedCityDisplayElement) selectedCityDisplayElement.style.display = 'flex';
            if (selectedDateDisplayElement) selectedDateDisplayElement.style.display = 'flex';

            // Activate the correct sub-options list
            subOptionsLists.forEach(list => {
                if (list.id === `sub-options-${featureKey}`) {
                    list.classList.add('active');
                    // Activate first sub-option tab in this list
                    const firstSubOptionTab = list.querySelector('.sub-option-tab');
                    if (firstSubOptionTab) {
                        setActiveSubOption(firstSubOptionTab, list);
                    }
                } else {
                    list.classList.remove('active');
                }
            });
        }

        // Update dynamic search button text based on featureKey (existing logic)
        if (dynamicSearchBtn) {
            switch (featureKey) {
                case 'city': // Keep 'game' here if search button logic is shared
                case 'game':
                    dynamicSearchBtn.textContent = '搜索玩伴';
                    break;
                case 'group':
                    dynamicSearchBtn.textContent = '搜索组局';
                    break;
                case 'ticket': // Added case for tickets
                    dynamicSearchBtn.textContent = '搜索门票';
                    break;
                default:
                    dynamicSearchBtn.textContent = '开始探索';
                    break;
            }
        }

        // Move indicator (existing logic)
        const selectedFeatureRect = selectedFeatureItem.getBoundingClientRect();
        const featureNavRect = featureNav.getBoundingClientRect();
        const indicatorOffset = selectedFeatureRect.left - featureNavRect.left + (selectedFeatureRect.width / 2) - (activeTabIndicatorContainer.offsetWidth / 2);
        activeTabIndicatorContainer.style.left = `${indicatorOffset}px`;

        // Note: The old logic for fetching game_companion_page.html is removed
        // as content is now part of the main page structure.
        // If dynamic loading per section is needed later, it would be a new feature.
    }

    // Initialize Feature Navigation and Event Listeners
    if (featureItems && featureItems.length > 0) {
        featureItems.forEach(item => {
            item.addEventListener('click', function() {
                setActiveFeature(this);
            });
        });

        // Set the first feature item as active by default
        // Ensure featureItems[0] exists before calling setActiveFeature
        // Find the initially active feature item from HTML and set it
        const initiallyActiveFeature = featureNav ? featureNav.querySelector('.feature-item.active') : null;
        if (initiallyActiveFeature) {
            setActiveFeature(initiallyActiveFeature);
        } else if (featureItems[0]) { // Fallback to the first item if none are marked active
            setActiveFeature(featureItems[0]);
        }
    } else {
        if (!featureNav) {
            console.error("Feature navigation container (.feature-nav) not found. Feature selection will not work.");
        } else {
            console.error("No feature items (.feature-item) found. Feature selection will not work.");
        }
        if (activeTabIndicatorContainer) {
            // Consider hiding the indicator if there are no items or it's not initialized
            // activeTabIndicatorContainer.style.display = 'none'; // Or handle appropriately
            console.log("Active tab indicator might not be positioned correctly as no feature items are active.");
        }
    }


    // --- Sub-Option Tab Logic ---
    function initializeSubOptionTabs() {
        subOptionsLists.forEach(list => {
            const tabsInList = list.querySelectorAll('.sub-option-tab');
            if (tabsInList.length > 0) {
                tabsInList.forEach(tab => {
                    tab.addEventListener('click', function(event) {
                        event.stopPropagation(); // Prevent click from bubbling to parent elements if not desired
                        // console.log("Sub-option tab clicked:", this.textContent); // Logging is now in setActiveSubOption or can be re-enabled if needed
                        const parentList = this.closest('.sub-options-list');
                        if (parentList) {
                            setActiveSubOption(this, parentList); // Call the centralized function
                        } else {
                            console.error("Could not find parent .sub-options-list for tab:", this);
                        }
                    });
                });
            }
        });
    }
    initializeSubOptionTabs();

    // --- City Selector Logic ---
    // const locationElement = document.getElementById('location');  // REMOVED
    // const citySelectorModal = document.getElementById('city-selector'); // Renamed from citySelector to avoid conflict if any // REMOVED
    // const closeCitySelectorBtn = document.getElementById('close-city-selector'); // Renamed from closeCitySelector // REMOVED
    // const cityItemsInModal = citySelectorModal ? citySelectorModal.querySelectorAll('.city-selector .city-item') : []; // REMOVED
    // const autoLocateBtn = document.getElementById('auto-locate-btn'); // REMOVED
    // const selectedCityDisplayElement = document.getElementById('selected-city-display'); // For white container display // REMOVED
    // const citySearchInput = document.getElementById('city-search-input'); // REMOVED
    // const cityListContainer = citySelectorModal ? citySelectorModal.querySelector('.city-list') : null; // REMOVED
    // const allCityItems = cityListContainer ? Array.from(cityListContainer.querySelectorAll('.city-item')) : []; // REMOVED

    // (City loading from localStorage, event listeners for locationElement, closeCitySelectorBtn, cityItemsInModal, autoLocateBtn, citySearchInput should be here)
    // Make sure to use citySelectorModal and closeCitySelectorBtn
    // The overlay variable is already defined above.

    // --- Date Selector Logic (Attempting to integrate previously unsaved code) ---
    // const selectedDateDisplayElement = document.getElementById('selected-date-display'); // REMOVED
    // const currentSelectedDateText = document.getElementById('current-selected-date-text'); // REMOVED
    // const dateSelectorModal = document.getElementById('date-selector-modal'); // REMOVED
    // const closeDateSelectorBtn = document.getElementById('close-date-selector'); // REMOVED
    // const prevMonthBtn = document.getElementById('prev-month-btn'); // REMOVED
    // const nextMonthBtn = document.getElementById('next-month-btn'); // REMOVED
    // const currentMonthYearDisplay = document.getElementById('current-month-year-display'); // REMOVED
    // const calendarDaysGrid = document.getElementById('calendar-days-grid'); // REMOVED
    // const confirmDateBtn = document.getElementById('confirm-date-btn'); // REMOVED
    // overlay is already defined at the top of DOMContentLoaded

    // --- Re-inserting City Selector Logic (using the single overlay) ---
    if (locationElement && citySelectorModal && overlay && closeCitySelectorBtn && cityNameSpanHeader) {
        locationElement.addEventListener('click', () => {
            citySelectorModal.classList.add('active');
            if(overlay) overlay.style.display = 'block';
            document.body.style.overflow = 'hidden';
        });

        function closeCityModal() { // Renamed to avoid conflict with date modal's closer
            if (citySelectorModal) citySelectorModal.classList.remove('active');
            // Only hide overlay if date selector is also not active
            if (overlay && !(dateSelectorModal && dateSelectorModal.classList.contains('active'))) {
                overlay.style.display = 'none';
                document.body.style.overflow = '';
            }
        }

        if(closeCitySelectorBtn) closeCitySelectorBtn.addEventListener('click', closeCityModal);
        // Note: The main overlay click listener will be defined later to handle both modals.

        cityItemsInModal.forEach(item => {
            item.addEventListener('click', () => {
                const selectedCity = item.getAttribute('data-city');
                updateCityDisplay(selectedCity);
                try {
                    localStorage.setItem('userCity', selectedCity);
                    localStorage.setItem('userCityTimestamp', new Date().getTime().toString());
                } catch (e) {
                    console.error("City Selector: 保存城市到 localStorage 失败: ", e);
                }
                closeCityModal();
            });
        });

        // Function to initialize location services and get current city
        function initLocation() {
            console.log("【增强日志】initLocation: 函数开始执行。");

            if (typeof AMap === 'undefined' || typeof AMap.Geolocation === 'undefined') {
                showToast("地图服务(AMap)尚未准备就绪，请稍后或手动选择城市。", 3500);
                console.error("【增强日志】initLocation: AMap 或 AMap.Geolocation 未定义。无法进行定位。");
                const storedCity = localStorage.getItem('userCity');
                const cityTimestamp = localStorage.getItem('userCityTimestamp');
                if (storedCity && cityTimestamp) {
                    const age = new Date().getTime() - parseInt(cityTimestamp, 10);
                    if (age < CITY_EXPIRATION_MS) {
                        console.log("【增强日志】initLocation: AMap未就绪，但从localStorage加载了有效城市: ", storedCity);
                        updateCityDisplay(storedCity);
                        return;
                    }
                }
                console.log("【增强日志】initLocation: AMap未就绪，且无有效本地缓存城市。");
                return;
            }

            console.log("【增强日志】initLocation: AMap API 已加载。准备实例化 Geolocation。");
            let geolocation;
            try {
                geolocation = new AMap.Geolocation({
                    enableHighAccuracy: true, // 尝试高精度
                    timeout: 15000,          // 延长超时时间至15秒
                    extensions: 'all',       // 获取详细地址信息
                    needAddress: true,       // 明确需要地址信息
                    noIpLocate: 0,           // 0: 使用IP定位；1: 不使用IP定位；2: 使用IP但仅返回城市信息；3: 使用IP但仅返回省信息
                    noGeoLocation: 0,        // 0: 使用浏览器定位；1: 仅使用IP定位；2: IP和浏览器同时使用；3: IP优先
                    GeoLocationFirst: false, // 是否优先使用浏览器定位，在高精度模式下建议为false
                    maximumAge: 0,           // 不使用缓存结果
                    convert: true            // 自动将高德坐标转换为标准GPS坐标
                });
                console.log("【增强日志】initLocation: AMap.Geolocation 实例化成功。");
            } catch (e) {
                console.error("【增强日志】initLocation: AMap.Geolocation 实例化失败: ", e);
                showToast("地图定位组件初始化失败，请刷新页面或联系技术支持。", 5000);
                return;
            }

            showToast("正在努力获取您当前的位置信息...", 3000);
            console.log("【增强日志】initLocation: 调用 getCurrentPosition。");

            geolocation.getCurrentPosition(function(status, result) {
                console.log("【增强日志】initLocation: getCurrentPosition 回调。 Status:", status, "Result:", JSON.stringify(result));

                if (status === 'complete' && result && result.info === 'SUCCESS') {
                    const addressComponent = result.addressComponent;
                    let city = addressComponent ? addressComponent.city : null;

                    if (!city && addressComponent && addressComponent.province) {
                        city = addressComponent.province; // 直辖市处理
                        console.log("【增强日志】initLocation: 获取到省份作为城市 (直辖市):", city);
                    }

                    if (city && typeof city === 'string' && city.trim() !== "") {
                        console.log("【增强日志】initLocation: 定位成功。城市:", city, "完整结果:", result);
                        showToast(`定位成功: ${city}`, 3000);
                        updateCityDisplay(city);

                        try {
                            localStorage.setItem('userCity', city);
                            localStorage.setItem('userCityTimestamp', new Date().getTime().toString());
                            console.log("【增强日志】initLocation: 城市信息已保存到 localStorage。");
                        } catch (e) {
                            console.error("【增强日志】initLocation: 保存城市到 localStorage 失败: ", e);
                            showToast("无法将城市信息保存到本地存储", 2500);
                        }

                        if (citySelectorModal && citySelectorModal.classList.contains('active')) {
                            closeCityModal();
                            console.log("【增强日志】initLocation: 自动关闭了城市选择器。");
                        }
                    } else {
                        console.warn("【增强日志】initLocation: 定位成功，但未能从结果中提取有效的城市信息。Result:", result);
                        showToast("获取到位置，但城市信息不明确，请尝试手动选择。", 4000);
                    }
                } else {
                    // 定位失败处理
                    console.error("【增强日志】initLocation: 定位失败。Status:", status, "Result:", result);
                    let errorMsg = "定位失败";
                    let detailedError = "未知错误";

                    if (result) {
                        detailedError = result.message || result.info || JSON.stringify(result);
                        if (result.message && result.message.includes("key")){
                            errorMsg = "定位服务API密钥可能存在问题，请联系管理员。";
                        } else if (result.info) {
                            switch (result.info) {
                                case 'PERMISSION_DENIED':
                                    errorMsg = "定位失败：您已拒绝位置权限。请检查浏览器或系统设置，允许后重试。";
                                    break;
                                case 'POSITION_UNAVAILABLE':
                                    errorMsg = "定位失败：暂时无法获取您的位置信息。请检查网络连接或GPS信号后重试。";
                                    break;
                                case 'TIMEOUT':
                                    errorMsg = "定位失败：获取位置信息超时。请检查网络连接后重试。";
                                    break;
                                case 'ILLEGAL_REQUEST':
                                case 'INVALID_PARAM':
                                    errorMsg = "定位请求参数错误，请联系技术支持。";
                                    break;
                                case 'SERVICE_NOT_AVAILABLE':
                                    errorMsg = "定位服务当前不可用，请稍后再试。";
                                    break;
                                default:
                                    errorMsg = `定位失败: ${result.info}`;
                                    if (result.message && result.message !== result.info) {
                                        errorMsg += ` (${result.message.substring(0,100)})`;
                                    }
                                    break;
                            }
                        } else if (result.message) {
                             errorMsg = `定位失败: ${result.message.substring(0,100)}`;
                        }
                    } else if (status) {
                        detailedError = `Status: ${status}`;
                        errorMsg = `定位失败 (状态: ${status})，请检查网络或权限。`;
                    }

                    console.error(`【增强日志】initLocation: 定位失败详细信息: ${detailedError}`);
                    showToast(errorMsg, 5000);
                }
            });
        }
        window.initLocation = initLocation; // Make it globally accessible for onAMapLoaded

        if (autoLocateBtn) {
            autoLocateBtn.addEventListener('click', () => {
                showToast("尝试自动定位...", 2000);
                if (typeof initLocation === 'function') {
                    initLocation(); // Call the main initLocation function
                } else {
                    showToast("定位功能初始化失败", 3000);
                    console.error("autoLocateBtn: initLocation function is not defined.");
                }
                // We might want to close the city selector Erfolg or failure,
                // but initLocation itself doesn't close it, so maybe not here.
                // Consider if closeCityModal() should be called after location attempt.
            });
        }
        if (selectedCityDisplayElement) {
            selectedCityDisplayElement.addEventListener('click', () => {
                if (citySelectorModal && overlay) {
                    citySelectorModal.classList.add('active');
                    overlay.style.display = 'block';
                    document.body.style.overflow = 'hidden';
                }
            });
        }
        if (citySearchInput) { /* ... citySearchInput logic ... */ }
    }

    // --- Date Selector Event Listener Logic ---
    if (selectedDateDisplayElement && dateSelectorModal && overlay && closeDateSelectorBtn && calendarDaysGrid && confirmDateBtn) {
        selectedDateDisplayElement.addEventListener('click', () => {
            // console.log("selectedDateDisplayElement clicked to open date selector");
            dateSelectorModal.classList.add('active');
            if (overlay) overlay.style.display = 'block';
            document.body.style.overflow = 'hidden';
            renderCalendar(); // Initial render when opening
        });

        if (closeDateSelectorBtn) {
            closeDateSelectorBtn.addEventListener('click', () => {
                // console.log("closeDateSelectorBtn clicked");
                if (dateSelectorModal) dateSelectorModal.classList.remove('active');
                if (overlay && !(citySelectorModal && citySelectorModal.classList.contains('active'))) {
                    overlay.style.display = 'none';
                    document.body.style.overflow = '';
                }
            });
        }

        // Event listener for confirm date button (ensure it closes modal and updates display)
        if (confirmDateBtn) {
            confirmDateBtn.addEventListener('click', () => {
                if (calendarSelectedDate) {
                    const formattedDate = `${calendarSelectedDate.getFullYear()}-${String(calendarSelectedDate.getMonth() + 1).padStart(2, '0')}-${String(calendarSelectedDate.getDate()).padStart(2, '0')}`;
                    if (currentSelectedDateText) currentSelectedDateText.textContent = formattedDate;
                    // console.log('Date confirmed:', formattedDate);
                    showToast(`日期已选择: ${formattedDate}`, 2000);
                    // Store confirmed date if needed, e.g., in a global variable or localStorage
                } else {
                    showToast('请先选择一个日期', 2000);
                    // return; // Optionally prevent closing if no date is selected, or let it close.
                }

                if (dateSelectorModal) dateSelectorModal.classList.remove('active');
                if (overlay && !(citySelectorModal && citySelectorModal.classList.contains('active'))) {
                    overlay.style.display = 'none';
                    document.body.style.overflow = '';
                }
            });
        }

        // Calendar navigation
        if (prevMonthBtn) {
            prevMonthBtn.addEventListener('click', () => {
                calendarCurrentDate.setMonth(calendarCurrentDate.getMonth() - 1);
                renderCalendar();
            });
        }

        if (nextMonthBtn) {
            nextMonthBtn.addEventListener('click', () => {
                calendarCurrentDate.setMonth(calendarCurrentDate.getMonth() + 1);
                renderCalendar();
            });
        }
    } else {
        console.error("One or more date selector elements (selectedDateDisplayElement, dateSelectorModal, overlay, closeDateSelectorBtn, calendarDaysGrid, confirmDateBtn) not found. Date selection functionality will be impaired.");
    }

    // Function to render the calendar (should be defined or moved here if not already)
    // Assuming renderCalendar is defined elsewhere and accessible, or include its definition if it was part of the unsaved code.
    // For now, let's ensure a placeholder or check if it's globally available.
    if (typeof renderCalendar !== 'function') {
        console.error("renderCalendar function is not defined. Calendar will not work.");
        // Placeholder if it's missing, to prevent further errors, though it won't be functional.
        // window.renderCalendar = function() { console.log('Dummy renderCalendar called.'); };
    }

    // --- Combined Overlay Click Listener ---
    if (overlay) {
        overlay.addEventListener('click', function() {
            let cityModalWasActive = false;
            let dateModalWasActive = false;

            if (citySelectorModal && citySelectorModal.classList.contains('active')) {
                citySelectorModal.classList.remove('active');
                cityModalWasActive = true;
            }
            if (dateSelectorModal && dateSelectorModal.classList.contains('active')) {
                dateSelectorModal.classList.remove('active');
                dateModalWasActive = true;
            }

            if (cityModalWasActive || dateModalWasActive) {
                 // If any modal was closed by overlay click, check if both are now closed
                if (!(citySelectorModal && citySelectorModal.classList.contains('active')) &&
                    !(dateSelectorModal && dateSelectorModal.classList.contains('active'))) {
                    overlay.style.display = 'none';
                    document.body.style.overflow = '';
                }
            }
        });
    }

    // --- 日期选择器相关 DOM ---
    // const selectedDateDisplayElement = document.getElementById('selected-date-display'); // REMOVED
    // const currentSelectedDateText = document.getElementById('current-selected-date-text'); // REMOVED
    // const dateSelectorModal = document.getElementById('date-selector-modal'); // REMOVED
    // const closeDateSelectorBtn = document.getElementById('close-date-selector'); // REMOVED
    // const prevMonthBtn = document.getElementById('prev-month-btn'); // REMOVED
    // const nextMonthBtn = document.getElementById('next-month-btn'); // REMOVED
    // const currentMonthYearDisplay = document.getElementById('current-month-year-display'); // REMOVED
    // const calendarDaysGrid = document.getElementById('calendar-days-grid'); // REMOVED
    // const confirmDateBtn = document.getElementById('confirm-date-btn'); // REMOVED
    // const overlay = document.getElementById('overlay'); // Assuming overlay is fetched once globally or here // REMOVED

    // let calendarCurrentDate = new Date(); // Renamed to avoid conflict with global currentCity // REMOVED
    // let today = new Date(); // REMOVED
    // today.setHours(0, 0, 0, 0);  // REMOVED
    // let calendarSelectedDate = null; // Renamed to avoid conflict // REMOVED

    function renderCalendar(year, month) {
        if (!calendarDaysGrid || !currentMonthYearDisplay || !prevMonthBtn) {
            console.error("Calendar elements not found for rendering.");
            return;
        }
        calendarDaysGrid.innerHTML = '';
        currentMonthYearDisplay.textContent = `${year}年${month + 1}月`;

        const firstDayOfMonth = new Date(year, month, 1);
        const daysInMonth = new Date(year, month + 1, 0).getDate();
        const startDayOfWeek = firstDayOfMonth.getDay();

        const firstDayOfCurrentDisplayMonth = new Date(year, month, 1);
        firstDayOfCurrentDisplayMonth.setHours(0,0,0,0);
        const firstDayOfTodayMonth = new Date(today.getFullYear(), today.getMonth(), 1);
        firstDayOfTodayMonth.setHours(0,0,0,0);

        prevMonthBtn.disabled = firstDayOfCurrentDisplayMonth <= firstDayOfTodayMonth;

        for (let i = 0; i < startDayOfWeek; i++) {
            const emptyCell = document.createElement('div');
            emptyCell.classList.add('calendar-day', 'empty');
            calendarDaysGrid.appendChild(emptyCell);
        }

        for (let day = 1; day <= daysInMonth; day++) {
            const dayCell = document.createElement('div');
            dayCell.classList.add('calendar-day');
            const dayNumberSpan = document.createElement('span');
            dayNumberSpan.classList.add('day-number');
            dayNumberSpan.textContent = day;
            dayCell.appendChild(dayNumberSpan);

            const date = new Date(year, month, day);
            date.setHours(0,0,0,0);

            if (date < today) {
                dayCell.classList.add('disabled');
            } else {
                dayCell.addEventListener('click', function() {
                    if (calendarSelectedDate) {
                        const prevSelectedElem = calendarDaysGrid.querySelector('.selected');
                        if (prevSelectedElem) prevSelectedElem.classList.remove('selected');
                    }
                    calendarSelectedDate = date;
                    this.classList.add('selected');
                });
            }

            if (date.getTime() === today.getTime()) {
                dayCell.classList.add('today');
                const dayInfoSpan = document.createElement('span');
                dayInfoSpan.classList.add('day-info');
                dayInfoSpan.textContent = '今天';
                dayCell.appendChild(dayInfoSpan);
            }

            if (calendarSelectedDate && date.getTime() === calendarSelectedDate.getTime()) {
                dayCell.classList.add('selected');
            }
            calendarDaysGrid.appendChild(dayCell);
        }
    }

    function openDateSelector() {
        calendarSelectedDate = null;
        calendarCurrentDate = new Date(today.getFullYear(), today.getMonth(), 1); // Start with today's month
        renderCalendar(calendarCurrentDate.getFullYear(), calendarCurrentDate.getMonth());
        if (dateSelectorModal && overlay) {
            dateSelectorModal.classList.add('active');
            overlay.style.display = 'block';
            document.body.style.overflow = 'hidden';
        }
    }

    function closeDateSelector() {
        if (dateSelectorModal && overlay) {
            dateSelectorModal.classList.remove('active');
            // Only hide overlay if city selector is also not active
            if (!document.getElementById('city-selector')?.classList.contains('active')) {
                overlay.style.display = 'none';
                document.body.style.overflow = '';
            }
        }
    }

    if (selectedDateDisplayElement) {
        selectedDateDisplayElement.addEventListener('click', openDateSelector);
    }
    if (closeDateSelectorBtn) {
        closeDateSelectorBtn.addEventListener('click', closeDateSelector);
    }

    // Modify overlay click listener to handle both modals
    if (overlay) {
        const citySelectorModal = document.getElementById('city-selector'); // Get city selector for combined logic
        overlay.addEventListener('click', function() {
            if (dateSelectorModal && dateSelectorModal.classList.contains('active')) {
                closeDateSelector();
            }
            // Assuming closeSelector() for city modal is defined elsewhere and handles citySelectorModal
            if (citySelectorModal && citySelectorModal.classList.contains('active')) {
                 // Call the city modal's close function, e.g., closeCitySelectorFunction();
                 // For now, directly manipulate:
                 citySelectorModal.classList.remove('active');
                 overlay.style.display = 'none'; // Hide if both are closed
                 document.body.style.overflow = '';
            }
             // If after attempting to close both, none are active, ensure overlay and body scroll are reset.
            if (!(dateSelectorModal && dateSelectorModal.classList.contains('active')) &&
                !(citySelectorModal && citySelectorModal.classList.contains('active'))) {
                overlay.style.display = 'none';
                document.body.style.overflow = '';
            }
        });
    }

    if (prevMonthBtn) {
        prevMonthBtn.addEventListener('click', () => {
            calendarCurrentDate.setMonth(calendarCurrentDate.getMonth() - 1);
            renderCalendar(calendarCurrentDate.getFullYear(), calendarCurrentDate.getMonth());
        });
    }
    if (nextMonthBtn) {
        nextMonthBtn.addEventListener('click', () => {
            calendarCurrentDate.setMonth(calendarCurrentDate.getMonth() + 1);
            renderCalendar(calendarCurrentDate.getFullYear(), calendarCurrentDate.getMonth());
        });
    }

    if (confirmDateBtn && currentSelectedDateText) {
        confirmDateBtn.addEventListener('click', () => {
            if (calendarSelectedDate) {
                const year = calendarSelectedDate.getFullYear();
                const month = calendarSelectedDate.getMonth() + 1;
                const day = calendarSelectedDate.getDate();
                const dayOfWeek = calendarSelectedDate.getDay();
                const weekdays = ['周日', '周一', '周二', '周三', '周四', '周五', '周六'];

                const formattedDate = `${month}月${day}日`;
                let relativeHint = weekdays[dayOfWeek]; // 默认为星期几

                const diffDays = Math.floor((calendarSelectedDate.getTime() - today.getTime()) / (1000 * 60 * 60 * 24));

                if (diffDays === 0) {
                    relativeHint = '今天';
                } else if (diffDays === 1) {
                    relativeHint = '明天';
                } else if (diffDays === 2) {
                    relativeHint = '后天';
                }
                currentSelectedDateText.textContent = `${formattedDate} ${relativeHint}`; // 更新后的显示方式
                closeDateSelector();
            } else {
                showToast('请先选择一个日期', 2000);
            }
        });
    }
    // --- END 日期选择器相关 ---

    // --- Dynamic Search Button Logic ---
    if (dynamicSearchBtn) {
        dynamicSearchBtn.addEventListener('click', () => {
            // 检查是否已选择日期
            // 在您的日期选择逻辑中，calendarSelectedDate 会在用户选择日期后被赋值
            // currentSelectedDateText 也会更新
            // 我们主要依赖 calendarSelectedDate
            if (calendarSelectedDate === null) {
                showToast("请先选择日期", 3000);
        } else {
                // 日期已选择，可以继续执行搜索逻辑
                // 获取当前按钮的文本，了解要搜索什么
                const searchType = dynamicSearchBtn.textContent; // 例如 "搜索玩伴", "搜索组局"

                // 临时提示，实际项目中这里会调用搜索函数
                showToast(`日期已选，开始"${searchType}"...`, 3000);

                // TODO: 在这里根据 searchType 和其他已选条件（如城市、子菜单选项）实现实际的搜索功能
                console.log("Search action for:", searchType,
                            "Selected City:", currentCity, // 全局变量，应已更新
                            "Selected Date:", calendarSelectedDate,
                            "Active Sub-option:", document.querySelector('.sub-options-list.active .sub-option-tab.active')?.textContent.trim());
            }
        });
    }

    // --- Search Overlay Page Logic (NEW) ---
    if (searchInput && searchOverlayPage) {
        searchInput.addEventListener('click', () => { // Using 'click' for simplicity, can be 'focus'
            searchOverlayPage.classList.add('active');
            document.body.style.overflow = 'hidden'; // Prevent body scroll when overlay is active
            // setTimeout to ensure the input is visible before focusing, especially if there are transitions
            setTimeout(() => {
                if(searchOverlayInput) searchOverlayInput.focus();
            }, 50); // Small delay for safety
        });
    }

    function closeSearchOverlay() {
        if (searchOverlayPage) {
            searchOverlayPage.classList.remove('active');
            document.body.style.overflow = ''; // Restore body scroll
        }
    }

    // --- Hot Search Tags Click Events ---
    const hotTags = document.querySelectorAll('.hot-tag');
    hotTags.forEach(tag => {
        tag.addEventListener('click', function() {
            // 获取文本内容，移除可能的图标
            let keyword = this.textContent.trim();

            // 移除FontAwesome图标（如果存在）
            keyword = keyword.replace(/^\s*露营/, '露营').trim();

            console.log('热搜词点击:', keyword); // 调试日志

            // 特殊处理"露营"关键词，跳转到专门的详情页
            if (keyword === '露营') {
                console.log('跳转到露营详情页'); // 调试日志
                window.location.href = '../camping/index.php';
                return;
            }

            // 其他关键词执行搜索
            console.log('执行搜索:', keyword); // 调试日志

            // 尝试使用SearchManager
            if (typeof SearchManager !== 'undefined') {
                const searchManager = new SearchManager();
                searchManager.performSearch(keyword);
            } else {
                // 备用方案：显示提示信息
                showToast(`正在搜索"${keyword}"...`);

                // 可以在这里添加实际的搜索逻辑
                setTimeout(() => {
                    showToast(`"${keyword}"相关功能即将上线，敬请期待！`);
                }, 1000);
            }
        });
    });

    if (searchOverlayBackBtn) {
        searchOverlayBackBtn.addEventListener('click', closeSearchOverlay);
    }

    if (searchOverlayCancelBtn) {
        searchOverlayCancelBtn.addEventListener('click', closeSearchOverlay);
    }

    // --- 子菜单触摸滑动控制 ---
    function initSubMenuTouchControl() {
        const activeSubOptionsLists = document.querySelectorAll('.sub-options-list.active');

        activeSubOptionsLists.forEach(list => {
            if (list) {
                // 禁止垂直滚动，只允许水平滑动
                list.addEventListener('touchmove', function(e) {
                    // 获取触摸点信息
                    if (e.touches.length === 1) {
                        const touch = e.touches[0];

                        // 如果没有记录起始位置，记录它
                        if (!this.touchStartX) {
                            this.touchStartX = touch.clientX;
                            this.touchStartY = touch.clientY;
                            return;
                        }

                        // 计算滑动距离
                        const deltaX = Math.abs(touch.clientX - this.touchStartX);
                        const deltaY = Math.abs(touch.clientY - this.touchStartY);

                        // 如果垂直滑动距离大于水平滑动距离，阻止默认行为
                        if (deltaY > deltaX && deltaY > 10) {
                            e.preventDefault();
                        }
                    }
                }, { passive: false });

                // 记录触摸开始位置
                list.addEventListener('touchstart', function(e) {
                    if (e.touches.length === 1) {
                        this.touchStartX = e.touches[0].clientX;
                        this.touchStartY = e.touches[0].clientY;
                    }
                }, { passive: true });

                // 清除触摸记录
                list.addEventListener('touchend', function(e) {
                    this.touchStartX = null;
                    this.touchStartY = null;
                }, { passive: true });

                // 确保子菜单标签不会换行
                const tabs = list.querySelectorAll('.sub-option-tab');
                tabs.forEach(tab => {
                    tab.style.flexShrink = '0';
                    tab.style.whiteSpace = 'nowrap';
                });
            }
        });
    }

    // 初始化子菜单触摸控制
    initSubMenuTouchControl();

    // 监听功能标签切换，重新初始化触摸控制
    const featureItemsForTouch = document.querySelectorAll('.feature-item');
    featureItemsForTouch.forEach(item => {
        item.addEventListener('click', function() {
            // 延迟执行，确保DOM更新完成
            setTimeout(() => {
                initSubMenuTouchControl();
            }, 100);
        });
    });

}); // End of DOMContentLoaded

// (可选) 提供一个手动触发定位的函数，供测试或用户点击按钮使用
// ... existing code ...

// Ensure extractCityFromAddress function exists and is robust, for example:
function extractCityFromAddress(formattedAddress) {
    if (!formattedAddress || typeof formattedAddress !== 'string') {
        return null;
    }
    // 尝试匹配常见的中国地址格式中的"市"
    // 例如 "广东省深圳市南山区" -> "深圳市"
    // "北京市海淀区" -> "北京市"
    // "上海市浦东新区" -> "上海市"
    // This regular expression is simple and might need to be adjusted based on actual situations
    const cityMatch = formattedAddress.match(/^(北京市|天津市|上海市|重庆市|[^省]+省|[^自治区]+自治区)([^市]+市)/);
    if (cityMatch && cityMatch[2]) {
        return cityMatch[2]; // Return XX市
    }
    // If there's no province, like direct cities or cities starting with "市"
    const directCityMatch = formattedAddress.match(/^([^市]+市)/);
    if (directCityMatch && directCityMatch[1]) {
        return directCityMatch[1];
    }
    // A simpler fallback option: If the address contains "市", take the first part before "市" as the city name, but this is not accurate
    // const cityEndIndex = formattedAddress.indexOf('市');
    // if (cityEndIndex > 0) {
    //    // Need more complex logic to determine the start position of the city
    // }
    console.warn("extractCityFromAddress: Unable to accurately extract city from address:", formattedAddress);
    return null; // If unable to extract, return null
}
