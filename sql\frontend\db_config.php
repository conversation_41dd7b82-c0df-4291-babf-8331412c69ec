<?php
/**
 * 前台数据库配置文件
 * 趣玩星球前台系统
 */

// 数据库配置常量
define('DB_HOST', 'localhost');
define('DB_NAME', 'quwanplanet');
define('DB_USER', 'quwanplanet');
define('DB_PASS', 'nJmJm23FB4Xn6Fc3');
define('DB_PORT', 3306);
define('DB_CHARSET', 'utf8mb4');

/**
 * 获取数据库连接
 * @return PDO
 * @throws Exception
 */
function getDbConnection() {
    static $pdo = null;
    
    if ($pdo === null) {
        try {
            $dsn = sprintf(
                'mysql:host=%s;port=%d;dbname=%s;charset=%s',
                DB_HOST,
                DB_PORT,
                DB_NAME,
                DB_CHARSET
            );
            
            $options = [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                PDO::ATTR_EMULATE_PREPARES => false,
                PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES " . DB_CHARSET
            ];
            
            $pdo = new PDO($dsn, DB_USER, DB_PASS, $options);
            
        } catch (PDOException $e) {
            error_log("数据库连接失败: " . $e->getMessage());
            throw new Exception("数据库连接失败");
        }
    }
    
    return $pdo;
}

/**
 * 执行查询并返回结果
 * @param string $sql SQL语句
 * @param array $params 参数数组
 * @return array
 */
function dbQuery($sql, $params = []) {
    try {
        $pdo = getDbConnection();
        $stmt = $pdo->prepare($sql);
        $stmt->execute($params);
        return $stmt->fetchAll();
    } catch (Exception $e) {
        error_log("数据库查询失败: " . $e->getMessage());
        return [];
    }
}

/**
 * 执行插入/更新/删除操作
 * @param string $sql SQL语句
 * @param array $params 参数数组
 * @return bool|int 成功返回影响行数或插入ID，失败返回false
 */
function dbExecute($sql, $params = []) {
    try {
        $pdo = getDbConnection();
        $stmt = $pdo->prepare($sql);
        $result = $stmt->execute($params);
        
        if (strpos(strtoupper(trim($sql)), 'INSERT') === 0) {
            return $pdo->lastInsertId();
        }
        
        return $stmt->rowCount();
    } catch (Exception $e) {
        error_log("数据库执行失败: " . $e->getMessage());
        return false;
    }
}

/**
 * 获取单行数据
 * @param string $sql SQL语句
 * @param array $params 参数数组
 * @return array|null
 */
function dbFetchOne($sql, $params = []) {
    try {
        $pdo = getDbConnection();
        $stmt = $pdo->prepare($sql);
        $stmt->execute($params);
        return $stmt->fetch();
    } catch (Exception $e) {
        error_log("数据库查询失败: " . $e->getMessage());
        return null;
    }
}

/**
 * 开始事务
 * @return bool
 */
function dbBeginTransaction() {
    try {
        $pdo = getDbConnection();
        return $pdo->beginTransaction();
    } catch (Exception $e) {
        error_log("开始事务失败: " . $e->getMessage());
        return false;
    }
}

/**
 * 提交事务
 * @return bool
 */
function dbCommit() {
    try {
        $pdo = getDbConnection();
        return $pdo->commit();
    } catch (Exception $e) {
        error_log("提交事务失败: " . $e->getMessage());
        return false;
    }
}

/**
 * 回滚事务
 * @return bool
 */
function dbRollback() {
    try {
        $pdo = getDbConnection();
        return $pdo->rollback();
    } catch (Exception $e) {
        error_log("回滚事务失败: " . $e->getMessage());
        return false;
    }
}
?>
