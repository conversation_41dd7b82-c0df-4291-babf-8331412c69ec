<?php
session_start();
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST');
header('Access-Control-Allow-Headers: Content-Type');

// 数据库配置
$host = 'localhost';
$dbname = 'quwanplanet';
$username = 'quwanplanet';
$password = 'nJmJm23FB4Xn6Fc3';

try {
    $pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
} catch(PDOException $e) {
    echo json_encode(['success' => false, 'error' => '数据库连接失败']);
    exit;
}

// 获取POST数据
$input = json_decode(file_get_contents('php://input'), true);

if (!isset($input['phone'])) {
    echo json_encode(['success' => false, 'error' => '缺少手机号参数']);
    exit;
}

$phone = trim($input['phone']);

// 验证手机号格式
if (!preg_match('/^1[3-9]\d{9}$/', $phone)) {
    echo json_encode(['success' => false, 'error' => '手机号格式不正确']);
    exit;
}

try {
    // 检查手机号是否已存在
    $stmt = $pdo->prepare("SELECT COUNT(*) FROM users WHERE phone = ?");
    $stmt->execute([$phone]);
    $count = $stmt->fetchColumn();

    if ($count > 0) {
        echo json_encode(['success' => false, 'error' => '该手机号已注册']);
        exit;
    }

    // 生成7位趣玩ID
    do {
        $quwanId = sprintf('%07d', mt_rand(1000000, 9999999));
        $stmt = $pdo->prepare("SELECT COUNT(*) FROM users WHERE quwan_id = ?");
        $stmt->execute([$quwanId]);
        $idExists = $stmt->fetchColumn() > 0;
    } while ($idExists);

    // 创建用户记录（包含必要字段，其他字段在引导页面填写）
    $stmt = $pdo->prepare("
        INSERT INTO users (phone, quwan_id, username, status, created_at, updated_at)
        VALUES (?, ?, ?, 'incomplete', NOW(), NOW())
    ");

    // 使用手机号作为临时用户名，后续在引导页面可以修改
    $tempUsername = 'user_' . $phone;
    $stmt->execute([$phone, $quwanId, $tempUsername]);
    $userId = $pdo->lastInsertId();

    // 设置登录会话
    $_SESSION['user_id'] = $userId;
    $_SESSION['phone'] = $phone;
    $_SESSION['quwan_id'] = $quwanId;
    $_SESSION['logged_in'] = true;
    $_SESSION['registration_incomplete'] = true; // 标记注册未完成

    echo json_encode([
        'success' => true,
        'user_id' => $userId,
        'quwan_id' => $quwanId,
        'message' => '账户创建成功'
    ]);

} catch(PDOException $e) {
    echo json_encode(['success' => false, 'error' => '创建用户失败: ' . $e->getMessage()]);
}
?>
