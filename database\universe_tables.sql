-- 创建趣玩宇宙内容表
CREATE TABLE IF NOT EXISTS `universe_posts` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL COMMENT '发布用户ID',
  `title` varchar(255) NOT NULL COMMENT '标题',
  `content` text NOT NULL COMMENT '内容（富文本）',
  `category_id` int(11) NOT NULL COMMENT '主分类ID',
  `subcategory_id` int(11) NOT NULL COMMENT '子分类ID',
  `cover_image` varchar(255) DEFAULT NULL COMMENT '封面图片URL',
  `has_images` tinyint(1) DEFAULT 0 COMMENT '是否包含图片',
  `has_video` tinyint(1) DEFAULT 0 COMMENT '是否包含视频',
  `video_url` varchar(255) DEFAULT NULL COMMENT '视频URL',
  `has_audio` tinyint(1) DEFAULT 0 COMMENT '是否包含音频',
  `audio_url` varchar(255) DEFAULT NULL COMMENT '音频URL',
  `views` int(11) DEFAULT 0 COMMENT '浏览次数',
  `likes` int(11) DEFAULT 0 COMMENT '点赞数',
  `comments` int(11) DEFAULT 0 COMMENT '评论数',
  `status` tinyint(1) DEFAULT 1 COMMENT '状态：0-草稿，1-已发布，2-已删除',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `user_id` (`user_id`),
  KEY `category_id` (`category_id`),
  KEY `subcategory_id` (`subcategory_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='趣玩宇宙内容表';

-- 创建趣玩宇宙分类表
CREATE TABLE IF NOT EXISTS `universe_categories` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(50) NOT NULL COMMENT '分类名称',
  `icon` varchar(255) DEFAULT NULL COMMENT '分类图标',
  `sort_order` int(11) DEFAULT 0 COMMENT '排序',
  `status` tinyint(1) DEFAULT 1 COMMENT '状态：0-禁用，1-启用',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='趣玩宇宙分类表';

-- 创建趣玩宇宙子分类表
CREATE TABLE IF NOT EXISTS `universe_subcategories` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `category_id` int(11) NOT NULL COMMENT '所属主分类ID',
  `name` varchar(50) NOT NULL COMMENT '子分类名称',
  `sort_order` int(11) DEFAULT 0 COMMENT '排序',
  `status` tinyint(1) DEFAULT 1 COMMENT '状态：0-禁用，1-启用',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `category_id` (`category_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='趣玩宇宙子分类表';

-- 创建趣玩宇宙评论表
CREATE TABLE IF NOT EXISTS `universe_comments` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `post_id` int(11) NOT NULL COMMENT '内容ID',
  `user_id` int(11) NOT NULL COMMENT '评论用户ID',
  `content` text NOT NULL COMMENT '评论内容',
  `parent_id` int(11) DEFAULT 0 COMMENT '父评论ID，0表示一级评论',
  `status` tinyint(1) DEFAULT 1 COMMENT '状态：0-已删除，1-正常',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `post_id` (`post_id`),
  KEY `user_id` (`user_id`),
  KEY `parent_id` (`parent_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='趣玩宇宙评论表';

-- 创建趣玩宇宙点赞表
CREATE TABLE IF NOT EXISTS `universe_likes` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `post_id` int(11) NOT NULL COMMENT '内容ID',
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `post_user` (`post_id`,`user_id`),
  KEY `post_id` (`post_id`),
  KEY `user_id` (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='趣玩宇宙点赞表';

-- 创建趣玩宇宙收藏表
CREATE TABLE IF NOT EXISTS `universe_favorites` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `post_id` int(11) NOT NULL COMMENT '内容ID',
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `post_user` (`post_id`,`user_id`),
  KEY `post_id` (`post_id`),
  KEY `user_id` (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='趣玩宇宙收藏表';

-- 创建趣玩宇宙媒体表（存储图片、视频、音频等媒体信息）
CREATE TABLE IF NOT EXISTS `universe_media` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `post_id` int(11) NOT NULL COMMENT '内容ID',
  `type` enum('image','video','audio') NOT NULL COMMENT '媒体类型',
  `url` varchar(255) NOT NULL COMMENT '媒体URL',
  `cloudinary_id` varchar(255) DEFAULT NULL COMMENT 'Cloudinary资源ID',
  `sort_order` int(11) DEFAULT 0 COMMENT '排序',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `post_id` (`post_id`),
  KEY `type` (`type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='趣玩宇宙媒体表';

-- 插入默认分类数据
INSERT INTO `universe_categories` (`name`, `icon`, `sort_order`) VALUES
('旅游', 'fa-plane', 1),
('交友', 'fa-user-friends', 2),
('运动', 'fa-running', 3),
('游戏', 'fa-gamepad', 4),
('娱乐', 'fa-film', 5),
('学习', 'fa-book', 6),
('宠物', 'fa-paw', 7),
('演唱会', 'fa-music', 8);

-- 插入默认子分类数据
INSERT INTO `universe_subcategories` (`category_id`, `name`, `sort_order`) VALUES
(1, '国内游', 1),
(1, '长途游', 2),
(1, '周边游', 3),
(1, '出境游', 4),
(1, '签证办理', 5),
(1, '自驾游', 6),
(1, '跟团游', 7),
(1, '民宿推荐', 8),
(1, '景点门票', 9),
(2, '兴趣交友', 1),
(2, '同城约会', 2),
(2, '户外聚会', 3),
(2, '社交技巧', 4),
(2, '相亲活动', 5),
(2, '主题派对', 6),
(3, '健身房', 1),
(3, '瑜伽', 2),
(3, '跑步', 3),
(3, '游泳', 4),
(3, '篮球', 5),
(3, '足球', 6),
(3, '羽毛球', 7),
(3, '网球', 8),
(3, '攀岩', 9),
(4, '手游', 1),
(4, '端游', 2),
(4, '主机游戏', 3),
(4, '桌游', 4),
(4, '密室逃脱', 5),
(4, '剧本杀', 6),
(5, '电影', 1),
(5, '电视剧', 2),
(5, '综艺', 3),
(5, 'KTV', 4),
(5, '酒吧', 5),
(5, '展览', 6),
(5, '音乐节', 7),
(5, '戏剧', 8),
(5, '脱口秀', 9),
(6, '语言学习', 1),
(6, '职业技能', 2),
(6, '兴趣培养', 3),
(6, '读书会', 4),
(6, '讲座沙龙', 5),
(6, '考证培训', 6),
(7, '猫咪', 1),
(7, '狗狗', 2),
(7, '小宠', 3),
(7, '宠物医院', 4),
(7, '宠物美容', 5),
(7, '宠物寄养', 6),
(8, '流行音乐', 1),
(8, '摇滚', 2),
(8, '民谣', 3),
(8, '嘻哈', 4),
(8, '电子音乐', 5),
(8, '古典音乐', 6);
