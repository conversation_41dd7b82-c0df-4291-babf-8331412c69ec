<?php
// 引入session配置
require_once '../../sql/session_config.php';
initLoginSession();

// 检查用户是否已登录
$is_logged_in = isUserLoggedIn();

if (!$is_logged_in) {
    header('Location: ../login/index.php');
    exit;
}

// 数据库连接配置
$db_config = [
    'host' => 'localhost',
    'username' => 'quwanplanet',
    'password' => 'nJmJm23FB4Xn6Fc3',
    'dbname' => 'quwanplanet',
    'port' => 3306,
    'charset' => 'utf8mb4'
];

// 获取用户信息
$user_avatar_display_url = 'https://s1.imagehub.cc/images/2025/04/26/f5c2f26c4564aabcbb56c5fd30f1d652.jpg';
$user_name = '用户';

try {
    $dsn = "mysql:host={$db_config['host']};dbname={$db_config['dbname']};charset={$db_config['charset']};port={$db_config['port']}";
    $pdo = new PDO($dsn, $db_config['username'], $db_config['password']);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

    $stmt = $pdo->prepare("SELECT avatar, nickname FROM users WHERE id = :user_id");
    $stmt->execute(['user_id' => $_SESSION['user_id']]);
    $user_data = $stmt->fetch(PDO::FETCH_ASSOC);

    if ($user_data) {
        if (!empty($user_data['avatar'])) {
            $user_avatar_display_url = $user_data['avatar'];
        }
        if (!empty($user_data['nickname'])) {
            $user_name = $user_data['nickname'];
        }
    }
} catch (PDOException $e) {
    error_log("获取用户信息错误: " . $e->getMessage());
}
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <meta name="theme-color" content="#6F7BF5">
    <title>发起露营活动 - 趣玩星球</title>
    <link rel="stylesheet" href="css/create-activity.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <?php
    include '../includes/bottom_nav.php';
    echo renderBottomNavCSS();
    ?>
</head>
<body>
    <!-- 页面头部 -->
    <header class="create-header">
        <div class="header-content">
            <button class="back-btn" onclick="history.back()">
                <i class="fas fa-arrow-left"></i>
            </button>
            <h1 class="page-title">发起露营活动</h1>
            <div class="header-placeholder"></div>
        </div>
    </header>

    <!-- 主要内容区域 -->
    <main class="create-main">
        <form id="activityForm" class="activity-form">
            <!-- 基本信息 -->
            <div class="form-section">
                <div class="section-header">
                    <i class="fas fa-info-circle"></i>
                    <h2>基本信息</h2>
                </div>
                
                <div class="form-group">
                    <label for="title">活动标题 *</label>
                    <input type="text" id="title" name="title" placeholder="给你的露营活动起个吸引人的名字" required>
                </div>
                
                <div class="form-group">
                    <label for="description">活动描述 *</label>
                    <textarea id="description" name="description" placeholder="详细描述你的露营活动，包括活动亮点、注意事项等" rows="4" required></textarea>
                </div>
                
                <div class="form-group">
                    <label for="category">活动类型 *</label>
                    <select id="category" name="category" required>
                        <option value="">请选择活动类型</option>
                        <option value="mountain">山地露营</option>
                        <option value="lake">湖边露营</option>
                        <option value="forest">森林露营</option>
                        <option value="beach">海边露营</option>
                    </select>
                </div>
            </div>

            <!-- 时间地点 -->
            <div class="form-section">
                <div class="section-header">
                    <i class="fas fa-map-marker-alt"></i>
                    <h2>时间地点</h2>
                </div>
                
                <div class="form-group">
                    <label for="location">活动地点 *</label>
                    <input type="text" id="location" name="location" placeholder="详细的活动地点，如：北京·怀柔山谷" required>
                </div>
                
                <div class="form-row">
                    <div class="form-group">
                        <label for="start_date">开始时间 *</label>
                        <input type="datetime-local" id="start_date" name="start_date" required>
                    </div>
                    <div class="form-group">
                        <label for="end_date">结束时间 *</label>
                        <input type="datetime-local" id="end_date" name="end_date" required>
                    </div>
                </div>
            </div>

            <!-- 参与设置 -->
            <div class="form-section">
                <div class="section-header">
                    <i class="fas fa-users"></i>
                    <h2>参与设置</h2>
                </div>
                
                <div class="form-row">
                    <div class="form-group">
                        <label for="max_participants">最大参与人数 *</label>
                        <input type="number" id="max_participants" name="max_participants" min="2" max="50" value="10" required>
                    </div>
                    <div class="form-group">
                        <label for="price">活动费用 (元) *</label>
                        <input type="number" id="price" name="price" min="0" step="0.01" placeholder="0.00" required>
                    </div>
                </div>
                
                <div class="form-group">
                    <label for="features">活动特色</label>
                    <div class="features-container">
                        <div class="feature-tags">
                            <label class="feature-tag">
                                <input type="checkbox" name="features[]" value="篝火晚会">
                                <span>篝火晚会</span>
                            </label>
                            <label class="feature-tag">
                                <input type="checkbox" name="features[]" value="观星">
                                <span>观星</span>
                            </label>
                            <label class="feature-tag">
                                <input type="checkbox" name="features[]" value="烧烤">
                                <span>烧烤</span>
                            </label>
                            <label class="feature-tag">
                                <input type="checkbox" name="features[]" value="徒步">
                                <span>徒步</span>
                            </label>
                            <label class="feature-tag">
                                <input type="checkbox" name="features[]" value="摄影">
                                <span>摄影</span>
                            </label>
                            <label class="feature-tag">
                                <input type="checkbox" name="features[]" value="钓鱼">
                                <span>钓鱼</span>
                            </label>
                            <label class="feature-tag">
                                <input type="checkbox" name="features[]" value="划船">
                                <span>划船</span>
                            </label>
                            <label class="feature-tag">
                                <input type="checkbox" name="features[]" value="日出">
                                <span>日出</span>
                            </label>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 活动图片 -->
            <div class="form-section">
                <div class="section-header">
                    <i class="fas fa-image"></i>
                    <h2>活动图片</h2>
                </div>
                
                <div class="form-group">
                    <label for="image_url">活动主图链接</label>
                    <input type="url" id="image_url" name="image_url" placeholder="https://example.com/image.jpg">
                    <div class="form-help">
                        <i class="fas fa-info-circle"></i>
                        建议使用高质量的露营场景图片，尺寸建议 400x200 像素
                    </div>
                </div>
            </div>

            <!-- 组局者信息 -->
            <div class="form-section">
                <div class="section-header">
                    <i class="fas fa-user-crown"></i>
                    <h2>组局者信息</h2>
                </div>
                
                <div class="organizer-info">
                    <div class="organizer-avatar">
                        <img src="<?php echo htmlspecialchars($user_avatar_display_url); ?>" alt="组局者头像">
                    </div>
                    <div class="organizer-details">
                        <div class="organizer-name"><?php echo htmlspecialchars($user_name); ?></div>
                        <div class="organizer-level">LV.1 新手组局者</div>
                        <div class="organizer-desc">作为组局者，您需要负责活动的组织和协调工作</div>
                    </div>
                </div>
            </div>

            <!-- 提交按钮 -->
            <div class="form-actions">
                <button type="button" class="btn btn-secondary" onclick="saveDraft()">
                    <i class="fas fa-save"></i> 保存草稿
                </button>
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-rocket"></i> 发布活动
                </button>
            </div>
        </form>
    </main>

    <!-- 加载动画 -->
    <div class="loading-overlay" id="loading-overlay">
        <div class="four-star-loader">
            <div class="star-point"></div>
            <div class="star-point"></div>
            <div class="star-point"></div>
            <div class="star-point"></div>
        </div>
        <div class="loading-text">正在发布活动...</div>
    </div>

    <!-- Toast提示 -->
    <div class="toast" id="toast"></div>

    <?php echo renderBottomNav('home', $is_logged_in); ?>

    <script>
        // 传递用户信息到JavaScript
        window.userInfo = {
            id: <?php echo $_SESSION['user_id']; ?>,
            name: '<?php echo htmlspecialchars($user_name); ?>',
            avatar: '<?php echo htmlspecialchars($user_avatar_display_url); ?>'
        };
    </script>
    <script src="js/create-activity.js"></script>
    <?php echo renderBottomNavJS($is_logged_in); ?>
</body>
</html>
