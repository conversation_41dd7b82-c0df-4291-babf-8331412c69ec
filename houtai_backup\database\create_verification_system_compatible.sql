-- 创建验证码系统相关表和字段（兼容版本）
-- 用于前后台联动发送验证码功能
-- 兼容MySQL 5.6+版本

-- 1. 创建验证码记录表
CREATE TABLE IF NOT EXISTS `verification_codes` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `phone` varchar(20) NOT NULL COMMENT '手机号',
  `code` varchar(10) NOT NULL COMMENT '验证码',
  `type` enum('admin_send','login','register','reset_password','security_verify') NOT NULL DEFAULT 'admin_send' COMMENT '验证码类型',
  `status` enum('pending','used','expired') NOT NULL DEFAULT 'pending' COMMENT '状态',
  `sent_by_admin` int(11) DEFAULT NULL COMMENT '发送的管理员ID',
  `admin_note` varchar(255) DEFAULT NULL COMMENT '管理员备注',
  `expires_at` datetime NOT NULL COMMENT '过期时间',
  `used_at` datetime DEFAULT NULL COMMENT '使用时间',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_phone` (`phone`),
  KEY `idx_type_status` (`type`, `status`),
  KEY `idx_expires_at` (`expires_at`),
  KEY `idx_sent_by_admin` (`sent_by_admin`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='验证码记录表';

-- 2. 创建实时通知表
CREATE TABLE IF NOT EXISTS `realtime_notifications` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL COMMENT '目标用户ID',
  `type` enum('verification_code','system_message','admin_notice') NOT NULL DEFAULT 'verification_code' COMMENT '通知类型',
  `title` varchar(100) NOT NULL COMMENT '通知标题',
  `content` text NOT NULL COMMENT '通知内容',
  `data` text DEFAULT NULL COMMENT '附加数据JSON格式',
  `status` enum('pending','delivered','read') NOT NULL DEFAULT 'pending' COMMENT '状态',
  `priority` tinyint(1) NOT NULL DEFAULT 1 COMMENT '优先级 1-5',
  `expires_at` datetime DEFAULT NULL COMMENT '过期时间',
  `delivered_at` datetime DEFAULT NULL COMMENT '送达时间',
  `read_at` datetime DEFAULT NULL COMMENT '阅读时间',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_user_id_status` (`user_id`, `status`),
  KEY `idx_type` (`type`),
  KEY `idx_expires_at` (`expires_at`),
  KEY `idx_priority` (`priority`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='实时通知表';

-- 3. 创建管理员操作日志表
CREATE TABLE IF NOT EXISTS `admin_operation_logs` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `admin_id` int(11) NOT NULL COMMENT '管理员ID',
  `operation_type` varchar(50) NOT NULL COMMENT '操作类型',
  `target_type` varchar(50) DEFAULT NULL COMMENT '目标类型',
  `target_id` int(11) DEFAULT NULL COMMENT '目标ID',
  `description` varchar(255) NOT NULL COMMENT '操作描述',
  `details` text DEFAULT NULL COMMENT '操作详情JSON格式',
  `ip_address` varchar(45) DEFAULT NULL COMMENT 'IP地址',
  `user_agent` text DEFAULT NULL COMMENT '用户代理',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_admin_id` (`admin_id`),
  KEY `idx_operation_type` (`operation_type`),
  KEY `idx_target` (`target_type`, `target_id`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='管理员操作日志表';

-- 4. 检查users表是否存在，如果不存在则创建基础表
CREATE TABLE IF NOT EXISTS `users` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `username` varchar(50) NOT NULL COMMENT '用户名',
  `phone` varchar(20) NOT NULL COMMENT '手机号',
  `password` varchar(255) DEFAULT NULL COMMENT '密码',
  `avatar` varchar(255) DEFAULT NULL COMMENT '头像',
  `nickname` varchar(50) DEFAULT NULL COMMENT '昵称',
  `status` enum('active','banned','deleted') NOT NULL DEFAULT 'active' COMMENT '状态',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_phone` (`phone`),
  KEY `idx_username` (`username`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户表';

-- 5. 为users表添加新字段（如果不存在）
-- 检查字段是否存在的安全方法
SET @sql = (SELECT IF(
  (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
   WHERE table_name = 'users' 
   AND table_schema = DATABASE() 
   AND column_name = 'last_notification_check') > 0,
  'SELECT "last_notification_check字段已存在" as message',
  'ALTER TABLE `users` ADD COLUMN `last_notification_check` datetime DEFAULT NULL COMMENT "最后检查通知时间"'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SET @sql = (SELECT IF(
  (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
   WHERE table_name = 'users' 
   AND table_schema = DATABASE() 
   AND column_name = 'notification_token') > 0,
  'SELECT "notification_token字段已存在" as message',
  'ALTER TABLE `users` ADD COLUMN `notification_token` varchar(64) DEFAULT NULL COMMENT "通知令牌"'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SET @sql = (SELECT IF(
  (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
   WHERE table_name = 'users' 
   AND table_schema = DATABASE() 
   AND column_name = 'online_status') > 0,
  'SELECT "online_status字段已存在" as message',
  'ALTER TABLE `users` ADD COLUMN `online_status` enum("online","offline","away") DEFAULT "offline" COMMENT "在线状态"'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SET @sql = (SELECT IF(
  (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
   WHERE table_name = 'users' 
   AND table_schema = DATABASE() 
   AND column_name = 'last_activity') > 0,
  'SELECT "last_activity字段已存在" as message',
  'ALTER TABLE `users` ADD COLUMN `last_activity` datetime DEFAULT NULL COMMENT "最后活动时间"'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 6. 插入一些测试数据
INSERT IGNORE INTO `verification_codes` (`user_id`, `phone`, `code`, `type`, `sent_by_admin`, `admin_note`, `expires_at`) VALUES
(1, '13800138000', '123456', 'admin_send', 1, '测试发送验证码', DATE_ADD(NOW(), INTERVAL 5 MINUTE)),
(2, '13900139000', '654321', 'admin_send', 1, '安全验证', DATE_ADD(NOW(), INTERVAL 5 MINUTE));

-- 7. 创建索引优化查询性能
-- 检查索引是否存在再创建
SET @sql = (SELECT IF(
  (SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS 
   WHERE table_name = 'realtime_notifications' 
   AND table_schema = DATABASE() 
   AND index_name = 'idx_user_notification_status') > 0,
  'SELECT "idx_user_notification_status索引已存在" as message',
  'CREATE INDEX `idx_user_notification_status` ON `realtime_notifications` (`user_id`, `status`, `created_at`)'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SET @sql = (SELECT IF(
  (SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS 
   WHERE table_name = 'verification_codes' 
   AND table_schema = DATABASE() 
   AND index_name = 'idx_verification_user_type') > 0,
  'SELECT "idx_verification_user_type索引已存在" as message',
  'CREATE INDEX `idx_verification_user_type` ON `verification_codes` (`user_id`, `type`, `status`)'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 8. 显示创建结果
SELECT 'verification_codes表创建完成！' as message;
SELECT COUNT(*) as verification_records FROM `verification_codes`;

SELECT 'realtime_notifications表创建完成！' as message;
SELECT COUNT(*) as notification_records FROM `realtime_notifications`;

SELECT 'admin_operation_logs表创建完成！' as message;
SELECT COUNT(*) as operation_records FROM `admin_operation_logs`;

-- 9. 检查users表结构
SELECT 'users表字段检查：' as message;
SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE, COLUMN_DEFAULT, COLUMN_COMMENT 
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE table_name = 'users' 
AND table_schema = DATABASE() 
AND COLUMN_NAME IN ('last_notification_check', 'notification_token', 'online_status', 'last_activity')
ORDER BY ORDINAL_POSITION;

-- 显示完成信息
SELECT '✅ 验证码系统数据库结构创建完成！' as status;
SELECT '包含验证码记录、实时通知、操作日志等功能' as description;
SELECT '所有表和字段已成功创建，可以开始使用前后台联动功能' as next_step;
