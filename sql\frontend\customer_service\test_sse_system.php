<?php
// SSE实时通信系统测试页面
header('Content-Type: text/html; charset=UTF-8');
ini_set('default_charset', 'UTF-8');

session_start();

// 模拟用户登录（用于测试）
if (!isset($_SESSION['user_id'])) {
    $_SESSION['user_id'] = 4;
    $_SESSION['user_name'] = '测试用户';
    $_SESSION['nickname'] = '测试用户';
}

require_once '../../houtai_backup/db_config.php';

echo '<h1>🚀 SSE实时通信系统测试</h1>';

try {
    $pdo = getDbConnection();
    
    // 获取或创建测试会话
    $testSessionId = 'sse_test_' . time();
    
    // 创建测试会话
    $stmt = $pdo->prepare("
        INSERT INTO customer_service_sessions
        (session_id, user_id, user_name, status, priority, source, started_at)
        VALUES (?, ?, ?, 'waiting', 'normal', 'web', NOW())
    ");
    $stmt->execute([
        $testSessionId,
        $_SESSION['user_id'],
        $_SESSION['user_name']
    ]);
    
    echo '<div style="background: #d4edda; padding: 15px; border-radius: 8px; margin: 10px 0;">';
    echo '<h3>✅ 测试会话创建成功</h3>';
    echo '<p><strong>会话ID:</strong> ' . htmlspecialchars($testSessionId) . '</p>';
    echo '<p><strong>用户ID:</strong> ' . $_SESSION['user_id'] . '</p>';
    echo '<p><strong>用户名:</strong> ' . $_SESSION['user_name'] . '</p>';
    echo '</div>';
    
    // 获取现有的活跃会话
    $stmt = $pdo->prepare("
        SELECT session_id, status, customer_service_id, started_at, message_count
        FROM customer_service_sessions 
        WHERE user_id = ? 
        ORDER BY started_at DESC 
        LIMIT 5
    ");
    $stmt->execute([$_SESSION['user_id']]);
    $sessions = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (!empty($sessions)) {
        echo '<h2>📋 您的客服会话</h2>';
        
        foreach ($sessions as $session) {
            $statusColor = $session['status'] === 'active' ? 'green' : ($session['status'] === 'waiting' ? 'orange' : 'gray');
            
            echo '<div style="border: 1px solid #ddd; padding: 15px; margin: 10px 0; border-radius: 8px; background: #f9f9f9;">';
            echo '<h4>会话: ' . htmlspecialchars($session['session_id']) . '</h4>';
            echo '<p><strong>状态:</strong> <span style="color: ' . $statusColor . ';">' . htmlspecialchars($session['status']) . '</span></p>';
            echo '<p><strong>客服ID:</strong> ' . htmlspecialchars($session['customer_service_id'] ?? '未分配') . '</p>';
            echo '<p><strong>消息数量:</strong> ' . htmlspecialchars($session['message_count']) . '</p>';
            echo '<p><strong>开始时间:</strong> ' . htmlspecialchars($session['started_at']) . '</p>';
            
            echo '<div style="margin-top: 15px;">';
            echo '<a href="chat_realtime.php?session_id=' . urlencode($session['session_id']) . '" style="background: #6F7BF5; color: white; padding: 8px 16px; text-decoration: none; border-radius: 5px; margin-right: 10px;">🚀 打开SSE聊天</a>';
            echo '<a href="chat.php?session_id=' . urlencode($session['session_id']) . '" style="background: #28a745; color: white; padding: 8px 16px; text-decoration: none; border-radius: 5px; margin-right: 10px;">💬 打开普通聊天</a>';
            echo '<a href="../../houtai_backup/customer_service_system/quick_message_test.php" target="_blank" style="background: #dc3545; color: white; padding: 8px 16px; text-decoration: none; border-radius: 5px;">📤 客服发送消息</a>';
            echo '</div>';
            
            echo '</div>';
        }
    }
    
} catch (Exception $e) {
    echo '<div style="background: #f8d7da; padding: 15px; border-radius: 8px; margin: 10px 0; color: #721c24;">';
    echo '<h3>❌ 错误</h3>';
    echo '<p>' . htmlspecialchars($e->getMessage()) . '</p>';
    echo '</div>';
}
?>

<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>SSE实时通信测试</title>
    <style>
        body { 
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; 
            margin: 20px; 
            background: #f5f5f5; 
        }
        .container { 
            max-width: 900px; 
            margin: 0 auto; 
            background: white; 
            padding: 20px; 
            border-radius: 10px; 
            box-shadow: 0 2px 10px rgba(0,0,0,0.1); 
        }
        .feature-box {
            background: #e7f3ff;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
            border-left: 4px solid #6F7BF5;
        }
        .step-box {
            background: #fff3cd;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
            border-left: 4px solid #ffc107;
        }
        .comparison {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin: 20px 0;
        }
        .comparison-item {
            padding: 15px;
            border-radius: 8px;
            border: 1px solid #ddd;
        }
        .old-system {
            background: #f8d7da;
            border-color: #dc3545;
        }
        .new-system {
            background: #d4edda;
            border-color: #28a745;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="feature-box">
            <h3>🚀 SSE实时通信系统特点</h3>
            <ul>
                <li><strong>真正的实时通信</strong>：使用Server-Sent Events技术</li>
                <li><strong>自动重连</strong>：连接断开时自动重新连接</li>
                <li><strong>低延迟</strong>：消息推送延迟小于1秒</li>
                <li><strong>状态监控</strong>：实时显示连接状态</li>
                <li><strong>心跳检测</strong>：确保连接稳定性</li>
                <li><strong>错误处理</strong>：完善的错误处理和用户提示</li>
            </ul>
        </div>
        
        <div class="comparison">
            <div class="comparison-item old-system">
                <h4>❌ 旧系统（轮询）</h4>
                <ul>
                    <li>每3秒请求一次</li>
                    <li>延迟高（最多3秒）</li>
                    <li>服务器压力大</li>
                    <li>可能丢失消息</li>
                    <li>不稳定</li>
                </ul>
            </div>
            <div class="comparison-item new-system">
                <h4>✅ 新系统（SSE）</h4>
                <ul>
                    <li>实时推送</li>
                    <li>延迟极低（<1秒）</li>
                    <li>服务器压力小</li>
                    <li>不会丢失消息</li>
                    <li>连接稳定</li>
                </ul>
            </div>
        </div>
        
        <div class="step-box">
            <h3>🧪 测试步骤</h3>
            <ol>
                <li><strong>打开SSE聊天页面</strong>：点击上面的"打开SSE聊天"按钮</li>
                <li><strong>观察连接状态</strong>：页面右上角应显示"已连接"</li>
                <li><strong>打开客服后台</strong>：在新标签页打开"客服发送消息"</li>
                <li><strong>发送测试消息</strong>：在客服后台发送消息给用户</li>
                <li><strong>验证实时接收</strong>：前台应立即收到消息（无需刷新）</li>
                <li><strong>测试双向通信</strong>：在前台发送消息，客服后台也应能看到</li>
            </ol>
        </div>
        
        <div class="feature-box">
            <h3>🔧 技术实现</h3>
            <ul>
                <li><strong>前端</strong>：EventSource API 建立SSE连接</li>
                <li><strong>后端</strong>：PHP 持续监听数据库变化</li>
                <li><strong>数据库</strong>：实时查询新消息和状态变化</li>
                <li><strong>通信协议</strong>：text/event-stream 格式</li>
                <li><strong>重连机制</strong>：连接断开后5秒自动重连</li>
            </ul>
        </div>
        
        <div style="background: #d1ecf1; padding: 15px; border-radius: 8px; margin: 20px 0;">
            <h3>💡 使用建议</h3>
            <p>如果SSE系统工作正常，建议：</p>
            <ul>
                <li>将所有前台聊天页面切换到SSE版本</li>
                <li>移除旧的轮询代码</li>
                <li>在生产环境中启用SSE</li>
                <li>监控服务器性能和连接数</li>
            </ul>
        </div>
        
        <p style="margin-top: 30px;">
            <a href="index.php">返回客服首页</a> | 
            <a href="../../houtai_backup/customer_service_system/sessions.php">客服后台</a> | 
            <a href="test_complete_flow.php">完整流程测试</a>
        </p>
    </div>
</body>
</html>
