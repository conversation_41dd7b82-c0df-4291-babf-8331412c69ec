<?php
/**
 * 实名认证详情页面
 * 趣玩星球管理后台
 */

session_start();

// 检查登录状态
if (!isset($_SESSION['admin_id'])) {
    header('Location: ../login.php');
    exit;
}

$admin_name = $_SESSION['admin_name'] ?? '管理员';
$admin_id = $_SESSION['admin_id'] ?? '';
$admin_employee_id = $_SESSION['admin_employee_id'] ?? '';

// 引用上级目录的数据库配置文件
require_once '../db_config.php';

$id = intval($_GET['id'] ?? 0);
if (!$id) {
    header('Location: index.php');
    exit;
}

try {
    $pdo = getDbConnection();

    // 获取认证详情
    $stmt = $pdo->prepare("
        SELECT
            rv.*,
            u.username,
            u.quwan_id,
            u.phone,
            u.email,
            u.created_at as user_created_at,
            au.name as admin_name,
            au.employee_id as admin_employee_id
        FROM realname_verification rv
        LEFT JOIN users u ON rv.user_id = u.id
        LEFT JOIN admin_users au ON rv.verified_by = au.id
        WHERE rv.id = ?
    ");
    $stmt->execute([$id]);
    $verification = $stmt->fetch(PDO::FETCH_ASSOC);

    if (!$verification) {
        header('Location: verification_list.php');
        exit;
    }

} catch (PDOException $e) {
    $error_message = '数据库错误：' . $e->getMessage();
}
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>实名认证详情 - <?php echo htmlspecialchars($verification['real_name']); ?> - 趣玩星球管理后台</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="../assets/css/admin-layout.css">
    <style>
        /* 实名认证详情页面专用样式 */
        .verification-detail-content {
            padding: 24px;
            background: var(--gray-50);
            min-height: calc(100vh - 60px);
        }

        .detail-header {
            margin-bottom: 32px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: 16px;
        }

        .detail-title {
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .detail-title h1 {
            font-size: 2rem;
            font-weight: 700;
            color: var(--gray-800);
            margin: 0;
        }

        .detail-title .icon {
            width: 48px;
            height: 48px;
            background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 20px;
        }

        .back-btn {
            background: linear-gradient(135deg, var(--gray-600), var(--gray-700));
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 12px;
            text-decoration: none;
            font-size: 0.875rem;
            font-weight: 600;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            transition: all 0.3s ease;
        }

        .back-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
        }

        .detail-container {
            max-width: 1000px;
            margin: 0 auto;
            display: block;
        }

        .detail-card {
            background: white;
            border-radius: 20px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
            overflow: hidden;
            margin-bottom: 24px;
            border: 2px solid transparent;
            transition: all 0.3s ease;
        }

        .detail-card:hover {
            border-color: var(--primary-color);
            box-shadow: 0 12px 40px rgba(0, 0, 0, 0.12);
            transform: translateY(-4px);
        }

        .card-header {
            background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
            padding: 24px 28px;
            color: white;
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: 16px;
        }

        .card-header h2 {
            font-size: 1.5rem;
            font-weight: 700;
            margin: 0;
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .card-body {
            padding: 32px 28px;
        }

        .info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 24px;
        }

        .info-item {
            background: var(--gray-50);
            padding: 20px;
            border-radius: 16px;
            border: 2px solid transparent;
            transition: all 0.3s ease;
        }

        .info-item:hover {
            border-color: var(--primary-color);
            background: white;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(64, 224, 208, 0.1);
        }

        .info-label {
            font-weight: 600;
            color: var(--gray-600);
            font-size: 0.875rem;
            margin-bottom: 8px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .info-value {
            font-size: 1.125rem;
            color: var(--gray-800);
            font-weight: 600;
        }

        .id-card-number {
            font-family: 'Courier New', monospace;
            font-size: 1.25rem;
            letter-spacing: 2px;
            background: var(--gray-100);
            padding: 12px 16px;
            border-radius: 8px;
            border: 1px solid var(--gray-200);
        }

        .quwanplanet-id {
            color: var(--primary-color);
            font-weight: 700;
            font-size: 1.25rem;
        }

        /* 状态徽章 */
        .status-badge {
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 0.875rem;
            font-weight: 600;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .status-pending {
            background: linear-gradient(135deg, #FEF3C7, #FDE68A);
            color: #92400E;
            border: 1px solid #F59E0B;
        }

        .status-approved {
            background: linear-gradient(135deg, #D1FAE5, #A7F3D0);
            color: #065F46;
            border: 1px solid #10B981;
        }

        .status-rejected {
            background: linear-gradient(135deg, #FEE2E2, #FECACA);
            color: #991B1B;
            border: 1px solid #EF4444;
        }

        /* 操作按钮 */
        .actions {
            display: flex;
            gap: 16px;
            margin-top: 32px;
            flex-wrap: wrap;
        }

        .action-btn {
            padding: 14px 28px;
            border: none;
            border-radius: 12px;
            cursor: pointer;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 10px;
            font-size: 1rem;
            font-weight: 600;
            transition: all 0.3s ease;
            min-width: 140px;
            justify-content: center;
        }

        .btn-approve {
            background: linear-gradient(135deg, #10B981, #34D399);
            color: white;
        }

        .btn-approve:hover {
            transform: translateY(-3px);
            box-shadow: 0 12px 25px rgba(16, 185, 129, 0.3);
        }

        .btn-reject {
            background: linear-gradient(135deg, #EF4444, #F87171);
            color: white;
        }

        .btn-reject:hover {
            transform: translateY(-3px);
            box-shadow: 0 12px 25px rgba(239, 68, 68, 0.3);
        }

        /* 拒绝原因区域 */
        .reason-section {
            background: linear-gradient(135deg, #FEF2F2, #FEE2E2);
            border: 2px solid #FECACA;
            border-radius: 16px;
            padding: 24px;
            margin-top: 24px;
        }

        .reason-section h4 {
            color: #DC2626;
            margin-bottom: 12px;
            font-size: 1.125rem;
            font-weight: 700;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .reason-text {
            color: #7F1D1D;
            font-size: 1rem;
            line-height: 1.6;
            background: white;
            padding: 16px;
            border-radius: 12px;
            border: 1px solid #FCA5A5;
        }

        /* 时间线样式 */
        .timeline {
            position: relative;
            padding-left: 32px;
        }

        .timeline::before {
            content: '';
            position: absolute;
            left: 16px;
            top: 0;
            bottom: 0;
            width: 3px;
            background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
            border-radius: 2px;
        }

        .timeline-item {
            margin-bottom: 32px;
            position: relative;
            background: white;
            padding: 24px;
            border-radius: 16px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
            border: 2px solid transparent;
            transition: all 0.3s ease;
        }

        .timeline-item:hover {
            border-color: var(--primary-color);
            transform: translateX(8px);
            box-shadow: 0 8px 30px rgba(64, 224, 208, 0.15);
        }

        .timeline-item::before {
            content: '';
            position: absolute;
            left: -44px;
            top: 24px;
            width: 16px;
            height: 16px;
            border-radius: 50%;
            background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
            border: 3px solid white;
            box-shadow: 0 0 0 3px var(--primary-color);
        }

        .timeline-time {
            font-size: 0.875rem;
            color: var(--gray-500);
            margin-bottom: 8px;
            font-weight: 500;
        }

        .timeline-content {
            font-size: 1rem;
            color: var(--gray-800);
            line-height: 1.6;
        }

        .timeline-content strong {
            color: var(--gray-900);
            font-weight: 700;
        }

        /* 响应式设计 */

        @media (max-width: 768px) {
            .verification-detail-content {
                padding: 16px;
            }

            .detail-header {
                flex-direction: column;
                align-items: flex-start;
                gap: 16px;
            }

            .detail-title h1 {
                font-size: 1.5rem;
            }

            .info-grid {
                grid-template-columns: 1fr;
                gap: 16px;
            }

            .actions {
                flex-direction: column;
                gap: 12px;
            }

            .action-btn {
                width: 100%;
            }

            .card-body {
                padding: 24px 20px;
            }

            .timeline {
                padding-left: 24px;
            }

            .timeline-item {
                padding: 20px;
            }

            .timeline-item::before {
                left: -36px;
            }
        }
    </style>
</head>
<body>
    <div class="admin-layout">
        <!-- 左侧菜单 -->
        <?php include '../includes/sidebar.php'; ?>

        <!-- 主内容区域 -->
        <main class="main-content">
            <!-- 顶部导航栏 -->
            <?php include '../includes/topbar.php'; ?>

            <div class="verification-detail-content">
                <!-- 页面标题 -->
                <div class="detail-header">
                    <div class="detail-title">
                        <div class="icon">
                            <i class="fas fa-user-check"></i>
                        </div>
                        <h1>实名认证详情</h1>
                    </div>
                    <a href="index.php" class="back-btn">
                        <i class="fas fa-arrow-left"></i>
                        返回列表
                    </a>
                </div>

                <div class="detail-container">
                        <!-- 基本信息 -->
                        <div class="detail-card">
                            <div class="card-header">
                                <h2>
                                    <i class="fas fa-id-card"></i>
                                    基本信息
                                </h2>
                                <?php
                                $status = $verification['verification_status'];
                                $status_class = 'status-' . $status;
                                $status_text = [
                                    'pending' => '待审核',
                                    'approved' => '已通过',
                                    'rejected' => '已拒绝'
                                ];
                                $status_icon = [
                                    'pending' => 'fas fa-clock',
                                    'approved' => 'fas fa-check-circle',
                                    'rejected' => 'fas fa-times-circle'
                                ];
                                ?>
                                <span class="status-badge <?php echo $status_class; ?>">
                                    <i class="<?php echo $status_icon[$status] ?? 'fas fa-question'; ?>"></i>
                                    <?php echo $status_text[$status] ?? $status; ?>
                                </span>
                            </div>
                            <div class="card-body">
                                <div class="info-grid">
                                    <div class="info-item">
                                        <div class="info-label">认证ID</div>
                                        <div class="info-value">#<?php echo $verification['id']; ?></div>
                                    </div>

                                    <div class="info-item">
                                        <div class="info-label">趣玩ID</div>
                                        <div class="info-value quwanplanet-id"><?php echo htmlspecialchars($verification['quwan_id']); ?></div>
                                    </div>

                                    <div class="info-item">
                                        <div class="info-label">用户名</div>
                                        <div class="info-value"><?php echo htmlspecialchars($verification['username']); ?></div>
                                    </div>

                                    <div class="info-item">
                                        <div class="info-label">手机号</div>
                                        <div class="info-value"><?php echo htmlspecialchars($verification['phone'] ?? '未绑定'); ?></div>
                                    </div>

                                    <div class="info-item">
                                        <div class="info-label">邮箱</div>
                                        <div class="info-value"><?php echo htmlspecialchars($verification['email'] ?? '未绑定'); ?></div>
                                    </div>

                                    <div class="info-item">
                                        <div class="info-label">真实姓名</div>
                                        <div class="info-value"><?php echo htmlspecialchars($verification['real_name']); ?></div>
                                    </div>

                                    <div class="info-item">
                                        <div class="info-label">身份证号</div>
                                        <div class="info-value">
                                            <div class="id-card-number"><?php echo htmlspecialchars($verification['id_card_number']); ?></div>
                                        </div>
                                    </div>

                                    <div class="info-item">
                                        <div class="info-label">提交时间</div>
                                        <div class="info-value">
                                            <?php
                                            if ($verification['submitted_at'] && $verification['submitted_at'] !== '0000-00-00 00:00:00') {
                                                echo date('Y年m月d日 H:i:s', strtotime($verification['submitted_at']));
                                            } else {
                                                echo '-';
                                            }
                                            ?>
                                        </div>
                                    </div>

                                    <div class="info-item">
                                        <div class="info-label">审核人员</div>
                                        <div class="info-value">
                                            <?php
                                            if ($verification['admin_name']) {
                                                echo htmlspecialchars($verification['admin_name']);
                                                if ($verification['admin_employee_id']) {
                                                    echo '（工号：' . htmlspecialchars($verification['admin_employee_id']) . '）';
                                                }
                                            } else {
                                                echo '未审核';
                                            }
                                            ?>
                                        </div>
                                    </div>
                                </div>

                                <?php if ($verification['verification_status'] === 'rejected' && !empty($verification['verification_reason'])): ?>
                                    <div class="reason-section">
                                        <h4>
                                            <i class="fas fa-exclamation-triangle"></i>
                                            拒绝原因
                                        </h4>
                                        <div class="reason-text">
                                            <?php echo htmlspecialchars($verification['verification_reason']); ?>
                                        </div>
                                    </div>
                                <?php endif; ?>

                                <!-- 操作按钮 -->
                                <?php if ($verification['verification_status'] === 'pending'): ?>
                                    <div class="actions">
                                        <a href="approve.php?id=<?php echo $verification['id']; ?>"
                                           class="action-btn btn-approve"
                                           onclick="return confirm('确认通过此实名认证申请？')">
                                            <i class="fas fa-check"></i>
                                            通过认证
                                        </a>
                                        <a href="reject.php?id=<?php echo $verification['id']; ?>"
                                           class="action-btn btn-reject">
                                            <i class="fas fa-times"></i>
                                            拒绝认证
                                        </a>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>

                        <!-- 操作时间线 -->
                        <div class="detail-card">
                            <div class="card-header">
                                <h2>
                                    <i class="fas fa-history"></i>
                                    操作记录
                                </h2>
                            </div>
                            <div class="card-body">
                                <div class="timeline">
                                    <?php if ($verification['submitted_at'] && $verification['submitted_at'] !== '0000-00-00 00:00:00'): ?>
                                        <div class="timeline-item">
                                            <div class="timeline-time">
                                                <?php echo date('Y年m月d日 H:i:s', strtotime($verification['submitted_at'])); ?>
                                            </div>
                                            <div class="timeline-content">
                                                <strong>用户提交实名认证申请</strong><br>
                                                申请人：<?php echo htmlspecialchars($verification['real_name']); ?><br>
                                                身份证号：<?php echo htmlspecialchars($verification['id_card_number']); ?>
                                            </div>
                                        </div>
                                    <?php endif; ?>

                                    <?php if ($verification['verification_status'] === 'approved' && $verification['verified_at'] && $verification['verified_at'] !== '0000-00-00 00:00:00'): ?>
                                        <div class="timeline-item">
                                            <div class="timeline-time">
                                                <?php echo date('Y年m月d日 H:i:s', strtotime($verification['verified_at'])); ?>
                                            </div>
                                            <div class="timeline-content">
                                                <strong style="color: #10B981;">✅ 认证审核通过</strong><br>
                                                审核人员：<?php
                                                if ($verification['admin_name']) {
                                                    echo htmlspecialchars($verification['admin_name']);
                                                    if ($verification['admin_employee_id']) {
                                                        echo '（工号：' . htmlspecialchars($verification['admin_employee_id']) . '）';
                                                    }
                                                } else {
                                                    echo '系统自动审核';
                                                }
                                                ?>
                                            </div>
                                        </div>
                                    <?php elseif ($verification['verification_status'] === 'rejected'): ?>
                                        <div class="timeline-item">
                                            <div class="timeline-time">
                                                <?php echo date('Y年m月d日 H:i:s'); ?>
                                            </div>
                                            <div class="timeline-content">
                                                <strong style="color: #EF4444;">❌ 认证审核被拒绝</strong><br>
                                                审核人员：<?php
                                                if ($verification['admin_name']) {
                                                    echo htmlspecialchars($verification['admin_name']);
                                                    if ($verification['admin_employee_id']) {
                                                        echo '（工号：' . htmlspecialchars($verification['admin_employee_id']) . '）';
                                                    }
                                                } else {
                                                    echo '系统自动审核';
                                                }
                                                ?><br>
                                                <?php if (!empty($verification['verification_reason'])): ?>
                                                    拒绝原因：<?php echo htmlspecialchars($verification['verification_reason']); ?>
                                                <?php endif; ?>
                                            </div>
                                        </div>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                </div>
            </div>
        </main>
    </div>

    <!-- 引入管理后台布局脚本 -->
    <script src="../assets/js/admin-layout.js"></script>
</body>
</html>
