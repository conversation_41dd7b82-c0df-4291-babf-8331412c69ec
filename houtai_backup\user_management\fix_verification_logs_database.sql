-- =====================================================
-- 验证码发送日志功能数据库修复脚本
-- 请在数据库管理工具中执行此脚本
-- =====================================================

-- 1. 检查并修复 admin_logs 表结构
-- =====================================================

-- 添加缺失的字段到 admin_logs 表
ALTER TABLE `admin_logs` 
ADD COLUMN IF NOT EXISTS `employee_id` VARCHAR(50) DEFAULT NULL COMMENT '员工工号',
ADD COLUMN IF NOT EXISTS `department` VARCHAR(100) DEFAULT NULL COMMENT '部门名称',
ADD COLUMN IF NOT EXISTS `target_user_id` INT(11) DEFAULT NULL COMMENT '目标用户ID',
ADD COLUMN IF NOT EXISTS `reason` TEXT DEFAULT NULL COMMENT '操作原因/备注';

-- 添加索引优化查询性能
ALTER TABLE `admin_logs` 
ADD INDEX IF NOT EXISTS `idx_target_user_id` (`target_user_id`),
ADD INDEX IF NOT EXISTS `idx_employee_id` (`employee_id`),
ADD INDEX IF NOT EXISTS `idx_action` (`action`);

-- 2. 创建 user_logs 表（如果不存在）
-- =====================================================

CREATE TABLE IF NOT EXISTS `user_logs` (
    `id` INT(11) NOT NULL AUTO_INCREMENT COMMENT '日志ID',
    `user_id` INT(11) NOT NULL COMMENT '用户ID',
    `operator_name` VARCHAR(100) NOT NULL COMMENT '操作员姓名',
    `employee_id` VARCHAR(50) DEFAULT NULL COMMENT '员工工号',
    `department` VARCHAR(100) DEFAULT NULL COMMENT '部门名称',
    `type` VARCHAR(50) NOT NULL COMMENT '日志类型',
    `content` TEXT NOT NULL COMMENT '日志内容',
    `ip_address` VARCHAR(45) DEFAULT NULL COMMENT '操作IP地址',
    `user_agent` TEXT DEFAULT NULL COMMENT '用户代理信息',
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    PRIMARY KEY (`id`),
    KEY `idx_user_id` (`user_id`),
    KEY `idx_type` (`type`),
    KEY `idx_created_at` (`created_at`),
    KEY `idx_employee_id` (`employee_id`),
    KEY `idx_operator_name` (`operator_name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户管理日志表';

-- 3. 确保 admin_users 表有必要字段
-- =====================================================

ALTER TABLE `admin_users` 
ADD COLUMN IF NOT EXISTS `employee_id` VARCHAR(50) DEFAULT NULL COMMENT '员工工号',
ADD COLUMN IF NOT EXISTS `department` VARCHAR(100) DEFAULT NULL COMMENT '部门名称';

-- 4. 创建验证码发送日志视图（方便查询）
-- =====================================================

CREATE OR REPLACE VIEW `v_verification_code_logs` AS
SELECT 
    ul.id,
    ul.user_id,
    u.username,
    u.quwan_id,
    ul.operator_name,
    ul.employee_id,
    ul.department,
    ul.content,
    ul.ip_address,
    ul.created_at,
    vc.code,
    vc.phone,
    vc.type as code_type,
    vc.status as code_status,
    vc.expires_at
FROM user_logs ul
LEFT JOIN users u ON ul.user_id = u.id
LEFT JOIN verification_codes vc ON ul.user_id = vc.user_id 
    AND ul.created_at BETWEEN DATE_SUB(vc.created_at, INTERVAL 5 SECOND) AND DATE_ADD(vc.created_at, INTERVAL 5 SECOND)
WHERE ul.type = '发送验证码'
ORDER BY ul.created_at DESC;

-- 5. 插入一些示例数据（可选，用于测试）
-- =====================================================

-- 更新现有管理员的员工信息（如果需要）
-- UPDATE admin_users SET employee_id = 'ADMIN001', department = '用户管理部' WHERE id = 1;

-- 6. 创建存储过程简化日志记录
-- =====================================================

DELIMITER $$

-- 创建记录验证码发送日志的存储过程
CREATE PROCEDURE IF NOT EXISTS `sp_log_verification_code_send`(
    IN p_user_id INT,
    IN p_admin_id INT,
    IN p_admin_name VARCHAR(100),
    IN p_employee_id VARCHAR(50),
    IN p_department VARCHAR(100),
    IN p_phone VARCHAR(20),
    IN p_code_type VARCHAR(50),
    IN p_note TEXT,
    IN p_ip_address VARCHAR(45),
    IN p_user_agent TEXT
)
BEGIN
    DECLARE v_username VARCHAR(100);
    DECLARE v_quwan_id VARCHAR(20);
    DECLARE v_content TEXT;
    
    -- 获取用户信息
    SELECT username, quwan_id INTO v_username, v_quwan_id 
    FROM users WHERE id = p_user_id;
    
    -- 构建日志内容
    SET v_content = CONCAT(
        '向用户 ', IFNULL(v_username, '未知'), 
        '（趣玩ID：', IFNULL(v_quwan_id, '未知'), '）',
        ' 发送验证码到手机号 ', p_phone,
        '，类型：', p_code_type
    );
    
    IF p_note IS NOT NULL AND p_note != '' THEN
        SET v_content = CONCAT(v_content, '，备注：', p_note);
    END IF;
    
    -- 插入管理员日志
    INSERT INTO admin_logs (
        admin_id, admin_name, employee_id, department,
        target_user_id, action, reason, created_at
    ) VALUES (
        p_admin_id, p_admin_name, p_employee_id, p_department,
        p_user_id, '发送验证码', v_content, NOW()
    );
    
    -- 插入用户日志
    INSERT INTO user_logs (
        user_id, operator_name, employee_id, department,
        type, content, ip_address, user_agent, created_at
    ) VALUES (
        p_user_id, p_admin_name, p_employee_id, p_department,
        '发送验证码', v_content, p_ip_address, p_user_agent, NOW()
    );
    
END$$

DELIMITER ;

-- 7. 创建查询最近验证码发送日志的存储过程
-- =====================================================

DELIMITER $$

CREATE PROCEDURE IF NOT EXISTS `sp_get_recent_verification_logs`(
    IN p_limit INT DEFAULT 50
)
BEGIN
    SELECT 
        ul.id,
        ul.user_id,
        u.username,
        u.quwan_id,
        ul.operator_name,
        ul.employee_id,
        ul.department,
        ul.content,
        ul.created_at,
        ul.ip_address
    FROM user_logs ul
    LEFT JOIN users u ON ul.user_id = u.id
    WHERE ul.type = '发送验证码'
    ORDER BY ul.created_at DESC
    LIMIT p_limit;
END$$

DELIMITER ;

-- 8. 创建按用户查询验证码发送日志的存储过程
-- =====================================================

DELIMITER $$

CREATE PROCEDURE IF NOT EXISTS `sp_get_user_verification_logs`(
    IN p_user_id INT,
    IN p_limit INT DEFAULT 20
)
BEGIN
    SELECT 
        ul.id,
        ul.operator_name,
        ul.employee_id,
        ul.department,
        ul.content,
        ul.created_at,
        ul.ip_address,
        vc.code,
        vc.phone,
        vc.type as code_type,
        vc.status as code_status
    FROM user_logs ul
    LEFT JOIN verification_codes vc ON ul.user_id = vc.user_id 
        AND ul.created_at BETWEEN DATE_SUB(vc.created_at, INTERVAL 5 SECOND) AND DATE_ADD(vc.created_at, INTERVAL 5 SECOND)
    WHERE ul.user_id = p_user_id AND ul.type = '发送验证码'
    ORDER BY ul.created_at DESC
    LIMIT p_limit;
END$$

DELIMITER ;

-- =====================================================
-- 执行完成提示
-- =====================================================

SELECT 'verification_logs_database_setup_completed' as status, NOW() as completed_at;

-- 验证表结构
SHOW TABLES LIKE '%logs%';

-- 验证字段
DESCRIBE admin_logs;
DESCRIBE user_logs;
