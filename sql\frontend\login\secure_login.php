<?php
/**
 * 安全登录API（需要验证码和密码）
 * 用于有安全风险的登录场景
 */

session_start();
header('Content-Type: application/json; charset=utf-8');

// 数据库连接配置
$db_config = [
    'host' => 'localhost',
    'username' => 'quwanplanet',
    'password' => 'nJmJm23FB4Xn6Fc3',
    'dbname' => 'quwanplanet',
    'port' => 3306,
    'charset' => 'utf8mb4'
];

try {
    $pdo = new PDO(
        "mysql:host={$db_config['host']};port={$db_config['port']};dbname={$db_config['dbname']};charset={$db_config['charset']}",
        $db_config['username'],
        $db_config['password'],
        [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
            PDO::ATTR_EMULATE_PREPARES => false
        ]
    );
} catch (PDOException $e) {
    echo json_encode(['success' => false, 'message' => '数据库连接失败']);
    exit;
}

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode(['success' => false, 'message' => '请求方法错误']);
    exit;
}

$input = json_decode(file_get_contents('php://input'), true);
$user_id = intval($input['user_id'] ?? 0);
$verification_code = trim($input['verification_code'] ?? '');
$password = trim($input['password'] ?? '');

if (!$user_id) {
    echo json_encode(['success' => false, 'message' => '用户ID无效']);
    exit;
}

if (empty($verification_code)) {
    echo json_encode(['success' => false, 'message' => '请输入验证码']);
    exit;
}

if (empty($password)) {
    echo json_encode(['success' => false, 'message' => '请输入密码']);
    exit;
}

// 获取用户信息
$stmt = $pdo->prepare("SELECT * FROM users WHERE id = ?");
$stmt->execute([$user_id]);
$user = $stmt->fetch();

if (!$user) {
    echo json_encode(['success' => false, 'message' => '用户不存在']);
    exit;
}

// 检查用户状态
if ($user['status'] === 'banned') {
    echo json_encode(['success' => false, 'message' => '账户已被封禁，请联系客服']);
    exit;
}

// 验证验证码
if (!isset($_SESSION['verification_code']) ||
    !isset($_SESSION['verification_time']) ||
    !isset($_SESSION['verification_phone']) ||
    $_SESSION['verification_phone'] !== $user['phone']) {
    echo json_encode(['success' => false, 'message' => '验证码已失效，请重新获取']);
    exit;
}

// 检查验证码是否过期（5分钟）
if (time() - $_SESSION['verification_time'] > 300) {
    unset($_SESSION['verification_code'], $_SESSION['verification_time'], $_SESSION['verification_phone']);
    echo json_encode(['success' => false, 'message' => '验证码已过期，请重新获取']);
    exit;
}

// 验证验证码
if ($_SESSION['verification_code'] !== $verification_code) {
    echo json_encode(['success' => false, 'message' => '验证码错误']);
    exit;
}

// 验证密码
if (!password_verify($password, $user['password'])) {
    // 记录失败的登录尝试
    $current_ip = $_SERVER['REMOTE_ADDR'];
    $current_user_agent = $_SERVER['HTTP_USER_AGENT'];
    $device_info = parseUserAgent($current_user_agent);
    $device_fingerprint = generateDeviceFingerprint($device_info);

    $stmt = $pdo->prepare("
        INSERT INTO login_logs (user_id, login_time, ip_address, user_agent, status, device_fingerprint, login_type)
        VALUES (?, NOW(), ?, ?, 'failed', ?, 'secure_login')
    ");
    $stmt->execute([
        $user['id'],
        $current_ip,
        $current_user_agent,
        $device_fingerprint
    ]);

    echo json_encode(['success' => false, 'message' => '密码错误']);
    exit;
}

// 清除验证码
unset($_SESSION['verification_code'], $_SESSION['verification_time'], $_SESSION['verification_phone']);

// 执行登录
$_SESSION['user_id'] = $user['id'];
$_SESSION['username'] = $user['username'];
$_SESSION['quwan_id'] = $user['quwan_id'];

// 更新最后登录时间
$stmt = $pdo->prepare("UPDATE users SET last_login = NOW() WHERE id = ?");
$stmt->execute([$user['id']]);

// 记录成功的登录日志
$current_ip = $_SERVER['REMOTE_ADDR'];
$current_user_agent = $_SERVER['HTTP_USER_AGENT'];
$device_info = parseUserAgent($current_user_agent);
$device_fingerprint = generateDeviceFingerprint($device_info);

$stmt = $pdo->prepare("
    INSERT INTO login_logs (user_id, login_time, ip_address, user_agent, status, device_fingerprint, login_type)
    VALUES (?, NOW(), ?, ?, 'success', ?, 'secure_login')
");
$stmt->execute([
    $user['id'],
    $current_ip,
    $current_user_agent,
    $device_fingerprint
]);

echo json_encode([
    'success' => true,
    'message' => '登录成功',
    'redirect' => '../home/<USER>'
]);

/**
 * 解析User Agent
 */
function parseUserAgent($user_agent) {
    $device = 'Unknown';
    $os = 'Unknown';
    $browser = 'Unknown';

    // 检测设备类型
    if (preg_match('/Mobile|Android|iPhone|iPad/', $user_agent)) {
        $device = 'Mobile';
    } else {
        $device = 'Desktop';
    }

    // 检测操作系统
    if (preg_match('/Windows NT ([0-9.]+)/', $user_agent, $matches)) {
        $os = 'Windows ' . $matches[1];
    } elseif (preg_match('/Mac OS X ([0-9_]+)/', $user_agent, $matches)) {
        $os = 'macOS ' . str_replace('_', '.', $matches[1]);
    } elseif (preg_match('/Android ([0-9.]+)/', $user_agent, $matches)) {
        $os = 'Android ' . $matches[1];
    } elseif (preg_match('/iPhone OS ([0-9_]+)/', $user_agent, $matches)) {
        $os = 'iOS ' . str_replace('_', '.', $matches[1]);
    }

    // 检测浏览器
    if (preg_match('/Chrome\/([0-9.]+)/', $user_agent, $matches)) {
        $browser = 'Chrome ' . $matches[1];
    } elseif (preg_match('/Firefox\/([0-9.]+)/', $user_agent, $matches)) {
        $browser = 'Firefox ' . $matches[1];
    } elseif (preg_match('/Safari\/([0-9.]+)/', $user_agent, $matches)) {
        $browser = 'Safari ' . $matches[1];
    } elseif (preg_match('/Edge\/([0-9.]+)/', $user_agent, $matches)) {
        $browser = 'Edge ' . $matches[1];
    }

    return [
        'device' => $device,
        'os' => $os,
        'browser' => $browser,
        'full' => $user_agent
    ];
}

/**
 * 生成设备指纹
 */
function generateDeviceFingerprint($device_info) {
    return md5($device_info['os'] . '|' . $device_info['browser'] . '|' . $device_info['device']);
}
?>
