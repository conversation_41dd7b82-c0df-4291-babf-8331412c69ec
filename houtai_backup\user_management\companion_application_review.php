<?php
// auth_check.php 或类似的登录验证文件，确保路径正确
require_once __DIR__ . '/../auth_check.php'; 
require_once __DIR__ . '/../db_config.php'; // 数据库连接

$page_title = '陪玩资质审核';
$current_page = 'companion_review'; // 用于菜单高亮

// 获取所有申请 (后续会加入分页和筛选)
$applications = [];
$pdo = null;
try {
    $dsn = "mysql:host={$db_config['host']};dbname={$db_config['dbname']};charset={$db_config['charset']};port={$db_config['port']}";
    $options = [
        PDO::ATTR_ERRMODE            => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
        PDO::ATTR_EMULATE_PREPARES   => false,
    ];
    $pdo = new PDO($dsn, $db_config['username'], $db_config['password'], $options);

    // 获取筛选条件
    $status_filter = $_GET['status'] ?? 'pending'; // Default to 'pending'
    $search_query = trim($_GET['search'] ?? '');

    // 构建查询条件
    $sql_conditions = [];
    $sql_params = [];

    if ($status_filter !== 'all' && in_array($status_filter, ['pending', 'approved', 'rejected'])) {
        $sql_conditions[] = "ca.status = :status";
        $sql_params[':status'] = $status_filter;
    }

    if (!empty($search_query)) {
        $sql_conditions[] = "(u.username LIKE :search OR ca.real_name LIKE :search OR ca.id_number LIKE :search OR ca.user_id LIKE :search_user_id)";
        $sql_params[':search'] = '%' . $search_query . '%';
        $sql_params[':search_user_id'] = $search_query; // For exact match on user_id if it's numeric
    }

    $where_clause = "";
    if (!empty($sql_conditions)) {
        $where_clause = "WHERE " . implode(" AND ", $sql_conditions);
    }

    // 查询 companion_applications 并关联 users 表获取用户名
    $sql = "
        SELECT ca.*, u.username 
        FROM companion_applications ca
        JOIN users u ON ca.user_id = u.id 
        $where_clause
        ORDER BY ca.application_date DESC
    ";
    $stmt = $pdo->prepare($sql);
    // 分页设置
    $page = max(1, intval($_GET['page'] ?? 1));
    $per_page = 15; // 每页显示15条
    $offset = ($page - 1) * $per_page;

    // 获取总数 (应用相同的筛选条件)
    $count_sql = "SELECT COUNT(*) FROM companion_applications ca JOIN users u ON ca.user_id = u.id $where_clause";
    $count_stmt = $pdo->prepare($count_sql);
    $count_stmt->execute($sql_params); // 使用与主查询相同的参数，除了 limit/offset
    $total_applications = $count_stmt->fetchColumn();
    $total_pages = ceil($total_applications / $per_page);

    // 查询 companion_applications 并关联 users 表获取用户名 (加入分页)
    $sql = "
        SELECT ca.*, u.username 
        FROM companion_applications ca
        JOIN users u ON ca.user_id = u.id 
        $where_clause
        ORDER BY ca.application_date DESC
        LIMIT $per_page OFFSET $offset
    ";
    $stmt = $pdo->prepare($sql);
    $stmt->execute($sql_params);
    $applications = $stmt->fetchAll();

    // 获取统计数据
    $stats_sql = "
        SELECT status, COUNT(*) as count
        FROM companion_applications
        GROUP BY status
    ";
    $stats_stmt = $pdo->query($stats_sql);
    $stats = [];
    while ($row = $stats_stmt->fetch(PDO::FETCH_ASSOC)) {
        $stats[$row['status']] = $row['count'];
    }
    $pending_count_stat = $stats['pending'] ?? 0;
    $approved_count_stat = $stats['approved'] ?? 0;
    $rejected_count_stat = $stats['rejected'] ?? 0;
    $total_applications_stat = array_sum($stats);

} catch (PDOException $e) {
    // 记录错误或显示错误信息
    error_log('Error fetching companion applications: ' . $e->getMessage());
    $fetch_error = '无法加载申请列表：' . $e->getMessage();
} catch (Exception $e) {
    error_log('General error: ' . $e->getMessage());
    $fetch_error = '发生未知错误。';
}

// 引入后台布局头部
// 假设 admin_layout.php 包含完整的 HTML 结构，或者分为 header 和 footer
// 如果 admin_layout.php 是一个完整的页面模板，可能需要调整这里的引入方式
// 或者 admin_layout.php 内部有内容区域的占位符
// 为简化，这里假设 admin_layout.php 包含了头部和菜单
// 并且它会使用 $page_title 和 $current_page 变量
// The admin_layout.php is expected to be included by a master layout file or called differently.
// For now, we assume admin_layout.php sets up the basic page structure including sidebar and topbar.
// If admin_layout.php is a full page template, this include might be okay, 
// but verification/index.php uses separate includes for sidebar and topbar.
// We will adjust this later if needed to match the structure of verification/index.php more closely.
// For now, let's ensure the content is wrapped correctly.

// No direct include of admin_layout.php here. 
// Instead, we will structure the HTML and assume admin_layout.php (or equivalent) is handled by the broader system.
// If admin_layout.php is indeed the entry point that includes this file's content, then this comment is just for clarification.
// Based on sidebar.php and verification/index.php, it seems admin_layout.php might be a wrapper.
// Let's assume for now that admin_layout.php is included by the calling script or is a wrapper.
// We will ensure our content starts after the header/sidebar that admin_layout.php might provide.

// If admin_layout.php is like a header include:
// include __DIR__ . '/../admin_layout.php'; 
// For now, we'll keep it as it was, assuming it provides the necessary structure.
include __DIR__ . '/../admin_layout.php';

?>

<div class="content-wrapper">
    <section class="content-header">
        <h1>
            <?php echo htmlspecialchars($page_title); ?>
            <small>管理和审核用户提交的陪玩资质申请</small>
        </h1>
    </section>

    <!-- 统计卡片区域 -->
    <section class="content" style="padding-bottom: 0;">
        <div class="row">
            <div class="col-lg-3 col-xs-6">
                <div class="small-box bg-yellow">
                    <div class="inner">
                        <h3><?php echo $pending_count_stat; ?></h3>
                        <p>待处理申请</p>
                    </div>
                    <div class="icon">
                        <i class="fa fa-hourglass-half"></i>
                    </div>
                    <a href="companion_application_review.php?status=pending" class="small-box-footer">查看详情 <i class="fa fa-arrow-circle-right"></i></a>
                </div>
            </div>
            <div class="col-lg-3 col-xs-6">
                <div class="small-box bg-green">
                    <div class="inner">
                        <h3><?php echo $approved_count_stat; ?></h3>
                        <p>已批准申请</p>
                    </div>
                    <div class="icon">
                        <i class="fa fa-check-circle"></i>
                    </div>
                    <a href="companion_application_review.php?status=approved" class="small-box-footer">查看详情 <i class="fa fa-arrow-circle-right"></i></a>
                </div>
            </div>
            <div class="col-lg-3 col-xs-6">
                <div class="small-box bg-red">
                    <div class="inner">
                        <h3><?php echo $rejected_count_stat; ?></h3>
                        <p>已拒绝申请</p>
                    </div>
                    <div class="icon">
                        <i class="fa fa-times-circle"></i>
                    </div>
                    <a href="companion_application_review.php?status=rejected" class="small-box-footer">查看详情 <i class="fa fa-arrow-circle-right"></i></a>
                </div>
            </div>
            <div class="col-lg-3 col-xs-6">
                <div class="small-box bg-aqua">
                    <div class="inner">
                        <h3><?php echo $total_applications_stat; ?></h3>
                        <p>总申请数</p>
                    </div>
                    <div class="icon">
                        <i class="fa fa-file-text-o"></i>
                    </div>
                    <a href="companion_application_review.php?status=all" class="small-box-footer">查看详情 <i class="fa fa-arrow-circle-right"></i></a>
                </div>
            </div>
        </div>
    </section>

    <section class="content">
        <div class="row">
            <div class="col-xs-12">
                <div class="box">
                    <div class="box-header with-border">
                        <h3 class="box-title">
                            <?php 
                                if ($status_filter === 'pending') echo '待处理';
                                elseif ($status_filter === 'approved') echo '已批准';
                                elseif ($status_filter === 'rejected') echo '已拒绝';
                                else echo '所有';
                            ?>
                            陪玩申请列表
                        </h3>
                         <div class="box-tools pull-right" style="padding-top: 5px;">
                            <form action="companion_application_review.php" method="GET" class="form-inline">
                                <div class="form-group">
                                    <input type="text" name="search" class="form-control input-sm" placeholder="搜索用户/姓名/ID" value="<?php echo htmlspecialchars($search_query); ?>">
                                </div>
                                <div class="form-group" style="margin-left: 10px;">
                                    <select name="status" class="form-control input-sm">
                                        <option value="all" <?php if ($status_filter === 'all') echo 'selected'; ?>>所有状态</option>
                                        <option value="pending" <?php if ($status_filter === 'pending') echo 'selected'; ?>>待处理</option>
                                        <option value="approved" <?php if ($status_filter === 'approved') echo 'selected'; ?>>已批准</option>
                                        <option value="rejected" <?php if ($status_filter === 'rejected') echo 'selected'; ?>>已拒绝</option>
                                    </select>
                                </div>
                                <button type="submit" class="btn btn-sm btn-primary" style="margin-left: 10px;"><i class="fa fa-search"></i> 筛选</button>
                                <a href="companion_application_review.php" class="btn btn-sm btn-default" style="margin-left: 5px;">重置</a>
                            </form>
                        </div>
                    </div>
                    <div class="box-body table-responsive no-padding">
                        <?php if (isset($fetch_error)): ?>
                            <div class="alert alert-danger" style="margin:15px;"><?php echo htmlspecialchars($fetch_error); ?></div>
                        <?php elseif (empty($applications)): ?>
                            <div class="alert alert-info" style="margin:15px;">
                                当前没有符合筛选条件的陪玩申请。
                                <?php if (!empty($search_query) || $status_filter !== 'pending'): ?>
                                    <br><a href="companion_application_review.php?status=pending" class="btn btn-xs btn-default">查看所有待处理申请</a>
                                <?php endif; ?>
                            </div>
                        <?php else: ?>
                            <table class="table table-hover table-bordered">
                                <thead>
                                    <tr>
                                        <th>用户ID</th>
                                        <th>用户名</th>
                                        <th>真实姓名</th>
                                        <th>身份证号</th>
                                        <th>申请类型</th>
                                        <th>类型详情</th>
                                        <th>自我介绍</th>
                                        <th>联系方式</th>
                                        <th>语音</th>
                                        <th>证件照</th>
                                        <th>申请日期</th>
                                        <th>状态</th>
                                        <th>操作</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($applications as $app): ?>
                                    <tr>
                                        <td><?php echo htmlspecialchars($app['user_id']); ?></td>
                                        <td><?php echo htmlspecialchars($app['username'] ?? 'N/A'); ?></td>
                                        <td><?php echo htmlspecialchars($app['real_name']); ?></td>
                                        <td><?php echo htmlspecialchars($app['id_number']); ?></td>
                                        <td> <!-- Application Type -->
                                            <?php 
                                                if ($app['application_type'] === 'game') echo '游戏陪玩';
                                                elseif ($app['application_type'] === 'city') echo '城市陪玩';
                                                else echo htmlspecialchars($app['application_type']); 
                                            ?>
                                        </td>
                                        <td> <!-- Type Specific Details -->
                                            <?php 
                                            if ($app['application_type'] === 'game') {
                                                echo nl2br(htmlspecialchars($app['game_interests']));
                                            } elseif ($app['application_type'] === 'city' && !empty($app['type_specific_details'])) {
                                                $details = json_decode($app['type_specific_details'], true);
                                                if ($details) {
                                                    echo '服务城市: ' . htmlspecialchars($details['service_city'] ?? 'N/A') . '<br>';
                                                    echo '服务项目: ' . nl2br(htmlspecialchars($details['service_items'] ?? 'N/A'));
                                                } else {
                                                    echo '无法解析城市详情';
                                                }
                                            } elseif (!empty($app['game_interests'])) { // Fallback for older data before application_type was introduced
                                                echo nl2br(htmlspecialchars($app['game_interests']));
                                            }
                                            else {
                                                echo 'N/A';
                                            }
                                            ?>
                                        </td>
                                        <td><?php echo nl2br(htmlspecialchars($app['self_description'])); ?></td>
                                        <td><?php echo htmlspecialchars($app['contact_info']); ?></td>
                                        <td> <!-- Voice Sample -->
                                            <?php if (!empty($app['voice_sample_url'])): ?>
                                                <a href="/<?php echo htmlspecialchars($app['voice_sample_url']); ?>" target="_blank" class="btn btn-xs btn-primary" style="padding: 1px 5px;">播放</a>
                                            <?php else: echo '无'; endif; ?>
                                        </td>
                                        <td> <!-- ID Card Photos -->
                                            <?php if (!empty($app['id_card_front_url'])): ?>
                                                <a href="/<?php echo htmlspecialchars($app['id_card_front_url']); ?>" target="_blank" title="身份证正面"><img src="/<?php echo htmlspecialchars($app['id_card_front_url']); ?>" alt="正面" style="width:30px;height:auto;margin-right:5px;border:1px solid #ddd;object-fit:cover;"></a>
                                            <?php endif; ?>
                                            <?php if (!empty($app['id_card_back_url'])): ?>
                                                <a href="/<?php echo htmlspecialchars($app['id_card_back_url']); ?>" target="_blank" title="身份证背面"><img src="/<?php echo htmlspecialchars($app['id_card_back_url']); ?>" alt="背面" style="width:30px;height:auto;border:1px solid #ddd;object-fit:cover;"></a>
                                            <?php endif; ?>
                                            <?php if (empty($app['id_card_front_url']) && empty($app['id_card_back_url'])): echo '无'; endif; ?>
                                        </td>
                                        <td><?php echo htmlspecialchars(date('Y-m-d H:i', strtotime($app['application_date']))); ?></td>
                                        <td> <!-- Status -->
                                            <span class="label label-<?php 
                                                switch ($app['status']) {
                                                    case 'pending': echo 'warning'; break;
                                                    case 'approved': echo 'success'; break;
                                                    case 'rejected': echo 'danger'; break;
                                                    default: echo 'default'; break;
                                                }
                                            ?>"><?php 
                                                if ($app['status'] === 'pending') echo '待处理';
                                                elseif ($app['status'] === 'approved') echo '已批准';
                                                elseif ($app['status'] === 'rejected') echo '已拒绝';
                                                else echo htmlspecialchars($app['status']);
                                            ?></span>
                                        </td>
                                        <td> <!-- Actions -->
                                            <a href="companion_application_detail.php?id=<?php echo $app['id']; ?>" class="btn btn-xs btn-info">查看/处理</a>
                                        </td>
                                    </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        <?php endif; ?>
                    </div>
                    <?php if ($total_pages > 1): ?>
                    <div class="box-footer clearfix">
                        <ul class="pagination pagination-sm no-margin pull-right">
                            <?php 
                                // 构建分页链接的查询参数，保留现有筛选条件
                                $query_params = [];
                                if (!empty($status_filter)) $query_params['status'] = $status_filter;
                                if (!empty($search_query)) $query_params['search'] = $search_query;
                            ?>
                            <?php if ($page > 1): ?>
                                <li><a href="?<?php echo http_build_query(array_merge($query_params, ['page' => $page - 1])); ?>">« 上一页</a></li>
                            <?php endif; ?>

                            <?php 
                                // 显示页码逻辑 (简单示例：显示当前页前后几页)
                                $num_links = 5; // 总共显示的页码链接数
                                $start = max(1, $page - floor($num_links / 2));
                                $end = min($total_pages, $start + $num_links - 1);
                                if ($end - $start + 1 < $num_links) {
                                    $start = max(1, $end - $num_links + 1);
                                }

                                if ($start > 1) {
                                    echo '<li><a href="?'.http_build_query(array_merge($query_params, ['page' => 1])).'">1</a></li>';
                                    if ($start > 2) {
                                        echo '<li class="disabled"><span>...</span></li>';
                                    }
                                }

                                for ($i = $start; $i <= $end; $i++):
                            ?>
                                <li class="<?php if ($i == $page) echo 'active'; ?>"><a href="?<?php echo http_build_query(array_merge($query_params, ['page' => $i])); ?>"><?php echo $i; ?></a></li>
                            <?php endfor; ?>

                            <?php 
                                if ($end < $total_pages) {
                                    if ($end < $total_pages - 1) {
                                        echo '<li class="disabled"><span>...</span></li>';
                                    }
                                    echo '<li><a href="?'.http_build_query(array_merge($query_params, ['page' => $total_pages])).'">'.$total_pages.'</a></li>';
                                }
                            ?>

                            <?php if ($page < $total_pages): ?>
                                <li><a href="?<?php echo http_build_query(array_merge($query_params, ['page' => $page + 1])); ?>">下一页 »</a></li>
                            <?php endif; ?>
                        </ul>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </section>
</div>

<?php
// Assuming admin_layout.php handles the footer or it's part of the main structure.
// If verification/index.php is the model, it doesn't explicitly include a separate footer in its body,
// it's part of the overall HTML structure.
?>
<script>
// Basic JS for potential future interactions, like modal popups for quick actions if needed.
</script>
