<?php
// 引入session配置
require_once '../../sql/session_config.php';
initLoginSession();

// 检查用户是否已登录
if (!isUserLoggedIn()) {
    header('Location: ../login/index.php');
    exit;
}
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <meta name="theme-color" content="#1E90FF">
    <title>钱包 - 趣玩星球</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'PingFang SC', 'Helvetica Neue', Arial, sans-serif;
        }

        body {
            background-color: #f8f9fa;
            color: #333333;
            line-height: 1.6;
            min-height: 100vh;
        }

        .header-bar {
            display: flex;
            align-items: center;
            padding: 15px;
            background-color: #1E90FF;
            color: white;
            position: sticky;
            top: 0;
            z-index: 100;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        .back-button {
            margin-right: 15px;
            font-size: 18px;
            color: white;
            text-decoration: none;
        }

        .header-title {
            font-size: 18px;
            font-weight: bold;
        }

        .wallet-container {
            padding: 20px;
        }

        .balance-card {
            background-color: white;
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
            margin-bottom: 20px;
            text-align: center;
        }

        .balance-label {
            font-size: 14px;
            color: #666;
            margin-bottom: 10px;
        }

        .balance-amount {
            font-size: 36px;
            font-weight: bold;
            color: #1E90FF;
            margin-bottom: 20px;
        }

        .action-buttons {
            display: flex;
            justify-content: space-around;
            margin-top: 20px;
        }

        .action-button {
            display: flex;
            flex-direction: column;
            align-items: center;
            text-decoration: none;
        }

        .action-icon {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            background-color: rgba(30, 144, 255, 0.1);
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 8px;
        }

        .action-icon i {
            font-size: 20px;
            color: #1E90FF;
        }

        .action-label {
            font-size: 14px;
            color: #333;
        }

        .transaction-section {
            margin-top: 30px;
        }

        .section-title {
            font-size: 16px;
            font-weight: bold;
            margin-bottom: 15px;
            color: #333;
        }

        .transaction-list {
            background-color: white;
            border-radius: 15px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
            overflow: hidden;
        }

        .transaction-item {
            display: flex;
            align-items: center;
            padding: 15px;
            border-bottom: 1px solid #f0f0f0;
        }

        .transaction-item:last-child {
            border-bottom: none;
        }

        .transaction-icon {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background-color: rgba(30, 144, 255, 0.1);
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 15px;
        }

        .transaction-icon i {
            font-size: 18px;
            color: #1E90FF;
        }

        .transaction-details {
            flex: 1;
        }

        .transaction-title {
            font-size: 15px;
            font-weight: 500;
            color: #333;
            margin-bottom: 3px;
        }

        .transaction-time {
            font-size: 12px;
            color: #999;
        }

        .transaction-amount {
            font-weight: bold;
        }

        .amount-positive {
            color: #4CAF50;
        }

        .amount-negative {
            color: #F44336;
        }

        .empty-state {
            text-align: center;
            padding: 30px 20px;
            color: #999;
        }

        .empty-state i {
            font-size: 40px;
            margin-bottom: 10px;
            color: #ddd;
        }

        .empty-state p {
            font-size: 14px;
        }
    </style>
    <!-- 引入字体图标库 -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">

    <!-- 实时通知系统 -->
    <?php include '../includes/realtime_notifications.php'; ?>
</head>
<body>
    <!-- 顶部导航栏 -->
    <div class="header-bar">
        <a href="../profile/index.php" class="back-button">
            <i class="fas fa-arrow-left"></i>
        </a>
        <div class="header-title">钱包</div>
    </div>

    <div class="wallet-container">
        <div class="balance-card">
            <div class="balance-label">钱包余额</div>
            <div class="balance-amount">¥0.00</div>

            <div class="action-buttons">
                <a href="#" class="action-button">
                    <div class="action-icon">
                        <i class="fas fa-plus"></i>
                    </div>
                    <div class="action-label">充值</div>
                </a>
                <a href="#" class="action-button">
                    <div class="action-icon">
                        <i class="fas fa-minus"></i>
                    </div>
                    <div class="action-label">提现</div>
                </a>
                <a href="#" class="action-button">
                    <div class="action-icon">
                        <i class="fas fa-gift"></i>
                    </div>
                    <div class="action-label">赠送</div>
                </a>
            </div>
        </div>

        <div class="transaction-section">
            <div class="section-title">交易记录</div>

            <div class="transaction-list">
                <div class="empty-state">
                    <i class="fas fa-list-alt"></i>
                    <p>暂无交易记录</p>
                </div>

                <!-- 交易记录示例（当有数据时显示）
                <div class="transaction-item">
                    <div class="transaction-icon">
                        <i class="fas fa-plus"></i>
                    </div>
                    <div class="transaction-details">
                        <div class="transaction-title">充值</div>
                        <div class="transaction-time">2025-05-15 14:30</div>
                    </div>
                    <div class="transaction-amount amount-positive">+¥100.00</div>
                </div>

                <div class="transaction-item">
                    <div class="transaction-icon">
                        <i class="fas fa-shopping-cart"></i>
                    </div>
                    <div class="transaction-details">
                        <div class="transaction-title">购买服务</div>
                        <div class="transaction-time">2025-05-14 10:15</div>
                    </div>
                    <div class="transaction-amount amount-negative">-¥50.00</div>
                </div>
                -->
            </div>
        </div>
    </div>
</body>
</html>
