-- =====================================================
-- 客服系统演示数据
-- 用于测试会话详情页面功能
-- =====================================================

-- 插入演示会话数据
INSERT IGNORE INTO `customer_service_sessions` (`session_id`, `user_id`, `user_phone`, `user_name`, `customer_service_id`, `status`, `priority`, `category`, `source`, `satisfaction_score`, `satisfaction_comment`, `started_at`, `ended_at`, `duration`, `message_count`) VALUES
('CS20240101001', 1001, '13800138001', '张小明', 2, 'active', 'normal', '账户问题', 'web', NULL, NULL, '2024-01-15 10:30:00', NULL, 0, 5),
('CS20240101002', 1002, '13800138002', '李小红', 2, 'closed', 'high', '支付问题', 'app', 5, '服务很好，问题解决得很及时！', '2024-01-15 09:15:00', '2024-01-15 09:45:00', 1800, 12),
('CS20240101003', 1003, '13800138003', '王小华', NULL, 'waiting', 'urgent', '技术故障', 'web', NULL, NULL, '2024-01-15 11:00:00', NULL, 0, 1),
('CS20240101004', 1004, '13800138004', '赵小强', 2, 'transferred', 'normal', '产品咨询', 'phone', 4, '转接及时，但等待时间稍长', '2024-01-15 08:30:00', '2024-01-15 09:00:00', 1800, 8);

-- 插入演示消息数据
INSERT IGNORE INTO `customer_service_messages` (`session_id`, `sender_type`, `sender_id`, `sender_name`, `message_type`, `content`, `created_at`) VALUES
-- 会话 CS20240101001 的消息
('CS20240101001', 'user', 1001, '张小明', 'text', '你好，我的账户登录不了，提示密码错误', '2024-01-15 10:30:15'),
('CS20240101001', 'customer_service', 2, '赖武浩', 'text', '您好！我是客服小赖，很高兴为您服务。请问您是否尝试过重置密码？', '2024-01-15 10:30:45'),
('CS20240101001', 'user', 1001, '张小明', 'text', '试过了，但是没有收到重置邮件', '2024-01-15 10:31:20'),
('CS20240101001', 'customer_service', 2, '赖武浩', 'text', '好的，我来帮您查看一下。请提供一下您注册时使用的邮箱地址。', '2024-01-15 10:31:50'),
('CS20240101001', 'user', 1001, '张小明', 'text', '我的邮箱是 <EMAIL>', '2024-01-15 10:32:30'),

-- 会话 CS20240101002 的消息
('CS20240101002', 'user', 1002, '李小红', 'text', '我刚才支付失败了，但是钱被扣了', '2024-01-15 09:15:30'),
('CS20240101002', 'customer_service', 2, '赖武浩', 'text', '您好！我来帮您查看支付情况。请提供一下订单号。', '2024-01-15 09:16:00'),
('CS20240101002', 'user', 1002, '李小红', 'text', '订单号是 ORD20240115001', '2024-01-15 09:16:45'),
('CS20240101002', 'customer_service', 2, '赖武浩', 'text', '我查看到您的订单确实支付失败了，但银行那边显示扣款成功。这种情况通常会在24小时内自动退款。', '2024-01-15 09:18:20'),
('CS20240101002', 'user', 1002, '李小红', 'text', '那我需要重新下单吗？', '2024-01-15 09:19:00'),
('CS20240101002', 'customer_service', 2, '赖武浩', 'text', '建议您先等待退款到账，然后再重新下单。我这边已经为您加急处理退款申请。', '2024-01-15 09:20:15'),
('CS20240101002', 'user', 1002, '李小红', 'text', '好的，谢谢！', '2024-01-15 09:21:00'),
('CS20240101002', 'customer_service', 2, '赖武浩', 'text', '不客气！如果有其他问题随时联系我们。祝您使用愉快！', '2024-01-15 09:21:30'),
('CS20240101002', 'system', NULL, '系统', 'system', '用户对本次服务进行了评价：5星 - 服务很好，问题解决得很及时！', '2024-01-15 09:45:00'),

-- 会话 CS20240101003 的消息
('CS20240101003', 'user', 1003, '王小华', 'text', '网站打不开了，一直显示500错误', '2024-01-15 11:00:30'),

-- 会话 CS20240101004 的消息
('CS20240101004', 'user', 1004, '赵小强', 'text', '请问你们有什么新产品推荐吗？', '2024-01-15 08:30:15'),
('CS20240101004', 'customer_service', 2, '赖武浩', 'text', '您好！我们最近推出了几款新产品，我来为您详细介绍一下。', '2024-01-15 08:31:00'),
('CS20240101004', 'user', 1004, '赵小强', 'text', '主要想了解价格和功能', '2024-01-15 08:32:20'),
('CS20240101004', 'customer_service', 2, '赖武浩', 'text', '这个需要我们的产品专家来为您详细介绍，我现在为您转接到产品咨询部门。', '2024-01-15 08:33:45'),
('CS20240101004', 'system', NULL, '系统', 'system', '会话已转接至产品咨询部门', '2024-01-15 08:34:00'),
('CS20240101004', 'customer_service', 1, '姚家荣', 'text', '您好！我是产品部的姚经理，很高兴为您介绍我们的新产品。', '2024-01-15 08:35:00'),
('CS20240101004', 'user', 1004, '赵小强', 'text', '好的，请介绍一下', '2024-01-15 08:35:30'),
('CS20240101004', 'system', NULL, '系统', 'system', '用户对本次服务进行了评价：4星 - 转接及时，但等待时间稍长', '2024-01-15 09:00:00');
