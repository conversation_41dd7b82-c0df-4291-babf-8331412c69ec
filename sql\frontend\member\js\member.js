/**
 * 会员中心交互脚本
 */

// 显示开通会员弹窗
function showUpgradeModal() {
    const modal = document.getElementById('upgradeModal');
    if (modal) {
        modal.style.display = 'flex';
        document.body.style.overflow = 'hidden';
        
        // 添加显示动画
        setTimeout(() => {
            modal.classList.add('show');
        }, 10);
    }
}

// 隐藏开通会员弹窗
function hideUpgradeModal() {
    const modal = document.getElementById('upgradeModal');
    if (modal) {
        modal.classList.remove('show');
        
        setTimeout(() => {
            modal.style.display = 'none';
            document.body.style.overflow = '';
        }, 300);
    }
}

// 选择套餐
function selectPackage(packageType) {
    console.log('选择套餐:', packageType);
    
    // 这里可以添加套餐选择逻辑
    let packageName = '';
    let price = '';
    
    switch(packageType) {
        case 'monthly':
            packageName = '月度会员';
            price = '19';
            break;
        case 'yearly':
            packageName = '年度会员';
            price = '199';
            break;
        case 'lifetime':
            packageName = '终身会员';
            price = '999';
            break;
    }
    
    // 显示确认对话框
    if (confirm(`确定要开通${packageName}吗？价格：¥${price}`)) {
        // 这里可以跳转到支付页面或调用支付接口
        showToast(`正在为您开通${packageName}...`);
        
        // 模拟支付过程
        setTimeout(() => {
            showToast('支付功能即将上线，敬请期待！');
        }, 1000);
    }
}

// Toast提示函数
function showToast(message, duration = 3000) {
    // 移除已存在的toast
    const existingToast = document.querySelector('.toast');
    if (existingToast) {
        existingToast.remove();
    }
    
    // 创建新的toast
    const toast = document.createElement('div');
    toast.className = 'toast';
    toast.textContent = message;
    
    // 添加样式
    toast.style.cssText = `
        position: fixed;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        background: var(--bg-card);
        color: var(--text-primary);
        padding: 16px 24px;
        border-radius: 12px;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.5);
        z-index: 10000;
        font-size: 14px;
        font-weight: 500;
        max-width: 90%;
        text-align: center;
        border: 1px solid var(--border-color);
        opacity: 0;
        transition: opacity 0.3s ease;
    `;
    
    document.body.appendChild(toast);
    
    // 显示动画
    setTimeout(() => {
        toast.style.opacity = '1';
    }, 10);
    
    // 自动隐藏
    setTimeout(() => {
        toast.style.opacity = '0';
        setTimeout(() => {
            if (toast.parentNode) {
                toast.parentNode.removeChild(toast);
            }
        }, 300);
    }, duration);
}

// 权益项点击效果
function initBenefitItems() {
    const benefitItems = document.querySelectorAll('.benefit-item');
    
    benefitItems.forEach(item => {
        item.addEventListener('click', function() {
            const title = this.querySelector('.benefit-title').textContent;
            const isActive = this.querySelector('.benefit-status').classList.contains('active');
            
            if (isActive) {
                showToast(`${title}权益已激活`);
            } else {
                showToast(`开通会员即可享受${title}权益`);
            }
            
            // 添加点击动画
            this.style.transform = 'scale(0.98)';
            setTimeout(() => {
                this.style.transform = '';
            }, 150);
        });
    });
}

// 套餐卡片悬停效果
function initPackageItems() {
    const packageItems = document.querySelectorAll('.package-item');
    
    packageItems.forEach(item => {
        item.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-4px)';
        });
        
        item.addEventListener('mouseleave', function() {
            this.style.transform = '';
        });
    });
}

// 返回按钮功能
function initBackButton() {
    const backBtn = document.querySelector('.back-btn');
    if (backBtn) {
        backBtn.addEventListener('click', function(e) {
            e.preventDefault();
            
            // 添加点击动画
            this.style.transform = 'scale(0.9)';
            setTimeout(() => {
                this.style.transform = '';
                // 返回上一页
                window.history.back();
            }, 150);
        });
    }
}

// 帮助按钮功能
function initHelpButton() {
    const helpBtn = document.querySelector('.help-btn');
    if (helpBtn) {
        helpBtn.addEventListener('click', function(e) {
            e.preventDefault();
            showToast('会员帮助功能即将上线');
        });
    }
}

// 弹窗外部点击关闭
function initModalClose() {
    const modal = document.getElementById('upgradeModal');
    if (modal) {
        modal.addEventListener('click', function(e) {
            if (e.target === this) {
                hideUpgradeModal();
            }
        });
    }
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    console.log('会员中心页面加载完成');
    
    // 初始化各种交互功能
    initBenefitItems();
    initPackageItems();
    initBackButton();
    initHelpButton();
    initModalClose();
    
    // 添加页面加载动画
    const elements = document.querySelectorAll('.member-status-card, .benefits-section, .packages-section');
    elements.forEach((element, index) => {
        element.style.opacity = '0';
        element.style.transform = 'translateY(20px)';
        
        setTimeout(() => {
            element.style.transition = 'all 0.6s ease';
            element.style.opacity = '1';
            element.style.transform = 'translateY(0)';
        }, index * 100);
    });
    
    console.log('会员中心交互功能初始化完成');
});

// 键盘事件处理
document.addEventListener('keydown', function(e) {
    // ESC键关闭弹窗
    if (e.key === 'Escape') {
        hideUpgradeModal();
    }
});

// 防止页面滚动时的性能问题
let ticking = false;

function updateScrollEffects() {
    const scrollY = window.scrollY;
    const header = document.querySelector('.member-header');
    
    if (header) {
        if (scrollY > 50) {
            header.style.boxShadow = '0 2px 20px rgba(0, 0, 0, 0.3)';
        } else {
            header.style.boxShadow = '';
        }
    }
    
    ticking = false;
}

window.addEventListener('scroll', function() {
    if (!ticking) {
        requestAnimationFrame(updateScrollEffects);
        ticking = true;
    }
});

// 导出函数供全局使用
window.memberCenter = {
    showUpgradeModal,
    hideUpgradeModal,
    selectPackage,
    showToast
};
