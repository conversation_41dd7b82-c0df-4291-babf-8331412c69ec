/* 页面防护样式 */

/* 禁用页面缩放和水平滚动 */
html, body {
    /* 禁用水平滚动 */
    overflow-x: hidden !important;
    
    /* 禁用缩放 */
    touch-action: pan-y !important;
    -webkit-touch-callout: none !important;
    -webkit-user-select: none !important;
    -khtml-user-select: none !important;
    -moz-user-select: none !important;
    -ms-user-select: none !important;
    user-select: none !important;
    
    /* 禁用拖拽 */
    -webkit-user-drag: none !important;
    -khtml-user-drag: none !important;
    -moz-user-drag: none !important;
    -o-user-drag: none !important;
    user-drag: none !important;
    
    /* 禁用右键菜单 */
    -webkit-context-menu: none !important;
    
    /* 固定宽度，防止水平滚动 */
    width: 100% !important;
    max-width: 100% !important;
    min-width: 100% !important;
    
    /* 禁用双击缩放 */
    -webkit-text-size-adjust: 100% !important;
    -ms-text-size-adjust: 100% !important;
    text-size-adjust: 100% !important;
}

/* 禁用所有元素的选择和拖拽 */
* {
    -webkit-user-select: none !important;
    -moz-user-select: none !important;
    -ms-user-select: none !important;
    user-select: none !important;
    
    -webkit-user-drag: none !important;
    -khtml-user-drag: none !important;
    -moz-user-drag: none !important;
    -o-user-drag: none !important;
    user-drag: none !important;
    
    /* 禁用长按选择 */
    -webkit-touch-callout: none !important;
    -webkit-tap-highlight-color: transparent !important;
}

/* 允许输入框的文本选择和输入 */
input[type="text"],
input[type="password"],
input[type="email"],
input[type="tel"],
input[type="number"],
textarea {
    -webkit-user-select: text !important;
    -moz-user-select: text !important;
    -ms-user-select: text !important;
    user-select: text !important;
    
    /* 但仍然禁用拖拽 */
    -webkit-user-drag: none !important;
    -khtml-user-drag: none !important;
    -moz-user-drag: none !important;
    -o-user-drag: none !important;
    user-drag: none !important;
}

/* 禁用图片的拖拽和保存 */
img {
    -webkit-user-drag: none !important;
    -khtml-user-drag: none !important;
    -moz-user-drag: none !important;
    -o-user-drag: none !important;
    user-drag: none !important;
    
    -webkit-user-select: none !important;
    -moz-user-select: none !important;
    -ms-user-select: none !important;
    user-select: none !important;
    
    /* 禁用右键保存 */
    pointer-events: none !important;
}

/* 但允许可点击的图片（如按钮）响应点击 */
img.clickable,
.avatar-preview img,
.camera-icon img,
button img,
.btn img {
    pointer-events: auto !important;
}

/* 禁用文本选择高亮 */
::selection {
    background: transparent !important;
}

::-moz-selection {
    background: transparent !important;
}

/* 禁用长按菜单 */
*:not(input):not(textarea) {
    -webkit-touch-callout: none !important;
    -webkit-user-select: none !important;
}

/* 防止页面被拖拽 */
body {
    -webkit-app-region: no-drag !important;
}

/* 禁用双指缩放 */
.container,
.page-container,
.main-container {
    touch-action: pan-y !important;
    -ms-touch-action: pan-y !important;
}

/* 移动端特殊处理 */
@media (max-width: 768px) {
    html {
        /* 禁用双击缩放 */
        -webkit-text-size-adjust: none !important;
        -ms-text-size-adjust: none !important;
        text-size-adjust: none !important;
    }
    
    body {
        /* 禁用弹性滚动 */
        -webkit-overflow-scrolling: auto !important;
        overflow-scrolling: auto !important;
    }
    
    /* 禁用iOS Safari的双击缩放 */
    * {
        -webkit-touch-callout: none !important;
        -webkit-tap-highlight-color: rgba(0,0,0,0) !important;
    }
}

/* 确保容器不会超出屏幕宽度 */
.container,
.page-container,
.main-container,
.content {
    max-width: 100vw !important;
    overflow-x: hidden !important;
    box-sizing: border-box !important;
}

/* 禁用开发者工具快捷键提示 */
.no-select {
    -webkit-user-select: none !important;
    -moz-user-select: none !important;
    -ms-user-select: none !important;
    user-select: none !important;
}

/* 隐藏滚动条但保持滚动功能 */
::-webkit-scrollbar {
    width: 0px !important;
    background: transparent !important;
}

/* Firefox */
html {
    scrollbar-width: none !important;
}

/* IE/Edge */
body {
    -ms-overflow-style: none !important;
}
