-- 创建用户钱包表
CREATE TABLE IF NOT EXISTS `user_wallets` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `balance` decimal(10,2) NOT NULL DEFAULT 0.00 COMMENT '可用余额',
  `frozen_balance` decimal(10,2) NOT NULL DEFAULT 0.00 COMMENT '冻结金额',
  `total_recharge` decimal(10,2) NOT NULL DEFAULT 0.00 COMMENT '累计充值',
  `total_withdraw` decimal(10,2) NOT NULL DEFAULT 0.00 COMMENT '累计提现',
  `total_consumption` decimal(10,2) NOT NULL DEFAULT 0.00 COMMENT '累计消费',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `user_id` (`user_id`),
  CONSTRAINT `user_wallets_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户钱包表';

-- 创建用户会员表
CREATE TABLE IF NOT EXISTS `user_memberships` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `level` enum('normal','vip','svip') NOT NULL DEFAULT 'normal' COMMENT '会员等级',
  `start_time` timestamp NULL DEFAULT NULL COMMENT '开始时间',
  `expire_time` timestamp NULL DEFAULT NULL COMMENT '到期时间',
  `total_days` int(11) NOT NULL DEFAULT 0 COMMENT '累计天数',
  `auto_renew` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否自动续费',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `user_id` (`user_id`),
  CONSTRAINT `user_memberships_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户会员表';

-- 创建用户作品表
CREATE TABLE IF NOT EXISTS `user_works` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `title` varchar(255) NOT NULL COMMENT '作品标题',
  `description` text DEFAULT NULL COMMENT '作品描述',
  `cover_image` varchar(255) DEFAULT NULL COMMENT '封面图片',
  `content_type` enum('image','video','audio','text') NOT NULL COMMENT '内容类型',
  `content_url` varchar(255) DEFAULT NULL COMMENT '内容URL',
  `status` enum('draft','published','deleted') NOT NULL DEFAULT 'draft' COMMENT '状态',
  `views` int(11) NOT NULL DEFAULT 0 COMMENT '浏览次数',
  `likes` int(11) NOT NULL DEFAULT 0 COMMENT '点赞数',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `user_id` (`user_id`),
  CONSTRAINT `user_works_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户作品表';

-- 创建用户活动表
CREATE TABLE IF NOT EXISTS `user_events` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `title` varchar(255) NOT NULL COMMENT '活动标题',
  `description` text DEFAULT NULL COMMENT '活动描述',
  `event_type` enum('game','travel','social','other') NOT NULL COMMENT '活动类型',
  `location` varchar(255) DEFAULT NULL COMMENT '活动地点',
  `start_time` timestamp NULL DEFAULT NULL COMMENT '开始时间',
  `end_time` timestamp NULL DEFAULT NULL COMMENT '结束时间',
  `max_participants` int(11) DEFAULT NULL COMMENT '最大参与人数',
  `current_participants` int(11) NOT NULL DEFAULT 0 COMMENT '当前参与人数',
  `status` enum('draft','published','ongoing','completed','cancelled') NOT NULL DEFAULT 'draft' COMMENT '状态',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `user_id` (`user_id`),
  CONSTRAINT `user_events_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户活动表';

-- 创建用户订单表
CREATE TABLE IF NOT EXISTS `user_orders` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `order_no` varchar(32) NOT NULL COMMENT '订单号',
  `order_type` enum('membership','recharge','product','service') NOT NULL COMMENT '订单类型',
  `title` varchar(255) NOT NULL COMMENT '订单标题',
  `amount` decimal(10,2) NOT NULL COMMENT '订单金额',
  `status` enum('pending','paid','cancelled','refunded') NOT NULL DEFAULT 'pending' COMMENT '订单状态',
  `payment_method` varchar(50) DEFAULT NULL COMMENT '支付方式',
  `payment_time` timestamp NULL DEFAULT NULL COMMENT '支付时间',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `order_no` (`order_no`),
  KEY `user_id` (`user_id`),
  CONSTRAINT `user_orders_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户订单表';

-- 创建用户禁言记录表
CREATE TABLE IF NOT EXISTS `user_mutes` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `admin_id` int(11) NOT NULL COMMENT '操作管理员ID',
  `admin_name` varchar(50) NOT NULL COMMENT '操作管理员姓名',
  `reason` text NOT NULL COMMENT '禁言原因',
  `start_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '开始时间',
  `end_time` timestamp NOT NULL COMMENT '结束时间',
  `status` enum('active','expired','cancelled') NOT NULL DEFAULT 'active' COMMENT '状态',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `user_id` (`user_id`),
  CONSTRAINT `user_mutes_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户禁言记录表';

-- 创建用户日志表（用于记录自定义日志）
CREATE TABLE IF NOT EXISTS `user_logs` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `operator_name` varchar(50) NOT NULL COMMENT '操作员姓名',
  `employee_id` varchar(20) DEFAULT NULL COMMENT '工号',
  `department` varchar(50) DEFAULT NULL COMMENT '部门',
  `type` varchar(50) NOT NULL COMMENT '日志类型',
  `content` text NOT NULL COMMENT '日志内容',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `user_id` (`user_id`),
  CONSTRAINT `user_logs_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户日志表';

-- 为现有的users表添加bio字段（如果不存在）
ALTER TABLE `users` ADD COLUMN IF NOT EXISTS `bio` text DEFAULT NULL COMMENT '个人简介';

-- 插入一些测试数据
INSERT IGNORE INTO `user_wallets` (`user_id`, `balance`, `frozen_balance`, `total_recharge`, `total_withdraw`, `total_consumption`) VALUES
(1, 1288.50, 0.00, 2000.00, 500.00, 211.50),
(2, 566.80, 100.00, 800.00, 0.00, 133.20),
(3, 0.00, 0.00, 0.00, 0.00, 0.00);

INSERT IGNORE INTO `user_memberships` (`user_id`, `level`, `start_time`, `expire_time`, `total_days`) VALUES
(1, 'vip', '2024-01-01 00:00:00', '2024-12-31 23:59:59', 365),
(2, 'normal', NULL, NULL, 0),
(3, 'svip', '2024-06-01 00:00:00', '2024-12-31 23:59:59', 214);

-- 创建IP封禁表
CREATE TABLE IF NOT EXISTS `banned_ips` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `ip_address` varchar(45) NOT NULL COMMENT 'IP地址',
  `admin_id` int(11) NOT NULL COMMENT '操作管理员ID',
  `admin_name` varchar(50) NOT NULL COMMENT '操作管理员姓名',
  `reason` text NOT NULL COMMENT '封禁原因',
  `status` enum('active','inactive') NOT NULL DEFAULT 'active' COMMENT '状态',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `ip_address` (`ip_address`),
  KEY `status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='IP封禁表';

-- 创建用户作品表
CREATE TABLE IF NOT EXISTS `user_works` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `title` varchar(200) NOT NULL COMMENT '作品标题',
  `description` text COMMENT '作品描述',
  `content_type` enum('image','video','audio','text') NOT NULL DEFAULT 'image' COMMENT '内容类型',
  `cover_image` varchar(500) COMMENT '封面图片',
  `content_url` varchar(500) COMMENT '内容链接',
  `status` enum('draft','published','deleted') NOT NULL DEFAULT 'draft' COMMENT '状态',
  `views` int(11) NOT NULL DEFAULT 0 COMMENT '浏览量',
  `likes` int(11) NOT NULL DEFAULT 0 COMMENT '点赞数',
  `comments` int(11) NOT NULL DEFAULT 0 COMMENT '评论数',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `user_id` (`user_id`),
  KEY `status` (`status`),
  KEY `created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户作品表';

-- 创建用户组局活动表
CREATE TABLE IF NOT EXISTS `user_events` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `organizer_id` int(11) NOT NULL COMMENT '组织者用户ID',
  `title` varchar(200) NOT NULL COMMENT '活动标题',
  `description` text COMMENT '活动描述',
  `event_type` enum('game','travel','social','other') NOT NULL DEFAULT 'other' COMMENT '活动类型',
  `location` varchar(200) COMMENT '活动地点',
  `start_time` datetime COMMENT '开始时间',
  `end_time` datetime COMMENT '结束时间',
  `max_participants` int(11) COMMENT '最大参与人数',
  `current_participants` int(11) NOT NULL DEFAULT 0 COMMENT '当前参与人数',
  `status` enum('draft','published','ongoing','completed','cancelled') NOT NULL DEFAULT 'draft' COMMENT '状态',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `organizer_id` (`organizer_id`),
  KEY `status` (`status`),
  KEY `start_time` (`start_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户组局活动表';


