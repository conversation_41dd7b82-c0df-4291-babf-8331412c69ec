-- 用户详情页面新增功能所需的数据库表和字段
-- 执行前请备份数据库

-- 1. 为users表添加新字段
ALTER TABLE `users` ADD COLUMN IF NOT EXISTS `level` int(11) NOT NULL DEFAULT 1 COMMENT '用户等级';
ALTER TABLE `users` ADD COLUMN IF NOT EXISTS `experience` int(11) NOT NULL DEFAULT 0 COMMENT '用户经验值';
ALTER TABLE `users` ADD COLUMN IF NOT EXISTS `points` int(11) NOT NULL DEFAULT 0 COMMENT '用户积分';
ALTER TABLE `users` ADD COLUMN IF NOT EXISTS `followers_count` int(11) NOT NULL DEFAULT 0 COMMENT '粉丝数';
ALTER TABLE `users` ADD COLUMN IF NOT EXISTS `following_count` int(11) NOT NULL DEFAULT 0 COMMENT '关注数';
ALTER TABLE `users` ADD COLUMN IF NOT EXISTS `likes_received` int(11) NOT NULL DEFAULT 0 COMMENT '获赞数';
ALTER TABLE `users` ADD COLUMN IF NOT EXISTS `favorites_count` int(11) NOT NULL DEFAULT 0 COMMENT '收藏数';
ALTER TABLE `users` ADD COLUMN IF NOT EXISTS `is_companion` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否是陪玩';
ALTER TABLE `users` ADD COLUMN IF NOT EXISTS `total_earnings` decimal(10,2) NOT NULL DEFAULT 0.00 COMMENT '总收益';

-- 2. 创建用户勋章表
CREATE TABLE IF NOT EXISTS `user_badges` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `badge_type` varchar(50) NOT NULL COMMENT '勋章类型',
  `badge_name` varchar(100) NOT NULL COMMENT '勋章名称',
  `badge_icon` varchar(255) DEFAULT NULL COMMENT '勋章图标',
  `badge_description` text DEFAULT NULL COMMENT '勋章描述',
  `earned_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '获得时间',
  `is_displayed` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否显示',
  PRIMARY KEY (`id`),
  KEY `user_id` (`user_id`),
  KEY `badge_type` (`badge_type`),
  CONSTRAINT `user_badges_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户勋章表';

-- 3. 创建用户背包表
CREATE TABLE IF NOT EXISTS `user_inventory` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `item_type` enum('prop','gift','decoration','other') NOT NULL COMMENT '物品类型',
  `item_name` varchar(100) NOT NULL COMMENT '物品名称',
  `item_icon` varchar(255) DEFAULT NULL COMMENT '物品图标',
  `item_description` text DEFAULT NULL COMMENT '物品描述',
  `quantity` int(11) NOT NULL DEFAULT 1 COMMENT '数量',
  `obtained_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '获得时间',
  `expires_at` timestamp NULL DEFAULT NULL COMMENT '过期时间',
  `status` enum('active','expired','used') NOT NULL DEFAULT 'active' COMMENT '状态',
  PRIMARY KEY (`id`),
  KEY `user_id` (`user_id`),
  KEY `item_type` (`item_type`),
  CONSTRAINT `user_inventory_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户背包表';

-- 4. 创建用户签到表
CREATE TABLE IF NOT EXISTS `user_checkins` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `checkin_date` date NOT NULL COMMENT '签到日期',
  `consecutive_days` int(11) NOT NULL DEFAULT 1 COMMENT '连续签到天数',
  `points_earned` int(11) NOT NULL DEFAULT 0 COMMENT '获得积分',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '签到时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `user_date` (`user_id`, `checkin_date`),
  KEY `user_id` (`user_id`),
  CONSTRAINT `user_checkins_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户签到表';

-- 5. 创建用户关注表
CREATE TABLE IF NOT EXISTS `user_follows` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `follower_id` int(11) NOT NULL COMMENT '关注者ID',
  `following_id` int(11) NOT NULL COMMENT '被关注者ID',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '关注时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `follow_unique` (`follower_id`, `following_id`),
  KEY `follower_id` (`follower_id`),
  KEY `following_id` (`following_id`),
  CONSTRAINT `user_follows_ibfk_1` FOREIGN KEY (`follower_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  CONSTRAINT `user_follows_ibfk_2` FOREIGN KEY (`following_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户关注表';

-- 6. 创建用户收藏表
CREATE TABLE IF NOT EXISTS `user_favorites` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `target_type` enum('work','event','user','other') NOT NULL COMMENT '收藏类型',
  `target_id` int(11) NOT NULL COMMENT '目标ID',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '收藏时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `favorite_unique` (`user_id`, `target_type`, `target_id`),
  KEY `user_id` (`user_id`),
  CONSTRAINT `user_favorites_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户收藏表';

-- 7. 创建用户收益记录表
CREATE TABLE IF NOT EXISTS `user_earnings` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `earning_type` enum('work','event','referral','bonus','other') NOT NULL COMMENT '收益类型',
  `amount` decimal(10,2) NOT NULL COMMENT '收益金额',
  `description` varchar(255) DEFAULT NULL COMMENT '收益描述',
  `source_id` int(11) DEFAULT NULL COMMENT '来源ID',
  `status` enum('pending','confirmed','cancelled') NOT NULL DEFAULT 'pending' COMMENT '状态',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `confirmed_at` timestamp NULL DEFAULT NULL COMMENT '确认时间',
  PRIMARY KEY (`id`),
  KEY `user_id` (`user_id`),
  KEY `earning_type` (`earning_type`),
  CONSTRAINT `user_earnings_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户收益记录表';

-- 8. 创建系统消息表
CREATE TABLE IF NOT EXISTS `system_messages` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `title` varchar(255) NOT NULL COMMENT '消息标题',
  `content` text NOT NULL COMMENT '消息内容',
  `message_type` enum('system','notification','warning','promotion') NOT NULL DEFAULT 'system' COMMENT '消息类型',
  `is_read` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否已读',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `read_at` timestamp NULL DEFAULT NULL COMMENT '阅读时间',
  PRIMARY KEY (`id`),
  KEY `user_id` (`user_id`),
  KEY `is_read` (`is_read`),
  CONSTRAINT `system_messages_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='系统消息表';

-- 9. 创建短信发送记录表
CREATE TABLE IF NOT EXISTS `sms_logs` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `phone` varchar(20) NOT NULL COMMENT '手机号',
  `content` text NOT NULL COMMENT '短信内容',
  `sms_type` enum('verification','notification','marketing','system') NOT NULL COMMENT '短信类型',
  `status` enum('pending','sent','failed','delivered') NOT NULL DEFAULT 'pending' COMMENT '发送状态',
  `admin_id` int(11) DEFAULT NULL COMMENT '发送管理员ID',
  `admin_name` varchar(50) DEFAULT NULL COMMENT '发送管理员姓名',
  `sent_at` timestamp NULL DEFAULT NULL COMMENT '发送时间',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `user_id` (`user_id`),
  KEY `phone` (`phone`),
  CONSTRAINT `sms_logs_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='短信发送记录表';

-- 10. 创建用户等级配置表
CREATE TABLE IF NOT EXISTS `user_level_config` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `level` int(11) NOT NULL COMMENT '等级',
  `level_name` varchar(50) NOT NULL COMMENT '等级名称',
  `required_experience` int(11) NOT NULL COMMENT '所需经验值',
  `level_icon` varchar(255) DEFAULT NULL COMMENT '等级图标',
  `privileges` json DEFAULT NULL COMMENT '等级特权',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `level` (`level`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户等级配置表';

-- 11. 插入初始数据

-- 用户等级配置初始数据
INSERT IGNORE INTO `user_level_config` (`level`, `level_name`, `required_experience`, `level_icon`, `privileges`) VALUES
(1, '新手', 0, '🌱', '{"daily_points": 10, "max_works": 5}'),
(2, '初级', 100, '🌿', '{"daily_points": 15, "max_works": 10}'),
(3, '中级', 300, '🍀', '{"daily_points": 20, "max_works": 20}'),
(4, '高级', 600, '🌳', '{"daily_points": 25, "max_works": 50}'),
(5, '专家', 1000, '🏆', '{"daily_points": 30, "max_works": 100}'),
(6, '大师', 1500, '👑', '{"daily_points": 50, "max_works": 200}');

-- 勋章类型初始数据
INSERT IGNORE INTO `user_badges` (`user_id`, `badge_type`, `badge_name`, `badge_icon`, `badge_description`) VALUES
(1, 'newcomer', '新人勋章', '🎉', '欢迎加入趣玩星球'),
(1, 'companion', '陪玩勋章', '🎮', '认证陪玩用户'),
(1, 'creator', '创作者', '✨', '优秀内容创作者');

-- 背包物品初始数据
INSERT IGNORE INTO `user_inventory` (`user_id`, `item_type`, `item_name`, `item_icon`, `item_description`, `quantity`) VALUES
(1, 'prop', '经验加速卡', '⚡', '使用后获得双倍经验，持续24小时', 3),
(1, 'gift', '鲜花', '🌹', '送给喜欢的用户表达心意', 10),
(1, 'decoration', '个性边框', '🖼️', '让你的头像更加个性化', 1);

-- 签到记录初始数据
INSERT IGNORE INTO `user_checkins` (`user_id`, `checkin_date`, `consecutive_days`, `points_earned`) VALUES
(1, CURDATE(), 1, 10),
(1, DATE_SUB(CURDATE(), INTERVAL 1 DAY), 1, 10);

-- 收益记录初始数据
INSERT IGNORE INTO `user_earnings` (`user_id`, `earning_type`, `amount`, `description`, `status`, `confirmed_at`) VALUES
(1, 'work', 50.00, '作品《美丽风景》获得打赏', 'confirmed', NOW()),
(1, 'event', 30.00, '参与活动获得奖励', 'confirmed', NOW()),
(1, 'bonus', 20.00, '新用户注册奖励', 'confirmed', NOW());

-- 系统消息初始数据
INSERT IGNORE INTO `system_messages` (`user_id`, `title`, `content`, `message_type`) VALUES
(1, '欢迎加入趣玩星球', '欢迎您加入趣玩星球大家庭！在这里您可以分享生活、结交朋友、参与有趣的活动。', 'system'),
(1, '实名认证提醒', '为了更好地保护您的账户安全，建议您完成实名认证。', 'notification');

-- 12. 创建管理员操作日志表（独立的日志功能）
CREATE TABLE IF NOT EXISTS `admin_operation_logs` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL COMMENT '目标用户ID',
  `admin_id` int(11) NOT NULL COMMENT '操作管理员ID',
  `admin_name` varchar(50) NOT NULL COMMENT '管理员姓名',
  `employee_id` varchar(20) DEFAULT NULL COMMENT '工号',
  `department` varchar(50) DEFAULT NULL COMMENT '部门',
  `operation_type` varchar(50) NOT NULL COMMENT '操作类型',
  `operation_content` text NOT NULL COMMENT '操作内容',
  `operation_reason` text DEFAULT NULL COMMENT '操作原因',
  `ip_address` varchar(45) DEFAULT NULL COMMENT '操作IP',
  `user_agent` text DEFAULT NULL COMMENT '用户代理',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '操作时间',
  PRIMARY KEY (`id`),
  KEY `user_id` (`user_id`),
  KEY `admin_id` (`admin_id`),
  KEY `operation_type` (`operation_type`),
  KEY `created_at` (`created_at`),
  CONSTRAINT `admin_operation_logs_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='管理员操作日志表';

-- 插入一些示例操作日志
INSERT IGNORE INTO `admin_operation_logs` (`user_id`, `admin_id`, `admin_name`, `employee_id`, `department`, `operation_type`, `operation_content`, `operation_reason`, `ip_address`) VALUES
(1, 1, '张三', '12001', '用户管理部', '查看用户详情', '查看了用户详细信息', '日常检查', '*************'),
(1, 1, '张三', '12001', '用户管理部', '编辑用户信息', '修改了用户的邮箱地址', '用户申请修改', '*************');
