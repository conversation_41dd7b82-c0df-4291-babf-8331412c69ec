<?php
require_once __DIR__ . '/../auth_check.php';
require_once __DIR__ . '/../db_config.php';

$page_title = '审核陪玩申请详情';
$current_page = 'companion_review'; // For menu highlighting, same as list page

$application_id = filter_input(INPUT_GET, 'id', FILTER_VALIDATE_INT);
$application = null;
$user_info = null;
$error_message = '';
$success_message = '';

if (!$application_id) {
    $error_message = '无效的申请ID。';
} else {
    $pdo = null;
    try {
        $dsn = "mysql:host={$db_config['host']};dbname={$db_config['dbname']};charset={$db_config['charset']};port={$db_config['port']}";
        $options = [PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION, PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC];
        $pdo = new PDO($dsn, $db_config['username'], $db_config['password'], $options);

        // Handle form submission for approve/reject
        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            $action = $_POST['action'] ?? '';
            $rejection_reason = trim($_POST['rejection_reason'] ?? '');
            $new_status = '';

            if ($action === 'approve') {
                $new_status = 'approved';
            } elseif ($action === 'reject') {
                $new_status = 'rejected';
                if (empty($rejection_reason) && $new_status === 'rejected') {
                    // Optional: make rejection reason mandatory for rejection
                    // $error_message = '拒绝申请时必须填写理由。'; 
                }
            }

            if ($new_status && (empty($error_message))) { // Proceed if status is set and no prior error
                $pdo->beginTransaction();

                // Update companion_applications table
                $stmt_app = $pdo->prepare("UPDATE companion_applications SET status = :status, rejection_reason = :rejection_reason, reviewed_by = :admin_id, reviewed_at = NOW() WHERE id = :id");
                $stmt_app->execute([
                    ':status' => $new_status,
                    ':rejection_reason' => ($new_status === 'rejected' ? $rejection_reason : null),
                    ':admin_id' => $_SESSION['admin_id'], // Assuming admin_id is stored in session
                    ':id' => $application_id
                ]);

                // Get user_id from application to update companion_verification
                $stmt_get_user = $pdo->prepare("SELECT user_id FROM companion_applications WHERE id = :id");
                $stmt_get_user->execute([':id' => $application_id]);
                $app_user_id = $stmt_get_user->fetchColumn();

                if ($app_user_id) {
                    // Update companion_verification table
                    $stmt_cv = $pdo->prepare("UPDATE companion_verification SET verification_status = :status, last_updated = NOW() WHERE user_id = :user_id AND application_id = :application_id");
                    $stmt_cv->execute([
                        ':status' => $new_status,
                        ':user_id' => $app_user_id,
                        ':application_id' => $application_id
                    ]);
                }
                $pdo->commit();
                $success_message = '申请状态已成功更新为：' . htmlspecialchars($new_status) . '。';
            } else if (!$new_status && empty($error_message)) {
                $error_message = '无效的操作。';
            }
        }

        // Fetch application details
        $stmt = $pdo->prepare("
            SELECT ca.*, u.username, u.email AS user_email 
            FROM companion_applications ca 
            JOIN users u ON ca.user_id = u.id 
            WHERE ca.id = :id
        ");
        $stmt->execute([':id' => $application_id]);
        $application = $stmt->fetch();

        if (!$application) {
            $error_message = '未找到指定的申请记录。';
        }

    } catch (PDOException $e) {
        if ($pdo && $pdo->inTransaction()) {
            $pdo->rollBack();
        }
        error_log('Error processing application detail/action: ' . $e->getMessage());
        $error_message = '数据库操作失败：' . $e->getMessage();
    } catch (Exception $e) {
        error_log('General error on detail page: ' . $e->getMessage());
        $error_message = '发生未知错误。';
    }
}

include __DIR__ . '/../admin_layout.php'; // Start admin page, includes header and sidebar

function display_file_link($file_path_from_db, $label) {
    if (empty($file_path_from_db)) {
        return '<span class="text-muted">未提供</span>';
    }
    // Assuming $file_path_from_db is like 'uploads/companion_applications/user_X/filename.jpg'
    // And the admin site is at 'houtai_backup', so need to go up to root.
    $web_path = '../../' . $file_path_from_db; 
    return '<a href="' . htmlspecialchars($web_path) . '" target="_blank">' . htmlspecialchars($label) . ' <i class="fa fa-external-link"></i></a>';
}

?>
<div class="content-wrapper">
    <section class="content-header">
        <h1><?php echo htmlspecialchars($page_title); ?></h1>
    </section>

    <section class="content">
        <?php if ($success_message): ?>
            <div class="alert alert-success"><?php echo htmlspecialchars($success_message); ?></div>
        <?php endif; ?>
        <?php if ($error_message): ?>
            <div class="alert alert-danger"><?php echo htmlspecialchars($error_message); ?></div>
        <?php endif; ?>

        <?php if ($application && !$error_message): ?>
            <div class="box box-primary">
                <div class="box-header with-border">
                    <h3 class="box-title">申请详情 (ID: <?php echo htmlspecialchars($application['id']); ?>)</h3>
                </div>
                <div class="box-body">
                    <dl class="dl-horizontal">
                        <dt>用户ID</dt>
                        <dd><?php echo htmlspecialchars($application['user_id']); ?></dd>
                        <dt>用户名</dt>
                        <dd><?php echo htmlspecialchars($application['username']); ?></dd>
                        <dt>用户邮箱</dt>
                        <dd><?php echo htmlspecialchars($application['user_email'] ?? 'N/A'); ?></dd>
                        <dt>真实姓名</dt>
                        <dd><?php echo htmlspecialchars($application['real_name']); ?></dd>
                        <dt>身份证号</dt>
                        <dd><?php echo htmlspecialchars($application['id_number']); ?></dd>
                        <dt>申请类型</dt>
                        <dd>
                            <?php 
                                if ($application['application_type'] === 'game') echo '游戏陪玩';
                                elseif ($application['application_type'] === 'city') echo '城市陪玩';
                                else echo htmlspecialchars($application['application_type'] ?? 'N/A'); 
                            ?>
                        </dd>
                        <dt>联系方式</dt>
                        <dd><?php echo htmlspecialchars($application['contact_info'] ?? '未提供'); ?></dd>
                        <dt>类型详情</dt>
                        <dd>
                            <?php 
                            if ($application['application_type'] === 'game') {
                                echo nl2br(htmlspecialchars($application['game_interests'] ?? '未提供'));
                            } elseif ($application['application_type'] === 'city' && !empty($application['type_specific_details'])) {
                                $details = json_decode($application['type_specific_details'], true);
                                if ($details) {
                                    echo '<strong>服务城市:</strong> ' . htmlspecialchars($details['service_city'] ?? 'N/A') . '<br>';
                                    echo '<strong>服务项目:</strong> ' . nl2br(htmlspecialchars($details['service_items'] ?? 'N/A'));
                                } else {
                                    echo '无法解析城市详情';
                                }
                            } elseif (!empty($application['game_interests'])) { // Fallback for older data
                                echo nl2br(htmlspecialchars($application['game_interests'] ?? '未提供'));
                            } else {
                                echo '未提供';
                            }
                            ?>
                        </dd>
                        <dt>自我介绍</dt>
                        <dd><?php echo nl2br(htmlspecialchars($application['self_description'])); ?></dd>
                        <dt>身份证正面</dt>
                        <dd><?php echo display_file_link($application['id_card_front_url'], '查看图片'); ?></dd>
                        <dt>身份证背面</dt>
                        <dd><?php echo display_file_link($application['id_card_back_url'], '查看图片'); ?></dd>
                        <dt>语音介绍</dt>
                        <dd><?php echo display_file_link($application['voice_sample_url'], '播放语音'); ?></dd>
                        <dt>申请状态</dt>
                        <dd><span class="label label-<?php echo $application['status'] === 'approved' ? 'success' : ($application['status'] === 'rejected' ? 'danger' : 'warning'); ?>"><?php echo htmlspecialchars($application['status']); ?></span></dd>
                        <dt>申请日期</dt>
                        <dd><?php echo htmlspecialchars(date('Y-m-d H:i', strtotime($application['application_date']))); ?></dd>
                        <?php if ($application['reviewed_at']): ?>
                        <dt>审核日期</dt>
                        <dd><?php echo htmlspecialchars(date('Y-m-d H:i', strtotime($application['reviewed_at']))); ?></dd>
                        <dt>审核人ID</dt> <!-- Assuming admin_id is stored, or use admin_name -->
                        <dd><?php echo htmlspecialchars($application['reviewed_by'] ?? 'N/A'); ?></dd>
                        <?php endif; ?>
                        <?php if ($application['status'] === 'rejected' && !empty($application['rejection_reason'])): ?>
                        <dt>拒绝理由</dt>
                        <dd><?php echo nl2br(htmlspecialchars($application['rejection_reason'])); ?></dd>
                        <?php endif; ?>
                    </dl>
                </div>
            </div>

            <?php if ($application['status'] == 'pending'): ?>
            <div class="box box-info">
                <div class="box-header with-border">
                    <h3 class="box-title">审核操作</h3>
                </div>
                <form method="POST" action="companion_application_detail.php?id=<?php echo $application_id; ?>">
                    <div class="box-body">
                        <div class="form-group">
                            <label for="rejection_reason">拒绝理由 (如果选择拒绝)</label>
                            <textarea name="rejection_reason" id="rejection_reason" class="form-control" rows="3"></textarea>
                        </div>
                    </div>
                    <div class="box-footer">
                        <button type="submit" name="action" value="approve" class="btn btn-success">批准申请</button>
                        <button type="submit" name="action" value="reject" class="btn btn-danger">拒绝申请</button>
                        <a href="companion_application_review.php" class="btn btn-default">返回列表</a>
                    </div>
                </form>
            </div>
            <?php else: ?>
            <div class="box-footer">
                 <a href="companion_application_review.php" class="btn btn-default">返回列表</a>
            </div>
            <?php endif; ?>

        <?php elseif (!$error_message): // Should not happen if $application is null and no $error_message, but as a fallback
            echo "<div class='alert alert-warning'>无法加载申请数据。</div>";
        endif; ?>
    </section>
</div>

<?php
// include __DIR__ . '/../admin_footer.php'; // If layout is split
?>
