document.addEventListener('DOMContentLoaded', function() {
    // Toast提示函数
    function showToast(message) {
        const toast = document.getElementById('toast');
        toast.textContent = message;
        toast.style.display = 'block';

        // 添加动画
        toast.style.animation = 'fadeIn 0.3s forwards';

        // 3秒后隐藏
        setTimeout(() => {
            toast.style.animation = 'fadeOut 0.3s forwards';
            setTimeout(() => {
                toast.style.display = 'none';
            }, 300);
        }, 3000);
    }

    // 确保window.showToast也可用
    window.showToast = showToast;

    // 搜索功能
    const searchInput = document.querySelector('.search-bar input');
    if (searchInput) {
        searchInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                const searchTerm = this.value.trim();
                if (searchTerm) {
                    // 获取当前URL参数
                    const urlParams = new URLSearchParams(window.location.search);

                    // 添加或更新搜索参数
                    urlParams.set('search', searchTerm);

                    // 跳转到搜索结果页面
                    window.location.href = `index.php?${urlParams.toString()}`;
                }
            }
        });
    }

    // 分类筛选滚动效果
    const categoryFilter = document.querySelector('.category-filter');
    const subcategoryFilter = document.querySelector('.subcategory-filter');

    // 如果有活跃的分类，滚动到可见区域
    if (categoryFilter) {
        const activeCategory = categoryFilter.querySelector('.filter-item.active');
        if (activeCategory) {
            // 计算滚动位置
            const containerWidth = categoryFilter.offsetWidth;
            const itemLeft = activeCategory.offsetLeft;
            const itemWidth = activeCategory.offsetWidth;

            // 滚动到活跃项目居中的位置
            categoryFilter.scrollLeft = itemLeft - (containerWidth / 2) + (itemWidth / 2);
        }
    }

    // 如果有活跃的子分类，滚动到可见区域
    if (subcategoryFilter) {
        const activeSubcategory = subcategoryFilter.querySelector('.filter-item.active');
        if (activeSubcategory) {
            // 计算滚动位置
            const containerWidth = subcategoryFilter.offsetWidth;
            const itemLeft = activeSubcategory.offsetLeft;
            const itemWidth = activeSubcategory.offsetWidth;

            // 滚动到活跃项目居中的位置
            subcategoryFilter.scrollLeft = itemLeft - (containerWidth / 2) + (itemWidth / 2);
        }
    }

    // 添加内容卡片的点击效果
    const contentCards = document.querySelectorAll('.content-card');
    contentCards.forEach(card => {
        card.addEventListener('click', function() {
            this.style.transform = 'scale(0.98)';
            setTimeout(() => {
                this.style.transform = 'scale(1)';
            }, 100);
        });
    });

    // 悬浮发布按钮动画效果
    const floatingBtn = document.querySelector('.floating-publish-btn');
    if (floatingBtn) {
        window.addEventListener('scroll', function() {
            if (window.scrollY > 100) {
                floatingBtn.classList.add('show');
            } else {
                floatingBtn.classList.remove('show');
            }
        });
    }
});

// 点赞功能
function likePost(event, element) {
    // 阻止事件冒泡，避免触发卡片点击
    event.stopPropagation();

    const postId = element.dataset.id;
    const likeIcon = element.querySelector('i');
    const likeCount = element.querySelector('span');
    const isLiked = likeIcon.classList.contains('fas');

    // 先更新UI，提供即时反馈
    if (isLiked) {
        likeIcon.classList.replace('fas', 'far');
        likeCount.textContent = parseInt(likeCount.textContent) - 1;
    } else {
        likeIcon.classList.replace('far', 'fas');
        likeIcon.style.color = '#ff6b6b';
        likeCount.textContent = parseInt(likeCount.textContent) + 1;

        // 添加点赞动画
        const heart = document.createElement('div');
        heart.className = 'floating-heart';
        heart.innerHTML = '<i class="fas fa-heart"></i>';
        element.appendChild(heart);

        setTimeout(() => {
            heart.remove();
        }, 1000);
    }

    // 发送请求到服务器
    fetch('../api/universe/like.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: `post_id=${postId}&action=${isLiked ? 'unlike' : 'like'}`
    })
    .then(response => response.json())
    .then(data => {
        if (!data.success) {
            // 如果失败，回滚UI更改
            if (isLiked) {
                likeIcon.classList.replace('far', 'fas');
                likeCount.textContent = parseInt(likeCount.textContent) + 1;
            } else {
                likeIcon.classList.replace('fas', 'far');
                likeIcon.style.color = '';
                likeCount.textContent = parseInt(likeCount.textContent) - 1;
            }
            showToast(data.message || '操作失败，请重试');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        // 如果出错，回滚UI更改
        if (isLiked) {
            likeIcon.classList.replace('far', 'fas');
            likeCount.textContent = parseInt(likeCount.textContent) + 1;
        } else {
            likeIcon.classList.replace('fas', 'far');
            likeIcon.style.color = '';
            likeCount.textContent = parseInt(likeCount.textContent) - 1;
        }
        showToast('网络错误，请稍后再试');
    });
}
