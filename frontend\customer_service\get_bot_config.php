<?php
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// 处理预检请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

// 数据库配置
$host = 'localhost';
$dbname = 'quwanplanet';
$username = 'quwanplanet';
$password = 'nJmJm23FB4Xn6Fc3';

try {
    // 连接数据库
    $pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password, [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
        PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES utf8mb4"
    ]);

    // 获取机器人配置
    $stmt = $pdo->prepare("SELECT * FROM customer_service_bot WHERE is_enabled = 1 ORDER BY id DESC LIMIT 1");
    $stmt->execute();
    $config = $stmt->fetch();

    if ($config) {
        echo json_encode([
            'success' => true,
            'config' => [
                'bot_name' => $config['bot_name'],
                'bot_avatar' => $config['bot_avatar'],
                'welcome_message' => $config['welcome_message'],
                'default_reply' => $config['default_reply']
            ]
        ], JSON_UNESCAPED_UNICODE);
    } else {
        // 返回默认配置
        echo json_encode([
            'success' => true,
            'config' => [
                'bot_name' => '趣玩小助手',
                'bot_avatar' => 'https://s1.imagehub.cc/images/2025/05/26/2cdd63b39f96072fc60cd733027d2a30.png',
                'welcome_message' => '您好！我是趣玩星球智能客服小助手，有什么可以帮助您的吗？',
                'default_reply' => '抱歉，我没有理解您的问题。您可以尝试重新描述问题或联系人工客服。'
            ]
        ], JSON_UNESCAPED_UNICODE);
    }

} catch (PDOException $e) {
    echo json_encode([
        'success' => false,
        'message' => '数据库连接失败',
        'error' => $e->getMessage()
    ], JSON_UNESCAPED_UNICODE);
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => '获取配置失败',
        'error' => $e->getMessage()
    ], JSON_UNESCAPED_UNICODE);
}
?>
