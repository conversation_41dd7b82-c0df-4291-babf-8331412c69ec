/* 发起露营活动页面样式 */

/* CSS变量定义 */
:root {
    --primary-color: #6F7BF5;
    --primary-light: #A8B2F8;
    --primary-dark: #5A67E8;
    --secondary-color: #8B95F7;
    --accent-color: #FF6B9D;
    --success-color: #06D6A0;
    --warning-color: #FFD166;
    --error-color: #FF6B6B;
    --gradient-primary: linear-gradient(135deg, #6F7BF5, #8B95F7);
    --gradient-secondary: linear-gradient(135deg, #A8B2F8, #6F7BF5);
    --text-primary: #333333;
    --text-secondary: #666666;
    --text-light: #999999;
    --bg-white: #FFFFFF;
    --bg-light: #F8F9FA;
    --shadow-sm: 0 2px 8px rgba(0, 0, 0, 0.08);
    --shadow-md: 0 4px 16px rgba(0, 0, 0, 0.12);
    --shadow-lg: 0 8px 32px rgba(0, 0, 0, 0.16);
    --radius-sm: 8px;
    --radius-md: 12px;
    --radius-lg: 16px;
    --radius-xl: 24px;
    --transition-fast: all 0.2s ease;
    --transition-normal: all 0.3s ease;
}

/* 基础样式重置 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background: var(--bg-light);
    color: var(--text-primary);
    line-height: 1.6;
    overflow-x: hidden;
}

/* 页面头部 */
.create-header {
    background: var(--bg-white);
    border-bottom: 1px solid #E5E7EB;
    position: sticky;
    top: 0;
    z-index: 100;
    box-shadow: var(--shadow-sm);
}

.header-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 16px 20px;
    max-width: 100%;
}

.back-btn {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: var(--bg-light);
    border: none;
    color: var(--text-primary);
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: var(--transition-normal);
}

.back-btn:hover {
    background: var(--primary-color);
    color: white;
    transform: scale(1.05);
}

.page-title {
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--text-primary);
    text-align: center;
    flex: 1;
}

.header-placeholder {
    width: 40px;
}

/* 主要内容区域 */
.create-main {
    padding: 20px;
    padding-bottom: 100px;
    max-width: 600px;
    margin: 0 auto;
}

/* 表单样式 */
.activity-form {
    display: flex;
    flex-direction: column;
    gap: 24px;
}

.form-section {
    background: var(--bg-white);
    border-radius: var(--radius-lg);
    padding: 20px;
    box-shadow: var(--shadow-md);
    transition: var(--transition-normal);
}

.form-section:hover {
    box-shadow: var(--shadow-lg);
}

.section-header {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 20px;
    padding-bottom: 12px;
    border-bottom: 2px solid var(--bg-light);
}

.section-header i {
    color: var(--primary-color);
    font-size: 1.25rem;
}

.section-header h2 {
    color: var(--text-primary);
    font-size: 1.125rem;
    font-weight: 600;
}

.form-group {
    margin-bottom: 20px;
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 16px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    color: var(--text-primary);
    font-weight: 500;
    font-size: 0.9rem;
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 12px 16px;
    border: 2px solid #E5E7EB;
    border-radius: var(--radius-md);
    font-size: 0.9rem;
    transition: var(--transition-normal);
    background: var(--bg-white);
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(111, 123, 245, 0.1);
}

.form-group textarea {
    resize: vertical;
    min-height: 100px;
}

.form-help {
    margin-top: 8px;
    font-size: 0.8rem;
    color: var(--text-light);
    display: flex;
    align-items: center;
    gap: 6px;
}

.form-help i {
    color: var(--primary-color);
}

/* 特色标签 */
.features-container {
    margin-top: 8px;
}

.feature-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 12px;
}

.feature-tag {
    display: flex;
    align-items: center;
    cursor: pointer;
    transition: var(--transition-normal);
}

.feature-tag input[type="checkbox"] {
    display: none;
}

.feature-tag span {
    padding: 8px 16px;
    border: 2px solid #E5E7EB;
    border-radius: var(--radius-lg);
    font-size: 0.875rem;
    font-weight: 500;
    color: var(--text-secondary);
    transition: var(--transition-normal);
    background: var(--bg-white);
}

.feature-tag input[type="checkbox"]:checked + span {
    background: var(--gradient-primary);
    color: white;
    border-color: var(--primary-color);
    transform: scale(1.05);
}

.feature-tag:hover span {
    border-color: var(--primary-color);
    transform: scale(1.02);
}

/* 组局者信息 */
.organizer-info {
    display: flex;
    align-items: center;
    gap: 16px;
    padding: 16px;
    background: var(--bg-light);
    border-radius: var(--radius-md);
    border: 2px solid rgba(111, 123, 245, 0.1);
}

.organizer-avatar {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    overflow: hidden;
    border: 3px solid var(--primary-color);
}

.organizer-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.organizer-details {
    flex: 1;
}

.organizer-name {
    font-size: 1rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 4px;
}

.organizer-level {
    font-size: 0.875rem;
    color: var(--primary-color);
    font-weight: 500;
    margin-bottom: 6px;
}

.organizer-desc {
    font-size: 0.8rem;
    color: var(--text-light);
    line-height: 1.4;
}

/* 表单操作按钮 */
.form-actions {
    display: flex;
    gap: 16px;
    margin-top: 32px;
}

.btn {
    flex: 1;
    padding: 14px 24px;
    border: none;
    border-radius: var(--radius-md);
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition-normal);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    text-decoration: none;
}

.btn-primary {
    background: var(--gradient-primary);
    color: white;
    box-shadow: var(--shadow-md);
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.btn-secondary {
    background: var(--bg-white);
    color: var(--text-secondary);
    border: 2px solid #E5E7EB;
}

.btn-secondary:hover {
    background: var(--bg-light);
    border-color: var(--primary-color);
    color: var(--primary-color);
}

.btn:active {
    transform: translateY(0);
}

.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
}

/* 四角星加载动画 */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(8px);
    display: none;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    z-index: 9999;
}

.loading-overlay.active {
    display: flex;
}

.four-star-loader {
    position: relative;
    width: 60px;
    height: 60px;
    margin-bottom: 20px;
}

.star-point {
    position: absolute;
    width: 12px;
    height: 12px;
    background: var(--primary-color);
    border-radius: 2px;
    animation: starRotate 1.5s ease-in-out infinite;
}

.star-point:nth-child(1) {
    top: 0;
    left: 50%;
    transform: translateX(-50%);
    animation-delay: 0s;
    background: var(--primary-color);
}

.star-point:nth-child(2) {
    top: 50%;
    right: 0;
    transform: translateY(-50%);
    animation-delay: 0.375s;
    background: #FF6B9D;
}

.star-point:nth-child(3) {
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    animation-delay: 0.75s;
    background: #FFD166;
}

.star-point:nth-child(4) {
    top: 50%;
    left: 0;
    transform: translateY(-50%);
    animation-delay: 1.125s;
    background: var(--primary-color);
}

@keyframes starRotate {
    0%, 100% {
        transform: scale(1) rotate(0deg);
        opacity: 1;
    }
    25% {
        transform: scale(1.2) rotate(90deg);
        opacity: 0.8;
    }
    50% {
        transform: scale(0.8) rotate(180deg);
        opacity: 0.6;
    }
    75% {
        transform: scale(1.1) rotate(270deg);
        opacity: 0.8;
    }
}

.loading-text {
    color: var(--text-secondary);
    font-size: 0.875rem;
    font-weight: 500;
}

/* Toast提示 */
.toast {
    position: fixed;
    bottom: 100px;
    left: 50%;
    transform: translateX(-50%);
    background: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 12px 20px;
    border-radius: var(--radius-lg);
    font-size: 0.875rem;
    z-index: 10000;
    display: none;
    backdrop-filter: blur(10px);
}

/* 响应式设计 */
@media (max-width: 768px) {
    .create-main {
        padding: 16px;
        padding-bottom: 100px;
    }
    
    .form-section {
        padding: 16px;
    }
    
    .form-row {
        grid-template-columns: 1fr;
        gap: 12px;
    }
    
    .form-actions {
        flex-direction: column;
    }
    
    .organizer-info {
        flex-direction: column;
        text-align: center;
    }
    
    .feature-tags {
        gap: 8px;
    }
    
    .feature-tag span {
        padding: 6px 12px;
        font-size: 0.8rem;
    }
}

@media (max-width: 480px) {
    .header-content {
        padding: 12px 16px;
    }
    
    .page-title {
        font-size: 1rem;
    }
    
    .section-header h2 {
        font-size: 1rem;
    }
    
    .btn {
        padding: 12px 20px;
        font-size: 0.9rem;
    }
}
