/**
 * 趣玩星球管理后台 - 现代化样式
 * 年轻化、美观、现代化设计
 */

/* ===== 基础重置和变量 ===== */
:root {
    /* 主色调 - 年轻化配色 */
    --primary-color: #6F7BF5;
    --primary-light: #8B93F7;
    --primary-dark: #5A67D8;
    --secondary-color: #667eea;
    --secondary-light: #764ba2;

    /* 功能色彩 */
    --success-color: #10B981;
    --warning-color: #F59E0B;
    --error-color: #EF4444;
    --info-color: #3B82F6;

    /* 中性色 */
    --white: #FFFFFF;
    --gray-50: #F9FAFB;
    --gray-100: #F3F4F6;
    --gray-200: #E5E7EB;
    --gray-300: #D1D5DB;
    --gray-400: #9CA3AF;
    --gray-500: #6B7280;
    --gray-600: #4B5563;
    --gray-700: #374151;
    --gray-800: #1F2937;
    --gray-900: #111827;

    /* 渐变背景 */
    --gradient-primary: linear-gradient(135deg, var(--primary-color), var(--primary-light));
    --gradient-secondary: linear-gradient(135deg, var(--secondary-color), var(--secondary-light));
    --gradient-success: linear-gradient(135deg, #10B981, #34D399);
    --gradient-warning: linear-gradient(135deg, #F59E0B, #FBBF24);
    --gradient-error: linear-gradient(135deg, #EF4444, #F87171);
    --gradient-info: linear-gradient(135deg, #3B82F6, #60A5FA);

    /* 阴影 */
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);

    /* 圆角 */
    --radius-sm: 0.375rem;
    --radius-md: 0.5rem;
    --radius-lg: 0.75rem;
    --radius-xl: 1rem;
    --radius-2xl: 1.5rem;

    /* 间距 */
    --spacing-xs: 0.5rem;
    --spacing-sm: 0.75rem;
    --spacing-md: 1rem;
    --spacing-lg: 1.5rem;
    --spacing-xl: 2rem;
    --spacing-2xl: 3rem;

    /* 字体 */
    --font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
    --font-mono: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;
}

/* ===== 基础样式重置 ===== */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    font-size: 16px;
    line-height: 1.5;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

body {
    font-family: var(--font-family);
    background: var(--gradient-secondary);
    color: var(--gray-800);
    min-height: 100vh;
    overflow-x: hidden;
}

/* ===== 滚动条样式 ===== */
::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

::-webkit-scrollbar-track {
    background: var(--gray-100);
    border-radius: var(--radius-md);
}

::-webkit-scrollbar-thumb {
    background: var(--gradient-primary);
    border-radius: var(--radius-md);
    transition: all 0.3s ease;
}

::-webkit-scrollbar-thumb:hover {
    background: var(--primary-dark);
}

/* ===== 主容器布局 ===== */
.admin-container {
    max-width: 1400px;
    margin: 0 auto;
    padding: var(--spacing-lg);
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    gap: var(--spacing-lg);
}

/* ===== 顶部导航栏 ===== */
.top-navbar {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border-radius: var(--radius-2xl);
    padding: var(--spacing-md) var(--spacing-xl);
    box-shadow: var(--shadow-lg);
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: sticky;
    top: var(--spacing-lg);
    z-index: 100;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.navbar-left {
    display: flex;
    align-items: center;
    gap: var(--spacing-lg);
}

.navbar-brand {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    font-size: 1.25rem;
    font-weight: 700;
    color: var(--gray-800);
    text-decoration: none;
    transition: all 0.3s ease;
}

.navbar-brand:hover {
    color: var(--primary-color);
    transform: translateY(-1px);
}

.brand-icon {
    width: 32px;
    height: 32px;
    background: var(--gradient-primary);
    border-radius: var(--radius-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1rem;
}

.page-title {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--gray-700);
    margin: 0;
}

.navbar-right {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
}

/* ===== 用户信息和操作 ===== */
.user-profile {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-sm) var(--spacing-md);
    background: rgba(64, 224, 208, 0.1);
    border-radius: var(--radius-xl);
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
}

.user-profile:hover {
    background: rgba(64, 224, 208, 0.2);
    transform: translateY(-1px);
}

.user-avatar {
    width: 36px;
    height: 36px;
    border-radius: var(--radius-lg);
    border: 2px solid rgba(64, 224, 208, 0.3);
    object-fit: cover;
    background: var(--gradient-primary);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: 600;
}

.user-info {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
}

.user-name {
    font-size: 0.875rem;
    font-weight: 600;
    color: var(--gray-800);
    line-height: 1.2;
}

.user-role {
    font-size: 0.75rem;
    color: var(--gray-500);
    line-height: 1.2;
}

/* ===== 操作按钮 ===== */
.action-btn {
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-xs);
    padding: var(--spacing-sm) var(--spacing-md);
    border: none;
    border-radius: var(--radius-lg);
    font-size: 0.875rem;
    font-weight: 500;
    text-decoration: none;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.action-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s ease;
}

.action-btn:hover::before {
    left: 100%;
}

.action-btn.primary {
    background: var(--gradient-primary);
    color: white;
}

.action-btn.primary:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.action-btn.secondary {
    background: var(--gradient-secondary);
    color: white;
}

.action-btn.danger {
    background: var(--gradient-error);
    color: white;
}

.action-btn.outline {
    background: transparent;
    border: 2px solid var(--primary-color);
    color: var(--primary-color);
}

.action-btn.outline:hover {
    background: var(--primary-color);
    color: white;
}

/* ===== 主内容区域 ===== */
.main-content {
    flex: 1;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border-radius: var(--radius-2xl);
    padding: var(--spacing-2xl);
    box-shadow: var(--shadow-xl);
    border: 1px solid rgba(255, 255, 255, 0.2);
    animation: fadeInUp 0.6s ease;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* ===== 响应式设计 ===== */
@media (max-width: 768px) {
    .admin-container {
        padding: var(--spacing-md);
        gap: var(--spacing-md);
    }

    .top-navbar {
        flex-direction: column;
        gap: var(--spacing-md);
        padding: var(--spacing-lg);
    }

    .navbar-left,
    .navbar-right {
        width: 100%;
        justify-content: center;
    }

    .main-content {
        padding: var(--spacing-lg);
    }

    .page-title {
        font-size: 1.25rem;
    }
}

@media (max-width: 480px) {
    .admin-container {
        padding: var(--spacing-sm);
    }

    .main-content {
        padding: var(--spacing-md);
    }
}

/* ===== 加载动画 ===== */
.loading-spinner {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 2px solid rgba(64, 224, 208, 0.3);
    border-radius: 50%;
    border-top-color: var(--primary-color);
    animation: spin 1s linear infinite;
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}

/* ===== 工具类 ===== */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.font-bold { font-weight: 700; }
.font-semibold { font-weight: 600; }
.font-medium { font-weight: 500; }

.text-sm { font-size: 0.875rem; }
.text-lg { font-size: 1.125rem; }
.text-xl { font-size: 1.25rem; }
.text-2xl { font-size: 1.5rem; }

.mb-2 { margin-bottom: var(--spacing-xs); }
.mb-4 { margin-bottom: var(--spacing-md); }
.mb-6 { margin-bottom: var(--spacing-lg); }
.mb-8 { margin-bottom: var(--spacing-xl); }

.mt-2 { margin-top: var(--spacing-xs); }
.mt-4 { margin-top: var(--spacing-md); }
.mt-6 { margin-top: var(--spacing-lg); }
.mt-8 { margin-top: var(--spacing-xl); }
