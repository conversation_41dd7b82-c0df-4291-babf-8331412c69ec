<?php
session_start();

// 检查用户是否已登录
if (!isset($_SESSION['user_id'])) {
    header('Location: ../../login/index.php');
    exit;
}

// 数据库连接配置
$db_config = [
    'host' => 'localhost',
    'username' => 'quwanplanet',
    'password' => 'nJmJm23FB4Xn6Fc3',
    'dbname' => 'quwanplanet',
    'port' => 3306,
    'charset' => 'utf8mb4'
];

// 获取用户信息
try {
    $dsn = "mysql:host={$db_config['host']};dbname={$db_config['dbname']};charset={$db_config['charset']};port={$db_config['port']}";
    $pdo = new PDO($dsn, $db_config['username'], $db_config['password']);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

    $stmt = $pdo->prepare("SELECT * FROM users WHERE id = :user_id");
    $stmt->execute(['user_id' => $_SESSION['user_id']]);
    $user = $stmt->fetch(PDO::FETCH_ASSOC);

    // 如果用户不存在，注销并重定向到登录页
    if (!$user) {
        session_destroy();
        header('Location: ../../login/index.php');
        exit;
    }

    // 设置默认头像
    if (empty($user['avatar'])) {
        $user['avatar'] = 'https://s1.imagehub.cc/images/2025/04/26/f5c2f26c4564aabcbb56c5fd30f1d652.jpg';
    }
} catch (PDOException $e) {
    error_log("编辑资料页面错误: " . $e->getMessage());
    $db_error = true;
}

// 处理表单提交
$success = '';
$error = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        // 获取表单数据
        $username = $_POST['username'] ?? '';
        $bio = $_POST['bio'] ?? '';
        $gender = $_POST['gender'] ?? '';
        $birth_date = $_POST['birth_date'] ?? '';
        $region = $_POST['region'] ?? '';

        // 验证用户名是否已存在
        if ($username !== $user['username']) {
            $stmt = $pdo->prepare("SELECT COUNT(*) FROM users WHERE username = :username AND id != :user_id");
            $stmt->execute([
                'username' => $username,
                'user_id' => $_SESSION['user_id']
            ]);

            if ($stmt->fetchColumn() > 0) {
                $error = '用户名已被使用';
                throw new Exception('用户名已被使用');
            }
        }

        // 处理头像上传
        $avatar = $user['avatar'];
        if (isset($_FILES['avatar']) && $_FILES['avatar']['error'] === UPLOAD_ERR_OK) {
            // 引入Cloudinary助手
            require_once('../../includes/cloudinary_helper.php');

            $tmp_name = $_FILES['avatar']['tmp_name'];
            $result = uploadToCloudinary($tmp_name);

            if (isset($result['secure_url'])) {
                $avatar = $result['secure_url'];
            } else {
                $error = '头像上传失败';
                throw new Exception('头像上传失败');
            }
        }

        // 更新用户信息
        $stmt = $pdo->prepare("UPDATE users SET username = :username, bio = :bio, gender = :gender, birth_date = :birth_date, region = :region, avatar = :avatar WHERE id = :user_id");
        $stmt->execute([
            'username' => $username,
            'bio' => $bio,
            'gender' => $gender,
            'birth_date' => $birth_date,
            'region' => $region,
            'avatar' => $avatar,
            'user_id' => $_SESSION['user_id']
        ]);

        // 更新会话中的用户名
        $_SESSION['username'] = $username;

        $success = '个人资料更新成功';
    } catch (Exception $e) {
        if (empty($error)) {
            $error = '更新失败，请稍后再试';
        }
        error_log("编辑资料错误: " . $e->getMessage());
    }
}
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <meta name="theme-color" content="#1E90FF">
    <title>编辑资料 - 趣玩星球</title>
    <link rel="stylesheet" href="../css/style.css">
    <style>
        /* 编辑资料页面特定样式 */
        .header-bar {
            display: flex;
            align-items: center;
            padding: 15px;
            background-color: #1E90FF;
            color: white;
            position: sticky;
            top: 0;
            z-index: 100;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        .back-button {
            margin-right: 15px;
            font-size: 18px;
            color: white;
            text-decoration: none;
        }

        .header-title {
            font-size: 18px;
            font-weight: bold;
        }

        .edit-form {
            background-color: white;
            border-radius: 15px;
            margin: 15px;
            padding: 20px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            color: #555;
        }

        .form-control {
            width: 100%;
            padding: 12px 15px;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            font-size: 16px;
            transition: all 0.3s ease;
            outline: none;
        }

        .form-control:focus {
            border-color: #1E90FF;
            box-shadow: 0 0 0 2px rgba(30, 144, 255, 0.1);
        }

        textarea.form-control {
            resize: vertical;
            min-height: 100px;
        }

        .radio-group {
            display: flex;
            gap: 20px;
        }

        .radio-label {
            display: flex;
            align-items: center;
            cursor: pointer;
        }

        .radio-label input {
            margin-right: 8px;
            accent-color: #1E90FF;
        }

        .avatar-upload {
            display: flex;
            flex-direction: column;
            align-items: center;
            margin-bottom: 20px;
        }

        .avatar-preview {
            width: 100px;
            height: 100px;
            border-radius: 50%;
            overflow: hidden;
            margin-bottom: 15px;
            border: 3px solid #1E90FF;
            box-shadow: 0 4px 10px rgba(30, 144, 255, 0.2);
        }

        .avatar-preview img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .avatar-input {
            display: none;
        }

        .avatar-label {
            padding: 8px 15px;
            background-color: #1E90FF;
            color: white;
            border-radius: 20px;
            font-size: 14px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .avatar-label:hover {
            background-color: #0c7cd5;
            transform: translateY(-2px);
        }

        .btn-submit {
            width: 100%;
            padding: 15px;
            background: #1E90FF;
            color: white;
            border: none;
            border-radius: 30px;
            font-size: 16px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 10px rgba(30, 144, 255, 0.3);
        }

        .btn-submit:hover {
            background: #0c7cd5;
            transform: translateY(-2px);
            box-shadow: 0 6px 15px rgba(30, 144, 255, 0.4);
        }

        .alert {
            padding: 12px 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            font-size: 14px;
        }

        .alert-success {
            background-color: #e8f5e9;
            color: #388e3c;
            border-left: 4px solid #388e3c;
        }

        .alert-danger {
            background-color: #ffebee;
            color: #d32f2f;
            border-left: 4px solid #d32f2f;
        }
    </style>
    <!-- 引入字体图标库 -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <!-- Toast提示 -->
    <div class="toast" id="toast"></div>

    <!-- 顶部导航栏 -->
    <div class="header-bar">
        <a href="../index.php" class="back-button">
            <i class="fas fa-arrow-left"></i>
        </a>
        <div class="header-title">编辑资料</div>
    </div>

    <div class="edit-form">
        <?php if (!empty($success)): ?>
            <div class="alert alert-success"><?php echo $success; ?></div>
        <?php endif; ?>

        <?php if (!empty($error)): ?>
            <div class="alert alert-danger"><?php echo $error; ?></div>
        <?php endif; ?>

        <form method="post" enctype="multipart/form-data">
            <div class="avatar-upload">
                <div class="avatar-preview">
                    <img src="<?php echo htmlspecialchars($user['avatar']); ?>" alt="用户头像" id="avatar-preview-img">
                </div>
                <input type="file" id="avatar" name="avatar" class="avatar-input" accept=".jpg,.jpeg,.png">
                <label for="avatar" class="avatar-label">
                    <i class="fas fa-camera"></i> 更换头像
                </label>
            </div>

            <div class="form-group">
                <label for="username">用户名</label>
                <input type="text" id="username" name="username" class="form-control" value="<?php echo htmlspecialchars($user['username']); ?>" required>
            </div>

            <div class="form-group">
                <label>趣玩ID</label>
                <input type="text" class="form-control" value="<?php echo htmlspecialchars($user['quwan_id']); ?>" disabled>
                <small style="color: #999; font-size: 12px;">趣玩ID不可修改</small>
            </div>

            <div class="form-group">
                <label for="bio">个人简介</label>
                <textarea id="bio" name="bio" class="form-control"><?php echo htmlspecialchars($user['bio'] ?? ''); ?></textarea>
            </div>

            <div class="form-group">
                <label>性别</label>
                <div class="radio-group">
                    <label class="radio-label">
                        <input type="radio" name="gender" value="male" <?php echo ($user['gender'] === 'male') ? 'checked' : ''; ?>>
                        <span>男</span>
                    </label>
                    <label class="radio-label">
                        <input type="radio" name="gender" value="female" <?php echo ($user['gender'] === 'female') ? 'checked' : ''; ?>>
                        <span>女</span>
                    </label>
                    <label class="radio-label">
                        <input type="radio" name="gender" value="other" <?php echo ($user['gender'] === 'other') ? 'checked' : ''; ?>>
                        <span>其他</span>
                    </label>
                </div>
            </div>

            <div class="form-group">
                <label for="birth_date">出生日期</label>
                <input type="date" id="birth_date" name="birth_date" class="form-control" value="<?php echo htmlspecialchars($user['birth_date'] ?? ''); ?>">
            </div>

            <div class="form-group">
                <label for="region">所在地区</label>
                <input type="text" id="region" name="region" class="form-control" value="<?php echo htmlspecialchars($user['region'] ?? ''); ?>" placeholder="例如：北京市海淀区">
            </div>

            <button type="submit" class="btn-submit">保存修改</button>
        </form>
    </div>

    <script src="../js/script.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 头像预览功能
            const avatarInput = document.getElementById('avatar');
            const avatarPreview = document.getElementById('avatar-preview-img');

            avatarInput.addEventListener('change', function() {
                if (this.files && this.files[0]) {
                    const reader = new FileReader();

                    reader.onload = function(e) {
                        avatarPreview.src = e.target.result;
                    }

                    reader.readAsDataURL(this.files[0]);
                }
            });

            // 显示成功消息
            <?php if (!empty($success)): ?>
            showToast('<?php echo $success; ?>');
            <?php endif; ?>

            // 显示错误消息
            <?php if (!empty($error)): ?>
            showToast('<?php echo $error; ?>');
            <?php endif; ?>
        });
    </script>
</body>
</html>
