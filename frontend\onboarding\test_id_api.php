<?php
header('Content-Type: application/json');

// 引入ID生成函数（从complete_registration.php复制）
function generate7DigitId() {
    return (string)mt_rand(1000000, 9999999);
}

function generate8DigitId() {
    return (string)mt_rand(10000000, 99999999);
}

function generate9DigitId() {
    return (string)mt_rand(100000000, 999999999);
}

function isValidId($id) {
    if ($id[0] === '0') {
        return false;
    }
    
    if (hasConsecutiveRepeats($id, 3)) {
        return false;
    }
    
    if (isSpecialNumber($id)) {
        return false;
    }
    
    if (hasSequentialDigits($id)) {
        return false;
    }
    
    return true;
}

function hasConsecutiveRepeats($id, $count = 3) {
    $length = strlen($id);
    for ($i = 0; $i <= $length - $count; $i++) {
        $allSame = true;
        for ($j = 1; $j < $count; $j++) {
            if ($id[$i] !== $id[$i + $j]) {
                $allSame = false;
                break;
            }
        }
        if ($allSame) {
            return true;
        }
    }
    return false;
}

function isSpecialNumber($id) {
    $specialPatterns = [
        '520', '521', '1314', '1413', '1314520', '5201314',
        '666', '888', '999', '168', '188', '288', '388',
        '588', '688', '788', '988', '123', '234', '345',
        '456', '567', '678', '789', '987', '876', '765',
        '654', '543', '432', '321'
    ];
    
    foreach ($specialPatterns as $pattern) {
        if (strpos($id, $pattern) !== false) {
            return true;
        }
    }
    
    return false;
}

function hasSequentialDigits($id) {
    $length = strlen($id);
    
    for ($i = 0; $i <= $length - 4; $i++) {
        $isAscending = true;
        $isDescending = true;
        
        for ($j = 1; $j < 4; $j++) {
            if ((int)$id[$i + $j] !== (int)$id[$i + $j - 1] + 1) {
                $isAscending = false;
            }
            if ((int)$id[$i + $j] !== (int)$id[$i + $j - 1] - 1) {
                $isDescending = false;
            }
        }
        
        if ($isAscending || $isDescending) {
            return true;
        }
    }
    
    return false;
}

function getValidationDetails($id) {
    $details = [];
    
    if ($id[0] === '0') {
        $details[] = '以0开头';
    }
    
    if (hasConsecutiveRepeats($id, 3)) {
        $details[] = '包含豹子号';
    }
    
    if (isSpecialNumber($id)) {
        $details[] = '包含靓号模式';
    }
    
    if (hasSequentialDigits($id)) {
        $details[] = '包含顺子号';
    }
    
    return empty($details) ? ['符合所有规则'] : $details;
}

$action = $_GET['action'] ?? 'single';

switch ($action) {
    case 'single':
        $start_time = microtime(true);
        $id = generate7DigitId();
        $valid = isValidId($id);
        $end_time = microtime(true);
        
        echo json_encode([
            'id' => $id,
            'valid' => $valid,
            'length' => strlen($id),
            'generation_time' => round(($end_time - $start_time) * 1000, 2),
            'validation_details' => getValidationDetails($id)
        ]);
        break;
        
    case 'batch':
        $start_time = microtime(true);
        $ids = [];
        $stats = [
            'seven_digit' => 0,
            'eight_digit' => 0,
            'nine_digit' => 0,
            'valid' => 0,
            'invalid' => 0
        ];
        $invalid_ids = [];
        
        for ($i = 0; $i < 100; $i++) {
            $id = generate7DigitId();
            $valid = isValidId($id);
            $length = strlen($id);
            
            $ids[] = [
                'id' => $id,
                'valid' => $valid,
                'length' => $length
            ];
            
            if ($length == 7) $stats['seven_digit']++;
            elseif ($length == 8) $stats['eight_digit']++;
            elseif ($length == 9) $stats['nine_digit']++;
            
            if ($valid) {
                $stats['valid']++;
            } else {
                $stats['invalid']++;
                $invalid_ids[] = [
                    'id' => $id,
                    'reason' => implode(', ', getValidationDetails($id))
                ];
            }
        }
        
        $end_time = microtime(true);
        
        echo json_encode([
            'total_generated' => 100,
            'stats' => $stats,
            'average_time' => round(($end_time - $start_time) * 1000 / 100, 2),
            'sample_ids' => array_slice($ids, 0, 20),
            'invalid_ids' => array_slice($invalid_ids, 0, 10)
        ]);
        break;
        
    case 'validation':
        $test_cases = [
            // 有效ID
            ['id' => '1234567', 'expected' => true],
            ['id' => '9876543', 'expected' => true],
            ['id' => '2468135', 'expected' => true],
            
            // 以0开头
            ['id' => '0123456', 'expected' => false],
            
            // 豹子号
            ['id' => '1112345', 'expected' => false],
            ['id' => '1233345', 'expected' => false],
            ['id' => '1234555', 'expected' => false],
            
            // 爱情号
            ['id' => '1520123', 'expected' => false],
            ['id' => '1314567', 'expected' => false],
            ['id' => '5201314', 'expected' => false],
            
            // 其他靓号
            ['id' => '1666789', 'expected' => false],
            ['id' => '1888999', 'expected' => false],
            ['id' => '1168888', 'expected' => false],
            
            // 顺子号
            ['id' => '1234567', 'expected' => false], // 连续递增
            ['id' => '9876543', 'expected' => false], // 连续递减
            ['id' => '1123456', 'expected' => false], // 部分连续
        ];
        
        $results = [];
        $passed = 0;
        
        foreach ($test_cases as $case) {
            $actual = isValidId($case['id']);
            $passed_test = ($actual === $case['expected']);
            
            if ($passed_test) {
                $passed++;
            }
            
            $results[] = [
                'id' => $case['id'],
                'expected' => $case['expected'],
                'actual' => $actual,
                'passed' => $passed_test,
                'reason' => $passed_test ? null : implode(', ', getValidationDetails($case['id']))
            ];
        }
        
        echo json_encode([
            'test_cases' => $results,
            'total_tests' => count($test_cases),
            'passed_tests' => $passed,
            'failed_tests' => count($test_cases) - $passed,
            'pass_rate' => round($passed / count($test_cases) * 100, 1)
        ]);
        break;
        
    default:
        echo json_encode(['error' => '无效的操作']);
}
?>
