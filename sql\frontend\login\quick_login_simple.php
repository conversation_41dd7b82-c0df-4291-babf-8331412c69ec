<?php
/**
 * 简化版快速登录API
 */

// 引入session配置
require_once '../../sql/session_config.php';
initLoginSession();
header('Content-Type: application/json; charset=utf-8');

// 错误处理
error_reporting(0);
ini_set('display_errors', 0);

// 捕获所有错误并返回JSON
set_error_handler(function($severity, $message, $file, $line) {
    echo json_encode(['success' => false, 'message' => '系统错误，请稍后重试']);
    exit;
});

set_exception_handler(function($exception) {
    echo json_encode(['success' => false, 'message' => '系统错误，请稍后重试']);
    exit;
});

// 数据库连接配置
$db_config = [
    'host' => 'localhost',
    'username' => 'quwanplanet',
    'password' => 'nJmJm23FB4Xn6Fc3',
    'dbname' => 'quwanplanet',
    'port' => 3306,
    'charset' => 'utf8mb4'
];

try {
    $pdo = new PDO(
        "mysql:host={$db_config['host']};port={$db_config['port']};dbname={$db_config['dbname']};charset={$db_config['charset']}",
        $db_config['username'],
        $db_config['password'],
        [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
            PDO::ATTR_EMULATE_PREPARES => false
        ]
    );
} catch (PDOException $e) {
    echo json_encode(['success' => false, 'message' => '数据库连接失败']);
    exit;
}

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode(['success' => false, 'message' => '请求方法错误']);
    exit;
}

$input = json_decode(file_get_contents('php://input'), true);
$user_id = intval($input['user_id'] ?? 0);

if (!$user_id) {
    echo json_encode(['success' => false, 'message' => '用户ID无效']);
    exit;
}

try {
    // 验证用户存在且状态正常
    $stmt = $pdo->prepare("SELECT id, username, quwan_id, status FROM users WHERE id = ?");
    $stmt->execute([$user_id]);
    $user = $stmt->fetch();

    if (!$user) {
        echo json_encode(['success' => false, 'message' => '用户不存在']);
        exit;
    }

    if ($user['status'] === 'banned') {
        echo json_encode(['success' => false, 'message' => '账户已被封禁']);
        exit;
    }

    // 设置登录会话
    setUserLoginSession($user);

    // 更新最后登录时间
    $stmt = $pdo->prepare("UPDATE users SET last_login = NOW() WHERE id = ?");
    $stmt->execute([$user_id]);

    // 记录登录日志（如果表存在）
    try {
        $stmt = $pdo->prepare("INSERT INTO login_logs (user_id, login_time, ip_address, user_agent, status, login_type) VALUES (?, NOW(), ?, ?, 'success', 'quick_login')");
        $stmt->execute([
            $user_id,
            $_SERVER['REMOTE_ADDR'],
            $_SERVER['HTTP_USER_AGENT']
        ]);
    } catch (PDOException $e) {
        // 忽略日志记录错误
    }

    echo json_encode([
        'success' => true,
        'message' => '登录成功',
        'redirect' => '../login/index.php?login_success=1'
    ]);

} catch (PDOException $e) {
    echo json_encode(['success' => false, 'message' => '登录失败，请稍后重试']);
}
?>
