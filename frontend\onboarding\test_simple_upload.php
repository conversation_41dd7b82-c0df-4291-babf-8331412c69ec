<?php
header('Content-Type: application/json');

// 检查是否有文件上传
if (!isset($_FILES['avatar']) || $_FILES['avatar']['error'] !== UPLOAD_ERR_OK) {
    echo json_encode(['success' => false, 'error' => '没有文件上传或上传失败']);
    exit;
}

// 检查文件类型
$allowed_types = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
$file_type = $_FILES['avatar']['type'];

if (!in_array($file_type, $allowed_types)) {
    echo json_encode(['success' => false, 'error' => '不支持的文件类型']);
    exit;
}

// 检查文件大小（5MB限制）
$max_size = 5 * 1024 * 1024; // 5MB
if ($_FILES['avatar']['size'] > $max_size) {
    echo json_encode(['success' => false, 'error' => '文件大小超过5MB限制']);
    exit;
}

try {
    // 检查Cloudinary助手文件是否存在
    $cloudinary_helper_path = '../includes/cloudinary_helper.php';
    if (!file_exists($cloudinary_helper_path)) {
        echo json_encode(['success' => false, 'error' => 'Cloudinary助手文件不存在']);
        exit;
    }

    // 引入Cloudinary助手
    require_once($cloudinary_helper_path);
    
    if (!class_exists('CloudinaryHelper')) {
        echo json_encode(['success' => false, 'error' => 'CloudinaryHelper类不存在']);
        exit;
    }

    $cloudinaryHelper = new CloudinaryHelper();

    // 简化的上传选项
    $upload_options = [
        'folder' => 'quwanplanet/avatars'
    ];

    // 上传到Cloudinary
    $result = $cloudinaryHelper->uploadImage($_FILES['avatar']['tmp_name'], $upload_options);

    if ($result['success']) {
        $avatar_url = $result['data']['secure_url'];
        
        echo json_encode([
            'success' => true,
            'avatar_url' => $avatar_url,
            'moderation' => ['approved' => true, 'reason' => '审核通过'],
            'debug' => [
                'file_name' => $_FILES['avatar']['name'],
                'file_size' => $_FILES['avatar']['size'],
                'file_type' => $_FILES['avatar']['type'],
                'cloudinary_response' => $result['data']
            ]
        ]);
    } else {
        echo json_encode([
            'success' => false, 
            'error' => '头像上传失败：' . ($result['message'] ?? '未知错误'),
            'debug' => [
                'file_name' => $_FILES['avatar']['name'],
                'file_size' => $_FILES['avatar']['size'],
                'file_type' => $_FILES['avatar']['type'],
                'cloudinary_error' => $result
            ]
        ]);
    }

} catch (Exception $e) {
    echo json_encode([
        'success' => false, 
        'error' => '上传过程中发生错误: ' . $e->getMessage(),
        'debug' => [
            'exception' => $e->getMessage(),
            'file' => $e->getFile(),
            'line' => $e->getLine()
        ]
    ]);
}
?>
