<?php
// 前台消息接收测试页面
header('Content-Type: text/html; charset=UTF-8');
ini_set('default_charset', 'UTF-8');

// 模拟前台用户
$userId = $_GET['user_id'] ?? 4; // 默认用户ID为4
$sessionId = $_GET['session_id'] ?? '';

require_once '../db_config.php';

echo '<h1>📱 前台消息接收测试</h1>';

try {
    $pdo = getDbConnection();
    
    // 如果有session_id，获取会话信息
    if ($sessionId) {
        $stmt = $pdo->prepare("SELECT * FROM customer_service_sessions WHERE session_id = ?");
        $stmt->execute([$sessionId]);
        $session = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($session) {
            $userId = $session['user_id'];
            echo '<div style="background: #e7f3ff; padding: 15px; border-radius: 8px; margin: 10px 0;">';
            echo '<h3>📋 会话信息</h3>';
            echo '<p><strong>会话ID:</strong> ' . htmlspecialchars($sessionId) . '</p>';
            echo '<p><strong>用户ID:</strong> ' . htmlspecialchars($userId) . '</p>';
            echo '<p><strong>用户名:</strong> ' . htmlspecialchars($session['user_name']) . '</p>';
            echo '<p><strong>状态:</strong> ' . htmlspecialchars($session['status']) . '</p>';
            echo '</div>';
        }
    }
    
    if ($userId) {
        // 获取用户的消息
        echo '<h2>💬 会话消息</h2>';
        $stmt = $pdo->prepare("
            SELECT m.*, s.session_id 
            FROM customer_service_messages m
            JOIN customer_service_sessions s ON m.session_id = s.session_id
            WHERE s.user_id = ?
            ORDER BY m.created_at DESC
            LIMIT 10
        ");
        $stmt->execute([$userId]);
        $messages = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        if (!empty($messages)) {
            echo '<div id="messages" style="background: #f8f9fa; padding: 15px; border-radius: 8px; max-height: 400px; overflow-y: auto;">';
            foreach ($messages as $msg) {
                $isCS = $msg['sender_type'] === 'customer_service';
                $bgColor = $isCS ? '#007cba' : '#28a745';
                $textColor = 'white';
                $align = $isCS ? 'right' : 'left';
                
                echo '<div style="margin: 10px 0; text-align: ' . $align . ';">';
                echo '<div style="display: inline-block; background: ' . $bgColor . '; color: ' . $textColor . '; padding: 8px 12px; border-radius: 15px; max-width: 70%;">';
                echo '<div style="font-size: 12px; opacity: 0.8;">' . $msg['sender_name'] . ' - ' . $msg['created_at'] . '</div>';
                echo '<div>' . htmlspecialchars($msg['content']) . '</div>';
                echo '</div>';
                echo '</div>';
            }
            echo '</div>';
        } else {
            echo '<p>没有找到消息</p>';
        }
        
        // 获取用户的通知
        echo '<h2>🔔 实时通知</h2>';
        $stmt = $pdo->prepare("
            SELECT * FROM realtime_notifications 
            WHERE user_id = ? 
            ORDER BY created_at DESC 
            LIMIT 10
        ");
        $stmt->execute([$userId]);
        $notifications = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        if (!empty($notifications)) {
            echo '<div id="notifications" style="background: #fff3cd; padding: 15px; border-radius: 8px;">';
            foreach ($notifications as $notif) {
                $statusColor = $notif['status'] === 'unread' ? '#dc3545' : '#28a745';
                echo '<div style="margin: 8px 0; padding: 10px; border: 1px solid #ddd; border-radius: 5px; border-left: 4px solid ' . $statusColor . ';">';
                echo '<div style="font-weight: bold;">' . htmlspecialchars($notif['title']) . '</div>';
                if (isset($notif['message'])) {
                    echo '<div style="color: #666;">' . htmlspecialchars($notif['message']) . '</div>';
                }
                echo '<div style="font-size: 12px; color: #999;">' . $notif['created_at'] . ' - ' . $notif['status'] . '</div>';
                echo '</div>';
            }
            echo '</div>';
        } else {
            echo '<p>没有找到通知</p>';
        }
        
    } else {
        echo '<div style="background: #fff3cd; padding: 15px; border-radius: 8px; margin: 10px 0;">';
        echo '<h3>⚠️ 需要用户ID</h3>';
        echo '<p>请在URL中指定用户ID，例如：?user_id=4</p>';
        echo '</div>';
    }
    
} catch (Exception $e) {
    echo '<div style="background: #f8d7da; padding: 15px; border-radius: 8px; margin: 10px 0; color: #721c24;">';
    echo '<h3>❌ 错误</h3>';
    echo '<p>' . htmlspecialchars($e->getMessage()) . '</p>';
    echo '</div>';
}
?>

<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>前台消息测试</title>
    <style>
        body { 
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; 
            margin: 20px; 
            background: #f5f5f5; 
        }
        .container { 
            max-width: 800px; 
            margin: 0 auto; 
            background: white; 
            padding: 20px; 
            border-radius: 10px; 
            box-shadow: 0 2px 10px rgba(0,0,0,0.1); 
        }
        .refresh-btn {
            background: #6F7BF5;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 10px 5px;
        }
        .refresh-btn:hover {
            background: #5a67d8;
        }
    </style>
</head>
<body>
    <div class="container">
        <div style="background: #d4edda; padding: 15px; border-radius: 8px; margin: 10px 0;">
            <h3>✅ 模拟前台用户界面</h3>
            <p>这个页面模拟前台用户看到的消息和通知</p>
            <p><strong>当前用户ID:</strong> <?php echo htmlspecialchars($userId); ?></p>
            <?php if ($sessionId): ?>
            <p><strong>当前会话ID:</strong> <?php echo htmlspecialchars($sessionId); ?></p>
            <?php endif; ?>
        </div>
        
        <div style="text-align: center; margin: 20px 0;">
            <button class="refresh-btn" onclick="location.reload()">🔄 刷新页面</button>
            <button class="refresh-btn" onclick="autoRefresh()">🔄 自动刷新</button>
            <button class="refresh-btn" onclick="stopAutoRefresh()">⏹️ 停止刷新</button>
        </div>
        
        <div style="background: #e7f3ff; padding: 15px; border-radius: 8px; margin: 10px 0;">
            <h3>🔍 测试步骤</h3>
            <ol>
                <li>在客服后台发送消息</li>
                <li>点击"刷新页面"查看是否收到消息</li>
                <li>检查通知是否正确显示</li>
                <li>如果没有收到，检查用户ID是否匹配</li>
            </ol>
        </div>
        
        <div style="background: #fff3cd; padding: 15px; border-radius: 8px; margin: 10px 0;">
            <h3>🔧 前台实时通信解决方案</h3>
            <p><strong>问题：</strong>前台用户看不到客服发送的消息</p>
            <p><strong>原因：</strong>前台页面缺少实时通信机制</p>
            <p><strong>解决方案：</strong></p>
            <ul>
                <li>添加定时轮询检查新消息</li>
                <li>实现WebSocket或SSE实时通信</li>
                <li>添加通知提醒功能</li>
            </ul>
        </div>
        
        <p>
            <a href="test_message_flow.php">返回消息流程测试</a> | 
            <a href="sessions.php">客服后台</a>
        </p>
    </div>
    
    <script>
        let autoRefreshInterval;
        
        function autoRefresh() {
            autoRefreshInterval = setInterval(() => {
                location.reload();
            }, 5000); // 每5秒刷新一次
            
            // 更新按钮状态
            document.querySelector('button[onclick="autoRefresh()"]').style.background = '#28a745';
            document.querySelector('button[onclick="autoRefresh()"]').textContent = '🔄 自动刷新中...';
        }
        
        function stopAutoRefresh() {
            if (autoRefreshInterval) {
                clearInterval(autoRefreshInterval);
                autoRefreshInterval = null;
            }
            
            // 恢复按钮状态
            document.querySelector('button[onclick="autoRefresh()"]').style.background = '#6F7BF5';
            document.querySelector('button[onclick="autoRefresh()"]').textContent = '🔄 自动刷新';
        }
        
        // 页面卸载时清除定时器
        window.addEventListener('beforeunload', stopAutoRefresh);
    </script>
</body>
</html>
