<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <meta name="format-detection" content="telephone=no">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="white">
    <title>趣玩星球</title>
    
    <!-- Vue 3 CDN -->
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
    <!-- Vue Router -->
    <script src="https://unpkg.com/vue-router@4/dist/vue-router.global.js"></script>
    <!-- Pinia 状态管理 -->
    <script src="https://unpkg.com/pinia@2/dist/pinia.iife.js"></script>
    
    <!-- 样式 -->
    <link rel="stylesheet" href="./css/global.css">
    <link rel="stylesheet" href="./css/components.css">
    
    <!-- PWA支持 -->
    <link rel="manifest" href="./manifest.json">
    <meta name="theme-color" content="#6F7BF5">
    
    <!-- 图标 -->
    <link rel="icon" href="./images/favicon.ico">
    <link rel="apple-touch-icon" href="./images/icon-192.png">
</head>
<body>
    <div id="app">
        <!-- 全局加载 -->
        <div v-if="globalLoading" class="global-loading">
            <div class="loading-content">
                <div class="loading-star"></div>
                <div class="loading-text">{{ loadingText }}</div>
            </div>
        </div>
        
        <!-- 路由视图 -->
        <router-view v-else></router-view>
        
        <!-- 全局通知容器 -->
        <div id="notification-container" class="notification-container"></div>
        
        <!-- 全局弹窗容器 -->
        <div id="modal-container" class="modal-container"></div>
    </div>

    <!-- 应用脚本 -->
    <script src="./js/utils.js"></script>
    <script src="./js/api.js"></script>
    <script src="./js/store.js"></script>
    <script src="./js/components.js"></script>
    <script src="./js/router.js"></script>
    <script src="./js/app.js"></script>
</body>
</html>
