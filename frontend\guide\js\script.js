// Toast提示函数
function showToast(message) {
    const toast = document.getElementById('toast');
    toast.textContent = message;
    toast.style.display = 'block';
    
    // 添加动画
    toast.style.animation = 'fadeIn 0.3s forwards';
    
    // 3秒后隐藏
    setTimeout(() => {
        toast.style.animation = 'fadeOut 0.3s forwards';
        setTimeout(() => {
            toast.style.display = 'none';
        }, 300);
    }, 3000);
}

// 确保window.showToast也可用
window.showToast = showToast;

document.addEventListener('DOMContentLoaded', function() {
    // 分类项点击效果
    const categoryItems = document.querySelectorAll('.category-item');
    categoryItems.forEach(item => {
        item.addEventListener('click', function() {
            // 获取分类名称
            const categoryName = this.querySelector('.category-label').textContent;
            
            // 显示提示
            showToast(`您选择了: ${categoryName}`);
        });
    });
    
    // 子分类项点击效果
    const subcategoryItems = document.querySelectorAll('.subcategory-item');
    subcategoryItems.forEach(item => {
        item.addEventListener('click', function() {
            // 获取子分类名称
            const subcategoryName = this.textContent;
            
            // 显示提示
            showToast(`您选择了: ${subcategoryName}`);
        });
    });
});
