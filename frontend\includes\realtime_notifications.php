<?php
/**
 * 实时通知系统集成文件
 * 在需要实时通知的页面中引入此文件
 */

// 检查是否已登录
$is_logged_in = isset($_SESSION['user_id']) && !empty($_SESSION['user_id']);

// 只有登录用户才加载通知系统
if ($is_logged_in): ?>
    <!-- 实时通知系统 -->
    <meta name="user-id" content="<?php echo htmlspecialchars($_SESSION['user_id']); ?>">
    <script src="/frontend/js/universal_notifications.js"></script>
    
    <script>
        // 页面特定配置（可选）
        document.addEventListener('DOMContentLoaded', function() {
            // 可以在这里设置页面特定的通知配置
            if (window.UniversalNotifications) {
                // 例如：在某些页面显示连接状态
                // window.UniversalNotifications.setConfig({ showStatus: true });
                
                console.log('实时通知系统已在此页面启用');
            }
        });
    </script>
<?php endif; ?>
