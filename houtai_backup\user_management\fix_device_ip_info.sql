-- 修复设备信息和IP信息功能
-- 确保login_logs表存在并添加测试数据

-- 创建登录日志表（如果不存在）
CREATE TABLE IF NOT EXISTS login_logs (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    login_time DATETIME NOT NULL,
    ip_address VARCHAR(45) NOT NULL,
    user_agent TEXT,
    status ENUM('success', 'failed') NOT NULL DEFAULT 'success',
    device_fingerprint VARCHAR(32),
    login_type ENUM('quick_login', 'secure_login', 'normal_login') DEFAULT 'normal_login',
    location VARCHAR(100),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_user_id (user_id),
    INDEX idx_login_time (login_time),
    INDEX idx_ip_address (ip_address),
    INDEX idx_device_fingerprint (device_fingerprint),
    INDEX idx_status (status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 添加一些测试数据（如果表为空）
INSERT IGNORE INTO login_logs (user_id, login_time, ip_address, user_agent, status, login_type) VALUES
-- 用户1的登录记录
(1, DATE_SUB(NOW(), INTERVAL 1 HOUR), '*************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36', 'success', 'normal_login'),
(1, DATE_SUB(NOW(), INTERVAL 2 HOURS), '*************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36', 'success', 'quick_login'),
(1, DATE_SUB(NOW(), INTERVAL 1 DAY), '**************', 'Mozilla/5.0 (iPhone; CPU iPhone OS 17_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.0 Mobile/15E148 Safari/604.1', 'success', 'normal_login'),
(1, DATE_SUB(NOW(), INTERVAL 2 DAYS), '************', 'Mozilla/5.0 (Linux; Android 13; SM-G998B) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Mobile Safari/537.36', 'success', 'secure_login'),
(1, DATE_SUB(NOW(), INTERVAL 3 DAYS), '************', 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36', 'success', 'normal_login'),

-- 用户2的登录记录
(2, DATE_SUB(NOW(), INTERVAL 30 MINUTES), '*************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Edge/120.0.0.0 Safari/537.36', 'success', 'normal_login'),
(2, DATE_SUB(NOW(), INTERVAL 1 DAY), '*************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Edge/120.0.0.0 Safari/537.36', 'success', 'quick_login'),
(2, DATE_SUB(NOW(), INTERVAL 2 DAYS), '************', 'Mozilla/5.0 (iPad; CPU OS 17_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.0 Mobile/15E148 Safari/604.1', 'success', 'normal_login'),

-- 用户3的登录记录
(3, DATE_SUB(NOW(), INTERVAL 15 MINUTES), '************', 'Mozilla/5.0 (Linux; Android 13; Pixel 7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Mobile Safari/537.36', 'success', 'normal_login'),
(3, DATE_SUB(NOW(), INTERVAL 4 HOURS), '************', 'Mozilla/5.0 (Linux; Android 13; Pixel 7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Mobile Safari/537.36', 'success', 'quick_login'),
(3, DATE_SUB(NOW(), INTERVAL 1 DAY), '************', 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36', 'success', 'secure_login'),

-- 用户4的登录记录（多地区登录）
(4, DATE_SUB(NOW(), INTERVAL 10 MINUTES), '************', 'Mozilla/5.0 (iPhone; CPU iPhone OS 17_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.0 Mobile/15E148 Safari/604.1', 'success', 'normal_login'),
(4, DATE_SUB(NOW(), INTERVAL 6 HOURS), '************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:120.0) Gecko/20100101 Firefox/120.0', 'success', 'normal_login'),
(4, DATE_SUB(NOW(), INTERVAL 1 DAY), '************', 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.0 Safari/605.1.15', 'success', 'quick_login'),
(4, DATE_SUB(NOW(), INTERVAL 2 DAYS), '*************', 'Mozilla/5.0 (Linux; Android 13; SM-A536B) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Mobile Safari/537.36', 'success', 'normal_login'),
(4, DATE_SUB(NOW(), INTERVAL 3 DAYS), '************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36', 'success', 'secure_login'),

-- 用户5的登录记录（高风险用户 - 多IP多地区）
(5, DATE_SUB(NOW(), INTERVAL 5 MINUTES), '***********', 'Mozilla/5.0 (iPhone; CPU iPhone OS 17_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.0 Mobile/15E148 Safari/604.1', 'success', 'normal_login'),
(5, DATE_SUB(NOW(), INTERVAL 2 HOURS), '************', 'Mozilla/5.0 (Linux; Android 13; OnePlus 11) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Mobile Safari/537.36', 'success', 'quick_login'),
(5, DATE_SUB(NOW(), INTERVAL 8 HOURS), '************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36', 'success', 'normal_login'),
(5, DATE_SUB(NOW(), INTERVAL 1 DAY), '************', 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36', 'success', 'secure_login'),
(5, DATE_SUB(NOW(), INTERVAL 2 DAYS), '*************', 'Mozilla/5.0 (iPad; CPU OS 17_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.0 Mobile/15E148 Safari/604.1', 'success', 'normal_login'),
(5, DATE_SUB(NOW(), INTERVAL 3 DAYS), '************', 'Mozilla/5.0 (X11; Linux x86_64; rv:120.0) Gecko/20100101 Firefox/120.0', 'success', 'quick_login'),
(5, DATE_SUB(NOW(), INTERVAL 4 DAYS), '*************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Edge/120.0.0.0 Safari/537.36', 'success', 'normal_login'),
(5, DATE_SUB(NOW(), INTERVAL 5 DAYS), '************', 'Mozilla/5.0 (Linux; Android 13; Xiaomi 13) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Mobile Safari/537.36', 'success', 'secure_login');

-- 记录修复执行
INSERT IGNORE INTO database_fixes (fix_name, description) VALUES 
('fix_device_ip_info', '修复用户详情页设备信息和IP信息功能，添加login_logs表和测试数据');
