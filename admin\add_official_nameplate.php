<?php
// 数据库连接配置
$db_config = [
    'host' => 'localhost',
    'username' => 'quwanplanet',
    'password' => 'nJmJm23FB4Xn6Fc3',
    'dbname' => 'quwanplanet',
    'port' => 3306,
    'charset' => 'utf8mb4'
];

// Cloudinary配置
$cloudinary_config = [
    'cloud_name' => 'dwcauq0wy',
    'api_key' => '965165511998959',
    'api_secret' => 'JYnkxTIAAC3GLuf3u6iiQpfqfMA',
    'preset' => 'chat_app_preset'
];

// 连接数据库
try {
    $dsn = "mysql:host={$db_config['host']};dbname={$db_config['dbname']};charset={$db_config['charset']};port={$db_config['port']}";
    $pdo = new PDO($dsn, $db_config['username'], $db_config['password']);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    echo "数据库连接成功<br>";
} catch (PDOException $e) {
    die("数据库连接失败: " . $e->getMessage());
}

// 上传铭牌图片到Cloudinary
function uploadToCloudinary($imageUrl, $config) {
    $data = [
        'file' => $imageUrl,
        'upload_preset' => $config['preset'],
        'folder' => 'nameplates'
    ];
    
    $url = "https://api.cloudinary.com/v1_1/{$config['cloud_name']}/image/upload";
    
    $ch = curl_init($url);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, $data);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    
    $response = curl_exec($ch);
    curl_close($ch);
    
    if (!$response) {
        return false;
    }
    
    return json_decode($response, true);
}

// 为指定手机号的用户添加铭牌
function addNameplateToUser($pdo, $phone, $nameplateUrl, $nameplateType) {
    try {
        // 查找用户
        $stmt = $pdo->prepare("SELECT id FROM users WHERE phone = :phone");
        $stmt->execute(['phone' => $phone]);
        $user = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$user) {
            return "用户不存在，手机号: " . $phone;
        }
        
        // 更新用户铭牌信息
        $stmt = $pdo->prepare("UPDATE users SET nameplate_url = :nameplate_url, nameplate_type = :nameplate_type, nameplate_active = 1 WHERE id = :user_id");
        $stmt->execute([
            'nameplate_url' => $nameplateUrl,
            'nameplate_type' => $nameplateType,
            'user_id' => $user['id']
        ]);
        
        return "铭牌添加成功，用户ID: " . $user['id'];
    } catch (PDOException $e) {
        return "错误: " . $e->getMessage();
    }
}

// 官方铭牌图片URL
$officialNameplateUrl = 'https://s1.imagehub.cc/images/2025/05/16/official_nameplate.png';

// 上传到Cloudinary
echo "正在上传铭牌图片到Cloudinary...<br>";
$result = uploadToCloudinary($officialNameplateUrl, $cloudinary_config);

if ($result && isset($result['secure_url'])) {
    echo "上传成功，Cloudinary URL: " . $result['secure_url'] . "<br>";
    
    // 为指定用户添加铭牌
    $phone = '19377988803';
    $message = addNameplateToUser($pdo, $phone, $result['secure_url'], 'official');
    echo $message;
} else {
    echo "上传到Cloudinary失败";
}
?>
