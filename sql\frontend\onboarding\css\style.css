/* 全局样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    font-family: 'PingFang SC', 'Helvetica Neue', Arial, sans-serif;
}

body {
    background: #f8f9fa;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    align-items: center;
    color: #333333;
    line-height: 1.6;
    margin: 0;
    padding: 0;
}

/* 移除状态栏样式 */

/* 品牌区域样式 */
.brand-section {
    text-align: center;
    margin-bottom: 20px;
    padding-top: 0;
}

.brand-logo {
    width: 60px;
    height: 60px;
    margin: 0 auto 12px;
    border-radius: 16px;
    overflow: hidden;
    box-shadow: 0 6px 20px rgba(111, 123, 245, 0.15);
}

.brand-text img {
    max-width: 180px;
    height: auto;
}



/* 新的标题样式 */
.onboarding-title {
    text-align: center;
    margin-bottom: 20px;
    padding: 0;
    background: none;
    border-radius: 0;
    box-shadow: none;
    position: relative;
    z-index: 10;
    margin-top: 0;
}

.onboarding-title h1 {
    font-size: 22px;
    margin-bottom: 8px;
    color: #333;
    font-weight: 600;
}

.onboarding-title p {
    font-size: 14px;
    color: #666;
    opacity: 0.9;
    margin-bottom: 10px;
}

.container {
    position: relative;
    z-index: 10;
    margin: 0 auto;
    padding: 20px;
    max-width: 400px;
    width: 100%;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
}

/* 引导卡片样式 */
.onboarding-card {
    background: #FFFFFF;
    border-radius: 20px;
    padding: 30px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    border: 1px solid #f0f0f0;
    width: 100%;
    max-width: 400px;
}

/* 头像上传样式 */
.avatar-upload {
    position: relative;
    max-width: 180px;
    margin: 0 auto 30px;
    text-align: center;
}

.avatar-preview {
    width: 140px;
    height: 140px;
    position: relative;
    border-radius: 50%;
    overflow: hidden;
    margin: 0 auto;
    border: 4px solid #6F7BF5;
    box-shadow: 0 5px 15px rgba(111, 123, 245, 0.15);
    transition: all 0.3s ease;
    background-color: #f5f5f5;
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    cursor: pointer;
}

.avatar-preview:hover {
    transform: scale(1.05);
    box-shadow: 0 8px 20px rgba(111, 123, 245, 0.2);
}

#avatar-preview {
    width: 100%;
    height: 100%;
    background-size: cover;
    background-position: center;
}

.avatar-edit {
    position: absolute;
    right: -8px;
    bottom: -8px;
    z-index: 100;
}

.avatar-edit input {
    display: none;
}

.avatar-edit label {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 44px;
    height: 44px;
    border-radius: 50%;
    background: #6F7BF5;
    color: white;
    cursor: pointer;
    box-shadow: 0 4px 12px rgba(111, 123, 245, 0.3);
    transition: all 0.3s ease;
    border: 3px solid white;
}

.avatar-edit label:hover {
    background: #5A67E8;
    transform: translateY(-2px) scale(1.1);
    box-shadow: 0 6px 16px rgba(111, 123, 245, 0.4);
}

.avatar-edit label svg {
    width: 22px;
    height: 22px;
    fill: currentColor;
}

.avatar-text {
    margin-top: 15px;
    font-size: 14px;
    color: #666;
    font-weight: 500;
}

/* 重置按钮样式 */
.reset-avatar {
    display: inline-block;
    margin-top: 10px;
    padding: 6px 12px;
    background-color: #f5f5f5;
    color: #666;
    border: none;
    border-radius: 15px;
    font-size: 12px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.reset-avatar:hover {
    background-color: #e0e0e0;
    transform: translateY(-2px);
}

/* 拖放相关样式 */
.avatar-upload.dragging .avatar-preview {
    border-color: #20B2AA;
    box-shadow: 0 0 0 4px rgba(64, 224, 208, 0.2);
    transform: scale(1.05);
}

/* 预览加载动画 */
@keyframes previewPulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.08); }
    100% { transform: scale(1); }
}

.preview-loaded {
    animation: previewPulse 0.5s ease;
}

/* 表单样式 */
.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    color: #555;
}

.required {
    color: #ff4757;
    font-weight: 700;
}

.radio-group {
    display: flex;
    gap: 20px;
}

.radio-label {
    display: flex;
    align-items: center;
    cursor: pointer;
}

.radio-label input {
    margin-right: 8px;
    accent-color: #6F7BF5;
}

input[type="text"],
input[type="email"],
input[type="password"],
input[type="date"],
textarea {
    width: 100%;
    padding: 12px 15px;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    font-size: 16px;
    transition: all 0.3s ease;
    outline: none;
}

input[type="text"]:focus,
input[type="email"]:focus,
input[type="password"]:focus,
input[type="date"]:focus,
textarea:focus {
    border-color: #6F7BF5;
    box-shadow: 0 0 0 2px rgba(111, 123, 245, 0.1);
}

/* 手机号字段特殊样式 */
input[readonly] {
    background-color: #f8f9fa;
    color: #666;
    cursor: not-allowed;
}

input[readonly]:focus {
    border-color: #e0e0e0;
    box-shadow: none;
}

.field-note {
    display: block;
    margin-top: 4px;
    font-size: 12px;
    color: #999;
    font-style: italic;
}

/* 地区选择容器 */
.region-input-container {
    position: relative;
    display: flex;
    align-items: center;
}

.region-input-container input {
    padding-right: 50px;
    cursor: pointer;
}

.region-select-btn {
    position: absolute;
    right: 8px;
    background: none;
    border: none;
    color: #6F7BF5;
    cursor: pointer;
    padding: 8px;
    border-radius: 6px;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.region-select-btn:hover {
    background: rgba(111, 123, 245, 0.1);
    transform: scale(1.1);
}

/* 日期选择容器 */
.date-input-container {
    position: relative;
    display: flex;
    align-items: center;
}

.date-input-container input {
    padding-right: 50px;
    cursor: pointer;
}

.date-select-btn {
    position: absolute;
    right: 8px;
    background: none;
    border: none;
    color: #6F7BF5;
    cursor: pointer;
    padding: 8px;
    border-radius: 6px;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.date-select-btn:hover {
    background: rgba(111, 123, 245, 0.1);
    transform: scale(1.1);
}

textarea {
    resize: vertical;
    min-height: 80px;
}

/* 按钮样式 */
.form-actions {
    display: flex;
    justify-content: space-between;
    margin-top: 30px;
}

.btn-submit {
    flex: 1;
    padding: 15px;
    background: #6F7BF5;
    color: white;
    border: none;
    border-radius: 30px;
    font-size: 16px;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 10px rgba(111, 123, 245, 0.3);
    margin-right: 10px;
}

.btn-submit:hover {
    background: #5A67E8;
    transform: translateY(-3px);
    box-shadow: 0 6px 15px rgba(111, 123, 245, 0.4);
}

.btn-skip {
    padding: 15px 25px;
    background: #f5f5f5;
    color: #666;
    border: none;
    border-radius: 30px;
    font-size: 16px;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    text-align: center;
}

.btn-skip:hover {
    background: #e5e5e5;
}

/* 消息样式 */
.error-message,
.success-message {
    padding: 12px 15px;
    border-radius: 8px;
    margin-bottom: 20px;
    font-size: 14px;
}

.error-message {
    background-color: #ffebee;
    color: #d32f2f;
    border-left: 4px solid #d32f2f;
}

.success-message {
    background-color: #e8f5e9;
    color: #388e3c;
    border-left: 4px solid #388e3c;
}

/* Toast提示样式 */
.toast {
    position: fixed;
    top: 20px;
    left: 50%;
    transform: translateX(-50%);
    background-color: rgba(255, 255, 255, 0.95);
    color: #333333;
    padding: 14px 24px;
    border-radius: 12px;
    box-shadow: 0 6px 16px rgba(0, 0, 0, 0.12);
    z-index: 9999;
    display: none;
    border-left: 4px solid #40E0D0;
    font-size: 15px;
    font-weight: 500;
    max-width: 90%;
    text-align: center;
    pointer-events: none;
}

/* 动画关键帧 */
@keyframes orbitRotate {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

@keyframes planetPulse {
    0%, 100% { transform: translate(-50%, -50%) scale(1); }
    50% { transform: translate(-50%, -50%) scale(1.05); }
}

@keyframes starTwinkle {
    0%, 100% { opacity: 0.3; transform: scale(0.8); }
    50% { opacity: 1; transform: scale(1.2); }
}

/* 头像审核动画覆盖层 */
.avatar-moderation-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    display: none;
    align-items: center;
    justify-content: center;
    z-index: 2000;
    flex-direction: column;
}

/* 审核动画四角星系统 */
.moderation-star-system {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 16px;
    margin-bottom: 30px;
    height: 60px;
}

.moderation-star {
    width: 16px;
    height: 16px;
    position: relative;
    animation: moderationStarMove 1.8s ease-in-out infinite;
}

.moderation-star:before {
    content: '';
    position: absolute;
    top: 0;
    left: 50%;
    width: 0;
    height: 0;
    border-left: 8px solid transparent;
    border-right: 8px solid transparent;
    border-bottom: 11px solid currentColor;
    transform: translateX(-50%);
}

.moderation-star:after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    width: 0;
    height: 0;
    border-left: 8px solid transparent;
    border-right: 8px solid transparent;
    border-top: 11px solid currentColor;
    transform: translateX(-50%);
}

.moderation-star-1 {
    color: #6F7BF5;
    animation-delay: 0s;
}

.moderation-star-2 {
    color: #FF8C42;
    animation-delay: 0.6s;
}

.moderation-star-3 {
    color: #FF6B6B;
    animation-delay: 1.2s;
}

@keyframes moderationStarMove {
    0%, 100% {
        transform: translateY(0) scale(1) rotate(0deg);
        opacity: 0.7;
    }
    50% {
        transform: translateY(-15px) scale(1.2) rotate(180deg);
        opacity: 1;
    }
}

.moderation-text {
    color: #333333;
    text-align: center;
    font-size: 1.2rem;
    font-weight: 600;
    margin-bottom: 12px;
    animation: textFadeIn 1s ease-out;
}

.moderation-subtext {
    color: #666666;
    text-align: center;
    font-size: 0.9rem;
    animation: textFadeIn 1s ease-out 0.3s both;
}

/* 审核动画关键帧 */
@keyframes moderationOrbitRotate {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

@keyframes moderationPlanetPulse {
    0%, 100% {
        transform: translate(-50%, -50%) scale(1);
        box-shadow:
            inset -4px -4px 8px rgba(32, 178, 170, 0.4),
            0 0 20px rgba(64, 224, 208, 0.8),
            0 0 40px rgba(64, 224, 208, 0.4);
    }
    50% {
        transform: translate(-50%, -50%) scale(1.2);
        box-shadow:
            inset -4px -4px 8px rgba(32, 178, 170, 0.6),
            0 0 30px rgba(64, 224, 208, 1),
            0 0 60px rgba(64, 224, 208, 0.6);
    }
}

@keyframes moderationStarTwinkle {
    0%, 100% {
        opacity: 0.5;
        transform: scale(0.8);
        box-shadow: 0 0 6px #FFD700, 0 0 12px #FFD700;
    }
    50% {
        opacity: 1;
        transform: scale(1.5);
        box-shadow: 0 0 12px #FFD700, 0 0 24px #FFD700;
    }
}

@keyframes textFadeIn {
    from {
        opacity: 0;
        transform: translateY(15px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 日期选择器弹窗样式 */
.date-picker-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.6);
    display: none;
    align-items: center;
    justify-content: center;
    z-index: 3000;
    backdrop-filter: blur(4px);
}

.date-picker-modal {
    background: white;
    border-radius: 16px;
    width: 90%;
    max-width: 400px;
    max-height: 80vh;
    overflow: hidden;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
    animation: modalSlideIn 0.3s ease-out;
}

.date-picker-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 20px 24px;
    border-bottom: 1px solid rgba(64, 224, 208, 0.1);
    background: linear-gradient(135deg, #40E0D0 0%, #20B2AA 100%);
    color: white;
}

.date-picker-header h3 {
    margin: 0;
    font-size: 1.2rem;
    font-weight: 600;
}

.date-picker-close {
    background: none;
    border: none;
    color: white;
    cursor: pointer;
    padding: 8px;
    border-radius: 50%;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.date-picker-close:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: scale(1.1);
}

.date-picker-content {
    padding: 24px;
}

.date-selectors {
    display: flex;
    gap: 16px;
    margin-bottom: 24px;
}

.selector-group {
    flex: 1;
    text-align: center;
}

.selector-group label {
    display: block;
    margin-bottom: 8px;
    font-size: 0.9rem;
    font-weight: 600;
    color: #333;
}

.selector-wrapper {
    position: relative;
}

.date-selector {
    width: 100%;
    padding: 12px 8px;
    border: 2px solid rgba(64, 224, 208, 0.2);
    border-radius: 8px;
    font-size: 1rem;
    text-align: center;
    background: white;
    cursor: pointer;
    transition: all 0.3s ease;
    appearance: none;
    -webkit-appearance: none;
    -moz-appearance: none;
}

.date-selector:focus {
    outline: none;
    border-color: #40E0D0;
    box-shadow: 0 0 0 3px rgba(64, 224, 208, 0.1);
}

.date-selector:hover {
    border-color: #40E0D0;
}

/* 自定义下拉箭头 */
.selector-wrapper::after {
    content: '';
    position: absolute;
    top: 50%;
    right: 8px;
    transform: translateY(-50%);
    width: 0;
    height: 0;
    border-left: 4px solid transparent;
    border-right: 4px solid transparent;
    border-top: 6px solid #40E0D0;
    pointer-events: none;
}

.date-picker-actions {
    display: flex;
    gap: 12px;
    justify-content: flex-end;
}

.btn-cancel,
.btn-confirm {
    padding: 12px 24px;
    border: none;
    border-radius: 8px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    min-width: 80px;
}

.btn-cancel {
    background: #f8f9fa;
    color: #666;
    border: 1px solid #e9ecef;
}

.btn-cancel:hover {
    background: #e9ecef;
    transform: translateY(-1px);
}

.btn-confirm {
    background: linear-gradient(135deg, #40E0D0 0%, #20B2AA 100%);
    color: white;
    box-shadow: 0 4px 12px rgba(64, 224, 208, 0.3);
}

.btn-confirm:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 16px rgba(64, 224, 208, 0.4);
}

/* 弹窗动画 */
@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: translateY(-30px) scale(0.95);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

/* 响应式调整 */
@media (max-width: 480px) {
    .date-picker-modal {
        width: 95%;
        margin: 20px;
    }

    .date-selectors {
        flex-direction: column;
        gap: 12px;
    }

    .date-picker-actions {
        flex-direction: column;
    }

    .btn-cancel,
    .btn-confirm {
        width: 100%;
    }
}
